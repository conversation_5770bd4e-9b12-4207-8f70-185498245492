#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?push_back@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEAAXAEBQEAVCMoveMapLimitRight@@@Z
 * Address: 0x1403AEB10

void  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::push_back(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, CMoveMapLimitRight *const *_Val)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  unsigned int64_t v4; // rax@4
  int64_t v5; // [sp+0h] [bp-78h]@1
  char v6; // [sp+20h] [bp-58h]@6
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *result; // [sp+38h] [bp-40h]@6
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > v8; // [sp+40h] [bp-38h]@6
  unsigned int64_t v9; // [sp+58h] [bp-20h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v10; // [sp+60h] [bp-18h]@6
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v11; // [sp+80h] [bp+8h]@1
  CMoveMapLimitRight **_Vala; // [sp+88h] [bp+10h]@1

  _Vala = (CMoveMapLimitRight **)_Val;
  v11 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v9 = std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::size(v11);
  v4 = std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::capacity(v11);
  if ( v9 >= v4 )
  {
    result = (std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v6;
    v10 = std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::end(
            v11,
            (std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v6);
    std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::insert(v11, &v8, v10, _Vala);
    std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(&v8);
  }
  else
  {
    v11->_Mylast = std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Ufill(
                     v11,
                     v11->_Mylast,
                     1ui64,
                     _Vala);
  }
}
