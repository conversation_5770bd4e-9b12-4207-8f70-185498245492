#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_IncreaseWeeklyGuildKillPvpPointSum@CRFWorldDatabase@@QEAA_NKN@Z
 * Address: 0x1404A7020

bool  CRFWorldDatabase::Update_IncreaseWeeklyGuildKillPvpPointSum(CRFWorldDatabase *this, unsigned int dwSerial, long double dPvpPoint)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp-30h] [bp-458h]@1
  char Dst; // [sp+0h] [bp-428h]@4
  unsigned int64_t v8; // [sp+410h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+430h] [bp+8h]@1
  unsigned int v10; // [sp+438h] [bp+10h]@1

  v10 = dwSerial;
  v9 = this;
  v3 = &v6;
  for ( i = 276i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v8 = (unsigned int64_t)&v6 ^ _security_cookie;
  memset_0(&Dst, 0, 0x400ui64);
  sprintf(&Dst, "{ CALL pUpdate_WeeklyGuildKillPVPPoint(%u, %f) }", v10, dPvpPoint);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &Dst, 1);
}
