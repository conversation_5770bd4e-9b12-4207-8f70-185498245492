#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Move_cat@PEAPEAVCUnmannedTraderSortType@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAPEAVCUnmannedTraderSortType@@@Z
 * Address: 0x140003350

std::_Undefined_move_tag  std::_Move_cat<CUnmannedTraderSortType * *>(CUnmannedTraderSortType **const *__formal)
{
  return std::_Move_cat<CUnmannedTraderSortType * *>(__formal);
}
