#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?invoke@?$void2ptr@$$A6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@SAP6APEAVCMonster@@PEAD0MMM@ZPEAX@Z
 * Address: 0x1400024AA

CMonster *( * lua_tinker::void2ptr<CMonster * (char *,char *,float,float,float)>::invoke(lua_tinker::void2ptr<CMonster * (char *,char *,float,float,float)> *this, void *input))(char *, char *, float, float, float)
{
  return lua_tinker::void2ptr<CMonster * (char *,char *,float,float,float)>::invoke(this, input);
}
