#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::_Tree_std::_Tmap_traits_std::basic_string_char_std::char_traits_char__std::allocator_char____AreaList_std::less_std::basic_string_char_std::char_traits_char__std::allocator_char______std::allocator_std::pair_std::basic_string_char_std::char_traits_char__std::allocator_char____const__AreaList____0___::_Buynode_::_1_::dtor$0
 * Address: 0x140191900

void  std::_Tree_std::_Tmap_traits_std::basic_string_char_std::char_traits_char__std::allocator_char____AreaList_std::less_std::basic_string_char_std::char_traits_char__std::allocator_char______std::allocator_std::pair_std::basic_string_char_std::char_traits_char__std::allocator_char____const__AreaList____0___::_Buynode_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  operator delete(*(void **)(a2 + 64), *(void **)(a2 + 48));
}
