<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;f5ac9aaf-f6e1-466c-ba76-68f5d5c72f16&quot;,&quot;conversations&quot;:{&quot;c8c08ee5-a437-4992-ba7d-036d1c0ba172&quot;:{&quot;id&quot;:&quot;c8c08ee5-a437-4992-ba7d-036d1c0ba172&quot;,&quot;createdAtIso&quot;:&quot;2025-07-15T08:42:21.276Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-15T08:42:21.276Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0},&quot;f5ac9aaf-f6e1-466c-ba76-68f5d5c72f16&quot;:{&quot;id&quot;:&quot;f5ac9aaf-f6e1-466c-ba76-68f5d5c72f16&quot;,&quot;createdAtIso&quot;:&quot;2025-07-15T08:42:21.567Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-15T08:48:57.212Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;70babbb1-ed75-452e-a00c-33fce13a0924&quot;,&quot;request_message&quot;:&quot;scan the folders and let me know where can you see the message when you open the zoneserver - \&quot;RF SERVER NOW LOADING\&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;scan the folders and let me know where can you see the message when you open the zoneserver - \&quot;RF SERVER NOW LOADING\&quot;&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01L7X6Lj5e68C3UvqR6hJVsP&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;.\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;I'll help you scan the folders to find where the \&quot;RF SERVER NOW LOADING\&quot; message appears when opening the zoneserver. Let me start by exploring the codebase structure and searching for this specific message.\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;scan the folders and let me know where can you see the message when you open the zoneserver - \&quot;RF SERVER NOW LOADING\&quot;&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;,&quot;folderRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-15T08:45:55.982Z&quot;,&quot;response_text&quot;:&quot;I'll help you scan the folders to find where the \&quot;RF SERVER NOW LOADING\&quot; message appears when opening the zoneserver. Let me start by exploring the codebase structure and searching for this specific message.\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;4ab6d1e6-4297-4198-b622-808a464ac518&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01K3fAwp5HEbK5kLBHVBazG7&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\\&quot;RF SERVER NOW LOADING\\\&quot; message in the zoneserver code\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;I can see this is a decompiled codebase for what appears to be an RF Online game server. Let me search for the specific message \&quot;RF SERVER NOW LOADING\&quot; in the codebase.\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./Decompiled Header - IDA Pro\n./Decompiled Header - IDA Pro\\ZoneServerUD_x64.h\n./Decompiled Source Code - IDA Pro\n./Decompiled Source Code - IDA Pro\\authentication\n./Decompiled Source Code - IDA Pro\\combat\n./Decompiled Source Code - IDA Pro\\database\n./Decompiled Source Code - IDA Pro\\economy\n./Decompiled Source Code - IDA Pro\\items\n./Decompiled Source Code - IDA Pro\\network\n./Decompiled Source Code - IDA Pro\\player\n./Decompiled Source Code - IDA Pro\\security\n./Decompiled Source Code - IDA Pro\\system\n./Decompiled Source Code - IDA Pro\\world\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01L7X6Lj5e68C3UvqR6hJVsP&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;,&quot;folderRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-15T08:46:10.032Z&quot;,&quot;response_text&quot;:&quot;I can see this is a decompiled codebase for what appears to be an RF Online game server. Let me search for the specific message \&quot;RF SERVER NOW LOADING\&quot; in the codebase.\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;0d1d47cf-b487-41c1-be22-67e4bc7300eb&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_0181qRsSGXjjTPRjLdSJ2QJR&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\\&quot;RF SERVER NOW LOADING\\\&quot; message in the zoneserver startup code\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;Let me continue searching for the specific \&quot;RF SERVER NOW LOADING\&quot; message:\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: Decompiled Source Code - IDA Pro/world/j_size_server_notify_inform_zoneQEAAHXZ_140005187.c\n     1\t/*\n     2\t * Function: j_?size@_server_notify_inform_zone@@QEAAHXZ\n     3\t * Address: 0x140005187\n     4\t */\n     5\t\n     6\tint __fastcall _server_notify_inform_zone::size(_server_notify_inform_zone *this)\n     7\t{\n     8\t  return _server_notify_inform_zone::size(this);\n     9\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/size_server_notify_inform_zoneQEAAHXZ_14011F1E0.c\n     1\t/*\n     2\t * Function: ?size@_server_notify_inform_zone@@QEAAHXZ\n     3\t * Address: 0x14011F1E0\n     4\t */\n     5\t\n     6\tsigned __int64 __fastcall _server_notify_inform_zone::size(_server_notify_inform_zone *this)\n     7\t{\n     8\t  return 6i64;\n     9\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/LoadAllBossSchedule_MapQEAA_NXZ_14041A070.c\n     1\t/*\n     2\t * Function: ?LoadAll@BossSchedule_Map@@QEAA_NXZ\n     3\t * Address: 0x14041A070\n     4\t */\n     5\t\n     6\tchar __fastcall BossSchedule_Map::LoadAll(BossSchedule_Map *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v4; // [sp+0h] [bp-48h]@1\n    12\t  unsigned int dwIndex; // [sp+20h] [bp-28h]@9\n    13\t  INI_Section *pSection; // [sp+28h] [bp-20h]@11\n    14\t  BossSchedule **v7; // [sp+30h] [bp-18h]@9\n    15\t  unsigned __int64 v8; // [sp+38h] [bp-10h]@9\n    16\t  BossSchedule_Map *pMapSchedule; // [sp+50h] [bp+8h]@1\n    17\t\n    18\t  pMapSchedule = this;\n    19\t  v1 = &amp;v4;\n    20\t  for ( i = 16i64; i; --i )\n    21\t  {\n    22\t    *(_DWORD *)v1 = -*********;\n    23\t    v1 = (__int64 *)((char *)v1 + 4);\n    24\t  }\n...\nPath: Decompiled Source Code - IDA Pro/combat/load_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F5E30.c\n...\n    20\t  CLogFile::Write(&amp;v4-&gt;m_cde.m_cde_log, \&quot;Loading Cash Discount-Rate Event\&quot;);\n    21\t  CNetTimer::BeginTimer(&amp;v4-&gt;m_cde.m_cde_timer, 0x3E8u);\n    22\t  v4-&gt;m_cde.m_cde_inform_before[0] = 1800;\n    23\t  v4-&gt;m_cde.m_cde_inform_before[1] = 300;\n    24\t  v4-&gt;m_cde.m_cde_status = 0;\n    25\t  CashItemRemoteStore::load_cde_ini(v4, &amp;v4-&gt;m_cde.m_ini, &amp;v4-&gt;m_cde.m_cde_ini_file_time);\n    26\t  CashItemRemoteStore::log_about_cash_event(v4, \&quot;Loaded From Ini File &lt;When Server Started&gt;\&quot;, &amp;v4-&gt;m_cde.m_ini);\n    27\t  CashItemRemoteStore::check_loaded_cde_status(v4);\n    28\t  CLogFile::Write(&amp;v4-&gt;m_cde.m_cde_log, \&quot;Complete For Cash Discount-Rate Event\&quot;);\n    29\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/LoadCUnmannedTraderSchedulerQEAA_NXZ_1403935E0.c\n...\n     5\t\n     6\tchar __fastcall CUnmannedTraderScheduler::Load(CUnmannedTraderScheduler *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v4; // [sp+0h] [bp-148h]@1\n    12\t  char Dst; // [sp+30h] [bp-118h]@4\n    13\t  char v6; // [sp+134h] [bp-14h]@4\n    14\t  CUnmannedTraderScheduler *v7; // [sp+150h] [bp+8h]@1\n    15\t\n    16\t  v7 = this;\n    17\t  v1 = &amp;v4;\n    18\t  for ( i = 80i64; i; --i )\n    19\t  {\n    20\t    *(_DWORD *)v1 = -*********;\n    21\t    v1 = (__int64 *)((char *)v1 + 4);\n    22\t  }\n    23\t  memset_0(&amp;Dst, 0, 0xF8ui64);\n    24\t  v6 = CRFWorldDatabase::Select_UnmannedTraderReservedSchedule(\n    25\t         pkDB,\n    26\t         CUnmannedTraderEnvironmentValue::Unmanned_Trader_Max_Schdule_Cnt,\n    27\t         (_unmannedtrader_reserved_schedule_info *)&amp;Dst);\n    28\t  if ( v6 == 2 )\n    29\t  {\n    30\t    v7-&gt;m_bLoad = 1;\n    31\t    result = 1;\n    32\t  }\n    33\t  else if ( v6 )\n    34\t  {\n    35\t    result = 0;\n    36\t  }\n...\nPath: Decompiled Source Code - IDA Pro/world/ZoneAliveCheckRequestCNetworkEXAEAA_NHPEADZ_1401C3D90.c\n     1\t/*\n     2\t * Function: ?ZoneAliveCheckRequest@CNetworkEX@@AEAA_NHPEAD@Z\n     3\t * Address: 0x1401C3D90\n     4\t */\n     5\t\n     6\tchar __fastcall CNetworkEX::ZoneAliveCheckRequest(CNetworkEX *this, int n, char *pBuf)\n     7\t{\n     8\t  __int64 *v3; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  CBillingManager *v5; // rax@4\n    11\t  __int64 v7; // [sp+0h] [bp-38h]@1\n    12\t  unsigned int *v8; // [sp+20h] [bp-18h]@4\n    13\t\n    14\t  v3 = &amp;v7;\n    15\t  for ( i = 12i64; i; --i )\n    16\t  {\n    17\t    *(_DWORD *)v3 = -*********;\n    18\t    v3 = (__int64 *)((char *)v3 + 4);\n    19\t  }\n    20\t  v8 = (unsigned int *)pBuf;\n    21\t  v5 = CTSingleton&lt;CBillingManager&gt;::Instance();\n    22\t  CBillingManager::SendMsg_ZoneAliveCheck(v5, *v8);\n    23\t  return 1;\n    24\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.c\n...\n     5\t\n     6\tchar __fastcall CNetworkEX::OpenWorldSuccessResult(CNetworkEX *this, unsigned int n, char *pMsg)\n     7\t{\n     8\t  __int64 *v3; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v6; // [sp+0h] [bp-38h]@1\n    12\t  char *v7; // [sp+20h] [bp-18h]@4\n    13\t\n    14\t  v3 = &amp;v6;\n    15\t  for ( i = 12i64; i; --i )\n    16\t  {\n    17\t    *(_DWORD *)v3 = -*********;\n    18\t    v3 = (__int64 *)((char *)v3 + 4);\n    19\t  }\n    20\t  v7 = pMsg;\n    21\t  if ( unk_1799C9AE9 == (unsigned __int8)pMsg[50] )\n    22\t  {\n    23\t    CMainThread::pc_OpenWorldSuccessResult(&amp;g_Main, *v7, v7 + 1, v7 + 33);\n    24\t    result = 1;\n    25\t  }\n    26\t  else\n    27\t  {\n    28\t    MyMessageBox(\n    29\t      \&quot;OpenWorld(Account -&gt; Zone)\&quot;,\n    30\t      \&quot;!!Server type is wrong!!(AC:%d)(ZO:%d)\&quot;,\n    31\t      (unsigned __int8)v7[50],\n    32\t      unk_1799C9AE9);\n    33\t    WriteServerStartHistory(\n    34\t      \&quot;ServerType is Wrong ==&gt; AccountServer(%d) != ZoneServer(%d)\&quot;,\n    35\t      (unsigned __int8)v7[50],\n    36\t      unk_1799C9AE9);\n    37\t    result = 1;\n    38\t  }\n...\nPath: Decompiled Source Code - IDA Pro/world/LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c\n...\n    41\t  v22 = -2i64;\n    42\t  v24 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    43\t  GetPrivateProfileStringA(\&quot;System\&quot;, \&quot;WorldName\&quot;, \&quot;X\&quot;, v25-&gt;m_szWorldName, 0x21u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    44\t  if ( !strcmp_0(v25-&gt;m_szWorldName, \&quot;X\&quot;) )\n    45\t    return 0xFFFFFFFFi64;\n    46\t  M2W(v25-&gt;m_szWorldName, v25-&gt;m_wszWorldName, 0x21u);\n    47\t  v25-&gt;m_bFreeServer = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;FreeServer\&quot;, 0, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    48\t  v25-&gt;m_byWorldType = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;ServerType\&quot;, 2, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    49\t  ReturnedString = 0;\n    50\t  memset(&amp;v10, 0, 8ui64);\n    51\t  GetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ReleaseType\&quot;, \&quot;X\&quot;, &amp;ReturnedString, 9u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    52\t  if ( !_stricmp(&amp;ReturnedString, \&quot;Internal\&quot;) )\n    53\t  {\n    54\t    v25-&gt;m_bReleaseServiceMode = 0;\n    55\t  }\n...\nPath: Decompiled Source Code - IDA Pro/player/SendMsg_PartyJoinJoinerResultCPlayerQEAAXXZ_1400DCE50.c\n...\n    27\t  v10 = (unsigned __int64)&amp;v4 ^ _security_cookie;\n    28\t  _party_join_joiner_result_zocl::_party_join_joiner_result_zocl(&amp;v5);\n    29\t  v6 = CPartyPlayer::GetPtrPartyMember(v11-&gt;m_pPartyMgr);\n    30\t  if ( v6 )\n    31\t  {\n    32\t    v5.byLootShareMode = v11-&gt;m_pPartyMgr-&gt;m_pPartyBoss-&gt;m_byLootShareSystem;\n    33\t    v5.byListNum = CPartyPlayer::GetPopPartyMember(v11-&gt;m_pPartyMgr) - 1;\n    34\t    for ( j = 0; j &lt; (unsigned __int8)v5.byListNum &amp;&amp; v6[j]; ++j )\n    35\t    {\n    36\t      if ( v6[j] != v11-&gt;m_pPartyMgr )\n    37\t      {\n    38\t        v5.List[j].wIndex = v6[j]-&gt;m_id.wIndex;\n    39\t        v5.List[j].dwSerial = v6[j]-&gt;m_id.dwSerial;\n    40\t        strcpy_0(v5.List[j].wszAvatorName, v6[j]-&gt;m_wszName);\n    41\t      }\n    42\t    }\n    43\t    pbyType = 16;\n    44\t    v9 = 7;\n    45\t    v3 = _party_join_joiner_result_zocl::size(&amp;v5);\n    46\t    CNetProcess::LoadSendMsg(unk_1414F2088, v11-&gt;m_ObjID.m_wIndex, &amp;pbyType, &amp;v5.byLootShareMode, v3);\n    47\t  }\n    48\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/Load_Cash_EventCashItemRemoteStoreQEAAXXZ_1402F7F10.c\n...\n    23\t  v6 = (unsigned __int64)&amp;v3 ^ _security_cookie;\n    24\t  for ( j = 0; j &lt; 3; ++j )\n    25\t  {\n    26\t    CNetTimer::BeginTimer(&amp;v7-&gt;m_cash_event[j].m_event_timer, 0x3E8u);\n    27\t    v7-&gt;m_cash_event[j].m_event_inform_before[0] = 1800;\n    28\t    v7-&gt;m_cash_event[j].m_event_inform_before[1] = 300;\n    29\t    CLogFile::Write(&amp;v7-&gt;m_cash_event[j].m_event_log, \&quot;Server Start and LogFile Loaing\&quot;);\n    30\t    CashItemRemoteStore::Set_CashEvent_Status(v7, j, 0);\n    31\t    Get_CashEvent_Name(j, &amp;szEventName);\n    32\t    CashItemRemoteStore::Load_Event_INI(\n    33\t      v7,\n    34\t      &amp;v7-&gt;m_cash_event[j].m_ini,\n    35\t      &amp;v7-&gt;m_cash_event[j].m_event_ini_file_time,\n    36\t      &amp;szEventName);\n    37\t    if ( j == 2 )\n    38\t      CashItemRemoteStore::Load_LimitedSale_Event_INI(\n    39\t        v7,\n    40\t        &amp;v7-&gt;m_cash_event[2].m_ini,\n    41\t        &amp;v7-&gt;m_cash_event[2].m_event_ini_file_time,\n    42\t        &amp;szEventName);\n    43\t    CashItemRemoteStore::Check_Loaded_Event_Status(v7, j);\n    44\t  }\n    45\t}\n...\nPath: Decompiled Source Code - IDA Pro/player/SendMsg_PcRoomErrorCPlayerQEAAXEZ_1400E88A0.c\n     1\t/*\n     2\t * Function: ?SendMsg_PcRoomError@CPlayer@@QEAAXE@Z\n     3\t * Address: 0x1400E88A0\n     4\t */\n     5\t\n     6\tvoid __fastcall CPlayer::SendMsg_PcRoomError(CPlayer *this, char byErrorCode)\n     7\t{\n     8\t  __int64 *v2; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v4; // [sp+0h] [bp-78h]@1\n    11\t  char szMsg; // [sp+34h] [bp-44h]@4\n    12\t  char pbyType; // [sp+54h] [bp-24h]@4\n    13\t  char v7; // [sp+55h] [bp-23h]@4\n    14\t  CPlayer *v8; // [sp+80h] [bp+8h]@1\n    15\t\n    16\t  v8 = this;\n    17\t  v2 = &amp;v4;\n    18\t  for ( i = 28i64; i; --i )\n    19\t  {\n    20\t    *(_DWORD *)v2 = -*********;\n    21\t    v2 = (__int64 *)((char *)v2 + 4);\n    22\t  }\n    23\t  szMsg = byErrorCode;\n    24\t  pbyType = 11;\n    25\t  v7 = 34;\n    26\t  CNetProcess::LoadSendMsg(unk_1414F2088, v8-&gt;m_ObjID.m_wIndex, &amp;pbyType, &amp;szMsg, 1u);\n    27\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/j_ZoneAliveCheckRequestCNetworkEXAEAA_NHPEADZ_14000D19D.c\n     1\t/*\n     2\t * Function: j_?ZoneAliveCheckRequest@CNetworkEX@@AEAA_NHPEAD@Z\n     3\t * Address: 0x14000D19D\n     4\t */\n     5\t\n     6\tbool __fastcall CNetworkEX::ZoneAliveCheckRequest(CNetworkEX *this, int n, char *pBuf)\n     7\t{\n     8\t  return CNetworkEX::ZoneAliveCheckRequest(this, n, pBuf);\n     9\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c\n...\n    29\t  v9 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    30\t  _strdate(&amp;Buffer);\n    31\t  _strtime(&amp;v8);\n    32\t  __trace(\&quot;%s-%s: Open World\&quot;, &amp;Buffer, &amp;v8);\n    33\t  v10-&gt;m_byWorldCode = v11;\n    34\t  WriteServerStartHistory(\&quot;DBInit Begin &gt;&gt; name: %s, ip: %s\&quot;, pszDBNamea, pszDBIPa);\n    35\t  if ( CMainThread::DatabaseInit(v10, pszDBNamea, pszDBIPa) )\n    36\t  {\n    37\t    v10-&gt;m_bWorldOpen = 1;\n    38\t    CMainThread::SerivceSelfStart(v10);\n    39\t    WriteServerStartHistory(\&quot;DBInit Complete &gt;&gt;\&quot;);\n    40\t  }\n    41\t  else\n    42\t  {\n    43\t    WriteServerStartHistory(\&quot;DBInit Fail &gt;&gt;\&quot;);\n    44\t    CLogFile::WriteString(&amp;v10-&gt;m_logSystemError, \&quot;init DB fail\&quot;);\n    45\t    CWnd::SendMessageA(g_pFrame, 0x10u, 0i64, 0i64);\n    46\t  }\n    47\t}\n...\nPath: Decompiled Source Code - IDA Pro/system/SystemLogCAsyncLoggerAEAAXPEBDZZ_1403BFE60.c\n     1\t/*\n     2\t * Function: ?SystemLog@CAsyncLogger@@AEAAXPEBDZZ\n     3\t * Address: 0x1403BFE60\n     4\t */\n     5\t\n     6\tvoid __usercall CAsyncLogger::SystemLog(CAsyncLogger *this@&lt;rcx&gt;, const char *fmt@&lt;rdx&gt;, signed __int64 a3@&lt;rax&gt;, ...)\n     7\t{\n     8\t  void *v3; // rsp@1\n     9\t  __int64 *v4; // rdi@1\n    10\t  signed __int64 i; // rcx@1\n    11\t  int v6; // edi@5\n    12\t  unsigned int v7; // eax@5\n    13\t  const char *v8; // rax@9\n    14\t  __int64 v9; // [sp-20h] [bp-2CF8h]@1\n    15\t  int v10; // [sp+0h] [bp-2CD8h]@5\n    16\t  int v11; // [sp+8h] [bp-2CD0h]@5\n    17\t  int v12; // [sp+10h] [bp-2CC8h]@5\n    18\t  int v13; // [sp+18h] [bp-2CC0h]@5\n    19\t  int v14; // [sp+20h] [bp-2CB8h]@5\n    20\t  int v15; // [sp+28h] [bp-2CB0h]@5\n    21\t  int v16; // [sp+30h] [bp-2CA8h]@5\n    22\t  char DstBuf; // [sp+50h] [bp-2C88h]@5\n    23\t  char v18; // [sp+51h] [bp-2C87h]@5\n    24\t  _SYSTEMTIME SystemTime; // [sp+2C68h] [bp-70h]@5\n    25\t  int v20; // [sp+2C84h] [bp-54h]@5\n    26\t  va_list ArgList; // [sp+2C98h] [bp-40h]@8\n    27\t  int nLen; // [sp+2CA4h] [bp-34h]@8\n    28\t  int v23; // [sp+2CB0h] [bp-28h]@5\n    29\t  int v24; // [sp+2CB4h] [bp-24h]@5\n    30\t  int v25; // [sp+2CB8h] [bp-20h]@5\n    31\t  int v26; // [sp+2CBCh] [bp-1Ch]@5\n    32\t  int v27; // [sp+2CC0h] [bp-18h]@5\n    33\t  int v28; // [sp+2CC4h] [bp-14h]@5\n    34\t  unsigned __int64 v29; // [sp+2CC8h] [bp-10h]@4\n    35\t  CAsyncLogger *v30; // [sp+2CE0h] [bp+8h]@1\n    36\t  char *Format; // [sp+2CE8h] [bp+10h]@0\n    37\t  va_list va; // [sp+2CF0h] [bp+18h]@1\n    38\t\n    39\t  va_start(va, fmt);\n    40\t  v30 = this;\n    41\t  v3 = alloca(a3);\n    42\t  v4 = &amp;v9;\n    43\t  for ( i = 2876i64; i; --i )\n    44\t  {\n    45\t    *(_DWORD *)v4 = -*********;\n    46\t    v4 = (__int64 *)((char *)v4 + 4);\n    47\t  }\n    48\t  v29 = (unsigned __int64)&amp;v9 ^ _security_cookie;\n    49\t  if ( v30-&gt;m_pSystemLogInfo )\n    50\t  {\n    51\t    DstBuf = 0;\n    52\t    memset(&amp;v18, 0, 0x2BFFui64);\n    53\t    SystemTime.wYear = 0;\n    54\t    memset(&amp;SystemTime.wMonth, 0, 0xEui64);\n    55\t    GetLocalTime(&amp;SystemTime);\n    56\t    v23 = SystemTime.wMilliseconds;\n    57\t    v24 = SystemTime.wSecond;\n    58\t    v25 = SystemTime.wMinute;\n    59\t    v6 = SystemTime.wHour;\n    60\t    v26 = SystemTime.wDay;\n    61\t    v27 = SystemTime.wMonth;\n    62\t    v28 = SystemTime.wYear;\n    63\t    v7 = CAsyncLogInfo::GetCount(v30-&gt;m_pSystemLogInfo);\n    64\t    v16 = v23;\n    65\t    v15 = v24;\n    66\t    v14 = v25;\n    67\t    v13 = v6;\n    68\t    v12 = v26;\n    69\t    v11 = v27;\n    70\t    v10 = v28;\n    71\t    v20 = sprintf_s(&amp;DstBuf, 0x2C00ui64, \&quot;%u\\t%04d-%02d-%02d %02d:%02d:%02d.%03d : \&quot;, v7);\n    72\t    if ( v20 &gt; 0 &amp;&amp; v20 &lt; 11264 )\n    73\t    {\n    74\t      ArgList = (va_list)va;\n    75\t      nLen = vsprintf_s(&amp;DstBuf + v20, 11264i64 - v20, Format, (va_list)va);\n    76\t      ArgList = 0i64;\n    77\t      if ( nLen &gt; 0 )\n    78\t      {\n    79\t        nLen += v20;\n    80\t        v8 = CAsyncLogInfo::GetFileName(v30-&gt;m_pSystemLogInfo);\n    81\t        CAsyncLogBufferList::WriteFile(v8, nLen, &amp;DstBuf);\n    82\t      }\n    83\t    }\n    84\t  }\n    85\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/InitialzieTRC_AutoTradeQEAA_NXZ_1402D7E00.c\n...\n    25\t  v9 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    26\t  CreateDirectoryA(\&quot;..\\\\ZoneServerLog\\\\ServiceLog\\\\ATradeTax\&quot;, 0i64);\n    27\t  CreateDirectoryA(\&quot;..\\\\ZoneServerLog\\\\SystemLog\\\\Concession\&quot;, 0i64);\n    28\t  Dest = 0;\n    29\t  memset(&amp;v8, 0, 0x7Fui64);\n    30\t  v3 = GetKorLocalTime();\n    31\t  sprintf(&amp;Dest, \&quot;..\\\\ZoneServerLog\\\\ServiceLog\\\\ATradeTax\\\\atrade_earn_%d_%d.log\&quot;, v10-&gt;m_byRace, v3);\n    32\t  CLogFile::SetWriteLogFile(&amp;v10-&gt;m_serviceLog, &amp;Dest, 1, 0, 1, 1);\n    33\t  v4 = GetKorLocalTime();\n    34\t  sprintf(&amp;Dest, \&quot;..\\\\ZoneServerLog\\\\SystemLog\\\\Concession\\\\system_TRC_%d_%d.log\&quot;, v10-&gt;m_byRace, v4);\n    35\t  CLogFile::SetWriteLogFile(&amp;v10-&gt;m_sysLog, &amp;Dest, 1, 0, 1, 1);\n    36\t  ControllerTaxRate::setLimitTaxRate(&amp;v10-&gt;m_Controller, 0.*********, 0.2);\n    37\t  return 1;\n    38\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/Load_Golden_Box_Item_EventCGoldenBoxItemMgrQEAA_NX_1404121A0.c\n...\n    21\t  CNetTimer::BeginTimer(&amp;v5-&gt;m_golden_box_event.m_event_timer, 0x1D4C0u);\n    22\t  CLogFile::Write(&amp;v5-&gt;m_golden_box_event.m_event_log, \&quot;Server Start and LogFile Loading\&quot;);\n    23\t  CGoldenBoxItemMgr::Set_Event_Status(v5, 0);\n    24\t  if ( CGoldenBoxItemMgr::Load_Event_INI(v5, &amp;v5-&gt;m_golden_box_event.m_ini) )\n    25\t  {\n    26\t    CGoldenBoxItemMgr::Check_Loaded_Event_Status(v5);\n    27\t    result = !CGoldenBoxItemMgr::Get_Event_Status(v5) || CGoldenBoxItemMgr::SetGoldBoxItemIndex(v5);\n    28\t  }\n    29\t  else\n    30\t  {\n    31\t    MyMessageBox(\&quot;CGoldenBoxItemMgr::Load_Event_INI() : \&quot;, \&quot;Load_Golden_Box_Item_Event() Fail!\&quot;);\n    32\t    result = 0;\n    33\t  }\n    34\t  return result;\n    35\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/0BossSchedule_MapQEAAXZ_14041B720.c\n     1\t/*\n     2\t * Function: ??0BossSchedule_Map@@QEAA@XZ\n     3\t * Address: 0x14041B720\n     4\t */\n     5\t\n     6\tvoid __fastcall BossSchedule_Map::BossSchedule_Map(BossSchedule_Map *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v3; // [sp+0h] [bp-28h]@1\n    11\t  BossSchedule_Map *Dst; // [sp+30h] [bp+8h]@1\n    12\t\n    13\t  Dst = this;\n    14\t  v1 = &amp;v3;\n    15\t  for ( i = 8i64; i; --i )\n    16\t  {\n    17\t    *(_DWORD *)v1 = -*********;\n    18\t    v1 = (__int64 *)((char *)v1 + 4);\n    19\t  }\n    20\t  CIniFile::CIniFile(&amp;Dst-&gt;m_INIFile);\n    21\t  memset_0(Dst, 0, 0x198ui64);\n    22\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/j_wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_14000B4BA.c\n     1\t/*\n     2\t * Function: j_?wa_EnterWorld@@YAXPEAU_WA_AVATOR_CODE@@G@Z\n     3\t * Address: 0x14000B4BA\n     4\t */\n     5\t\n     6\tvoid __fastcall wa_EnterWorld(_WA_AVATOR_CODE *pData, unsigned __int16 wZoneIndex)\n     7\t{\n     8\t  wa_EnterWorld(pData, wZoneIndex);\n     9\t}\n...\nPath: Decompiled Source Code - IDA Pro/system/InitCMsgListRACE_BOSS_MSGIEAA_NXZ_14029EE60.c\n     1\t/*\n     2\t * Function: ?Init@CMsgList@RACE_BOSS_MSG@@IEAA_NXZ\n     3\t * Address: 0x14029EE60\n     4\t */\n     5\t\n     6\tchar __fastcall RACE_BOSS_MSG::CMsgList::Init(RACE_BOSS_MSG::CMsgList *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v4; // rax@9\n    12\t  __int64 v5; // [sp+0h] [bp-68h]@1\n    13\t  unsigned int dwID; // [sp+20h] [bp-48h]@6\n    14\t  unsigned int dwIndex; // [sp+24h] [bp-44h]@12\n    15\t  unsigned int j; // [sp+28h] [bp-40h]@15\n    16\t  RACE_BOSS_MSG::CMsg **v9; // [sp+30h] [bp-38h]@6\n    17\t  RACE_BOSS_MSG::CMsg *v10; // [sp+38h] [bp-30h]@11\n    18\t  RACE_BOSS_MSG::CMsg *v11; // [sp+40h] [bp-28h]@8\n    19\t  __int64 v12; // [sp+48h] [bp-20h]@4\n    20\t  unsigned __int64 v13; // [sp+50h] [bp-18h]@6\n    21\t  RACE_BOSS_MSG::CMsg *v14; // [sp+58h] [bp-10h]@9\n    22\t  RACE_BOSS_MSG::CMsgList *v15; // [sp+70h] [bp+8h]@1\n    23\t\n    24\t  v15 = this;\n    25\t  v1 = &amp;v5;\n    26\t  for ( i = 24i64; i; --i )\n    27\t  {\n    28\t    *(_DWORD *)v1 = -*********;\n    29\t    v1 = (__int64 *)((char *)v1 + 4);\n    30\t  }\n    31\t  v12 = -2i64;\n    32\t  if ( v15-&gt;m_uiSize )\n    33\t  {\n    34\t    CNetIndexList::SetList(&amp;v15-&gt;m_kEmptyInxList, v15-&gt;m_uiSize);\n    35\t    CNetIndexList::SetList(&amp;v15-&gt;m_kUseInxList, v15-&gt;m_uiSize);\n    36\t    CNetIndexList::SetList(&amp;v15-&gt;m_kWaitInxList, v15-&gt;m_uiSize);\n    37\t    v13 = v15-&gt;m_uiSize;\n    38\t    v9 = (RACE_BOSS_MSG::CMsg **)operator new[](saturated_mul(8ui64, v13));\n    39\t    v15-&gt;m_ppMsg = v9;\n    40\t    for ( dwID = 0; dwID &lt; v15-&gt;m_uiSize; ++dwID )\n    41\t    {\n    42\t      v11 = (RACE_BOSS_MSG::CMsg *)operator new(0x58ui64);\n    43\t      if ( v11 )\n    44\t      {\n    45\t        RACE_BOSS_MSG::CMsg::CMsg(v11, v15-&gt;m_ucRace, dwID);\n    46\t        v14 = (RACE_BOSS_MSG::CMsg *)v4;\n    47\t      }\n    48\t      else\n    49\t      {\n    50\t        v14 = 0i64;\n    51\t      }\n    52\t      v10 = v14;\n    53\t      v15-&gt;m_ppMsg[dwID] = v14;\n    54\t    }\n...\nPath: Decompiled Source Code - IDA Pro/items/InitTimeItemQEAA_NXZ_14030E160.c\n...\n    25\t  v9 = (unsigned __int64)&amp;v4 ^ _security_cookie;\n    26\t  lpPathName = \&quot;..\\\\ZoneServerLog\\\\SystemLog\&quot;;\n    27\t  CreateDirectoryA(\&quot;..\\\\ZoneServerLog\\\\SystemLog\&quot;, 0i64);\n    28\t  DstBuf = 0;\n    29\t  memset(&amp;v8, 0, 0xFFui64);\n    30\t  *(_DWORD *)bDate = GetKorLocalTime();\n    31\t  sprintf_s(&amp;DstBuf, 0x100ui64, \&quot;%s\\\\TimeItem_%d.log\&quot;, lpPathName);\n    32\t  CLogFile::SetWriteLogFile(&amp;v10-&gt;_kLogger, &amp;DstBuf, 1, 0, 1, 1);\n    33\t  return TimeItem::ReadGoods(v10) != 0;\n    34\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.c\n     1\t/*\n     2\t * Function: ?wa_EnterWorld@@YAXPEAU_WA_AV...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01K3fAwp5HEbK5kLBHVBazG7&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;,&quot;folderRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-15T08:46:53.716Z&quot;,&quot;response_text&quot;:&quot;Let me continue searching for the specific \&quot;RF SERVER NOW LOADING\&quot; message:\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;86e506c5-4135-438d-ba36-09cf2f4366e1&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01KaG5pz9nTHKp8v5CTaUi4x&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;Find the exact string \\\&quot;RF SERVER NOW LOADING\\\&quot; in the codebase\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;Let me search more specifically for the \&quot;RF SERVER NOW LOADING\&quot; message:\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: Decompiled Source Code - IDA Pro/world/pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c\n     1\t/*\n     2\t * Function: ?pc_OpenWorldSuccessResult@CMainThread@@QEAAXEPEAD0@Z\n     3\t * Address: 0x1401F5580\n     4\t */\n     5\t\n     6\tvoid __fastcall CMainThread::pc_OpenWorldSuccessResult(CMainThread *this, char byWorldCode, char *pszDBName, char *pszDBIP)\n     7\t{\n     8\t  __int64 *v4; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v6; // [sp+0h] [bp-178h]@1\n    11\t  char Buffer; // [sp+30h] [bp-148h]@4\n    12\t  char v8; // [sp+D0h] [bp-A8h]@4\n    13\t  unsigned __int64 v9; // [sp+160h] [bp-18h]@4\n    14\t  CMainThread *v10; // [sp+180h] [bp+8h]@1\n    15\t  char v11; // [sp+188h] [bp+10h]@1\n    16\t  char *pszDBNamea; // [sp+190h] [bp+18h]@1\n    17\t  char *pszDBIPa; // [sp+198h] [bp+20h]@1\n    18\t\n    19\t  pszDBIPa = pszDBIP;\n    20\t  pszDBNamea = pszDBName;\n    21\t  v11 = byWorldCode;\n    22\t  v10 = this;\n    23\t  v4 = &amp;v6;\n    24\t  for ( i = 92i64; i; --i )\n    25\t  {\n    26\t    *(_DWORD *)v4 = -*********;\n    27\t    v4 = (__int64 *)((char *)v4 + 4);\n    28\t  }\n    29\t  v9 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    30\t  _strdate(&amp;Buffer);\n    31\t  _strtime(&amp;v8);\n    32\t  __trace(\&quot;%s-%s: Open World\&quot;, &amp;Buffer, &amp;v8);\n    33\t  v10-&gt;m_byWorldCode = v11;\n    34\t  WriteServerStartHistory(\&quot;DBInit Begin &gt;&gt; name: %s, ip: %s\&quot;, pszDBNamea, pszDBIPa);\n    35\t  if ( CMainThread::DatabaseInit(v10, pszDBNamea, pszDBIPa) )\n    36\t  {\n    37\t    v10-&gt;m_bWorldOpen = 1;\n    38\t    CMainThread::SerivceSelfStart(v10);\n    39\t    WriteServerStartHistory(\&quot;DBInit Complete &gt;&gt;\&quot;);\n    40\t  }\n    41\t  else\n    42\t  {\n    43\t    WriteServerStartHistory(\&quot;DBInit Fail &gt;&gt;\&quot;);\n    44\t    CLogFile::WriteString(&amp;v10-&gt;m_logSystemError, \&quot;init DB fail\&quot;);\n    45\t    CWnd::SendMessageA(g_pFrame, 0x10u, 0i64, 0i64);\n    46\t  }\n    47\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/ZoneAliveCheckRequestCNetworkEXAEAA_NHPEADZ_1401C3D90.c\n     1\t/*\n     2\t * Function: ?ZoneAliveCheckRequest@CNetworkEX@@AEAA_NHPEAD@Z\n     3\t * Address: 0x1401C3D90\n     4\t */\n     5\t\n     6\tchar __fastcall CNetworkEX::ZoneAliveCheckRequest(CNetworkEX *this, int n, char *pBuf)\n     7\t{\n     8\t  __int64 *v3; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  CBillingManager *v5; // rax@4\n    11\t  __int64 v7; // [sp+0h] [bp-38h]@1\n    12\t  unsigned int *v8; // [sp+20h] [bp-18h]@4\n    13\t\n    14\t  v3 = &amp;v7;\n    15\t  for ( i = 12i64; i; --i )\n    16\t  {\n    17\t    *(_DWORD *)v3 = -*********;\n    18\t    v3 = (__int64 *)((char *)v3 + 4);\n    19\t  }\n    20\t  v8 = (unsigned int *)pBuf;\n    21\t  v5 = CTSingleton&lt;CBillingManager&gt;::Instance();\n    22\t  CBillingManager::SendMsg_ZoneAliveCheck(v5, *v8);\n    23\t  return 1;\n    24\t}\n...\nPath: Decompiled Source Code - IDA Pro/system/SetNetSystemCNetWorkingQEAA_NKPEAU_NET_TYPE_PARAMP_1404813E0.c\n...\n    35\t  v20 = (unsigned __int64)&amp;v10 ^ _security_cookie;\n    36\t  pNetwork-&gt;m_dwUseProcessNum = dwUseProcessNum;\n    37\t  wsprintfA(pNetwork-&gt;m_szSystemName, \&quot;%s\&quot;, szSystemName);\n    38\t  if ( pszLogPath )\n    39\t    strcpy_s(pNetwork-&gt;m_szLogPath, 0x80ui64, pszLogPath);\n    40\t  else\n    41\t    strcpy_s(pNetwork-&gt;m_szLogPath, 0x80ui64, \&quot;.\\\\NetLog\&quot;);\n    42\t  CreateDirectoryA(pNetwork-&gt;m_szLogPath, 0i64);\n    43\t  *(_DWORD *)bDate = GetKorLocalTime();\n    44\t  wsprintfA(&amp;szFileName, \&quot;%s\\\\%s_Sys%d.log\&quot;, pNetwork-&gt;m_szLogPath, pNetwork-&gt;m_szSystemName);\n    45\t  CLogFile::SetWriteLogFile(&amp;pNetwork-&gt;m_LogFile, &amp;szFileName, 1, 1, 1, 1);\n    46\t  *(_DWORD *)bDate = GetKorLocalTime();\n    47\t  wsprintfA(&amp;szFileName, \&quot;%s\\\\%s_CcrFgSys%d.log\&quot;, pNetwork-&gt;m_szLogPath, pNetwork-&gt;m_szSystemName);\n    48\t  CLogFile::SetWriteLogFile(&amp;g_FgLogFile, &amp;szFileName, 1, 1, 1, 1);\n    49\t  ReturnedString = 0;\n    50\t  memset(&amp;v14, 0, 0xFui64);\n    51\t  GetPrivateProfileStringA(\&quot;FireGuard Use\&quot;, \&quot;Use\&quot;, \&quot;TRUE\&quot;, &amp;ReturnedString, 0x10u, \&quot;.\\\\fireguard\\\\fgrs.ini\&quot;);\n    52\t  pNetwork-&gt;m_bUseFG = strcmp_0(&amp;ReturnedString, \&quot;FALSE\&quot;) != 0;\n    53\t  GetPrivateProfileStringA(\&quot;System\&quot;, \&quot;WorldName\&quot;, \&quot;X\&quot;, pNetwork-&gt;m_szServerName, 0x21u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    54\t  if ( pNetwork-&gt;m_szServerName[0] == 88 )\n    55\t  {\n    56\t    CLogFile::Write(&amp;g_FgLogFile, \&quot;FG Error : WorldName Read Failed In \\\&quot;WorldInfo.ini\\\&quot; File\&quot;);\n    57\t    result = 0;\n    58\t  }\n    59\t  else\n    60\t  {\n    61\t    for ( nIndex = 0; nIndex &lt; pNetwork-&gt;m_dwUseProcessNum; ++nIndex )\n    62\t    {\n    63\t      bDate[0] = pNetwork-&gt;m_bUseFG;\n    64\t      if ( !CNetProcess::SetProcess(&amp;pNetwork-&gt;m_Process[nIndex], nIndex, &amp;v22[nIndex], pNetwork, bDate[0]) )\n    65\t        return 0;\n    66\t      if ( !nIndex )\n    67\t      {\n    68\t        Filename = 0;\n    69\t        memset(&amp;v17, 0, 0x103ui64);\n    70\t        GetModuleFileNameA(0i64, &amp;Filename, 0x104u);\n    71\t        v18 = _tcsrchr(&amp;Filename, 0x5Cu) + 1;\n    72\t        *v18 = 0;\n    73\t        strcat_s(&amp;Filename, 0x104ui64, \&quot;fireguard\\\\\&quot;);\n    74\t        v8 = strlen_0(&amp;Filename);\n    75\t        if ( AddEnvVariable(\&quot;path\&quot;, &amp;Filename, v8) &lt;= 0 )\n    76\t        {\n    77\t          CLogFile::Write(\n    78\t            &amp;pNetwork-&gt;m_LogFile,\n    79\t            \&quot;SetNetSystem(%d) CCRFG SERVER : AddEnvVariable() Fail\&quot;,\n    80\t            (unsigned int)nIndex);\n    81\t          return 0;\n    82\t        }\n...\nPath: Decompiled Source Code - IDA Pro/world/j_size_server_notify_inform_zoneQEAAHXZ_140005187.c\n     1\t/*\n     2\t * Function: j_?size@_server_notify_inform_zone@@QEAAHXZ\n     3\t * Address: 0x140005187\n     4\t */\n     5\t\n     6\tint __fastcall _server_notify_inform_zone::size(_server_notify_inform_zone *this)\n     7\t{\n     8\t  return _server_notify_inform_zone::size(this);\n     9\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.c\n...\n     5\t\n     6\tchar __fastcall CNetworkEX::OpenWorldSuccessResult(CNetworkEX *this, unsigned int n, char *pMsg)\n     7\t{\n     8\t  __int64 *v3; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v6; // [sp+0h] [bp-38h]@1\n    12\t  char *v7; // [sp+20h] [bp-18h]@4\n    13\t\n    14\t  v3 = &amp;v6;\n    15\t  for ( i = 12i64; i; --i )\n    16\t  {\n    17\t    *(_DWORD *)v3 = -*********;\n    18\t    v3 = (__int64 *)((char *)v3 + 4);\n    19\t  }\n    20\t  v7 = pMsg;\n    21\t  if ( unk_1799C9AE9 == (unsigned __int8)pMsg[50] )\n    22\t  {\n    23\t    CMainThread::pc_OpenWorldSuccessResult(&amp;g_Main, *v7, v7 + 1, v7 + 33);\n    24\t    result = 1;\n    25\t  }\n    26\t  else\n    27\t  {\n    28\t    MyMessageBox(\n    29\t      \&quot;OpenWorld(Account -&gt; Zone)\&quot;,\n    30\t      \&quot;!!Server type is wrong!!(AC:%d)(ZO:%d)\&quot;,\n    31\t      (unsigned __int8)v7[50],\n    32\t      unk_1799C9AE9);\n    33\t    WriteServerStartHistory(\n    34\t      \&quot;ServerType is Wrong ==&gt; AccountServer(%d) != ZoneServer(%d)\&quot;,\n    35\t      (unsigned __int8)v7[50],\n    36\t      unk_1799C9AE9);\n    37\t    result = 1;\n    38\t  }\n...\nPath: Decompiled Source Code - IDA Pro/world/LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c\n...\n    41\t  v22 = -2i64;\n    42\t  v24 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    43\t  GetPrivateProfileStringA(\&quot;System\&quot;, \&quot;WorldName\&quot;, \&quot;X\&quot;, v25-&gt;m_szWorldName, 0x21u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    44\t  if ( !strcmp_0(v25-&gt;m_szWorldName, \&quot;X\&quot;) )\n    45\t    return 0xFFFFFFFFi64;\n    46\t  M2W(v25-&gt;m_szWorldName, v25-&gt;m_wszWorldName, 0x21u);\n    47\t  v25-&gt;m_bFreeServer = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;FreeServer\&quot;, 0, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    48\t  v25-&gt;m_byWorldType = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;ServerType\&quot;, 2, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    49\t  ReturnedString = 0;\n    50\t  memset(&amp;v10, 0, 8ui64);\n    51\t  GetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ReleaseType\&quot;, \&quot;X\&quot;, &amp;ReturnedString, 9u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    52\t  if ( !_stricmp(&amp;ReturnedString, \&quot;Internal\&quot;) )\n    53\t  {\n    54\t    v25-&gt;m_bReleaseServiceMode = 0;\n    55\t  }\n    56\t  else\n    57\t  {\n    58\t    if ( _stricmp(&amp;ReturnedString, \&quot;Release\&quot;) )\n    59\t    {\n    60\t      MyMessageBox(\n    61\t        \&quot;CMainThread::LoadWorldSystemINI()\&quot;,\n    62\t        \&quot;WorldInfo.ini\\r\\n[ServerMode]\\r\\nReleaseType = %s Invalid!!\&quot;,\n    63\t        &amp;ReturnedString);\n    64\t      return 4294967294i64;\n    65\t    }\n    66\t    v25-&gt;m_bReleaseServiceMode = 1;\n    67\t  }\n    68\t  Str1 = 0;\n    69\t  memset(&amp;v12, 0, 5ui64);\n    70\t  GetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ExcuteService\&quot;, \&quot;X\&quot;, &amp;Str1, 6u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    71\t  if ( !_stricmp(&amp;Str1, \&quot;true\&quot;) )\n    72\t  {\n    73\t    v25-&gt;m_bExcuteService = 1;\n    74\t  }\n    75\t  else\n    76\t  {\n    77\t    if ( _stricmp(&amp;Str1, \&quot;false\&quot;) )\n    78\t    {\n    79\t      MyMessageBox(\n    80\t        \&quot;CMainThread::LoadWorldSystemINI()\&quot;,\n    81\t        \&quot;WorldInfo.ini\\r\\n[ServerMode]\\r\\nExcuteService = %s Invalid!!\&quot;,\n    82\t        &amp;Str1);\n    83\t      return 4294967293i64;\n    84\t    }\n    85\t    v25-&gt;m_bExcuteService = 0;\n    86\t  }\n...\nPath: Decompiled Source Code - IDA Pro/world/size_server_notify_inform_zoneQEAAHXZ_14011F1E0.c\n     1\t/*\n     2\t * Function: ?size@_server_notify_inform_zone@@QEAAHXZ\n     3\t * Address: 0x14011F1E0\n     4\t */\n     5\t\n     6\tsigned __int64 __fastcall _server_notify_inform_zone::size(_server_notify_inform_zone *this)\n     7\t{\n     8\t  return 6i64;\n     9\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/start_cdeCashItemRemoteStoreQEAA_NHHHHZ_1402F7590.c\n...\n    64\t    v19-&gt;m_cde.m_ini.m_byHour[1] = Tm.tm_hour;\n    65\t    v19-&gt;m_cde.m_ini.m_byMinute[1] = Tm.tm_min;\n    66\t    CashItemRemoteStore::set_cde_status(v19, 1);\n    67\t    CashItemRemoteStore::log_about_cash_event(v19, \&quot;Loaded From Cheat-Args &lt;When Server Running&gt;\&quot;, &amp;v19-&gt;m_cde.m_ini);\n    68\t    result = 1;\n    69\t  }\n    70\t  return result;\n    71\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/load_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F5E30.c\n...\n    20\t  CLogFile::Write(&amp;v4-&gt;m_cde.m_cde_log, \&quot;Loading Cash Discount-Rate Event\&quot;);\n    21\t  CNetTimer::BeginTimer(&amp;v4-&gt;m_cde.m_cde_timer, 0x3E8u);\n    22\t  v4-&gt;m_cde.m_cde_inform_before[0] = 1800;\n    23\t  v4-&gt;m_cde.m_cde_inform_before[1] = 300;\n    24\t  v4-&gt;m_cde.m_cde_status = 0;\n    25\t  CashItemRemoteStore::load_cde_ini(v4, &amp;v4-&gt;m_cde.m_ini, &amp;v4-&gt;m_cde.m_cde_ini_file_time);\n    26\t  CashItemRemoteStore::log_about_cash_event(v4, \&quot;Loaded From Ini File &lt;When Server Started&gt;\&quot;, &amp;v4-&gt;m_cde.m_ini);\n    27\t  CashItemRemoteStore::check_loaded_cde_status(v4);\n    28\t  CLogFile::Write(&amp;v4-&gt;m_cde.m_cde_log, \&quot;Complete For Cash Discount-Rate Event\&quot;);\n    29\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/Load_Golden_Box_Item_EventCGoldenBoxItemMgrQEAA_NX_1404121A0.c\n...\n    21\t  CNetTimer::BeginTimer(&amp;v5-&gt;m_golden_box_event.m_event_timer, 0x1D4C0u);\n    22\t  CLogFile::Write(&amp;v5-&gt;m_golden_box_event.m_event_log, \&quot;Server Start and LogFile Loading\&quot;);\n    23\t  CGoldenBoxItemMgr::Set_Event_Status(v5, 0);\n    24\t  if ( CGoldenBoxItemMgr::Load_Event_INI(v5, &amp;v5-&gt;m_golden_box_event.m_ini) )\n    25\t  {\n    26\t    CGoldenBoxItemMgr::Check_Loaded_Event_Status(v5);\n    27\t    result = !CGoldenBoxItemMgr::Get_Event_Status(v5) || CGoldenBoxItemMgr::SetGoldBoxItemIndex(v5);\n    28\t  }\n    29\t  else\n    30\t  {\n    31\t    MyMessageBox(\&quot;CGoldenBoxItemMgr::Load_Event_INI() : \&quot;, \&quot;Load_Golden_Box_Item_Event() Fail!\&quot;);\n    32\t    result = 0;\n    33\t  }\n    34\t  return result;\n    35\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/Load_Cash_EventCashItemRemoteStoreQEAAXXZ_1402F7F10.c\n...\n    23\t  v6 = (unsigned __int64)&amp;v3 ^ _security_cookie;\n    24\t  for ( j = 0; j &lt; 3; ++j )\n    25\t  {\n    26\t    CNetTimer::BeginTimer(&amp;v7-&gt;m_cash_event[j].m_event_timer, 0x3E8u);\n    27\t    v7-&gt;m_cash_event[j].m_event_inform_before[0] = 1800;\n    28\t    v7-&gt;m_cash_event[j].m_event_inform_before[1] = 300;\n    29\t    CLogFile::Write(&amp;v7-&gt;m_cash_event[j].m_event_log, \&quot;Server Start and LogFile Loaing\&quot;);\n    30\t    CashItemRemoteStore::Set_CashEvent_Status(v7, j, 0);\n    31\t    Get_CashEvent_Name(j, &amp;szEventName);\n    32\t    CashItemRemoteStore::Load_Event_INI(\n    33\t      v7,\n    34\t      &amp;v7-&gt;m_cash_event[j].m_ini,\n    35\t      &amp;v7-&gt;m_cash_event[j].m_event_ini_file_time,\n    36\t      &amp;szEventName);\n    37\t    if ( j == 2 )\n    38\t      CashItemRemoteStore::Load_LimitedSale_Event_INI(\n    39\t        v7,\n    40\t        &amp;v7-&gt;m_cash_event[2].m_ini,\n    41\t        &amp;v7-&gt;m_cash_event[2].m_event_ini_file_time,\n    42\t        &amp;szEventName);\n    43\t    CashItemRemoteStore::Check_Loaded_Event_Status(v7, j);\n    44\t  }\n    45\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/LoadCUnmannedTraderSchedulerQEAA_NXZ_1403935E0.c\n...\n     5\t\n     6\tchar __fastcall CUnmannedTraderScheduler::Load(CUnmannedTraderScheduler *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v4; // [sp+0h] [bp-148h]@1\n    12\t  char Dst; // [sp+30h] [bp-118h]@4\n    13\t  char v6; // [sp+134h] [bp-14h]@4\n    14\t  CUnmannedTraderScheduler *v7; // [sp+150h] [bp+8h]@1\n    15\t\n    16\t  v7 = this;\n    17\t  v1 = &amp;v4;\n    18\t  for ( i = 80i64; i; --i )\n    19\t  {\n    20\t    *(_DWORD *)v1 = -*********;\n    21\t    v1 = (__int64 *)((char *)v1 + 4);\n    22\t  }\n    23\t  memset_0(&amp;Dst, 0, 0xF8ui64);\n    24\t  v6 = CRFWorldDatabase::Select_UnmannedTraderReservedSchedule(\n    25\t         pkDB,\n    26\t         CUnmannedTraderEnvironmentValue::Unmanned_Trader_Max_Schdule_Cnt,\n    27\t         (_unmannedtrader_reserved_schedule_info *)&amp;Dst);\n    28\t  if ( v6 == 2 )\n    29\t  {\n    30\t    v7-&gt;m_bLoad = 1;\n    31\t    result = 1;\n    32\t  }\n    33\t  else if ( v6 )\n    34\t  {\n    35\t    result = 0;\n    36\t  }\n...\nPath: Decompiled Source Code - IDA Pro/system/SystemLogCAsyncLoggerAEAAXPEBDZZ_1403BFE60.c\n     1\t/*\n     2\t * Function: ?SystemLog@CAsyncLogger@@AEAAXPEBDZZ\n     3\t * Address: 0x1403BFE60\n     4\t */\n     5\t\n     6\tvoid __usercall CAsyncLogger::SystemLog(CAsyncLogger *this@&lt;rcx&gt;, const char *fmt@&lt;rdx&gt;, signed __int64 a3@&lt;rax&gt;, ...)\n     7\t{\n     8\t  void *v3; // rsp@1\n     9\t  __int64 *v4; // rdi@1\n    10\t  signed __int64 i; // rcx@1\n    11\t  int v6; // edi@5\n    12\t  unsigned int v7; // eax@5\n    13\t  const char *v8; // rax@9\n    14\t  __int64 v9; // [sp-20h] [bp-2CF8h]@1\n    15\t  int v10; // [sp+0h] [bp-2CD8h]@5\n    16\t  int v11; // [sp+8h] [bp-2CD0h]@5\n    17\t  int v12; // [sp+10h] [bp-2CC8h]@5\n    18\t  int v13; // [sp+18h] [bp-2CC0h]@5\n    19\t  int v14; // [sp+20h] [bp-2CB8h]@5\n    20\t  int v15; // [sp+28h] [bp-2CB0h]@5\n    21\t  int v16; // [sp+30h] [bp-2CA8h]@5\n    22\t  char DstBuf; // [sp+50h] [bp-2C88h]@5\n    23\t  char v18; // [sp+51h] [bp-2C87h]@5\n    24\t  _SYSTEMTIME SystemTime; // [sp+2C68h] [bp-70h]@5\n    25\t  int v20; // [sp+2C84h] [bp-54h]@5\n    26\t  va_list ArgList; // [sp+2C98h] [bp-40h]@8\n    27\t  int nLen; // [sp+2CA4h] [bp-34h]@8\n    28\t  int v23; // [sp+2CB0h] [bp-28h]@5\n    29\t  int v24; // [sp+2CB4h] [bp-24h]@5\n    30\t  int v25; // [sp+2CB8h] [bp-20h]@5\n    31\t  int v26; // [sp+2CBCh] [bp-1Ch]@5\n    32\t  int v27; // [sp+2CC0h] [bp-18h]@5\n    33\t  int v28; // [sp+2CC4h] [bp-14h]@5\n    34\t  unsigned __int64 v29; // [sp+2CC8h] [bp-10h]@4\n    35\t  CAsyncLogger *v30; // [sp+2CE0h] [bp+8h]@1\n    36\t  char *Format; // [sp+2CE8h] [bp+10h]@0\n    37\t  va_list va; // [sp+2CF0h] [bp+18h]@1\n    38\t\n    39\t  va_start(va, fmt);\n    40\t  v30 = this;\n    41\t  v3 = alloca(a3);\n    42\t  v4 = &amp;v9;\n    43\t  for ( i = 2876i64; i; --i )\n    44\t  {\n    45\t    *(_DWORD *)v4 = -*********;\n    46\t    v4 = (__int64 *)((char *)v4 + 4);\n    47\t  }\n    48\t  v29 = (unsigned __int64)&amp;v9 ^ _security_cookie;\n    49\t  if ( v30-&gt;m_pSystemLogInfo )\n    50\t  {\n    51\t    DstBuf = 0;\n    52\t    memset(&amp;v18, 0, 0x2BFFui64);\n    53\t    SystemTime.wYear = 0;\n    54\t    memset(&amp;SystemTime.wMonth, 0, 0xEui64);\n    55\t    GetLocalTime(&amp;SystemTime);\n    56\t    v23 = SystemTime.wMilliseconds;\n    57\t    v24 = SystemTime.wSecond;\n    58\t    v25 = SystemTime.wMinute;\n    59\t    v6 = SystemTime.wHour;\n    60\t    v26 = SystemTime.wDay;\n    61\t    v27 = SystemTime.wMonth;\n    62\t    v28 = SystemTime.wYear;\n    63\t    v7 = CAsyncLogInfo::GetCount(v30-&gt;m_pSystemLogInfo);\n    64\t    v16 = v23;\n    65\t    v15 = v24;\n    66\t    v14 = v25;\n    67\t    v13 = v6;\n    68\t    v12 = v26;\n    69\t    v11 = v27;\n    70\t    v10 = v28;\n    71\t    v20 = sprintf_s(&amp;DstBuf, 0x2C00ui64, \&quot;%u\\t%04d-%02d-%02d %02d:%02d:%02d.%03d : \&quot;, v7);\n    72\t    if ( v20 &gt; 0 &amp;&amp; v20 &lt; 11264 )\n    73\t    {\n    74\t      ArgList = (va_list)va;\n    75\t      nLen = vsprintf_s(&amp;DstBuf + v20, 11264i64 - v20, Format, (va_list)va);\n    76\t      ArgList = 0i64;\n    77\t      if ( nLen &gt; 0 )\n    78\t      {\n    79\t        nLen += v20;\n    80\t        v8 = CAsyncLogInfo::GetFileName(v30-&gt;m_pSystemLogInfo);\n    81\t        CAsyncLogBufferList::WriteFile(v8, nLen, &amp;DstBuf);\n    82\t      }\n    83\t    }\n    84\t  }\n    85\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/0BossSchedule_MapQEAAXZ_14041B720.c\n     1\t/*\n     2\t * Function: ??0BossSchedule_Map@@QEAA@XZ\n     3\t * Address: 0x14041B720\n     4\t */\n     5\t\n     6\tvoid __fastcall BossSchedule_Map::BossSchedule_Map(BossSchedule_Map *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v3; // [sp+0h] [bp-28h]@1\n    11\t  BossSchedule_Map *Dst; // [sp+30h] [bp+8h]@1\n    12\t\n    13\t  Dst = this;\n    14\t  v1 = &amp;v3;\n    15\t  for ( i = 8i64; i; --i )\n    16\t  {\n    17\t    *(_DWORD *)v1 = -*********;\n    18\t    v1 = (__int64 *)((char *)v1 + 4);\n    19\t  }\n    20\t  CIniFile::CIniFile(&amp;Dst-&gt;m_INIFile);\n    21\t  memset_0(Dst, 0, 0x198ui64);\n    22\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/InitialzieTRC_AutoTradeQEAA_NXZ_1402D7E00.c\n...\n    25\t  v9 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    26\t  CreateDirectoryA(\&quot;..\\\\ZoneServerLog\\\\ServiceLog\\\\ATradeTax\&quot;, 0i64);\n    27\t  CreateDirectoryA(\&quot;..\\\\ZoneServerLog\\\\SystemLog\\\\Concession\&quot;, 0i64);\n    28\t  Dest = 0;\n    29\t  memset(&amp;v8, 0, 0x7Fui64);\n    30\t  v3 = GetKorLocalTime();\n    31\t  sprintf(&amp;Dest, \&quot;..\\\\ZoneServerLog\\\\ServiceLog\\\\ATradeTax\\\\atrade_earn_%d_%d.log\&quot;, v10-&gt;m_byRace, v3);\n    32\t  CLogFile::SetWriteLogFile(&amp;v10-&gt;m_serviceLog, &amp;Dest, 1, 0, 1, 1);\n    33\t  v4 = GetKorLocalTime();\n    34\t  sprintf(&amp;Dest, \&quot;..\\\\ZoneServerLog\\\\SystemLog\\\\Concession\\\\system_TRC_%d_%d.log\&quot;, v10-&gt;m_byRace, v4);\n    35\t  CLogFile::SetWriteLogFile(&amp;v10-&gt;m_sysLog, &amp;Dest, 1, 0, 1, 1);\n    36\t  ControllerTaxRate::setLimitTaxRate(&amp;v10-&gt;m_Controller, 0.*********, 0.2);\n    37\t  return 1;\n    38\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.c\n     1\t/*\n     2\t * Function: ?wa_EnterWorld@@YAXPEAU_WA_AVATOR_CODE@@G@Z\n     3\t * Address: 0x140046110\n     4\t */\n     5\t\n     6\tvoid __fastcall wa_EnterWorld(_WA_AVATOR_CODE *pData, unsigned __int16 wZoneIndex)\n     7\t{\n     8\t  __int64 *v2; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v4; // [sp+0h] [bp-38h]@1\n    11\t  CPartyPlayer *v5; // [sp+20h] [bp-18h]@4\n    12\t  _WA_AVATOR_CODE *pDataa; // [sp+40h] [bp+8h]@1\n    13\t\n    14\t  pDataa = pData;\n    15\t  v2 = &amp;v4;\n    16\t  for ( i = 12i64; i; --i )\n    17\t  {\n    18\t    *(_DWORD *)v2 = -*********;\n    19\t    v2 = (__int64 *)((char *)v2 + 4);\n    20\t  }\n    21\t  v5 = (CPartyPlayer *)((char *)&amp;g_PartyPlayer + 128 * (unsigned __int64)pDataa-&gt;m_id.wIndex);\n    22\t  if ( !v5-&gt;m_bLogin )\n    23\t    CPartyPlayer::EnterWorld(v5, pDataa, wZoneIndex);\n    24\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/InitTimeItemQEAA_NXZ_14030E160.c\n...\n    25\t  v9 = (unsigned __int64)&amp;v4 ^ _security_cookie;\n    26\t  lpPathName = \&quot;..\\\\ZoneServerLog\\\\SystemLog\&quot;;\n    27\t  CreateDirectoryA(\&quot;..\\\\ZoneServerLog\\\\SystemLog\&quot;, 0i64);\n    28\t  DstBuf = 0;\n    29\t  memset(&amp;v8, 0, 0xFFui64);\n    30\t  *(_DWORD *)bDate = GetKorLocalTime();\n    31\t  sprintf_s(&amp;DstBuf, 0x100ui64, \&quot;%s\\\\TimeItem_%d.log\&quot;, lpPathName);\n    32\t  CLogFile::SetWriteLogFile(&amp;v10-&gt;_kLogger, &amp;DstBuf, 1, 0, 1, 1);\n    33\t  return TimeItem::ReadGoods(v10) != 0;\n    34\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/j_LoadWorldIn...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_0181qRsSGXjjTPRjLdSJ2QJR&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;,&quot;folderRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-15T08:47:18.718Z&quot;,&quot;response_text&quot;:&quot;Let me search more specifically for the \&quot;RF SERVER NOW LOADING\&quot; message:\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;708e822b-c3b7-459d-8cae-88af11d17836&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01BfMfHuj83Mnt1tCbW91KxT&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;Decompiled Source Code - IDA Pro\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;Let me search more specifically for the exact string \&quot;RF SERVER NOW LOADING\&quot;:\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: Decompiled Source Code - IDA Pro/system/SetNetSystemCNetWorkingQEAA_NKPEAU_NET_TYPE_PARAMP_1404813E0.c\n...\n    50\t  memset(&amp;v14, 0, 0xFui64);\n    51\t  GetPrivateProfileStringA(\&quot;FireGuard Use\&quot;, \&quot;Use\&quot;, \&quot;TRUE\&quot;, &amp;ReturnedString, 0x10u, \&quot;.\\\\fireguard\\\\fgrs.ini\&quot;);\n    52\t  pNetwork-&gt;m_bUseFG = strcmp_0(&amp;ReturnedString, \&quot;FALSE\&quot;) != 0;\n    53\t  GetPrivateProfileStringA(\&quot;System\&quot;, \&quot;WorldName\&quot;, \&quot;X\&quot;, pNetwork-&gt;m_szServerName, 0x21u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    54\t  if ( pNetwork-&gt;m_szServerName[0] == 88 )\n    55\t  {\n    56\t    CLogFile::Write(&amp;g_FgLogFile, \&quot;FG Error : WorldName Read Failed In \\\&quot;WorldInfo.ini\\\&quot; File\&quot;);\n    57\t    result = 0;\n    58\t  }\n...\nPath: Decompiled Source Code - IDA Pro/world/LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c\n...\n    41\t  v22 = -2i64;\n    42\t  v24 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    43\t  GetPrivateProfileStringA(\&quot;System\&quot;, \&quot;WorldName\&quot;, \&quot;X\&quot;, v25-&gt;m_szWorldName, 0x21u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    44\t  if ( !strcmp_0(v25-&gt;m_szWorldName, \&quot;X\&quot;) )\n    45\t    return 0xFFFFFFFFi64;\n    46\t  M2W(v25-&gt;m_szWorldName, v25-&gt;m_wszWorldName, 0x21u);\n    47\t  v25-&gt;m_bFreeServer = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;FreeServer\&quot;, 0, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    48\t  v25-&gt;m_byWorldType = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;ServerType\&quot;, 2, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    49\t  ReturnedString = 0;\n    50\t  memset(&amp;v10, 0, 8ui64);\n    51\t  GetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ReleaseType\&quot;, \&quot;X\&quot;, &amp;ReturnedString, 9u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    52\t  if ( !_stricmp(&amp;ReturnedString, \&quot;Internal\&quot;) )\n    53\t  {\n    54\t    v25-&gt;m_bReleaseServiceMode = 0;\n    55\t  }\n    56\t  else\n    57\t  {\n    58\t    if ( _stricmp(&amp;ReturnedString, \&quot;Release\&quot;) )\n    59\t    {\n    60\t      MyMessageBox(\n    61\t        \&quot;CMainThread::LoadWorldSystemINI()\&quot;,\n    62\t        \&quot;WorldInfo.ini\\r\\n[ServerMode]\\r\\nReleaseType = %s Invalid!!\&quot;,\n    63\t        &amp;ReturnedString);\n    64\t      return 4294967294i64;\n    65\t    }\n    66\t    v25-&gt;m_bReleaseServiceMode = 1;\n    67\t  }\n    68\t  Str1 = 0;\n    69\t  memset(&amp;v12, 0, 5ui64);\n    70\t  GetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ExcuteService\&quot;, \&quot;X\&quot;, &amp;Str1, 6u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    71\t  if ( !_stricmp(&amp;Str1, \&quot;true\&quot;) )\n    72\t  {\n    73\t    v25-&gt;m_bExcuteService = 1;\n    74\t  }\n    75\t  else\n    76\t  {\n    77\t    if ( _stricmp(&amp;Str1, \&quot;false\&quot;) )\n    78\t    {\n    79\t      MyMessageBox(\n    80\t        \&quot;CMainThread::LoadWorldSystemINI()\&quot;,\n    81\t        \&quot;WorldInfo.ini\\r\\n[ServerMode]\\r\\nExcuteService = %s Invalid!!\&quot;,\n    82\t        &amp;Str1);\n    83\t      return 4294967293i64;\n    84\t    }\n    85\t    v25-&gt;m_bExcuteService = 0;\n    86\t  }\n...\nPath: Decompiled Source Code - IDA Pro/world/LoadEventSetLootingCMonsterEventSetQEAA_NXZ_1402A91E0.c\n...\n    49\t  v32 = (unsigned __int64)&amp;v13 ^ _security_cookie;\n    50\t  v14 = 0;\n    51\t  ppszDst = &amp;Dst;\n    52\t  Str = &amp;v18;\n    53\t  v24 = &amp;v19;\n    54\t  v25 = &amp;v20;\n    55\t  v26 = &amp;v21;\n    56\t  if ( GetLastWriteFileTime(\&quot;.\\\\Initialize\\\\EventSetLooting.ini\&quot;, &amp;ftWrite) )\n    57\t  {\n    58\t    v33-&gt;m_ftLootingWrite = ftWrite;\n    59\t    memset_0(v33-&gt;m_EventSetLootingList, 0, 0x57670ui64);\n    60\t    v28 = 0;\n    61\t    if ( fopen_s(&amp;File, \&quot;.\\\\Initialize\\\\EventSetLooting.ini\&quot;, \&quot;r+t\&quot;) )\n    62\t    {\n    63\t      CLogFile::Write(\n    64\t        &amp;stru_1799C8F30,\n    65\t        \&quot;Event Set Looting INI Load Error &gt;&gt; can't open INI file : %s\&quot;,\n    66\t        \&quot;.\\\\Initialize\\\\EventSetLooting.ini\&quot;);\n    67\t      v33-&gt;m_bLoadEventLooting = 0;\n    68\t      result = 0;\n    69\t    }\n    70\t    else\n    71\t    {\n    72\t      while ( fgets(&amp;Buf, 1024, File) )\n    73\t      {\n    74\t        if ( Buf != 59 &amp;&amp; Buf != 10 )\n    75\t        {\n    76\t          v6 = strlen_0(\&quot;[Looting]\&quot;);\n    77\t          if ( !strncmp(&amp;Buf, \&quot;[Looting]\&quot;, v6) )\n    78\t          {\n    79\t            v14 = 0;\n    80\t          }\n...\n   117\t              else if ( ParsingCommandA(&amp;Buf, 5, &amp;ppszDst, 1023) == 5 )\n   118\t              {\n   119\t                Dest = v33-&gt;m_EventSetLootingList[v28].strCode;\n   120\t                strcpy_0(Dest, ppszDst);\n   121\t                v8 = atoi(Str);\n   122\t                *((_WORD *)Dest + 32) = v8;\n   123\t                v9 = atoi(v24);\n   124\t                *((_WORD *)Dest + 33) = v9;\n   125\t                *((_DWORD *)Dest + 17) = atoi(v25);\n   126\t                Dest[72] = atoi(v26);\n   127\t                ++v28;\n   128\t              }\n   129\t            }\n   130\t          }\n   131\t        }\n   132\t      }\n   133\t      fclose(File);\n   134\t      v33-&gt;m_bLoadEventLooting = 1;\n   135\t      result = 1;\n   136\t    }\n   137\t  }\n   138\t  else\n   139\t  {\n   140\t    CLogFile::Write(\n   141\t      &amp;stru_1799C8F30,\n   142\t      \&quot;Event Set Looting INI Load Error &gt;&gt; can't find INI file : %s\&quot;,\n   143\t      \&quot;.\\\\Initialize\\\\EventSetLooting.ini\&quot;);\n   144\t    v33-&gt;m_bLoadEventLooting = 0;\n   145\t    result = 0;\n   146\t  }\n   147\t  return result;\n   148\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c\n...\n    29\t  v9 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    30\t  _strdate(&amp;Buffer);\n    31\t  _strtime(&amp;v8);\n    32\t  __trace(\&quot;%s-%s: Open World\&quot;, &amp;Buffer, &amp;v8);\n    33\t  v10-&gt;m_byWorldCode = v11;\n    34\t  WriteServerStartHistory(\&quot;DBInit Begin &gt;&gt; name: %s, ip: %s\&quot;, pszDBNamea, pszDBIPa);\n    35\t  if ( CMainThread::DatabaseInit(v10, pszDBNamea, pszDBIPa) )\n    36\t  {\n    37\t    v10-&gt;m_bWorldOpen = 1;\n    38\t    CMainThread::SerivceSelfStart(v10);\n    39\t    WriteServerStartHistory(\&quot;DBInit Complete &gt;&gt;\&quot;);\n    40\t  }\n    41\t  else\n    42\t  {\n    43\t    WriteServerStartHistory(\&quot;DBInit Fail &gt;&gt;\&quot;);\n    44\t    CLogFile::WriteString(&amp;v10-&gt;m_logSystemError, \&quot;init DB fail\&quot;);\n    45\t    CWnd::SendMessageA(g_pFrame, 0x10u, 0i64, 0i64);\n    46\t  }\n    47\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/CheckEventSetRespawnCMonsterEventSetQEAAXXZ_1402A8A90.c\n...\n    98\t                  v26 = CreateRepMonster(v16-&gt;pMap, 0, &amp;Dst, v3, 0i64, 0, 1, 0, 0, 0);\n    99\t                  if ( v26 )\n   100\t                  {\n   101\t                    v21-&gt;pMon = v26;\n   102\t                    v21-&gt;dwSerial = v26-&gt;m_dwObjSerial;\n   103\t                    v21-&gt;pMonFld = v16-&gt;pMonsterFld;\n   104\t                    CMonster::DisableStdItemLoot(v26);\n   105\t                    CMonster::LinkEventSet(v26, pEventSet);\n   106\t                  }\n   107\t                  else\n   108\t                  {\n   109\t                    v21-&gt;pMon = 0i64;\n   110\t                  }\n   111\t                }\n   112\t              }\n   113\t            }\n   114\t            v16-&gt;m_State.dwLastUpdateTime = v11;\n   115\t          }\n   116\t        }\n   117\t      }\n   118\t      if ( !v14 )\n   119\t      {\n   120\t        pEventSet-&gt;m_bOper = 0;\n   121\t        CLogFile::Write(&amp;stru_1799C95A8, \&quot;Stop Event Set (by during) &gt;&gt; %s\&quot;, pEventSet);\n   122\t      }\n   123\t    }\n   124\t  }\n   125\t  if ( CMonsterEventSet::IsINIFileChanged(v29, \&quot;.\\\\Initialize\\\\EventSetLooting.ini\&quot;, v29-&gt;m_ftLootingWrite)\n   126\t    &amp;&amp; !CMonsterEventSet::LoadEventSetLooting(v29) )\n   127\t  {\n   128\t    CLogFile::Write(\n   129\t      &amp;stru_1799C95A8,\n   130\t      \&quot;Reload Event set looting INI file fail &gt;&gt; %s\&quot;,\n   131\t      \&quot;.\\\\Initialize\\\\EventSetLooting.ini\&quot;);\n   132\t  }\n   133\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/Load_Event_INICGoldenBoxItemMgrQEAA_NPEAU_golden_b_140412CA0.c\n...\n    50\t    else\n    51\t    {\n    52\t      pInia-&gt;m_bUse_event = 1;\n    53\t      pInia-&gt;m_dwStarterBoxCnt = GetPrivateProfileIntA(\n    54\t                                   \&quot;GoldBox_Item\&quot;,\n    55\t                                   \&quot;StarterBox\&quot;,\n    56\t                                   1000,\n    57\t                                   \&quot;./initialize/GoldenBox_Item.ini\&quot;);\n    58\t      v17-&gt;m_wStarterBoxNum = pInia-&gt;m_dwStarterBoxCnt;\n    59\t      v6 = GetPrivateProfileStringA(\n    60\t             \&quot;GoldBox_Item\&quot;,\n    61\t             \&quot;StarterBoxCode_0\&quot;,\n    62\t             \&quot;NULL\&quot;,\n    63\t             (LPSTR)pInia-&gt;m_szStarterBoxCode,\n    64\t             0x40u,\n    65\t             \&quot;./initialize/GoldenBox_Item.ini\&quot;);\n    66\t      if ( !strcmp_0((const char *)pInia-&gt;m_szStarterBoxCode, \&quot;NULL\&quot;) )\n    67\t      {\n    68\t        pInia-&gt;m_bUse_event = 0;\n    69\t        result = 0;\n    70\t      }\n    71\t      else\n    72\t      {\n    73\t        v6 = GetPrivateProfileStringA(\n    74\t               \&quot;GoldBox_Item\&quot;,\n    75\t               \&quot;StarterBoxCode_1\&quot;,\n    76\t               \&quot;NULL\&quot;,\n    77\t               pInia-&gt;m_szStarterBoxCode[1],\n    78\t               0x40u,\n    79\t               \&quot;./initialize/GoldenBox_Item.ini\&quot;);\n    80\t        if ( !strcmp_0(pInia-&gt;m_szStarterBoxCode[1], \&quot;NULL\&quot;) )\n    81\t        {\n    82\t          pInia-&gt;m_bUse_event = 0;\n    83\t          result = 0;\n    84\t        }\n...\nPath: Decompiled Source Code - IDA Pro/items/LoadDataItemCombineMgrSA_NXZ_1402AB7C0.c\n...\n    21\t  v5 = (unsigned __int64)&amp;v3 ^ _security_cookie;\n    22\t  if ( CRecordData::ReadRecord_Ex(\n    23\t         &amp;ItemCombineMgr::ms_tbl_ItemCombine,\n    24\t         \&quot;.\\\\script\\\\CombineTable.dat\&quot;,\n    25\t         \&quot;.\\\\script\\\\CombineTable2.dat\&quot;,\n    26\t         0x3C8u,\n    27\t         &amp;szMessage) )\n    28\t  {\n    29\t    if ( CRecordData::ReadRecord(\n    30\t           &amp;ItemCombineMgr::ms_tbl_ItemCombine_Link_Stuff,\n    31\t           \&quot;.\\\\script\\\\LinkedStuff.dat\&quot;,\n    32\t           0x1944u,\n    33\t           &amp;szMessage) )\n    34\t    {\n    35\t      if ( CRecordData::ReadRecord(\n    36\t             &amp;ItemCombineMgr::ms_tbl_ItemCombine_Link_Result,\n    37\t             \&quot;.\\\\script\\\\LinkedResult.dat\&quot;,\n    38\t             0x1944u,\n    39\t             &amp;szMessage) )\n    40\t      {\n    41\t        result = 1;\n    42\t      }\n    43\t      else\n    44\t      {\n    45\t        MyMessageBox(\&quot;DatafileInit\&quot;, &amp;szMessage);\n    46\t        result = 0;\n    47\t      }\n    48\t    }\n    49\t    else\n    50\t    {\n    51\t      MyMessageBox(\&quot;DatafileInit\&quot;, &amp;szMessage);\n    52\t      result = 0;\n    53\t    }\n    54\t  }\n    55\t  else\n    56\t  {\n    57\t    MyMessageBox(\&quot;DatafileInit\&quot;, &amp;szMessage);\n    58\t    result = 0;\n    59\t  }\n    60\t  return result;\n    61\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/Load_Golden_Box_Item_EventCGoldenBoxItemMgrQEAA_NX_1404121A0.c\n...\n    21\t  CNetTimer::BeginTimer(&amp;v5-&gt;m_golden_box_event.m_event_timer, 0x1D4C0u);\n    22\t  CLogFile::Write(&amp;v5-&gt;m_golden_box_event.m_event_log, \&quot;Server Start and LogFile Loading\&quot;);\n    23\t  CGoldenBoxItemMgr::Set_Event_Status(v5, 0);\n    24\t  if ( CGoldenBoxItemMgr::Load_Event_INI(v5, &amp;v5-&gt;m_golden_box_event.m_ini) )\n    25\t  {\n    26\t    CGoldenBoxItemMgr::Check_Loaded_Event_Status(v5);\n    27\t    result = !CGoldenBoxItemMgr::Get_Event_Status(v5) || CGoldenBoxItemMgr::SetGoldBoxItemIndex(v5);\n    28\t  }\n    29\t  else\n    30\t  {\n    31\t    MyMessageBox(\&quot;CGoldenBoxItemMgr::Load_Event_INI() : \&quot;, \&quot;Load_Golden_Box_Item_Event() Fail!\&quot;);\n    32\t    result = 0;\n    33\t  }\n    34\t  return result;\n    35\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/LogCUnmannedTraderControllerIEAAXPEADZZ_1403501E0.c\n     1\t/*\n     2\t * Function: ?Log@CUnmannedTraderController@@IEAAXPEADZZ\n     3\t * Address: 0x1403501E0\n     4\t */\n     5\t\n     6\tvoid CUnmannedTraderController::Log(CUnmannedTraderController *this, char *fmt, ...)\n     7\t{\n     8\t  __int64 *v2; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v4; // [sp+0h] [bp-48h]@1\n    11\t  char *arg; // [sp+28h] [bp-20h]@5\n    12\t  CUnmannedTraderController *v6; // [sp+50h] [bp+8h]@1\n    13\t  va_list va; // [sp+60h] [bp+18h]@1\n    14\t\n    15\t  va_start(va, fmt);\n    16\t  v6 = this;\n    17\t  v2 = &amp;v4;\n    18\t  for ( i = 16i64; i; --i )\n    19\t  {\n    20\t    *(_DWORD *)v2 = -*********;\n    21\t    v2 = (__int64 *)((char *)v2 + 4);\n    22\t  }\n    23\t  if ( v6-&gt;m_pkLogger )\n    24\t  {\n    25\t    arg = (char *)va;\n    26\t    CLogFile::WriteFromArg(v6-&gt;m_pkLogger, fmt, (char *)va);\n    27\t  }\n    28\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/InitializeCGoldenBoxItemMgrQEAA_NXZ_1404120D0.c\n     1\t/*\n     2\t * Function: ?Initialize@CGoldenBoxItemMgr@@QEAA_NXZ\n     3\t * Address: 0x1404120D0\n     4\t */\n     5\t\n     6\tchar __fastcall CGoldenBoxItemMgr::Initialize(CGoldenBoxItemMgr *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v4; // [sp+0h] [bp-28h]@1\n    12\t  CGoldenBoxItemMgr *v5; // [sp+30h] [bp+8h]@1\n    13\t\n    14\t  v5 = this;\n    15\t  v1 = &amp;v4;\n    16\t  for ( i = 8i64; i; --i )\n    17\t  {\n    18\t    *(_DWORD *)v1 = -*********;\n    19\t    v1 = (__int64 *)((char *)v1 + 4);\n    20\t  }\n    21\t  CGoldenBoxItemMgr::_init_loggers(v5);\n    22\t  CMyTimer::BeginTimer(&amp;v5-&gt;m_tmLoopTimer, 0x2710u);\n    23\t  if ( CGoldenBoxItemMgr::Load_Golden_Box_Item_Event(v5) )\n    24\t  {\n    25\t    GetLocalTime(&amp;v5-&gt;tm1);\n    26\t    GetLocalTime(&amp;v5-&gt;tm);\n    27\t    v5-&gt;m_bInit = 0;\n    28\t    result = 1;\n    29\t  }\n    30\t  else\n    31\t  {\n    32\t    result = 0;\n    33\t  }\n    34\t  return result;\n    35\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/InitLoggerCUnmannedTraderControllerIEAA_NXZ_14034FC50.c\n...\n    52\t  v14 = v19;\n    53\t  v22-&gt;m_pkLogger = v19;\n    54\t  if ( v22-&gt;m_pkLogger )\n    55\t  {\n    56\t    v12 = GetKorLocalTime();\n    57\t    sprintf_s&lt;128&gt;((char (*)[128])_Dest, \&quot;..\\\\ZoneServerLog\\\\Systemlog\\\\UnmannedTrader\\\\UnmannedTrader%d.log\&quot;, v12);\n    58\t    bAddCount = 1;\n    59\t    bDate = 1;\n    60\t    CLogFile::SetWriteLogFile(v22-&gt;m_pkLogger, _Dest, 1, 0, 1, 1);\n    61\t    CreateDirectoryA(\&quot;..\\\\ServiceLog\\\\UnmannedTrader\&quot;, 0i64);\n    62\t    clear_file(\&quot;..\\\\ServiceLog\\\\UnmannedTrader\&quot;, 0xFu);\n    63\t    v17 = (CLogFile *)operator new(0xB8ui64);\n    64\t    if ( v17 )\n    65\t    {\n    66\t      CLogFile::CLogFile(v17);\n    67\t      v20 = (CLogFile *)v5;\n    68\t    }\n    69\t    else\n    70\t    {\n    71\t      v20 = 0i64;\n    72\t    }\n...\nPath: Decompiled Source Code - IDA Pro/player/ct_trunk_initYA_NPEAVCPlayerZ_140291C00.c\n     1\t/*\n     2\t * Function: ?ct_trunk_init@@YA_NPEAVCPlayer@@@Z\n     3\t * Address: 0x140291C00\n     4\t */\n     5\t\n     6\tbool __fastcall ct_trunk_init(CPlayer *pOne)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  bool result; // al@5\n    11\t  __int64 v4; // [sp+0h] [bp-28h]@1\n    12\t  CPlayer *v5; // [sp+30h] [bp+8h]@1\n    13\t\n    14\t  v5 = pOne;\n    15\t  v1 = &amp;v4;\n    16\t  for ( i = 8i64; i; --i )\n    17\t  {\n    18\t    *(_DWORD *)v1 = -*********;\n    19\t    v1 = (__int64 *)((char *)v1 + 4);\n    20\t  }\n    21\t  if ( v5 )\n    22\t    result = CPlayer::mgr_TrunkInit(v5);\n    23\t  else\n    24\t    result = 0;\n    25\t  return result;\n    26\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZ_1403CEED0.c\n     1\t/*\n     2\t * Function: ?Log@CNormalGuildBattleLogger@GUILD_BATTLE@@QEAAXPEA_WZZ\n     3\t * Address: 0x1403CEED0\n     4\t */\n     5\t\n     6\tvoid GUILD_BATTLE::CNormalGuildBattleLogger::Log(GUILD_BATTLE::CNormalGuildBattleLogger *this, wchar_t *fmt, ...)\n     7\t{\n     8\t  __int64 *v2; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v4; // [sp+0h] [bp-48h]@1\n    11\t  char *arg; // [sp+28h] [bp-20h]@5\n    12\t  GUILD_BATTLE::CNormalGuildBattleLogger *v6; // [sp+50h] [bp+8h]@1\n    13\t  va_list va; // [sp+60h] [bp+18h]@1\n    14\t\n    15\t  va_start(va, fmt);\n    16\t  v6 = this;\n    17\t  v2 = &amp;v4;\n    18\t  for ( i = 16i64; i; --i )\n    19\t  {\n    20\t    *(_DWORD *)v2 = -*********;\n    21\t    v2 = (__int64 *)((char *)v2 + 4);\n    22\t  }\n    23\t  if ( v6-&gt;m_pkLogger )\n    24\t  {\n    25\t    arg = (char *)va;\n    26\t    CLogFile::WriteFromArg(v6-&gt;m_pkLogger, fmt, (char *)va);\n    27\t  }\n    28\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/InitializeCashItemRemoteStoreQEAA_NXZ_1402F4EF0.c\n...\n     5\t\n     6\tbool __fastcall CashItemRemoteStore::Initialize(CashItemRemoteStore *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  bool result; // al@5\n    11\t  __int64 v4; // [sp+0h] [bp-28h]@1\n    12\t  CashItemRemoteStore *v5; // [sp+30h] [bp+8h]@1\n    13\t\n    14\t  v5 = this;\n    15\t  v1 = &amp;v4;\n    16\t  for ( i = 8i64; i; --i )\n    17\t  {\n    18\t    *(_DWORD *)v1 = -*********;\n    19\t    v1 = (__int64 *)((char *)v1 + 4);\n    20\t  }\n    21\t  if ( CashItemRemoteStore::_InitLoggers(v5) )\n    22\t  {\n    23\t    if ( CashItemRemoteStore::_ReadGoods(v5) )\n    24\t    {\n    25\t      CMyTimer::BeginTimer(&amp;v5-&gt;m_TotalEventTimer, 0x3E8u);\n    26\t      CashItemRemoteStore::Load_Conditional_Event(v5);\n    27\t      CashItemRemoteStore::load_cash_discount_event(v5);\n    28\t      CashItemRemoteStore::Load_Cash_Event(v5);\n    29\t      result = CashItemRemoteStore::LoadBuyCashMode(v5) != 0;\n    30\t    }\n    31\t    else\n    32\t    {\n    33\t      result = 0;\n    34\t    }\n    35\t  }\n    36\t  else\n    37\t  {\n    38\t    result = 0;\n    39\t  }\n    40\t  return result;\n    41\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/InitialzieTRC_AutoTradeQEAA_NXZ_1402D7E00.c\n     1\t/*\n     2\t * Function: ?Initialzie@TRC_AutoTrade@@QEAA_NXZ\n     3\t * Address: 0x1402D7E00\n     4\t */\n     5\t\n     6\tchar __fastcall TRC_AutoTrade::Initialzie(TRC_AutoTrade *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  unsigned int v3; // eax@4\n    11\t  unsigned int v4; // eax@4\n    12\t  __int64 v6; // [sp+0h] [bp-E8h]@1\n    13\t  char Dest; // [sp+40h] [bp-A8h]@4\n    14\t  char v8; // [sp+41h] [bp-A7h]@4\n    15\t  unsigned __int64 v9; // [sp+D0h] [bp-18h]@4\n    16\t  TRC_AutoTrade *v10; // [sp+F0h] [bp+8h]@1\n    17\t\n    18\t  v10 = this;\n    19\t  v1 = &amp;v6;\n    20\t  for ( i = 56i64; i; --i )\n    21\t  {\n    22\t    *(_DWORD *)v1 = -*********;\n    23\t    v1 = (__int64 *)((char *)v1 + 4);\n    24\t  }\n...\nPath: Decompiled Source Code - IDA Pro/combat/LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZZ_1403CEB50.c\n     1\t/*\n     2\t * Function: ?Log@CGuildBattleLogger@GUILD_BATTLE@@QEAAXPEA_WZZ\n     3\t * Address: 0x1403CEB50\n     4\t */\n     5\t\n     6\tvoid GUILD_BATTLE::CGuildBattleLogger::Log(GUILD_BATTLE::CGuildBattleLogger *this, wchar_t *fmt, ...)\n     7\t{\n     8\t  __int64 *v2; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v4; // [sp+0h] [bp-48h]@1\n    11\t  char *arg; // [sp+28h] [bp-20h]@5\n    12\t  GUILD_BATTLE::CGuildBattleLogger *v6; // [sp+50h] [bp+8h]@1\n    13\t  va_list va; // [sp+60h] [bp+18h]@1\n    14\t\n    15\t  va_start(va, fmt);\n    16\t  v6 = this;\n    17\t  v2 = &amp;v4;\n    18\t  for ( i = 16i64; i; --i )\n    19\t  {\n    20\t    *(_DWORD *)v2 = -*********;\n    21\t    v2 = (__int64 *)((char *)v2 + 4);\n    22\t  }\n    23\t  if ( v6-&gt;m_pkLogger )\n    24\t  {\n    25\t    arg = (char *)va;\n    26\t    CLogFile::WriteFromArg(v6-&gt;m_pkLogger, fmt, (char *)va);\n    27\t  }\n    28\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/LoadCGuildBattleControllerQEAA_NXZ_1403D5950.c\n...\n    33\t  v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();\n    34\t  iCurDay = GetCurDay();\n    35\t  if ( iCurDay &gt;= 0 )\n    36\t  {\n    37\t    uiMapCnt = 0;\n    38\t    iToday = 0;\n    39\t    iTodayDayID = 0;\n    40\t    v15 = 0;\n    41\t    v16 = 1;\n    42\t    if ( !CGuildBattleController::LoadINI(v17, &amp;uiMapCnt, &amp;iToday, &amp;iTodayDayID, &amp;v15, &amp;v16) )\n    43\t      GUILD_BATTLE::CGuildBattleLogger::Log(v10, \&quot;CGuildBattleController::Load(%d) LoadINI() Fail!\&quot;);\n    44\t    v4 = GUILD_BATTLE::CGuildBattleRankManager::Instance();\n    45\t    if ( GUILD_BATTLE::CGuildBattleRankManager::Load(v4) )\n    46\t    {\n    47\t      v5 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();\n    48\t      if ( GUILD_BATTLE::CGuildBattleScheduleManager::Load(v5, iCurDay, uiMapCnt, iToday, iTodayDayID, v15, v16) )\n    49\t      {\n    50\t        v6 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();\n...\nPath: Decompiled Source Code - IDA Pro/items/InitCUnmannedTraderControllerQEAA_NXZ_14034CBE0.c\n...\n    25\t  if ( CUnmannedTraderController::InitLogger(v9) )\n    26\t  {\n    27\t    v4 = CUnmannedTraderUserInfoTable::Instance();\n    2...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01KaG5pz9nTHKp8v5CTaUi4x&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;,&quot;folderRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-15T08:47:48.302Z&quot;,&quot;response_text&quot;:&quot;Let me search more specifically for the exact string \&quot;RF SERVER NOW LOADING\&quot;:\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;ef00de4a-08e9-415c-9620-532b0b2e140e&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01QgBikgZeKtF6WaP9i8Nyyk&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;Decompiled Source Code - IDA Pro/world/pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;Let me search for the \&quot;RF SERVER NOW LOADING\&quot; message in the main thread or startup code:\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in Decompiled Source Code - IDA Pro, excluding hidden items:\nDecompiled Source Code - IDA Pro/authentication\nDecompiled Source Code - IDA Pro/authentication\\0CAsyncLogInfoQEAAXZ_1403BC9F0.c\nDecompiled Source Code - IDA Pro/authentication\\0CBHPEAVCAsyncLogInfopairHPEAVCAsyncLogInfostdQEAA_1403C7590.c\nDecompiled Source Code - IDA Pro/authentication\\0MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140465820.c\nDecompiled Source Code - IDA Pro/authentication\\0UpairCBHPEAVCAsyncLogInfostdallocatorPEAU_Node_Li_1403C7E70.c\nDecompiled Source Code - IDA Pro/authentication\\0UpairCBHPEAVCAsyncLogInfostdallocatorU_Node_List__1403C7FF0.c\nDecompiled Source Code - IDA Pro/authentication\\0UpairCBHPEAVCAsyncLogInfostdallocatorV_Iterator0A_1403C7670.c\nDecompiled Source Code - IDA Pro/authentication\\0W4ASYNC_LOG_TYPEPEAVCAsyncLogInfopairCBHPEAVCAsyn_1403C7630.c\nDecompiled Source Code - IDA Pro/authentication\\0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C1560.c\nDecompiled Source Code - IDA Pro/authentication\\0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C5E20.c\nDecompiled Source Code - IDA Pro/authentication\\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C1480.c\nDecompiled Source Code - IDA Pro/authentication\\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C5B90.c\nDecompiled Source Code - IDA Pro/authentication\\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C6D10.c\nDecompiled Source Code - IDA Pro/authentication\\0_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compare_1403C2EC0.c\nDecompiled Source Code - IDA Pro/authentication\\0_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUless_1403C4520.c\nDecompiled Source Code - IDA Pro/authentication\\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2D20.c\nDecompiled Source Code - IDA Pro/authentication\\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C42C0.c\nDecompiled Source Code - IDA Pro/authentication\\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C5B30.c\nDecompiled Source Code - IDA Pro/authentication\\0_List_nodUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C7520.c\nDecompiled Source Code - IDA Pro/authentication\\0_List_ptrUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C6F70.c\nDecompiled Source Code - IDA Pro/authentication\\0_List_valUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C66F0.c\nDecompiled Source Code - IDA Pro/authentication\\0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C5EE0.c\nDecompiled Source Code - IDA Pro/authentication\\0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C74D0.c\nDecompiled Source Code - IDA Pro/authentication\\0_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C5E70.c\nDecompiled Source Code - IDA Pro/authentication\\0_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C73C0.c\nDecompiled Source Code - IDA Pro/authentication\\0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C5DC0.c\nDecompiled Source Code - IDA Pro/authentication\\0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6D70.c\nDecompiled Source Code - IDA Pro/authentication\\0_Vector_valV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C6BE0.c\nDecompiled Source Code - IDA Pro/authentication\\0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAAEBV0_1403C5F40.c\nDecompiled Source Code - IDA Pro/authentication\\0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAXZ_1403C35F0.c\nDecompiled Source Code - IDA Pro/authentication\\0allocatorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C6C50.c\nDecompiled Source Code - IDA Pro/authentication\\0hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHstd_1403C17E0.c\nDecompiled Source Code - IDA Pro/authentication\\0listUpairCBHPEAVCAsyncLogInfostdVallocatorUpairCB_1403C45B0.c\nDecompiled Source Code - IDA Pro/authentication\\0pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdV_1403C43F0.c\nDecompiled Source Code - IDA Pro/authentication\\0pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdQEAAAEBW4_1403C8010.c\nDecompiled Source Code - IDA Pro/authentication\\0vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C4EF0.c\nDecompiled Source Code - IDA Pro/authentication\\1CAsyncLogInfoQEAAXZ_1403BCA80.c\nDecompiled Source Code - IDA Pro/authentication\\1MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140464F50.c\nDecompiled Source Code - IDA Pro/authentication\\1_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C1230.c\nDecompiled Source Code - IDA Pro/authentication\\1_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C11F0.c\nDecompiled Source Code - IDA Pro/authentication\\1_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compare_1403C1860.c\nDecompiled Source Code - IDA Pro/authentication\\1_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C11B0.c\nDecompiled Source Code - IDA Pro/authentication\\1_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C44E0.c\nDecompiled Source Code - IDA Pro/authentication\\1_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C44A0.c\nDecompiled Source Code - IDA Pro/authentication\\1_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C4460.c\nDecompiled Source Code - IDA Pro/authentication\\1hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHstd_1403C1170.c\nDecompiled Source Code - IDA Pro/authentication\\1listUpairCBHPEAVCAsyncLogInfostdVallocatorUpairCB_1403C3630.c\nDecompiled Source Code - IDA Pro/authentication\\1pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdV_1403C1670.c\nDecompiled Source Code - IDA Pro/authentication\\1vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C3EE0.c\nDecompiled Source Code - IDA Pro/authentication\\4_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C2DF0.c\nDecompiled Source Code - IDA Pro/authentication\\4_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2D80.c\nDecompiled Source Code - IDA Pro/authentication\\4_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2CC0.c\nDecompiled Source Code - IDA Pro/authentication\\8UpairCBHPEAVCAsyncLogInfostdU01stdYA_NAEBVallocat_1403C7690.c\nDecompiled Source Code - IDA Pro/authentication\\8_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400A6CA0.c\nDecompiled Source Code - IDA Pro/authentication\\8_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2BE0.c\nDecompiled Source Code - IDA Pro/authentication\\8_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C7460.c\nDecompiled Source Code - IDA Pro/authentication\\9_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400CFE90.c\nDecompiled Source Code - IDA Pro/authentication\\9_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2C50.c\nDecompiled Source Code - IDA Pro/authentication\\9_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C6E30.c\nDecompiled Source Code - IDA Pro/authentication\\AccountServerLoginCMainThreadQEAAXXZ_1401F8140.c\nDecompiled Source Code - IDA Pro/authentication\\AuthLastCriTicketMiningTicketQEAAHGEEEEZ_1400D01D0.c\nDecompiled Source Code - IDA Pro/authentication\\AuthLastMentalTicketMiningTicketQEAAHGEEEEZ_1400CFDB0.c\nDecompiled Source Code - IDA Pro/authentication\\AuthMiningTicketCHolyStoneSystemQEAA_NIZ_14027DBD0.c\nDecompiled Source Code - IDA Pro/authentication\\AvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C4290.c\nDecompiled Source Code - IDA Pro/authentication\\CN_InvalidateNatureYAXXZ_140504ED0.c\nDecompiled Source Code - IDA Pro/authentication\\C_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2AF0.c\nDecompiled Source Code - IDA Pro/authentication\\CallFunc_RFOnline_AuthCEnglandBillingMgrQEAAHAEAU__1403198F0.c\nDecompiled Source Code - IDA Pro/authentication\\CallFunc_RFOnline_AuthCRusiaBillingMgrQEAAHAEAU_pa_1403213A0.c\nDecompiled Source Code - IDA Pro/authentication\\CallProc_RFOnlineAuthCRFCashItemDatabaseQEAAHAEAU__140482430.c\nDecompiled Source Code - IDA Pro/authentication\\CallProc_RFOnlineAuth_JapCRFCashItemDatabaseQEAAHA_1404836A0.c\nDecompiled Source Code - IDA Pro/authentication\\CompleteLogInCompeteCUnmannedTraderControllerQEAAX_14034EF80.c\nDecompiled Source Code - IDA Pro/authentication\\D3D_R3InvalidateDeviceYAJXZ_14050B040.c\nDecompiled Source Code - IDA Pro/authentication\\D_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2B30.c\nDecompiled Source Code - IDA Pro/authentication\\D_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C4310.c\nDecompiled Source Code - IDA Pro/authentication\\E_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2B80.c\nDecompiled Source Code - IDA Pro/authentication\\E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C4350.c\nDecompiled Source Code - IDA Pro/authentication\\E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C7270.c\nDecompiled Source Code - IDA Pro/authentication\\F_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C5BF0.c\nDecompiled Source Code - IDA Pro/authentication\\F_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C43A0.c\nDecompiled Source Code - IDA Pro/authentication\\GenerateEphemeralKeyPairAuthenticatedKeyAgreementD_1405F6600.c\nDecompiled Source Code - IDA Pro/authentication\\GenerateStaticKeyPairAuthenticatedKeyAgreementDoma_1405F65A0.c\nDecompiled Source Code - IDA Pro/authentication\\GetCountCAsyncLogInfoQEAAKXZ_1403C16B0.c\nDecompiled Source Code - IDA Pro/authentication\\GetDirPathCAsyncLogInfoQEAAPEBDXZ_1403C1630.c\nDecompiled Source Code - IDA Pro/authentication\\GetFileNameCAsyncLogInfoQEAAPEBDXZ_1403C16D0.c\nDecompiled Source Code - IDA Pro/authentication\\GetOccDialogInfoCDialogMEAAPEAU_AFX_OCC_DIALOG_INF_1404DBD48.c\nDecompiled Source Code - IDA Pro/authentication\\GetOccDialogInfoCFormViewMEAAPEAU_AFX_OCC_DIALOG_I_1404DC15C.c\nDecompiled Source Code - IDA Pro/authentication\\GetOccDialogInfoCWndMEAAPEAU_AFX_OCC_DIALOG_INFOXZ_1404DBE20.c\nDecompiled Source Code - IDA Pro/authentication\\GetTypeNameCAsyncLogInfoQEAAPEBDXZ_1403C1650.c\nDecompiled Source Code - IDA Pro/authentication\\H_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C5C70.c\nDecompiled Source Code - IDA Pro/authentication\\IncreaseCountCAsyncLogInfoQEAAXXZ_1403C16F0.c\nDecompiled Source Code - IDA Pro/authentication\\InitCAsyncLogInfoQEAA_NW4ASYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c\nDecompiled Source Code - IDA Pro/authentication\\Init_AuthKeyTicketMiningTicketQEAAXXZ_140073BC0.c\nDecompiled Source Code - IDA Pro/authentication\\InvalidateDeviceObjectsCR3FontQEAAJXZ_140528820.c\nDecompiled Source Code - IDA Pro/authentication\\InvalidateSkySkyQEAAXXZ_1405229B0.c\nDecompiled Source Code - IDA Pro/authentication\\InvalidateSunSunQEAAXXZ_1405221E0.c\nDecompiled Source Code - IDA Pro/authentication\\IsLogInStateCUnmannedTraderUserInfoQEAA_NXZ_140366F20.c\nDecompiled Source Code - IDA Pro/authentication\\LogInCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_1403E4050.c\nDecompiled Source Code - IDA Pro/authentication\\LogInCNormalGuildBattleGuildGUILD_BATTLEQEAAXHKEPE_1403E0DD0.c\nDecompiled Source Code - IDA Pro/authentication\\LogInCNormalGuildBattleManagerGUILD_BATTLEQEAAXHKK_1403D4360.c\nDecompiled Source Code - IDA Pro/authentication\\LogInControllServerCNetworkEXAEAA_NHPEADZ_1401C7250.c\nDecompiled Source Code - IDA Pro/authentication\\LogInWebAgentServerCNetworkEXAEAA_NHPEADZ_1401DA860.c\nDecompiled Source Code - IDA Pro/authentication\\LoginCBillingIDUEAAXPEAVCUserDBZ_14028E0F0.c\nDecompiled Source Code - IDA Pro/authentication\\LoginCBillingJPUEAAXPEAVCUserDBZ_14028E910.c\nDecompiled Source Code - IDA Pro/authentication\\LoginCBillingManagerQEAAXPEAVCUserDBZ_140079030.c\nDecompiled Source Code - IDA Pro/authentication\\LoginCBillingNULLUEAAXPEAVCUserDBZ_14028DBD0.c\nDecompiled Source Code - IDA Pro/authentication\\LoginCBillingUEAAXPEAVCUserDBZ_14028CAC0.c\nDecompiled Source Code - IDA Pro/authentication\\LoginCNormalGuildBattleGuildMemberGUILD_BATTLEQEAA_1403DFA80.c\nDecompiled Source Code - IDA Pro/authentication\\NotifyLogInSetBuffCRaceBuffInfoByHolyQuestAEAAXGZ_1403B42D0.c\nDecompiled Source Code - IDA Pro/authentication\\OnCheckSession_FirstVerifyCHackShieldExSystemUEAA__140417250.c\nDecompiled Source Code - IDA Pro/authentication\\OnCheckSession_FirstVerifyCNationSettingManagerQEA_140229470.c\nDecompiled Source Code - IDA Pro/authentication\\OnCheckSession_FirstVerifyHACKSHEILD_PARAM_ANTICPU_140417960.c\nDecompiled Source Code - IDA Pro/authentication\\OnConnectSessionCHackShieldExSystemUEAAXHZ_1404170D0.c\nDecompiled Source Code - IDA Pro/authentication\\OnConnectSessionCNationSettingManagerQEAAXHZ_140229400.c\nDecompiled Source Code - IDA Pro/authentication\\OnDisConnectSessionCHackShieldExSystemUEAAXHZ_140417140.c\nDecompiled Source Code - IDA Pro/authentication\\OnDisConnectSessionCNationSettingManagerQEAAXHZ_1402294F0.c\nDecompiled Source Code - IDA Pro/authentication\\OnLoopSessionCHackShieldExSystemUEAAXHZ_1404171A0.c\nDecompiled Source Code - IDA Pro/authentication\\OnRecvSessionHACKSHEILD_PARAM_ANTICPUEAA_NPEAVCHac_140417F10.c\nDecompiled Source Code - IDA Pro/authentication\\OnRecvSession_ClientCheckSum_ResponseHACKSHEILD_PA_140418120.c\nDecompiled Source Code - IDA Pro/authentication\\OnRecvSession_ClientCrc_ResponseHACKSHEILD_PARAM_A_140418290.c\nDecompiled Source Code - IDA Pro/authentication\\OnRecvSession_ServerCheckSum_RequestHACKSHEILD_PAR_140417FB0.c\nDecompiled Source Code - IDA Pro/authentication\\R3InvalidateDeviceYAJXZ_1404E9FC0.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_CurAllUserLoginCBillingIEAAXXZ_14028D610.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_GuildMemberLoginCGuildQEAAXKGGZ_1402570F0.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_LoginCBillingIDMEAA_NPEAD00FPEAU_SYSTEMTIM_14028E600.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_LoginCBillingJPMEAA_NPEAD00FPEAU_SYSTEMTIM_14028ECC0.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_LoginCBillingMEAA_NPEAD00FPEAU_SYSTEMTIMEJ_14028D3C0.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_LoginCBillingNULLMEAA_NPEAD00FPEAU_SYSTEMT_14028DC10.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_UserLogInNotifyTaxRateTRC_AutoTradeQEAAXHZ_1402D8540.c\nDecompiled Source Code - IDA Pro/authentication\\SetOccDialogInfoCDialogMEAAHPEAU_AFX_OCC_DIALOG_IN_1404DBD42.c\nDecompiled Source Code - IDA Pro/authentication\\SetOccDialogInfoCFormViewMEAAHPEAU_AFX_OCC_DIALOG__1404DC156.c\nDecompiled Source Code - IDA Pro/authentication\\SetOccDialogInfoCWndMEAAHPEAU_AFX_OCC_DIALOG_INFOZ_1404DBE1A.c\nDecompiled Source Code - IDA Pro/authentication\\Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ_1400A6BA0.c\nDecompiled Source Code - IDA Pro/authentication\\Set_AuthKeyTicketMiningTicketQEAAXIZ_140078ED0.c\nDecompiled Source Code - IDA Pro/authentication\\UpdateLogFileNameCAsyncLogInfoQEAAXXZ_1403BD0F0.c\nDecompiled Source Code - IDA Pro/authentication\\UpdateLogInCompleteCUnmannedTraderControllerQEAAEP_14034E440.c\nDecompiled Source Code - IDA Pro/authentication\\Update_TrunkPasswordCUserDBQEAA_NPEADZ_140116FD0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAD0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAF0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADD90.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046A900.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046A920.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046AD80.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersVIntegerCryptoPPCryptoPP_140551AC0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersVIntegerCryptoPPCryptoPP_1405AD4F0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PrivateKeyImplVDL_GroupParameters_DSACr_140568460.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_1404515F0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_140558CA0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PrivateKeyImplVDL_GroupParameters_GFPCr_140635EE0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PrivateKeyImplVDL_GroupParameters_GFP_D_140637C30.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PublicKeyImplVDL_GroupParameters_DSACry_140568F30.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PublicKeyImplVDL_GroupParameters_ECVEC2_140558420.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PublicKeyImplVDL_GroupParameters_ECVECP_140450870.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PublicKeyImplVDL_GroupParameters_GFPCry_1406369F0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PublicKeyImplVDL_GroupParameters_GFP_De_1406373A0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateElementDL_GroupParameters_ECVEC2NCryptoPPC_140583CF0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateElementDL_GroupParameters_ECVECPCryptoPPCr_14057FB10.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateElementDL_GroupParameters_IntegerBasedCryp_140630AE0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateGroupDL_GroupParameters_DSACryptoPPUEBA_NA_140630230.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateGroupDL_GroupParameters_ECVEC2NCryptoPPCry_1405835A0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateGroupDL_GroupParameters_ECVECPCryptoPPCryp_14057F300.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateGroupDL_GroupParameters_IntegerBasedCrypto_140630680.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateParametersEC2NCryptoPPQEBA_NAEAVRandomNumb_14062E210.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateParametersECPCryptoPPQEBA_NAEAVRandomNumbe_14060E2A0.c\nDecompiled Source Code - IDA Pro/authentication\\Y_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C7420.c\nDecompiled Source Code - IDA Pro/authentication\\Y_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6DD0.c\nDecompiled Source Code - IDA Pro/authentication\\_AllocateU_Node_List_nodUpairCBHPEAVCAsyncLogInfos_1403C7E90.c\nDecompiled Source Code - IDA Pro/authentication\\_AllocateV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C7D00.c\nDecompiled Source Code - IDA Pro/authentication\\_BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C6370.c\nDecompiled Source Code - IDA Pro/authentication\\_BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C6500.c\nDecompiled Source Code - IDA Pro/authentication\\_BuyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C7100.c\nDecompiled Source Code - IDA Pro/authentication\\_CAsyncLogInfoInit__1_dtor0_1403BD0C0.c\nDecompiled Source Code - IDA Pro/authentication\\_CAsyncLogInfo_CAsyncLogInfo__1_dtor0_1403BCB50.c\nDecompiled Source Code - IDA Pro/authentication\\_CEnglandBillingMgrCallFunc_RFOnline_Auth__1_dtor0_140319C50.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitInfoListLogIn__1_dtor0_1403A5C90.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitInfoListLogIn__1_dtor1_1403A5CC0.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitInfoListLogIn__1_dtor2_1403A5CF0.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitInfoListLogIn__1_dtor3_1403A5D20.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitRightInfoLogIn__1_dtor0_1403AD090.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitRightInfoLogIn__1_dtor1_1403AD0C0.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitRightInfoLogIn__1_dtor2_1403AD0F0.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitRightInfoLogIn__1_dtor3_1403AD120.c\nDecompiled Source Code - IDA Pro/authentication\\_ConstructPEAU_Node_List_nodUpairCBHPEAVCAsyncLogI_1403C7F50.c\nDecompiled Source Code - IDA Pro/authentication\\_ConstructUpairCBHPEAVCAsyncLogInfostdU12stdYAXPEA_1403C7DB0.c\nDecompiled Source Code - IDA Pro/authentication\\_ConstructV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C8B70.c\nDecompiled Source Code - IDA Pro/authentication\\_Construct_nvectorV_Iterator0AlistUpairCBHPEAVCAsy_1403C6820.c\nDecompiled Source Code - IDA Pro/authentication\\_Copy_backward_optPEAV_Iterator0AlistUpairCBHPEAVC_1403C8AD0.c\nDecompiled Source Code - IDA Pro/authentication\\_Copy_optPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C8530.c\nDecompiled Source Code - IDA Pro/authentication\\_CryptoPPDL_PrivateKeyImpl_CryptoPPDL_GroupParamet_140451850.c\nDecompiled Source Code - IDA Pro/authentication\\_DestroyPEAU_Node_List_nodUpairCBHPEAVCAsyncLogInf_1403C7BC0.c\nDecompiled Source Code - IDA Pro/authentication\\_DestroyU_Node_List_nodUpairCBHPEAVCAsyncLogInfost_1403C7F40.c\nDecompiled Source Code - IDA Pro/authentication\\_DestroyV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1403C8C60.c\nDecompiled Source Code - IDA Pro/authentication\\_Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C7BD0.c\nDecompiled Source Code - IDA Pro/authentication\\_Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C8810.c\nDecompiled Source Code - IDA Pro/authentication\\_DestroyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C69F0.c\nDecompiled Source Code - IDA Pro/authentication\\_EMessageAuthenticationCodeImplVHMAC_BaseCryptoPPV_140465900.c\nDecompiled Source Code - IDA Pro/authentication\\_EMessageAuthenticationCodeImplVHMAC_BaseCryptoPPV_14046AC80.c\nDecompiled Source Code - IDA Pro/authentication\\_FillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1403C8680.c\nDecompiled Source Code - IDA Pro/authentication\\_GCAsyncLogInfoQEAAPEAXIZ_1403C14F0.c\nDecompiled Source Code - IDA Pro/authentication\\_G_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVallo_1403C8CB0.c\nDecompiled Source Code - IDA Pro/authentication\\_Get_iter_from_vec_HashV_Hmap_traitsHPEAVCAsyncLog_1403C2E50.c\nDecompiled Source Code - IDA Pro/authentication\\_Hashval_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash__1403C3530.c\nDecompiled Source Code - IDA Pro/authentication\\_IncsizelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C4D90.c\nDecompiled Source Code - IDA Pro/authentication\\_InsertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C80C0.c\nDecompiled Source Code - IDA Pro/authentication\\_Insert_nvectorV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C5320.c\nDecompiled Source Code - IDA Pro/authentication\\_InsertlistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1403C4670.c\nDecompiled Source Code - IDA Pro/authentication\\_Iter_catV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C8060.c\nDecompiled Source Code - IDA Pro/authentication\\_Iter_randomPEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8470.c\nDecompiled Source Code - IDA Pro/authentication\\_Kfn_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUl_1403C35E0.c\nDecompiled Source Code - IDA Pro/authentication\\_Move_backward_optPEAV_Iterator0AlistUpairCBHPEAVC_1403C8760.c\nDecompiled Source Code - IDA Pro/authentication\\_Move_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C8700.c\nDecompiled Source Code - IDA Pro/authentication\\_Mynode_Const_iterator0AlistUpairCBHPEAVCAsyncLogI_1403C5C50.c\nDecompiled Source Code - IDA Pro/authentication\\_MyvallistUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C3610.c\nDecompiled Source Code - IDA Pro/authentication\\_NextnodelistUpairCBHPEAVCAsyncLogInfostdVallocato_1403C3600.c\nDecompiled Source Code - IDA Pro/authentication\\_PrevnodelistUpairCBHPEAVCAsyncLogInfostdVallocato_1403C4590.c\nDecompiled Source Code - IDA Pro/authentication\\_Ptr_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C84D0.c\nDecompiled Source Code - IDA Pro/authentication\\_SplicelistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1403C38D0.c\nDecompiled Source Code - IDA Pro/authentication\\_TidylistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C4CC0.c\nDecompiled Source Code - IDA Pro/authentication\\_TidyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C5240.c\nDecompiled Source Code - IDA Pro/authentication\\_UfillvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C6A60.c\nDecompiled Source Code - IDA Pro/authentication\\_UmovePEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C7A30.c\nDecompiled Source Code - IDA Pro/authentication\\_Unchecked_move_backwardPEAV_Iterator0AlistUpairCB_1403C7B00.c\nDecompiled Source Code - IDA Pro/authentication\\_Unchecked_uninitialized_movePEAV_Iterator0AlistUp_1403C85D0.c\nDecompiled Source Code - IDA Pro/authentication\\_Uninit_copyPEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8DD0.c\nDecompiled Source Code - IDA Pro/authentication\\_Uninit_fill_nPEAV_Iterator0AlistUpairCBHPEAVCAsyn_1403C8890.c\nDecompiled Source Code - IDA Pro/authentication\\_Uninit_movePEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8A60.c\nDecompiled Source Code - IDA Pro/authentication\\_ValidateImageBase_1404DE4C0.c\nDecompiled Source Code - IDA Pro/authentication\\_XlenvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C6AF0.c\nDecompiled Source Code - IDA Pro/authentication\\_std_Construct_stdlist_stdpair_int_const__CAsyncLo_1403C8C00.c\nDecompiled Source Code - IDA Pro/authentication\\_std_Uninit_copy_stdlist_stdpair_int_const__CAsync_1403C8E60.c\nDecompiled Source Code - IDA Pro/authentication\\_std_Uninit_fill_n_stdlist_stdpair_int_const__CAsy_1403C8920.c\nDecompiled Source Code - IDA Pro/authentication\\_std_Vector_iterator_stdlist_stdpair_int_const__CA_1403C5D10.c\nDecompiled Source Code - IDA Pro/authentication\\_std_Vector_iterator_stdlist_stdpair_int_const__CA_1403C5D40.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C18C0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2480.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C24B0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C24E0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2510.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2540.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2570.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C25A0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C25D0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2600.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2630.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2660.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2690.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C26C0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2700.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2FA0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2FD0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3000.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3370.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C33A0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C33D0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3410.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3820.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3850.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3C90.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3CC0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3CF0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D20.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D50.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D80.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4730.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4A70.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4AA0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4AD0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B10.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B50.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B90.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4E80.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6130.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6160.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6190.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6440.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6600.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7310.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7340.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7800.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7830.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7860.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7890.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C78C0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8200.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8230.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8260.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8290.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C82C0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8390.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C40F0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C4120.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C4150.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5150.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5180.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C51B0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5860.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5890.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C58C0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5920.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C68B0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C6B80.c\nDecompiled Source Code - IDA Pro/authentication\\allocateallocatorU_Node_List_nodUpairCBHPEAVCAsync_1403C7000.c\nDecompiled Source Code - IDA Pro/authentication\\allocateallocatorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6CC0.c\nDecompiled Source Code - IDA Pro/authentication\\auto_trade_login_sellCMgrAvatorItemHistoryQEAAXPEB_14023A3E0.c\nDecompiled Source Code - IDA Pro/authentication\\begin_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_com_1403C1910.c\nDecompiled Source Code - IDA Pro/authentication\\beginlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C3670.c\nDecompiled Source Code - IDA Pro/authentication\\beginvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C4F90.c\nDecompiled Source Code - IDA Pro/authentication\\capacityvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C6920.c\nDecompiled Source Code - IDA Pro/authentication\\clearlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C6250.c\nDecompiled Source Code - IDA Pro/authentication\\constructallocatorPEAU_Node_List_nodUpairCBHPEAVCA_1403C70A0.c\nDecompiled Source Code - IDA Pro/authentication\\constructallocatorUpairCBHPEAVCAsyncLogInfostdstdQ_1403C6EA0.c\nDecompiled Source Code - IDA Pro/authentication\\constructallocatorV_Iterator0AlistUpairCBHPEAVCAsy_1403C89B0.c\nDecompiled Source Code - IDA Pro/authentication\\deallocateallocatorU_Node_List_nodUpairCBHPEAVCAsy_1403C6780.c\nDecompiled Source Code - IDA Pro/authentication\\deallocateallocatorV_Iterator0AlistUpairCBHPEAVCAs_1403C6C70.c\nDecompiled Source Code - IDA Pro/authentication\\destroyallocatorPEAU_Node_List_nodUpairCBHPEAVCAsy_1403C67D0.c\nDecompiled Source Code - IDA Pro/authentication\\destroyallocatorU_Node_List_nodUpairCBHPEAVCAsyncL_1403C7050.c\nDecompiled Source Code - IDA Pro/authentication\\destroyallocatorV_Iterator0AlistUpairCBHPEAVCAsync_1403C8A10.c\nDecompiled Source Code - IDA Pro/authentication\\dtor00__F_afxSessionMapYAXXZ4HA_0_14057B120.c\n... additional lines truncated ...\nDecompiled Source Code - IDA Pro/world\\j__BossDieWriteLog_StartCMonsterQEAAXEPEAVCGameObj_140004D40.c\nDecompiled Source Code - IDA Pro/world\\j__Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140001E65.c\nDecompiled Source Code - IDA Pro/world\\j__Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140004FA7.c\nDecompiled Source Code - IDA Pro/world\\j__BuyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_140012251.c\nDecompiled Source Code - IDA Pro/world\\j__BuyvectorPEAVCMoveMapLimitRightVallocatorPEAVCM_14000AD8F.c\nDecompiled Source Code - IDA Pro/world\\j__BuyvectorVCMoveMapLimitRightInfoVallocatorVCMov_14000A38A.c\nDecompiled Source Code - IDA Pro/world\\j__CheckDestMonsterLimitLvYA_NHHEZ_140011400.c\nDecompiled Source Code - IDA Pro/world\\j__Color_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140002B85.c\nDecompiled Source Code - IDA Pro/world\\j__ConstructPEAU_Node_Tree_nodV_Tmap_traitsVbasic__14000A7B3.c\nDecompiled Source Code - IDA Pro/world\\j__ConstructPEAVCMoveMapLimitRightPEAV1stdYAXPEAPE_14000378D.c\nDecompiled Source Code - IDA Pro/world\\j__ConstructVCMoveMapLimitRightInfoV1stdYAXPEAVCMo_1400022D9.c\nDecompiled Source Code - IDA Pro/world\\j__Copy_backward_optPEAPEAVCMoveMapLimitInfoPEAPEA_140004BD8.c\nDecompiled Source Code - IDA Pro/world\\j__Copy_backward_optPEAPEAVCMoveMapLimitRightPEAPE_140010A5A.c\nDecompiled Source Code - IDA Pro/world\\j__Copy_backward_optPEAVCMoveMapLimitRightInfoPEAV_140010E3D.c\nDecompiled Source Code - IDA Pro/world\\j__Copy_optPEAPEAVCMoveMapLimitInfoPEAPEAV1Urandom_140003F5D.c\nDecompiled Source Code - IDA Pro/world\\j__Copy_optPEAPEAVCMoveMapLimitRightPEAPEAV1Urando_14000B1FE.c\nDecompiled Source Code - IDA Pro/world\\j__Copy_optPEAVCMoveMapLimitRightInfoPEAV1Urandom__14000F128.c\nDecompiled Source Code - IDA Pro/world\\j__CreateMonYAPEAVCMonsterPEAD0MMMZ_1400074BE.c\nDecompiled Source Code - IDA Pro/world\\j__Decconst_iterator_TreeV_Tmap_traitsVbasic_strin_140012B48.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyPEAU_Node_Tree_nodV_Tmap_traitsVbasic_st_140011798.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyPEAVCMoveMapLimitRightstdYAXPEAPEAVCMove_140006AAF.c\nDecompiled Source Code - IDA Pro/world\\j__DestroySDMCMonsterSAXXZ_14000CCA7.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyU_Node_Tree_nodV_Tmap_traitsVbasic_strin_14000BD57.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyVCMoveMapLimitRightInfostdYAXPEAVCMoveMa_140001451.c\nDecompiled Source Code - IDA Pro/world\\j__Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEA_140004F20.c\nDecompiled Source Code - IDA Pro/world\\j__Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEA_14000F69B.c\nDecompiled Source Code - IDA Pro/world\\j__Destroy_rangePEAVCMoveMapLimitRightVallocatorPE_140008323.c\nDecompiled Source Code - IDA Pro/world\\j__Destroy_rangePEAVCMoveMapLimitRightVallocatorPE_1400090E3.c\nDecompiled Source Code - IDA Pro/world\\j__Destroy_rangeVCMoveMapLimitRightInfoVallocatorV_140005E98.c\nDecompiled Source Code - IDA Pro/world\\j__Destroy_rangeVCMoveMapLimitRightInfoVallocatorV_14000C0DB.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyvectorPEAVCMoveMapLimitInfoVallocatorPEA_1400083FA.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyvectorPEAVCMoveMapLimitRightVallocatorPE_14000D94A.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyvectorVCMoveMapLimitRightInfoVallocatorV_140012C65.c\nDecompiled Source Code - IDA Pro/world\\j__DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCS_140001799.c\nDecompiled Source Code - IDA Pro/world\\j__ECCircleZoneUEAAPEAXIZ_1400060EB.c\nDecompiled Source Code - IDA Pro/world\\j__ECMapDataUEAAPEAXIZ_140011BFD.c\nDecompiled Source Code - IDA Pro/world\\j__ECMapDisplayUEAAPEAXIZ_0_140007923.c\nDecompiled Source Code - IDA Pro/world\\j__ECMapDisplayUEAAPEAXIZ_140003332.c\nDecompiled Source Code - IDA Pro/world\\j__ECMapTabUEAAPEAXIZ_0_1400076B7.c\nDecompiled Source Code - IDA Pro/world\\j__ECMapTabUEAAPEAXIZ_140001B8B.c\nDecompiled Source Code - IDA Pro/world\\j__ECMonsterEventSetUEAAPEAXIZ_0_14000DA53.c\nDecompiled Source Code - IDA Pro/world\\j__ECMonsterEventSetUEAAPEAXIZ_14000D7C9.c\nDecompiled Source Code - IDA Pro/world\\j__ECMonsterUEAAPEAXIZ_14000546B.c\nDecompiled Source Code - IDA Pro/world\\j__Erase_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140005D94.c\nDecompiled Source Code - IDA Pro/world\\j__FillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVC_14000FA6F.c\nDecompiled Source Code - IDA Pro/world\\j__FillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAV_1400126CF.c\nDecompiled Source Code - IDA Pro/world\\j__FillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMove_14000FB8C.c\nDecompiled Source Code - IDA Pro/world\\j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1Urandom_ac_1400054C5.c\nDecompiled Source Code - IDA Pro/world\\j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1stdYAXPEAP_140001EF6.c\nDecompiled Source Code - IDA Pro/world\\j__Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1Urandom_a_140012F85.c\nDecompiled Source Code - IDA Pro/world\\j__Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1stdYAXPEA_140013C91.c\nDecompiled Source Code - IDA Pro/world\\j__FindV_Vector_iteratorPEAVCMoveMapLimitRightVall_14000AE43.c\nDecompiled Source Code - IDA Pro/world\\j__GBossSchedule_MapQEAAPEAXIZ_140002BFD.c\nDecompiled Source Code - IDA Pro/world\\j__GCCircleZoneUEAAPEAXIZ_140009CEB.c\nDecompiled Source Code - IDA Pro/world\\j__GCMapDataUEAAPEAXIZ_1400069B5.c\nDecompiled Source Code - IDA Pro/world\\j__GCMapOperationUEAAPEAXIZ_0_14001042E.c\nDecompiled Source Code - IDA Pro/world\\j__GCMapOperationUEAAPEAXIZ_14000A984.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterAIUEAAPEAXIZ_0_140013697.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterAIUEAAPEAXIZ_14000E4CB.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterEventRespawnUEAAPEAXIZ_0_14000C635.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterEventRespawnUEAAPEAXIZ_14000216C.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterHierarchyUEAAPEAXIZ_0_14000E633.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterHierarchyUEAAPEAXIZ_14000B5E1.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterUEAAPEAXIZ_1400109BF.c\nDecompiled Source Code - IDA Pro/world\\j__GCMoveMapLimitInfoQEAAPEAXIZ_14000E200.c\nDecompiled Source Code - IDA Pro/world\\j__GCMoveMapLimitManagerQEAAPEAXIZ_1400042B9.c\nDecompiled Source Code - IDA Pro/world\\j__GCMoveMapLimitRightInfoQEAAPEAXIZ_140012FC6.c\nDecompiled Source Code - IDA Pro/world\\j__GCMoveMapLimitRightQEAAPEAXIZ_1400129E5.c\nDecompiled Source Code - IDA Pro/world\\j__GCRFMonsterAIMgrQEAAPEAXIZ_14000934A.c\nDecompiled Source Code - IDA Pro/world\\j__G_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_1400011AE.c\nDecompiled Source Code - IDA Pro/world\\j__G__change_monsterQEAAPEAXIZ_140002572.c\nDecompiled Source Code - IDA Pro/world\\j__GetBaseClassCMapTabKAPEAUCRuntimeClassXZ_140006366.c\nDecompiled Source Code - IDA Pro/world\\j__GetBlinkNodeCMonsterAggroMgrIEAAPEAUCAggroNodeX_14000BFC3.c\nDecompiled Source Code - IDA Pro/world\\j__GetMonsterContTimeYAGEEZ_14000AF9C.c\nDecompiled Source Code - IDA Pro/world\\j__Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_0_14000E74B.c\nDecompiled Source Code - IDA Pro/world\\j__Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_140005C36.c\nDecompiled Source Code - IDA Pro/world\\j__Incconst_iterator_TreeV_Tmap_traitsVbasic_strin_14000FA0B.c\nDecompiled Source Code - IDA Pro/world\\j__Insert_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14000ECAA.c\nDecompiled Source Code - IDA Pro/world\\j__Insert_nvectorPEAVCMoveMapLimitInfoVallocatorPE_140012D5A.c\nDecompiled Source Code - IDA Pro/world\\j__Insert_nvectorPEAVCMoveMapLimitRightVallocatorP_140001749.c\nDecompiled Source Code - IDA Pro/world\\j__Insert_nvectorVCMoveMapLimitRightInfoVallocator_14000B460.c\nDecompiled Source Code - IDA Pro/world\\j__Isnil_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400042E6.c\nDecompiled Source Code - IDA Pro/world\\j__Iter_catPEAPEAVCMoveMapLimitInfostdYAAUrandom_a_14001348A.c\nDecompiled Source Code - IDA Pro/world\\j__Iter_catPEAPEAVCMoveMapLimitRightstdYAAUrandom__14000A146.c\nDecompiled Source Code - IDA Pro/world\\j__Iter_randomPEAPEAVCMoveMapLimitInfoPEAPEAV1stdY_14000CC9D.c\nDecompiled Source Code - IDA Pro/world\\j__Iter_randomPEAPEAVCMoveMapLimitRightPEAPEAV1std_14000E4B2.c\nDecompiled Source Code - IDA Pro/world\\j__Iter_randomPEAVCMoveMapLimitRightInfoPEAV1stdYA_140008E72.c\nDecompiled Source Code - IDA Pro/world\\j__Key_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140011793.c\nDecompiled Source Code - IDA Pro/world\\j__Kfn_Tmap_traitsVbasic_stringDUchar_traitsDstdVa_14000D7D8.c\nDecompiled Source Code - IDA Pro/world\\j__Lbound_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1400019DD.c\nDecompiled Source Code - IDA Pro/world\\j__Left_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000543E.c\nDecompiled Source Code - IDA Pro/world\\j__Lmost_TreeV_Tmap_traitsVbasic_stringDUchar_trai_14000DEC7.c\nDecompiled Source Code - IDA Pro/world\\j__LoadMonBlkCMapDataAEAA_NPEADPEAU_map_fldZ_14000C162.c\nDecompiled Source Code - IDA Pro/world\\j__LoadPortalCMapDataAEAA_NPEADZ_14000ADCB.c\nDecompiled Source Code - IDA Pro/world\\j__LoadQuestCMapDataAEAA_NPEADZ_140009AB6.c\nDecompiled Source Code - IDA Pro/world\\j__LoadResourceCMapDataAEAA_NPEADZ_140001500.c\nDecompiled Source Code - IDA Pro/world\\j__LoadSafeCMapDataAEAA_NPEADZ_140001762.c\nDecompiled Source Code - IDA Pro/world\\j__LoadStartCMapDataAEAA_NPEADZ_1400059C0.c\nDecompiled Source Code - IDA Pro/world\\j__LoadStoreDummyCMapDataAEAA_NPEADZ_140003ACB.c\nDecompiled Source Code - IDA Pro/world\\j__Lrotate_TreeV_Tmap_traitsVbasic_stringDUchar_tr_14000BB27.c\nDecompiled Source Code - IDA Pro/world\\j__Max_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140009DC2.c\nDecompiled Source Code - IDA Pro/world\\j__Min_TreeV_Tmap_traitsVbasic_stringDUchar_traits_1400133A9.c\nDecompiled Source Code - IDA Pro/world\\j__Move_backward_optPEAPEAVCMoveMapLimitInfoPEAPEA_140002347.c\nDecompiled Source Code - IDA Pro/world\\j__Move_backward_optPEAPEAVCMoveMapLimitRightPEAPE_14000B2B2.c\nDecompiled Source Code - IDA Pro/world\\j__Move_backward_optPEAVCMoveMapLimitRightInfoPEAV_140004570.c\nDecompiled Source Code - IDA Pro/world\\j__Move_catPEAPEAVCMoveMapLimitInfostdYAAU_Undefin_14000BC71.c\nDecompiled Source Code - IDA Pro/world\\j__Move_catPEAPEAVCMoveMapLimitRightstdYAAU_Undefi_140008D19.c\nDecompiled Source Code - IDA Pro/world\\j__Move_catPEAVCMoveMapLimitRightInfostdYAAU_Undef_14000DADA.c\nDecompiled Source Code - IDA Pro/world\\j__Mynodeconst_iterator_TreeV_Tmap_traitsVbasic_st_140004E62.c\nDecompiled Source Code - IDA Pro/world\\j__Myval_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400099C6.c\nDecompiled Source Code - IDA Pro/world\\j__Parent_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14000E4DA.c\nDecompiled Source Code - IDA Pro/world\\j__Ptr_catPEAPEAVCMoveMapLimitInfoPEAPEAV1stdYAAU__140011D1A.c\nDecompiled Source Code - IDA Pro/world\\j__Ptr_catPEAPEAVCMoveMapLimitRightPEAPEAV1stdYAAU_14000409D.c\nDecompiled Source Code - IDA Pro/world\\j__Ptr_catPEAVCMoveMapLimitRightInfoPEAV1stdYAAU_N_140005272.c\nDecompiled Source Code - IDA Pro/world\\j__Ptr_catV_Vector_const_iteratorPEAVCMoveMapLimit_140010640.c\nDecompiled Source Code - IDA Pro/world\\j__Right_TreeV_Tmap_traitsVbasic_stringDUchar_trai_14000B753.c\nDecompiled Source Code - IDA Pro/world\\j__Rmost_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140003E18.c\nDecompiled Source Code - IDA Pro/world\\j__Root_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000949E.c\nDecompiled Source Code - IDA Pro/world\\j__Rrotate_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140013926.c\nDecompiled Source Code - IDA Pro/world\\j__ShortRankCMonsterAggroMgrIEAAXXZ_140004381.c\nDecompiled Source Code - IDA Pro/world\\j__Tidy_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140007BAD.c\nDecompiled Source Code - IDA Pro/world\\j__TidyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140010DB6.c\nDecompiled Source Code - IDA Pro/world\\j__TidyvectorPEAVCMoveMapLimitRightVallocatorPEAVC_140013971.c\nDecompiled Source Code - IDA Pro/world\\j__TidyvectorVCMoveMapLimitRightInfoVallocatorVCMo_140013F25.c\nDecompiled Source Code - IDA Pro/world\\j__UcopyV_Vector_const_iteratorPEAVCMoveMapLimitRi_14001024E.c\nDecompiled Source Code - IDA Pro/world\\j__UfillvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1400104D8.c\nDecompiled Source Code - IDA Pro/world\\j__UfillvectorPEAVCMoveMapLimitRightVallocatorPEAV_14000CF5E.c\nDecompiled Source Code - IDA Pro/world\\j__UfillvectorVCMoveMapLimitRightInfoVallocatorVCM_140013ACA.c\nDecompiled Source Code - IDA Pro/world\\j__UmovePEAPEAVCMoveMapLimitInfovectorPEAVCMoveMap_14000C469.c\nDecompiled Source Code - IDA Pro/world\\j__UmovePEAPEAVCMoveMapLimitRightvectorPEAVCMoveMa_14000E98A.c\nDecompiled Source Code - IDA Pro/world\\j__UmovePEAVCMoveMapLimitRightInfovectorVCMoveMapL_1400126D4.c\nDecompiled Source Code - IDA Pro/world\\j__Unchecked_move_backwardPEAPEAVCMoveMapLimitInfo_140012AA8.c\nDecompiled Source Code - IDA Pro/world\\j__Unchecked_move_backwardPEAPEAVCMoveMapLimitRigh_14000807B.c\nDecompiled Source Code - IDA Pro/world\\j__Unchecked_move_backwardPEAVCMoveMapLimitRightIn_14000C856.c\nDecompiled Source Code - IDA Pro/world\\j__XlenvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140008D91.c\nDecompiled Source Code - IDA Pro/world\\j__XlenvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000B005.c\nDecompiled Source Code - IDA Pro/world\\j__XlenvectorVCMoveMapLimitRightInfoVallocatorVCMo_140008DFA.c\nDecompiled Source Code - IDA Pro/world\\j_allocateallocatorPEAVCMoveMapLimitInfostdQEAAPEA_14000984F.c\nDecompiled Source Code - IDA Pro/world\\j_allocateallocatorPEAVCMoveMapLimitRightstdQEAAPE_1400086DE.c\nDecompiled Source Code - IDA Pro/world\\j_allocateallocatorU_Node_Tree_nodV_Tmap_traitsVba_140006F28.c\nDecompiled Source Code - IDA Pro/world\\j_allocateallocatorVCMoveMapLimitRightInfostdQEAAP_14000CF6D.c\nDecompiled Source Code - IDA Pro/world\\j_assignvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_14000E5F2.c\nDecompiled Source Code - IDA Pro/world\\j_assignvectorVCMoveMapLimitRightInfoVallocatorVCM_1400059B6.c\nDecompiled Source Code - IDA Pro/world\\j_begin_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000965B.c\nDecompiled Source Code - IDA Pro/world\\j_beginvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140003F85.c\nDecompiled Source Code - IDA Pro/world\\j_beginvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000CEF0.c\nDecompiled Source Code - IDA Pro/world\\j_beginvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000D706.c\nDecompiled Source Code - IDA Pro/world\\j_beginvectorVCMoveMapLimitRightInfoVallocatorVCMo_1400132D7.c\nDecompiled Source Code - IDA Pro/world\\j_capacityvectorPEAVCMoveMapLimitInfoVallocatorPEA_140006839.c\nDecompiled Source Code - IDA Pro/world\\j_capacityvectorPEAVCMoveMapLimitRightVallocatorPE_14001099C.c\nDecompiled Source Code - IDA Pro/world\\j_capacityvectorVCMoveMapLimitRightInfoVallocatorV_140001F50.c\nDecompiled Source Code - IDA Pro/world\\j_check_dummyAutominePersonalMgrQEAA_NPEAVCMapData_140012DF0.c\nDecompiled Source Code - IDA Pro/world\\j_class_addVCMonsterlua_tinkerYAXPEAUlua_StatePEBD_140002D8D.c\nDecompiled Source Code - IDA Pro/world\\j_class_defVCMonsterP81EAAPEAVCLuaSignalReActorXZl_140002AC2.c\nDecompiled Source Code - IDA Pro/world\\j_clear_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000D657.c\nDecompiled Source Code - IDA Pro/world\\j_clearvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_1400102D0.c\nDecompiled Source Code - IDA Pro/world\\j_clearvectorPEAVCMoveMapLimitRightVallocatorPEAVC_140013F8E.c\nDecompiled Source Code - IDA Pro/world\\j_constructallocatorPEAU_Node_Tree_nodV_Tmap_trait_140007EF5.c\nDecompiled Source Code - IDA Pro/world\\j_constructallocatorPEAVCMoveMapLimitRightstdQEAAX_14000DD3C.c\nDecompiled Source Code - IDA Pro/world\\j_constructallocatorVCMoveMapLimitRightInfostdQEAA_14000FE43.c\nDecompiled Source Code - IDA Pro/world\\j_deallocateallocatorPEAVCMoveMapLimitInfostdQEAAX_140001D52.c\nDecompiled Source Code - IDA Pro/world\\j_deallocateallocatorPEAVCMoveMapLimitRightstdQEAA_14001327D.c\nDecompiled Source Code - IDA Pro/world\\j_deallocateallocatorU_Node_Tree_nodV_Tmap_traitsV_14001329B.c\nDecompiled Source Code - IDA Pro/world\\j_deallocateallocatorVCMoveMapLimitRightInfostdQEA_140003391.c\nDecompiled Source Code - IDA Pro/world\\j_defP6APEAVCMonsterPEAD0MMMZlua_tinkerYAXPEAUlua__14000A8E4.c\nDecompiled Source Code - IDA Pro/world\\j_destroyallocatorPEAU_Node_Tree_nodV_Tmap_traitsV_140007234.c\nDecompiled Source Code - IDA Pro/world\\j_destroyallocatorPEAVCMoveMapLimitRightstdQEAAXPE_140010226.c\nDecompiled Source Code - IDA Pro/world\\j_destroyallocatorU_Node_Tree_nodV_Tmap_traitsVbas_140013AAC.c\nDecompiled Source Code - IDA Pro/world\\j_destroyallocatorVCMoveMapLimitRightInfostdQEAAXP_14000D300.c\nDecompiled Source Code - IDA Pro/world\\j_destroyerVCMonsterlua_tinkerYAHPEAUlua_StateZ_1400137A5.c\nDecompiled Source Code - IDA Pro/world\\j_emptyvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000AC63.c\nDecompiled Source Code - IDA Pro/world\\j_end_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_1400054BB.c\nDecompiled Source Code - IDA Pro/world\\j_endvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_140002E41.c\nDecompiled Source Code - IDA Pro/world\\j_endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_140001D8E.c\nDecompiled Source Code - IDA Pro/world\\j_endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_14000B8ED.c\nDecompiled Source Code - IDA Pro/world\\j_endvectorVCMoveMapLimitRightInfoVallocatorVCMove_14000EC91.c\nDecompiled Source Code - IDA Pro/world\\j_erase_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140003968.c\nDecompiled Source Code - IDA Pro/world\\j_erase_TreeV_Tmap_traitsVbasic_stringDUchar_trait_1400118AB.c\nDecompiled Source Code - IDA Pro/world\\j_erasevectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_14000B7B7.c\nDecompiled Source Code - IDA Pro/world\\j_erasevectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000D391.c\nDecompiled Source Code - IDA Pro/world\\j_erasevectorVCMoveMapLimitRightInfoVallocatorVCMo_140008A9E.c\nDecompiled Source Code - IDA Pro/world\\j_fillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVCM_140002153.c\nDecompiled Source Code - IDA Pro/world\\j_fillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAVC_14001335E.c\nDecompiled Source Code - IDA Pro/world\\j_fillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMoveM_14001310B.c\nDecompiled Source Code - IDA Pro/world\\j_findV_Vector_iteratorPEAVCMoveMapLimitRightVallo_1400103B1.c\nDecompiled Source Code - IDA Pro/world\\j_gm_MapChangeCMainThreadQEAAXPEAVCMapDataZ_140008F6C.c\nDecompiled Source Code - IDA Pro/world\\j_gm_UpdateMapCMainThreadQEAAXXZ_1400050D3.c\nDecompiled Source Code - IDA Pro/world\\j_insert_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400086ED.c\nDecompiled Source Code - IDA Pro/world\\j_insert_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140011527.c\nDecompiled Source Code - IDA Pro/world\\j_insertvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1400024BE.c\nDecompiled Source Code - IDA Pro/world\\j_insertvectorPEAVCMoveMapLimitRightVallocatorPEAV_140006E60.c\nDecompiled Source Code - IDA Pro/world\\j_insertvectorVCMoveMapLimitRightInfoVallocatorVCM_14000DDFF.c\nDecompiled Source Code - IDA Pro/world\\j_invokePEAVCLuaSignalReActormem_functorVCMonsterX_14000309E.c\nDecompiled Source Code - IDA Pro/world\\j_invokePEAVCMonsterfunctorPEADPEADMMMlua_tinkerSA_14000BB2C.c\nDecompiled Source Code - IDA Pro/world\\j_invokelua2objectPEAVCMonsterlua_tinkerSAPEAVCMon_140012A80.c\nDecompiled Source Code - IDA Pro/world\\j_invokeobject2luaPEAVCMonsterlua_tinkerSAXPEAUlua_140003A3F.c\nDecompiled Source Code - IDA Pro/world\\j_invokeptr2luaVCMonsterlua_tinkerSAXPEAUlua_State_14000558D.c\nDecompiled Source Code - IDA Pro/world\\j_invokeuser2typeP6APEAVCMonsterPEAD0MMMZlua_tinke_14001023F.c\nDecompiled Source Code - IDA Pro/world\\j_invokeuser2typeP8CMonsterEAAPEAVCLuaSignalReActo_140009A7A.c\nDecompiled Source Code - IDA Pro/world\\j_invokevoid2ptrA6APEAVCMonsterPEAD0MMMZlua_tinker_1400024AA.c\nDecompiled Source Code - IDA Pro/world\\j_invokevoid2ptrVCMonsterlua_tinkerSAPEAVCMonsterP_14000FDAD.c\nDecompiled Source Code - IDA Pro/world\\j_invokevoid2typeP6APEAVCMonsterPEAD0MMMZlua_tinke_14000DDAF.c\nDecompiled Source Code - IDA Pro/world\\j_invokevoid2typeP8CMonsterEAAPEAVCLuaSignalReActo_14000A04C.c\nDecompiled Source Code - IDA Pro/world\\j_invokevoid2typePEAVCMonsterlua_tinkerSAPEAVCMons_140009070.c\nDecompiled Source Code - IDA Pro/world\\j_invokevoid2valP8CMonsterEAAPEAVCLuaSignalReActor_140013377.c\nDecompiled Source Code - IDA Pro/world\\j_lower_bound_TreeV_Tmap_traitsVbasic_stringDUchar_14000B23F.c\nDecompiled Source Code - IDA Pro/world\\j_lua2typePEAVCMonsterlua_tinkerYAPEAVCMonsterPEAU_14000E35E.c\nDecompiled Source Code - IDA Pro/world\\j_max_size_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140009B33.c\nDecompiled Source Code - IDA Pro/world\\j_max_sizeallocatorPEAVCMoveMapLimitInfostdQEBA_KX_1400129C2.c\nDecompiled Source Code - IDA Pro/world\\j_max_sizeallocatorPEAVCMoveMapLimitRightstdQEBA_K_14000A894.c\nDecompiled Source Code - IDA Pro/world\\j_max_sizeallocatorVCMoveMapLimitRightInfostdQEBA__140003788.c\nDecompiled Source Code - IDA Pro/world\\j_max_sizevectorPEAVCMoveMapLimitInfoVallocatorPEA_14000A26D.c\nDecompiled Source Code - IDA Pro/world\\j_max_sizevectorPEAVCMoveMapLimitRightVallocatorPE_14000991C.c\nDecompiled Source Code - IDA Pro/world\\j_max_sizevectorVCMoveMapLimitRightInfoVallocatorV_14000FFC4.c\nDecompiled Source Code - IDA Pro/world\\j_mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeon_14001007D.c\nDecompiled Source Code - IDA Pro/world\\j_mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDung_14000C9F0.c\nDecompiled Source Code - IDA Pro/world\\j_mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkH_140004ABB.c\nDecompiled Source Code - IDA Pro/world\\j_mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDun_140002BC1.c\nDecompiled Source Code - IDA Pro/world\\j_nameclass_nameVCMonsterlua_tinkerSAPEBDPEBDZ_140007130.c\nDecompiled Source Code - IDA Pro/world\\j_pc_AlterWorldServiceCMainThreadQEAAX_NZ_14000D15C.c\nDecompiled Source Code - IDA Pro/world\\j_pc_EnterWorldResultCMainThreadQEAAXEPEAU_CLIDZ_14000649C.c\nDecompiled Source Code - IDA Pro/world\\j_pc_OpenWorldFailureResultCMainThreadQEAAXPEADZ_1400082E7.c\nDecompiled Source Code - IDA Pro/world\\j_pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1400132C8.c\nDecompiled Source Code - IDA Pro/world\\j_pushPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAVCM_140004705.c\nDecompiled Source Code - IDA Pro/world\\j_push_backvectorPEAVCMoveMapLimitRightVallocatorP_140009917.c\nDecompiled Source Code - IDA Pro/world\\j_push_functorPEAVCLuaSignalReActorVCMonsterlua_ti_1400031B6.c\nDecompiled Source Code - IDA Pro/world\\j_push_functorPEAVCMonsterPEADPEADMMMlua_tinkerYAX_14000DA1C.c\nDecompiled Source Code - IDA Pro/world\\j_qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140011D5B.c\nDecompiled Source Code - IDA Pro/world\\j_qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDunge_1400066DB.c\nDecompiled Source Code - IDA Pro/world\\j_readPEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlua__140010726.c\nDecompiled Source Code - IDA Pro/world\\j_set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__140006654.c\nDecompiled Source Code - IDA Pro/world\\j_size_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14001127A.c\nDecompiled Source Code - IDA Pro/world\\j_size_add_char_result_zoneQEAAHXZ_1400047E1.c\nDecompiled Source Code - IDA Pro/world\\j_size_del_char_result_zoneQEAAHXZ_14000C180.c\nDecompiled Source Code - IDA Pro/world\\j_size_enter_world_request_wracQEAAHXZ_14000B924.c\nDecompiled Source Code - IDA Pro/world\\j_size_enter_world_result_zoneQEAAHXZ_14000B06E.c\nDecompiled Source Code - IDA Pro/world\\j_size_move_to_own_stonemap_inform_zoclQEAAHXZ_1400015A5.c\nDecompiled Source Code - IDA Pro/world\\j_size_move_to_own_stonemap_result_zoclQEAAHXZ_140007C7F.c\nDecompiled Source Code - IDA Pro/world\\j_size_moveout_user_result_zoneQEAAHXZ_140006D34.c\nDecompiled Source Code - IDA Pro/world\\j_size_open_world_request_wracQEAAHXZ_140009F61.c\nDecompiled Source Code - IDA Pro/world\\j_size_reged_char_result_zoneQEAAHXZ_140003044.c\nDecompiled Source Code - IDA Pro/world\\j_size_sel_char_result_zoneQEAAHXZ_140012468.c\nDecompiled Source Code - IDA Pro/world\\j_size_server_notify_inform_zoneQEAAHXZ_140005187.c\nDecompiled Source Code - IDA Pro/world\\j_size_start_world_request_wracQEAAHXZ_14000BF55.c\nDecompiled Source Code - IDA Pro/world\\j_size_stop_world_request_wracQEAAHXZ_140005D3F.c\nDecompiled Source Code - IDA Pro/world\\j_size_target_monster_contsf_allinform_zoclQEAAHXZ_14000B2D0.c\nDecompiled Source Code - IDA Pro/world\\j_size_world_account_ping_wracQEAAHXZ_14000276B.c\nDecompiled Source Code - IDA Pro/world\\j_sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1400080A3.c\nDecompiled Source Code - IDA Pro/world\\j_sizevectorPEAVCMoveMapLimitRightVallocatorPEAVCM_140004D81.c\nDecompiled Source Code - IDA Pro/world\\j_sizevectorVCMoveMapLimitRightInfoVallocatorVCMov_14000D3D7.c\nDecompiled Source Code - IDA Pro/world\\j_type2luaPEAVCMonsterlua_tinkerYAXPEAUlua_StatePE_140004C2D.c\nDecompiled Source Code - IDA Pro/world\\j_unchecked_copyPEAPEAVCMoveMapLimitInfoPEAPEAV1st_14000D84B.c\nDecompiled Source Code - IDA Pro/world\\j_unchecked_copyPEAPEAVCMoveMapLimitRightPEAPEAV1s_14000F281.c\nDecompiled Source Code - IDA Pro/world\\j_unchecked_copyPEAVCMoveMapLimitRightInfoPEAV1std_140001276.c\nDecompiled Source Code - IDA Pro/world\\j_unchecked_fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1s_140007C61.c\nDecompiled Source Code - IDA Pro/world\\j_unchecked_fill_nPEAPEAVCMoveMapLimitRight_KPEAV1_140004D5E.c\nDecompiled Source Code - IDA Pro/world\\j_unregist_from_mapAutominePersonalQEAA_NEZ_14000267B.c\nDecompiled Source Code - IDA Pro/world\\j_upvalue_P6APEAVCMonsterPEAD0MMMZlua_tinkerYAP6AP_14000A439.c\nDecompiled Source Code - IDA Pro/world\\j_upvalue_P8CMonsterEAAPEAVCLuaSignalReActorXZlua__14000F0FB.c\nDecompiled Source Code - IDA Pro/world\\j_wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_14000B4BA.c\nDecompiled Source Code - IDA Pro/world\\j_wa_ExitWorldYAXPEAU_CLIDZ_14000FFAB.c\nDecompiled Source Code - IDA Pro/world\\lower_bound_TreeV_Tmap_traitsVbasic_stringDUchar_t_14018EB80.c\nDecompiled Source Code - IDA Pro/world\\lua2typePEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlu_14040AFF0.c\nDecompiled Source Code - IDA Pro/world\\max_size_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1401913D0.c\nDecompiled Source Code - IDA Pro/world\\max_sizeallocatorPEAVCMoveMapLimitInfostdQEBA_KXZ_1403A31C0.c\nDecompiled Source Code - IDA Pro/world\\max_sizeallocatorPEAVCMoveMapLimitRightstdQEBA_KXZ_1403B0CC0.c\nDecompiled Source Code - IDA Pro/world\\max_sizeallocatorVCMoveMapLimitRightInfostdQEBA_KX_1403A3230.c\nDecompiled Source Code - IDA Pro/world\\max_sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1403A2A70.c\nDecompiled Source Code - IDA Pro/world\\max_sizevectorPEAVCMoveMapLimitRightVallocatorPEAV_1403AFD20.c\nDecompiled Source Code - IDA Pro/world\\max_sizevectorVCMoveMapLimitRightInfoVallocatorVCM_1403A2CE0.c\nDecompiled Source Code - IDA Pro/world\\mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeonQu_140275730.c\nDecompiled Source Code - IDA Pro/world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c\nDecompiled Source Code - IDA Pro/world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c\nDecompiled Source Code - IDA Pro/world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c\nDecompiled Source Code - IDA Pro/world\\nameclass_nameVCMonsterlua_tinkerSAPEBDPEBDZ_1404082A0.c\nDecompiled Source Code - IDA Pro/world\\pc_AlterWorldServiceCMainThreadQEAAX_NZ_1401F61B0.c\nDecompiled Source Code - IDA Pro/world\\pc_EnterWorldResultCMainThreadQEAAXEPEAU_CLIDZ_1401F5B30.c\nDecompiled Source Code - IDA Pro/world\\pc_OpenWorldFailureResultCMainThreadQEAAXPEADZ_1401F56F0.c\nDecompiled Source Code - IDA Pro/world\\pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c\nDecompiled Source Code - IDA Pro/world\\pushPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAVCMon_14040A3F0.c\nDecompiled Source Code - IDA Pro/world\\push_backvectorPEAVCMoveMapLimitRightVallocatorPEA_1403AEB10.c\nDecompiled Source Code - IDA Pro/world\\push_functorPEAVCLuaSignalReActorVCMonsterlua_tink_1404089C0.c\nDecompiled Source Code - IDA Pro/world\\push_functorPEAVCMonsterPEADPEADMMMlua_tinkerYAXPE_140408A80.c\nDecompiled Source Code - IDA Pro/world\\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.c\nDecompiled Source Code - IDA Pro/world\\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.c\nDecompiled Source Code - IDA Pro/world\\readPEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlua_St_14040A210.c\nDecompiled Source Code - IDA Pro/world\\set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__r_14026F0F0.c\nDecompiled Source Code - IDA Pro/world\\size_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_14018FFA0.c\nDecompiled Source Code - IDA Pro/world\\size_add_char_result_zoneQEAAHXZ_14011F870.c\nDecompiled Source Code - IDA Pro/world\\size_del_char_result_zoneQEAAHXZ_14011F880.c\nDecompiled Source Code - IDA Pro/world\\size_enter_world_request_wracQEAAHXZ_14011F240.c\nDecompiled Source Code - IDA Pro/world\\size_enter_world_result_zoneQEAAHXZ_14011F250.c\nDecompiled Source Code - IDA Pro/world\\size_move_to_own_stonemap_inform_zoclQEAAHXZ_1400F03D0.c\nDecompiled Source Code - IDA Pro/world\\size_move_to_own_stonemap_result_zoclQEAAHXZ_1400F03C0.c\nDecompiled Source Code - IDA Pro/world\\size_moveout_user_result_zoneQEAAHXZ_14011FBD0.c\nDecompiled Source Code - IDA Pro/world\\size_open_world_request_wracQEAAHXZ_1402080C0.c\nDecompiled Source Code - IDA Pro/world\\size_reged_char_result_zoneQEAAHXZ_14011F6F0.c\nDecompiled Source Code - IDA Pro/world\\size_sel_char_result_zoneQEAAHXZ_14011F8D0.c\nDecompiled Source Code - IDA Pro/world\\size_server_notify_inform_zoneQEAAHXZ_14011F1E0.c\nDecompiled Source Code - IDA Pro/world\\size_start_world_request_wracQEAAHXZ_1402080D0.c\nDecompiled Source Code - IDA Pro/world\\size_stop_world_request_wracQEAAHXZ_1402080E0.c\nDecompiled Source Code - IDA Pro/world\\size_target_monster_contsf_allinform_zoclQEAAHXZ_1400F0140.c\nDecompiled Source Code - IDA Pro/world\\size_world_account_ping_wracQEAAHXZ_1402080F0.c\nDecompiled Source Code - IDA Pro/world\\sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMove_1403A78D0.c\nDecompiled Source Code - IDA Pro/world\\sizevectorPEAVCMoveMapLimitRightVallocatorPEAVCMov_1403AE9F0.c\nDecompiled Source Code - IDA Pro/world\\sizevectorVCMoveMapLimitRightInfoVallocatorVCMoveM_1403A21E0.c\nDecompiled Source Code - IDA Pro/world\\type2luaPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAV_14040B0E0.c\nDecompiled Source Code - IDA Pro/world\\unchecked_copyPEAPEAVCMoveMapLimitInfoPEAPEAV1stde_1403AAB40.c\nDecompiled Source Code - IDA Pro/world\\unchecked_copyPEAPEAVCMoveMapLimitRightPEAPEAV1std_1403B1C00.c\nDecompiled Source Code - IDA Pro/world\\unchecked_copyPEAVCMoveMapLimitRightInfoPEAV1stdex_1403B21A0.c\nDecompiled Source Code - IDA Pro/world\\unchecked_fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1std_1403ABFD0.c\nDecompiled Source Code - IDA Pro/world\\unchecked_fill_nPEAPEAVCMoveMapLimitRight_KPEAV1st_1403B3280.c\nDecompiled Source Code - IDA Pro/world\\unregist_from_mapAutominePersonalQEAA_NEZ_1402DB110.c\nDecompiled Source Code - IDA Pro/world\\upvalue_P6APEAVCMonsterPEAD0MMMZlua_tinkerYAP6APEA_14040A3A0.c\nDecompiled Source Code - IDA Pro/world\\upvalue_P8CMonsterEAAPEAVCLuaSignalReActorXZlua_ti_14040A260.c\nDecompiled Source Code - IDA Pro/world\\wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.c\nDecompiled Source Code - IDA Pro/world\\wa_ExitWorldYAXPEAU_CLIDZ_140046190.c\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01BfMfHuj83Mnt1tCbW91KxT&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;,&quot;folderRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-15T08:48:03.255Z&quot;,&quot;response_text&quot;:&quot;Let me search for the \&quot;RF SERVER NOW LOADING\&quot; message in the main thread or startup code:\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;8192f3dc-1072-40f8-8fa8-1eb0ab083e1e&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01L6FFggsbWdpMaTJDbKBh92&quot;,&quot;tool_name&quot;:&quot;launch-process&quot;,&quot;input_json&quot;:&quot;{\&quot;command\&quot;: \&quot;findstr /s /i \\\&quot;RF SERVER NOW LOADING\\\&quot; \\\&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard\\\\Decompiled Source Code - IDA Pro\\\\*\\\&quot;\&quot;, \&quot;cwd\&quot;: \&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard\&quot;, \&quot;wait\&quot;: true, \&quot;max_wait_seconds\&quot;: 30}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;Let me search for the exact string \&quot;RF SERVER NOW LOADING\&quot; in the entire codebase:\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on Decompiled Source Code - IDA Pro/world/pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c:\n     1\t/*\n     2\t * Function: ?pc_OpenWorldSuccessResult@CMainThread@@QEAAXEPEAD0@Z\n     3\t * Address: 0x1401F5580\n     4\t */\n     5\t\n     6\tvoid __fastcall CMainThread::pc_OpenWorldSuccessResult(CMainThread *this, char byWorldCode, char *pszDBName, char *pszDBIP)\n     7\t{\n     8\t  __int64 *v4; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v6; // [sp+0h] [bp-178h]@1\n    11\t  char Buffer; // [sp+30h] [bp-148h]@4\n    12\t  char v8; // [sp+D0h] [bp-A8h]@4\n    13\t  unsigned __int64 v9; // [sp+160h] [bp-18h]@4\n    14\t  CMainThread *v10; // [sp+180h] [bp+8h]@1\n    15\t  char v11; // [sp+188h] [bp+10h]@1\n    16\t  char *pszDBNamea; // [sp+190h] [bp+18h]@1\n    17\t  char *pszDBIPa; // [sp+198h] [bp+20h]@1\n    18\t\n    19\t  pszDBIPa = pszDBIP;\n    20\t  pszDBNamea = pszDBName;\n    21\t  v11 = byWorldCode;\n    22\t  v10 = this;\n    23\t  v4 = &amp;v6;\n    24\t  for ( i = 92i64; i; --i )\n    25\t  {\n    26\t    *(_DWORD *)v4 = -*********;\n    27\t    v4 = (__int64 *)((char *)v4 + 4);\n    28\t  }\n    29\t  v9 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    30\t  _strdate(&amp;Buffer);\n    31\t  _strtime(&amp;v8);\n    32\t  __trace(\&quot;%s-%s: Open World\&quot;, &amp;Buffer, &amp;v8);\n    33\t  v10-&gt;m_byWorldCode = v11;\n    34\t  WriteServerStartHistory(\&quot;DBInit Begin &gt;&gt; name: %s, ip: %s\&quot;, pszDBNamea, pszDBIPa);\n    35\t  if ( CMainThread::DatabaseInit(v10, pszDBNamea, pszDBIPa) )\n    36\t  {\n    37\t    v10-&gt;m_bWorldOpen = 1;\n    38\t    CMainThread::SerivceSelfStart(v10);\n    39\t    WriteServerStartHistory(\&quot;DBInit Complete &gt;&gt;\&quot;);\n    40\t  }\n    41\t  else\n    42\t  {\n    43\t    WriteServerStartHistory(\&quot;DBInit Fail &gt;&gt;\&quot;);\n    44\t    CLogFile::WriteString(&amp;v10-&gt;m_logSystemError, \&quot;init DB fail\&quot;);\n    45\t    CWnd::SendMessageA(g_pFrame, 0x10u, 0i64, 0i64);\n    46\t  }\n    47\t}\n    48\t\nTotal lines in file: 48\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01QgBikgZeKtF6WaP9i8Nyyk&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;,&quot;folderRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-15T08:48:30.694Z&quot;,&quot;response_text&quot;:&quot;Let me search for the exact string \&quot;RF SERVER NOW LOADING\&quot; in the entire codebase:\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;de31b5cf-f4f5-4ae5-be46-f9bfa09aab2f&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:0,&quot;content&quot;:&quot;Based on my search through the codebase, I couldn't find the exact string \&quot;RF SERVER NOW LOADING\&quot; in the source code. However, I did find several related components that might display this message when the zoneserver starts up.\n\nThe main components involved in server startup are:\n\n1. The `CMainThread::pc_OpenWorldSuccessResult` function in `Decompiled Source Code - IDA Pro/world/pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c` which handles the successful opening of the world server.\n\n2. The server startup logging functions like `WriteServerStartHistory` which log messages during server initialization.\n\n3. The `LoadWorldInfoINI` function that loads configuration from the WorldInfo.ini file.\n\nThe message \&quot;RF SERVER NOW LOADING\&quot; is likely displayed in one of these places:\n\n1. It might be displayed in a UI component that isn't directly visible in the decompiled code\n2. It could be part of a resource string that's loaded at runtime\n3. It might be in a log file that's written during server startup\n\nSince I couldn't find the exact string in the code, the message might be:\n\n1. Generated by a UI framework component when the server starts\n2. Displayed by the game client when connecting to the server, rather than by the server itself\n3. Part of a splash screen or loading screen resource\n\nIf you need to modify this message, you would likely need to look at:\n1. The server startup UI code\n2. Resource files or string tables\n3. Log message templates\n\nWould you like me to search for any specific related components or functions that might help locate where this message is displayed?\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here are the results from executing the command.\n        &lt;return-code&gt;\n        0\n        &lt;/return-code&gt;\n        &lt;stdout&gt;\n        \u001b[?9001h\u001b[?1004h\u001b[?25l\u001b[2J\u001b[m\u001b[38;5;15m\u001b[HD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\0MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140465820.c:  \u001b[mCryptoPP::AlgorithmImpl&lt;CryptoPP::SimpleKeyingInterfaceImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;::AlgorithmImpl&lt;CryptoPP::SimpleKeyingInterfaceImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;((CryptoPP::AlgorithmImpl&lt;CryptoPP::SimpleKeyingInterfaceImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt; &gt;,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt; &gt; *)&amp;v4-&gt;vfptr);\u001b[38;5;15m\nD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\0MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140465820.c:  \u001b[mv4-&gt;vfptr = (CryptoPP::SimpleKeyingInterfaceVtbl *)&amp;CryptoPP::MessageAuthenticationCodeImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;::`vftable'{for `CryptoPP::SimpleKeyingInterface'};\u001b[38;5;15m\nD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\1MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140464F50.c:  \u001b[mCryptoPP::AlgorithmImpl&lt;CryptoPP::SimpleKeyingInterfaceImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;::~AlgorithmImpl&lt;CryptoPP::SimpleKeyingInterfaceImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;((CryptoPP::AlgorithmImpl&lt;CryptoPP::SimpleKeyingInterfaceImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt; &gt;,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt; &gt; *)&amp;v4-&gt;vfptr);\u001b[38;5;15m\nD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\AccountServerLoginCMainThreadQEAAXXZ_1401F8140.c: \u001b[m* Function: ?AccountServerLogin@CMainThread@@QEAAXXZ\u001b[38;5;15m\nD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\AccountServerLoginCMainThreadQEAAXXZ_1401F8140.c:\u001b[mvoid __fastcall CMainThread::AccountServerLogin(CMainThread *this)\n\u001b]0;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe\u0007\u001b[?25h\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallFunc_RFOnline_AuthCEnglandBillingMgrQEAAHAEAU__1403198F0.c: \u001b[m* Function: ?CallFunc_RFOnline_Auth@CEnglandBillingMgr@@QEAAHAEAU_param_cash_select@@H@Z\u001b[38;5;15m\nD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallFunc_RFOnline_AuthCEnglandBillingMgrQEAAHAEAU__1403198F0.c:\u001b[msigned __int64 __fastcall CEnglandBillingMgr::CallFunc_RFOnline_Auth(CEnglandBillingMgr *this, _param_cash_select *rParam, int nIdx)\u001b[38;5;15m\nD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallFunc_RFOnline_AuthCRusiaBillingMgrQEAAHAEAU_pa_1403213A0.c: \u001b[m* Function: ?CallFunc_RFOnline_Auth@CRusiaBillingMgr@@QEAAHAEAU_param_cash_select@@\n\u001b[24;120H@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallFunc_RFOnline_AuthCRusi\u001b[m\n\u001b[38;5;15m\u001b[24;120HiaBillingMgrQEAAHAEAU_pa_1403213A0.c:\u001b[m__int64 __usercall CRusiaBillingMgr::CallFunc_RFOnline_Auth@&lt;rax&gt;(CRusiaBillingMgr *\n\u001b[24;120H*this@&lt;rcx&gt;, _param_cash_select *rParam@&lt;rdx&gt;, double a3@&lt;xmm0&gt;)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallFunc_RFOnline_AuthCRusi\u001b[m\n\u001b[38;5;15m\u001b[24;120HiaBillingMgrQEAAHAEAU_pa_1403213A0.c:  \u001b[mRFACC_CheckBalance(v8-&gt;in_szAcc);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c: \u001b[m* Function: ?CallProc_RFOnlineAuth@CRFCashItemDatabase@@QEAAHAEAU_param_cash_select\n\u001b[24;120Ht@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:\u001b[msigned __int64 __fastcall CRFCashItemDatabase::CallProc_RFOnlineAuth(CRFCashItemData\n\u001b[24;120Habase *this, _param_cash_select *rParam)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:  \u001b[mCRFCashItemDatabase *v15; // [sp+190h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:    \u001b[m\&quot;declare @out_amount int exec prc_rfonline_auth '%s', @s_amount = @out_amount ou\n\u001b[24;120Hutput select @out_amount\&quot;,\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:    \u001b[mCRFNewDatabase::Log((CRFNewDatabase *)&amp;v15-&gt;vfptr, &amp;DstBuf);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:  \u001b[mif ( v15-&gt;m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&amp;v1\n\u001b[24;120H15-&gt;vfptr) )\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:        \u001b[mCRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, v11, &amp;DstBuf, \&quot;SQ\n\u001b[24;120HQLExecDirect\&quot;, SQLStmt);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:        \u001b[mCRFNewDatabase::ErrorAction((CRFNewDatabase *)&amp;v15-&gt;vfptr, v11, v15-&gt;m_hStmt\n\u001b[24;120HtSelect);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:          \u001b[mCRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, v11, &amp;DstBuf, \&quot;\n\u001b[24;120H\&quot;SQLExecDirect\&quot;, SQLStmt);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:          \u001b[mCRFNewDatabase::ErrorAction((CRFNewDatabase *)&amp;v15-&gt;vfptr, v11, v15-&gt;m_hSt\n\u001b[24;120HtmtSelect);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:            \u001b[mCRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, v11, &amp;DstBuf,\n\u001b[24;120H, \&quot;SQLExecDirect\&quot;, SQLStmt);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:            \u001b[mCRFNewDatabase::ErrorAction((CRFNewDatabase *)&amp;v15-&gt;vfptr, v11, v15-&gt;m_h\n\u001b[24;120HhStmtSelect);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:            \u001b[mCRFNewDatabase::FmtLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, \&quot;%s Success\&quot;, &amp;Dst\n\u001b[24;120HtBuf);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:    \u001b[mCRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, \&quot;ReConnectDataBase Fail\n\u001b[24;120Hl. Query : %s\&quot;, &amp;DstBuf);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c: \u001b[m* Function: ?CallProc_RFOnlineAuth_Jap@CRFCashItemDatabase@@QEAAHAEAU_param_cash_se\n\u001b[24;120Helect@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:\u001b[msigned __int64 __fastcall CRFCashItemDatabase::CallProc_RFOnlineAuth_Jap(CRFCashItem\n\u001b[24;120HmDatabase *this, _param_cash_select *rParam)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:  \u001b[mCRFCashItemDatabase *v15; // [sp+190h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:    \u001b[m\&quot;declare @out_amount int exec dbo.SP_RF_CHK_GEM_GAMEON @uid = '%s', @s_amount = \n\u001b[24;120H @out_amount output select @out_amount\&quot;,\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:    \u001b[mCRFNewDatabase::Log((CRFNewDatabase *)&amp;v15-&gt;vfptr, &amp;DstBuf);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:  \u001b[mif ( v15-&gt;m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&amp;v1\n\u001b[24;120H15-&gt;vfptr) )\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:        \u001b[mCRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, v9, &amp;DstBuf, \&quot;SQL\n\u001b[24;120HLExecDirect\&quot;, SQLStmt);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:        \u001b[mCRFNewDatabase::ErrorAction((CRFNewDatabase *)&amp;v15-&gt;vfptr, v9, v15-&gt;m_hStmtS\n\u001b[24;120HSelect);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:          \u001b[mCRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, v9, &amp;DstBuf, \&quot;S\n\u001b[24;120HSQLExecDirect\&quot;, SQLStmt);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:          \u001b[mCRFNewDatabase::ErrorAction((CRFNewDatabase *)&amp;v15-&gt;vfptr, v9, v15-&gt;m_hStm\n\u001b[24;120HmtSelect);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:            \u001b[mCRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, v9, &amp;DstBuf, \n\u001b[24;120H \&quot;SQLExecDirect\&quot;, SQLStmt);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:            \u001b[mCRFNewDatabase::ErrorAction((CRFNewDatabase *)&amp;v15-&gt;vfptr, v9, v15-&gt;m_hS\n\u001b[24;120HStmtSelect);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:            \u001b[mCRFNewDatabase::FmtLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, \&quot;%s Success\&quot;, &amp;Dst\n\u001b[24;120HtBuf);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:    \u001b[mCRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, \&quot;ReConnectDataBase Fail\n\u001b[24;120Hl. Query : %s\&quot;, &amp;DstBuf);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\InitCAsyncLogInfoQEAA_NW4AS\u001b[m\n\u001b[38;5;15m\u001b[24;120HSYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c:\u001b[mchar __usercall CAsyncLogInfo::Init@&lt;al&gt;(CAsyncLogInfo *this@&lt;rcx&gt;, ASYNC_LOG_TYPE e\n\u001b[24;120HeType@&lt;edx&gt;, const char *szDirPath@&lt;r8&gt;, const char *szTypeName@&lt;r9&gt;, signed __int64 a5@&lt;rax&gt;, bool bAddDateFileName, uns\n\u001b[24;120Hsigned int dwUpdateFileNameDelay, CLogFile *logLoading)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\InitCAsyncLogInfoQEAA_NW4AS\u001b[m\n\u001b[38;5;15m\u001b[24;120HSYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c:      \u001b[mlogLoading,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\InitCAsyncLogInfoQEAA_NW4AS\u001b[m\n\u001b[38;5;15m\u001b[24;120HSYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c:\u001b[m\u001b[12ClogLoading,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\InitCAsyncLogInfoQEAA_NW4AS\u001b[m\n\u001b[38;5;15m\u001b[24;120HSYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c:\u001b[m\u001b[10ClogLoading,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\InitCAsyncLogInfoQEAA_NW4AS\u001b[m\n\u001b[38;5;15m\u001b[24;120HSYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c:        \u001b[mlogLoading,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\InitCAsyncLogInfoQEAA_NW4AS\u001b[m\n\u001b[38;5;15m\u001b[24;120HSYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c:      \u001b[mlogLoading,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_AccountServerLoginCMainTh\u001b[m\n\u001b[38;5;15m\u001b[24;120HhreadQEAAXXZ_14001102C.c: \u001b[m* Function: j_?AccountServerLogin@CMainThread@@QEAAXXZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_AccountServerLoginCMainTh\u001b[m\n\u001b[38;5;15m\u001b[24;120HhreadQEAAXXZ_14001102C.c:\u001b[mvoid __fastcall CMainThread::AccountServerLogin(CMainThread *this)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_AccountServerLoginCMainTh\u001b[m\n\u001b[38;5;15m\u001b[24;120HhreadQEAAXXZ_14001102C.c:  \u001b[mCMainThread::AccountServerLogin(this);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallFunc_RFOnline_AuthCEn\u001b[m\n\u001b[38;5;15m\u001b[24;120HnglandBillingMgrQEAAHAEA_14000D4B3.c: \u001b[m* Function: j_?CallFunc_RFOnline_Auth@CEnglandBillingMgr@@QEAAHAEAU_param_cash_sele\n\u001b[24;120Hect@@H@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallFunc_RFOnline_AuthCEn\u001b[m\n\u001b[38;5;15m\u001b[24;120HnglandBillingMgrQEAAHAEA_14000D4B3.c:\u001b[mint __fastcall CEnglandBillingMgr::CallFunc_RFOnline_Auth(CEnglandBillingMgr *this, \n\u001b[24;120H _param_cash_select *rParam, int nIdx)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallFunc_RFOnline_AuthCEn\u001b[m\n\u001b[38;5;15m\u001b[24;120HnglandBillingMgrQEAAHAEA_14000D4B3.c:  \u001b[mreturn CEnglandBillingMgr::CallFunc_RFOnline_Auth(this, rParam, nIdx);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallFunc_RFOnline_AuthCRu\u001b[m\n\u001b[38;5;15m\u001b[24;120HusiaBillingMgrQEAAHAEAU__140001E24.c: \u001b[m* Function: j_?CallFunc_RFOnline_Auth@CRusiaBillingMgr@@QEAAHAEAU_param_cash_select\n\u001b[24;120Ht@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallFunc_RFOnline_AuthCRu\u001b[m\n\u001b[38;5;15m\u001b[24;120HusiaBillingMgrQEAAHAEAU__140001E24.c:\u001b[mint __fastcall CRusiaBillingMgr::CallFunc_RFOnline_Auth(CRusiaBillingMgr *this, _par\n\u001b[24;120Hram_cash_select *rParam)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallFunc_RFOnline_AuthCRu\u001b[m\n\u001b[38;5;15m\u001b[24;120HusiaBillingMgrQEAAHAEAU__140001E24.c:  \u001b[mreturn CRusiaBillingMgr::CallFunc_RFOnline_Auth(this, rParam);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallProc_RFOnlineAuthCRFC\u001b[m\n\u001b[38;5;15m\u001b[24;120HCashItemDatabaseQEAAHAEA_14000C2F2.c: \u001b[m* Function: j_?CallProc_RFOnlineAuth@CRFCashItemDatabase@@QEAAHAEAU_param_cash_sele\n\u001b[24;120Hect@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallProc_RFOnlineAuthCRFC\u001b[m\n\u001b[38;5;15m\u001b[24;120HCashItemDatabaseQEAAHAEA_14000C2F2.c:\u001b[mint __fastcall CRFCashItemDatabase::CallProc_RFOnlineAuth(CRFCashItemDatabase *this,\n\u001b[24;120H, _param_cash_select *rParam)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallProc_RFOnlineAuthCRFC\u001b[m\n\u001b[38;5;15m\u001b[24;120HCashItemDatabaseQEAAHAEA_14000C2F2.c:  \u001b[mreturn CRFCashItemDatabase::CallProc_RFOnlineAuth(this, rParam);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallProc_RFOnlineAuth_Jap\u001b[m\n\u001b[38;5;15m\u001b[24;120HpCRFCashItemDatabaseQEAA_14000B334.c: \u001b[m* Function: j_?CallProc_RFOnlineAuth_Jap@CRFCashItemDatabase@@QEAAHAEAU_param_cash_\n\u001b[24;120H_select@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallProc_RFOnlineAuth_Jap\u001b[m\n\u001b[38;5;15m\u001b[24;120HpCRFCashItemDatabaseQEAA_14000B334.c:\u001b[mint __fastcall CRFCashItemDatabase::CallProc_RFOnlineAuth_Jap(CRFCashItemDatabase *t\n\u001b[24;120Hthis, _param_cash_select *rParam)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallProc_RFOnlineAuth_Jap\u001b[m\n\u001b[38;5;15m\u001b[24;120HpCRFCashItemDatabaseQEAA_14000B334.c:  \u001b[mreturn CRFCashItemDatabase::CallProc_RFOnlineAuth_Jap(this, rParam);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_InitCAsyncLogInfoQEAA_NW4\u001b[m\n\u001b[38;5;15m\u001b[24;120H4ASYNC_LOG_TYPEPEBD1_NKA_14000C3F1.c:\u001b[mbool __fastcall CAsyncLogInfo::Init(CAsyncLogInfo *this, ASYNC_LOG_TYPE eType, const\n\u001b[24;120Ht char *szDirPath, const char *szTypeName, bool bAddDateFileName, unsigned int dwUpdateFileNameDelay, CLogFile *logLoadin\n\u001b[24;120Hng)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_InitCAsyncLogInfoQEAA_NW4\u001b[m\n\u001b[38;5;15m\u001b[24;120H4ASYNC_LOG_TYPEPEBD1_NKA_14000C3F1.c:  \u001b[mreturn CAsyncLogInfo::Init(this, eType, szDirPath, szTypeName, bAddDateFileName, d\n\u001b[24;120HdwUpdateFileNameDelay, logLoading);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_LogInControllServerCNetwo\u001b[m\n\u001b[38;5;15m\u001b[24;120HorkEXAEAA_NHPEADZ_14000E8DB.c: \u001b[m* Function: j_?LogInControllServer@CNetworkEX@@AEAA_NHPEAD@Z\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_LogInControllServerCNetwo\u001b[m\n\u001b[38;5;15m\u001b[24;120HorkEXAEAA_NHPEADZ_14000E8DB.c:\u001b[mbool __fastcall CNetworkEX::LogInControllServer(CNetworkEX *this, int n, char *pBuf)       \u001b[25;1H\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_LogInControllServerCNetwo\u001b[m\n\u001b[38;5;15m\u001b[24;120HorkEXAEAA_NHPEADZ_14000E8DB.c:  \u001b[mreturn CNetworkEX::LogInControllServer(this, n, pBuf);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_LogInWebAgentServerCNetwo\u001b[m\n\u001b[38;5;15m\u001b[24;120HorkEXAEAA_NHPEADZ_1400026FD.c: \u001b[m* Function: j_?LogInWebAgentServer@CNetworkEX@@AEAA_NHPEAD@Z\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_LogInWebAgentServerCNetwo\u001b[m\n\u001b[38;5;15m\u001b[24;120HorkEXAEAA_NHPEADZ_1400026FD.c:\u001b[mbool __fastcall CNetworkEX::LogInWebAgentServer(CNetworkEX *this, int n, char *pBuf)       \u001b[25;1H\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_LogInWebAgentServerCNetwo\u001b[m\n\u001b[38;5;15m\u001b[24;120HorkEXAEAA_NHPEADZ_1400026FD.c:  \u001b[mreturn CNetworkEX::LogInWebAgentServer(this, n, pBuf);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_OnRecvSession_ServerCheck\u001b[m\n\u001b[38;5;15m\u001b[24;120HkSum_RequestHACKSHEILD_P_14000C342.c: \u001b[m* Function: j_?OnRecvSession_ServerCheckSum_Request@HACKSHEILD_PARAM_ANTICP@@QEAA_N\n\u001b[24;120HNH@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_OnRecvSession_ServerCheck\u001b[m\n\u001b[38;5;15m\u001b[24;120HkSum_RequestHACKSHEILD_P_14000C342.c:\u001b[mbool __fastcall HACKSHEILD_PARAM_ANTICP::OnRecvSession_ServerCheckSum_Request(HACKSH\n\u001b[24;120HHEILD_PARAM_ANTICP *this, int nIndex)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_OnRecvSession_ServerCheck\u001b[m\n\u001b[38;5;15m\u001b[24;120HkSum_RequestHACKSHEILD_P_14000C342.c:  \u001b[mreturn HACKSHEILD_PARAM_ANTICP::OnRecvSession_ServerCheckSum_Request(this, nIndex)\n\u001b[24;120H);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\LogInControllServerCNetwork\u001b[m\n\u001b[38;5;15m\u001b[24;120HkEXAEAA_NHPEADZ_1401C7250.c: \u001b[m* Function: ?LogInControllServer@CNetworkEX@@AEAA_NHPEAD@Z\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\LogInControllServerCNetwork\u001b[m\n\u001b[38;5;15m\u001b[24;120HkEXAEAA_NHPEADZ_1401C7250.c:\u001b[mchar __fastcall CNetworkEX::LogInControllServer(CNetworkEX *this, int n, char *pBuf)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\LogInWebAgentServerCNetwork\u001b[m\n\u001b[38;5;15m\u001b[24;120HkEXAEAA_NHPEADZ_1401DA860.c: \u001b[m* Function: ?LogInWebAgentServer@CNetworkEX@@AEAA_NHPEAD@Z\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\LogInWebAgentServerCNetwork\u001b[m\n\u001b[38;5;15m\u001b[24;120HkEXAEAA_NHPEADZ_1401DA860.c:\u001b[mchar __fastcall CNetworkEX::LogInWebAgentServer(CNetworkEX *this, int n, char *pBuf)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\OnRecvSessionHACKSHEILD_PAR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRAM_ANTICPUEAA_NPEAVCHac_140417F10.c:    \u001b[mresult = HACKSHEILD_PARAM_ANTICP::OnRecvSession_ServerCheckSum_Request(v11, nInd\n\u001b[24;120Hdex);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\OnRecvSession_ServerCheckSu\u001b[m\n\u001b[38;5;15m\u001b[24;120Hum_RequestHACKSHEILD_PAR_140417FB0.c: \u001b[m* Function: ?OnRecvSession_ServerCheckSum_Request@HACKSHEILD_PARAM_ANTICP@@QEAA_NH@\n\u001b[24;120H@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\OnRecvSession_ServerCheckSu\u001b[m\n\u001b[38;5;15m\u001b[24;120Hum_RequestHACKSHEILD_PAR_140417FB0.c:\u001b[mchar __fastcall HACKSHEILD_PARAM_ANTICP::OnRecvSession_ServerCheckSum_Request(HACKSH\n\u001b[24;120HHEILD_PARAM_ANTICP *this, int nIndex)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\UpdateLogInCompleteCUnmanne\u001b[m\n\u001b[38;5;15m\u001b[24;120HedTraderControllerQEAAEP_14034E440.c:        \u001b[mif ( !CRFWorldDatabase::Update_UnmannedTraderItemState(\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\UpdateLogInCompleteCUnmanne\u001b[m\n\u001b[38;5;15m\u001b[24;120HedTraderControllerQEAAEP_14034E440.c:        \u001b[mif ( !CRFWorldDatabase::Update_UnmannedTraderResutlInfo(\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\_CEnglandBillingMgrCallFunc\u001b[m\n\u001b[38;5;15m\u001b[24;120Hc_RFOnline_Auth__1_dtor0_140319C50.c: \u001b[m* Function: _CEnglandBillingMgr::CallFunc_RFOnline_Auth_::_1_::dtor$0\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\_CEnglandBillingMgrCallFunc\u001b[m\n\u001b[38;5;15m\u001b[24;120Hc_RFOnline_Auth__1_dtor0_140319C50.c:\u001b[mvoid __fastcall CEnglandBillingMgr::CallFunc_RFOnline_Auth_::_1_::dtor_0(__int64 a1,\n\u001b[24;120H, __int64 a2)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\0CRFCashItemDatabaseQEAAXZ_1402F2AE\u001b[m\n\u001b[38;5;15m\u001b[24;120HE0.c: \u001b[m* Function: ??0CRFCashItemDatabase@@QEAA@XZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\0CRFCashItemDatabaseQEAAXZ_1402F2AE\u001b[m\n\u001b[38;5;15m\u001b[24;120HE0.c:\u001b[mvoid __fastcall CRFCashItemDatabase::CRFCashItemDatabase(CRFCashItemDatabase *this)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\0CRFCashItemDatabaseQEAAXZ_1402F2AE\u001b[m\n\u001b[38;5;15m\u001b[24;120HE0.c:  \u001b[mCRFCashItemDatabase *v4; // [sp+30h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\0CRFCashItemDatabaseQEAAXZ_1402F2AE\u001b[m\n\u001b[38;5;15m\u001b[24;120HE0.c:  \u001b[mCRFNewDatabase::CRFNewDatabase((CRFNewDatabase *)&amp;v4-&gt;vfptr);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\0CRFCashItemDatabaseQEAAXZ_1402F2AE\u001b[m\n\u001b[38;5;15m\u001b[24;120HE0.c:  \u001b[mv4-&gt;vfptr = (CRFNewDatabaseVtbl *)&amp;CRFCashItemDatabase::`vftable';\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\1CRFCashItemDatabaseUEAAXZ_1402F2BB\u001b[m\n\u001b[38;5;15m\u001b[24;120HB0.c: \u001b[m* Function: ??1CRFCashItemDatabase@@UEAA@XZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\1CRFCashItemDatabaseUEAAXZ_1402F2BB\u001b[m\n\u001b[38;5;15m\u001b[24;120HB0.c:\u001b[mvoid __fastcall CRFCashItemDatabase::~CRFCashItemDatabase(CRFCashItemDatabase *this)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\1CRFCashItemDatabaseUEAAXZ_1402F2BB\u001b[m\n\u001b[38;5;15m\u001b[24;120HB0.c:  \u001b[mCRFCashItemDatabase *v4; // [sp+30h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\1CRFCashItemDatabaseUEAAXZ_1402F2BB\u001b[m\n\u001b[38;5;15m\u001b[24;120HB0.c:  \u001b[mv4-&gt;vfptr = (CRFNewDatabaseVtbl *)&amp;CRFCashItemDatabase::`vftable';\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\1CRFCashItemDatabaseUEAAXZ_1402F2BB\u001b[m\n\u001b[38;5;15m\u001b[24;120HB0.c:  \u001b[mCRFNewDatabase::~CRFNewDatabase((CRFNewDatabase *)&amp;v4-&gt;vfptr);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\AddDefaultDBRecordCNormalGuildBattl\u001b[m\n\u001b[38;5;15m\u001b[24;120HleManagerGUILD_B_1403D4FD0.c:  \u001b[mif ( CRFWorldDatabase::InsertGuildBattleDefaultRecord(pkDB, 46 * v6-&gt;m_uiMapCnt) )        \u001b[25;1H\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\AddDefaultDBTableCGuildBattleSchedu\u001b[m\n\u001b[38;5;15m\u001b[24;120HuleManagerGUILD__1403DD320.c:  \u001b[mif ( CRFWorldDatabase::InsertGuildBattleScheduleDefaultRecord(pkDB, 2u, v7-&gt;m_uiMapCnt, 23\n\u001b[24;120H3, 1) )\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\AddScheduleCGuildBattleControllerQE\u001b[m\n\u001b[38;5;15m\u001b[24;120HEAAEPEADZ_1403D6AD0.c:  \u001b[mCRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&amp;pkDB-&gt;vfptr, 0);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\AddScheduleCGuildBattleControllerQE\u001b[m\n\u001b[38;5;15m\u001b[24;120HEAAEPEADZ_1403D6AD0.c:  \u001b[mif ( CRFWorldDatabase::UpdateGuildBattleInfo(\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\AddScheduleCGuildBattleControllerQE\u001b[m\n... additional lines truncated ...\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_CreateSurfaceFromBitmapCDisplayQEAAJPEAPEAVCSurf_1400108B6.c:\u001b[mHRESULT __fastcall CDisplay::CreateSurfaceFromBitmap(CDisplay *this, CS\n\u001b[10;218HSurface **ppSurface, char *strBMP, unsigned int dwDesiredWidth, unsigned int dwDesiredHeight)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_CreateSurfaceFromBitmapCDisplayQEAAJPEAPEAVCSurf_1400108B6.c:  \u001b[mreturn CDisplay::CreateSurfaceFromBitmap(this, ppSurface, strBMP, dwD\n\u001b[10;218HDesiredWidth, dwDesiredHeight);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DestoryCRFMonsterAIMgrSAXXZ_14000CA8B.c: \u001b[m* Function: j_?Destory@CRFMonsterAIMgr@@SAXXZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DestoryCRFMonsterAIMgrSAXXZ_14000CA8B.c:\u001b[mvoid CRFMonsterAIMgr::Destory(void)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DestoryCRFMonsterAIMgrSAXXZ_14000CA8B.c:  \u001b[mCRFMonsterAIMgr::Destory();\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DrawBitmapCSurfaceQEAAJPEADKKZ_14000327E.c: \u001b[m* Function: j_?DrawBitmap@CSurface@@QEAAJPEADKK@Z\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DrawBitmapCSurfaceQEAAJPEADKKZ_14000327E.c:\u001b[mHRESULT __fastcall CSurface::DrawBitmap(CSurface *this, char *strBMP, unsigned int dwDesi\n\u001b[10;218HiredWidth, unsigned int dwDesiredHeight)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DrawBitmapCSurfaceQEAAJPEADKKZ_14000327E.c:  \u001b[mreturn CSurface::DrawBitmap(this, strBMP, dwDesiredWidth, dwDesiredHeight);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DrawBitmapCSurfaceQEAAJPEAUHBITMAP__KKKKZ_140002F0E.c: \u001b[m* Function: j_?DrawBitmap@CSurface@@QEAAJPEAUHBITMAP__@@KKKK@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DrawBitmapCSurfaceQEAAJPEAUHBITMAP__KKKKZ_140002F0E.c:\u001b[mHRESULT __fastcall CSurface::DrawBitmap(CSurface *this, HBITMAP__ *hBMP, unsig\n\u001b[10;218Hgned int dwBMPOriginX, unsigned int dwBMPOriginY, unsigned int dwBMPWidth, unsigned int dwBMPHeight)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DrawBitmapCSurfaceQEAAJPEAUHBITMAP__KKKKZ_140002F0E.c:  \u001b[mreturn CSurface::DrawBitmap(this, hBMP, dwBMPOriginX, dwBMPOriginY, dwBMPWid\n\u001b[10;218Hdth, dwBMPHeight);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_GetMonsterForcePowerRateMonsterSetInfoDataQEAAMX_14000DFF8.c: \u001b[m* Function: j_?GetMonsterForcePowerRate@MonsterSetInfoData@@QEAAMXZ\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_GetMonsterForcePowerRateMonsterSetInfoDataQEAAMX_14000DFF8.c:\u001b[mvoid __fastcall MonsterSetInfoData::GetMonsterForcePowerRate(MonsterSet\n\u001b[10;218HtInfoData *this)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_GetMonsterForcePowerRateMonsterSetInfoDataQEAAMX_14000DFF8.c:  \u001b[mMonsterSetInfoData::GetMonsterForcePowerRate(this);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_GetStateTBLCRFMonsterAIMgrQEAAAVUsPointVUsStateT_140010118.c: \u001b[m* Function: j_?GetStateTBL@CRFMonsterAIMgr@@QEAA?AV?$UsPoint@VUsStateT\n\u001b[10;218HTBL@@@@H@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_GetStateTBLCRFMonsterAIMgrQEAAAVUsPointVUsStateT_140010118.c:\u001b[mUsPoint&lt;UsStateTBL&gt; *__fastcall CRFMonsterAIMgr::GetStateTBL(CRFMonster\n\u001b[10;218HrAIMgr *this, UsPoint&lt;UsStateTBL&gt; *result, int nIndex)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_GetStateTBLCRFMonsterAIMgrQEAAAVUsPointVUsStateT_140010118.c:  \u001b[mreturn CRFMonsterAIMgr::GetStateTBL(this, result, nIndex);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_InstanceCRFMonsterAIMgrSAPEAV1XZ_1400121CA.c: \u001b[m* Function: j_?Instance@CRFMonsterAIMgr@@SAPEAV1@XZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_InstanceCRFMonsterAIMgrSAPEAV1XZ_1400121CA.c:\u001b[mCRFMonsterAIMgr *__cdecl CRFMonsterAIMgr::Instance()\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_InstanceCRFMonsterAIMgrSAPEAV1XZ_1400121CA.c:  \u001b[mreturn CRFMonsterAIMgr::Instance();\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_LoadMonsterSetInfoDataQEAAHPEBDZ_140006AB4.c:\u001b[mint __fastcall MonsterSetInfoData::Load(MonsterSetInfoData *this, const char *strFileNa\n\u001b[10;218Hame)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_LoadMonsterSetInfoDataQEAAHPEBDZ_140006AB4.c:  \u001b[mreturn MonsterSetInfoData::Load(this, strFileName);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeon_14001007D.c: \u001b[m* Function: j_?mc_AddMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQue\n\u001b[10;218HestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeon_14001007D.c:\u001b[mbool __fastcall mc_AddMonster(strFILE *fstr, CDarkHoleDungeonQuestSetup\n\u001b[10;218Hp *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDung_14000C9F0.c: \u001b[m* Function: j_?mc_ChangeMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeon\n\u001b[10;218HnQuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDung_14000C9F0.c:\u001b[mbool __fastcall mc_ChangeMonster(strFILE *fstr, CDarkHoleDungeonQuestSe\n\u001b[10;218Hetup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkH_140004ABB.c: \u001b[m* Function: j_?mc_RespawnMonsterOption@@YA_NPEAUstrFILE@@PEAVCDarkHole\n\u001b[10;218HeDungeonQuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkH_140004ABB.c:\u001b[mbool __fastcall mc_RespawnMonsterOption(strFILE *fstr, CDarkHoleDungeon\n\u001b[10;218HnQuestSetup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDun_140002BC1.c: \u001b[m* Function: j_?mc_RespawnMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeo\n\u001b[10;218HonQuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDun_140002BC1.c:\u001b[mbool __fastcall mc_RespawnMonster(strFILE *fstr, CDarkHoleDungeonQuestS\n\u001b[10;218HSetup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_OnButtonMonsterCGameServerViewQEAAXXZ_14000A367.c: \u001b[m* Function: j_?OnButtonMonster@CGameServerView@@QEAAXXZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_OnButtonMonsterCGameServerViewQEAAXXZ_14000A367.c:\u001b[mvoid __fastcall CGameServerView::OnButtonMonster(CGameServerView *this)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_OnButtonMonsterCGameServerViewQEAAXXZ_14000A367.c:  \u001b[mCGameServerView::OnButtonMonster(this);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDunge_1400066DB.c: \u001b[m* Function: j_?qc_monsterGroup@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQ\n\u001b[10;218HQuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDunge_1400066DB.c:\u001b[mbool __fastcall qc_monsterGroup(strFILE *fstr, CDarkHoleDungeonQuestSet\n\u001b[10;218Htup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140011D5B.c: \u001b[m* Function: j_?qc_UseMap@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSe\n\u001b[10;218Hetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140011D5B.c:\u001b[mbool __fastcall qc_UseMap(strFILE *fstr, CDarkHoleDungeonQuestSetup *pS\n\u001b[10;218HSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_RequestElanMapUserForceMoveHQCMoveMapLimitManage_1400095BB.c: \u001b[m* Function: j_?RequestElanMapUserForceMoveHQ@CMoveMapLimitManager@@QEA\n\u001b[10;218HAAEXZ\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_RequestElanMapUserForceMoveHQCMoveMapLimitManage_1400095BB.c:\u001b[mchar __fastcall CMoveMapLimitManager::RequestElanMapUserForceMoveHQ(CMo\n\u001b[10;218HoveMapLimitManager *this)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_RequestElanMapUserForceMoveHQCMoveMapLimitManage_1400095BB.c:  \u001b[mreturn CMoveMapLimitManager::RequestElanMapUserForceMoveHQ(this);    \u001b[11;1H\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_size_server_notify_inform_zoneQEAAHXZ_140005187.c: \u001b[m* Function: j_?size@_server_notify_inform_zone@@QEAAHXZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_size_server_notify_inform_zoneQEAAHXZ_140005187.c:\u001b[mint __fastcall _server_notify_inform_zone::size(_server_notify_inform_zone *this) \u001b[11;1H\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_size_server_notify_inform_zoneQEAAHXZ_140005187.c:  \u001b[mreturn _server_notify_inform_zone::size(this);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j__DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCS_140001799.c: \u001b[m* Function: j_?_DrawObject@CMapDisplay@@AEAAJPEAVCGameObject@@PEAVCSur\n\u001b[10;218Hrface@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j__DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCS_140001799.c:\u001b[mHRESULT __fastcall CMapDisplay::_DrawObject(CMapDisplay *this, CGameObj\n\u001b[10;218Hject *pObj, CSurface *pSF)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j__GCRFMonsterAIMgrQEAAPEAXIZ_14000934A.c: \u001b[m* Function: j_??_GCRFMonsterAIMgr@@QEAAPEAXI@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j__GCRFMonsterAIMgrQEAAPEAXIZ_14000934A.c:\u001b[mvoid *__fastcall CRFMonsterAIMgr::`scalar deleting destructor'(CRFMonsterAIMgr *this, unsign\n\u001b[10;218Hned int a2)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j__GCRFMonsterAIMgrQEAAPEAXIZ_14000934A.c:  \u001b[mreturn CRFMonsterAIMgr::`scalar deleting destructor'(this, a2);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LightMappingTex1YAXPEAU_BSP_MAT_GROUPZ_1404EFAF0.c:  \u001b[mIUnknownVtbl *v4; // rbx@3\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LightMappingTex1YAXPEAU_BSP_MAT_GROUPZ_1404EFAF0.c:    \u001b[mv5 = GetLightMapSurface(v3);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LightMappingTex1YAXPEAU_BSP_MAT_GROUPZ_1404EFAF0.c:  \u001b[m((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64, signed _\n\u001b[10;218H__int64))v2-&gt;vfptr[21].QueryInterface)(\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadEventSetCMonsterEventSetQEAA_NPEADZ_1402A79E0.c:\u001b[m\u001b[12Cif ( !strcmp_0(v5, \&quot;UNKNOWN\&quot;) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadEventSetCMonsterEventSetQEAA_NPEADZ_1402A79E0.c:\u001b[m\u001b[14Cv44-&gt;bUnknownMap = 1;\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadEventSetCMonsterEventSetQEAA_NPEADZ_1402A79E0.c:\u001b[m\u001b[14Cv44-&gt;bUnknownMap = 0;\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadEventSetCMonsterEventSetQEAA_NPEADZ_1402A79E0.c:\u001b[m\u001b[12Cv44-&gt;pMonsterFld = v47;\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadMapsCMapOperationAEAA_NXZ_140196750.c:    \u001b[mif ( !CMapData::OpenMap(&amp;v19-&gt;m_Map[dwIndex], pMapSet-&gt;m_strFileName, pMapSet, 1) )     \u001b[11;1H\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadMapsCMapOperationAEAA_NXZ_140196750.c:      \u001b[mMyMessageBox(\&quot;Map Load Error\&quot;, \&quot;%s - Read Error\&quot;, pMapSet-&gt;m_strFileName);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadMapsCMapOperationAEAA_NXZ_140196750.c:      \u001b[mMyMessageBox(\&quot;ItemStore Load Error\&quot;, \&quot;LoadMaps() : pMapItemStoreList-&gt;CreateStores(%s)\n\u001b[10;218H)\&quot;, pMapSet-&gt;m_strFileName);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadMonsterSetInfoDataQEAAHPEBDZ_14015C7E0.c:\u001b[m__int64 __fastcall MonsterSetInfoData::Load(MonsterSetInfoData *this, const char *strFile\n\u001b[10;218HeName)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadMonsterSetInfoDataQEAAHPEBDZ_14015C7E0.c:  \u001b[mv38 = strFileName;\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadMonsterSetInfoDataQEAAHPEBDZ_14015C7E0.c:  \u001b[mv37-&gt;m_fMonsterForcePowerRate = (float)(signed int)GetPrivateProfileIntA(\&quot;Common\&quot;, \&quot;Mon\n\u001b[10;218HnsterForcePowerRate\&quot;, 40, v38);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c:  \u001b[mv25-&gt;m_bFreeServer = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;FreeServer\&quot;, 0, \&quot;..\\\\WorldInfo\\\n\u001b[10;218H\\\\WorldInfo.ini\&quot;);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c:  \u001b[mv25-&gt;m_byWorldType = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;ServerType\&quot;, 2, \&quot;..\\\\WorldInfo\\\n\u001b[10;218H\\\\WorldInfo.ini\&quot;);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c:  \u001b[mGetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ReleaseType\&quot;, \&quot;X\&quot;, &amp;ReturnedString, 9u, \&quot;..\\\\\n\u001b[10;218H\\WorldInfo\\\\WorldInfo.ini\&quot;);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c:        \u001b[m\&quot;WorldInfo.ini\\r\\n[ServerMode]\\r\\nReleaseType = %s Invalid!!\&quot;,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c:  \u001b[mGetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ExcuteService\&quot;, \&quot;X\&quot;, &amp;Str1, 6u, \&quot;..\\\\WorldInf\n\u001b[10;218Hfo\\\\WorldInfo.ini\&quot;);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c:        \u001b[m\&quot;WorldInfo.ini\\r\\n[ServerMode]\\r\\nExcuteService = %s Invalid!!\&quot;,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeonQu_140275730.c: \u001b[m* Function: ?mc_AddMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuest\n\u001b[10;218HtSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeonQu_140275730.c:\u001b[mbool __fastcall mc_AddMonster(strFILE *fstr, CDarkHoleDungeonQuestSetup\n\u001b[10;218Hp *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeonQu_140275730.c:  \u001b[mstrFILE *fstra; // [sp+E0h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:\u001b[m * Function: ?mc_ChangeMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQu\n\u001b[10;218HuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:\u001b[mbool __fastcall mc_ChangeMonster(strFILE *fstr, CDarkHoleDungeonQuestSe\n\u001b[10;218Hetup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:  \u001b[mstrFILE *fstra; // [sp+260h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:  \u001b[mif ( strFILE::word(fstra, &amp;poutszWord) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:    \u001b[mif ( strFILE::word(fstra, &amp;szRecordCode) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:      \u001b[mif ( strFILE::word(fstra, Str) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:            \u001b[mpSetupa-&gt;m_pCurLoadMission-&gt;pChangeMonster[v15]-&gt;pMonsterFl\n\u001b[10;218HldA = (_monster_fld *)v11;\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:            \u001b[mpSetupa-&gt;m_pCurLoadMission-&gt;pChangeMonster[v15]-&gt;pMonsterFl\n\u001b[10;218HldB = (_monster_fld *)v12;\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c: \u001b[m* Function: ?mc_RespawnMonsterOption@@YA_NPEAUstrFILE@@PEAVCDarkHoleDu\n\u001b[10;218HungeonQuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c:\u001b[mbool __fastcall mc_RespawnMonsterOption(strFILE *fstr, CDarkHoleDungeon\n\u001b[10;218HnQuestSetup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c:  \u001b[mstrFILE *fstra; // [sp+230h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c:  \u001b[mif ( strFILE::word(fstra, &amp;poutszWord) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c:    \u001b[mif ( strFILE::word(fstra, &amp;v8) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c:      \u001b[mif ( strFILE::word(fstra, &amp;Source) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c: \u001b[m* Function: ?mc_RespawnMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQ\n\u001b[10;218HQuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c:\u001b[mbool __fastcall mc_RespawnMonster(strFILE *fstr, CDarkHoleDungeonQuestS\n\u001b[10;218HSetup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c:  \u001b[mstrFILE *fstra; // [sp+270h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c:        \u001b[mif ( strFILE::word(fstra, &amp;poutszWord) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c:\u001b[m\u001b[12Cif ( strFILE::word(fstra, &amp;pnoutVal) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c:\u001b[m\u001b[14Cif ( strFILE::word(fstra, &amp;Str1) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c:\u001b[m\u001b[18Cif ( strFILE::word(fstra, &amp;v13) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\OnButtonMonsterCGameServerViewQEAAXXZ_14002B190.c: \u001b[m* Function: ?OnButtonMonster@CGameServerView@@QEAAXXZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\OnButtonMonsterCGameServerViewQEAAXXZ_14002B190.c:\u001b[mvoid __fastcall CGameServerView::OnButtonMonster(CGameServerView *this)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\OnDisplayCMapDisplayQEAA_NPEAVCMapDataGZ_14019EEB0.c:    \u001b[mv7 = CMapDisplay::InitSurface(v8, pMap);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.c:      \u001b[m\&quot;!!Server type is wrong!!(AC:%d)(ZO:%d)\&quot;,\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.c:    \u001b[mWriteServerStartHistory(\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.c:      \u001b[m\&quot;ServerType is Wrong ==&gt; AccountServer(%d) != ZoneServer(%d)\&quot;,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c:  \u001b[mWriteServerStartHistory(\&quot;DBInit Begin &gt;&gt; name: %s, ip: %s\&quot;, pszDBNamea,\n\u001b[10;218H, pszDBIPa);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c:    \u001b[mWriteServerStartHistory(\&quot;DBInit Complete &gt;&gt;\&quot;);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c:    \u001b[mWriteServerStartHistory(\&quot;DBInit Fail &gt;&gt;\&quot;);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.c: \u001b[m* Function: ?qc_monsterGroup@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQue\n\u001b[10;218HestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.c:\u001b[mbool __fastcall qc_monsterGroup(strFILE *fstr, CDarkHoleDungeonQuestSet\n\u001b[10;218Htup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.c:  \u001b[mstrFILE *fstra; // [sp+1C0h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.c:  \u001b[mif ( strFILE::word(fstra, &amp;poutszWord) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.c:    \u001b[mif ( strFILE::word(fstra, &amp;szRecordCode) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.c: \u001b[m* Function: ?qc_UseMap@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetu\n\u001b[10;218Hup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.c:\u001b[mbool __fastcall qc_UseMap(strFILE *fstr, CDarkHoleDungeonQuestSetup *pS\n\u001b[10;218HSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.c:  \u001b[mstrFILE *fstra; // [sp+E0h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.c:  \u001b[mif ( strFILE::word(fstra, &amp;poutszWord) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v7; // [sp+28h] [bp-80h]@5\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v8; // [sp+30h] [bp-78h]@5\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v9; // [sp+38h] [bp-70h]@10\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v10; // [sp+40h] [bp-68h]@10\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v11; // [sp+48h] [bp-60h]@19\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v12; // [sp+50h] [bp-58h]@19\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v13; // [sp+58h] [bp-50h]@26\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v14; // [sp+60h] [bp-48h]@26\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v15; // [sp+68h] [bp-40h]@31\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v16; // [sp+70h] [bp-38h]@31\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:      \u001b[mv17 = CSurface::`scalar deleting destructor'(v7, 1u);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:      \u001b[mv18 = CSurface::`scalar deleting destructor'(v9, 1u);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:\u001b[m\u001b[10Cv19 = CSurface::`scalar deleting destructor'(v11, 1u);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:      \u001b[mv20 = CSurface::`scalar deleting destructor'(v13, 1u);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:      \u001b[mv21 = CSurface::`scalar deleting destructor'(v15, 1u);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\RequestElanMapUserForceMoveHQCMoveMapLimitManagerQ_140284700.c: \u001b[m* Function: ?RequestElanMapUserForceMoveHQ@CMoveMapLimitManager@@QEAAE\n\u001b[10;218HEXZ\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\RequestElanMapUserForceMoveHQCMoveMapLimitManagerQ_140284700.c:\u001b[mchar __fastcall CMoveMapLimitManager::RequestElanMapUserForceMoveHQ(CMo\n\u001b[10;218HoveMapLimitManager *this)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\SetLightMapYAXJZ_1404EDF30.c:  \u001b[mIUnknownVtbl *v4; // rbx@3\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\SetLightMapYAXJZ_1404EDF30.c:    \u001b[mv5 = GetLightMapSurface(v1);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:  \u001b[mCSurface v7; // [sp+40h] [bp-C8h]@9\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:      \u001b[m((void (__fastcall *)(IDirectDrawSurface7 *, IDirectDrawPalette *\n\u001b[10;218H*))v11-&gt;m_pddsFrontBuffer-&gt;vfptr[10].AddRef)(\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:    \u001b[mCSurface::CSurface(&amp;v7);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:    \u001b[mCSurface::Create(&amp;v7, v11-&gt;m_pddsBackBuffer);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:    \u001b[mif ( CSurface::DrawBitmap(&amp;v7, hBMP, 0, 0, 0, 0) &gt;= 0 )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:      \u001b[mCSurface::~CSurface(&amp;v7);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:      \u001b[mCSurface::~CSurface(&amp;v7);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\size_server_notify_inform_zoneQEAAHXZ_14011F1E0.c: \u001b[m* Function: ?size@_server_notify_inform_zone@@QEAAHXZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\size_server_notify_inform_zoneQEAAHXZ_14011F1E0.c:\u001b[msigned __int64 __fastcall _server_notify_inform_zone::size(_server_notify_inform_zon\n\u001b[10;218Hne *this)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\StartRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6B90.c:      \u001b[msprintf(Dest, \&quot;now actived\&quot;);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\StartRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6B90.c:        \u001b[mv6 = v18-&gt;pMonsterFld-&gt;m_strCode;\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\StartRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6B90.c:          \u001b[mpEventRespawn-&gt;State.MonInfo[v16++].pMonFld = v18-&gt;pMonsterFld\n\u001b[10;218Hd;\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\StopEventSetCMonsterEventSetQEAA_NPEAD0Z_1402A8870.c:\u001b[m\u001b[10Csprintf(Dest, \&quot;now stoped\&quot;);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\StopRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6E50.c:      \u001b[msprintf(Dest, \&quot;now stoped\&quot;);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\SubProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAEHP_1403A4D10.c:          \u001b[mif ( CGameObject::GetCurSecNum(v12) == -1 || v12-&gt;m_bMapLoadi\n\u001b[10;218Hing )\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\UnLightMappingTex1YAXXZ_1404EFB90.c:  \u001b[m((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64))v0-&gt;vfptr[21].QueryInte\n\u001b[10;218Herface)(\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_CDisplayCreateSurfaceFromBitmap__1_dtor0_140433DE0.c: \u001b[m* Function: _CDisplay::CreateSurfaceFromBitmap_::_1_::dtor$0\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_CDisplayCreateSurfaceFromBitmap__1_dtor0_140433DE0.c:\u001b[mvoid __fastcall CDisplay::CreateSurfaceFromBitmap_::_1_::dtor_0(__int64 a1, __in\n\u001b[10;218Hnt64 a2)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_CDisplayShowBitmap__1_dtor0_140434370.c:  \u001b[mCSurface::~CSurface((CSurface *)(a2 + 64));\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_CRFMonsterAIMgrInstance__1_dtor0_14014C190.c: \u001b[m* Function: _CRFMonsterAIMgr::Instance_::_1_::dtor$0\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_CRFMonsterAIMgrInstance__1_dtor0_14014C190.c:\u001b[mvoid __fastcall CRFMonsterAIMgr::Instance_::_1_::dtor_0(__int64 a1, __int64 a2)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c: \u001b[m* Function: ?_DrawObject@CMapDisplay@@AEAAJPEAVCGameObject@@PEAVCSurfa\n\u001b[10;218Hace@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:\u001b[m__int64 __fastcall CMapDisplay::_DrawObject(CMapDisplay *this, CGameObj\n\u001b[10;218Hject *pObj, CSurface *pSF)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mIDirectDrawSurface7 *v6; // rax@14\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mIDirectDrawSurface7 *v7; // rax@17\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mIDirectDrawSurface7 *v12; // [sp+38h] [bp-30h]@14\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mIUnknownVtbl *v13; // [sp+40h] [bp-28h]@14\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mIDirectDrawSurface7 *v14; // [sp+48h] [bp-20h]@17\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mIUnknownVtbl *v15; // [sp+50h] [bp-18h]@17\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mCSurface *v18; // [sp+80h] [bp+18h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:\u001b[m\u001b[10Cv12 = CSurface::GetDDrawSurface(v16-&gt;m_pSFMap);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:\u001b[m\u001b[10Cv6 = CSurface::GetDDrawSurface(v18);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:          \u001b[mv11 = ((int (__fastcall *)(IDirectDrawSurface7 *, _QWORD, _QW\n\u001b[10;218HWORD, IDirectDrawSurface7 *))v13[2].AddRef)(\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:        \u001b[mv14 = CSurface::GetDDrawSurface(v16-&gt;m_pSFMap);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:        \u001b[mv7 = CSurface::GetDDrawSurface(v18);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:        \u001b[mv11 = ((int (__fastcall *)(IDirectDrawSurface7 *, _QWORD, _QWOR\n\u001b[10;218HRD, IDirectDrawSurface7 *))v15[2].AddRef)(\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_GCRFMonsterAIMgrQEAAPEAXIZ_140203390.c: \u001b[m* Function: ??_GCRFMonsterAIMgr@@QEAAPEAXI@Z\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_GCRFMonsterAIMgrQEAAPEAXIZ_140203390.c:\u001b[mCRFMonsterAIMgr *__fastcall CRFMonsterAIMgr::`scalar deleting destructor'(CRFMonsterAIMgr *thi\n\u001b[10;218His, int a2)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_GCRFMonsterAIMgrQEAAPEAXIZ_140203390.c:  \u001b[mCRFMonsterAIMgr *v6; // [sp+30h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_GCRFMonsterAIMgrQEAAPEAXIZ_140203390.c:  \u001b[mCRFMonsterAIMgr::~CRFMonsterAIMgr(v6);\u001b[K\n\n        &lt;/stdout&gt;\n        &lt;stderr&gt;\n        \n        &lt;/stderr&gt;&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01L6FFggsbWdpMaTJDbKBh92&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;,&quot;folderRoot&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;}],&quot;workspaceFoldersUnchanged&quot;:false,&quot;currentTerminal&quot;:{&quot;terminalId&quot;:2,&quot;currentWorkingDirectory&quot;:&quot;D:/_2.NexusPro_Project2025_RFOnlineGameguard&quot;}}}],&quot;timestamp&quot;:&quot;2025-07-15T08:48:57.212Z&quot;,&quot;response_text&quot;:&quot;Based on my search through the codebase, I couldn't find the exact string \&quot;RF SERVER NOW LOADING\&quot; in the source code. However, I did find several related components that might display this message when the zoneserver starts up.\n\nThe main components involved in server startup are:\n\n1. The `CMainThread::pc_OpenWorldSuccessResult` function in `Decompiled Source Code - IDA Pro/world/pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c` which handles the successful opening of the world server.\n\n2. The server startup logging functions like `WriteServerStartHistory` which log messages during server initialization.\n\n3. The `LoadWorldInfoINI` function that loads configuration from the WorldInfo.ini file.\n\nThe message \&quot;RF SERVER NOW LOADING\&quot; is likely displayed in one of these places:\n\n1. It might be displayed in a UI component that isn't directly visible in the decompiled code\n2. It could be part of a resource string that's loaded at runtime\n3. It might be in a log file that's written during server startup\n\nSince I couldn't find the exact string in the code, the message might be:\n\n1. Generated by a UI framework component when the server starts\n2. Displayed by the game client when connecting to the server, rather than by the server itself\n3. Part of a splash screen or loading screen resource\n\nIf you need to modify this message, you would likely need to look at:\n1. The server startup UI code\n2. Resource files or string tables\n3. Log message templates\n\nWould you like me to search for any specific related components or functions that might help locate where this message is displayed?\n&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-ad182aa6-0f9c-4aff-838e-1dd04a435aeb&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-88604be4-aa58-4640-a068-b082b70d39dc&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-5d0a652d-8cc6-4e54-8297-2c9d1aa5d581&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-28668874-c015-48b1-8b57-eef0380bd914&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-c8f393c3-c172-4fbe-99f5-827000ac7602&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-bda6fc7e-3200-4e84-9bbe-f69b7b93e8d4&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-3a3e9b88-8a0c-44fe-a41b-9e6c50552ed4&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-3a5bc075-f4d3-4a84-8828-ce8fbdb1b5a8&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;70babbb1-ed75-452e-a00c-33fce13a0924;toolu_vrtx_01L7X6Lj5e68C3UvqR6hJVsP&quot;:{&quot;requestId&quot;:&quot;70babbb1-ed75-452e-a00c-33fce13a0924&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01L7X6Lj5e68C3UvqR6hJVsP&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./Decompiled Header - IDA Pro\n./Decompiled Header - IDA Pro\\ZoneServerUD_x64.h\n./Decompiled Source Code - IDA Pro\n./Decompiled Source Code - IDA Pro\\authentication\n./Decompiled Source Code - IDA Pro\\combat\n./Decompiled Source Code - IDA Pro\\database\n./Decompiled Source Code - IDA Pro\\economy\n./Decompiled Source Code - IDA Pro\\items\n./Decompiled Source Code - IDA Pro\\network\n./Decompiled Source Code - IDA Pro\\player\n./Decompiled Source Code - IDA Pro\\security\n./Decompiled Source Code - IDA Pro\\system\n./Decompiled Source Code - IDA Pro\\world\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;4ab6d1e6-4297-4198-b622-808a464ac518;toolu_01K3fAwp5HEbK5kLBHVBazG7&quot;:{&quot;requestId&quot;:&quot;4ab6d1e6-4297-4198-b622-808a464ac518&quot;,&quot;toolUseId&quot;:&quot;toolu_01K3fAwp5HEbK5kLBHVBazG7&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: Decompiled Source Code - IDA Pro/world/j_size_server_notify_inform_zoneQEAAHXZ_140005187.c\n     1\t/*\n     2\t * Function: j_?size@_server_notify_inform_zone@@QEAAHXZ\n     3\t * Address: 0x140005187\n     4\t */\n     5\t\n     6\tint __fastcall _server_notify_inform_zone::size(_server_notify_inform_zone *this)\n     7\t{\n     8\t  return _server_notify_inform_zone::size(this);\n     9\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/size_server_notify_inform_zoneQEAAHXZ_14011F1E0.c\n     1\t/*\n     2\t * Function: ?size@_server_notify_inform_zone@@QEAAHXZ\n     3\t * Address: 0x14011F1E0\n     4\t */\n     5\t\n     6\tsigned __int64 __fastcall _server_notify_inform_zone::size(_server_notify_inform_zone *this)\n     7\t{\n     8\t  return 6i64;\n     9\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/LoadAllBossSchedule_MapQEAA_NXZ_14041A070.c\n     1\t/*\n     2\t * Function: ?LoadAll@BossSchedule_Map@@QEAA_NXZ\n     3\t * Address: 0x14041A070\n     4\t */\n     5\t\n     6\tchar __fastcall BossSchedule_Map::LoadAll(BossSchedule_Map *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v4; // [sp+0h] [bp-48h]@1\n    12\t  unsigned int dwIndex; // [sp+20h] [bp-28h]@9\n    13\t  INI_Section *pSection; // [sp+28h] [bp-20h]@11\n    14\t  BossSchedule **v7; // [sp+30h] [bp-18h]@9\n    15\t  unsigned __int64 v8; // [sp+38h] [bp-10h]@9\n    16\t  BossSchedule_Map *pMapSchedule; // [sp+50h] [bp+8h]@1\n    17\t\n    18\t  pMapSchedule = this;\n    19\t  v1 = &amp;v4;\n    20\t  for ( i = 16i64; i; --i )\n    21\t  {\n    22\t    *(_DWORD *)v1 = -*********;\n    23\t    v1 = (__int64 *)((char *)v1 + 4);\n    24\t  }\n...\nPath: Decompiled Source Code - IDA Pro/combat/load_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F5E30.c\n...\n    20\t  CLogFile::Write(&amp;v4-&gt;m_cde.m_cde_log, \&quot;Loading Cash Discount-Rate Event\&quot;);\n    21\t  CNetTimer::BeginTimer(&amp;v4-&gt;m_cde.m_cde_timer, 0x3E8u);\n    22\t  v4-&gt;m_cde.m_cde_inform_before[0] = 1800;\n    23\t  v4-&gt;m_cde.m_cde_inform_before[1] = 300;\n    24\t  v4-&gt;m_cde.m_cde_status = 0;\n    25\t  CashItemRemoteStore::load_cde_ini(v4, &amp;v4-&gt;m_cde.m_ini, &amp;v4-&gt;m_cde.m_cde_ini_file_time);\n    26\t  CashItemRemoteStore::log_about_cash_event(v4, \&quot;Loaded From Ini File &lt;When Server Started&gt;\&quot;, &amp;v4-&gt;m_cde.m_ini);\n    27\t  CashItemRemoteStore::check_loaded_cde_status(v4);\n    28\t  CLogFile::Write(&amp;v4-&gt;m_cde.m_cde_log, \&quot;Complete For Cash Discount-Rate Event\&quot;);\n    29\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/LoadCUnmannedTraderSchedulerQEAA_NXZ_1403935E0.c\n...\n     5\t\n     6\tchar __fastcall CUnmannedTraderScheduler::Load(CUnmannedTraderScheduler *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v4; // [sp+0h] [bp-148h]@1\n    12\t  char Dst; // [sp+30h] [bp-118h]@4\n    13\t  char v6; // [sp+134h] [bp-14h]@4\n    14\t  CUnmannedTraderScheduler *v7; // [sp+150h] [bp+8h]@1\n    15\t\n    16\t  v7 = this;\n    17\t  v1 = &amp;v4;\n    18\t  for ( i = 80i64; i; --i )\n    19\t  {\n    20\t    *(_DWORD *)v1 = -*********;\n    21\t    v1 = (__int64 *)((char *)v1 + 4);\n    22\t  }\n    23\t  memset_0(&amp;Dst, 0, 0xF8ui64);\n    24\t  v6 = CRFWorldDatabase::Select_UnmannedTraderReservedSchedule(\n    25\t         pkDB,\n    26\t         CUnmannedTraderEnvironmentValue::Unmanned_Trader_Max_Schdule_Cnt,\n    27\t         (_unmannedtrader_reserved_schedule_info *)&amp;Dst);\n    28\t  if ( v6 == 2 )\n    29\t  {\n    30\t    v7-&gt;m_bLoad = 1;\n    31\t    result = 1;\n    32\t  }\n    33\t  else if ( v6 )\n    34\t  {\n    35\t    result = 0;\n    36\t  }\n...\nPath: Decompiled Source Code - IDA Pro/world/ZoneAliveCheckRequestCNetworkEXAEAA_NHPEADZ_1401C3D90.c\n     1\t/*\n     2\t * Function: ?ZoneAliveCheckRequest@CNetworkEX@@AEAA_NHPEAD@Z\n     3\t * Address: 0x1401C3D90\n     4\t */\n     5\t\n     6\tchar __fastcall CNetworkEX::ZoneAliveCheckRequest(CNetworkEX *this, int n, char *pBuf)\n     7\t{\n     8\t  __int64 *v3; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  CBillingManager *v5; // rax@4\n    11\t  __int64 v7; // [sp+0h] [bp-38h]@1\n    12\t  unsigned int *v8; // [sp+20h] [bp-18h]@4\n    13\t\n    14\t  v3 = &amp;v7;\n    15\t  for ( i = 12i64; i; --i )\n    16\t  {\n    17\t    *(_DWORD *)v3 = -*********;\n    18\t    v3 = (__int64 *)((char *)v3 + 4);\n    19\t  }\n    20\t  v8 = (unsigned int *)pBuf;\n    21\t  v5 = CTSingleton&lt;CBillingManager&gt;::Instance();\n    22\t  CBillingManager::SendMsg_ZoneAliveCheck(v5, *v8);\n    23\t  return 1;\n    24\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.c\n...\n     5\t\n     6\tchar __fastcall CNetworkEX::OpenWorldSuccessResult(CNetworkEX *this, unsigned int n, char *pMsg)\n     7\t{\n     8\t  __int64 *v3; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v6; // [sp+0h] [bp-38h]@1\n    12\t  char *v7; // [sp+20h] [bp-18h]@4\n    13\t\n    14\t  v3 = &amp;v6;\n    15\t  for ( i = 12i64; i; --i )\n    16\t  {\n    17\t    *(_DWORD *)v3 = -*********;\n    18\t    v3 = (__int64 *)((char *)v3 + 4);\n    19\t  }\n    20\t  v7 = pMsg;\n    21\t  if ( unk_1799C9AE9 == (unsigned __int8)pMsg[50] )\n    22\t  {\n    23\t    CMainThread::pc_OpenWorldSuccessResult(&amp;g_Main, *v7, v7 + 1, v7 + 33);\n    24\t    result = 1;\n    25\t  }\n    26\t  else\n    27\t  {\n    28\t    MyMessageBox(\n    29\t      \&quot;OpenWorld(Account -&gt; Zone)\&quot;,\n    30\t      \&quot;!!Server type is wrong!!(AC:%d)(ZO:%d)\&quot;,\n    31\t      (unsigned __int8)v7[50],\n    32\t      unk_1799C9AE9);\n    33\t    WriteServerStartHistory(\n    34\t      \&quot;ServerType is Wrong ==&gt; AccountServer(%d) != ZoneServer(%d)\&quot;,\n    35\t      (unsigned __int8)v7[50],\n    36\t      unk_1799C9AE9);\n    37\t    result = 1;\n    38\t  }\n...\nPath: Decompiled Source Code - IDA Pro/world/LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c\n...\n    41\t  v22 = -2i64;\n    42\t  v24 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    43\t  GetPrivateProfileStringA(\&quot;System\&quot;, \&quot;WorldName\&quot;, \&quot;X\&quot;, v25-&gt;m_szWorldName, 0x21u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    44\t  if ( !strcmp_0(v25-&gt;m_szWorldName, \&quot;X\&quot;) )\n    45\t    return 0xFFFFFFFFi64;\n    46\t  M2W(v25-&gt;m_szWorldName, v25-&gt;m_wszWorldName, 0x21u);\n    47\t  v25-&gt;m_bFreeServer = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;FreeServer\&quot;, 0, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    48\t  v25-&gt;m_byWorldType = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;ServerType\&quot;, 2, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    49\t  ReturnedString = 0;\n    50\t  memset(&amp;v10, 0, 8ui64);\n    51\t  GetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ReleaseType\&quot;, \&quot;X\&quot;, &amp;ReturnedString, 9u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    52\t  if ( !_stricmp(&amp;ReturnedString, \&quot;Internal\&quot;) )\n    53\t  {\n    54\t    v25-&gt;m_bReleaseServiceMode = 0;\n    55\t  }\n...\nPath: Decompiled Source Code - IDA Pro/player/SendMsg_PartyJoinJoinerResultCPlayerQEAAXXZ_1400DCE50.c\n...\n    27\t  v10 = (unsigned __int64)&amp;v4 ^ _security_cookie;\n    28\t  _party_join_joiner_result_zocl::_party_join_joiner_result_zocl(&amp;v5);\n    29\t  v6 = CPartyPlayer::GetPtrPartyMember(v11-&gt;m_pPartyMgr);\n    30\t  if ( v6 )\n    31\t  {\n    32\t    v5.byLootShareMode = v11-&gt;m_pPartyMgr-&gt;m_pPartyBoss-&gt;m_byLootShareSystem;\n    33\t    v5.byListNum = CPartyPlayer::GetPopPartyMember(v11-&gt;m_pPartyMgr) - 1;\n    34\t    for ( j = 0; j &lt; (unsigned __int8)v5.byListNum &amp;&amp; v6[j]; ++j )\n    35\t    {\n    36\t      if ( v6[j] != v11-&gt;m_pPartyMgr )\n    37\t      {\n    38\t        v5.List[j].wIndex = v6[j]-&gt;m_id.wIndex;\n    39\t        v5.List[j].dwSerial = v6[j]-&gt;m_id.dwSerial;\n    40\t        strcpy_0(v5.List[j].wszAvatorName, v6[j]-&gt;m_wszName);\n    41\t      }\n    42\t    }\n    43\t    pbyType = 16;\n    44\t    v9 = 7;\n    45\t    v3 = _party_join_joiner_result_zocl::size(&amp;v5);\n    46\t    CNetProcess::LoadSendMsg(unk_1414F2088, v11-&gt;m_ObjID.m_wIndex, &amp;pbyType, &amp;v5.byLootShareMode, v3);\n    47\t  }\n    48\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/Load_Cash_EventCashItemRemoteStoreQEAAXXZ_1402F7F10.c\n...\n    23\t  v6 = (unsigned __int64)&amp;v3 ^ _security_cookie;\n    24\t  for ( j = 0; j &lt; 3; ++j )\n    25\t  {\n    26\t    CNetTimer::BeginTimer(&amp;v7-&gt;m_cash_event[j].m_event_timer, 0x3E8u);\n    27\t    v7-&gt;m_cash_event[j].m_event_inform_before[0] = 1800;\n    28\t    v7-&gt;m_cash_event[j].m_event_inform_before[1] = 300;\n    29\t    CLogFile::Write(&amp;v7-&gt;m_cash_event[j].m_event_log, \&quot;Server Start and LogFile Loaing\&quot;);\n    30\t    CashItemRemoteStore::Set_CashEvent_Status(v7, j, 0);\n    31\t    Get_CashEvent_Name(j, &amp;szEventName);\n    32\t    CashItemRemoteStore::Load_Event_INI(\n    33\t      v7,\n    34\t      &amp;v7-&gt;m_cash_event[j].m_ini,\n    35\t      &amp;v7-&gt;m_cash_event[j].m_event_ini_file_time,\n    36\t      &amp;szEventName);\n    37\t    if ( j == 2 )\n    38\t      CashItemRemoteStore::Load_LimitedSale_Event_INI(\n    39\t        v7,\n    40\t        &amp;v7-&gt;m_cash_event[2].m_ini,\n    41\t        &amp;v7-&gt;m_cash_event[2].m_event_ini_file_time,\n    42\t        &amp;szEventName);\n    43\t    CashItemRemoteStore::Check_Loaded_Event_Status(v7, j);\n    44\t  }\n    45\t}\n...\nPath: Decompiled Source Code - IDA Pro/player/SendMsg_PcRoomErrorCPlayerQEAAXEZ_1400E88A0.c\n     1\t/*\n     2\t * Function: ?SendMsg_PcRoomError@CPlayer@@QEAAXE@Z\n     3\t * Address: 0x1400E88A0\n     4\t */\n     5\t\n     6\tvoid __fastcall CPlayer::SendMsg_PcRoomError(CPlayer *this, char byErrorCode)\n     7\t{\n     8\t  __int64 *v2; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v4; // [sp+0h] [bp-78h]@1\n    11\t  char szMsg; // [sp+34h] [bp-44h]@4\n    12\t  char pbyType; // [sp+54h] [bp-24h]@4\n    13\t  char v7; // [sp+55h] [bp-23h]@4\n    14\t  CPlayer *v8; // [sp+80h] [bp+8h]@1\n    15\t\n    16\t  v8 = this;\n    17\t  v2 = &amp;v4;\n    18\t  for ( i = 28i64; i; --i )\n    19\t  {\n    20\t    *(_DWORD *)v2 = -*********;\n    21\t    v2 = (__int64 *)((char *)v2 + 4);\n    22\t  }\n    23\t  szMsg = byErrorCode;\n    24\t  pbyType = 11;\n    25\t  v7 = 34;\n    26\t  CNetProcess::LoadSendMsg(unk_1414F2088, v8-&gt;m_ObjID.m_wIndex, &amp;pbyType, &amp;szMsg, 1u);\n    27\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/j_ZoneAliveCheckRequestCNetworkEXAEAA_NHPEADZ_14000D19D.c\n     1\t/*\n     2\t * Function: j_?ZoneAliveCheckRequest@CNetworkEX@@AEAA_NHPEAD@Z\n     3\t * Address: 0x14000D19D\n     4\t */\n     5\t\n     6\tbool __fastcall CNetworkEX::ZoneAliveCheckRequest(CNetworkEX *this, int n, char *pBuf)\n     7\t{\n     8\t  return CNetworkEX::ZoneAliveCheckRequest(this, n, pBuf);\n     9\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c\n...\n    29\t  v9 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    30\t  _strdate(&amp;Buffer);\n    31\t  _strtime(&amp;v8);\n    32\t  __trace(\&quot;%s-%s: Open World\&quot;, &amp;Buffer, &amp;v8);\n    33\t  v10-&gt;m_byWorldCode = v11;\n    34\t  WriteServerStartHistory(\&quot;DBInit Begin &gt;&gt; name: %s, ip: %s\&quot;, pszDBNamea, pszDBIPa);\n    35\t  if ( CMainThread::DatabaseInit(v10, pszDBNamea, pszDBIPa) )\n    36\t  {\n    37\t    v10-&gt;m_bWorldOpen = 1;\n    38\t    CMainThread::SerivceSelfStart(v10);\n    39\t    WriteServerStartHistory(\&quot;DBInit Complete &gt;&gt;\&quot;);\n    40\t  }\n    41\t  else\n    42\t  {\n    43\t    WriteServerStartHistory(\&quot;DBInit Fail &gt;&gt;\&quot;);\n    44\t    CLogFile::WriteString(&amp;v10-&gt;m_logSystemError, \&quot;init DB fail\&quot;);\n    45\t    CWnd::SendMessageA(g_pFrame, 0x10u, 0i64, 0i64);\n    46\t  }\n    47\t}\n...\nPath: Decompiled Source Code - IDA Pro/system/SystemLogCAsyncLoggerAEAAXPEBDZZ_1403BFE60.c\n     1\t/*\n     2\t * Function: ?SystemLog@CAsyncLogger@@AEAAXPEBDZZ\n     3\t * Address: 0x1403BFE60\n     4\t */\n     5\t\n     6\tvoid __usercall CAsyncLogger::SystemLog(CAsyncLogger *this@&lt;rcx&gt;, const char *fmt@&lt;rdx&gt;, signed __int64 a3@&lt;rax&gt;, ...)\n     7\t{\n     8\t  void *v3; // rsp@1\n     9\t  __int64 *v4; // rdi@1\n    10\t  signed __int64 i; // rcx@1\n    11\t  int v6; // edi@5\n    12\t  unsigned int v7; // eax@5\n    13\t  const char *v8; // rax@9\n    14\t  __int64 v9; // [sp-20h] [bp-2CF8h]@1\n    15\t  int v10; // [sp+0h] [bp-2CD8h]@5\n    16\t  int v11; // [sp+8h] [bp-2CD0h]@5\n    17\t  int v12; // [sp+10h] [bp-2CC8h]@5\n    18\t  int v13; // [sp+18h] [bp-2CC0h]@5\n    19\t  int v14; // [sp+20h] [bp-2CB8h]@5\n    20\t  int v15; // [sp+28h] [bp-2CB0h]@5\n    21\t  int v16; // [sp+30h] [bp-2CA8h]@5\n    22\t  char DstBuf; // [sp+50h] [bp-2C88h]@5\n    23\t  char v18; // [sp+51h] [bp-2C87h]@5\n    24\t  _SYSTEMTIME SystemTime; // [sp+2C68h] [bp-70h]@5\n    25\t  int v20; // [sp+2C84h] [bp-54h]@5\n    26\t  va_list ArgList; // [sp+2C98h] [bp-40h]@8\n    27\t  int nLen; // [sp+2CA4h] [bp-34h]@8\n    28\t  int v23; // [sp+2CB0h] [bp-28h]@5\n    29\t  int v24; // [sp+2CB4h] [bp-24h]@5\n    30\t  int v25; // [sp+2CB8h] [bp-20h]@5\n    31\t  int v26; // [sp+2CBCh] [bp-1Ch]@5\n    32\t  int v27; // [sp+2CC0h] [bp-18h]@5\n    33\t  int v28; // [sp+2CC4h] [bp-14h]@5\n    34\t  unsigned __int64 v29; // [sp+2CC8h] [bp-10h]@4\n    35\t  CAsyncLogger *v30; // [sp+2CE0h] [bp+8h]@1\n    36\t  char *Format; // [sp+2CE8h] [bp+10h]@0\n    37\t  va_list va; // [sp+2CF0h] [bp+18h]@1\n    38\t\n    39\t  va_start(va, fmt);\n    40\t  v30 = this;\n    41\t  v3 = alloca(a3);\n    42\t  v4 = &amp;v9;\n    43\t  for ( i = 2876i64; i; --i )\n    44\t  {\n    45\t    *(_DWORD *)v4 = -*********;\n    46\t    v4 = (__int64 *)((char *)v4 + 4);\n    47\t  }\n    48\t  v29 = (unsigned __int64)&amp;v9 ^ _security_cookie;\n    49\t  if ( v30-&gt;m_pSystemLogInfo )\n    50\t  {\n    51\t    DstBuf = 0;\n    52\t    memset(&amp;v18, 0, 0x2BFFui64);\n    53\t    SystemTime.wYear = 0;\n    54\t    memset(&amp;SystemTime.wMonth, 0, 0xEui64);\n    55\t    GetLocalTime(&amp;SystemTime);\n    56\t    v23 = SystemTime.wMilliseconds;\n    57\t    v24 = SystemTime.wSecond;\n    58\t    v25 = SystemTime.wMinute;\n    59\t    v6 = SystemTime.wHour;\n    60\t    v26 = SystemTime.wDay;\n    61\t    v27 = SystemTime.wMonth;\n    62\t    v28 = SystemTime.wYear;\n    63\t    v7 = CAsyncLogInfo::GetCount(v30-&gt;m_pSystemLogInfo);\n    64\t    v16 = v23;\n    65\t    v15 = v24;\n    66\t    v14 = v25;\n    67\t    v13 = v6;\n    68\t    v12 = v26;\n    69\t    v11 = v27;\n    70\t    v10 = v28;\n    71\t    v20 = sprintf_s(&amp;DstBuf, 0x2C00ui64, \&quot;%u\\t%04d-%02d-%02d %02d:%02d:%02d.%03d : \&quot;, v7);\n    72\t    if ( v20 &gt; 0 &amp;&amp; v20 &lt; 11264 )\n    73\t    {\n    74\t      ArgList = (va_list)va;\n    75\t      nLen = vsprintf_s(&amp;DstBuf + v20, 11264i64 - v20, Format, (va_list)va);\n    76\t      ArgList = 0i64;\n    77\t      if ( nLen &gt; 0 )\n    78\t      {\n    79\t        nLen += v20;\n    80\t        v8 = CAsyncLogInfo::GetFileName(v30-&gt;m_pSystemLogInfo);\n    81\t        CAsyncLogBufferList::WriteFile(v8, nLen, &amp;DstBuf);\n    82\t      }\n    83\t    }\n    84\t  }\n    85\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/InitialzieTRC_AutoTradeQEAA_NXZ_1402D7E00.c\n...\n    25\t  v9 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    26\t  CreateDirectoryA(\&quot;..\\\\ZoneServerLog\\\\ServiceLog\\\\ATradeTax\&quot;, 0i64);\n    27\t  CreateDirectoryA(\&quot;..\\\\ZoneServerLog\\\\SystemLog\\\\Concession\&quot;, 0i64);\n    28\t  Dest = 0;\n    29\t  memset(&amp;v8, 0, 0x7Fui64);\n    30\t  v3 = GetKorLocalTime();\n    31\t  sprintf(&amp;Dest, \&quot;..\\\\ZoneServerLog\\\\ServiceLog\\\\ATradeTax\\\\atrade_earn_%d_%d.log\&quot;, v10-&gt;m_byRace, v3);\n    32\t  CLogFile::SetWriteLogFile(&amp;v10-&gt;m_serviceLog, &amp;Dest, 1, 0, 1, 1);\n    33\t  v4 = GetKorLocalTime();\n    34\t  sprintf(&amp;Dest, \&quot;..\\\\ZoneServerLog\\\\SystemLog\\\\Concession\\\\system_TRC_%d_%d.log\&quot;, v10-&gt;m_byRace, v4);\n    35\t  CLogFile::SetWriteLogFile(&amp;v10-&gt;m_sysLog, &amp;Dest, 1, 0, 1, 1);\n    36\t  ControllerTaxRate::setLimitTaxRate(&amp;v10-&gt;m_Controller, 0.*********, 0.2);\n    37\t  return 1;\n    38\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/Load_Golden_Box_Item_EventCGoldenBoxItemMgrQEAA_NX_1404121A0.c\n...\n    21\t  CNetTimer::BeginTimer(&amp;v5-&gt;m_golden_box_event.m_event_timer, 0x1D4C0u);\n    22\t  CLogFile::Write(&amp;v5-&gt;m_golden_box_event.m_event_log, \&quot;Server Start and LogFile Loading\&quot;);\n    23\t  CGoldenBoxItemMgr::Set_Event_Status(v5, 0);\n    24\t  if ( CGoldenBoxItemMgr::Load_Event_INI(v5, &amp;v5-&gt;m_golden_box_event.m_ini) )\n    25\t  {\n    26\t    CGoldenBoxItemMgr::Check_Loaded_Event_Status(v5);\n    27\t    result = !CGoldenBoxItemMgr::Get_Event_Status(v5) || CGoldenBoxItemMgr::SetGoldBoxItemIndex(v5);\n    28\t  }\n    29\t  else\n    30\t  {\n    31\t    MyMessageBox(\&quot;CGoldenBoxItemMgr::Load_Event_INI() : \&quot;, \&quot;Load_Golden_Box_Item_Event() Fail!\&quot;);\n    32\t    result = 0;\n    33\t  }\n    34\t  return result;\n    35\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/0BossSchedule_MapQEAAXZ_14041B720.c\n     1\t/*\n     2\t * Function: ??0BossSchedule_Map@@QEAA@XZ\n     3\t * Address: 0x14041B720\n     4\t */\n     5\t\n     6\tvoid __fastcall BossSchedule_Map::BossSchedule_Map(BossSchedule_Map *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v3; // [sp+0h] [bp-28h]@1\n    11\t  BossSchedule_Map *Dst; // [sp+30h] [bp+8h]@1\n    12\t\n    13\t  Dst = this;\n    14\t  v1 = &amp;v3;\n    15\t  for ( i = 8i64; i; --i )\n    16\t  {\n    17\t    *(_DWORD *)v1 = -*********;\n    18\t    v1 = (__int64 *)((char *)v1 + 4);\n    19\t  }\n    20\t  CIniFile::CIniFile(&amp;Dst-&gt;m_INIFile);\n    21\t  memset_0(Dst, 0, 0x198ui64);\n    22\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/j_wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_14000B4BA.c\n     1\t/*\n     2\t * Function: j_?wa_EnterWorld@@YAXPEAU_WA_AVATOR_CODE@@G@Z\n     3\t * Address: 0x14000B4BA\n     4\t */\n     5\t\n     6\tvoid __fastcall wa_EnterWorld(_WA_AVATOR_CODE *pData, unsigned __int16 wZoneIndex)\n     7\t{\n     8\t  wa_EnterWorld(pData, wZoneIndex);\n     9\t}\n...\nPath: Decompiled Source Code - IDA Pro/system/InitCMsgListRACE_BOSS_MSGIEAA_NXZ_14029EE60.c\n     1\t/*\n     2\t * Function: ?Init@CMsgList@RACE_BOSS_MSG@@IEAA_NXZ\n     3\t * Address: 0x14029EE60\n     4\t */\n     5\t\n     6\tchar __fastcall RACE_BOSS_MSG::CMsgList::Init(RACE_BOSS_MSG::CMsgList *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v4; // rax@9\n    12\t  __int64 v5; // [sp+0h] [bp-68h]@1\n    13\t  unsigned int dwID; // [sp+20h] [bp-48h]@6\n    14\t  unsigned int dwIndex; // [sp+24h] [bp-44h]@12\n    15\t  unsigned int j; // [sp+28h] [bp-40h]@15\n    16\t  RACE_BOSS_MSG::CMsg **v9; // [sp+30h] [bp-38h]@6\n    17\t  RACE_BOSS_MSG::CMsg *v10; // [sp+38h] [bp-30h]@11\n    18\t  RACE_BOSS_MSG::CMsg *v11; // [sp+40h] [bp-28h]@8\n    19\t  __int64 v12; // [sp+48h] [bp-20h]@4\n    20\t  unsigned __int64 v13; // [sp+50h] [bp-18h]@6\n    21\t  RACE_BOSS_MSG::CMsg *v14; // [sp+58h] [bp-10h]@9\n    22\t  RACE_BOSS_MSG::CMsgList *v15; // [sp+70h] [bp+8h]@1\n    23\t\n    24\t  v15 = this;\n    25\t  v1 = &amp;v5;\n    26\t  for ( i = 24i64; i; --i )\n    27\t  {\n    28\t    *(_DWORD *)v1 = -*********;\n    29\t    v1 = (__int64 *)((char *)v1 + 4);\n    30\t  }\n    31\t  v12 = -2i64;\n    32\t  if ( v15-&gt;m_uiSize )\n    33\t  {\n    34\t    CNetIndexList::SetList(&amp;v15-&gt;m_kEmptyInxList, v15-&gt;m_uiSize);\n    35\t    CNetIndexList::SetList(&amp;v15-&gt;m_kUseInxList, v15-&gt;m_uiSize);\n    36\t    CNetIndexList::SetList(&amp;v15-&gt;m_kWaitInxList, v15-&gt;m_uiSize);\n    37\t    v13 = v15-&gt;m_uiSize;\n    38\t    v9 = (RACE_BOSS_MSG::CMsg **)operator new[](saturated_mul(8ui64, v13));\n    39\t    v15-&gt;m_ppMsg = v9;\n    40\t    for ( dwID = 0; dwID &lt; v15-&gt;m_uiSize; ++dwID )\n    41\t    {\n    42\t      v11 = (RACE_BOSS_MSG::CMsg *)operator new(0x58ui64);\n    43\t      if ( v11 )\n    44\t      {\n    45\t        RACE_BOSS_MSG::CMsg::CMsg(v11, v15-&gt;m_ucRace, dwID);\n    46\t        v14 = (RACE_BOSS_MSG::CMsg *)v4;\n    47\t      }\n    48\t      else\n    49\t      {\n    50\t        v14 = 0i64;\n    51\t      }\n    52\t      v10 = v14;\n    53\t      v15-&gt;m_ppMsg[dwID] = v14;\n    54\t    }\n...\nPath: Decompiled Source Code - IDA Pro/items/InitTimeItemQEAA_NXZ_14030E160.c\n...\n    25\t  v9 = (unsigned __int64)&amp;v4 ^ _security_cookie;\n    26\t  lpPathName = \&quot;..\\\\ZoneServerLog\\\\SystemLog\&quot;;\n    27\t  CreateDirectoryA(\&quot;..\\\\ZoneServerLog\\\\SystemLog\&quot;, 0i64);\n    28\t  DstBuf = 0;\n    29\t  memset(&amp;v8, 0, 0xFFui64);\n    30\t  *(_DWORD *)bDate = GetKorLocalTime();\n    31\t  sprintf_s(&amp;DstBuf, 0x100ui64, \&quot;%s\\\\TimeItem_%d.log\&quot;, lpPathName);\n    32\t  CLogFile::SetWriteLogFile(&amp;v10-&gt;_kLogger, &amp;DstBuf, 1, 0, 1, 1);\n    33\t  return TimeItem::ReadGoods(v10) != 0;\n    34\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.c\n     1\t/*\n     2\t * Function: ?wa_EnterWorld@@YAXPEAU_WA_AV...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;0d1d47cf-b487-41c1-be22-67e4bc7300eb;toolu_0181qRsSGXjjTPRjLdSJ2QJR&quot;:{&quot;requestId&quot;:&quot;0d1d47cf-b487-41c1-be22-67e4bc7300eb&quot;,&quot;toolUseId&quot;:&quot;toolu_0181qRsSGXjjTPRjLdSJ2QJR&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: Decompiled Source Code - IDA Pro/world/pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c\n     1\t/*\n     2\t * Function: ?pc_OpenWorldSuccessResult@CMainThread@@QEAAXEPEAD0@Z\n     3\t * Address: 0x1401F5580\n     4\t */\n     5\t\n     6\tvoid __fastcall CMainThread::pc_OpenWorldSuccessResult(CMainThread *this, char byWorldCode, char *pszDBName, char *pszDBIP)\n     7\t{\n     8\t  __int64 *v4; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v6; // [sp+0h] [bp-178h]@1\n    11\t  char Buffer; // [sp+30h] [bp-148h]@4\n    12\t  char v8; // [sp+D0h] [bp-A8h]@4\n    13\t  unsigned __int64 v9; // [sp+160h] [bp-18h]@4\n    14\t  CMainThread *v10; // [sp+180h] [bp+8h]@1\n    15\t  char v11; // [sp+188h] [bp+10h]@1\n    16\t  char *pszDBNamea; // [sp+190h] [bp+18h]@1\n    17\t  char *pszDBIPa; // [sp+198h] [bp+20h]@1\n    18\t\n    19\t  pszDBIPa = pszDBIP;\n    20\t  pszDBNamea = pszDBName;\n    21\t  v11 = byWorldCode;\n    22\t  v10 = this;\n    23\t  v4 = &amp;v6;\n    24\t  for ( i = 92i64; i; --i )\n    25\t  {\n    26\t    *(_DWORD *)v4 = -*********;\n    27\t    v4 = (__int64 *)((char *)v4 + 4);\n    28\t  }\n    29\t  v9 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    30\t  _strdate(&amp;Buffer);\n    31\t  _strtime(&amp;v8);\n    32\t  __trace(\&quot;%s-%s: Open World\&quot;, &amp;Buffer, &amp;v8);\n    33\t  v10-&gt;m_byWorldCode = v11;\n    34\t  WriteServerStartHistory(\&quot;DBInit Begin &gt;&gt; name: %s, ip: %s\&quot;, pszDBNamea, pszDBIPa);\n    35\t  if ( CMainThread::DatabaseInit(v10, pszDBNamea, pszDBIPa) )\n    36\t  {\n    37\t    v10-&gt;m_bWorldOpen = 1;\n    38\t    CMainThread::SerivceSelfStart(v10);\n    39\t    WriteServerStartHistory(\&quot;DBInit Complete &gt;&gt;\&quot;);\n    40\t  }\n    41\t  else\n    42\t  {\n    43\t    WriteServerStartHistory(\&quot;DBInit Fail &gt;&gt;\&quot;);\n    44\t    CLogFile::WriteString(&amp;v10-&gt;m_logSystemError, \&quot;init DB fail\&quot;);\n    45\t    CWnd::SendMessageA(g_pFrame, 0x10u, 0i64, 0i64);\n    46\t  }\n    47\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/ZoneAliveCheckRequestCNetworkEXAEAA_NHPEADZ_1401C3D90.c\n     1\t/*\n     2\t * Function: ?ZoneAliveCheckRequest@CNetworkEX@@AEAA_NHPEAD@Z\n     3\t * Address: 0x1401C3D90\n     4\t */\n     5\t\n     6\tchar __fastcall CNetworkEX::ZoneAliveCheckRequest(CNetworkEX *this, int n, char *pBuf)\n     7\t{\n     8\t  __int64 *v3; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  CBillingManager *v5; // rax@4\n    11\t  __int64 v7; // [sp+0h] [bp-38h]@1\n    12\t  unsigned int *v8; // [sp+20h] [bp-18h]@4\n    13\t\n    14\t  v3 = &amp;v7;\n    15\t  for ( i = 12i64; i; --i )\n    16\t  {\n    17\t    *(_DWORD *)v3 = -*********;\n    18\t    v3 = (__int64 *)((char *)v3 + 4);\n    19\t  }\n    20\t  v8 = (unsigned int *)pBuf;\n    21\t  v5 = CTSingleton&lt;CBillingManager&gt;::Instance();\n    22\t  CBillingManager::SendMsg_ZoneAliveCheck(v5, *v8);\n    23\t  return 1;\n    24\t}\n...\nPath: Decompiled Source Code - IDA Pro/system/SetNetSystemCNetWorkingQEAA_NKPEAU_NET_TYPE_PARAMP_1404813E0.c\n...\n    35\t  v20 = (unsigned __int64)&amp;v10 ^ _security_cookie;\n    36\t  pNetwork-&gt;m_dwUseProcessNum = dwUseProcessNum;\n    37\t  wsprintfA(pNetwork-&gt;m_szSystemName, \&quot;%s\&quot;, szSystemName);\n    38\t  if ( pszLogPath )\n    39\t    strcpy_s(pNetwork-&gt;m_szLogPath, 0x80ui64, pszLogPath);\n    40\t  else\n    41\t    strcpy_s(pNetwork-&gt;m_szLogPath, 0x80ui64, \&quot;.\\\\NetLog\&quot;);\n    42\t  CreateDirectoryA(pNetwork-&gt;m_szLogPath, 0i64);\n    43\t  *(_DWORD *)bDate = GetKorLocalTime();\n    44\t  wsprintfA(&amp;szFileName, \&quot;%s\\\\%s_Sys%d.log\&quot;, pNetwork-&gt;m_szLogPath, pNetwork-&gt;m_szSystemName);\n    45\t  CLogFile::SetWriteLogFile(&amp;pNetwork-&gt;m_LogFile, &amp;szFileName, 1, 1, 1, 1);\n    46\t  *(_DWORD *)bDate = GetKorLocalTime();\n    47\t  wsprintfA(&amp;szFileName, \&quot;%s\\\\%s_CcrFgSys%d.log\&quot;, pNetwork-&gt;m_szLogPath, pNetwork-&gt;m_szSystemName);\n    48\t  CLogFile::SetWriteLogFile(&amp;g_FgLogFile, &amp;szFileName, 1, 1, 1, 1);\n    49\t  ReturnedString = 0;\n    50\t  memset(&amp;v14, 0, 0xFui64);\n    51\t  GetPrivateProfileStringA(\&quot;FireGuard Use\&quot;, \&quot;Use\&quot;, \&quot;TRUE\&quot;, &amp;ReturnedString, 0x10u, \&quot;.\\\\fireguard\\\\fgrs.ini\&quot;);\n    52\t  pNetwork-&gt;m_bUseFG = strcmp_0(&amp;ReturnedString, \&quot;FALSE\&quot;) != 0;\n    53\t  GetPrivateProfileStringA(\&quot;System\&quot;, \&quot;WorldName\&quot;, \&quot;X\&quot;, pNetwork-&gt;m_szServerName, 0x21u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    54\t  if ( pNetwork-&gt;m_szServerName[0] == 88 )\n    55\t  {\n    56\t    CLogFile::Write(&amp;g_FgLogFile, \&quot;FG Error : WorldName Read Failed In \\\&quot;WorldInfo.ini\\\&quot; File\&quot;);\n    57\t    result = 0;\n    58\t  }\n    59\t  else\n    60\t  {\n    61\t    for ( nIndex = 0; nIndex &lt; pNetwork-&gt;m_dwUseProcessNum; ++nIndex )\n    62\t    {\n    63\t      bDate[0] = pNetwork-&gt;m_bUseFG;\n    64\t      if ( !CNetProcess::SetProcess(&amp;pNetwork-&gt;m_Process[nIndex], nIndex, &amp;v22[nIndex], pNetwork, bDate[0]) )\n    65\t        return 0;\n    66\t      if ( !nIndex )\n    67\t      {\n    68\t        Filename = 0;\n    69\t        memset(&amp;v17, 0, 0x103ui64);\n    70\t        GetModuleFileNameA(0i64, &amp;Filename, 0x104u);\n    71\t        v18 = _tcsrchr(&amp;Filename, 0x5Cu) + 1;\n    72\t        *v18 = 0;\n    73\t        strcat_s(&amp;Filename, 0x104ui64, \&quot;fireguard\\\\\&quot;);\n    74\t        v8 = strlen_0(&amp;Filename);\n    75\t        if ( AddEnvVariable(\&quot;path\&quot;, &amp;Filename, v8) &lt;= 0 )\n    76\t        {\n    77\t          CLogFile::Write(\n    78\t            &amp;pNetwork-&gt;m_LogFile,\n    79\t            \&quot;SetNetSystem(%d) CCRFG SERVER : AddEnvVariable() Fail\&quot;,\n    80\t            (unsigned int)nIndex);\n    81\t          return 0;\n    82\t        }\n...\nPath: Decompiled Source Code - IDA Pro/world/j_size_server_notify_inform_zoneQEAAHXZ_140005187.c\n     1\t/*\n     2\t * Function: j_?size@_server_notify_inform_zone@@QEAAHXZ\n     3\t * Address: 0x140005187\n     4\t */\n     5\t\n     6\tint __fastcall _server_notify_inform_zone::size(_server_notify_inform_zone *this)\n     7\t{\n     8\t  return _server_notify_inform_zone::size(this);\n     9\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.c\n...\n     5\t\n     6\tchar __fastcall CNetworkEX::OpenWorldSuccessResult(CNetworkEX *this, unsigned int n, char *pMsg)\n     7\t{\n     8\t  __int64 *v3; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v6; // [sp+0h] [bp-38h]@1\n    12\t  char *v7; // [sp+20h] [bp-18h]@4\n    13\t\n    14\t  v3 = &amp;v6;\n    15\t  for ( i = 12i64; i; --i )\n    16\t  {\n    17\t    *(_DWORD *)v3 = -*********;\n    18\t    v3 = (__int64 *)((char *)v3 + 4);\n    19\t  }\n    20\t  v7 = pMsg;\n    21\t  if ( unk_1799C9AE9 == (unsigned __int8)pMsg[50] )\n    22\t  {\n    23\t    CMainThread::pc_OpenWorldSuccessResult(&amp;g_Main, *v7, v7 + 1, v7 + 33);\n    24\t    result = 1;\n    25\t  }\n    26\t  else\n    27\t  {\n    28\t    MyMessageBox(\n    29\t      \&quot;OpenWorld(Account -&gt; Zone)\&quot;,\n    30\t      \&quot;!!Server type is wrong!!(AC:%d)(ZO:%d)\&quot;,\n    31\t      (unsigned __int8)v7[50],\n    32\t      unk_1799C9AE9);\n    33\t    WriteServerStartHistory(\n    34\t      \&quot;ServerType is Wrong ==&gt; AccountServer(%d) != ZoneServer(%d)\&quot;,\n    35\t      (unsigned __int8)v7[50],\n    36\t      unk_1799C9AE9);\n    37\t    result = 1;\n    38\t  }\n...\nPath: Decompiled Source Code - IDA Pro/world/LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c\n...\n    41\t  v22 = -2i64;\n    42\t  v24 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    43\t  GetPrivateProfileStringA(\&quot;System\&quot;, \&quot;WorldName\&quot;, \&quot;X\&quot;, v25-&gt;m_szWorldName, 0x21u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    44\t  if ( !strcmp_0(v25-&gt;m_szWorldName, \&quot;X\&quot;) )\n    45\t    return 0xFFFFFFFFi64;\n    46\t  M2W(v25-&gt;m_szWorldName, v25-&gt;m_wszWorldName, 0x21u);\n    47\t  v25-&gt;m_bFreeServer = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;FreeServer\&quot;, 0, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    48\t  v25-&gt;m_byWorldType = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;ServerType\&quot;, 2, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    49\t  ReturnedString = 0;\n    50\t  memset(&amp;v10, 0, 8ui64);\n    51\t  GetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ReleaseType\&quot;, \&quot;X\&quot;, &amp;ReturnedString, 9u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    52\t  if ( !_stricmp(&amp;ReturnedString, \&quot;Internal\&quot;) )\n    53\t  {\n    54\t    v25-&gt;m_bReleaseServiceMode = 0;\n    55\t  }\n    56\t  else\n    57\t  {\n    58\t    if ( _stricmp(&amp;ReturnedString, \&quot;Release\&quot;) )\n    59\t    {\n    60\t      MyMessageBox(\n    61\t        \&quot;CMainThread::LoadWorldSystemINI()\&quot;,\n    62\t        \&quot;WorldInfo.ini\\r\\n[ServerMode]\\r\\nReleaseType = %s Invalid!!\&quot;,\n    63\t        &amp;ReturnedString);\n    64\t      return 4294967294i64;\n    65\t    }\n    66\t    v25-&gt;m_bReleaseServiceMode = 1;\n    67\t  }\n    68\t  Str1 = 0;\n    69\t  memset(&amp;v12, 0, 5ui64);\n    70\t  GetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ExcuteService\&quot;, \&quot;X\&quot;, &amp;Str1, 6u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    71\t  if ( !_stricmp(&amp;Str1, \&quot;true\&quot;) )\n    72\t  {\n    73\t    v25-&gt;m_bExcuteService = 1;\n    74\t  }\n    75\t  else\n    76\t  {\n    77\t    if ( _stricmp(&amp;Str1, \&quot;false\&quot;) )\n    78\t    {\n    79\t      MyMessageBox(\n    80\t        \&quot;CMainThread::LoadWorldSystemINI()\&quot;,\n    81\t        \&quot;WorldInfo.ini\\r\\n[ServerMode]\\r\\nExcuteService = %s Invalid!!\&quot;,\n    82\t        &amp;Str1);\n    83\t      return 4294967293i64;\n    84\t    }\n    85\t    v25-&gt;m_bExcuteService = 0;\n    86\t  }\n...\nPath: Decompiled Source Code - IDA Pro/world/size_server_notify_inform_zoneQEAAHXZ_14011F1E0.c\n     1\t/*\n     2\t * Function: ?size@_server_notify_inform_zone@@QEAAHXZ\n     3\t * Address: 0x14011F1E0\n     4\t */\n     5\t\n     6\tsigned __int64 __fastcall _server_notify_inform_zone::size(_server_notify_inform_zone *this)\n     7\t{\n     8\t  return 6i64;\n     9\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/start_cdeCashItemRemoteStoreQEAA_NHHHHZ_1402F7590.c\n...\n    64\t    v19-&gt;m_cde.m_ini.m_byHour[1] = Tm.tm_hour;\n    65\t    v19-&gt;m_cde.m_ini.m_byMinute[1] = Tm.tm_min;\n    66\t    CashItemRemoteStore::set_cde_status(v19, 1);\n    67\t    CashItemRemoteStore::log_about_cash_event(v19, \&quot;Loaded From Cheat-Args &lt;When Server Running&gt;\&quot;, &amp;v19-&gt;m_cde.m_ini);\n    68\t    result = 1;\n    69\t  }\n    70\t  return result;\n    71\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/load_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F5E30.c\n...\n    20\t  CLogFile::Write(&amp;v4-&gt;m_cde.m_cde_log, \&quot;Loading Cash Discount-Rate Event\&quot;);\n    21\t  CNetTimer::BeginTimer(&amp;v4-&gt;m_cde.m_cde_timer, 0x3E8u);\n    22\t  v4-&gt;m_cde.m_cde_inform_before[0] = 1800;\n    23\t  v4-&gt;m_cde.m_cde_inform_before[1] = 300;\n    24\t  v4-&gt;m_cde.m_cde_status = 0;\n    25\t  CashItemRemoteStore::load_cde_ini(v4, &amp;v4-&gt;m_cde.m_ini, &amp;v4-&gt;m_cde.m_cde_ini_file_time);\n    26\t  CashItemRemoteStore::log_about_cash_event(v4, \&quot;Loaded From Ini File &lt;When Server Started&gt;\&quot;, &amp;v4-&gt;m_cde.m_ini);\n    27\t  CashItemRemoteStore::check_loaded_cde_status(v4);\n    28\t  CLogFile::Write(&amp;v4-&gt;m_cde.m_cde_log, \&quot;Complete For Cash Discount-Rate Event\&quot;);\n    29\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/Load_Golden_Box_Item_EventCGoldenBoxItemMgrQEAA_NX_1404121A0.c\n...\n    21\t  CNetTimer::BeginTimer(&amp;v5-&gt;m_golden_box_event.m_event_timer, 0x1D4C0u);\n    22\t  CLogFile::Write(&amp;v5-&gt;m_golden_box_event.m_event_log, \&quot;Server Start and LogFile Loading\&quot;);\n    23\t  CGoldenBoxItemMgr::Set_Event_Status(v5, 0);\n    24\t  if ( CGoldenBoxItemMgr::Load_Event_INI(v5, &amp;v5-&gt;m_golden_box_event.m_ini) )\n    25\t  {\n    26\t    CGoldenBoxItemMgr::Check_Loaded_Event_Status(v5);\n    27\t    result = !CGoldenBoxItemMgr::Get_Event_Status(v5) || CGoldenBoxItemMgr::SetGoldBoxItemIndex(v5);\n    28\t  }\n    29\t  else\n    30\t  {\n    31\t    MyMessageBox(\&quot;CGoldenBoxItemMgr::Load_Event_INI() : \&quot;, \&quot;Load_Golden_Box_Item_Event() Fail!\&quot;);\n    32\t    result = 0;\n    33\t  }\n    34\t  return result;\n    35\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/Load_Cash_EventCashItemRemoteStoreQEAAXXZ_1402F7F10.c\n...\n    23\t  v6 = (unsigned __int64)&amp;v3 ^ _security_cookie;\n    24\t  for ( j = 0; j &lt; 3; ++j )\n    25\t  {\n    26\t    CNetTimer::BeginTimer(&amp;v7-&gt;m_cash_event[j].m_event_timer, 0x3E8u);\n    27\t    v7-&gt;m_cash_event[j].m_event_inform_before[0] = 1800;\n    28\t    v7-&gt;m_cash_event[j].m_event_inform_before[1] = 300;\n    29\t    CLogFile::Write(&amp;v7-&gt;m_cash_event[j].m_event_log, \&quot;Server Start and LogFile Loaing\&quot;);\n    30\t    CashItemRemoteStore::Set_CashEvent_Status(v7, j, 0);\n    31\t    Get_CashEvent_Name(j, &amp;szEventName);\n    32\t    CashItemRemoteStore::Load_Event_INI(\n    33\t      v7,\n    34\t      &amp;v7-&gt;m_cash_event[j].m_ini,\n    35\t      &amp;v7-&gt;m_cash_event[j].m_event_ini_file_time,\n    36\t      &amp;szEventName);\n    37\t    if ( j == 2 )\n    38\t      CashItemRemoteStore::Load_LimitedSale_Event_INI(\n    39\t        v7,\n    40\t        &amp;v7-&gt;m_cash_event[2].m_ini,\n    41\t        &amp;v7-&gt;m_cash_event[2].m_event_ini_file_time,\n    42\t        &amp;szEventName);\n    43\t    CashItemRemoteStore::Check_Loaded_Event_Status(v7, j);\n    44\t  }\n    45\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/LoadCUnmannedTraderSchedulerQEAA_NXZ_1403935E0.c\n...\n     5\t\n     6\tchar __fastcall CUnmannedTraderScheduler::Load(CUnmannedTraderScheduler *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v4; // [sp+0h] [bp-148h]@1\n    12\t  char Dst; // [sp+30h] [bp-118h]@4\n    13\t  char v6; // [sp+134h] [bp-14h]@4\n    14\t  CUnmannedTraderScheduler *v7; // [sp+150h] [bp+8h]@1\n    15\t\n    16\t  v7 = this;\n    17\t  v1 = &amp;v4;\n    18\t  for ( i = 80i64; i; --i )\n    19\t  {\n    20\t    *(_DWORD *)v1 = -*********;\n    21\t    v1 = (__int64 *)((char *)v1 + 4);\n    22\t  }\n    23\t  memset_0(&amp;Dst, 0, 0xF8ui64);\n    24\t  v6 = CRFWorldDatabase::Select_UnmannedTraderReservedSchedule(\n    25\t         pkDB,\n    26\t         CUnmannedTraderEnvironmentValue::Unmanned_Trader_Max_Schdule_Cnt,\n    27\t         (_unmannedtrader_reserved_schedule_info *)&amp;Dst);\n    28\t  if ( v6 == 2 )\n    29\t  {\n    30\t    v7-&gt;m_bLoad = 1;\n    31\t    result = 1;\n    32\t  }\n    33\t  else if ( v6 )\n    34\t  {\n    35\t    result = 0;\n    36\t  }\n...\nPath: Decompiled Source Code - IDA Pro/system/SystemLogCAsyncLoggerAEAAXPEBDZZ_1403BFE60.c\n     1\t/*\n     2\t * Function: ?SystemLog@CAsyncLogger@@AEAAXPEBDZZ\n     3\t * Address: 0x1403BFE60\n     4\t */\n     5\t\n     6\tvoid __usercall CAsyncLogger::SystemLog(CAsyncLogger *this@&lt;rcx&gt;, const char *fmt@&lt;rdx&gt;, signed __int64 a3@&lt;rax&gt;, ...)\n     7\t{\n     8\t  void *v3; // rsp@1\n     9\t  __int64 *v4; // rdi@1\n    10\t  signed __int64 i; // rcx@1\n    11\t  int v6; // edi@5\n    12\t  unsigned int v7; // eax@5\n    13\t  const char *v8; // rax@9\n    14\t  __int64 v9; // [sp-20h] [bp-2CF8h]@1\n    15\t  int v10; // [sp+0h] [bp-2CD8h]@5\n    16\t  int v11; // [sp+8h] [bp-2CD0h]@5\n    17\t  int v12; // [sp+10h] [bp-2CC8h]@5\n    18\t  int v13; // [sp+18h] [bp-2CC0h]@5\n    19\t  int v14; // [sp+20h] [bp-2CB8h]@5\n    20\t  int v15; // [sp+28h] [bp-2CB0h]@5\n    21\t  int v16; // [sp+30h] [bp-2CA8h]@5\n    22\t  char DstBuf; // [sp+50h] [bp-2C88h]@5\n    23\t  char v18; // [sp+51h] [bp-2C87h]@5\n    24\t  _SYSTEMTIME SystemTime; // [sp+2C68h] [bp-70h]@5\n    25\t  int v20; // [sp+2C84h] [bp-54h]@5\n    26\t  va_list ArgList; // [sp+2C98h] [bp-40h]@8\n    27\t  int nLen; // [sp+2CA4h] [bp-34h]@8\n    28\t  int v23; // [sp+2CB0h] [bp-28h]@5\n    29\t  int v24; // [sp+2CB4h] [bp-24h]@5\n    30\t  int v25; // [sp+2CB8h] [bp-20h]@5\n    31\t  int v26; // [sp+2CBCh] [bp-1Ch]@5\n    32\t  int v27; // [sp+2CC0h] [bp-18h]@5\n    33\t  int v28; // [sp+2CC4h] [bp-14h]@5\n    34\t  unsigned __int64 v29; // [sp+2CC8h] [bp-10h]@4\n    35\t  CAsyncLogger *v30; // [sp+2CE0h] [bp+8h]@1\n    36\t  char *Format; // [sp+2CE8h] [bp+10h]@0\n    37\t  va_list va; // [sp+2CF0h] [bp+18h]@1\n    38\t\n    39\t  va_start(va, fmt);\n    40\t  v30 = this;\n    41\t  v3 = alloca(a3);\n    42\t  v4 = &amp;v9;\n    43\t  for ( i = 2876i64; i; --i )\n    44\t  {\n    45\t    *(_DWORD *)v4 = -*********;\n    46\t    v4 = (__int64 *)((char *)v4 + 4);\n    47\t  }\n    48\t  v29 = (unsigned __int64)&amp;v9 ^ _security_cookie;\n    49\t  if ( v30-&gt;m_pSystemLogInfo )\n    50\t  {\n    51\t    DstBuf = 0;\n    52\t    memset(&amp;v18, 0, 0x2BFFui64);\n    53\t    SystemTime.wYear = 0;\n    54\t    memset(&amp;SystemTime.wMonth, 0, 0xEui64);\n    55\t    GetLocalTime(&amp;SystemTime);\n    56\t    v23 = SystemTime.wMilliseconds;\n    57\t    v24 = SystemTime.wSecond;\n    58\t    v25 = SystemTime.wMinute;\n    59\t    v6 = SystemTime.wHour;\n    60\t    v26 = SystemTime.wDay;\n    61\t    v27 = SystemTime.wMonth;\n    62\t    v28 = SystemTime.wYear;\n    63\t    v7 = CAsyncLogInfo::GetCount(v30-&gt;m_pSystemLogInfo);\n    64\t    v16 = v23;\n    65\t    v15 = v24;\n    66\t    v14 = v25;\n    67\t    v13 = v6;\n    68\t    v12 = v26;\n    69\t    v11 = v27;\n    70\t    v10 = v28;\n    71\t    v20 = sprintf_s(&amp;DstBuf, 0x2C00ui64, \&quot;%u\\t%04d-%02d-%02d %02d:%02d:%02d.%03d : \&quot;, v7);\n    72\t    if ( v20 &gt; 0 &amp;&amp; v20 &lt; 11264 )\n    73\t    {\n    74\t      ArgList = (va_list)va;\n    75\t      nLen = vsprintf_s(&amp;DstBuf + v20, 11264i64 - v20, Format, (va_list)va);\n    76\t      ArgList = 0i64;\n    77\t      if ( nLen &gt; 0 )\n    78\t      {\n    79\t        nLen += v20;\n    80\t        v8 = CAsyncLogInfo::GetFileName(v30-&gt;m_pSystemLogInfo);\n    81\t        CAsyncLogBufferList::WriteFile(v8, nLen, &amp;DstBuf);\n    82\t      }\n    83\t    }\n    84\t  }\n    85\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/0BossSchedule_MapQEAAXZ_14041B720.c\n     1\t/*\n     2\t * Function: ??0BossSchedule_Map@@QEAA@XZ\n     3\t * Address: 0x14041B720\n     4\t */\n     5\t\n     6\tvoid __fastcall BossSchedule_Map::BossSchedule_Map(BossSchedule_Map *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v3; // [sp+0h] [bp-28h]@1\n    11\t  BossSchedule_Map *Dst; // [sp+30h] [bp+8h]@1\n    12\t\n    13\t  Dst = this;\n    14\t  v1 = &amp;v3;\n    15\t  for ( i = 8i64; i; --i )\n    16\t  {\n    17\t    *(_DWORD *)v1 = -*********;\n    18\t    v1 = (__int64 *)((char *)v1 + 4);\n    19\t  }\n    20\t  CIniFile::CIniFile(&amp;Dst-&gt;m_INIFile);\n    21\t  memset_0(Dst, 0, 0x198ui64);\n    22\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/InitialzieTRC_AutoTradeQEAA_NXZ_1402D7E00.c\n...\n    25\t  v9 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    26\t  CreateDirectoryA(\&quot;..\\\\ZoneServerLog\\\\ServiceLog\\\\ATradeTax\&quot;, 0i64);\n    27\t  CreateDirectoryA(\&quot;..\\\\ZoneServerLog\\\\SystemLog\\\\Concession\&quot;, 0i64);\n    28\t  Dest = 0;\n    29\t  memset(&amp;v8, 0, 0x7Fui64);\n    30\t  v3 = GetKorLocalTime();\n    31\t  sprintf(&amp;Dest, \&quot;..\\\\ZoneServerLog\\\\ServiceLog\\\\ATradeTax\\\\atrade_earn_%d_%d.log\&quot;, v10-&gt;m_byRace, v3);\n    32\t  CLogFile::SetWriteLogFile(&amp;v10-&gt;m_serviceLog, &amp;Dest, 1, 0, 1, 1);\n    33\t  v4 = GetKorLocalTime();\n    34\t  sprintf(&amp;Dest, \&quot;..\\\\ZoneServerLog\\\\SystemLog\\\\Concession\\\\system_TRC_%d_%d.log\&quot;, v10-&gt;m_byRace, v4);\n    35\t  CLogFile::SetWriteLogFile(&amp;v10-&gt;m_sysLog, &amp;Dest, 1, 0, 1, 1);\n    36\t  ControllerTaxRate::setLimitTaxRate(&amp;v10-&gt;m_Controller, 0.*********, 0.2);\n    37\t  return 1;\n    38\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.c\n     1\t/*\n     2\t * Function: ?wa_EnterWorld@@YAXPEAU_WA_AVATOR_CODE@@G@Z\n     3\t * Address: 0x140046110\n     4\t */\n     5\t\n     6\tvoid __fastcall wa_EnterWorld(_WA_AVATOR_CODE *pData, unsigned __int16 wZoneIndex)\n     7\t{\n     8\t  __int64 *v2; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v4; // [sp+0h] [bp-38h]@1\n    11\t  CPartyPlayer *v5; // [sp+20h] [bp-18h]@4\n    12\t  _WA_AVATOR_CODE *pDataa; // [sp+40h] [bp+8h]@1\n    13\t\n    14\t  pDataa = pData;\n    15\t  v2 = &amp;v4;\n    16\t  for ( i = 12i64; i; --i )\n    17\t  {\n    18\t    *(_DWORD *)v2 = -*********;\n    19\t    v2 = (__int64 *)((char *)v2 + 4);\n    20\t  }\n    21\t  v5 = (CPartyPlayer *)((char *)&amp;g_PartyPlayer + 128 * (unsigned __int64)pDataa-&gt;m_id.wIndex);\n    22\t  if ( !v5-&gt;m_bLogin )\n    23\t    CPartyPlayer::EnterWorld(v5, pDataa, wZoneIndex);\n    24\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/InitTimeItemQEAA_NXZ_14030E160.c\n...\n    25\t  v9 = (unsigned __int64)&amp;v4 ^ _security_cookie;\n    26\t  lpPathName = \&quot;..\\\\ZoneServerLog\\\\SystemLog\&quot;;\n    27\t  CreateDirectoryA(\&quot;..\\\\ZoneServerLog\\\\SystemLog\&quot;, 0i64);\n    28\t  DstBuf = 0;\n    29\t  memset(&amp;v8, 0, 0xFFui64);\n    30\t  *(_DWORD *)bDate = GetKorLocalTime();\n    31\t  sprintf_s(&amp;DstBuf, 0x100ui64, \&quot;%s\\\\TimeItem_%d.log\&quot;, lpPathName);\n    32\t  CLogFile::SetWriteLogFile(&amp;v10-&gt;_kLogger, &amp;DstBuf, 1, 0, 1, 1);\n    33\t  return TimeItem::ReadGoods(v10) != 0;\n    34\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/j_LoadWorldIn...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;86e506c5-4135-438d-ba36-09cf2f4366e1;toolu_vrtx_01KaG5pz9nTHKp8v5CTaUi4x&quot;:{&quot;requestId&quot;:&quot;86e506c5-4135-438d-ba36-09cf2f4366e1&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01KaG5pz9nTHKp8v5CTaUi4x&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: Decompiled Source Code - IDA Pro/system/SetNetSystemCNetWorkingQEAA_NKPEAU_NET_TYPE_PARAMP_1404813E0.c\n...\n    50\t  memset(&amp;v14, 0, 0xFui64);\n    51\t  GetPrivateProfileStringA(\&quot;FireGuard Use\&quot;, \&quot;Use\&quot;, \&quot;TRUE\&quot;, &amp;ReturnedString, 0x10u, \&quot;.\\\\fireguard\\\\fgrs.ini\&quot;);\n    52\t  pNetwork-&gt;m_bUseFG = strcmp_0(&amp;ReturnedString, \&quot;FALSE\&quot;) != 0;\n    53\t  GetPrivateProfileStringA(\&quot;System\&quot;, \&quot;WorldName\&quot;, \&quot;X\&quot;, pNetwork-&gt;m_szServerName, 0x21u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    54\t  if ( pNetwork-&gt;m_szServerName[0] == 88 )\n    55\t  {\n    56\t    CLogFile::Write(&amp;g_FgLogFile, \&quot;FG Error : WorldName Read Failed In \\\&quot;WorldInfo.ini\\\&quot; File\&quot;);\n    57\t    result = 0;\n    58\t  }\n...\nPath: Decompiled Source Code - IDA Pro/world/LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c\n...\n    41\t  v22 = -2i64;\n    42\t  v24 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    43\t  GetPrivateProfileStringA(\&quot;System\&quot;, \&quot;WorldName\&quot;, \&quot;X\&quot;, v25-&gt;m_szWorldName, 0x21u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    44\t  if ( !strcmp_0(v25-&gt;m_szWorldName, \&quot;X\&quot;) )\n    45\t    return 0xFFFFFFFFi64;\n    46\t  M2W(v25-&gt;m_szWorldName, v25-&gt;m_wszWorldName, 0x21u);\n    47\t  v25-&gt;m_bFreeServer = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;FreeServer\&quot;, 0, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    48\t  v25-&gt;m_byWorldType = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;ServerType\&quot;, 2, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    49\t  ReturnedString = 0;\n    50\t  memset(&amp;v10, 0, 8ui64);\n    51\t  GetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ReleaseType\&quot;, \&quot;X\&quot;, &amp;ReturnedString, 9u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    52\t  if ( !_stricmp(&amp;ReturnedString, \&quot;Internal\&quot;) )\n    53\t  {\n    54\t    v25-&gt;m_bReleaseServiceMode = 0;\n    55\t  }\n    56\t  else\n    57\t  {\n    58\t    if ( _stricmp(&amp;ReturnedString, \&quot;Release\&quot;) )\n    59\t    {\n    60\t      MyMessageBox(\n    61\t        \&quot;CMainThread::LoadWorldSystemINI()\&quot;,\n    62\t        \&quot;WorldInfo.ini\\r\\n[ServerMode]\\r\\nReleaseType = %s Invalid!!\&quot;,\n    63\t        &amp;ReturnedString);\n    64\t      return 4294967294i64;\n    65\t    }\n    66\t    v25-&gt;m_bReleaseServiceMode = 1;\n    67\t  }\n    68\t  Str1 = 0;\n    69\t  memset(&amp;v12, 0, 5ui64);\n    70\t  GetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ExcuteService\&quot;, \&quot;X\&quot;, &amp;Str1, 6u, \&quot;..\\\\WorldInfo\\\\WorldInfo.ini\&quot;);\n    71\t  if ( !_stricmp(&amp;Str1, \&quot;true\&quot;) )\n    72\t  {\n    73\t    v25-&gt;m_bExcuteService = 1;\n    74\t  }\n    75\t  else\n    76\t  {\n    77\t    if ( _stricmp(&amp;Str1, \&quot;false\&quot;) )\n    78\t    {\n    79\t      MyMessageBox(\n    80\t        \&quot;CMainThread::LoadWorldSystemINI()\&quot;,\n    81\t        \&quot;WorldInfo.ini\\r\\n[ServerMode]\\r\\nExcuteService = %s Invalid!!\&quot;,\n    82\t        &amp;Str1);\n    83\t      return 4294967293i64;\n    84\t    }\n    85\t    v25-&gt;m_bExcuteService = 0;\n    86\t  }\n...\nPath: Decompiled Source Code - IDA Pro/world/LoadEventSetLootingCMonsterEventSetQEAA_NXZ_1402A91E0.c\n...\n    49\t  v32 = (unsigned __int64)&amp;v13 ^ _security_cookie;\n    50\t  v14 = 0;\n    51\t  ppszDst = &amp;Dst;\n    52\t  Str = &amp;v18;\n    53\t  v24 = &amp;v19;\n    54\t  v25 = &amp;v20;\n    55\t  v26 = &amp;v21;\n    56\t  if ( GetLastWriteFileTime(\&quot;.\\\\Initialize\\\\EventSetLooting.ini\&quot;, &amp;ftWrite) )\n    57\t  {\n    58\t    v33-&gt;m_ftLootingWrite = ftWrite;\n    59\t    memset_0(v33-&gt;m_EventSetLootingList, 0, 0x57670ui64);\n    60\t    v28 = 0;\n    61\t    if ( fopen_s(&amp;File, \&quot;.\\\\Initialize\\\\EventSetLooting.ini\&quot;, \&quot;r+t\&quot;) )\n    62\t    {\n    63\t      CLogFile::Write(\n    64\t        &amp;stru_1799C8F30,\n    65\t        \&quot;Event Set Looting INI Load Error &gt;&gt; can't open INI file : %s\&quot;,\n    66\t        \&quot;.\\\\Initialize\\\\EventSetLooting.ini\&quot;);\n    67\t      v33-&gt;m_bLoadEventLooting = 0;\n    68\t      result = 0;\n    69\t    }\n    70\t    else\n    71\t    {\n    72\t      while ( fgets(&amp;Buf, 1024, File) )\n    73\t      {\n    74\t        if ( Buf != 59 &amp;&amp; Buf != 10 )\n    75\t        {\n    76\t          v6 = strlen_0(\&quot;[Looting]\&quot;);\n    77\t          if ( !strncmp(&amp;Buf, \&quot;[Looting]\&quot;, v6) )\n    78\t          {\n    79\t            v14 = 0;\n    80\t          }\n...\n   117\t              else if ( ParsingCommandA(&amp;Buf, 5, &amp;ppszDst, 1023) == 5 )\n   118\t              {\n   119\t                Dest = v33-&gt;m_EventSetLootingList[v28].strCode;\n   120\t                strcpy_0(Dest, ppszDst);\n   121\t                v8 = atoi(Str);\n   122\t                *((_WORD *)Dest + 32) = v8;\n   123\t                v9 = atoi(v24);\n   124\t                *((_WORD *)Dest + 33) = v9;\n   125\t                *((_DWORD *)Dest + 17) = atoi(v25);\n   126\t                Dest[72] = atoi(v26);\n   127\t                ++v28;\n   128\t              }\n   129\t            }\n   130\t          }\n   131\t        }\n   132\t      }\n   133\t      fclose(File);\n   134\t      v33-&gt;m_bLoadEventLooting = 1;\n   135\t      result = 1;\n   136\t    }\n   137\t  }\n   138\t  else\n   139\t  {\n   140\t    CLogFile::Write(\n   141\t      &amp;stru_1799C8F30,\n   142\t      \&quot;Event Set Looting INI Load Error &gt;&gt; can't find INI file : %s\&quot;,\n   143\t      \&quot;.\\\\Initialize\\\\EventSetLooting.ini\&quot;);\n   144\t    v33-&gt;m_bLoadEventLooting = 0;\n   145\t    result = 0;\n   146\t  }\n   147\t  return result;\n   148\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c\n...\n    29\t  v9 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    30\t  _strdate(&amp;Buffer);\n    31\t  _strtime(&amp;v8);\n    32\t  __trace(\&quot;%s-%s: Open World\&quot;, &amp;Buffer, &amp;v8);\n    33\t  v10-&gt;m_byWorldCode = v11;\n    34\t  WriteServerStartHistory(\&quot;DBInit Begin &gt;&gt; name: %s, ip: %s\&quot;, pszDBNamea, pszDBIPa);\n    35\t  if ( CMainThread::DatabaseInit(v10, pszDBNamea, pszDBIPa) )\n    36\t  {\n    37\t    v10-&gt;m_bWorldOpen = 1;\n    38\t    CMainThread::SerivceSelfStart(v10);\n    39\t    WriteServerStartHistory(\&quot;DBInit Complete &gt;&gt;\&quot;);\n    40\t  }\n    41\t  else\n    42\t  {\n    43\t    WriteServerStartHistory(\&quot;DBInit Fail &gt;&gt;\&quot;);\n    44\t    CLogFile::WriteString(&amp;v10-&gt;m_logSystemError, \&quot;init DB fail\&quot;);\n    45\t    CWnd::SendMessageA(g_pFrame, 0x10u, 0i64, 0i64);\n    46\t  }\n    47\t}\n...\nPath: Decompiled Source Code - IDA Pro/world/CheckEventSetRespawnCMonsterEventSetQEAAXXZ_1402A8A90.c\n...\n    98\t                  v26 = CreateRepMonster(v16-&gt;pMap, 0, &amp;Dst, v3, 0i64, 0, 1, 0, 0, 0);\n    99\t                  if ( v26 )\n   100\t                  {\n   101\t                    v21-&gt;pMon = v26;\n   102\t                    v21-&gt;dwSerial = v26-&gt;m_dwObjSerial;\n   103\t                    v21-&gt;pMonFld = v16-&gt;pMonsterFld;\n   104\t                    CMonster::DisableStdItemLoot(v26);\n   105\t                    CMonster::LinkEventSet(v26, pEventSet);\n   106\t                  }\n   107\t                  else\n   108\t                  {\n   109\t                    v21-&gt;pMon = 0i64;\n   110\t                  }\n   111\t                }\n   112\t              }\n   113\t            }\n   114\t            v16-&gt;m_State.dwLastUpdateTime = v11;\n   115\t          }\n   116\t        }\n   117\t      }\n   118\t      if ( !v14 )\n   119\t      {\n   120\t        pEventSet-&gt;m_bOper = 0;\n   121\t        CLogFile::Write(&amp;stru_1799C95A8, \&quot;Stop Event Set (by during) &gt;&gt; %s\&quot;, pEventSet);\n   122\t      }\n   123\t    }\n   124\t  }\n   125\t  if ( CMonsterEventSet::IsINIFileChanged(v29, \&quot;.\\\\Initialize\\\\EventSetLooting.ini\&quot;, v29-&gt;m_ftLootingWrite)\n   126\t    &amp;&amp; !CMonsterEventSet::LoadEventSetLooting(v29) )\n   127\t  {\n   128\t    CLogFile::Write(\n   129\t      &amp;stru_1799C95A8,\n   130\t      \&quot;Reload Event set looting INI file fail &gt;&gt; %s\&quot;,\n   131\t      \&quot;.\\\\Initialize\\\\EventSetLooting.ini\&quot;);\n   132\t  }\n   133\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/Load_Event_INICGoldenBoxItemMgrQEAA_NPEAU_golden_b_140412CA0.c\n...\n    50\t    else\n    51\t    {\n    52\t      pInia-&gt;m_bUse_event = 1;\n    53\t      pInia-&gt;m_dwStarterBoxCnt = GetPrivateProfileIntA(\n    54\t                                   \&quot;GoldBox_Item\&quot;,\n    55\t                                   \&quot;StarterBox\&quot;,\n    56\t                                   1000,\n    57\t                                   \&quot;./initialize/GoldenBox_Item.ini\&quot;);\n    58\t      v17-&gt;m_wStarterBoxNum = pInia-&gt;m_dwStarterBoxCnt;\n    59\t      v6 = GetPrivateProfileStringA(\n    60\t             \&quot;GoldBox_Item\&quot;,\n    61\t             \&quot;StarterBoxCode_0\&quot;,\n    62\t             \&quot;NULL\&quot;,\n    63\t             (LPSTR)pInia-&gt;m_szStarterBoxCode,\n    64\t             0x40u,\n    65\t             \&quot;./initialize/GoldenBox_Item.ini\&quot;);\n    66\t      if ( !strcmp_0((const char *)pInia-&gt;m_szStarterBoxCode, \&quot;NULL\&quot;) )\n    67\t      {\n    68\t        pInia-&gt;m_bUse_event = 0;\n    69\t        result = 0;\n    70\t      }\n    71\t      else\n    72\t      {\n    73\t        v6 = GetPrivateProfileStringA(\n    74\t               \&quot;GoldBox_Item\&quot;,\n    75\t               \&quot;StarterBoxCode_1\&quot;,\n    76\t               \&quot;NULL\&quot;,\n    77\t               pInia-&gt;m_szStarterBoxCode[1],\n    78\t               0x40u,\n    79\t               \&quot;./initialize/GoldenBox_Item.ini\&quot;);\n    80\t        if ( !strcmp_0(pInia-&gt;m_szStarterBoxCode[1], \&quot;NULL\&quot;) )\n    81\t        {\n    82\t          pInia-&gt;m_bUse_event = 0;\n    83\t          result = 0;\n    84\t        }\n...\nPath: Decompiled Source Code - IDA Pro/items/LoadDataItemCombineMgrSA_NXZ_1402AB7C0.c\n...\n    21\t  v5 = (unsigned __int64)&amp;v3 ^ _security_cookie;\n    22\t  if ( CRecordData::ReadRecord_Ex(\n    23\t         &amp;ItemCombineMgr::ms_tbl_ItemCombine,\n    24\t         \&quot;.\\\\script\\\\CombineTable.dat\&quot;,\n    25\t         \&quot;.\\\\script\\\\CombineTable2.dat\&quot;,\n    26\t         0x3C8u,\n    27\t         &amp;szMessage) )\n    28\t  {\n    29\t    if ( CRecordData::ReadRecord(\n    30\t           &amp;ItemCombineMgr::ms_tbl_ItemCombine_Link_Stuff,\n    31\t           \&quot;.\\\\script\\\\LinkedStuff.dat\&quot;,\n    32\t           0x1944u,\n    33\t           &amp;szMessage) )\n    34\t    {\n    35\t      if ( CRecordData::ReadRecord(\n    36\t             &amp;ItemCombineMgr::ms_tbl_ItemCombine_Link_Result,\n    37\t             \&quot;.\\\\script\\\\LinkedResult.dat\&quot;,\n    38\t             0x1944u,\n    39\t             &amp;szMessage) )\n    40\t      {\n    41\t        result = 1;\n    42\t      }\n    43\t      else\n    44\t      {\n    45\t        MyMessageBox(\&quot;DatafileInit\&quot;, &amp;szMessage);\n    46\t        result = 0;\n    47\t      }\n    48\t    }\n    49\t    else\n    50\t    {\n    51\t      MyMessageBox(\&quot;DatafileInit\&quot;, &amp;szMessage);\n    52\t      result = 0;\n    53\t    }\n    54\t  }\n    55\t  else\n    56\t  {\n    57\t    MyMessageBox(\&quot;DatafileInit\&quot;, &amp;szMessage);\n    58\t    result = 0;\n    59\t  }\n    60\t  return result;\n    61\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/Load_Golden_Box_Item_EventCGoldenBoxItemMgrQEAA_NX_1404121A0.c\n...\n    21\t  CNetTimer::BeginTimer(&amp;v5-&gt;m_golden_box_event.m_event_timer, 0x1D4C0u);\n    22\t  CLogFile::Write(&amp;v5-&gt;m_golden_box_event.m_event_log, \&quot;Server Start and LogFile Loading\&quot;);\n    23\t  CGoldenBoxItemMgr::Set_Event_Status(v5, 0);\n    24\t  if ( CGoldenBoxItemMgr::Load_Event_INI(v5, &amp;v5-&gt;m_golden_box_event.m_ini) )\n    25\t  {\n    26\t    CGoldenBoxItemMgr::Check_Loaded_Event_Status(v5);\n    27\t    result = !CGoldenBoxItemMgr::Get_Event_Status(v5) || CGoldenBoxItemMgr::SetGoldBoxItemIndex(v5);\n    28\t  }\n    29\t  else\n    30\t  {\n    31\t    MyMessageBox(\&quot;CGoldenBoxItemMgr::Load_Event_INI() : \&quot;, \&quot;Load_Golden_Box_Item_Event() Fail!\&quot;);\n    32\t    result = 0;\n    33\t  }\n    34\t  return result;\n    35\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/LogCUnmannedTraderControllerIEAAXPEADZZ_1403501E0.c\n     1\t/*\n     2\t * Function: ?Log@CUnmannedTraderController@@IEAAXPEADZZ\n     3\t * Address: 0x1403501E0\n     4\t */\n     5\t\n     6\tvoid CUnmannedTraderController::Log(CUnmannedTraderController *this, char *fmt, ...)\n     7\t{\n     8\t  __int64 *v2; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v4; // [sp+0h] [bp-48h]@1\n    11\t  char *arg; // [sp+28h] [bp-20h]@5\n    12\t  CUnmannedTraderController *v6; // [sp+50h] [bp+8h]@1\n    13\t  va_list va; // [sp+60h] [bp+18h]@1\n    14\t\n    15\t  va_start(va, fmt);\n    16\t  v6 = this;\n    17\t  v2 = &amp;v4;\n    18\t  for ( i = 16i64; i; --i )\n    19\t  {\n    20\t    *(_DWORD *)v2 = -*********;\n    21\t    v2 = (__int64 *)((char *)v2 + 4);\n    22\t  }\n    23\t  if ( v6-&gt;m_pkLogger )\n    24\t  {\n    25\t    arg = (char *)va;\n    26\t    CLogFile::WriteFromArg(v6-&gt;m_pkLogger, fmt, (char *)va);\n    27\t  }\n    28\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/InitializeCGoldenBoxItemMgrQEAA_NXZ_1404120D0.c\n     1\t/*\n     2\t * Function: ?Initialize@CGoldenBoxItemMgr@@QEAA_NXZ\n     3\t * Address: 0x1404120D0\n     4\t */\n     5\t\n     6\tchar __fastcall CGoldenBoxItemMgr::Initialize(CGoldenBoxItemMgr *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  char result; // al@5\n    11\t  __int64 v4; // [sp+0h] [bp-28h]@1\n    12\t  CGoldenBoxItemMgr *v5; // [sp+30h] [bp+8h]@1\n    13\t\n    14\t  v5 = this;\n    15\t  v1 = &amp;v4;\n    16\t  for ( i = 8i64; i; --i )\n    17\t  {\n    18\t    *(_DWORD *)v1 = -*********;\n    19\t    v1 = (__int64 *)((char *)v1 + 4);\n    20\t  }\n    21\t  CGoldenBoxItemMgr::_init_loggers(v5);\n    22\t  CMyTimer::BeginTimer(&amp;v5-&gt;m_tmLoopTimer, 0x2710u);\n    23\t  if ( CGoldenBoxItemMgr::Load_Golden_Box_Item_Event(v5) )\n    24\t  {\n    25\t    GetLocalTime(&amp;v5-&gt;tm1);\n    26\t    GetLocalTime(&amp;v5-&gt;tm);\n    27\t    v5-&gt;m_bInit = 0;\n    28\t    result = 1;\n    29\t  }\n    30\t  else\n    31\t  {\n    32\t    result = 0;\n    33\t  }\n    34\t  return result;\n    35\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/InitLoggerCUnmannedTraderControllerIEAA_NXZ_14034FC50.c\n...\n    52\t  v14 = v19;\n    53\t  v22-&gt;m_pkLogger = v19;\n    54\t  if ( v22-&gt;m_pkLogger )\n    55\t  {\n    56\t    v12 = GetKorLocalTime();\n    57\t    sprintf_s&lt;128&gt;((char (*)[128])_Dest, \&quot;..\\\\ZoneServerLog\\\\Systemlog\\\\UnmannedTrader\\\\UnmannedTrader%d.log\&quot;, v12);\n    58\t    bAddCount = 1;\n    59\t    bDate = 1;\n    60\t    CLogFile::SetWriteLogFile(v22-&gt;m_pkLogger, _Dest, 1, 0, 1, 1);\n    61\t    CreateDirectoryA(\&quot;..\\\\ServiceLog\\\\UnmannedTrader\&quot;, 0i64);\n    62\t    clear_file(\&quot;..\\\\ServiceLog\\\\UnmannedTrader\&quot;, 0xFu);\n    63\t    v17 = (CLogFile *)operator new(0xB8ui64);\n    64\t    if ( v17 )\n    65\t    {\n    66\t      CLogFile::CLogFile(v17);\n    67\t      v20 = (CLogFile *)v5;\n    68\t    }\n    69\t    else\n    70\t    {\n    71\t      v20 = 0i64;\n    72\t    }\n...\nPath: Decompiled Source Code - IDA Pro/player/ct_trunk_initYA_NPEAVCPlayerZ_140291C00.c\n     1\t/*\n     2\t * Function: ?ct_trunk_init@@YA_NPEAVCPlayer@@@Z\n     3\t * Address: 0x140291C00\n     4\t */\n     5\t\n     6\tbool __fastcall ct_trunk_init(CPlayer *pOne)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  bool result; // al@5\n    11\t  __int64 v4; // [sp+0h] [bp-28h]@1\n    12\t  CPlayer *v5; // [sp+30h] [bp+8h]@1\n    13\t\n    14\t  v5 = pOne;\n    15\t  v1 = &amp;v4;\n    16\t  for ( i = 8i64; i; --i )\n    17\t  {\n    18\t    *(_DWORD *)v1 = -*********;\n    19\t    v1 = (__int64 *)((char *)v1 + 4);\n    20\t  }\n    21\t  if ( v5 )\n    22\t    result = CPlayer::mgr_TrunkInit(v5);\n    23\t  else\n    24\t    result = 0;\n    25\t  return result;\n    26\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZ_1403CEED0.c\n     1\t/*\n     2\t * Function: ?Log@CNormalGuildBattleLogger@GUILD_BATTLE@@QEAAXPEA_WZZ\n     3\t * Address: 0x1403CEED0\n     4\t */\n     5\t\n     6\tvoid GUILD_BATTLE::CNormalGuildBattleLogger::Log(GUILD_BATTLE::CNormalGuildBattleLogger *this, wchar_t *fmt, ...)\n     7\t{\n     8\t  __int64 *v2; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v4; // [sp+0h] [bp-48h]@1\n    11\t  char *arg; // [sp+28h] [bp-20h]@5\n    12\t  GUILD_BATTLE::CNormalGuildBattleLogger *v6; // [sp+50h] [bp+8h]@1\n    13\t  va_list va; // [sp+60h] [bp+18h]@1\n    14\t\n    15\t  va_start(va, fmt);\n    16\t  v6 = this;\n    17\t  v2 = &amp;v4;\n    18\t  for ( i = 16i64; i; --i )\n    19\t  {\n    20\t    *(_DWORD *)v2 = -*********;\n    21\t    v2 = (__int64 *)((char *)v2 + 4);\n    22\t  }\n    23\t  if ( v6-&gt;m_pkLogger )\n    24\t  {\n    25\t    arg = (char *)va;\n    26\t    CLogFile::WriteFromArg(v6-&gt;m_pkLogger, fmt, (char *)va);\n    27\t  }\n    28\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/InitializeCashItemRemoteStoreQEAA_NXZ_1402F4EF0.c\n...\n     5\t\n     6\tbool __fastcall CashItemRemoteStore::Initialize(CashItemRemoteStore *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  bool result; // al@5\n    11\t  __int64 v4; // [sp+0h] [bp-28h]@1\n    12\t  CashItemRemoteStore *v5; // [sp+30h] [bp+8h]@1\n    13\t\n    14\t  v5 = this;\n    15\t  v1 = &amp;v4;\n    16\t  for ( i = 8i64; i; --i )\n    17\t  {\n    18\t    *(_DWORD *)v1 = -*********;\n    19\t    v1 = (__int64 *)((char *)v1 + 4);\n    20\t  }\n    21\t  if ( CashItemRemoteStore::_InitLoggers(v5) )\n    22\t  {\n    23\t    if ( CashItemRemoteStore::_ReadGoods(v5) )\n    24\t    {\n    25\t      CMyTimer::BeginTimer(&amp;v5-&gt;m_TotalEventTimer, 0x3E8u);\n    26\t      CashItemRemoteStore::Load_Conditional_Event(v5);\n    27\t      CashItemRemoteStore::load_cash_discount_event(v5);\n    28\t      CashItemRemoteStore::Load_Cash_Event(v5);\n    29\t      result = CashItemRemoteStore::LoadBuyCashMode(v5) != 0;\n    30\t    }\n    31\t    else\n    32\t    {\n    33\t      result = 0;\n    34\t    }\n    35\t  }\n    36\t  else\n    37\t  {\n    38\t    result = 0;\n    39\t  }\n    40\t  return result;\n    41\t}\n...\nPath: Decompiled Source Code - IDA Pro/items/InitialzieTRC_AutoTradeQEAA_NXZ_1402D7E00.c\n     1\t/*\n     2\t * Function: ?Initialzie@TRC_AutoTrade@@QEAA_NXZ\n     3\t * Address: 0x1402D7E00\n     4\t */\n     5\t\n     6\tchar __fastcall TRC_AutoTrade::Initialzie(TRC_AutoTrade *this)\n     7\t{\n     8\t  __int64 *v1; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  unsigned int v3; // eax@4\n    11\t  unsigned int v4; // eax@4\n    12\t  __int64 v6; // [sp+0h] [bp-E8h]@1\n    13\t  char Dest; // [sp+40h] [bp-A8h]@4\n    14\t  char v8; // [sp+41h] [bp-A7h]@4\n    15\t  unsigned __int64 v9; // [sp+D0h] [bp-18h]@4\n    16\t  TRC_AutoTrade *v10; // [sp+F0h] [bp+8h]@1\n    17\t\n    18\t  v10 = this;\n    19\t  v1 = &amp;v6;\n    20\t  for ( i = 56i64; i; --i )\n    21\t  {\n    22\t    *(_DWORD *)v1 = -*********;\n    23\t    v1 = (__int64 *)((char *)v1 + 4);\n    24\t  }\n...\nPath: Decompiled Source Code - IDA Pro/combat/LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZZ_1403CEB50.c\n     1\t/*\n     2\t * Function: ?Log@CGuildBattleLogger@GUILD_BATTLE@@QEAAXPEA_WZZ\n     3\t * Address: 0x1403CEB50\n     4\t */\n     5\t\n     6\tvoid GUILD_BATTLE::CGuildBattleLogger::Log(GUILD_BATTLE::CGuildBattleLogger *this, wchar_t *fmt, ...)\n     7\t{\n     8\t  __int64 *v2; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v4; // [sp+0h] [bp-48h]@1\n    11\t  char *arg; // [sp+28h] [bp-20h]@5\n    12\t  GUILD_BATTLE::CGuildBattleLogger *v6; // [sp+50h] [bp+8h]@1\n    13\t  va_list va; // [sp+60h] [bp+18h]@1\n    14\t\n    15\t  va_start(va, fmt);\n    16\t  v6 = this;\n    17\t  v2 = &amp;v4;\n    18\t  for ( i = 16i64; i; --i )\n    19\t  {\n    20\t    *(_DWORD *)v2 = -*********;\n    21\t    v2 = (__int64 *)((char *)v2 + 4);\n    22\t  }\n    23\t  if ( v6-&gt;m_pkLogger )\n    24\t  {\n    25\t    arg = (char *)va;\n    26\t    CLogFile::WriteFromArg(v6-&gt;m_pkLogger, fmt, (char *)va);\n    27\t  }\n    28\t}\n...\nPath: Decompiled Source Code - IDA Pro/combat/LoadCGuildBattleControllerQEAA_NXZ_1403D5950.c\n...\n    33\t  v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();\n    34\t  iCurDay = GetCurDay();\n    35\t  if ( iCurDay &gt;= 0 )\n    36\t  {\n    37\t    uiMapCnt = 0;\n    38\t    iToday = 0;\n    39\t    iTodayDayID = 0;\n    40\t    v15 = 0;\n    41\t    v16 = 1;\n    42\t    if ( !CGuildBattleController::LoadINI(v17, &amp;uiMapCnt, &amp;iToday, &amp;iTodayDayID, &amp;v15, &amp;v16) )\n    43\t      GUILD_BATTLE::CGuildBattleLogger::Log(v10, \&quot;CGuildBattleController::Load(%d) LoadINI() Fail!\&quot;);\n    44\t    v4 = GUILD_BATTLE::CGuildBattleRankManager::Instance();\n    45\t    if ( GUILD_BATTLE::CGuildBattleRankManager::Load(v4) )\n    46\t    {\n    47\t      v5 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();\n    48\t      if ( GUILD_BATTLE::CGuildBattleScheduleManager::Load(v5, iCurDay, uiMapCnt, iToday, iTodayDayID, v15, v16) )\n    49\t      {\n    50\t        v6 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();\n...\nPath: Decompiled Source Code - IDA Pro/items/InitCUnmannedTraderControllerQEAA_NXZ_14034CBE0.c\n...\n    25\t  if ( CUnmannedTraderController::InitLogger(v9) )\n    26\t  {\n    27\t    v4 = CUnmannedTraderUserInfoTable::Instance();\n    2...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;708e822b-c3b7-459d-8cae-88af11d17836;toolu_vrtx_01BfMfHuj83Mnt1tCbW91KxT&quot;:{&quot;requestId&quot;:&quot;708e822b-c3b7-459d-8cae-88af11d17836&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01BfMfHuj83Mnt1tCbW91KxT&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in Decompiled Source Code - IDA Pro, excluding hidden items:\nDecompiled Source Code - IDA Pro/authentication\nDecompiled Source Code - IDA Pro/authentication\\0CAsyncLogInfoQEAAXZ_1403BC9F0.c\nDecompiled Source Code - IDA Pro/authentication\\0CBHPEAVCAsyncLogInfopairHPEAVCAsyncLogInfostdQEAA_1403C7590.c\nDecompiled Source Code - IDA Pro/authentication\\0MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140465820.c\nDecompiled Source Code - IDA Pro/authentication\\0UpairCBHPEAVCAsyncLogInfostdallocatorPEAU_Node_Li_1403C7E70.c\nDecompiled Source Code - IDA Pro/authentication\\0UpairCBHPEAVCAsyncLogInfostdallocatorU_Node_List__1403C7FF0.c\nDecompiled Source Code - IDA Pro/authentication\\0UpairCBHPEAVCAsyncLogInfostdallocatorV_Iterator0A_1403C7670.c\nDecompiled Source Code - IDA Pro/authentication\\0W4ASYNC_LOG_TYPEPEAVCAsyncLogInfopairCBHPEAVCAsyn_1403C7630.c\nDecompiled Source Code - IDA Pro/authentication\\0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C1560.c\nDecompiled Source Code - IDA Pro/authentication\\0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C5E20.c\nDecompiled Source Code - IDA Pro/authentication\\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C1480.c\nDecompiled Source Code - IDA Pro/authentication\\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C5B90.c\nDecompiled Source Code - IDA Pro/authentication\\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C6D10.c\nDecompiled Source Code - IDA Pro/authentication\\0_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compare_1403C2EC0.c\nDecompiled Source Code - IDA Pro/authentication\\0_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUless_1403C4520.c\nDecompiled Source Code - IDA Pro/authentication\\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2D20.c\nDecompiled Source Code - IDA Pro/authentication\\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C42C0.c\nDecompiled Source Code - IDA Pro/authentication\\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C5B30.c\nDecompiled Source Code - IDA Pro/authentication\\0_List_nodUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C7520.c\nDecompiled Source Code - IDA Pro/authentication\\0_List_ptrUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C6F70.c\nDecompiled Source Code - IDA Pro/authentication\\0_List_valUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C66F0.c\nDecompiled Source Code - IDA Pro/authentication\\0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C5EE0.c\nDecompiled Source Code - IDA Pro/authentication\\0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C74D0.c\nDecompiled Source Code - IDA Pro/authentication\\0_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C5E70.c\nDecompiled Source Code - IDA Pro/authentication\\0_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C73C0.c\nDecompiled Source Code - IDA Pro/authentication\\0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C5DC0.c\nDecompiled Source Code - IDA Pro/authentication\\0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6D70.c\nDecompiled Source Code - IDA Pro/authentication\\0_Vector_valV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C6BE0.c\nDecompiled Source Code - IDA Pro/authentication\\0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAAEBV0_1403C5F40.c\nDecompiled Source Code - IDA Pro/authentication\\0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAXZ_1403C35F0.c\nDecompiled Source Code - IDA Pro/authentication\\0allocatorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C6C50.c\nDecompiled Source Code - IDA Pro/authentication\\0hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHstd_1403C17E0.c\nDecompiled Source Code - IDA Pro/authentication\\0listUpairCBHPEAVCAsyncLogInfostdVallocatorUpairCB_1403C45B0.c\nDecompiled Source Code - IDA Pro/authentication\\0pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdV_1403C43F0.c\nDecompiled Source Code - IDA Pro/authentication\\0pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdQEAAAEBW4_1403C8010.c\nDecompiled Source Code - IDA Pro/authentication\\0vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C4EF0.c\nDecompiled Source Code - IDA Pro/authentication\\1CAsyncLogInfoQEAAXZ_1403BCA80.c\nDecompiled Source Code - IDA Pro/authentication\\1MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140464F50.c\nDecompiled Source Code - IDA Pro/authentication\\1_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C1230.c\nDecompiled Source Code - IDA Pro/authentication\\1_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C11F0.c\nDecompiled Source Code - IDA Pro/authentication\\1_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compare_1403C1860.c\nDecompiled Source Code - IDA Pro/authentication\\1_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C11B0.c\nDecompiled Source Code - IDA Pro/authentication\\1_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C44E0.c\nDecompiled Source Code - IDA Pro/authentication\\1_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C44A0.c\nDecompiled Source Code - IDA Pro/authentication\\1_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C4460.c\nDecompiled Source Code - IDA Pro/authentication\\1hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHstd_1403C1170.c\nDecompiled Source Code - IDA Pro/authentication\\1listUpairCBHPEAVCAsyncLogInfostdVallocatorUpairCB_1403C3630.c\nDecompiled Source Code - IDA Pro/authentication\\1pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdV_1403C1670.c\nDecompiled Source Code - IDA Pro/authentication\\1vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C3EE0.c\nDecompiled Source Code - IDA Pro/authentication\\4_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C2DF0.c\nDecompiled Source Code - IDA Pro/authentication\\4_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2D80.c\nDecompiled Source Code - IDA Pro/authentication\\4_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2CC0.c\nDecompiled Source Code - IDA Pro/authentication\\8UpairCBHPEAVCAsyncLogInfostdU01stdYA_NAEBVallocat_1403C7690.c\nDecompiled Source Code - IDA Pro/authentication\\8_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400A6CA0.c\nDecompiled Source Code - IDA Pro/authentication\\8_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2BE0.c\nDecompiled Source Code - IDA Pro/authentication\\8_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C7460.c\nDecompiled Source Code - IDA Pro/authentication\\9_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400CFE90.c\nDecompiled Source Code - IDA Pro/authentication\\9_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2C50.c\nDecompiled Source Code - IDA Pro/authentication\\9_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C6E30.c\nDecompiled Source Code - IDA Pro/authentication\\AccountServerLoginCMainThreadQEAAXXZ_1401F8140.c\nDecompiled Source Code - IDA Pro/authentication\\AuthLastCriTicketMiningTicketQEAAHGEEEEZ_1400D01D0.c\nDecompiled Source Code - IDA Pro/authentication\\AuthLastMentalTicketMiningTicketQEAAHGEEEEZ_1400CFDB0.c\nDecompiled Source Code - IDA Pro/authentication\\AuthMiningTicketCHolyStoneSystemQEAA_NIZ_14027DBD0.c\nDecompiled Source Code - IDA Pro/authentication\\AvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C4290.c\nDecompiled Source Code - IDA Pro/authentication\\CN_InvalidateNatureYAXXZ_140504ED0.c\nDecompiled Source Code - IDA Pro/authentication\\C_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2AF0.c\nDecompiled Source Code - IDA Pro/authentication\\CallFunc_RFOnline_AuthCEnglandBillingMgrQEAAHAEAU__1403198F0.c\nDecompiled Source Code - IDA Pro/authentication\\CallFunc_RFOnline_AuthCRusiaBillingMgrQEAAHAEAU_pa_1403213A0.c\nDecompiled Source Code - IDA Pro/authentication\\CallProc_RFOnlineAuthCRFCashItemDatabaseQEAAHAEAU__140482430.c\nDecompiled Source Code - IDA Pro/authentication\\CallProc_RFOnlineAuth_JapCRFCashItemDatabaseQEAAHA_1404836A0.c\nDecompiled Source Code - IDA Pro/authentication\\CompleteLogInCompeteCUnmannedTraderControllerQEAAX_14034EF80.c\nDecompiled Source Code - IDA Pro/authentication\\D3D_R3InvalidateDeviceYAJXZ_14050B040.c\nDecompiled Source Code - IDA Pro/authentication\\D_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2B30.c\nDecompiled Source Code - IDA Pro/authentication\\D_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C4310.c\nDecompiled Source Code - IDA Pro/authentication\\E_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2B80.c\nDecompiled Source Code - IDA Pro/authentication\\E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C4350.c\nDecompiled Source Code - IDA Pro/authentication\\E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C7270.c\nDecompiled Source Code - IDA Pro/authentication\\F_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C5BF0.c\nDecompiled Source Code - IDA Pro/authentication\\F_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C43A0.c\nDecompiled Source Code - IDA Pro/authentication\\GenerateEphemeralKeyPairAuthenticatedKeyAgreementD_1405F6600.c\nDecompiled Source Code - IDA Pro/authentication\\GenerateStaticKeyPairAuthenticatedKeyAgreementDoma_1405F65A0.c\nDecompiled Source Code - IDA Pro/authentication\\GetCountCAsyncLogInfoQEAAKXZ_1403C16B0.c\nDecompiled Source Code - IDA Pro/authentication\\GetDirPathCAsyncLogInfoQEAAPEBDXZ_1403C1630.c\nDecompiled Source Code - IDA Pro/authentication\\GetFileNameCAsyncLogInfoQEAAPEBDXZ_1403C16D0.c\nDecompiled Source Code - IDA Pro/authentication\\GetOccDialogInfoCDialogMEAAPEAU_AFX_OCC_DIALOG_INF_1404DBD48.c\nDecompiled Source Code - IDA Pro/authentication\\GetOccDialogInfoCFormViewMEAAPEAU_AFX_OCC_DIALOG_I_1404DC15C.c\nDecompiled Source Code - IDA Pro/authentication\\GetOccDialogInfoCWndMEAAPEAU_AFX_OCC_DIALOG_INFOXZ_1404DBE20.c\nDecompiled Source Code - IDA Pro/authentication\\GetTypeNameCAsyncLogInfoQEAAPEBDXZ_1403C1650.c\nDecompiled Source Code - IDA Pro/authentication\\H_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C5C70.c\nDecompiled Source Code - IDA Pro/authentication\\IncreaseCountCAsyncLogInfoQEAAXXZ_1403C16F0.c\nDecompiled Source Code - IDA Pro/authentication\\InitCAsyncLogInfoQEAA_NW4ASYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c\nDecompiled Source Code - IDA Pro/authentication\\Init_AuthKeyTicketMiningTicketQEAAXXZ_140073BC0.c\nDecompiled Source Code - IDA Pro/authentication\\InvalidateDeviceObjectsCR3FontQEAAJXZ_140528820.c\nDecompiled Source Code - IDA Pro/authentication\\InvalidateSkySkyQEAAXXZ_1405229B0.c\nDecompiled Source Code - IDA Pro/authentication\\InvalidateSunSunQEAAXXZ_1405221E0.c\nDecompiled Source Code - IDA Pro/authentication\\IsLogInStateCUnmannedTraderUserInfoQEAA_NXZ_140366F20.c\nDecompiled Source Code - IDA Pro/authentication\\LogInCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_1403E4050.c\nDecompiled Source Code - IDA Pro/authentication\\LogInCNormalGuildBattleGuildGUILD_BATTLEQEAAXHKEPE_1403E0DD0.c\nDecompiled Source Code - IDA Pro/authentication\\LogInCNormalGuildBattleManagerGUILD_BATTLEQEAAXHKK_1403D4360.c\nDecompiled Source Code - IDA Pro/authentication\\LogInControllServerCNetworkEXAEAA_NHPEADZ_1401C7250.c\nDecompiled Source Code - IDA Pro/authentication\\LogInWebAgentServerCNetworkEXAEAA_NHPEADZ_1401DA860.c\nDecompiled Source Code - IDA Pro/authentication\\LoginCBillingIDUEAAXPEAVCUserDBZ_14028E0F0.c\nDecompiled Source Code - IDA Pro/authentication\\LoginCBillingJPUEAAXPEAVCUserDBZ_14028E910.c\nDecompiled Source Code - IDA Pro/authentication\\LoginCBillingManagerQEAAXPEAVCUserDBZ_140079030.c\nDecompiled Source Code - IDA Pro/authentication\\LoginCBillingNULLUEAAXPEAVCUserDBZ_14028DBD0.c\nDecompiled Source Code - IDA Pro/authentication\\LoginCBillingUEAAXPEAVCUserDBZ_14028CAC0.c\nDecompiled Source Code - IDA Pro/authentication\\LoginCNormalGuildBattleGuildMemberGUILD_BATTLEQEAA_1403DFA80.c\nDecompiled Source Code - IDA Pro/authentication\\NotifyLogInSetBuffCRaceBuffInfoByHolyQuestAEAAXGZ_1403B42D0.c\nDecompiled Source Code - IDA Pro/authentication\\OnCheckSession_FirstVerifyCHackShieldExSystemUEAA__140417250.c\nDecompiled Source Code - IDA Pro/authentication\\OnCheckSession_FirstVerifyCNationSettingManagerQEA_140229470.c\nDecompiled Source Code - IDA Pro/authentication\\OnCheckSession_FirstVerifyHACKSHEILD_PARAM_ANTICPU_140417960.c\nDecompiled Source Code - IDA Pro/authentication\\OnConnectSessionCHackShieldExSystemUEAAXHZ_1404170D0.c\nDecompiled Source Code - IDA Pro/authentication\\OnConnectSessionCNationSettingManagerQEAAXHZ_140229400.c\nDecompiled Source Code - IDA Pro/authentication\\OnDisConnectSessionCHackShieldExSystemUEAAXHZ_140417140.c\nDecompiled Source Code - IDA Pro/authentication\\OnDisConnectSessionCNationSettingManagerQEAAXHZ_1402294F0.c\nDecompiled Source Code - IDA Pro/authentication\\OnLoopSessionCHackShieldExSystemUEAAXHZ_1404171A0.c\nDecompiled Source Code - IDA Pro/authentication\\OnRecvSessionHACKSHEILD_PARAM_ANTICPUEAA_NPEAVCHac_140417F10.c\nDecompiled Source Code - IDA Pro/authentication\\OnRecvSession_ClientCheckSum_ResponseHACKSHEILD_PA_140418120.c\nDecompiled Source Code - IDA Pro/authentication\\OnRecvSession_ClientCrc_ResponseHACKSHEILD_PARAM_A_140418290.c\nDecompiled Source Code - IDA Pro/authentication\\OnRecvSession_ServerCheckSum_RequestHACKSHEILD_PAR_140417FB0.c\nDecompiled Source Code - IDA Pro/authentication\\R3InvalidateDeviceYAJXZ_1404E9FC0.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_CurAllUserLoginCBillingIEAAXXZ_14028D610.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_GuildMemberLoginCGuildQEAAXKGGZ_1402570F0.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_LoginCBillingIDMEAA_NPEAD00FPEAU_SYSTEMTIM_14028E600.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_LoginCBillingJPMEAA_NPEAD00FPEAU_SYSTEMTIM_14028ECC0.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_LoginCBillingMEAA_NPEAD00FPEAU_SYSTEMTIMEJ_14028D3C0.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_LoginCBillingNULLMEAA_NPEAD00FPEAU_SYSTEMT_14028DC10.c\nDecompiled Source Code - IDA Pro/authentication\\SendMsg_UserLogInNotifyTaxRateTRC_AutoTradeQEAAXHZ_1402D8540.c\nDecompiled Source Code - IDA Pro/authentication\\SetOccDialogInfoCDialogMEAAHPEAU_AFX_OCC_DIALOG_IN_1404DBD42.c\nDecompiled Source Code - IDA Pro/authentication\\SetOccDialogInfoCFormViewMEAAHPEAU_AFX_OCC_DIALOG__1404DC156.c\nDecompiled Source Code - IDA Pro/authentication\\SetOccDialogInfoCWndMEAAHPEAU_AFX_OCC_DIALOG_INFOZ_1404DBE1A.c\nDecompiled Source Code - IDA Pro/authentication\\Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ_1400A6BA0.c\nDecompiled Source Code - IDA Pro/authentication\\Set_AuthKeyTicketMiningTicketQEAAXIZ_140078ED0.c\nDecompiled Source Code - IDA Pro/authentication\\UpdateLogFileNameCAsyncLogInfoQEAAXXZ_1403BD0F0.c\nDecompiled Source Code - IDA Pro/authentication\\UpdateLogInCompleteCUnmannedTraderControllerQEAAEP_14034E440.c\nDecompiled Source Code - IDA Pro/authentication\\Update_TrunkPasswordCUserDBQEAA_NPEADZ_140116FD0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAD0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAF0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADD90.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046A900.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046A920.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046AD80.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersVIntegerCryptoPPCryptoPP_140551AC0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_GroupParametersVIntegerCryptoPPCryptoPP_1405AD4F0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PrivateKeyImplVDL_GroupParameters_DSACr_140568460.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_1404515F0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_140558CA0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PrivateKeyImplVDL_GroupParameters_GFPCr_140635EE0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PrivateKeyImplVDL_GroupParameters_GFP_D_140637C30.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PublicKeyImplVDL_GroupParameters_DSACry_140568F30.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PublicKeyImplVDL_GroupParameters_ECVEC2_140558420.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PublicKeyImplVDL_GroupParameters_ECVECP_140450870.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PublicKeyImplVDL_GroupParameters_GFPCry_1406369F0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateDL_PublicKeyImplVDL_GroupParameters_GFP_De_1406373A0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateElementDL_GroupParameters_ECVEC2NCryptoPPC_140583CF0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateElementDL_GroupParameters_ECVECPCryptoPPCr_14057FB10.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateElementDL_GroupParameters_IntegerBasedCryp_140630AE0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateGroupDL_GroupParameters_DSACryptoPPUEBA_NA_140630230.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateGroupDL_GroupParameters_ECVEC2NCryptoPPCry_1405835A0.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateGroupDL_GroupParameters_ECVECPCryptoPPCryp_14057F300.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateGroupDL_GroupParameters_IntegerBasedCrypto_140630680.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateParametersEC2NCryptoPPQEBA_NAEAVRandomNumb_14062E210.c\nDecompiled Source Code - IDA Pro/authentication\\ValidateParametersECPCryptoPPQEBA_NAEAVRandomNumbe_14060E2A0.c\nDecompiled Source Code - IDA Pro/authentication\\Y_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C7420.c\nDecompiled Source Code - IDA Pro/authentication\\Y_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6DD0.c\nDecompiled Source Code - IDA Pro/authentication\\_AllocateU_Node_List_nodUpairCBHPEAVCAsyncLogInfos_1403C7E90.c\nDecompiled Source Code - IDA Pro/authentication\\_AllocateV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C7D00.c\nDecompiled Source Code - IDA Pro/authentication\\_BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C6370.c\nDecompiled Source Code - IDA Pro/authentication\\_BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C6500.c\nDecompiled Source Code - IDA Pro/authentication\\_BuyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C7100.c\nDecompiled Source Code - IDA Pro/authentication\\_CAsyncLogInfoInit__1_dtor0_1403BD0C0.c\nDecompiled Source Code - IDA Pro/authentication\\_CAsyncLogInfo_CAsyncLogInfo__1_dtor0_1403BCB50.c\nDecompiled Source Code - IDA Pro/authentication\\_CEnglandBillingMgrCallFunc_RFOnline_Auth__1_dtor0_140319C50.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitInfoListLogIn__1_dtor0_1403A5C90.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitInfoListLogIn__1_dtor1_1403A5CC0.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitInfoListLogIn__1_dtor2_1403A5CF0.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitInfoListLogIn__1_dtor3_1403A5D20.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitRightInfoLogIn__1_dtor0_1403AD090.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitRightInfoLogIn__1_dtor1_1403AD0C0.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitRightInfoLogIn__1_dtor2_1403AD0F0.c\nDecompiled Source Code - IDA Pro/authentication\\_CMoveMapLimitRightInfoLogIn__1_dtor3_1403AD120.c\nDecompiled Source Code - IDA Pro/authentication\\_ConstructPEAU_Node_List_nodUpairCBHPEAVCAsyncLogI_1403C7F50.c\nDecompiled Source Code - IDA Pro/authentication\\_ConstructUpairCBHPEAVCAsyncLogInfostdU12stdYAXPEA_1403C7DB0.c\nDecompiled Source Code - IDA Pro/authentication\\_ConstructV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C8B70.c\nDecompiled Source Code - IDA Pro/authentication\\_Construct_nvectorV_Iterator0AlistUpairCBHPEAVCAsy_1403C6820.c\nDecompiled Source Code - IDA Pro/authentication\\_Copy_backward_optPEAV_Iterator0AlistUpairCBHPEAVC_1403C8AD0.c\nDecompiled Source Code - IDA Pro/authentication\\_Copy_optPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C8530.c\nDecompiled Source Code - IDA Pro/authentication\\_CryptoPPDL_PrivateKeyImpl_CryptoPPDL_GroupParamet_140451850.c\nDecompiled Source Code - IDA Pro/authentication\\_DestroyPEAU_Node_List_nodUpairCBHPEAVCAsyncLogInf_1403C7BC0.c\nDecompiled Source Code - IDA Pro/authentication\\_DestroyU_Node_List_nodUpairCBHPEAVCAsyncLogInfost_1403C7F40.c\nDecompiled Source Code - IDA Pro/authentication\\_DestroyV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1403C8C60.c\nDecompiled Source Code - IDA Pro/authentication\\_Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C7BD0.c\nDecompiled Source Code - IDA Pro/authentication\\_Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C8810.c\nDecompiled Source Code - IDA Pro/authentication\\_DestroyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C69F0.c\nDecompiled Source Code - IDA Pro/authentication\\_EMessageAuthenticationCodeImplVHMAC_BaseCryptoPPV_140465900.c\nDecompiled Source Code - IDA Pro/authentication\\_EMessageAuthenticationCodeImplVHMAC_BaseCryptoPPV_14046AC80.c\nDecompiled Source Code - IDA Pro/authentication\\_FillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1403C8680.c\nDecompiled Source Code - IDA Pro/authentication\\_GCAsyncLogInfoQEAAPEAXIZ_1403C14F0.c\nDecompiled Source Code - IDA Pro/authentication\\_G_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVallo_1403C8CB0.c\nDecompiled Source Code - IDA Pro/authentication\\_Get_iter_from_vec_HashV_Hmap_traitsHPEAVCAsyncLog_1403C2E50.c\nDecompiled Source Code - IDA Pro/authentication\\_Hashval_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash__1403C3530.c\nDecompiled Source Code - IDA Pro/authentication\\_IncsizelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C4D90.c\nDecompiled Source Code - IDA Pro/authentication\\_InsertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C80C0.c\nDecompiled Source Code - IDA Pro/authentication\\_Insert_nvectorV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C5320.c\nDecompiled Source Code - IDA Pro/authentication\\_InsertlistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1403C4670.c\nDecompiled Source Code - IDA Pro/authentication\\_Iter_catV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C8060.c\nDecompiled Source Code - IDA Pro/authentication\\_Iter_randomPEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8470.c\nDecompiled Source Code - IDA Pro/authentication\\_Kfn_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUl_1403C35E0.c\nDecompiled Source Code - IDA Pro/authentication\\_Move_backward_optPEAV_Iterator0AlistUpairCBHPEAVC_1403C8760.c\nDecompiled Source Code - IDA Pro/authentication\\_Move_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C8700.c\nDecompiled Source Code - IDA Pro/authentication\\_Mynode_Const_iterator0AlistUpairCBHPEAVCAsyncLogI_1403C5C50.c\nDecompiled Source Code - IDA Pro/authentication\\_MyvallistUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C3610.c\nDecompiled Source Code - IDA Pro/authentication\\_NextnodelistUpairCBHPEAVCAsyncLogInfostdVallocato_1403C3600.c\nDecompiled Source Code - IDA Pro/authentication\\_PrevnodelistUpairCBHPEAVCAsyncLogInfostdVallocato_1403C4590.c\nDecompiled Source Code - IDA Pro/authentication\\_Ptr_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C84D0.c\nDecompiled Source Code - IDA Pro/authentication\\_SplicelistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1403C38D0.c\nDecompiled Source Code - IDA Pro/authentication\\_TidylistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C4CC0.c\nDecompiled Source Code - IDA Pro/authentication\\_TidyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C5240.c\nDecompiled Source Code - IDA Pro/authentication\\_UfillvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C6A60.c\nDecompiled Source Code - IDA Pro/authentication\\_UmovePEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C7A30.c\nDecompiled Source Code - IDA Pro/authentication\\_Unchecked_move_backwardPEAV_Iterator0AlistUpairCB_1403C7B00.c\nDecompiled Source Code - IDA Pro/authentication\\_Unchecked_uninitialized_movePEAV_Iterator0AlistUp_1403C85D0.c\nDecompiled Source Code - IDA Pro/authentication\\_Uninit_copyPEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8DD0.c\nDecompiled Source Code - IDA Pro/authentication\\_Uninit_fill_nPEAV_Iterator0AlistUpairCBHPEAVCAsyn_1403C8890.c\nDecompiled Source Code - IDA Pro/authentication\\_Uninit_movePEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8A60.c\nDecompiled Source Code - IDA Pro/authentication\\_ValidateImageBase_1404DE4C0.c\nDecompiled Source Code - IDA Pro/authentication\\_XlenvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C6AF0.c\nDecompiled Source Code - IDA Pro/authentication\\_std_Construct_stdlist_stdpair_int_const__CAsyncLo_1403C8C00.c\nDecompiled Source Code - IDA Pro/authentication\\_std_Uninit_copy_stdlist_stdpair_int_const__CAsync_1403C8E60.c\nDecompiled Source Code - IDA Pro/authentication\\_std_Uninit_fill_n_stdlist_stdpair_int_const__CAsy_1403C8920.c\nDecompiled Source Code - IDA Pro/authentication\\_std_Vector_iterator_stdlist_stdpair_int_const__CA_1403C5D10.c\nDecompiled Source Code - IDA Pro/authentication\\_std_Vector_iterator_stdlist_stdpair_int_const__CA_1403C5D40.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C18C0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2480.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C24B0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C24E0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2510.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2540.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2570.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C25A0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C25D0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2600.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2630.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2660.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2690.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C26C0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2700.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2FA0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2FD0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3000.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3370.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C33A0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C33D0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3410.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3820.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3850.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3C90.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3CC0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3CF0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D20.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D50.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D80.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4730.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4A70.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4AA0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4AD0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B10.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B50.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B90.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4E80.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6130.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6160.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6190.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6440.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6600.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7310.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7340.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7800.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7830.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7860.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7890.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C78C0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8200.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8230.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8260.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8290.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C82C0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8390.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C40F0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C4120.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C4150.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5150.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5180.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C51B0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5860.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5890.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C58C0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5920.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C68B0.c\nDecompiled Source Code - IDA Pro/authentication\\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C6B80.c\nDecompiled Source Code - IDA Pro/authentication\\allocateallocatorU_Node_List_nodUpairCBHPEAVCAsync_1403C7000.c\nDecompiled Source Code - IDA Pro/authentication\\allocateallocatorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6CC0.c\nDecompiled Source Code - IDA Pro/authentication\\auto_trade_login_sellCMgrAvatorItemHistoryQEAAXPEB_14023A3E0.c\nDecompiled Source Code - IDA Pro/authentication\\begin_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_com_1403C1910.c\nDecompiled Source Code - IDA Pro/authentication\\beginlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C3670.c\nDecompiled Source Code - IDA Pro/authentication\\beginvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C4F90.c\nDecompiled Source Code - IDA Pro/authentication\\capacityvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C6920.c\nDecompiled Source Code - IDA Pro/authentication\\clearlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C6250.c\nDecompiled Source Code - IDA Pro/authentication\\constructallocatorPEAU_Node_List_nodUpairCBHPEAVCA_1403C70A0.c\nDecompiled Source Code - IDA Pro/authentication\\constructallocatorUpairCBHPEAVCAsyncLogInfostdstdQ_1403C6EA0.c\nDecompiled Source Code - IDA Pro/authentication\\constructallocatorV_Iterator0AlistUpairCBHPEAVCAsy_1403C89B0.c\nDecompiled Source Code - IDA Pro/authentication\\deallocateallocatorU_Node_List_nodUpairCBHPEAVCAsy_1403C6780.c\nDecompiled Source Code - IDA Pro/authentication\\deallocateallocatorV_Iterator0AlistUpairCBHPEAVCAs_1403C6C70.c\nDecompiled Source Code - IDA Pro/authentication\\destroyallocatorPEAU_Node_List_nodUpairCBHPEAVCAsy_1403C67D0.c\nDecompiled Source Code - IDA Pro/authentication\\destroyallocatorU_Node_List_nodUpairCBHPEAVCAsyncL_1403C7050.c\nDecompiled Source Code - IDA Pro/authentication\\destroyallocatorV_Iterator0AlistUpairCBHPEAVCAsync_1403C8A10.c\nDecompiled Source Code - IDA Pro/authentication\\dtor00__F_afxSessionMapYAXXZ4HA_0_14057B120.c\n... additional lines truncated ...\nDecompiled Source Code - IDA Pro/world\\j__BossDieWriteLog_StartCMonsterQEAAXEPEAVCGameObj_140004D40.c\nDecompiled Source Code - IDA Pro/world\\j__Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140001E65.c\nDecompiled Source Code - IDA Pro/world\\j__Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140004FA7.c\nDecompiled Source Code - IDA Pro/world\\j__BuyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_140012251.c\nDecompiled Source Code - IDA Pro/world\\j__BuyvectorPEAVCMoveMapLimitRightVallocatorPEAVCM_14000AD8F.c\nDecompiled Source Code - IDA Pro/world\\j__BuyvectorVCMoveMapLimitRightInfoVallocatorVCMov_14000A38A.c\nDecompiled Source Code - IDA Pro/world\\j__CheckDestMonsterLimitLvYA_NHHEZ_140011400.c\nDecompiled Source Code - IDA Pro/world\\j__Color_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140002B85.c\nDecompiled Source Code - IDA Pro/world\\j__ConstructPEAU_Node_Tree_nodV_Tmap_traitsVbasic__14000A7B3.c\nDecompiled Source Code - IDA Pro/world\\j__ConstructPEAVCMoveMapLimitRightPEAV1stdYAXPEAPE_14000378D.c\nDecompiled Source Code - IDA Pro/world\\j__ConstructVCMoveMapLimitRightInfoV1stdYAXPEAVCMo_1400022D9.c\nDecompiled Source Code - IDA Pro/world\\j__Copy_backward_optPEAPEAVCMoveMapLimitInfoPEAPEA_140004BD8.c\nDecompiled Source Code - IDA Pro/world\\j__Copy_backward_optPEAPEAVCMoveMapLimitRightPEAPE_140010A5A.c\nDecompiled Source Code - IDA Pro/world\\j__Copy_backward_optPEAVCMoveMapLimitRightInfoPEAV_140010E3D.c\nDecompiled Source Code - IDA Pro/world\\j__Copy_optPEAPEAVCMoveMapLimitInfoPEAPEAV1Urandom_140003F5D.c\nDecompiled Source Code - IDA Pro/world\\j__Copy_optPEAPEAVCMoveMapLimitRightPEAPEAV1Urando_14000B1FE.c\nDecompiled Source Code - IDA Pro/world\\j__Copy_optPEAVCMoveMapLimitRightInfoPEAV1Urandom__14000F128.c\nDecompiled Source Code - IDA Pro/world\\j__CreateMonYAPEAVCMonsterPEAD0MMMZ_1400074BE.c\nDecompiled Source Code - IDA Pro/world\\j__Decconst_iterator_TreeV_Tmap_traitsVbasic_strin_140012B48.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyPEAU_Node_Tree_nodV_Tmap_traitsVbasic_st_140011798.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyPEAVCMoveMapLimitRightstdYAXPEAPEAVCMove_140006AAF.c\nDecompiled Source Code - IDA Pro/world\\j__DestroySDMCMonsterSAXXZ_14000CCA7.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyU_Node_Tree_nodV_Tmap_traitsVbasic_strin_14000BD57.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyVCMoveMapLimitRightInfostdYAXPEAVCMoveMa_140001451.c\nDecompiled Source Code - IDA Pro/world\\j__Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEA_140004F20.c\nDecompiled Source Code - IDA Pro/world\\j__Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEA_14000F69B.c\nDecompiled Source Code - IDA Pro/world\\j__Destroy_rangePEAVCMoveMapLimitRightVallocatorPE_140008323.c\nDecompiled Source Code - IDA Pro/world\\j__Destroy_rangePEAVCMoveMapLimitRightVallocatorPE_1400090E3.c\nDecompiled Source Code - IDA Pro/world\\j__Destroy_rangeVCMoveMapLimitRightInfoVallocatorV_140005E98.c\nDecompiled Source Code - IDA Pro/world\\j__Destroy_rangeVCMoveMapLimitRightInfoVallocatorV_14000C0DB.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyvectorPEAVCMoveMapLimitInfoVallocatorPEA_1400083FA.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyvectorPEAVCMoveMapLimitRightVallocatorPE_14000D94A.c\nDecompiled Source Code - IDA Pro/world\\j__DestroyvectorVCMoveMapLimitRightInfoVallocatorV_140012C65.c\nDecompiled Source Code - IDA Pro/world\\j__DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCS_140001799.c\nDecompiled Source Code - IDA Pro/world\\j__ECCircleZoneUEAAPEAXIZ_1400060EB.c\nDecompiled Source Code - IDA Pro/world\\j__ECMapDataUEAAPEAXIZ_140011BFD.c\nDecompiled Source Code - IDA Pro/world\\j__ECMapDisplayUEAAPEAXIZ_0_140007923.c\nDecompiled Source Code - IDA Pro/world\\j__ECMapDisplayUEAAPEAXIZ_140003332.c\nDecompiled Source Code - IDA Pro/world\\j__ECMapTabUEAAPEAXIZ_0_1400076B7.c\nDecompiled Source Code - IDA Pro/world\\j__ECMapTabUEAAPEAXIZ_140001B8B.c\nDecompiled Source Code - IDA Pro/world\\j__ECMonsterEventSetUEAAPEAXIZ_0_14000DA53.c\nDecompiled Source Code - IDA Pro/world\\j__ECMonsterEventSetUEAAPEAXIZ_14000D7C9.c\nDecompiled Source Code - IDA Pro/world\\j__ECMonsterUEAAPEAXIZ_14000546B.c\nDecompiled Source Code - IDA Pro/world\\j__Erase_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140005D94.c\nDecompiled Source Code - IDA Pro/world\\j__FillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVC_14000FA6F.c\nDecompiled Source Code - IDA Pro/world\\j__FillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAV_1400126CF.c\nDecompiled Source Code - IDA Pro/world\\j__FillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMove_14000FB8C.c\nDecompiled Source Code - IDA Pro/world\\j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1Urandom_ac_1400054C5.c\nDecompiled Source Code - IDA Pro/world\\j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1stdYAXPEAP_140001EF6.c\nDecompiled Source Code - IDA Pro/world\\j__Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1Urandom_a_140012F85.c\nDecompiled Source Code - IDA Pro/world\\j__Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1stdYAXPEA_140013C91.c\nDecompiled Source Code - IDA Pro/world\\j__FindV_Vector_iteratorPEAVCMoveMapLimitRightVall_14000AE43.c\nDecompiled Source Code - IDA Pro/world\\j__GBossSchedule_MapQEAAPEAXIZ_140002BFD.c\nDecompiled Source Code - IDA Pro/world\\j__GCCircleZoneUEAAPEAXIZ_140009CEB.c\nDecompiled Source Code - IDA Pro/world\\j__GCMapDataUEAAPEAXIZ_1400069B5.c\nDecompiled Source Code - IDA Pro/world\\j__GCMapOperationUEAAPEAXIZ_0_14001042E.c\nDecompiled Source Code - IDA Pro/world\\j__GCMapOperationUEAAPEAXIZ_14000A984.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterAIUEAAPEAXIZ_0_140013697.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterAIUEAAPEAXIZ_14000E4CB.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterEventRespawnUEAAPEAXIZ_0_14000C635.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterEventRespawnUEAAPEAXIZ_14000216C.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterHierarchyUEAAPEAXIZ_0_14000E633.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterHierarchyUEAAPEAXIZ_14000B5E1.c\nDecompiled Source Code - IDA Pro/world\\j__GCMonsterUEAAPEAXIZ_1400109BF.c\nDecompiled Source Code - IDA Pro/world\\j__GCMoveMapLimitInfoQEAAPEAXIZ_14000E200.c\nDecompiled Source Code - IDA Pro/world\\j__GCMoveMapLimitManagerQEAAPEAXIZ_1400042B9.c\nDecompiled Source Code - IDA Pro/world\\j__GCMoveMapLimitRightInfoQEAAPEAXIZ_140012FC6.c\nDecompiled Source Code - IDA Pro/world\\j__GCMoveMapLimitRightQEAAPEAXIZ_1400129E5.c\nDecompiled Source Code - IDA Pro/world\\j__GCRFMonsterAIMgrQEAAPEAXIZ_14000934A.c\nDecompiled Source Code - IDA Pro/world\\j__G_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_1400011AE.c\nDecompiled Source Code - IDA Pro/world\\j__G__change_monsterQEAAPEAXIZ_140002572.c\nDecompiled Source Code - IDA Pro/world\\j__GetBaseClassCMapTabKAPEAUCRuntimeClassXZ_140006366.c\nDecompiled Source Code - IDA Pro/world\\j__GetBlinkNodeCMonsterAggroMgrIEAAPEAUCAggroNodeX_14000BFC3.c\nDecompiled Source Code - IDA Pro/world\\j__GetMonsterContTimeYAGEEZ_14000AF9C.c\nDecompiled Source Code - IDA Pro/world\\j__Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_0_14000E74B.c\nDecompiled Source Code - IDA Pro/world\\j__Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_140005C36.c\nDecompiled Source Code - IDA Pro/world\\j__Incconst_iterator_TreeV_Tmap_traitsVbasic_strin_14000FA0B.c\nDecompiled Source Code - IDA Pro/world\\j__Insert_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14000ECAA.c\nDecompiled Source Code - IDA Pro/world\\j__Insert_nvectorPEAVCMoveMapLimitInfoVallocatorPE_140012D5A.c\nDecompiled Source Code - IDA Pro/world\\j__Insert_nvectorPEAVCMoveMapLimitRightVallocatorP_140001749.c\nDecompiled Source Code - IDA Pro/world\\j__Insert_nvectorVCMoveMapLimitRightInfoVallocator_14000B460.c\nDecompiled Source Code - IDA Pro/world\\j__Isnil_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400042E6.c\nDecompiled Source Code - IDA Pro/world\\j__Iter_catPEAPEAVCMoveMapLimitInfostdYAAUrandom_a_14001348A.c\nDecompiled Source Code - IDA Pro/world\\j__Iter_catPEAPEAVCMoveMapLimitRightstdYAAUrandom__14000A146.c\nDecompiled Source Code - IDA Pro/world\\j__Iter_randomPEAPEAVCMoveMapLimitInfoPEAPEAV1stdY_14000CC9D.c\nDecompiled Source Code - IDA Pro/world\\j__Iter_randomPEAPEAVCMoveMapLimitRightPEAPEAV1std_14000E4B2.c\nDecompiled Source Code - IDA Pro/world\\j__Iter_randomPEAVCMoveMapLimitRightInfoPEAV1stdYA_140008E72.c\nDecompiled Source Code - IDA Pro/world\\j__Key_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140011793.c\nDecompiled Source Code - IDA Pro/world\\j__Kfn_Tmap_traitsVbasic_stringDUchar_traitsDstdVa_14000D7D8.c\nDecompiled Source Code - IDA Pro/world\\j__Lbound_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1400019DD.c\nDecompiled Source Code - IDA Pro/world\\j__Left_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000543E.c\nDecompiled Source Code - IDA Pro/world\\j__Lmost_TreeV_Tmap_traitsVbasic_stringDUchar_trai_14000DEC7.c\nDecompiled Source Code - IDA Pro/world\\j__LoadMonBlkCMapDataAEAA_NPEADPEAU_map_fldZ_14000C162.c\nDecompiled Source Code - IDA Pro/world\\j__LoadPortalCMapDataAEAA_NPEADZ_14000ADCB.c\nDecompiled Source Code - IDA Pro/world\\j__LoadQuestCMapDataAEAA_NPEADZ_140009AB6.c\nDecompiled Source Code - IDA Pro/world\\j__LoadResourceCMapDataAEAA_NPEADZ_140001500.c\nDecompiled Source Code - IDA Pro/world\\j__LoadSafeCMapDataAEAA_NPEADZ_140001762.c\nDecompiled Source Code - IDA Pro/world\\j__LoadStartCMapDataAEAA_NPEADZ_1400059C0.c\nDecompiled Source Code - IDA Pro/world\\j__LoadStoreDummyCMapDataAEAA_NPEADZ_140003ACB.c\nDecompiled Source Code - IDA Pro/world\\j__Lrotate_TreeV_Tmap_traitsVbasic_stringDUchar_tr_14000BB27.c\nDecompiled Source Code - IDA Pro/world\\j__Max_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140009DC2.c\nDecompiled Source Code - IDA Pro/world\\j__Min_TreeV_Tmap_traitsVbasic_stringDUchar_traits_1400133A9.c\nDecompiled Source Code - IDA Pro/world\\j__Move_backward_optPEAPEAVCMoveMapLimitInfoPEAPEA_140002347.c\nDecompiled Source Code - IDA Pro/world\\j__Move_backward_optPEAPEAVCMoveMapLimitRightPEAPE_14000B2B2.c\nDecompiled Source Code - IDA Pro/world\\j__Move_backward_optPEAVCMoveMapLimitRightInfoPEAV_140004570.c\nDecompiled Source Code - IDA Pro/world\\j__Move_catPEAPEAVCMoveMapLimitInfostdYAAU_Undefin_14000BC71.c\nDecompiled Source Code - IDA Pro/world\\j__Move_catPEAPEAVCMoveMapLimitRightstdYAAU_Undefi_140008D19.c\nDecompiled Source Code - IDA Pro/world\\j__Move_catPEAVCMoveMapLimitRightInfostdYAAU_Undef_14000DADA.c\nDecompiled Source Code - IDA Pro/world\\j__Mynodeconst_iterator_TreeV_Tmap_traitsVbasic_st_140004E62.c\nDecompiled Source Code - IDA Pro/world\\j__Myval_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400099C6.c\nDecompiled Source Code - IDA Pro/world\\j__Parent_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14000E4DA.c\nDecompiled Source Code - IDA Pro/world\\j__Ptr_catPEAPEAVCMoveMapLimitInfoPEAPEAV1stdYAAU__140011D1A.c\nDecompiled Source Code - IDA Pro/world\\j__Ptr_catPEAPEAVCMoveMapLimitRightPEAPEAV1stdYAAU_14000409D.c\nDecompiled Source Code - IDA Pro/world\\j__Ptr_catPEAVCMoveMapLimitRightInfoPEAV1stdYAAU_N_140005272.c\nDecompiled Source Code - IDA Pro/world\\j__Ptr_catV_Vector_const_iteratorPEAVCMoveMapLimit_140010640.c\nDecompiled Source Code - IDA Pro/world\\j__Right_TreeV_Tmap_traitsVbasic_stringDUchar_trai_14000B753.c\nDecompiled Source Code - IDA Pro/world\\j__Rmost_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140003E18.c\nDecompiled Source Code - IDA Pro/world\\j__Root_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000949E.c\nDecompiled Source Code - IDA Pro/world\\j__Rrotate_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140013926.c\nDecompiled Source Code - IDA Pro/world\\j__ShortRankCMonsterAggroMgrIEAAXXZ_140004381.c\nDecompiled Source Code - IDA Pro/world\\j__Tidy_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140007BAD.c\nDecompiled Source Code - IDA Pro/world\\j__TidyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140010DB6.c\nDecompiled Source Code - IDA Pro/world\\j__TidyvectorPEAVCMoveMapLimitRightVallocatorPEAVC_140013971.c\nDecompiled Source Code - IDA Pro/world\\j__TidyvectorVCMoveMapLimitRightInfoVallocatorVCMo_140013F25.c\nDecompiled Source Code - IDA Pro/world\\j__UcopyV_Vector_const_iteratorPEAVCMoveMapLimitRi_14001024E.c\nDecompiled Source Code - IDA Pro/world\\j__UfillvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1400104D8.c\nDecompiled Source Code - IDA Pro/world\\j__UfillvectorPEAVCMoveMapLimitRightVallocatorPEAV_14000CF5E.c\nDecompiled Source Code - IDA Pro/world\\j__UfillvectorVCMoveMapLimitRightInfoVallocatorVCM_140013ACA.c\nDecompiled Source Code - IDA Pro/world\\j__UmovePEAPEAVCMoveMapLimitInfovectorPEAVCMoveMap_14000C469.c\nDecompiled Source Code - IDA Pro/world\\j__UmovePEAPEAVCMoveMapLimitRightvectorPEAVCMoveMa_14000E98A.c\nDecompiled Source Code - IDA Pro/world\\j__UmovePEAVCMoveMapLimitRightInfovectorVCMoveMapL_1400126D4.c\nDecompiled Source Code - IDA Pro/world\\j__Unchecked_move_backwardPEAPEAVCMoveMapLimitInfo_140012AA8.c\nDecompiled Source Code - IDA Pro/world\\j__Unchecked_move_backwardPEAPEAVCMoveMapLimitRigh_14000807B.c\nDecompiled Source Code - IDA Pro/world\\j__Unchecked_move_backwardPEAVCMoveMapLimitRightIn_14000C856.c\nDecompiled Source Code - IDA Pro/world\\j__XlenvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140008D91.c\nDecompiled Source Code - IDA Pro/world\\j__XlenvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000B005.c\nDecompiled Source Code - IDA Pro/world\\j__XlenvectorVCMoveMapLimitRightInfoVallocatorVCMo_140008DFA.c\nDecompiled Source Code - IDA Pro/world\\j_allocateallocatorPEAVCMoveMapLimitInfostdQEAAPEA_14000984F.c\nDecompiled Source Code - IDA Pro/world\\j_allocateallocatorPEAVCMoveMapLimitRightstdQEAAPE_1400086DE.c\nDecompiled Source Code - IDA Pro/world\\j_allocateallocatorU_Node_Tree_nodV_Tmap_traitsVba_140006F28.c\nDecompiled Source Code - IDA Pro/world\\j_allocateallocatorVCMoveMapLimitRightInfostdQEAAP_14000CF6D.c\nDecompiled Source Code - IDA Pro/world\\j_assignvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_14000E5F2.c\nDecompiled Source Code - IDA Pro/world\\j_assignvectorVCMoveMapLimitRightInfoVallocatorVCM_1400059B6.c\nDecompiled Source Code - IDA Pro/world\\j_begin_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000965B.c\nDecompiled Source Code - IDA Pro/world\\j_beginvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140003F85.c\nDecompiled Source Code - IDA Pro/world\\j_beginvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000CEF0.c\nDecompiled Source Code - IDA Pro/world\\j_beginvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000D706.c\nDecompiled Source Code - IDA Pro/world\\j_beginvectorVCMoveMapLimitRightInfoVallocatorVCMo_1400132D7.c\nDecompiled Source Code - IDA Pro/world\\j_capacityvectorPEAVCMoveMapLimitInfoVallocatorPEA_140006839.c\nDecompiled Source Code - IDA Pro/world\\j_capacityvectorPEAVCMoveMapLimitRightVallocatorPE_14001099C.c\nDecompiled Source Code - IDA Pro/world\\j_capacityvectorVCMoveMapLimitRightInfoVallocatorV_140001F50.c\nDecompiled Source Code - IDA Pro/world\\j_check_dummyAutominePersonalMgrQEAA_NPEAVCMapData_140012DF0.c\nDecompiled Source Code - IDA Pro/world\\j_class_addVCMonsterlua_tinkerYAXPEAUlua_StatePEBD_140002D8D.c\nDecompiled Source Code - IDA Pro/world\\j_class_defVCMonsterP81EAAPEAVCLuaSignalReActorXZl_140002AC2.c\nDecompiled Source Code - IDA Pro/world\\j_clear_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000D657.c\nDecompiled Source Code - IDA Pro/world\\j_clearvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_1400102D0.c\nDecompiled Source Code - IDA Pro/world\\j_clearvectorPEAVCMoveMapLimitRightVallocatorPEAVC_140013F8E.c\nDecompiled Source Code - IDA Pro/world\\j_constructallocatorPEAU_Node_Tree_nodV_Tmap_trait_140007EF5.c\nDecompiled Source Code - IDA Pro/world\\j_constructallocatorPEAVCMoveMapLimitRightstdQEAAX_14000DD3C.c\nDecompiled Source Code - IDA Pro/world\\j_constructallocatorVCMoveMapLimitRightInfostdQEAA_14000FE43.c\nDecompiled Source Code - IDA Pro/world\\j_deallocateallocatorPEAVCMoveMapLimitInfostdQEAAX_140001D52.c\nDecompiled Source Code - IDA Pro/world\\j_deallocateallocatorPEAVCMoveMapLimitRightstdQEAA_14001327D.c\nDecompiled Source Code - IDA Pro/world\\j_deallocateallocatorU_Node_Tree_nodV_Tmap_traitsV_14001329B.c\nDecompiled Source Code - IDA Pro/world\\j_deallocateallocatorVCMoveMapLimitRightInfostdQEA_140003391.c\nDecompiled Source Code - IDA Pro/world\\j_defP6APEAVCMonsterPEAD0MMMZlua_tinkerYAXPEAUlua__14000A8E4.c\nDecompiled Source Code - IDA Pro/world\\j_destroyallocatorPEAU_Node_Tree_nodV_Tmap_traitsV_140007234.c\nDecompiled Source Code - IDA Pro/world\\j_destroyallocatorPEAVCMoveMapLimitRightstdQEAAXPE_140010226.c\nDecompiled Source Code - IDA Pro/world\\j_destroyallocatorU_Node_Tree_nodV_Tmap_traitsVbas_140013AAC.c\nDecompiled Source Code - IDA Pro/world\\j_destroyallocatorVCMoveMapLimitRightInfostdQEAAXP_14000D300.c\nDecompiled Source Code - IDA Pro/world\\j_destroyerVCMonsterlua_tinkerYAHPEAUlua_StateZ_1400137A5.c\nDecompiled Source Code - IDA Pro/world\\j_emptyvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000AC63.c\nDecompiled Source Code - IDA Pro/world\\j_end_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_1400054BB.c\nDecompiled Source Code - IDA Pro/world\\j_endvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_140002E41.c\nDecompiled Source Code - IDA Pro/world\\j_endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_140001D8E.c\nDecompiled Source Code - IDA Pro/world\\j_endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_14000B8ED.c\nDecompiled Source Code - IDA Pro/world\\j_endvectorVCMoveMapLimitRightInfoVallocatorVCMove_14000EC91.c\nDecompiled Source Code - IDA Pro/world\\j_erase_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140003968.c\nDecompiled Source Code - IDA Pro/world\\j_erase_TreeV_Tmap_traitsVbasic_stringDUchar_trait_1400118AB.c\nDecompiled Source Code - IDA Pro/world\\j_erasevectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_14000B7B7.c\nDecompiled Source Code - IDA Pro/world\\j_erasevectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000D391.c\nDecompiled Source Code - IDA Pro/world\\j_erasevectorVCMoveMapLimitRightInfoVallocatorVCMo_140008A9E.c\nDecompiled Source Code - IDA Pro/world\\j_fillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVCM_140002153.c\nDecompiled Source Code - IDA Pro/world\\j_fillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAVC_14001335E.c\nDecompiled Source Code - IDA Pro/world\\j_fillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMoveM_14001310B.c\nDecompiled Source Code - IDA Pro/world\\j_findV_Vector_iteratorPEAVCMoveMapLimitRightVallo_1400103B1.c\nDecompiled Source Code - IDA Pro/world\\j_gm_MapChangeCMainThreadQEAAXPEAVCMapDataZ_140008F6C.c\nDecompiled Source Code - IDA Pro/world\\j_gm_UpdateMapCMainThreadQEAAXXZ_1400050D3.c\nDecompiled Source Code - IDA Pro/world\\j_insert_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400086ED.c\nDecompiled Source Code - IDA Pro/world\\j_insert_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140011527.c\nDecompiled Source Code - IDA Pro/world\\j_insertvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1400024BE.c\nDecompiled Source Code - IDA Pro/world\\j_insertvectorPEAVCMoveMapLimitRightVallocatorPEAV_140006E60.c\nDecompiled Source Code - IDA Pro/world\\j_insertvectorVCMoveMapLimitRightInfoVallocatorVCM_14000DDFF.c\nDecompiled Source Code - IDA Pro/world\\j_invokePEAVCLuaSignalReActormem_functorVCMonsterX_14000309E.c\nDecompiled Source Code - IDA Pro/world\\j_invokePEAVCMonsterfunctorPEADPEADMMMlua_tinkerSA_14000BB2C.c\nDecompiled Source Code - IDA Pro/world\\j_invokelua2objectPEAVCMonsterlua_tinkerSAPEAVCMon_140012A80.c\nDecompiled Source Code - IDA Pro/world\\j_invokeobject2luaPEAVCMonsterlua_tinkerSAXPEAUlua_140003A3F.c\nDecompiled Source Code - IDA Pro/world\\j_invokeptr2luaVCMonsterlua_tinkerSAXPEAUlua_State_14000558D.c\nDecompiled Source Code - IDA Pro/world\\j_invokeuser2typeP6APEAVCMonsterPEAD0MMMZlua_tinke_14001023F.c\nDecompiled Source Code - IDA Pro/world\\j_invokeuser2typeP8CMonsterEAAPEAVCLuaSignalReActo_140009A7A.c\nDecompiled Source Code - IDA Pro/world\\j_invokevoid2ptrA6APEAVCMonsterPEAD0MMMZlua_tinker_1400024AA.c\nDecompiled Source Code - IDA Pro/world\\j_invokevoid2ptrVCMonsterlua_tinkerSAPEAVCMonsterP_14000FDAD.c\nDecompiled Source Code - IDA Pro/world\\j_invokevoid2typeP6APEAVCMonsterPEAD0MMMZlua_tinke_14000DDAF.c\nDecompiled Source Code - IDA Pro/world\\j_invokevoid2typeP8CMonsterEAAPEAVCLuaSignalReActo_14000A04C.c\nDecompiled Source Code - IDA Pro/world\\j_invokevoid2typePEAVCMonsterlua_tinkerSAPEAVCMons_140009070.c\nDecompiled Source Code - IDA Pro/world\\j_invokevoid2valP8CMonsterEAAPEAVCLuaSignalReActor_140013377.c\nDecompiled Source Code - IDA Pro/world\\j_lower_bound_TreeV_Tmap_traitsVbasic_stringDUchar_14000B23F.c\nDecompiled Source Code - IDA Pro/world\\j_lua2typePEAVCMonsterlua_tinkerYAPEAVCMonsterPEAU_14000E35E.c\nDecompiled Source Code - IDA Pro/world\\j_max_size_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140009B33.c\nDecompiled Source Code - IDA Pro/world\\j_max_sizeallocatorPEAVCMoveMapLimitInfostdQEBA_KX_1400129C2.c\nDecompiled Source Code - IDA Pro/world\\j_max_sizeallocatorPEAVCMoveMapLimitRightstdQEBA_K_14000A894.c\nDecompiled Source Code - IDA Pro/world\\j_max_sizeallocatorVCMoveMapLimitRightInfostdQEBA__140003788.c\nDecompiled Source Code - IDA Pro/world\\j_max_sizevectorPEAVCMoveMapLimitInfoVallocatorPEA_14000A26D.c\nDecompiled Source Code - IDA Pro/world\\j_max_sizevectorPEAVCMoveMapLimitRightVallocatorPE_14000991C.c\nDecompiled Source Code - IDA Pro/world\\j_max_sizevectorVCMoveMapLimitRightInfoVallocatorV_14000FFC4.c\nDecompiled Source Code - IDA Pro/world\\j_mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeon_14001007D.c\nDecompiled Source Code - IDA Pro/world\\j_mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDung_14000C9F0.c\nDecompiled Source Code - IDA Pro/world\\j_mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkH_140004ABB.c\nDecompiled Source Code - IDA Pro/world\\j_mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDun_140002BC1.c\nDecompiled Source Code - IDA Pro/world\\j_nameclass_nameVCMonsterlua_tinkerSAPEBDPEBDZ_140007130.c\nDecompiled Source Code - IDA Pro/world\\j_pc_AlterWorldServiceCMainThreadQEAAX_NZ_14000D15C.c\nDecompiled Source Code - IDA Pro/world\\j_pc_EnterWorldResultCMainThreadQEAAXEPEAU_CLIDZ_14000649C.c\nDecompiled Source Code - IDA Pro/world\\j_pc_OpenWorldFailureResultCMainThreadQEAAXPEADZ_1400082E7.c\nDecompiled Source Code - IDA Pro/world\\j_pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1400132C8.c\nDecompiled Source Code - IDA Pro/world\\j_pushPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAVCM_140004705.c\nDecompiled Source Code - IDA Pro/world\\j_push_backvectorPEAVCMoveMapLimitRightVallocatorP_140009917.c\nDecompiled Source Code - IDA Pro/world\\j_push_functorPEAVCLuaSignalReActorVCMonsterlua_ti_1400031B6.c\nDecompiled Source Code - IDA Pro/world\\j_push_functorPEAVCMonsterPEADPEADMMMlua_tinkerYAX_14000DA1C.c\nDecompiled Source Code - IDA Pro/world\\j_qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140011D5B.c\nDecompiled Source Code - IDA Pro/world\\j_qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDunge_1400066DB.c\nDecompiled Source Code - IDA Pro/world\\j_readPEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlua__140010726.c\nDecompiled Source Code - IDA Pro/world\\j_set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__140006654.c\nDecompiled Source Code - IDA Pro/world\\j_size_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14001127A.c\nDecompiled Source Code - IDA Pro/world\\j_size_add_char_result_zoneQEAAHXZ_1400047E1.c\nDecompiled Source Code - IDA Pro/world\\j_size_del_char_result_zoneQEAAHXZ_14000C180.c\nDecompiled Source Code - IDA Pro/world\\j_size_enter_world_request_wracQEAAHXZ_14000B924.c\nDecompiled Source Code - IDA Pro/world\\j_size_enter_world_result_zoneQEAAHXZ_14000B06E.c\nDecompiled Source Code - IDA Pro/world\\j_size_move_to_own_stonemap_inform_zoclQEAAHXZ_1400015A5.c\nDecompiled Source Code - IDA Pro/world\\j_size_move_to_own_stonemap_result_zoclQEAAHXZ_140007C7F.c\nDecompiled Source Code - IDA Pro/world\\j_size_moveout_user_result_zoneQEAAHXZ_140006D34.c\nDecompiled Source Code - IDA Pro/world\\j_size_open_world_request_wracQEAAHXZ_140009F61.c\nDecompiled Source Code - IDA Pro/world\\j_size_reged_char_result_zoneQEAAHXZ_140003044.c\nDecompiled Source Code - IDA Pro/world\\j_size_sel_char_result_zoneQEAAHXZ_140012468.c\nDecompiled Source Code - IDA Pro/world\\j_size_server_notify_inform_zoneQEAAHXZ_140005187.c\nDecompiled Source Code - IDA Pro/world\\j_size_start_world_request_wracQEAAHXZ_14000BF55.c\nDecompiled Source Code - IDA Pro/world\\j_size_stop_world_request_wracQEAAHXZ_140005D3F.c\nDecompiled Source Code - IDA Pro/world\\j_size_target_monster_contsf_allinform_zoclQEAAHXZ_14000B2D0.c\nDecompiled Source Code - IDA Pro/world\\j_size_world_account_ping_wracQEAAHXZ_14000276B.c\nDecompiled Source Code - IDA Pro/world\\j_sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1400080A3.c\nDecompiled Source Code - IDA Pro/world\\j_sizevectorPEAVCMoveMapLimitRightVallocatorPEAVCM_140004D81.c\nDecompiled Source Code - IDA Pro/world\\j_sizevectorVCMoveMapLimitRightInfoVallocatorVCMov_14000D3D7.c\nDecompiled Source Code - IDA Pro/world\\j_type2luaPEAVCMonsterlua_tinkerYAXPEAUlua_StatePE_140004C2D.c\nDecompiled Source Code - IDA Pro/world\\j_unchecked_copyPEAPEAVCMoveMapLimitInfoPEAPEAV1st_14000D84B.c\nDecompiled Source Code - IDA Pro/world\\j_unchecked_copyPEAPEAVCMoveMapLimitRightPEAPEAV1s_14000F281.c\nDecompiled Source Code - IDA Pro/world\\j_unchecked_copyPEAVCMoveMapLimitRightInfoPEAV1std_140001276.c\nDecompiled Source Code - IDA Pro/world\\j_unchecked_fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1s_140007C61.c\nDecompiled Source Code - IDA Pro/world\\j_unchecked_fill_nPEAPEAVCMoveMapLimitRight_KPEAV1_140004D5E.c\nDecompiled Source Code - IDA Pro/world\\j_unregist_from_mapAutominePersonalQEAA_NEZ_14000267B.c\nDecompiled Source Code - IDA Pro/world\\j_upvalue_P6APEAVCMonsterPEAD0MMMZlua_tinkerYAP6AP_14000A439.c\nDecompiled Source Code - IDA Pro/world\\j_upvalue_P8CMonsterEAAPEAVCLuaSignalReActorXZlua__14000F0FB.c\nDecompiled Source Code - IDA Pro/world\\j_wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_14000B4BA.c\nDecompiled Source Code - IDA Pro/world\\j_wa_ExitWorldYAXPEAU_CLIDZ_14000FFAB.c\nDecompiled Source Code - IDA Pro/world\\lower_bound_TreeV_Tmap_traitsVbasic_stringDUchar_t_14018EB80.c\nDecompiled Source Code - IDA Pro/world\\lua2typePEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlu_14040AFF0.c\nDecompiled Source Code - IDA Pro/world\\max_size_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1401913D0.c\nDecompiled Source Code - IDA Pro/world\\max_sizeallocatorPEAVCMoveMapLimitInfostdQEBA_KXZ_1403A31C0.c\nDecompiled Source Code - IDA Pro/world\\max_sizeallocatorPEAVCMoveMapLimitRightstdQEBA_KXZ_1403B0CC0.c\nDecompiled Source Code - IDA Pro/world\\max_sizeallocatorVCMoveMapLimitRightInfostdQEBA_KX_1403A3230.c\nDecompiled Source Code - IDA Pro/world\\max_sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1403A2A70.c\nDecompiled Source Code - IDA Pro/world\\max_sizevectorPEAVCMoveMapLimitRightVallocatorPEAV_1403AFD20.c\nDecompiled Source Code - IDA Pro/world\\max_sizevectorVCMoveMapLimitRightInfoVallocatorVCM_1403A2CE0.c\nDecompiled Source Code - IDA Pro/world\\mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeonQu_140275730.c\nDecompiled Source Code - IDA Pro/world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c\nDecompiled Source Code - IDA Pro/world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c\nDecompiled Source Code - IDA Pro/world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c\nDecompiled Source Code - IDA Pro/world\\nameclass_nameVCMonsterlua_tinkerSAPEBDPEBDZ_1404082A0.c\nDecompiled Source Code - IDA Pro/world\\pc_AlterWorldServiceCMainThreadQEAAX_NZ_1401F61B0.c\nDecompiled Source Code - IDA Pro/world\\pc_EnterWorldResultCMainThreadQEAAXEPEAU_CLIDZ_1401F5B30.c\nDecompiled Source Code - IDA Pro/world\\pc_OpenWorldFailureResultCMainThreadQEAAXPEADZ_1401F56F0.c\nDecompiled Source Code - IDA Pro/world\\pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c\nDecompiled Source Code - IDA Pro/world\\pushPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAVCMon_14040A3F0.c\nDecompiled Source Code - IDA Pro/world\\push_backvectorPEAVCMoveMapLimitRightVallocatorPEA_1403AEB10.c\nDecompiled Source Code - IDA Pro/world\\push_functorPEAVCLuaSignalReActorVCMonsterlua_tink_1404089C0.c\nDecompiled Source Code - IDA Pro/world\\push_functorPEAVCMonsterPEADPEADMMMlua_tinkerYAXPE_140408A80.c\nDecompiled Source Code - IDA Pro/world\\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.c\nDecompiled Source Code - IDA Pro/world\\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.c\nDecompiled Source Code - IDA Pro/world\\readPEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlua_St_14040A210.c\nDecompiled Source Code - IDA Pro/world\\set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__r_14026F0F0.c\nDecompiled Source Code - IDA Pro/world\\size_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_14018FFA0.c\nDecompiled Source Code - IDA Pro/world\\size_add_char_result_zoneQEAAHXZ_14011F870.c\nDecompiled Source Code - IDA Pro/world\\size_del_char_result_zoneQEAAHXZ_14011F880.c\nDecompiled Source Code - IDA Pro/world\\size_enter_world_request_wracQEAAHXZ_14011F240.c\nDecompiled Source Code - IDA Pro/world\\size_enter_world_result_zoneQEAAHXZ_14011F250.c\nDecompiled Source Code - IDA Pro/world\\size_move_to_own_stonemap_inform_zoclQEAAHXZ_1400F03D0.c\nDecompiled Source Code - IDA Pro/world\\size_move_to_own_stonemap_result_zoclQEAAHXZ_1400F03C0.c\nDecompiled Source Code - IDA Pro/world\\size_moveout_user_result_zoneQEAAHXZ_14011FBD0.c\nDecompiled Source Code - IDA Pro/world\\size_open_world_request_wracQEAAHXZ_1402080C0.c\nDecompiled Source Code - IDA Pro/world\\size_reged_char_result_zoneQEAAHXZ_14011F6F0.c\nDecompiled Source Code - IDA Pro/world\\size_sel_char_result_zoneQEAAHXZ_14011F8D0.c\nDecompiled Source Code - IDA Pro/world\\size_server_notify_inform_zoneQEAAHXZ_14011F1E0.c\nDecompiled Source Code - IDA Pro/world\\size_start_world_request_wracQEAAHXZ_1402080D0.c\nDecompiled Source Code - IDA Pro/world\\size_stop_world_request_wracQEAAHXZ_1402080E0.c\nDecompiled Source Code - IDA Pro/world\\size_target_monster_contsf_allinform_zoclQEAAHXZ_1400F0140.c\nDecompiled Source Code - IDA Pro/world\\size_world_account_ping_wracQEAAHXZ_1402080F0.c\nDecompiled Source Code - IDA Pro/world\\sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMove_1403A78D0.c\nDecompiled Source Code - IDA Pro/world\\sizevectorPEAVCMoveMapLimitRightVallocatorPEAVCMov_1403AE9F0.c\nDecompiled Source Code - IDA Pro/world\\sizevectorVCMoveMapLimitRightInfoVallocatorVCMoveM_1403A21E0.c\nDecompiled Source Code - IDA Pro/world\\type2luaPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAV_14040B0E0.c\nDecompiled Source Code - IDA Pro/world\\unchecked_copyPEAPEAVCMoveMapLimitInfoPEAPEAV1stde_1403AAB40.c\nDecompiled Source Code - IDA Pro/world\\unchecked_copyPEAPEAVCMoveMapLimitRightPEAPEAV1std_1403B1C00.c\nDecompiled Source Code - IDA Pro/world\\unchecked_copyPEAVCMoveMapLimitRightInfoPEAV1stdex_1403B21A0.c\nDecompiled Source Code - IDA Pro/world\\unchecked_fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1std_1403ABFD0.c\nDecompiled Source Code - IDA Pro/world\\unchecked_fill_nPEAPEAVCMoveMapLimitRight_KPEAV1st_1403B3280.c\nDecompiled Source Code - IDA Pro/world\\unregist_from_mapAutominePersonalQEAA_NEZ_1402DB110.c\nDecompiled Source Code - IDA Pro/world\\upvalue_P6APEAVCMonsterPEAD0MMMZlua_tinkerYAP6APEA_14040A3A0.c\nDecompiled Source Code - IDA Pro/world\\upvalue_P8CMonsterEAAPEAVCLuaSignalReActorXZlua_ti_14040A260.c\nDecompiled Source Code - IDA Pro/world\\wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.c\nDecompiled Source Code - IDA Pro/world\\wa_ExitWorldYAXPEAU_CLIDZ_140046190.c\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;ef00de4a-08e9-415c-9620-532b0b2e140e;toolu_vrtx_01QgBikgZeKtF6WaP9i8Nyyk&quot;:{&quot;requestId&quot;:&quot;ef00de4a-08e9-415c-9620-532b0b2e140e&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01QgBikgZeKtF6WaP9i8Nyyk&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on Decompiled Source Code - IDA Pro/world/pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c:\n     1\t/*\n     2\t * Function: ?pc_OpenWorldSuccessResult@CMainThread@@QEAAXEPEAD0@Z\n     3\t * Address: 0x1401F5580\n     4\t */\n     5\t\n     6\tvoid __fastcall CMainThread::pc_OpenWorldSuccessResult(CMainThread *this, char byWorldCode, char *pszDBName, char *pszDBIP)\n     7\t{\n     8\t  __int64 *v4; // rdi@1\n     9\t  signed __int64 i; // rcx@1\n    10\t  __int64 v6; // [sp+0h] [bp-178h]@1\n    11\t  char Buffer; // [sp+30h] [bp-148h]@4\n    12\t  char v8; // [sp+D0h] [bp-A8h]@4\n    13\t  unsigned __int64 v9; // [sp+160h] [bp-18h]@4\n    14\t  CMainThread *v10; // [sp+180h] [bp+8h]@1\n    15\t  char v11; // [sp+188h] [bp+10h]@1\n    16\t  char *pszDBNamea; // [sp+190h] [bp+18h]@1\n    17\t  char *pszDBIPa; // [sp+198h] [bp+20h]@1\n    18\t\n    19\t  pszDBIPa = pszDBIP;\n    20\t  pszDBNamea = pszDBName;\n    21\t  v11 = byWorldCode;\n    22\t  v10 = this;\n    23\t  v4 = &amp;v6;\n    24\t  for ( i = 92i64; i; --i )\n    25\t  {\n    26\t    *(_DWORD *)v4 = -*********;\n    27\t    v4 = (__int64 *)((char *)v4 + 4);\n    28\t  }\n    29\t  v9 = (unsigned __int64)&amp;v6 ^ _security_cookie;\n    30\t  _strdate(&amp;Buffer);\n    31\t  _strtime(&amp;v8);\n    32\t  __trace(\&quot;%s-%s: Open World\&quot;, &amp;Buffer, &amp;v8);\n    33\t  v10-&gt;m_byWorldCode = v11;\n    34\t  WriteServerStartHistory(\&quot;DBInit Begin &gt;&gt; name: %s, ip: %s\&quot;, pszDBNamea, pszDBIPa);\n    35\t  if ( CMainThread::DatabaseInit(v10, pszDBNamea, pszDBIPa) )\n    36\t  {\n    37\t    v10-&gt;m_bWorldOpen = 1;\n    38\t    CMainThread::SerivceSelfStart(v10);\n    39\t    WriteServerStartHistory(\&quot;DBInit Complete &gt;&gt;\&quot;);\n    40\t  }\n    41\t  else\n    42\t  {\n    43\t    WriteServerStartHistory(\&quot;DBInit Fail &gt;&gt;\&quot;);\n    44\t    CLogFile::WriteString(&amp;v10-&gt;m_logSystemError, \&quot;init DB fail\&quot;);\n    45\t    CWnd::SendMessageA(g_pFrame, 0x10u, 0i64, 0i64);\n    46\t  }\n    47\t}\n    48\t\nTotal lines in file: 48\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;8192f3dc-1072-40f8-8fa8-1eb0ab083e1e;toolu_vrtx_01L6FFggsbWdpMaTJDbKBh92&quot;:{&quot;requestId&quot;:&quot;8192f3dc-1072-40f8-8fa8-1eb0ab083e1e&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01L6FFggsbWdpMaTJDbKBh92&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here are the results from executing the command.\n        &lt;return-code&gt;\n        0\n        &lt;/return-code&gt;\n        &lt;stdout&gt;\n        \u001b[?9001h\u001b[?1004h\u001b[?25l\u001b[2J\u001b[m\u001b[38;5;15m\u001b[HD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\0MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140465820.c:  \u001b[mCryptoPP::AlgorithmImpl&lt;CryptoPP::SimpleKeyingInterfaceImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;::AlgorithmImpl&lt;CryptoPP::SimpleKeyingInterfaceImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;((CryptoPP::AlgorithmImpl&lt;CryptoPP::SimpleKeyingInterfaceImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt; &gt;,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt; &gt; *)&amp;v4-&gt;vfptr);\u001b[38;5;15m\nD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\0MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140465820.c:  \u001b[mv4-&gt;vfptr = (CryptoPP::SimpleKeyingInterfaceVtbl *)&amp;CryptoPP::MessageAuthenticationCodeImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;::`vftable'{for `CryptoPP::SimpleKeyingInterface'};\u001b[38;5;15m\nD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\1MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140464F50.c:  \u001b[mCryptoPP::AlgorithmImpl&lt;CryptoPP::SimpleKeyingInterfaceImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;::~AlgorithmImpl&lt;CryptoPP::SimpleKeyingInterfaceImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt;&gt;((CryptoPP::AlgorithmImpl&lt;CryptoPP::SimpleKeyingInterfaceImpl&lt;CryptoPP::HMAC_Base,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt; &gt;,CryptoPP::HMAC&lt;CryptoPP::SHA1&gt; &gt; *)&amp;v4-&gt;vfptr);\u001b[38;5;15m\nD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\AccountServerLoginCMainThreadQEAAXXZ_1401F8140.c: \u001b[m* Function: ?AccountServerLogin@CMainThread@@QEAAXXZ\u001b[38;5;15m\nD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\AccountServerLoginCMainThreadQEAAXXZ_1401F8140.c:\u001b[mvoid __fastcall CMainThread::AccountServerLogin(CMainThread *this)\n\u001b]0;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe\u0007\u001b[?25h\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallFunc_RFOnline_AuthCEnglandBillingMgrQEAAHAEAU__1403198F0.c: \u001b[m* Function: ?CallFunc_RFOnline_Auth@CEnglandBillingMgr@@QEAAHAEAU_param_cash_select@@H@Z\u001b[38;5;15m\nD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallFunc_RFOnline_AuthCEnglandBillingMgrQEAAHAEAU__1403198F0.c:\u001b[msigned __int64 __fastcall CEnglandBillingMgr::CallFunc_RFOnline_Auth(CEnglandBillingMgr *this, _param_cash_select *rParam, int nIdx)\u001b[38;5;15m\nD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallFunc_RFOnline_AuthCRusiaBillingMgrQEAAHAEAU_pa_1403213A0.c: \u001b[m* Function: ?CallFunc_RFOnline_Auth@CRusiaBillingMgr@@QEAAHAEAU_param_cash_select@@\n\u001b[24;120H@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallFunc_RFOnline_AuthCRusi\u001b[m\n\u001b[38;5;15m\u001b[24;120HiaBillingMgrQEAAHAEAU_pa_1403213A0.c:\u001b[m__int64 __usercall CRusiaBillingMgr::CallFunc_RFOnline_Auth@&lt;rax&gt;(CRusiaBillingMgr *\n\u001b[24;120H*this@&lt;rcx&gt;, _param_cash_select *rParam@&lt;rdx&gt;, double a3@&lt;xmm0&gt;)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallFunc_RFOnline_AuthCRusi\u001b[m\n\u001b[38;5;15m\u001b[24;120HiaBillingMgrQEAAHAEAU_pa_1403213A0.c:  \u001b[mRFACC_CheckBalance(v8-&gt;in_szAcc);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c: \u001b[m* Function: ?CallProc_RFOnlineAuth@CRFCashItemDatabase@@QEAAHAEAU_param_cash_select\n\u001b[24;120Ht@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:\u001b[msigned __int64 __fastcall CRFCashItemDatabase::CallProc_RFOnlineAuth(CRFCashItemData\n\u001b[24;120Habase *this, _param_cash_select *rParam)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:  \u001b[mCRFCashItemDatabase *v15; // [sp+190h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:    \u001b[m\&quot;declare @out_amount int exec prc_rfonline_auth '%s', @s_amount = @out_amount ou\n\u001b[24;120Hutput select @out_amount\&quot;,\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:    \u001b[mCRFNewDatabase::Log((CRFNewDatabase *)&amp;v15-&gt;vfptr, &amp;DstBuf);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:  \u001b[mif ( v15-&gt;m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&amp;v1\n\u001b[24;120H15-&gt;vfptr) )\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:        \u001b[mCRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, v11, &amp;DstBuf, \&quot;SQ\n\u001b[24;120HQLExecDirect\&quot;, SQLStmt);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:        \u001b[mCRFNewDatabase::ErrorAction((CRFNewDatabase *)&amp;v15-&gt;vfptr, v11, v15-&gt;m_hStmt\n\u001b[24;120HtSelect);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:          \u001b[mCRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, v11, &amp;DstBuf, \&quot;\n\u001b[24;120H\&quot;SQLExecDirect\&quot;, SQLStmt);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:          \u001b[mCRFNewDatabase::ErrorAction((CRFNewDatabase *)&amp;v15-&gt;vfptr, v11, v15-&gt;m_hSt\n\u001b[24;120HtmtSelect);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:            \u001b[mCRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, v11, &amp;DstBuf,\n\u001b[24;120H, \&quot;SQLExecDirect\&quot;, SQLStmt);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:            \u001b[mCRFNewDatabase::ErrorAction((CRFNewDatabase *)&amp;v15-&gt;vfptr, v11, v15-&gt;m_h\n\u001b[24;120HhStmtSelect);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:            \u001b[mCRFNewDatabase::FmtLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, \&quot;%s Success\&quot;, &amp;Dst\n\u001b[24;120HtBuf);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuthCRFCas\u001b[m\n\u001b[38;5;15m\u001b[24;120HshItemDatabaseQEAAHAEAU__140482430.c:    \u001b[mCRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, \&quot;ReConnectDataBase Fail\n\u001b[24;120Hl. Query : %s\&quot;, &amp;DstBuf);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c: \u001b[m* Function: ?CallProc_RFOnlineAuth_Jap@CRFCashItemDatabase@@QEAAHAEAU_param_cash_se\n\u001b[24;120Helect@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:\u001b[msigned __int64 __fastcall CRFCashItemDatabase::CallProc_RFOnlineAuth_Jap(CRFCashItem\n\u001b[24;120HmDatabase *this, _param_cash_select *rParam)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:  \u001b[mCRFCashItemDatabase *v15; // [sp+190h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:    \u001b[m\&quot;declare @out_amount int exec dbo.SP_RF_CHK_GEM_GAMEON @uid = '%s', @s_amount = \n\u001b[24;120H @out_amount output select @out_amount\&quot;,\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:    \u001b[mCRFNewDatabase::Log((CRFNewDatabase *)&amp;v15-&gt;vfptr, &amp;DstBuf);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:  \u001b[mif ( v15-&gt;m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&amp;v1\n\u001b[24;120H15-&gt;vfptr) )\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:        \u001b[mCRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, v9, &amp;DstBuf, \&quot;SQL\n\u001b[24;120HLExecDirect\&quot;, SQLStmt);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:        \u001b[mCRFNewDatabase::ErrorAction((CRFNewDatabase *)&amp;v15-&gt;vfptr, v9, v15-&gt;m_hStmtS\n\u001b[24;120HSelect);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:          \u001b[mCRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, v9, &amp;DstBuf, \&quot;S\n\u001b[24;120HSQLExecDirect\&quot;, SQLStmt);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:          \u001b[mCRFNewDatabase::ErrorAction((CRFNewDatabase *)&amp;v15-&gt;vfptr, v9, v15-&gt;m_hStm\n\u001b[24;120HmtSelect);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:            \u001b[mCRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, v9, &amp;DstBuf, \n\u001b[24;120H \&quot;SQLExecDirect\&quot;, SQLStmt);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:            \u001b[mCRFNewDatabase::ErrorAction((CRFNewDatabase *)&amp;v15-&gt;vfptr, v9, v15-&gt;m_hS\n\u001b[24;120HStmtSelect);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:            \u001b[mCRFNewDatabase::FmtLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, \&quot;%s Success\&quot;, &amp;Dst\n\u001b[24;120HtBuf);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\CallProc_RFOnlineAuth_JapCR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRFCashItemDatabaseQEAAHA_1404836A0.c:    \u001b[mCRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&amp;v15-&gt;vfptr, \&quot;ReConnectDataBase Fail\n\u001b[24;120Hl. Query : %s\&quot;, &amp;DstBuf);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\InitCAsyncLogInfoQEAA_NW4AS\u001b[m\n\u001b[38;5;15m\u001b[24;120HSYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c:\u001b[mchar __usercall CAsyncLogInfo::Init@&lt;al&gt;(CAsyncLogInfo *this@&lt;rcx&gt;, ASYNC_LOG_TYPE e\n\u001b[24;120HeType@&lt;edx&gt;, const char *szDirPath@&lt;r8&gt;, const char *szTypeName@&lt;r9&gt;, signed __int64 a5@&lt;rax&gt;, bool bAddDateFileName, uns\n\u001b[24;120Hsigned int dwUpdateFileNameDelay, CLogFile *logLoading)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\InitCAsyncLogInfoQEAA_NW4AS\u001b[m\n\u001b[38;5;15m\u001b[24;120HSYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c:      \u001b[mlogLoading,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\InitCAsyncLogInfoQEAA_NW4AS\u001b[m\n\u001b[38;5;15m\u001b[24;120HSYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c:\u001b[m\u001b[12ClogLoading,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\InitCAsyncLogInfoQEAA_NW4AS\u001b[m\n\u001b[38;5;15m\u001b[24;120HSYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c:\u001b[m\u001b[10ClogLoading,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\InitCAsyncLogInfoQEAA_NW4AS\u001b[m\n\u001b[38;5;15m\u001b[24;120HSYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c:        \u001b[mlogLoading,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\InitCAsyncLogInfoQEAA_NW4AS\u001b[m\n\u001b[38;5;15m\u001b[24;120HSYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c:      \u001b[mlogLoading,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_AccountServerLoginCMainTh\u001b[m\n\u001b[38;5;15m\u001b[24;120HhreadQEAAXXZ_14001102C.c: \u001b[m* Function: j_?AccountServerLogin@CMainThread@@QEAAXXZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_AccountServerLoginCMainTh\u001b[m\n\u001b[38;5;15m\u001b[24;120HhreadQEAAXXZ_14001102C.c:\u001b[mvoid __fastcall CMainThread::AccountServerLogin(CMainThread *this)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_AccountServerLoginCMainTh\u001b[m\n\u001b[38;5;15m\u001b[24;120HhreadQEAAXXZ_14001102C.c:  \u001b[mCMainThread::AccountServerLogin(this);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallFunc_RFOnline_AuthCEn\u001b[m\n\u001b[38;5;15m\u001b[24;120HnglandBillingMgrQEAAHAEA_14000D4B3.c: \u001b[m* Function: j_?CallFunc_RFOnline_Auth@CEnglandBillingMgr@@QEAAHAEAU_param_cash_sele\n\u001b[24;120Hect@@H@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallFunc_RFOnline_AuthCEn\u001b[m\n\u001b[38;5;15m\u001b[24;120HnglandBillingMgrQEAAHAEA_14000D4B3.c:\u001b[mint __fastcall CEnglandBillingMgr::CallFunc_RFOnline_Auth(CEnglandBillingMgr *this, \n\u001b[24;120H _param_cash_select *rParam, int nIdx)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallFunc_RFOnline_AuthCEn\u001b[m\n\u001b[38;5;15m\u001b[24;120HnglandBillingMgrQEAAHAEA_14000D4B3.c:  \u001b[mreturn CEnglandBillingMgr::CallFunc_RFOnline_Auth(this, rParam, nIdx);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallFunc_RFOnline_AuthCRu\u001b[m\n\u001b[38;5;15m\u001b[24;120HusiaBillingMgrQEAAHAEAU__140001E24.c: \u001b[m* Function: j_?CallFunc_RFOnline_Auth@CRusiaBillingMgr@@QEAAHAEAU_param_cash_select\n\u001b[24;120Ht@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallFunc_RFOnline_AuthCRu\u001b[m\n\u001b[38;5;15m\u001b[24;120HusiaBillingMgrQEAAHAEAU__140001E24.c:\u001b[mint __fastcall CRusiaBillingMgr::CallFunc_RFOnline_Auth(CRusiaBillingMgr *this, _par\n\u001b[24;120Hram_cash_select *rParam)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallFunc_RFOnline_AuthCRu\u001b[m\n\u001b[38;5;15m\u001b[24;120HusiaBillingMgrQEAAHAEAU__140001E24.c:  \u001b[mreturn CRusiaBillingMgr::CallFunc_RFOnline_Auth(this, rParam);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallProc_RFOnlineAuthCRFC\u001b[m\n\u001b[38;5;15m\u001b[24;120HCashItemDatabaseQEAAHAEA_14000C2F2.c: \u001b[m* Function: j_?CallProc_RFOnlineAuth@CRFCashItemDatabase@@QEAAHAEAU_param_cash_sele\n\u001b[24;120Hect@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallProc_RFOnlineAuthCRFC\u001b[m\n\u001b[38;5;15m\u001b[24;120HCashItemDatabaseQEAAHAEA_14000C2F2.c:\u001b[mint __fastcall CRFCashItemDatabase::CallProc_RFOnlineAuth(CRFCashItemDatabase *this,\n\u001b[24;120H, _param_cash_select *rParam)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallProc_RFOnlineAuthCRFC\u001b[m\n\u001b[38;5;15m\u001b[24;120HCashItemDatabaseQEAAHAEA_14000C2F2.c:  \u001b[mreturn CRFCashItemDatabase::CallProc_RFOnlineAuth(this, rParam);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallProc_RFOnlineAuth_Jap\u001b[m\n\u001b[38;5;15m\u001b[24;120HpCRFCashItemDatabaseQEAA_14000B334.c: \u001b[m* Function: j_?CallProc_RFOnlineAuth_Jap@CRFCashItemDatabase@@QEAAHAEAU_param_cash_\n\u001b[24;120H_select@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallProc_RFOnlineAuth_Jap\u001b[m\n\u001b[38;5;15m\u001b[24;120HpCRFCashItemDatabaseQEAA_14000B334.c:\u001b[mint __fastcall CRFCashItemDatabase::CallProc_RFOnlineAuth_Jap(CRFCashItemDatabase *t\n\u001b[24;120Hthis, _param_cash_select *rParam)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_CallProc_RFOnlineAuth_Jap\u001b[m\n\u001b[38;5;15m\u001b[24;120HpCRFCashItemDatabaseQEAA_14000B334.c:  \u001b[mreturn CRFCashItemDatabase::CallProc_RFOnlineAuth_Jap(this, rParam);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_InitCAsyncLogInfoQEAA_NW4\u001b[m\n\u001b[38;5;15m\u001b[24;120H4ASYNC_LOG_TYPEPEBD1_NKA_14000C3F1.c:\u001b[mbool __fastcall CAsyncLogInfo::Init(CAsyncLogInfo *this, ASYNC_LOG_TYPE eType, const\n\u001b[24;120Ht char *szDirPath, const char *szTypeName, bool bAddDateFileName, unsigned int dwUpdateFileNameDelay, CLogFile *logLoadin\n\u001b[24;120Hng)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_InitCAsyncLogInfoQEAA_NW4\u001b[m\n\u001b[38;5;15m\u001b[24;120H4ASYNC_LOG_TYPEPEBD1_NKA_14000C3F1.c:  \u001b[mreturn CAsyncLogInfo::Init(this, eType, szDirPath, szTypeName, bAddDateFileName, d\n\u001b[24;120HdwUpdateFileNameDelay, logLoading);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_LogInControllServerCNetwo\u001b[m\n\u001b[38;5;15m\u001b[24;120HorkEXAEAA_NHPEADZ_14000E8DB.c: \u001b[m* Function: j_?LogInControllServer@CNetworkEX@@AEAA_NHPEAD@Z\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_LogInControllServerCNetwo\u001b[m\n\u001b[38;5;15m\u001b[24;120HorkEXAEAA_NHPEADZ_14000E8DB.c:\u001b[mbool __fastcall CNetworkEX::LogInControllServer(CNetworkEX *this, int n, char *pBuf)       \u001b[25;1H\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_LogInControllServerCNetwo\u001b[m\n\u001b[38;5;15m\u001b[24;120HorkEXAEAA_NHPEADZ_14000E8DB.c:  \u001b[mreturn CNetworkEX::LogInControllServer(this, n, pBuf);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_LogInWebAgentServerCNetwo\u001b[m\n\u001b[38;5;15m\u001b[24;120HorkEXAEAA_NHPEADZ_1400026FD.c: \u001b[m* Function: j_?LogInWebAgentServer@CNetworkEX@@AEAA_NHPEAD@Z\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_LogInWebAgentServerCNetwo\u001b[m\n\u001b[38;5;15m\u001b[24;120HorkEXAEAA_NHPEADZ_1400026FD.c:\u001b[mbool __fastcall CNetworkEX::LogInWebAgentServer(CNetworkEX *this, int n, char *pBuf)       \u001b[25;1H\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_LogInWebAgentServerCNetwo\u001b[m\n\u001b[38;5;15m\u001b[24;120HorkEXAEAA_NHPEADZ_1400026FD.c:  \u001b[mreturn CNetworkEX::LogInWebAgentServer(this, n, pBuf);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_OnRecvSession_ServerCheck\u001b[m\n\u001b[38;5;15m\u001b[24;120HkSum_RequestHACKSHEILD_P_14000C342.c: \u001b[m* Function: j_?OnRecvSession_ServerCheckSum_Request@HACKSHEILD_PARAM_ANTICP@@QEAA_N\n\u001b[24;120HNH@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_OnRecvSession_ServerCheck\u001b[m\n\u001b[38;5;15m\u001b[24;120HkSum_RequestHACKSHEILD_P_14000C342.c:\u001b[mbool __fastcall HACKSHEILD_PARAM_ANTICP::OnRecvSession_ServerCheckSum_Request(HACKSH\n\u001b[24;120HHEILD_PARAM_ANTICP *this, int nIndex)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\j_OnRecvSession_ServerCheck\u001b[m\n\u001b[38;5;15m\u001b[24;120HkSum_RequestHACKSHEILD_P_14000C342.c:  \u001b[mreturn HACKSHEILD_PARAM_ANTICP::OnRecvSession_ServerCheckSum_Request(this, nIndex)\n\u001b[24;120H);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\LogInControllServerCNetwork\u001b[m\n\u001b[38;5;15m\u001b[24;120HkEXAEAA_NHPEADZ_1401C7250.c: \u001b[m* Function: ?LogInControllServer@CNetworkEX@@AEAA_NHPEAD@Z\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\LogInControllServerCNetwork\u001b[m\n\u001b[38;5;15m\u001b[24;120HkEXAEAA_NHPEADZ_1401C7250.c:\u001b[mchar __fastcall CNetworkEX::LogInControllServer(CNetworkEX *this, int n, char *pBuf)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\LogInWebAgentServerCNetwork\u001b[m\n\u001b[38;5;15m\u001b[24;120HkEXAEAA_NHPEADZ_1401DA860.c: \u001b[m* Function: ?LogInWebAgentServer@CNetworkEX@@AEAA_NHPEAD@Z\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\LogInWebAgentServerCNetwork\u001b[m\n\u001b[38;5;15m\u001b[24;120HkEXAEAA_NHPEADZ_1401DA860.c:\u001b[mchar __fastcall CNetworkEX::LogInWebAgentServer(CNetworkEX *this, int n, char *pBuf)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\OnRecvSessionHACKSHEILD_PAR\u001b[m\n\u001b[38;5;15m\u001b[24;120HRAM_ANTICPUEAA_NPEAVCHac_140417F10.c:    \u001b[mresult = HACKSHEILD_PARAM_ANTICP::OnRecvSession_ServerCheckSum_Request(v11, nInd\n\u001b[24;120Hdex);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\OnRecvSession_ServerCheckSu\u001b[m\n\u001b[38;5;15m\u001b[24;120Hum_RequestHACKSHEILD_PAR_140417FB0.c: \u001b[m* Function: ?OnRecvSession_ServerCheckSum_Request@HACKSHEILD_PARAM_ANTICP@@QEAA_NH@\n\u001b[24;120H@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\OnRecvSession_ServerCheckSu\u001b[m\n\u001b[38;5;15m\u001b[24;120Hum_RequestHACKSHEILD_PAR_140417FB0.c:\u001b[mchar __fastcall HACKSHEILD_PARAM_ANTICP::OnRecvSession_ServerCheckSum_Request(HACKSH\n\u001b[24;120HHEILD_PARAM_ANTICP *this, int nIndex)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\UpdateLogInCompleteCUnmanne\u001b[m\n\u001b[38;5;15m\u001b[24;120HedTraderControllerQEAAEP_14034E440.c:        \u001b[mif ( !CRFWorldDatabase::Update_UnmannedTraderItemState(\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\UpdateLogInCompleteCUnmanne\u001b[m\n\u001b[38;5;15m\u001b[24;120HedTraderControllerQEAAEP_14034E440.c:        \u001b[mif ( !CRFWorldDatabase::Update_UnmannedTraderResutlInfo(\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\_CEnglandBillingMgrCallFunc\u001b[m\n\u001b[38;5;15m\u001b[24;120Hc_RFOnline_Auth__1_dtor0_140319C50.c: \u001b[m* Function: _CEnglandBillingMgr::CallFunc_RFOnline_Auth_::_1_::dtor$0\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\authentication\\_CEnglandBillingMgrCallFunc\u001b[m\n\u001b[38;5;15m\u001b[24;120Hc_RFOnline_Auth__1_dtor0_140319C50.c:\u001b[mvoid __fastcall CEnglandBillingMgr::CallFunc_RFOnline_Auth_::_1_::dtor_0(__int64 a1,\n\u001b[24;120H, __int64 a2)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\0CRFCashItemDatabaseQEAAXZ_1402F2AE\u001b[m\n\u001b[38;5;15m\u001b[24;120HE0.c: \u001b[m* Function: ??0CRFCashItemDatabase@@QEAA@XZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\0CRFCashItemDatabaseQEAAXZ_1402F2AE\u001b[m\n\u001b[38;5;15m\u001b[24;120HE0.c:\u001b[mvoid __fastcall CRFCashItemDatabase::CRFCashItemDatabase(CRFCashItemDatabase *this)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\0CRFCashItemDatabaseQEAAXZ_1402F2AE\u001b[m\n\u001b[38;5;15m\u001b[24;120HE0.c:  \u001b[mCRFCashItemDatabase *v4; // [sp+30h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\0CRFCashItemDatabaseQEAAXZ_1402F2AE\u001b[m\n\u001b[38;5;15m\u001b[24;120HE0.c:  \u001b[mCRFNewDatabase::CRFNewDatabase((CRFNewDatabase *)&amp;v4-&gt;vfptr);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\0CRFCashItemDatabaseQEAAXZ_1402F2AE\u001b[m\n\u001b[38;5;15m\u001b[24;120HE0.c:  \u001b[mv4-&gt;vfptr = (CRFNewDatabaseVtbl *)&amp;CRFCashItemDatabase::`vftable';\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\1CRFCashItemDatabaseUEAAXZ_1402F2BB\u001b[m\n\u001b[38;5;15m\u001b[24;120HB0.c: \u001b[m* Function: ??1CRFCashItemDatabase@@UEAA@XZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\1CRFCashItemDatabaseUEAAXZ_1402F2BB\u001b[m\n\u001b[38;5;15m\u001b[24;120HB0.c:\u001b[mvoid __fastcall CRFCashItemDatabase::~CRFCashItemDatabase(CRFCashItemDatabase *this)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\1CRFCashItemDatabaseUEAAXZ_1402F2BB\u001b[m\n\u001b[38;5;15m\u001b[24;120HB0.c:  \u001b[mCRFCashItemDatabase *v4; // [sp+30h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\1CRFCashItemDatabaseUEAAXZ_1402F2BB\u001b[m\n\u001b[38;5;15m\u001b[24;120HB0.c:  \u001b[mv4-&gt;vfptr = (CRFNewDatabaseVtbl *)&amp;CRFCashItemDatabase::`vftable';\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\1CRFCashItemDatabaseUEAAXZ_1402F2BB\u001b[m\n\u001b[38;5;15m\u001b[24;120HB0.c:  \u001b[mCRFNewDatabase::~CRFNewDatabase((CRFNewDatabase *)&amp;v4-&gt;vfptr);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\AddDefaultDBRecordCNormalGuildBattl\u001b[m\n\u001b[38;5;15m\u001b[24;120HleManagerGUILD_B_1403D4FD0.c:  \u001b[mif ( CRFWorldDatabase::InsertGuildBattleDefaultRecord(pkDB, 46 * v6-&gt;m_uiMapCnt) )        \u001b[25;1H\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\AddDefaultDBTableCGuildBattleSchedu\u001b[m\n\u001b[38;5;15m\u001b[24;120HuleManagerGUILD__1403DD320.c:  \u001b[mif ( CRFWorldDatabase::InsertGuildBattleScheduleDefaultRecord(pkDB, 2u, v7-&gt;m_uiMapCnt, 23\n\u001b[24;120H3, 1) )\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\AddScheduleCGuildBattleControllerQE\u001b[m\n\u001b[38;5;15m\u001b[24;120HEAAEPEADZ_1403D6AD0.c:  \u001b[mCRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&amp;pkDB-&gt;vfptr, 0);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\AddScheduleCGuildBattleControllerQE\u001b[m\n\u001b[38;5;15m\u001b[24;120HEAAEPEADZ_1403D6AD0.c:  \u001b[mif ( CRFWorldDatabase::UpdateGuildBattleInfo(\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\combat\\AddScheduleCGuildBattleControllerQE\u001b[m\n... additional lines truncated ...\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_CreateSurfaceFromBitmapCDisplayQEAAJPEAPEAVCSurf_1400108B6.c:\u001b[mHRESULT __fastcall CDisplay::CreateSurfaceFromBitmap(CDisplay *this, CS\n\u001b[10;218HSurface **ppSurface, char *strBMP, unsigned int dwDesiredWidth, unsigned int dwDesiredHeight)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_CreateSurfaceFromBitmapCDisplayQEAAJPEAPEAVCSurf_1400108B6.c:  \u001b[mreturn CDisplay::CreateSurfaceFromBitmap(this, ppSurface, strBMP, dwD\n\u001b[10;218HDesiredWidth, dwDesiredHeight);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DestoryCRFMonsterAIMgrSAXXZ_14000CA8B.c: \u001b[m* Function: j_?Destory@CRFMonsterAIMgr@@SAXXZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DestoryCRFMonsterAIMgrSAXXZ_14000CA8B.c:\u001b[mvoid CRFMonsterAIMgr::Destory(void)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DestoryCRFMonsterAIMgrSAXXZ_14000CA8B.c:  \u001b[mCRFMonsterAIMgr::Destory();\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DrawBitmapCSurfaceQEAAJPEADKKZ_14000327E.c: \u001b[m* Function: j_?DrawBitmap@CSurface@@QEAAJPEADKK@Z\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DrawBitmapCSurfaceQEAAJPEADKKZ_14000327E.c:\u001b[mHRESULT __fastcall CSurface::DrawBitmap(CSurface *this, char *strBMP, unsigned int dwDesi\n\u001b[10;218HiredWidth, unsigned int dwDesiredHeight)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DrawBitmapCSurfaceQEAAJPEADKKZ_14000327E.c:  \u001b[mreturn CSurface::DrawBitmap(this, strBMP, dwDesiredWidth, dwDesiredHeight);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DrawBitmapCSurfaceQEAAJPEAUHBITMAP__KKKKZ_140002F0E.c: \u001b[m* Function: j_?DrawBitmap@CSurface@@QEAAJPEAUHBITMAP__@@KKKK@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DrawBitmapCSurfaceQEAAJPEAUHBITMAP__KKKKZ_140002F0E.c:\u001b[mHRESULT __fastcall CSurface::DrawBitmap(CSurface *this, HBITMAP__ *hBMP, unsig\n\u001b[10;218Hgned int dwBMPOriginX, unsigned int dwBMPOriginY, unsigned int dwBMPWidth, unsigned int dwBMPHeight)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_DrawBitmapCSurfaceQEAAJPEAUHBITMAP__KKKKZ_140002F0E.c:  \u001b[mreturn CSurface::DrawBitmap(this, hBMP, dwBMPOriginX, dwBMPOriginY, dwBMPWid\n\u001b[10;218Hdth, dwBMPHeight);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_GetMonsterForcePowerRateMonsterSetInfoDataQEAAMX_14000DFF8.c: \u001b[m* Function: j_?GetMonsterForcePowerRate@MonsterSetInfoData@@QEAAMXZ\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_GetMonsterForcePowerRateMonsterSetInfoDataQEAAMX_14000DFF8.c:\u001b[mvoid __fastcall MonsterSetInfoData::GetMonsterForcePowerRate(MonsterSet\n\u001b[10;218HtInfoData *this)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_GetMonsterForcePowerRateMonsterSetInfoDataQEAAMX_14000DFF8.c:  \u001b[mMonsterSetInfoData::GetMonsterForcePowerRate(this);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_GetStateTBLCRFMonsterAIMgrQEAAAVUsPointVUsStateT_140010118.c: \u001b[m* Function: j_?GetStateTBL@CRFMonsterAIMgr@@QEAA?AV?$UsPoint@VUsStateT\n\u001b[10;218HTBL@@@@H@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_GetStateTBLCRFMonsterAIMgrQEAAAVUsPointVUsStateT_140010118.c:\u001b[mUsPoint&lt;UsStateTBL&gt; *__fastcall CRFMonsterAIMgr::GetStateTBL(CRFMonster\n\u001b[10;218HrAIMgr *this, UsPoint&lt;UsStateTBL&gt; *result, int nIndex)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_GetStateTBLCRFMonsterAIMgrQEAAAVUsPointVUsStateT_140010118.c:  \u001b[mreturn CRFMonsterAIMgr::GetStateTBL(this, result, nIndex);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_InstanceCRFMonsterAIMgrSAPEAV1XZ_1400121CA.c: \u001b[m* Function: j_?Instance@CRFMonsterAIMgr@@SAPEAV1@XZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_InstanceCRFMonsterAIMgrSAPEAV1XZ_1400121CA.c:\u001b[mCRFMonsterAIMgr *__cdecl CRFMonsterAIMgr::Instance()\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_InstanceCRFMonsterAIMgrSAPEAV1XZ_1400121CA.c:  \u001b[mreturn CRFMonsterAIMgr::Instance();\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_LoadMonsterSetInfoDataQEAAHPEBDZ_140006AB4.c:\u001b[mint __fastcall MonsterSetInfoData::Load(MonsterSetInfoData *this, const char *strFileNa\n\u001b[10;218Hame)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_LoadMonsterSetInfoDataQEAAHPEBDZ_140006AB4.c:  \u001b[mreturn MonsterSetInfoData::Load(this, strFileName);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeon_14001007D.c: \u001b[m* Function: j_?mc_AddMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQue\n\u001b[10;218HestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeon_14001007D.c:\u001b[mbool __fastcall mc_AddMonster(strFILE *fstr, CDarkHoleDungeonQuestSetup\n\u001b[10;218Hp *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDung_14000C9F0.c: \u001b[m* Function: j_?mc_ChangeMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeon\n\u001b[10;218HnQuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDung_14000C9F0.c:\u001b[mbool __fastcall mc_ChangeMonster(strFILE *fstr, CDarkHoleDungeonQuestSe\n\u001b[10;218Hetup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkH_140004ABB.c: \u001b[m* Function: j_?mc_RespawnMonsterOption@@YA_NPEAUstrFILE@@PEAVCDarkHole\n\u001b[10;218HeDungeonQuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkH_140004ABB.c:\u001b[mbool __fastcall mc_RespawnMonsterOption(strFILE *fstr, CDarkHoleDungeon\n\u001b[10;218HnQuestSetup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDun_140002BC1.c: \u001b[m* Function: j_?mc_RespawnMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeo\n\u001b[10;218HonQuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDun_140002BC1.c:\u001b[mbool __fastcall mc_RespawnMonster(strFILE *fstr, CDarkHoleDungeonQuestS\n\u001b[10;218HSetup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_OnButtonMonsterCGameServerViewQEAAXXZ_14000A367.c: \u001b[m* Function: j_?OnButtonMonster@CGameServerView@@QEAAXXZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_OnButtonMonsterCGameServerViewQEAAXXZ_14000A367.c:\u001b[mvoid __fastcall CGameServerView::OnButtonMonster(CGameServerView *this)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_OnButtonMonsterCGameServerViewQEAAXXZ_14000A367.c:  \u001b[mCGameServerView::OnButtonMonster(this);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDunge_1400066DB.c: \u001b[m* Function: j_?qc_monsterGroup@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQ\n\u001b[10;218HQuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDunge_1400066DB.c:\u001b[mbool __fastcall qc_monsterGroup(strFILE *fstr, CDarkHoleDungeonQuestSet\n\u001b[10;218Htup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140011D5B.c: \u001b[m* Function: j_?qc_UseMap@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSe\n\u001b[10;218Hetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140011D5B.c:\u001b[mbool __fastcall qc_UseMap(strFILE *fstr, CDarkHoleDungeonQuestSetup *pS\n\u001b[10;218HSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_RequestElanMapUserForceMoveHQCMoveMapLimitManage_1400095BB.c: \u001b[m* Function: j_?RequestElanMapUserForceMoveHQ@CMoveMapLimitManager@@QEA\n\u001b[10;218HAAEXZ\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_RequestElanMapUserForceMoveHQCMoveMapLimitManage_1400095BB.c:\u001b[mchar __fastcall CMoveMapLimitManager::RequestElanMapUserForceMoveHQ(CMo\n\u001b[10;218HoveMapLimitManager *this)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_RequestElanMapUserForceMoveHQCMoveMapLimitManage_1400095BB.c:  \u001b[mreturn CMoveMapLimitManager::RequestElanMapUserForceMoveHQ(this);    \u001b[11;1H\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_size_server_notify_inform_zoneQEAAHXZ_140005187.c: \u001b[m* Function: j_?size@_server_notify_inform_zone@@QEAAHXZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_size_server_notify_inform_zoneQEAAHXZ_140005187.c:\u001b[mint __fastcall _server_notify_inform_zone::size(_server_notify_inform_zone *this) \u001b[11;1H\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j_size_server_notify_inform_zoneQEAAHXZ_140005187.c:  \u001b[mreturn _server_notify_inform_zone::size(this);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j__DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCS_140001799.c: \u001b[m* Function: j_?_DrawObject@CMapDisplay@@AEAAJPEAVCGameObject@@PEAVCSur\n\u001b[10;218Hrface@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j__DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCS_140001799.c:\u001b[mHRESULT __fastcall CMapDisplay::_DrawObject(CMapDisplay *this, CGameObj\n\u001b[10;218Hject *pObj, CSurface *pSF)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j__GCRFMonsterAIMgrQEAAPEAXIZ_14000934A.c: \u001b[m* Function: j_??_GCRFMonsterAIMgr@@QEAAPEAXI@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j__GCRFMonsterAIMgrQEAAPEAXIZ_14000934A.c:\u001b[mvoid *__fastcall CRFMonsterAIMgr::`scalar deleting destructor'(CRFMonsterAIMgr *this, unsign\n\u001b[10;218Hned int a2)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\j__GCRFMonsterAIMgrQEAAPEAXIZ_14000934A.c:  \u001b[mreturn CRFMonsterAIMgr::`scalar deleting destructor'(this, a2);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LightMappingTex1YAXPEAU_BSP_MAT_GROUPZ_1404EFAF0.c:  \u001b[mIUnknownVtbl *v4; // rbx@3\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LightMappingTex1YAXPEAU_BSP_MAT_GROUPZ_1404EFAF0.c:    \u001b[mv5 = GetLightMapSurface(v3);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LightMappingTex1YAXPEAU_BSP_MAT_GROUPZ_1404EFAF0.c:  \u001b[m((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64, signed _\n\u001b[10;218H__int64))v2-&gt;vfptr[21].QueryInterface)(\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadEventSetCMonsterEventSetQEAA_NPEADZ_1402A79E0.c:\u001b[m\u001b[12Cif ( !strcmp_0(v5, \&quot;UNKNOWN\&quot;) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadEventSetCMonsterEventSetQEAA_NPEADZ_1402A79E0.c:\u001b[m\u001b[14Cv44-&gt;bUnknownMap = 1;\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadEventSetCMonsterEventSetQEAA_NPEADZ_1402A79E0.c:\u001b[m\u001b[14Cv44-&gt;bUnknownMap = 0;\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadEventSetCMonsterEventSetQEAA_NPEADZ_1402A79E0.c:\u001b[m\u001b[12Cv44-&gt;pMonsterFld = v47;\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadMapsCMapOperationAEAA_NXZ_140196750.c:    \u001b[mif ( !CMapData::OpenMap(&amp;v19-&gt;m_Map[dwIndex], pMapSet-&gt;m_strFileName, pMapSet, 1) )     \u001b[11;1H\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadMapsCMapOperationAEAA_NXZ_140196750.c:      \u001b[mMyMessageBox(\&quot;Map Load Error\&quot;, \&quot;%s - Read Error\&quot;, pMapSet-&gt;m_strFileName);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadMapsCMapOperationAEAA_NXZ_140196750.c:      \u001b[mMyMessageBox(\&quot;ItemStore Load Error\&quot;, \&quot;LoadMaps() : pMapItemStoreList-&gt;CreateStores(%s)\n\u001b[10;218H)\&quot;, pMapSet-&gt;m_strFileName);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadMonsterSetInfoDataQEAAHPEBDZ_14015C7E0.c:\u001b[m__int64 __fastcall MonsterSetInfoData::Load(MonsterSetInfoData *this, const char *strFile\n\u001b[10;218HeName)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadMonsterSetInfoDataQEAAHPEBDZ_14015C7E0.c:  \u001b[mv38 = strFileName;\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadMonsterSetInfoDataQEAAHPEBDZ_14015C7E0.c:  \u001b[mv37-&gt;m_fMonsterForcePowerRate = (float)(signed int)GetPrivateProfileIntA(\&quot;Common\&quot;, \&quot;Mon\n\u001b[10;218HnsterForcePowerRate\&quot;, 40, v38);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c:  \u001b[mv25-&gt;m_bFreeServer = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;FreeServer\&quot;, 0, \&quot;..\\\\WorldInfo\\\n\u001b[10;218H\\\\WorldInfo.ini\&quot;);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c:  \u001b[mv25-&gt;m_byWorldType = GetPrivateProfileIntA(\&quot;System\&quot;, \&quot;ServerType\&quot;, 2, \&quot;..\\\\WorldInfo\\\n\u001b[10;218H\\\\WorldInfo.ini\&quot;);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c:  \u001b[mGetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ReleaseType\&quot;, \&quot;X\&quot;, &amp;ReturnedString, 9u, \&quot;..\\\\\n\u001b[10;218H\\WorldInfo\\\\WorldInfo.ini\&quot;);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c:        \u001b[m\&quot;WorldInfo.ini\\r\\n[ServerMode]\\r\\nReleaseType = %s Invalid!!\&quot;,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c:  \u001b[mGetPrivateProfileStringA(\&quot;ServerMode\&quot;, \&quot;ExcuteService\&quot;, \&quot;X\&quot;, &amp;Str1, 6u, \&quot;..\\\\WorldInf\n\u001b[10;218Hfo\\\\WorldInfo.ini\&quot;);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.c:        \u001b[m\&quot;WorldInfo.ini\\r\\n[ServerMode]\\r\\nExcuteService = %s Invalid!!\&quot;,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeonQu_140275730.c: \u001b[m* Function: ?mc_AddMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuest\n\u001b[10;218HtSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeonQu_140275730.c:\u001b[mbool __fastcall mc_AddMonster(strFILE *fstr, CDarkHoleDungeonQuestSetup\n\u001b[10;218Hp *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeonQu_140275730.c:  \u001b[mstrFILE *fstra; // [sp+E0h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:\u001b[m * Function: ?mc_ChangeMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQu\n\u001b[10;218HuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:\u001b[mbool __fastcall mc_ChangeMonster(strFILE *fstr, CDarkHoleDungeonQuestSe\n\u001b[10;218Hetup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:  \u001b[mstrFILE *fstra; // [sp+260h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:  \u001b[mif ( strFILE::word(fstra, &amp;poutszWord) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:    \u001b[mif ( strFILE::word(fstra, &amp;szRecordCode) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:      \u001b[mif ( strFILE::word(fstra, Str) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:            \u001b[mpSetupa-&gt;m_pCurLoadMission-&gt;pChangeMonster[v15]-&gt;pMonsterFl\n\u001b[10;218HldA = (_monster_fld *)v11;\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.c:            \u001b[mpSetupa-&gt;m_pCurLoadMission-&gt;pChangeMonster[v15]-&gt;pMonsterFl\n\u001b[10;218HldB = (_monster_fld *)v12;\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c: \u001b[m* Function: ?mc_RespawnMonsterOption@@YA_NPEAUstrFILE@@PEAVCDarkHoleDu\n\u001b[10;218HungeonQuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c:\u001b[mbool __fastcall mc_RespawnMonsterOption(strFILE *fstr, CDarkHoleDungeon\n\u001b[10;218HnQuestSetup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c:  \u001b[mstrFILE *fstra; // [sp+230h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c:  \u001b[mif ( strFILE::word(fstra, &amp;poutszWord) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c:    \u001b[mif ( strFILE::word(fstra, &amp;v8) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.c:      \u001b[mif ( strFILE::word(fstra, &amp;Source) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c: \u001b[m* Function: ?mc_RespawnMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQ\n\u001b[10;218HQuestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c:\u001b[mbool __fastcall mc_RespawnMonster(strFILE *fstr, CDarkHoleDungeonQuestS\n\u001b[10;218HSetup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c:  \u001b[mstrFILE *fstra; // [sp+270h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c:        \u001b[mif ( strFILE::word(fstra, &amp;poutszWord) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c:\u001b[m\u001b[12Cif ( strFILE::word(fstra, &amp;pnoutVal) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c:\u001b[m\u001b[14Cif ( strFILE::word(fstra, &amp;Str1) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.c:\u001b[m\u001b[18Cif ( strFILE::word(fstra, &amp;v13) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\OnButtonMonsterCGameServerViewQEAAXXZ_14002B190.c: \u001b[m* Function: ?OnButtonMonster@CGameServerView@@QEAAXXZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\OnButtonMonsterCGameServerViewQEAAXXZ_14002B190.c:\u001b[mvoid __fastcall CGameServerView::OnButtonMonster(CGameServerView *this)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\OnDisplayCMapDisplayQEAA_NPEAVCMapDataGZ_14019EEB0.c:    \u001b[mv7 = CMapDisplay::InitSurface(v8, pMap);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.c:      \u001b[m\&quot;!!Server type is wrong!!(AC:%d)(ZO:%d)\&quot;,\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.c:    \u001b[mWriteServerStartHistory(\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.c:      \u001b[m\&quot;ServerType is Wrong ==&gt; AccountServer(%d) != ZoneServer(%d)\&quot;,\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c:  \u001b[mWriteServerStartHistory(\&quot;DBInit Begin &gt;&gt; name: %s, ip: %s\&quot;, pszDBNamea,\n\u001b[10;218H, pszDBIPa);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c:    \u001b[mWriteServerStartHistory(\&quot;DBInit Complete &gt;&gt;\&quot;);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.c:    \u001b[mWriteServerStartHistory(\&quot;DBInit Fail &gt;&gt;\&quot;);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.c: \u001b[m* Function: ?qc_monsterGroup@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQue\n\u001b[10;218HestSetup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.c:\u001b[mbool __fastcall qc_monsterGroup(strFILE *fstr, CDarkHoleDungeonQuestSet\n\u001b[10;218Htup *pSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.c:  \u001b[mstrFILE *fstra; // [sp+1C0h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.c:  \u001b[mif ( strFILE::word(fstra, &amp;poutszWord) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.c:    \u001b[mif ( strFILE::word(fstra, &amp;szRecordCode) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.c: \u001b[m* Function: ?qc_UseMap@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetu\n\u001b[10;218Hup@@PEAD@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.c:\u001b[mbool __fastcall qc_UseMap(strFILE *fstr, CDarkHoleDungeonQuestSetup *pS\n\u001b[10;218HSetup, char *pszoutErrMsg)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.c:  \u001b[mstrFILE *fstra; // [sp+E0h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.c:  \u001b[mif ( strFILE::word(fstra, &amp;poutszWord) )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v7; // [sp+28h] [bp-80h]@5\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v8; // [sp+30h] [bp-78h]@5\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v9; // [sp+38h] [bp-70h]@10\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v10; // [sp+40h] [bp-68h]@10\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v11; // [sp+48h] [bp-60h]@19\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v12; // [sp+50h] [bp-58h]@19\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v13; // [sp+58h] [bp-50h]@26\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v14; // [sp+60h] [bp-48h]@26\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v15; // [sp+68h] [bp-40h]@31\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:  \u001b[mCSurface *v16; // [sp+70h] [bp-38h]@31\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:      \u001b[mv17 = CSurface::`scalar deleting destructor'(v7, 1u);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:      \u001b[mv18 = CSurface::`scalar deleting destructor'(v9, 1u);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:\u001b[m\u001b[10Cv19 = CSurface::`scalar deleting destructor'(v11, 1u);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:      \u001b[mv20 = CSurface::`scalar deleting destructor'(v13, 1u);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.c:      \u001b[mv21 = CSurface::`scalar deleting destructor'(v15, 1u);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\RequestElanMapUserForceMoveHQCMoveMapLimitManagerQ_140284700.c: \u001b[m* Function: ?RequestElanMapUserForceMoveHQ@CMoveMapLimitManager@@QEAAE\n\u001b[10;218HEXZ\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\RequestElanMapUserForceMoveHQCMoveMapLimitManagerQ_140284700.c:\u001b[mchar __fastcall CMoveMapLimitManager::RequestElanMapUserForceMoveHQ(CMo\n\u001b[10;218HoveMapLimitManager *this)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\SetLightMapYAXJZ_1404EDF30.c:  \u001b[mIUnknownVtbl *v4; // rbx@3\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\SetLightMapYAXJZ_1404EDF30.c:    \u001b[mv5 = GetLightMapSurface(v1);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:  \u001b[mCSurface v7; // [sp+40h] [bp-C8h]@9\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:      \u001b[m((void (__fastcall *)(IDirectDrawSurface7 *, IDirectDrawPalette *\n\u001b[10;218H*))v11-&gt;m_pddsFrontBuffer-&gt;vfptr[10].AddRef)(\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:    \u001b[mCSurface::CSurface(&amp;v7);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:    \u001b[mCSurface::Create(&amp;v7, v11-&gt;m_pddsBackBuffer);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:    \u001b[mif ( CSurface::DrawBitmap(&amp;v7, hBMP, 0, 0, 0, 0) &gt;= 0 )\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:      \u001b[mCSurface::~CSurface(&amp;v7);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.c:      \u001b[mCSurface::~CSurface(&amp;v7);\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\size_server_notify_inform_zoneQEAAHXZ_14011F1E0.c: \u001b[m* Function: ?size@_server_notify_inform_zone@@QEAAHXZ\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\size_server_notify_inform_zoneQEAAHXZ_14011F1E0.c:\u001b[msigned __int64 __fastcall _server_notify_inform_zone::size(_server_notify_inform_zon\n\u001b[10;218Hne *this)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\StartRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6B90.c:      \u001b[msprintf(Dest, \&quot;now actived\&quot;);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\StartRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6B90.c:        \u001b[mv6 = v18-&gt;pMonsterFld-&gt;m_strCode;\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\StartRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6B90.c:          \u001b[mpEventRespawn-&gt;State.MonInfo[v16++].pMonFld = v18-&gt;pMonsterFld\n\u001b[10;218Hd;\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\StopEventSetCMonsterEventSetQEAA_NPEAD0Z_1402A8870.c:\u001b[m\u001b[10Csprintf(Dest, \&quot;now stoped\&quot;);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\StopRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6E50.c:      \u001b[msprintf(Dest, \&quot;now stoped\&quot;);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\SubProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAEHP_1403A4D10.c:          \u001b[mif ( CGameObject::GetCurSecNum(v12) == -1 || v12-&gt;m_bMapLoadi\n\u001b[10;218Hing )\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\UnLightMappingTex1YAXXZ_1404EFB90.c:  \u001b[m((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64))v0-&gt;vfptr[21].QueryInte\n\u001b[10;218Herface)(\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_CDisplayCreateSurfaceFromBitmap__1_dtor0_140433DE0.c: \u001b[m* Function: _CDisplay::CreateSurfaceFromBitmap_::_1_::dtor$0\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_CDisplayCreateSurfaceFromBitmap__1_dtor0_140433DE0.c:\u001b[mvoid __fastcall CDisplay::CreateSurfaceFromBitmap_::_1_::dtor_0(__int64 a1, __in\n\u001b[10;218Hnt64 a2)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_CDisplayShowBitmap__1_dtor0_140434370.c:  \u001b[mCSurface::~CSurface((CSurface *)(a2 + 64));\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_CRFMonsterAIMgrInstance__1_dtor0_14014C190.c: \u001b[m* Function: _CRFMonsterAIMgr::Instance_::_1_::dtor$0\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_CRFMonsterAIMgrInstance__1_dtor0_14014C190.c:\u001b[mvoid __fastcall CRFMonsterAIMgr::Instance_::_1_::dtor_0(__int64 a1, __int64 a2)\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c: \u001b[m* Function: ?_DrawObject@CMapDisplay@@AEAAJPEAVCGameObject@@PEAVCSurfa\n\u001b[10;218Hace@@@Z\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:\u001b[m__int64 __fastcall CMapDisplay::_DrawObject(CMapDisplay *this, CGameObj\n\u001b[10;218Hject *pObj, CSurface *pSF)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mIDirectDrawSurface7 *v6; // rax@14\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mIDirectDrawSurface7 *v7; // rax@17\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mIDirectDrawSurface7 *v12; // [sp+38h] [bp-30h]@14\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mIUnknownVtbl *v13; // [sp+40h] [bp-28h]@14\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mIDirectDrawSurface7 *v14; // [sp+48h] [bp-20h]@17\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mIUnknownVtbl *v15; // [sp+50h] [bp-18h]@17\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:  \u001b[mCSurface *v18; // [sp+80h] [bp+18h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:\u001b[m\u001b[10Cv12 = CSurface::GetDDrawSurface(v16-&gt;m_pSFMap);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:\u001b[m\u001b[10Cv6 = CSurface::GetDDrawSurface(v18);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:          \u001b[mv11 = ((int (__fastcall *)(IDirectDrawSurface7 *, _QWORD, _QW\n\u001b[10;218HWORD, IDirectDrawSurface7 *))v13[2].AddRef)(\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:        \u001b[mv14 = CSurface::GetDDrawSurface(v16-&gt;m_pSFMap);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:        \u001b[mv7 = CSurface::GetDDrawSurface(v18);\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.c:        \u001b[mv11 = ((int (__fastcall *)(IDirectDrawSurface7 *, _QWORD, _QWOR\n\u001b[10;218HRD, IDirectDrawSurface7 *))v15[2].AddRef)(\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_GCRFMonsterAIMgrQEAAPEAXIZ_140203390.c: \u001b[m* Function: ??_GCRFMonsterAIMgr@@QEAAPEAXI@Z\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_GCRFMonsterAIMgrQEAAPEAXIZ_140203390.c:\u001b[mCRFMonsterAIMgr *__fastcall CRFMonsterAIMgr::`scalar deleting destructor'(CRFMonsterAIMgr *thi\n\u001b[10;218His, int a2)\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_GCRFMonsterAIMgrQEAAPEAXIZ_140203390.c:  \u001b[mCRFMonsterAIMgr *v6; // [sp+30h] [bp+8h]@1\u001b[K\n\u001b[38;5;15mD:/_2.NexusPro_Project2025_RFOnlineGameguard\\Decompiled Source Code - IDA Pro\\world\\_GCRFMonsterAIMgrQEAAPEAXIZ_140203390.c:  \u001b[mCRFMonsterAIMgr::~CRFMonsterAIMgr(v6);\u001b[K\n\n        &lt;/stdout&gt;\n        &lt;stderr&gt;\n        \n        &lt;/stderr&gt;&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false,&quot;isAgentConversation&quot;:true,&quot;baselineTimestamp&quot;:0},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;4c8cc4e0-a0e7-4202-bb53-86be77c71542&quot;}},&quot;agentExecutionMode&quot;:&quot;auto&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>