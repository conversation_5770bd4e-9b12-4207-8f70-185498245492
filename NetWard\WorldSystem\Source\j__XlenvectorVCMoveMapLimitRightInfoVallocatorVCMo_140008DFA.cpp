#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_<PERSON>len@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@KAXXZ
 * Address: 0x140008DFA

void __noreturn std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_<PERSON>len()
{
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_<PERSON>len();
}
