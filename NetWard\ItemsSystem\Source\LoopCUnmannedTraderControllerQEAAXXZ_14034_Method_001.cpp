#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Loop@CUnmannedTraderController@@QEAAXXZ
 * Address: 0x14034CD60

void  CUnmannedTraderController::Loop(CUnmannedTraderController *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  CUnmannedTraderScheduler *v3; // rax@4
  CUnmannedTraderTaxRateManager *v4; // rax@4
  int64_t v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderController *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -*********;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v3 = CUnmannedTraderScheduler::Instance();
  CUnmannedTraderScheduler::Loop(v3);
  v4 = CUnmannedTraderTaxRateManager::Instance();
  CUnmannedTraderTaxRateManager::Loop(v4);
  CUnmannedTraderTradeInfo::Loop(&v6->m_kTradeInfo);
  CUnmannedTraderLazyCleaner::Loop(&v6->m_kLazyCleaner);
}
