#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SelectCleanUp@CRFNewDatabase@@QEAAXPEAD@Z
 * Address: 0x140485300

void  CRFNewDatabase::SelectCleanUp(CRFNewDatabase *this, char *strQuery)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-28h]@1
  CRFNewDatabase *v5; // [sp+30h] [bp+8h]@1
  char *v6; // [sp+38h] [bp+10h]@1

  v6 = strQuery;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v5->m_hStmtSelect )
    SQLCloseCursor_0(v5->m_hStmtSelect);
  if ( v5->m_bSaveDBLog )
    CRFNewDatabase::FmtLog(v5, "%s Success", v6);
}
