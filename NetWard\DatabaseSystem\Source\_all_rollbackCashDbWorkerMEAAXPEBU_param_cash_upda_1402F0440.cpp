#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_all_rollback@CashDbWorker@@MEAAXPEBU_param_cash_update@@@Z
 * Address: 0x1402F0440

void  CashDbWorker::_all_rollback(CashDbWorker *this, _param_cash_update *psheet)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char *v4; // rax@7
  int v5; // eax@9
  int64_t v6; // [sp+0h] [bp-4A8h]@1
  char *v7; // [sp+20h] [bp-488h]@7
  _param_cash_rollback v8; // [sp+40h] [bp-468h]@4
  int j; // [sp+474h] [bp-34h]@4
  char *v10; // [sp+478h] [bp-30h]@6
  int64_t v11; // [sp+488h] [bp-20h]@4
  CLogFile *v12; // [sp+490h] [bp-18h]@7
  unsigned int64_t v13; // [sp+498h] [bp-10h]@4
  CashDbWorker *v14; // [sp+4B0h] [bp+8h]@1
  _param_cash_update *v15; // [sp+4B8h] [bp+10h]@1

  v15 = psheet;
  v14 = this;
  v2 = &v6;
  for ( i = 296i64; i; --i )
  {
    *(uint32_t*)v2 = -*********;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v11 = -2i64;
  v13 = (unsigned int64_t)&v6 ^ _security_cookie;
  _param_cash_rollback::_param_cash_rollback(
    &v8,
    psheet->in_dwAccountSerial,
    psheet->in_dwAvatorSerial,
    psheet->in_wSockIndex);
  strcpy_s(v8.in_szAcc, 0xDui64, v15->in_szAcc);
  for ( j = 0; j < v15->in_nNum10; ++j )
  {
    v10 = &v15->in_item[(signed int64_t)j].byRet;
    if ( !*v10 )
    {
      v8.data[v8.in_byNum].in_lnUID = v15->in_item[(signed int64_t)j].in_lnUID;
      v8.data[v8.in_byNum].in_nPrice = v15->in_item[(signed int64_t)j].in_nPrice;
      v8.data[v8.in_byNum].in_nDiscount = v15->in_item[(signed int64_t)j].in_nDiscount;
      v8.data[v8.in_byNum].in_byOverlapNum = v15->in_item[(signed int64_t)j].in_byOverlapNum;
      strcpy_s(v8.data[v8.in_byNum].in_strItemCode, 8ui64, v10 + 1);
      ++v8.in_byNum;
      v4 = GetItemKorName((unsigned int8_t)v10[10], *((uint16_t*)v10 + 6));
      v12 = v14->_kLogger;
      v7 = v4;
      CLogFile::Write(v14->_kLogger, "Rollback >> %I64d-%s(%s)", *((uint64_t*)v10 + 4), v10 + 1);
    }
  }
  v5 = _param_cash_rollback::size(&v8);
  CashDbWorker::PushTask(v14, 2, (char *)&v8, v5);
  _param_cash_rollback::~_param_cash_rollback(&v8);
}
