#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Select_MacroData@CRFWorldDatabase@@QEAAEKPEAU_AIOC_A_MACRODATA@@@Z
 * Address: 0x1404A52F0

char  CRFWorldDatabase::Select_MacroData(CRFWorldDatabase *this, unsigned int dwSerial, _AIOC_A_MACRODATA *pMacro)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@8
  int64_t v6; // [sp+0h] [bp-718h]@1
  void *SQLStmt; // [sp+20h] [bp-6F8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-6F0h]@17
  SQLLEN v9; // [sp+38h] [bp-6E0h]@17
  int16_t v10; // [sp+44h] [bp-6D4h]@9
  char Dest; // [sp+60h] [bp-6B8h]@4
  int j; // [sp+164h] [bp-5B4h]@4
  int k; // [sp+168h] [bp-5B0h]@4
  int Dst; // [sp+178h] [bp-5A0h]@15
  char TargetValue; // [sp+17Ch] [bp-59Ch]@17
  char v16; // [sp+180h] [bp-598h]@17
  int v17; // [sp+184h] [bp-594h]@17
  char v18; // [sp+188h] [bp-590h]@17
  char v19; // [sp+18Ch] [bp-58Ch]@17
  int v20; // [sp+1A8h] [bp-570h]@15
  char v21; // [sp+1ACh] [bp-56Ch]@17
  char v22; // [sp+1B0h] [bp-568h]@17
  char v23; // [sp+1B4h] [bp-564h]@17
  char v24; // [sp+1B8h] [bp-560h]@17
  char v25; // [sp+1BCh] [bp-55Ch]@17
  char v26; // [sp+1C0h] [bp-558h]@17
  char v27; // [sp+1C4h] [bp-554h]@17
  char v28; // [sp+1C8h] [bp-550h]@17
  char v29; // [sp+1CCh] [bp-54Ch]@17
  char Source[256]; // [sp+1F0h] [bp-528h]@15
  char v31; // [sp+2F0h] [bp-428h]@17
  char v32; // [sp+3F0h] [bp-328h]@17
  char v33; // [sp+4F0h] [bp-228h]@17
  char v34; // [sp+5F0h] [bp-128h]@17
  unsigned int64_t v35; // [sp+700h] [bp-18h]@4
  CRFWorldDatabase *v36; // [sp+720h] [bp+8h]@1
  _AIOC_A_MACRODATA *v37; // [sp+730h] [bp+18h]@1

  v37 = pMacro;
  v36 = this;
  v3 = &v6;
  for ( i = 452i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v35 = (unsigned int64_t)&v6 ^ _security_cookie;
  j = 0;
  k = 0;
  sprintf(
    &Dest,
    "Select hp, fp, sp, action0, action1, action2, action3, action4, action5, action6, action7, action8, action9, chat0, "
    "chat1, chat2, chat3, chat4, hpvalue, fpvalue, spvalue from tbl_macro where serial=%d order by belt asc",
    dwSerial);
  if ( v36->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v36->vfptr, &Dest);
  if ( v36->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v36->vfptr) )
  {
    v10 = SQLExecDirectA_0(v36->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v36->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v36->vfptr, v10, &Dest, "_SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v36->vfptr, v10, v36->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      for ( j = 0; ; ++j )
      {
        memset_0(&Dst, 0, 0x18ui64);
        memset_0(&v20, 0, 0x28ui64);
        memset_0(Source, 0, 0x500ui64);
        v10 = SQLFetch_0(v36->m_hStmtSelect);
        if ( v10 )
        {
          if ( v10 != 1 )
            break;
        }
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 1u, 4, &Dst, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 2u, 4, &TargetValue, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 3u, 4, &v16, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 4u, 4, &v20, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 5u, 4, &v21, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 6u, 4, &v22, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 7u, 4, &v23, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 8u, 4, &v24, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 9u, 4, &v25, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 0xAu, 4, &v26, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 0xBu, 4, &v27, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 0xCu, 4, &v28, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 0xDu, 4, &v29, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)255;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 0xEu, 1, Source, 255i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)255;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 0xFu, 1, &v31, 255i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)255;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 0x10u, 1, &v32, 255i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)255;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 0x11u, 1, &v33, 255i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)255;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 0x12u, 1, &v34, 255i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 0x13u, 4, &v17, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 0x14u, 4, &v18, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v36->m_hStmtSelect, 0x15u, 4, &v19, 0i64, &v9);
        if ( j < 1 )
        {
          for ( k = 0; k < 3; ++k )
          {
            v37->mcr_Potion[j].Potion[k] = *(&Dst + k);
            v37->mcr_Potion[j].PotionValue[k] = *(&v17 + k);
          }
        }
        if ( j < 3 )
        {
          for ( k = 0; k < 10; ++k )
            v37->mcr_Action[j].Action[k] = *(&v20 + k);
        }
        if ( j < 2 )
        {
          for ( k = 0; k < 5; ++k )
            strcpy_0((char *)&v37->mcr_Chat[j] + 256 * (signed int64_t)k, &Source[256 * (signed int64_t)k]);
        }
      }
      if ( j )
      {
        if ( v10 && v10 != 1 && (j != 3 || v10 != 100) )
        {
          SQLStmt = v36->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v36->vfptr, v10, &Dest, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v36->vfptr, v10, v36->m_hStmtSelect);
          if ( v36->m_hStmtSelect )
            SQLCloseCursor_0(v36->m_hStmtSelect);
          result = 1;
        }
        else
        {
          if ( v36->m_hStmtSelect )
            SQLCloseCursor_0(v36->m_hStmtSelect);
          if ( v36->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v36->vfptr, "%s Success", &Dest);
          result = 0;
        }
      }
      else
      {
        if ( v36->m_hStmtSelect )
          SQLCloseCursor_0(v36->m_hStmtSelect);
        result = 2;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v36->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
