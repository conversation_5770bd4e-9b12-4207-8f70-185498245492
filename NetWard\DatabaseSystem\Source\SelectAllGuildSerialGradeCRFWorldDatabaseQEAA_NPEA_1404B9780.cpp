#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SelectAllGuildSerialGrade@CRFWorldDatabase@@QEAA_NPEAK0PEAE@Z
 * Address: 0x1404B9780

char  CRFWorldDatabase::SelectAllGuildSerialGrade@<al>(CRFWorldDatabase *this@<rcx>, unsigned int *pdwCount@<rdx>, unsigned int *pdwSerial@<r8>, char *pbyGrade@<r9>, signed int64_t a5@<rax>)
{
  void *v5; // rsp@1
  int64_t *v6; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@8
  unsigned int *v9; // rax@16
  int64_t v10; // rax@16
  int64_t v11; // [sp-20h] [bp-2888h]@1
  void *SQLStmt; // [sp+0h] [bp-2868h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+8h] [bp-2860h]@16
  SQLLEN v14; // [sp+18h] [bp-2850h]@16
  int16_t v15; // [sp+24h] [bp-2844h]@9
  char Dest; // [sp+40h] [bp-2828h]@4
  unsigned int64_t v17; // [sp+2850h] [bp-18h]@4
  CRFWorldDatabase *v18; // [sp+2870h] [bp+8h]@1
  unsigned int *v19; // [sp+2878h] [bp+10h]@1
  unsigned int *v20; // [sp+2880h] [bp+18h]@1
  char *v21; // [sp+2888h] [bp+20h]@1

  v21 = pbyGrade;
  v20 = pdwSerial;
  v19 = pdwCount;
  v18 = this;
  v5 = alloca(a5);
  v6 = &v11;
  for ( i = 2592i64; i; --i )
  {
    *(uint32_t*)v6 = -858993460;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  v17 = (unsigned int64_t)&v11 ^ _security_cookie;
  *pdwCount = 0;
  sprintf(&Dest, "select top %u [Serial], [Grade] from [dbo].[tbl_guild] where [DCK]=0 order by serial", 500i64);
  if ( v18->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v18->vfptr, &Dest);
  if ( v18->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v18->vfptr) )
  {
    v15 = SQLExecDirectA_0(v18->m_hStmtSelect, &Dest, -3);
    if ( v15 && v15 != 1 )
    {
      if ( v15 == 100 )
      {
        result = 1;
      }
      else
      {
        SQLStmt = v18->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v15, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v15, v18->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      while ( 1 )
      {
        v15 = SQLFetch_0(v18->m_hStmtSelect);
        if ( v15 )
        {
          if ( v15 != 1 )
            break;
        }
        v9 = &v20[*v19];
        StrLen_or_IndPtr = &v14;
        SQLStmt = 0i64;
        v15 = SQLGetData_0(v18->m_hStmtSelect, 1u, 4, v9, 0i64, &v14);
        v10 = *v19;
        StrLen_or_IndPtr = &v14;
        SQLStmt = 0i64;
        v15 = SQLGetData_0(v18->m_hStmtSelect, 2u, -15, &v21[v10], 0i64, &v14);
        ++*v19;
      }
      if ( v18->m_hStmtSelect )
        SQLCloseCursor_0(v18->m_hStmtSelect);
      if ( v18->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v18->vfptr, "%s Success", &Dest);
      result = 1;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v18->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
