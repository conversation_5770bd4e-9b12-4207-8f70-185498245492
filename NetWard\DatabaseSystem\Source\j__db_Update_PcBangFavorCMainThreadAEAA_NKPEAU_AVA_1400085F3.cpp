#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_db_Update_PcBangFavor@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEADH@Z
 * Address: 0x1400085F3

bool  CMainThread::_db_Update_PcBangFavor(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *szPcBangFavorQuery, int nSize)
{
  return CMainThread::_db_Update_PcBangFavor(this, dwSerial, pNewData, pOldData, szPcBangFavorQuery, nSize);
}
