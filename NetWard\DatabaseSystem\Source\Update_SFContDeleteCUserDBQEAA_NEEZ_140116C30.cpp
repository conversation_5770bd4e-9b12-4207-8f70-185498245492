#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_SFContDelete@CUserDB@@QEAA_NEE@Z
 * Address: 0x140116C30

char  CUserDB::Update_SFContDelete(CUserDB *this, char byContCode, char bySlotIndex)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@5
  CUserDB *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  char v10; // [sp+50h] [bp+18h]@1

  v10 = bySlotIndex;
  v9 = byContCode;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned int8_t)byContCode < 2 )
  {
    if ( (signed int)(unsigned int8_t)bySlotIndex < 8 )
    {
      if ( _SFCONT_DB_BASE::_LIST::IsFilled((_SFCONT_DB_BASE::_LIST *)&v8->m_AvatorData.dbSfcont + 8
                                                                                                 * (unsigned int8_t)byContCode
                                                                                                 + (unsigned int8_t)bySlotIndex) )
      {
        _SFCONT_DB_BASE::_LIST::Init((_SFCONT_DB_BASE::_LIST *)&v8->m_AvatorData.dbSfcont + 8 * (unsigned int8_t)v9
                                                                                          + (unsigned int8_t)v10);
        result = 1;
      }
      else
      {
        v7 = (unsigned int8_t)v10;
        CLogFile::Write(
          &stru_1799C8E78,
          "%s : Update_SFContDelete(NOTHING) : code : %d, slot : %d",
          v8->m_aszAvatorName,
          (unsigned int8_t)v9);
        result = 0;
      }
    }
    else
    {
      v7 = (unsigned int8_t)bySlotIndex;
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_SFContDelete(SlotIndex OVER) : code : %dslot : %d",
        v8->m_aszAvatorName,
        (unsigned int8_t)byContCode);
      result = 0;
    }
  }
  else
  {
    v7 = (unsigned int8_t)bySlotIndex;
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_SFContDelete(byContCode OVER) : code : %d, slot : %d",
      v8->m_aszAvatorName,
      (unsigned int8_t)byContCode);
    result = 0;
  }
  return result;
}
