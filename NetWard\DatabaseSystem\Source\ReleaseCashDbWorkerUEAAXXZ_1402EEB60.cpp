#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Release@CashDbWorker@@UEAAXXZ
 * Address: 0x1402EEB60

void  CashDbWorker::Release(CashDbWorker *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // rax@6
  int64_t v4; // [sp+0h] [bp-48h]@1
  CRFCashItemDatabase *v5; // [sp+20h] [bp-28h]@5
  CRFCashItemDatabase *v6; // [sp+28h] [bp-20h]@5
  int64_t v7; // [sp+30h] [bp-18h]@6
  CashDbWorker *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v8->_pkDb )
  {
    v6 = v8->_pkDb;
    v5 = v6;
    if ( v6 )
    {
      LODWORD(v3) = ((int ( *)(CRFCashItemDatabase *, signed int64_t))v5->vfptr->__vecDelDtor)(v5, 1i64);
      v7 = v3;
    }
    else
    {
      v7 = 0i64;
    }
    v8->_pkDb = 0i64;
  }
}
