#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$unchecked_uninitialized_copy@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@PEAPEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@2@@stdext@@YAPEAPEAVCLogTypeDBTask@@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@0PEAPEAV1@AEAV?$allocator@PEAVCLogTypeDBTask@@@3@@Z
 * Address: 0x1402C7BE0

CLogTypeDBTask ** stdext::unchecked_uninitialized_copy<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_First, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_Last, CLogTypeDBTask **_Dest, std::allocator<CLogTypeDBTask *> *_Al)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v6; // rax@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v7; // rax@4
  int64_t v9; // [sp+0h] [bp-A8h]@1
  CLogTypeDBTask **v10; // [sp+30h] [bp-78h]@4
  std::_Range_checked_iterator_tag v11; // [sp+38h] [bp-70h]@4
  std::_Nonscalar_ptr_iterator_tag v12; // [sp+39h] [bp-6Fh]@4
  char v13; // [sp+40h] [bp-68h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v14; // [sp+58h] [bp-50h]@4
  char v15; // [sp+60h] [bp-48h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v16; // [sp+78h] [bp-30h]@4
  int64_t v17; // [sp+80h] [bp-28h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v18; // [sp+88h] [bp-20h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v19; // [sp+90h] [bp-18h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v20; // [sp+98h] [bp-10h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__formal; // [sp+B0h] [bp+8h]@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__that; // [sp+B8h] [bp+10h]@1
  CLogTypeDBTask **v23; // [sp+C0h] [bp+18h]@1
  std::allocator<CLogTypeDBTask *> *v24; // [sp+C8h] [bp+20h]@1

  v24 = _Al;
  v23 = _Dest;
  __that = _Last;
  __formal = _First;
  v4 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v17 = -2i64;
  memset(&v11, 0, sizeof(v11));
  v12 = std::_Ptr_cat<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *>(
          __formal,
          &v23);
  v14 = (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v13;
  v16 = (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v15;
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v13,
    __that);
  v18 = v6;
  v19 = v6;
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    v16,
    __formal);
  v20 = v7;
  v10 = std::_Uninit_copy<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(
          v7,
          v19,
          v23,
          v24,
          v12,
          v11);
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(__formal);
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(__that);
  return v10;
}
