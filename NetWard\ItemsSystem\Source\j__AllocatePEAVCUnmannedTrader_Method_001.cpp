#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Allocate@PEAVCUnmannedTraderClassInfo@@@std@@YAPEAPEAVCUnmannedTraderClassInfo@@_KPEAPEAV1@@Z
 * Address: 0x14000E4EE

CUnmannedTraderClassInfo ** std::_Allocate<CUnmannedTraderClassInfo *>(unsigned int64_t _Count, CUnmannedTraderClassInfo **__formal)
{
  return std::_Allocate<CUnmannedTraderClassInfo *>(_Count, __formal);
}
