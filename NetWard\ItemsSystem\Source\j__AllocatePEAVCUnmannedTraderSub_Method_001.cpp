#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Allocate@PEAVCUnmannedTraderSubClassInfo@@@std@@YAPEAPEAVCUnmannedTraderSubClassInfo@@_KPEAPEAV1@@Z
 * Address: 0x140001325

CUnmannedTraderSubClassInfo ** std::_Allocate<CUnmannedTraderSubClassInfo *>(unsigned int64_t _Count, CUnmannedTraderSubClassInfo **__formal)
{
  return std::_Allocate<CUnmannedTraderSubClassInfo *>(_Count, __formal);
}
