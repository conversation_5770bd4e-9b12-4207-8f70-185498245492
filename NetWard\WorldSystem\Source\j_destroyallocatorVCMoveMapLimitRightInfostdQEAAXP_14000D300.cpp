#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?destroy@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAXPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x14000D300

void  std::allocator<CMoveMapLimitRightInfo>::destroy(std::allocator<CMoveMapLimitRightInfo> *this, CMoveMapLimitRightInfo *_Ptr)
{
  std::allocator<CMoveMapLimitRightInfo>::destroy(this, _Ptr);
}
