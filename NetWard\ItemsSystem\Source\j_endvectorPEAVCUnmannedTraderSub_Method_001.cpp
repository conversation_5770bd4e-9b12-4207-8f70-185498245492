#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?end@?$vector@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@2@XZ
 * Address: 0x1400055D8

std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > * std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::end(std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this, std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *result)
{
  return std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::end(this, result);
}
