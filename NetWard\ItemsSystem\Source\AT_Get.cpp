// NetWard RF Online Server - AutoTrader System
// File: AT_Get.cpp - AutoTrader Group ID Retrieval
// Compatible with Visual Studio 2022 (C++20)
// Original Function: CUnmannedTraderSubClassInfoCode::GetGroupID

#pragma once
#include "../../Headers/AutoTrader/AutoTraderCore.h"
#include "../../Headers/Common/NetWardTypes.h"
#include "../../Headers/Data/RecordManager.h"
#include <vector>
#include <algorithm>
#include <memory>

namespace NetWard::AutoTrader {

/**
 * @brief Retrieves the group ID for an item in the AutoTrader system
 * @param tableCode The table code identifier (0-255)
 * @param itemTableIndex The item's index in the table
 * @param[out] subClassId Pointer to store the retrieved subclass ID
 * @return true if group ID found and retrieved successfully, false otherwise
 *
 * @details This function searches through the AutoTrader item code list
 *          to find a matching item and retrieve its associated group ID.
 *          Used for categorizing items in the automated trading system.
 */
bool AutoTraderSubClassInfo::GetGroupID(
    uint8_t tableCode,
    uint16_t itemTableIndex,
    uint8_t* subClassId) noexcept
{
    // Input validation
    if (!subClassId) {
        return false;
    }

    // Get item record from the data table
    const auto* itemRecord = RecordManager::GetItemRecord(tableCode, itemTableIndex);
    if (!itemRecord || !itemRecord->itemCode) {
        return false;
    }

    // Search through the code list for matching item
    const std::string_view targetCode(itemRecord->itemCode);

    auto it = std::find_if(
        m_codeList.begin(),
        m_codeList.end(),
        [&targetCode](const AutoTraderItemCodeInfo& codeInfo) {
            return codeInfo.GetItemCode() == targetCode;
        }
    );

    // If found, set the subclass ID and return success
    if (it != m_codeList.end()) {
        *subClassId = static_cast<uint8_t>(m_groupId);
        return true;
    }

    return false;
}
