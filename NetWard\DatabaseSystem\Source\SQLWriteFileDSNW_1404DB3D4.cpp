#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLWriteFileDSNW
 * Address: 0x1404DB3D4

int  SQLWriteFileDSNW(const unsigned int16_t *lpszFileName, const unsigned int16_t *lpszAppName, const unsigned int16_t *lpszKeyName, const unsigned int16_t *lpszString)
{
  const unsigned int16_t *v4; // rbp@1
  const unsigned int16_t *v5; // rbx@1
  const unsigned int16_t *v6; // rdi@1
  const unsigned int16_t *v7; // rsi@1
  int64_t ( *v8)(); // rax@1
  int result; // eax@2

  v4 = lpszFileName;
  v5 = lpszString;
  v6 = lpszKeyName;
  v7 = lpszAppName;
  v8 = ODBC___GetSetupProc("SQLWriteFileDSNW");
  if ( v8 )
    result = ((int ( *)(const unsigned int16_t *, const unsigned int16_t *, const unsigned int16_t *, const unsigned int16_t *))v8)(
               v4,
               v7,
               v6,
               v5);
  else
    result = 0;
  return result;
}
