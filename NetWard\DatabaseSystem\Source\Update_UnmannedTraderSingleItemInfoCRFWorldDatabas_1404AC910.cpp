#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_UnmannedTraderSingleItemInfo@CRFWorldDatabase@@QEAA_NKAEBU_unmannedtrader_registsingleitem@@@Z
 * Address: 0x1404AC910

bool  CRFWorldDatabase::Update_UnmannedTraderSingleItemInfo(CRFWorldDatabase *this, unsigned int dwRegSerial, _unmannedtrader_registsingleitem *kInfo)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int v5; // eax@4
  int v6; // ecx@4
  int v7; // edx@4
  int v8; // er8@4
  int v9; // er9@4
  int v10; // er10@4
  int64_t v12; // [sp+0h] [bp-4B8h]@1
  int v13; // [sp+20h] [bp-498h]@4
  unsigned int v14; // [sp+28h] [bp-490h]@4
  unsigned int64_t v15; // [sp+30h] [bp-488h]@4
  unsigned int v16; // [sp+38h] [bp-480h]@4
  int v17; // [sp+40h] [bp-478h]@4
  int v18; // [sp+48h] [bp-470h]@4
  int v19; // [sp+50h] [bp-468h]@4
  int v20; // [sp+58h] [bp-460h]@4
  int v21; // [sp+60h] [bp-458h]@4
  unsigned int64_t v22; // [sp+68h] [bp-450h]@4
  unsigned int v23; // [sp+70h] [bp-448h]@4
  char DstBuf; // [sp+90h] [bp-428h]@4
  unsigned int64_t v25; // [sp+4A0h] [bp-18h]@4
  CRFWorldDatabase *v26; // [sp+4C0h] [bp+8h]@1
  unsigned int v27; // [sp+4C8h] [bp+10h]@1
  _unmannedtrader_registsingleitem *v28; // [sp+4D0h] [bp+18h]@1

  v28 = kInfo;
  v27 = dwRegSerial;
  v26 = this;
  v3 = &v12;
  for ( i = 300i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v25 = (unsigned int64_t)&v12 ^ _security_cookie;
  v5 = kInfo->byClass3;
  v6 = kInfo->byClass2;
  v7 = kInfo->byClass1;
  v8 = kInfo->byGrade;
  v9 = v28->byLv;
  v10 = v28->byInveninx;
  v23 = v28->dwT;
  v22 = v28->lnUID;
  v21 = v5;
  v20 = v6;
  v19 = v7;
  v18 = v8;
  v17 = v9;
  v16 = v28->dwU;
  v15 = v28->dwD;
  v14 = v28->dwK;
  v13 = v10;
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "{ CALL pUpdate_utsingleiteminfo_20070601( %d, %u, %d, %I64d, %d, %u, %u, %u, %u, %u, %I64d, %d ) }",
    v27);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v26->vfptr, &DstBuf, 1);
}
