#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Precompute@?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@UEAAXAEBV?$DL_GroupPrecomputation@UECPPoint@CryptoPP@@@2@II@Z
 * Address: 0x1405781E0

int64_t  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::Precompute(int64_t a1, int64_t a2, unsigned int a3, unsigned int a4)
{
  int64_t result; // rax@8
  int64_t *v5; // rax@9
  int64_t v6; // rax@9
  CryptoPP::ECPPoint *v7; // rax@9
  CryptoPP::ECPPoint *v8; // rax@9
  signed int i; // [sp+20h] [bp-D8h]@7
  CryptoPP::Integer v10; // [sp+28h] [bp-D0h]@6
  CryptoPP::ECPPoint v11; // [sp+50h] [bp-A8h]@9
  int64_t v12; // [sp+A8h] [bp-50h]@1
  struct CryptoPP::Integer *v13; // [sp+B0h] [bp-48h]@6
  struct CryptoPP::Integer *v14; // [sp+B8h] [bp-40h]@6
  int64_t *v15; // [sp+C0h] [bp-38h]@9
  int64_t v16; // [sp+C8h] [bp-30h]@9
  int64_t v17; // [sp+D0h] [bp-28h]@9
  int64_t v18; // [sp+D8h] [bp-20h]@9
  CryptoPP::ECPPoint *v19; // [sp+E0h] [bp-18h]@9
  CryptoPP::ECPPoint *__that; // [sp+E8h] [bp-10h]@9
  int64_t v21; // [sp+100h] [bp+8h]@1
  int64_t v22; // [sp+108h] [bp+10h]@1
  unsigned int v23; // [sp+110h] [bp+18h]@1
  unsigned int v24; // [sp+118h] [bp+20h]@1

  v24 = a4;
  v23 = a3;
  v22 = a2;
  v21 = a1;
  v12 = -2i64;
  if ( !std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::size((std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(a1 + 144)) )
    _wassert(L"m_bases.size() > 0", L"d:\\rf project\\rf_server64\\28 crypto++\\eprecomp.cpp", 0x1Fu);
  if ( v24 > v23 )
    _wassert(L"storage <= maxExpBits", L"d:\\rf project\\rf_server64\\28 crypto++\\eprecomp.cpp", 0x20u);
  if ( v24 > 1 )
  {
    *(uint32_t*)(v21 + 96) = (v23 + v24 - 1) / v24;
    v13 = CryptoPP::Integer::Power2(&v10, *(uint32_t*)(v21 + 96));
    v14 = v13;
    CryptoPP::Integer::operator=(v21 + 104);
    CryptoPP::Integer::~Integer(&v10);
  }
  std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::resize(v21 + 144, v24);
  for ( i = 1; ; ++i )
  {
    result = v24;
    if ( i >= v24 )
      break;
    LODWORD(v5) = (*(int ( **)(int64_t))(*(uint64_t*)v22 + 24i64))(v22);
    v15 = v5;
    v16 = v21 + 104;
    v17 = v21 + 144;
    LODWORD(v6) = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator[](
                    v21 + 144,
                    (unsigned int)(i - 1));
    v18 = *v15;
    LODWORD(v7) = (*(int ( **)(int64_t *, CryptoPP::ECPPoint *, int64_t, int64_t))(v18 + 80))(
                    v15,
                    &v11,
                    v6,
                    v16);
    v19 = v7;
    __that = v7;
    LODWORD(v8) = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator[](
                    v21 + 144,
                    (unsigned int)i);
    CryptoPP::ECPPoint::operator=(v8, __that);
    CryptoPP::ECPPoint::~ECPPoint(&v11);
  }
  return result;
}
