#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?push_back@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEAAXAEBQEAVCMoveMapLimitRight@@@Z
 * Address: 0x140009917

void  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::push_back(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, CMoveMapLimitRight *const *_Val)
{
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::push_back(this, _Val);
}
