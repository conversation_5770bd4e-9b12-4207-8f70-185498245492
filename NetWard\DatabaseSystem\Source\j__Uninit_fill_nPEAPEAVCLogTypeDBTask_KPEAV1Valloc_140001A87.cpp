#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Uninit_fill_n@PEAPEAVCLogTypeDBTask@@_KPEAV1@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@YAXPEAPEAVCLogTypeDBTask@@_KAEBQEAV1@AEAV?$allocator@PEAVCLogTypeDBTask@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140001A87

void  std::_Uninit_fill_n<CLogTypeDBTask * *,unsigned int64_t,CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(CLogTypeDBTask **_First, unsigned int64_t _Count, CLogTypeDBTask *const *_Val, std::allocator<CLogTypeDBTask *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CLogTypeDBTask * *,unsigned int64_t,CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
