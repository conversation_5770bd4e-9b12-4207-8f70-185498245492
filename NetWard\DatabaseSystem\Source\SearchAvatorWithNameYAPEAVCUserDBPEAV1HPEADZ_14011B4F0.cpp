#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SearchAvatorWithName@@YAPEAVCUserDB@@PEAV1@HPEAD@Z
 * Address: 0x14011B4F0

CUserDB * SearchAvatorWithName(CUserDB *pList, int nMax, char *pwszName)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-38h]@1
  unsigned int8_t v7; // [sp+20h] [bp-18h]@4
  int j; // [sp+24h] [bp-14h]@4
  CUserDB *v9; // [sp+40h] [bp+8h]@1
  int v10; // [sp+48h] [bp+10h]@1
  const char *Str; // [sp+50h] [bp+18h]@1

  Str = pwszName;
  v10 = nMax;
  v9 = pList;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v7 = strlen_0(pwszName);
  for ( j = 0; j < v10; ++j )
  {
    if ( v9[j].m_bActive && v9[j].m_bField && v9[j].m_byNameLen == v7 && !strncmp(v9[j].m_wszAvatorName, Str, v7) )
      return &v9[j];
  }
  return 0i64;
}
