#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$unchecked_fill_n@PEAPEAVCUnmannedTraderDivisionInfo@@_KPEAV1@@stdext@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@_KAEBQEAV1@@Z
 * Address: 0x14038CA20

void  stdext::unchecked_fill_n<CUnmannedTraderDivisionInfo * *,unsigned int64_t,CUnmannedTraderDivisionInfo *>(CUnmannedTraderDivisionInfo **_First, unsigned int64_t _Count, CUnmannedTraderDivisionInfo *const *_Val)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  std::random_access_iterator_tag *v5; // rax@4
  int64_t v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+20h] [bp-28h]@4
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  CUnmannedTraderDivisionInfo **__formal; // [sp+31h] [bp-17h]@4
  CUnmannedTraderDivisionInfo **_Firsta; // [sp+50h] [bp+8h]@1
  unsigned int64_t _Counta; // [sp+58h] [bp+10h]@1
  CUnmannedTraderDivisionInfo **_Vala; // [sp+60h] [bp+18h]@1

  _Vala = (CUnmannedTraderDivisionInfo **)_Val;
  _Counta = _Count;
  _Firsta = _First;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  LOBYTE(v5) = std::_Iter_cat<CUnmannedTraderDivisionInfo * *>(&__formal);
  v7 = v8;
  std::_Fill_n<CUnmannedTraderDivisionInfo * *,unsigned int64_t,CUnmannedTraderDivisionInfo *,std::random_access_iterator_tag>(
    _Firsta,
    _Counta,
    _Vala,
    (std::random_access_iterator_tag)v5->0,
    v8);
}
