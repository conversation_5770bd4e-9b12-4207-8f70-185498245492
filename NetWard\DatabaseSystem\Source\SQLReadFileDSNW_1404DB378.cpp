#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLReadFileDSNW
 * Address: 0x1404DB378

int  SQLReadFileDSNW(const unsigned int16_t *lpszFileName, const unsigned int16_t *lpszAppName, const unsigned int16_t *lpszKeyName, unsigned int16_t *lpszString, unsigned int16_t cbString, unsigned int16_t *pcbString)
{
  const unsigned int16_t *v6; // rbp@1
  unsigned int16_t *v7; // rbx@1
  const unsigned int16_t *v8; // rdi@1
  const unsigned int16_t *v9; // rsi@1
  int64_t ( *v10)(); // rax@1
  int result; // eax@2

  v6 = lpszFileName;
  v7 = lpszString;
  v8 = lpszKeyName;
  v9 = lpszAppName;
  v10 = ODBC___GetSetupProc("SQLReadFileDSNW");
  if ( v10 )
    result = ((int ( *)(const unsigned int16_t *, const unsigned int16_t *, const unsigned int16_t *, unsigned int16_t *))v10)(
               v6,
               v9,
               v8,
               v7);
  else
    result = 0;
  return result;
}
