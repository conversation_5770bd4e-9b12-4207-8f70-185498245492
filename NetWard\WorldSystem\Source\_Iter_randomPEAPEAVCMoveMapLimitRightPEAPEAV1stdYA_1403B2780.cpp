#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Iter_random@PEAPEAVCMoveMapLimitRight@@PEAPEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCMoveMapLimitRight@@0@Z
 * Address: 0x1403B2780

CMoveMapLimitRight **const * std::_Iter_random<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(CMoveMapLimitRight **const *__formal, CMoveMapLimitRight **const *a2)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-48h]@1
  CMoveMapLimitRight **const *v6; // [sp+50h] [bp+8h]@1

  v6 = __formal;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  return v6;
}
