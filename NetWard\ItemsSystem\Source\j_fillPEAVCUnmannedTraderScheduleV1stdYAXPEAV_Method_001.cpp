#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$fill@PEAVCUnmannedTraderSchedule@@V1@@std@@YAXPEAVCUnmannedTraderSchedule@@0AEBV1@@Z
 * Address: 0x140007DBA

void  std::fill<CUnmannedTraderSchedule *,CUnmannedTraderSchedule>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Val)
{
  std::fill<CUnmannedTraderSchedule *,CUnmannedTraderSchedule>(_First, _Last, _Val);
}
