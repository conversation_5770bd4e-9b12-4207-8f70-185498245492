#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_Set_Limit_Run@CRFWorldDatabase@@QEAA_NPEAEH@Z
 * Address: 0x1404C8940

bool  CRFWorldDatabase::Update_Set_Limit_Run(CRFWorldDatabase *this, char *pData, int iSize)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-468h]@1
  char _Dest[1024]; // [sp+40h] [bp-428h]@4
  unsigned int64_t v8; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+470h] [bp+8h]@1
  char *buf; // [sp+478h] [bp+10h]@1
  int size; // [sp+480h] [bp+18h]@1

  size = iSize;
  buf = pData;
  v9 = this;
  v3 = &v6;
  for ( i = 280i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v8 = (unsigned int64_t)&v6 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0x3FFui64);
  sprintf_s<1024>((char (*)[1024])_Dest, "update [dbo].[tbl_sf_delay] set [effect] = ? where [aserial] = 0");
  return CRFNewDatabase::ExecUpdateBinaryQuery((CRFNewDatabase *)&v9->vfptr, _Dest, buf, size, 1);
}
