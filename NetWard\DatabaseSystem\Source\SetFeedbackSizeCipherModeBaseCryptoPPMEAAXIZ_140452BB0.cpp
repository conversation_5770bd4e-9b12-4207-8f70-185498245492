#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetFeedbackSize@CipherModeBase@CryptoPP@@MEAAXI@Z
 * Address: 0x140452BB0

void  CryptoPP::CipherModeBase::SetFeedbackSize(CryptoPP::CipherModeBase *this, unsigned int feedbackSize)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-B8h]@1
  CryptoPP::InvalidArgument v5; // [sp+20h] [bp-98h]@6
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+70h] [bp-48h]@6
  unsigned int8_t v7; // [sp+A0h] [bp-18h]@6
  int64_t v8; // [sp+A8h] [bp-10h]@4
  CryptoPP::CipherModeBase *v9; // [sp+C0h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 44i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v8 = -2i64;
  if ( feedbackSize && feedbackSize != CryptoPP::CipherModeBase::BlockSize(v9) )
  {
    memset(&v7, 0, sizeof(v7));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &s,
      "CipherModeBase: feedback size cannot be specified for this cipher mode",
      v7);
    CryptoPP::InvalidArgument::InvalidArgument(&v5, &s);
    CxxThrowException_0(&v5, &TI3_AVInvalidArgument_CryptoPP__);
  }
}
