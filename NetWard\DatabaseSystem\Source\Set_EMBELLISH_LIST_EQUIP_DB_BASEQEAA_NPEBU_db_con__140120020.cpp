#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Set@_EMBELLISH_LIST@_EQUIP_DB_BASE@@QEAA_NPEBU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x140120020

char  _EQUIP_DB_BASE::_EMBELLISH_LIST::Set(_EQUIP_DB_BASE::_EMBELLISH_LIST *this, _STORAGE_LIST::_db_con *pItem)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v5; // [sp+0h] [bp-28h]@1
  _EQUIP_DB_BASE::_EMBELLISH_LIST *v6; // [sp+30h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v7; // [sp+38h] [bp+10h]@1

  v7 = pItem;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( _EMBELLKEY::IsFilled(&v6->Key) )
  {
    result = 0;
  }
  else
  {
    v6->Key.byTableCode = v7->m_byTableCode;
    v6->Key.wItemIndex = v7->m_wItemIndex;
    v6->wAmount = v7->m_dwDur;
    v6->lnUID = v7->m_lnUID;
    v6->dwT = v7->m_dwT;
    v6->byCsMethod = v7->m_byCsMethod;
    v6->dwLendRegdTime = v7->m_dwLendRegdTime;
    result = 1;
  }
  return result;
}
