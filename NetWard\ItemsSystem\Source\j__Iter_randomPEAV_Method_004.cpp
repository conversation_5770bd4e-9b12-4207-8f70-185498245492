#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Iter_random@PEAVCUnmannedTraderUserInfo@@PEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAVCUnmannedTraderUserInfo@@0@Z
 * Address: 0x1400011DB

std::random_access_iterator_tag  std::_Iter_random<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *>(CUnmannedTraderUserInfo *const *__formal, CUnmannedTraderUserInfo *const *a2)
{
  return std::_Iter_random<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *>(__formal, a2);
}
