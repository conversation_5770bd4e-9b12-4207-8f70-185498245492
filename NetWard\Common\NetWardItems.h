#pragma once

#ifndef NETWARD_ITEMS_H
#define NETWARD_ITEMS_H

// NetWard Items System Header
// Generated from RF Online ZoneServerUD_x64 decompiled source

#include <windows.h>
#include <stdint.h>
#include <memory>
#include <vector>
#include <string>

// Forward declarations
struct CItemUpgradeTableVtbl;
struct CItemLootTableVtbl;
struct CItemLootTable::_linker_code;
struct CItemUpgradeTable;
struct CItemStoreManager;
struct CItemDropMgr;
struct tagTCITEMHEADERA;
struct tagTCITEMA;
struct tagTCITEMW;
struct tagTCITEMHEADERW;

// Class definitions

// CItemUpgradeTableVtbl
struct CItemUpgradeTableVtbl
{
 void *( *__vecDelDtor)(CItemUpgradeTable *this, unsigned int);
};

// CItemLootTableVtbl
struct CItemLootTableVtbl
{
 void *( *__vecDelDtor)(CItemLootTable *this, unsigned int);
};

// CItemLootTable::_linker_code
struct CItemLootTable::_linker_code
{
 char byTableCode;
 unsigned int16_t wItemIndex;
 int bExist;
};

// CItemUpgradeTable
struct CItemUpgradeTable
{
 CItemUpgradeTableVtbl *vfptr;
 CRecordData m_tblItemUpgrade;
 int m_nResNum;
 unsigned int16_t *m_pwResIndex;
};

// CItemStoreManager
struct CItemStoreManager
{
 CLogFile *m_pkLogger;
 CMyTimer m_tmrCheckTime;
 CMyTimer m_tmrSaveTime;
 int64_t m_tNextInitTime;
 _qry_case_all_store_limit_item m_Sheet;
 int m_nInstanceItemStoreListNum;
 CMapItemStoreList *m_InstanceItemStoreList;
 CRecordData m_tblItemStore;
 int m_nMapItemStoreListNum;
 CMapItemStoreList *m_MapItemStoreList;
 CItemStore *m_pLimitInitNormalStore[3];
};

// CItemDropMgr
struct CItemDropMgr
{
 _DropItemGroupInfo m_Pool[333];
 CNetIndexList m_listEmpty;
 CNetIndexList m_listTask;
 unsigned int m_dwTotalDropCount;
 CLogFile *m_pLogFile;
};

// tagTCITEMHEADERA
struct tagTCITEMHEADERA
{
 unsigned int mask;
 unsigned int lpReserved1;
 unsigned int lpReserved2;
 char *pszText;
 int cchTextMax;
 int iImage;
};

// tagTCITEMA
struct tagTCITEMA
{
 unsigned int mask;
 unsigned int dwState;
 unsigned int dwStateMask;
 char *pszText;
 int cchTextMax;
 int iImage;
 int64_t lParam;
};

// tagTCITEMW
struct tagTCITEMW
{
 unsigned int mask;
 unsigned int dwState;
 unsigned int dwStateMask;
 wchar_t *pszText;
 int cchTextMax;
 int iImage;
 int64_t lParam;
};

// tagTCITEMHEADERW
struct tagTCITEMHEADERW
{
 unsigned int mask;
 unsigned int lpReserved1;
 unsigned int lpReserved2;
 wchar_t *pszText;
 int cchTextMax;
 int iImage;
};


#endif // NETWARD_ITEMS_H
