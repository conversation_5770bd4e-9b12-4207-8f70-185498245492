#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@stdext@@YAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@Z
 * Address: 0x140008FF3

CUnmannedTraderClassInfo ** stdext::_Unchecked_uninitialized_move<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo **_Dest, std::allocator<CUnmannedTraderClassInfo *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
