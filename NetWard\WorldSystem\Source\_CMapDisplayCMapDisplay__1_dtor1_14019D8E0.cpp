#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CMapDisplay::CMapDisplay_::_1_::dtor$1
 * Address: 0x14019D8E0

void  CMapDisplay::CMapDisplay_::_1_::dtor_1(int64_t a1, int64_t a2)
{
  `eh vector destructor iterator'(
    (void *)(*(uint64_t*)(a2 + 160) + 1048i64),
    0x40ui64,
    60,
    (void ( *)(void *))CCollLineDraw::~CCollLineDraw);
}
