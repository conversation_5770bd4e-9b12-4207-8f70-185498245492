#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVCUnmannedTraderClassInfo@@_KPEAV1@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@stdext@@YAXPEAPEAVCUnmannedTraderClassInfo@@_KAEBQEAV1@AEAV?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@Z
 * Address: 0x14000951B

void  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderClassInfo * *,unsigned int64_t,CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(CUnmannedTraderClassInfo **_First, unsigned int64_t _Count, CUnmannedTraderClassInfo *const *_Val, std::allocator<CUnmannedTraderClassInfo *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderClassInfo * *,unsigned int64_t,CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
