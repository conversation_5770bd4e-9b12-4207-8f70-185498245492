#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Fill@PEAPEAVCMoveMapLimitRight@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitRight@@0AEBQEAV1@@Z
 * Address: 0x1403B2BD0

void  std::_Fill<CMoveMapLimitRight * *,CMoveMapLimitRight *>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight *const *_Val)
{
  CMoveMapLimitRight **i; // [sp+10h] [bp+8h]@1

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}
