#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?MakeLimitItemUpdateQuery@CItemStoreManager@@QEAAXKEHKPEAU_limit_item_db_data@@_KPEADH@Z
 * Address: 0x14034A7E0

void  CItemStoreManager::MakeLimitItemUpdateQuery(CItemStoreManager *this, unsigned int dwSerial, char byStoreType, int nTypeSerial, unsigned int dwStoreIndex, _limit_item_db_data *pItemData, unsigned int64_t dwLimitInitTime, char *pszQuery, int nBufSize)
{
  int64_t *v9; // rdi@1
  signed int64_t i; // rcx@1
  int v11; // eax@6
  int64_t v12; // [sp+0h] [bp-108h]@1
  int v13; // [sp+20h] [bp-E8h]@4
  unsigned int v14; // [sp+28h] [bp-E0h]@4
  unsigned int64_t v15; // [sp+30h] [bp-D8h]@4
  char DstBuf; // [sp+50h] [bp-B8h]@4
  char v17; // [sp+51h] [bp-B7h]@4
  unsigned int j; // [sp+D4h] [bp-34h]@4
  int v19; // [sp+D8h] [bp-30h]@7
  int64_t v20; // [sp+E8h] [bp-20h]@6
  unsigned int64_t v21; // [sp+F0h] [bp-18h]@4
  unsigned int v22; // [sp+118h] [bp+10h]@1

  v22 = dwSerial;
  v9 = &v12;
  for ( i = 64i64; i; --i )
  {
    *(uint32_t*)v9 = -858993460;
    v9 = (int64_t *)((char *)v9 + 4);
  }
  v21 = (unsigned int64_t)&v12 ^ _security_cookie;
  DstBuf = 0;
  memset(&v17, 0, 0x7Fui64);
  v15 = dwLimitInitTime;
  v14 = dwStoreIndex;
  v13 = nTypeSerial;
  sprintf_s(
    pszQuery,
    nBufSize,
    "Update tbl_StoreLimitItem_061212 set dck=0,type=%d,typeserial=%d,storeinx=%d,resettime=%I64d,",
    (unsigned int8_t)byStoreType);
  for ( j = 0; (signed int)j < 16; ++j )
  {
    v20 = (signed int)j;
    v11 = _INVENKEY::CovDBKey((_INVENKEY *)&pItemData[j]);
    LODWORD(v15) = pItemData[v20].nLimitNum;
    v14 = j;
    v13 = v11;
    sprintf_s(&DstBuf, 0x80ui64, "k%d=%d,num%d=%d,", j);
    strcat_s(pszQuery, nBufSize, &DstBuf);
  }
  pszQuery[strlen_0(pszQuery) - 1] = 32;
  sprintf_s(&DstBuf, 0x80ui64, "where serial=%d", v22);
  strcat_s(pszQuery, nBufSize, &DstBuf);
  v19 = strlen_0(pszQuery);
}
