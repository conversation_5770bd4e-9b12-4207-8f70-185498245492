#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_fill_n@PEAPEAVCMoveMapLimitRight@@_KPEAV1@@stdext@@YAXPEAPEAVCMoveMapLimitRight@@_KAEBQEAV1@@Z
 * Address: 0x140004D5E

void  stdext::unchecked_fill_n<CMoveMapLimitRight * *,unsigned int64_t,CMoveMapLimitRight *>(CMoveMapLimitRight **_First, unsigned int64_t _Count, CMoveMapLimitRight *const *_Val)
{
  stdext::unchecked_fill_n<CMoveMapLimitRight * *,unsigned int64_t,CMoveMapLimitRight *>(_First, _Count, _Val);
}
