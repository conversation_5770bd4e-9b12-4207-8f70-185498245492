#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Fill@PEAPEAVCUnmannedTraderSubClassInfo@@PEAV1@@std@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@0AEBQEAV1@@Z
 * Address: 0x140001EEC

void  std::_Fill<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo *>(CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last, CUnmannedTraderSubClassInfo *const *_Val)
{
  std::_Fill<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo *>(_First, _Last, _Val);
}
