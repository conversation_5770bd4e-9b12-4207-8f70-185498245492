#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Destroy_range@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@YAXPEAPEAVCMoveMapLimitRight@@0AEAV?$allocator@PEAVCMoveMapLimitRight@@@0@U_Scalar_ptr_iterator_tag@0@@Z
 * Address: 0x1403A3CE0

void  std::_Destroy_range<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, std::allocator<CMoveMapLimitRight *> *_Al, std::_Scalar_ptr_iterator_tag __formal)
{
  ;
}
