#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?update_amine_workstate@CRFWorldDatabase@@QEAA_NEEK_N@Z
 * Address: 0x1404A96C0

bool  CRFWorldDatabase::update_amine_workstate(CRFWorldDatabase *this, char byType, char byRace, unsigned int dwSerial, bool bWorking)
{
  int64_t *v5; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v8; // [sp+0h] [bp-E8h]@1
  unsigned int v9; // [sp+20h] [bp-C8h]@4
  int v10; // [sp+28h] [bp-C0h]@4
  char Dest; // [sp+40h] [bp-A8h]@4
  char v12; // [sp+41h] [bp-A7h]@4
  unsigned int64_t v13; // [sp+D0h] [bp-18h]@4
  CRFWorldDatabase *v14; // [sp+F0h] [bp+8h]@1

  v14 = this;
  v5 = &v8;
  for ( i = 56i64; i; --i )
  {
    *(uint32_t*)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  v13 = (unsigned int64_t)&v8 ^ _security_cookie;
  Dest = 0;
  memset(&v12, 0, 0x7Fui64);
  v10 = bWorking;
  v9 = dwSerial;
  sprintf(
    &Dest,
    "{ CALL pupdate_automine_workingstate(%d,%d,%d,%d) }",
    (unsigned int8_t)byType,
    (unsigned int8_t)byRace);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v14->vfptr, &Dest, 1);
}
