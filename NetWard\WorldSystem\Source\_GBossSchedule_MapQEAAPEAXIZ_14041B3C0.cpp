#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??_GBossSchedule_Map@@QEAAPEAXI@Z
 * Address: 0x14041B3C0

BossSchedule_Map * BossSchedule_Map::`scalar deleting destructor'(BossSchedule_Map *this, int a2)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-28h]@1
  BossSchedule_Map *v6; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  BossSchedule_Map::~BossSchedule_Map(v6);
  if ( v7 & 1 )
    operator delete(v6);
  return v6;
}
