#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Ufill@?$vector@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x1400120AD

CUnmannedTraderDivisionInfo ** std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Ufill(std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this, CUnmannedTraderDivisionInfo **_Ptr, unsigned int64_t _Count, CUnmannedTraderDivisionInfo *const *_Val)
{
  return std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Ufill(
           this,
           _Ptr,
           _Count,
           _Val);
}
