#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Fill@PEAVCMoveMapLimitRightInfo@@V1@@std@@YAXPEAVCMoveMapLimitRightInfo@@0AEBV1@@Z
 * Address: 0x1403B2EE0

void  std::_Fill<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Val)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfo *v6; // [sp+30h] [bp+8h]@1
  CMoveMapLimitRightInfo *v7; // [sp+38h] [bp+10h]@1
  CMoveMapLimitRightInfo *rhs; // [sp+40h] [bp+18h]@1

  rhs = _Val;
  v7 = _Last;
  v6 = _First;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  while ( v6 != v7 )
  {
    CMoveMapLimitRightInfo::operator=(v6, rhs);
    ++v6;
  }
}
