// NetWard RF Online Server - Data Record Management
// File: RecordManager.h - Game data record management system
// Compatible with Visual Studio 2022 (C++20)

#pragma once

#include "../Common/NetWardTypes.h"
#include <unordered_map>
#include <memory>
#include <string_view>
#include <mutex>

namespace NetWard::Data {

// ============================================================================
// FORWARD DECLARATIONS
// ============================================================================

class RecordManager;
struct ItemRecord;
struct CharacterRecord;
struct SkillRecord;

// ============================================================================
// RECORD TYPES
// ============================================================================

enum class RecordType : uint8 {
    Item = 0,
    Character = 1,
    Skill = 2,
    Monster = 3,
    Map = 4,
    Quest = 5,
    Invalid = 0xFF
};

// ============================================================================
// BASE RECORD STRUCTURE
// ============================================================================

struct BaseRecord {
    uint32 recordId;
    String recordName;
    String description;
    bool isActive;
    
    BaseRecord() : recordId(0), isActive(true) {}
    virtual ~BaseRecord() = default;
    
    NETWARD_DISABLE_COPY_AND_MOVE(BaseRecord);
};

// ============================================================================
// ITEM RECORD
// ============================================================================

struct ItemRecord : public BaseRecord {
    ItemIndex itemIndex;
    ItemCode itemCode;
    String itemName;
    uint8 itemType;
    uint8 itemSubType;
    uint32 itemLevel;
    uint64 itemPrice;
    uint32 maxStackSize;
    bool isTradeable;
    bool isDroppable;
    bool isStorable;
    float32 weight;
    String iconPath;
    String modelPath;
    
    // Item stats
    uint32 attack;
    uint32 defense;
    uint32 accuracy;
    uint32 dodge;
    uint32 critical;
    uint32 durability;
    uint32 maxDurability;
    
    ItemRecord() 
        : itemIndex(0)
        , itemType(0)
        , itemSubType(0)
        , itemLevel(1)
        , itemPrice(0)
        , maxStackSize(1)
        , isTradeable(true)
        , isDroppable(true)
        , isStorable(true)
        , weight(0.0f)
        , attack(0)
        , defense(0)
        , accuracy(0)
        , dodge(0)
        , critical(0)
        , durability(100)
        , maxDurability(100)
    {}
};

// ============================================================================
// CHARACTER RECORD
// ============================================================================

struct CharacterRecord : public BaseRecord {
    CharacterID characterId;
    String characterName;
    RaceType race;
    ClassType characterClass;
    uint32 level;
    uint64 experience;
    uint32 strength;
    uint32 dexterity;
    uint32 intelligence;
    uint32 constitution;
    uint32 leadership;
    uint32 skillPoints;
    uint32 statPoints;
    Money money;
    
    CharacterRecord()
        : characterId(0)
        , race(RaceType::Invalid)
        , characterClass(ClassType::Invalid)
        , level(1)
        , experience(0)
        , strength(10)
        , dexterity(10)
        , intelligence(10)
        , constitution(10)
        , leadership(10)
        , skillPoints(0)
        , statPoints(0)
        , money(0)
    {}
};

// ============================================================================
// RECORD MANAGER CLASS
// ============================================================================

class RecordManager {
public:
    // Singleton pattern
    static RecordManager& GetInstance();
    
    // Initialization
    bool Initialize();
    void Shutdown();
    
    // Record retrieval
    const ItemRecord* GetItemRecord(uint8 tableCode, uint16 itemIndex) const;
    const ItemRecord* GetItemRecordByCode(const String& itemCode) const;
    const CharacterRecord* GetCharacterRecord(CharacterID characterId) const;
    
    // Record management
    bool LoadItemRecords(const String& filePath);
    bool LoadCharacterRecords(const String& filePath);
    bool SaveRecords() const;
    
    // Utility functions
    bool IsValidItemIndex(uint8 tableCode, uint16 itemIndex) const;
    bool IsValidItemCode(const String& itemCode) const;
    
    // Statistics
    size_t GetItemRecordCount() const;
    size_t GetCharacterRecordCount() const;
    
    // Thread safety
    void Lock() const { m_mutex.lock(); }
    void Unlock() const { m_mutex.unlock(); }

private:
    RecordManager() = default;
    ~RecordManager() = default;
    
    NETWARD_DISABLE_COPY_AND_MOVE(RecordManager);
    
    // Internal data structures
    using ItemRecordMap = HashMap<uint32, UniquePtr<ItemRecord>>;
    using ItemCodeMap = HashMap<String, ItemRecord*>;
    using CharacterRecordMap = HashMap<CharacterID, UniquePtr<CharacterRecord>>;
    
    ItemRecordMap m_itemRecords;
    ItemCodeMap m_itemCodeIndex;
    CharacterRecordMap m_characterRecords;
    
    mutable std::mutex m_mutex;
    bool m_initialized = false;
    
    // Helper functions
    uint32 MakeItemKey(uint8 tableCode, uint16 itemIndex) const;
    bool LoadItemRecord(const String& line);
    bool LoadCharacterRecord(const String& line);
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

// Global convenience functions
inline const ItemRecord* GetItemRecord(uint8 tableCode, uint16 itemIndex) {
    return RecordManager::GetInstance().GetItemRecord(tableCode, itemIndex);
}

inline const ItemRecord* GetItemRecordByCode(const String& itemCode) {
    return RecordManager::GetInstance().GetItemRecordByCode(itemCode);
}

inline const CharacterRecord* GetCharacterRecord(CharacterID characterId) {
    return RecordManager::GetInstance().GetCharacterRecord(characterId);
}

// ============================================================================
// RECORD FACTORY
// ============================================================================

template<typename RecordType>
class RecordFactory {
public:
    static UniquePtr<RecordType> CreateRecord() {
        return std::make_unique<RecordType>();
    }
    
    template<typename... Args>
    static UniquePtr<RecordType> CreateRecord(Args&&... args) {
        return std::make_unique<RecordType>(std::forward<Args>(args)...);
    }
};

// Type aliases for convenience
using ItemRecordFactory = RecordFactory<ItemRecord>;
using CharacterRecordFactory = RecordFactory<CharacterRecord>;

} // namespace NetWard::Data
