#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CryptoPP::CipherModeBase::SetFeedbackSize_::_1_::dtor$0
 * Address: 0x140452C70

int  CryptoPP::CipherModeBase::SetFeedbackSize_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  return std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(a2 + 112);
}
