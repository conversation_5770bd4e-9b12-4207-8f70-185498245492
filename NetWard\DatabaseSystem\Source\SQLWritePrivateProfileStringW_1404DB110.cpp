#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLWritePrivateProfileStringW
 * Address: 0x1404DB110

int  SQLWritePrivateProfileStringW(const unsigned int16_t *lpszSection, const unsigned int16_t *lpszEntry, const unsigned int16_t *lpszString, const unsigned int16_t *lpszFilename)
{
  const unsigned int16_t *v4; // rbp@1
  const unsigned int16_t *v5; // rbx@1
  const unsigned int16_t *v6; // rdi@1
  const unsigned int16_t *v7; // rsi@1
  int64_t ( *v8)(); // rax@1
  int result; // eax@2

  v4 = lpszSection;
  v5 = lpszFilename;
  v6 = lpszString;
  v7 = lpszEntry;
  v8 = ODBC___GetSetupProc("SQLWritePrivateProfileStringW");
  if ( v8 )
    result = ((int ( *)(const unsigned int16_t *, const unsigned int16_t *, const unsigned int16_t *, const unsigned int16_t *))v8)(
               v4,
               v7,
               v6,
               v5);
  else
    result = 0;
  return result;
}
