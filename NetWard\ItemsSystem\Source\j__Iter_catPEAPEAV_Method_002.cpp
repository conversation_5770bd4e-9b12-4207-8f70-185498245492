#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Iter_cat@PEAPEAVCUnmannedTraderSortType@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCUnmannedTraderSortType@@@Z
 * Address: 0x140011AD6

std::random_access_iterator_tag  std::_Iter_cat<CUnmannedTraderSortType * *>(CUnmannedTraderSortType **const *__formal)
{
  return std::_Iter_cat<CUnmannedTraderSortType * *>(__formal);
}
