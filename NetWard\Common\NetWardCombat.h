#pragma once

#ifndef NETWARD_COMBAT_H
#define NETWARD_COMBAT_H

// NetWard Combat System Header
// Generated from RF Online ZoneServerUD_x64 decompiled source

#include <windows.h>
#include <stdint.h>
#include <memory>
#include <vector>
#include <string>

// Forward declarations
struct CBattleTournamentInfo;
struct CPvpPointLimiter;
struct CPvpOrderView;
struct CPvpUserRankingInfo;
struct CPvpUserRankingTargetUserList;
struct CPvpCashMng;
struct CPvpUserAndGuildRankingSystem;

// Class definitions

// CBattleTournamentInfo
struct CBattleTournamentInfo
{
 bool m_bLoad;
 int m_nCurNum;
 TournamentWinner m_WinnerInfo[48];
};

// CPvpPointLimiter
struct CPvpPointLimiter
{
 _PVPPOINT_LIMIT_DB_BASE *m_pkInfo;
};

// CPvpOrderView
struct CPvpOrderView
{
 unsigned int m_dwLastAttackTime;
 unsigned int m_dwLastDamagedTime;
 int m_nKillCnt;
 int m_nDeahtCnt;
 long double m_dTodayPvpPoint;
 long double m_dOriginalPvpPoint;
 long double m_dPvpPoint;
 long double m_dPvpTempCash;
 long double m_dPvpCash;
 bool m_bAttack;
 bool m_bDamaged;
 _PVP_ORDER_VIEW_DB_BASE *m_pkInfo;
};

// CPvpUserRankingInfo
struct CPvpUserRankingInfo
{
 std::vector<_PVP_RANK_DATA *,std::allocator<_PVP_RANK_DATA *> > m_vecPvpRankDataCurrent;
 std::vector<_PVP_RANK_DATA *,std::allocator<_PVP_RANK_DATA *> > m_vecPvpRankDataTomorrow;
 std::vector<_PVP_RANK_PACKED_DATA *,std::allocator<_PVP_RANK_PACKED_DATA *> > m_vecPackedRankData;
 char m_byPvpRankDataVersion;
 std::vector<unsigned long *,std::allocator<unsigned long *> > m_dwUpdateRaceBossSerial;
 std::vector<unsigned long *,std::allocator<unsigned long *> > m_dwCurrentRaceBossSerial;
};

// CPvpUserRankingTargetUserList
struct CPvpUserRankingTargetUserList
{
 unsigned int m_uiAddTotalCnt;
 unsigned int m_uiRefreshCnt;
 std::vector<_PVP_RANK_REFRESH_USER *,std::allocator<_PVP_RANK_REFRESH_USER *> > m_PvpRankRefreshUser;
};

// CPvpCashMng
struct CPvpCashMng
{
 _talik_recvr_list m_TalikList;
 _class_value m_ClassValList[50];
 _max_point m_LimitPoint[30];
};

// CPvpUserAndGuildRankingSystem
struct CPvpUserAndGuildRankingSystem
{
 CLogFile *m_pkLogger;
 bool m_bInit;
 CUserRankingProcess m_kUserRankingProcess;
};


#endif // NETWARD_COMBAT_H
