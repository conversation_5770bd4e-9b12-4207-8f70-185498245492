#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?sell_unit@CMgrAvatorItemHistory@@QEAAXHEEMKKKKPEAD@Z
 * Address: 0x14023CA70

void  CMgrAvatorItemHistory::sell_unit(CMgrAvatorItemHistory *this, int n, char bySlotIndex, char byFrameCode, float fGaugeRate, unsigned int dwSellMoney, unsigned int dwPayDalant, unsigned int dwNewDalant, unsigned int dwNewGold, char *pszFileName)
{
  int64_t *v10; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v12; // [sp+0h] [bp-68h]@1
  double v13; // [sp+20h] [bp-48h]@4
  unsigned int v14; // [sp+28h] [bp-40h]@4
  unsigned int v15; // [sp+30h] [bp-38h]@4
  unsigned int v16; // [sp+38h] [bp-30h]@4
  unsigned int v17; // [sp+40h] [bp-28h]@4
  char *v18; // [sp+48h] [bp-20h]@4
  char *v19; // [sp+50h] [bp-18h]@4
  CMgrAvatorItemHistory *v20; // [sp+70h] [bp+8h]@1

  v20 = this;
  v10 = &v12;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t*)v10 = -858993460;
    v10 = (int64_t *)((char *)v10 + 4);
  }
  v19 = v20->m_szCurTime;
  v18 = v20->m_szCurDate;
  v17 = dwNewGold;
  v16 = dwNewDalant;
  v15 = dwPayDalant;
  v14 = dwSellMoney;
  v13 = (float)(fGaugeRate * 100.0);
  sprintf(
    sData,
    "UNIT SELL: %d>fr:%d gauge:%.0f%% rev(D:%u) pay(D:%u) $D:%u $G:%u [%s %s]\r\n",
    (unsigned int8_t)bySlotIndex,
    (unsigned int8_t)byFrameCode);
  CMgrAvatorItemHistory::WriteFile(v20, pszFileName, sData);
}
