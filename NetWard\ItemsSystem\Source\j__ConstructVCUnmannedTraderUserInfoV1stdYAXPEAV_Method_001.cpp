#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Construct@VCUnmannedTraderUserInfo@@V1@@std@@YAXPEAVCUnmannedTraderUserInfo@@AEBV1@@Z
 * Address: 0x1400079D7

void  std::_Construct<CUnmannedTraderUserInfo,CUnmannedTraderUserInfo>(CUnmannedTraderUserInfo *_Ptr, CUnmannedTraderUserInfo *_Val)
{
  std::_Construct<CUnmannedTraderUserInfo,CUnmannedTraderUserInfo>(_Ptr, _Val);
}
