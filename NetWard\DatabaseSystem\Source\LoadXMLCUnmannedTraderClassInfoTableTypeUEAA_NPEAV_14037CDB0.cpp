#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?LoadXML@CUnmannedTraderClassInfoTableType@@UEAA_NPEAVTiXmlElement@@AEAVCLogFile@@K@Z
 * Address: 0x14037CDB0

char  CUnmannedTraderClassInfoTableType::LoadXML(CUnmannedTraderClassInfoTableType *this, TiXmlElement *elemClass, CLogFile *kLogger, unsigned int dwDivisionID)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  CUnmannedTraderSubClassInfoVtbl *v7; // rax@24
  int64_t v8; // [sp+0h] [bp-108h]@1
  unsigned int v9; // [sp+20h] [bp-E8h]@17
  unsigned int v10; // [sp+28h] [bp-E0h]@21
  int v11; // [sp+34h] [bp-D4h]@6
  char *Source; // [sp+48h] [bp-C0h]@8
  int v13; // [sp+50h] [bp-B8h]@10
  char *szType; // [sp+58h] [bp-B0h]@10
  CUnmannedTraderSubClassFactory v15; // [sp+68h] [bp-A0h]@10
  CUnmannedTraderSubClassInfo *_Val; // [sp+A8h] [bp-60h]@10
  unsigned int dwID; // [sp+C4h] [bp-44h]@10
  TiXmlNode *v18; // [sp+D8h] [bp-30h]@10
  unsigned int v19; // [sp+E0h] [bp-28h]@14
  char v20; // [sp+E4h] [bp-24h]@12
  char v21; // [sp+E5h] [bp-23h]@17
  char v22; // [sp+E6h] [bp-22h]@19
  char v23; // [sp+E7h] [bp-21h]@21
  char v24; // [sp+E8h] [bp-20h]@23
  char v25; // [sp+E9h] [bp-1Fh]@25
  char v26; // [sp+EAh] [bp-1Eh]@27
  int64_t v27; // [sp+F0h] [bp-18h]@4
  CUnmannedTraderClassInfoTableType *v28; // [sp+110h] [bp+8h]@1
  TiXmlElement *v29; // [sp+118h] [bp+10h]@1
  CLogFile *v30; // [sp+120h] [bp+18h]@1
  unsigned int v31; // [sp+128h] [bp+20h]@1

  v31 = dwDivisionID;
  v30 = kLogger;
  v29 = elemClass;
  v28 = this;
  v4 = &v8;
  for ( i = 64i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v27 = -2i64;
  if ( !elemClass )
    return 0;
  v11 = -1;
  if ( !TiXmlElement::Attribute(elemClass, "tableIndex", &v11) )
  {
    CLogFile::Write(
      v30,
      "CUnmannedTraderClassInfo::LoadXML( TiXmlElement * pkElement, CLogFile & pkLogger )\r\n"
      "\t\tDivisionID(%u), ClassID(%u) pkElement->Attribute( tableIndex ) NULL!\r\n",
      v31,
      v28->m_dwID);
    return 0;
  }
  Source = (char *)TiXmlElement::Attribute(v29, "name");
  if ( !Source )
  {
    CLogFile::Write(
      v30,
      "CUnmannedTraderClassInfo::LoadXML( TiXmlElement * pkElement, CLogFile & pkLogger )\r\n"
      "\t\tDivisionID(%u), ClassID(%u) pkElement->Attribute( name ) NULL!\r\n",
      v31,
      v28->m_dwID);
    return 0;
  }
  v13 = 0;
  szType = 0i64;
  CUnmannedTraderSubClassFactory::CUnmannedTraderSubClassFactory(&v15);
  _Val = 0i64;
  dwID = -1;
  v18 = (TiXmlNode *)TiXmlNode::FirstChildElement((TiXmlNode *)&v29->vfptr, "subclass");
  if ( !v18 )
  {
    _Val = CUnmannedTraderSubClassFactory::Create(&v15, "default", 0);
    if ( !_Val )
    {
      CLogFile::Write(
        v30,
        "CUnmannedTraderDivisionInfo::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger )\r\n"
        "\t\tkFactoy.Create( default, 0 ) NULL!\r\n");
      v20 = 0;
      CUnmannedTraderSubClassFactory::~CUnmannedTraderSubClassFactory(&v15);
      return v20;
    }
    std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::push_back(
      &v28->m_vecSubClass,
      &_Val);
  }
  v19 = 0;
  while ( v18 )
  {
    szType = (char *)TiXmlElement::Attribute((TiXmlElement *)v18, "type");
    if ( !szType )
    {
      v9 = v28->m_dwID;
      CLogFile::Write(
        v30,
        "CUnmannedTraderClassInfo::LoadXML( TiXmlElement * pkElement, CLogFile & pkLogger, DWORD dwDivisionID(%u) )\r\n"
        "\t\tDivisionID(%u), ClassID(%u) pkElement->Attribute( code ) NULL!\r\n",
        v31,
        v31);
      v21 = 0;
      CUnmannedTraderSubClassFactory::~CUnmannedTraderSubClassFactory(&v15);
      return v21;
    }
    dwID = -1;
    if ( !TiXmlElement::Attribute((TiXmlElement *)v18, "id", (int *)&dwID) )
    {
      v9 = v28->m_dwID;
      CLogFile::Write(
        v30,
        "CUnmannedTraderClassInfo::LoadXML( TiXmlElement * pkElement, CLogFile & pkLogger, DWORD dwDivisionID(%u) )\r\n"
        "\t\tDivisionID(%u), ClassID(%u) 0 == elemSubClass->Attribute( id, &iID ) Fail!\r\n",
        v31,
        v31);
      v22 = 0;
      CUnmannedTraderSubClassFactory::~CUnmannedTraderSubClassFactory(&v15);
      return v22;
    }
    if ( !CUnmannedTraderClassInfoTableType::IsValidID(v28, dwID) )
    {
      v10 = dwID;
      v9 = v19;
      CLogFile::Write(
        v30,
        "CUnmannedTraderDivisionInfo::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger )\r\n"
        "\t\tDivisionID(%u), ClassID(%u) %dth SubClass IsValidID( iID(%u) ) Invalid!\r\n",
        v31,
        v28->m_dwID);
      v23 = 0;
      CUnmannedTraderSubClassFactory::~CUnmannedTraderSubClassFactory(&v15);
      return v23;
    }
    _Val = CUnmannedTraderSubClassFactory::Create(&v15, szType, dwID);
    if ( !_Val )
    {
      CLogFile::Write(
        v30,
        "CUnmannedTraderDivisionInfo::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger )\r\n"
        "\t\tkFactoy.Create( szSubClassType(%s), iID(%u) ) NULL!\r\n",
        szType,
        dwID);
      v24 = 0;
      CUnmannedTraderSubClassFactory::~CUnmannedTraderSubClassFactory(&v15);
      return v24;
    }
    v7 = _Val->vfptr;
    v9 = v28->m_dwID;
    if ( !(unsigned int8_t)((int ( *)(CUnmannedTraderSubClassInfo *, TiXmlNode *, CLogFile *, _QWORD))v7->LoadXML)(
                             _Val,
                             v18,
                             v30,
                             v31) )
    {
      v25 = 0;
      CUnmannedTraderSubClassFactory::~CUnmannedTraderSubClassFactory(&v15);
      return v25;
    }
    std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::push_back(
      &v28->m_vecSubClass,
      &_Val);
    v18 = (TiXmlNode *)TiXmlNode::NextSiblingElement(v18, "subclass");
    ++v19;
  }
  v28->m_byTableCode = v11;
  strcpy_0(v28->m_szClassName, Source);
  v26 = 1;
  CUnmannedTraderSubClassFactory::~CUnmannedTraderSubClassFactory(&v15);
  return v26;
}
