#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?MakeHashTable@CRecordData@@QEAA_NHHPEAD@Z
 * Address: 0x140044020

char  CRecordData::MakeHashTable(CRecordData *this, int offset, int len, char *pszErrMsg)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@7
  int64_t v7; // [sp+0h] [bp-48h]@1
  int n; // [sp+20h] [bp-28h]@12
  _base_fld *v9; // [sp+28h] [bp-20h]@14
  unsigned int *v10; // [sp+30h] [bp-18h]@12
  unsigned int64_t v11; // [sp+38h] [bp-10h]@12
  CRecordData *v12; // [sp+50h] [bp+8h]@1
  int v13; // [sp+58h] [bp+10h]@1
  int lena; // [sp+60h] [bp+18h]@1

  lena = len;
  v13 = offset;
  v12 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( v12->m_bLoad )
  {
    if ( v12->m_pdwHashList )
    {
      if ( pszErrMsg )
        sprintf(pszErrMsg, "̹ ؽ̺ε  ");
      result = 0;
    }
    else
    {
      v11 = v12->m_Header.m_nRecordNum;
      v10 = (unsigned int *)operator new[](saturated_mul(4ui64, v11));
      v12->m_pdwHashList = v10;
      for ( n = 0; n < v12->m_Header.m_nRecordNum; ++n )
      {
        v9 = CRecordData::GetRecord(v12, n);
        v12->m_pdwHashList[n] = CRecordData::MakeHash(&v9->m_strCode[v13], lena);
      }
      result = 1;
    }
  }
  else
  {
    if ( pszErrMsg )
      sprintf(pszErrMsg, "̺ε  ° ƴ");
    result = 0;
  }
  return result;
}
