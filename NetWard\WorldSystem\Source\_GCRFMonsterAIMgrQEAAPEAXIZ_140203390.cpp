#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??_GCRFMonsterAIMgr@@QEAAPEAXI@Z
 * Address: 0x140203390

CRFMonsterAIMgr * CRFMonsterAIMgr::`scalar deleting destructor'(CRFMonsterAIMgr *this, int a2)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-28h]@1
  CRFMonsterAIMgr *v6; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  CRFMonsterAIMgr::~CRFMonsterAIMgr(v6);
  if ( v7 & 1 )
    operator delete(v6);
  return v6;
}
