<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>

  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{K1L2M3N4-O5P6-7890-4567-123456789012}</ProjectGuid>
    <RootNamespace>WorldSystem</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemGroup>
    <ClCompile Include="Source\0allocatorPEAVCMoveMapLimitInfostdQEAAAEBV01Z_1403A2C20.cpp" />
    <ClCompile Include="Source\0allocatorPEAVCMoveMapLimitInfostdQEAAXZ_1403A25A0.cpp" />
    <ClCompile Include="Source\0allocatorPEAVCMoveMapLimitRightstdQEAAAEBV01Z_1403B05F0.cpp" />
    <ClCompile Include="Source\0allocatorPEAVCMoveMapLimitRightstdQEAAXZ_1403AF5D0.cpp" />
    <ClCompile Include="Source\0allocatorVCMoveMapLimitRightInfostdQEAAAEBV01Z_1403A2E90.cpp" />
    <ClCompile Include="Source\0allocatorVCMoveMapLimitRightInfostdQEAAXZ_1403A2800.cpp" />
    <ClCompile Include="Source\0BossSchedule_MapQEAAXZ_14041B720.cpp" />
    <ClCompile Include="Source\0CCircleZoneQEAAXZ_14012D660.cpp" />
    <ClCompile Include="Source\0CMapDataQEAAXZ_140180050.cpp" />
    <ClCompile Include="Source\0CMapDisplayQEAAXZ_14019D560.cpp" />
    <ClCompile Include="Source\0CMapExtendQEAAPEAPEAVCSurfaceZ_1401A1500.cpp" />
    <ClCompile Include="Source\0CMapExtendQEAAXZ_1401A1410.cpp" />
    <ClCompile Include="Source\0CMapOperationQEAAXZ_140195E20.cpp" />
    <ClCompile Include="Source\0CMapTabQEAAXZ_14002E480.cpp" />
    <ClCompile Include="Source\0CMonsterAggroMgrQEAAXZ_14015DB60.cpp" />
    <ClCompile Include="Source\0CMonsterAIQEAAXZ_14014F950.cpp" />
    <ClCompile Include="Source\0CMonsterEventRespawnQEAAXZ_1402A5D40.cpp" />
    <ClCompile Include="Source\0CMonsterEventSetQEAAXZ_1402A7920.cpp" />
    <ClCompile Include="Source\0CMonsterHierarchyQEAAXZ_14014B660.cpp" />
    <ClCompile Include="Source\0CMonsterQEAAXZ_1401414E0.cpp" />
    <ClCompile Include="Source\0CMoveMapLimitInfoListQEAAXZ_1403A1DC0.cpp" />
    <ClCompile Include="Source\0CMoveMapLimitInfoPortalQEAAIHZ_1403A3EE0.cpp" />
    <ClCompile Include="Source\0CMoveMapLimitInfoQEAAIHZ_1403A3D00.cpp" />
    <ClCompile Include="Source\0CMoveMapLimitManagerQEAAXZ_1403A1D10.cpp" />
    <ClCompile Include="Source\0CMoveMapLimitRightInfoListQEAAXZ_1403A1E10.cpp" />
    <ClCompile Include="Source\0CMoveMapLimitRightInfoQEAAAEBV0Z_1403AF990.cpp" />
    <ClCompile Include="Source\0CMoveMapLimitRightInfoQEAAXZ_1403AE760.cpp" />
    <ClCompile Include="Source\0CMoveMapLimitRightPortalQEAAHZ_1403AC7F0.cpp" />
    <ClCompile Include="Source\0CMoveMapLimitRightQEAAHZ_1403AE460.cpp" />
    <ClCompile Include="Source\0const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_14018CAC0.cpp" />
    <ClCompile Include="Source\0const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191BB0.cpp" />
    <ClCompile Include="Source\0const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191C10.cpp" />
    <ClCompile Include="Source\0CRFMonsterAIMgrQEAAXZ_14014C1E0.cpp" />
    <ClCompile Include="Source\0CWorldScheduleQEAAXZ_1403F34F0.cpp" />
    <ClCompile Include="Source\0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14018C980.cpp" />
    <ClCompile Include="Source\0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1401910C0.cpp" />
    <ClCompile Include="Source\0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_140191110.cpp" />
    <ClCompile Include="Source\0mapVbasic_stringDUchar_traitsDstdVallocatorD2stdU_140193BE0.cpp" />
    <ClCompile Include="Source\0MonsterSetInfoDataQEAAXZ_140161980.cpp" />
    <ClCompile Include="Source\0MonsterStateDataQEAAXZ_14014B700.cpp" />
    <ClCompile Include="Source\0pairViterator_TreeV_Tmap_traitsVbasic_stringDUcha_140191D10.cpp" />
    <ClCompile Include="Source\0ptr2userVCMonsterlua_tinkerQEAAPEAVCMonsterZ_14040B880.cpp" />
    <ClCompile Include="Source\0UpairCBVbasic_stringDUchar_traitsDstdVallocatorD2_1401942F0.cpp" />
    <ClCompile Include="Source\0UpairCBVbasic_stringDUchar_traitsDstdVallocatorD2_140194310.cpp" />
    <ClCompile Include="Source\0vectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveMap_1403A2060.cpp" />
    <ClCompile Include="Source\0vectorPEAVCMoveMapLimitRightVallocatorPEAVCMoveMa_1403AE7B0.cpp" />
    <ClCompile Include="Source\0vectorPEAVCMoveMapLimitRightVallocatorPEAVCMoveMa_1403AFB00.cpp" />
    <ClCompile Include="Source\0vectorVCMoveMapLimitRightInfoVallocatorVCMoveMapL_1403A2120.cpp" />
    <ClCompile Include="Source\0_Deque_mapIVallocatorIstdstdIEAAVallocatorI1Z_14065ABD0.cpp" />
    <ClCompile Include="Source\0_Deque_map_KVallocator_KstdstdIEAAVallocator_K1Z_140659590.cpp" />
    <ClCompile Include="Source\0_event_respawnQEAAXZ_1402A7740.cpp" />
    <ClCompile Include="Source\0_mapCGameStatisticsQEAAXZ_140232BE0.cpp" />
    <ClCompile Include="Source\0_map_fldQEAAXZ_140198FF0.cpp" />
    <ClCompile Include="Source\0_monster_create_setdataQEAAXZ_14014C340.cpp" />
    <ClCompile Include="Source\0_monster_set_event_setQEAAXZ_1402A9E80.cpp" />
    <ClCompile Include="Source\0_monster_sp_groupQEAAXZ_1401618D0.cpp" />
    <ClCompile Include="Source\0_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_tr_140192330.cpp" />
    <ClCompile Include="Source\0_notice_move_limit_map_msg_zoclQEAAXZ_1403A7100.cpp" />
    <ClCompile Include="Source\0_NPCQuestIndexTempDataQEAAXZ_140073EA0.cpp" />
    <ClCompile Include="Source\0_npc_create_setdataQEAAXZ_140199140.cpp" />
    <ClCompile Include="Source\0_npc_quest_list_result_zoclQEAAXZ_1400EFD20.cpp" />
    <ClCompile Include="Source\0_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1stdQ_1403A7400.cpp" />
    <ClCompile Include="Source\0_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1stdQ_1403A9D80.cpp" />
    <ClCompile Include="Source\0_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1std_1403AE650.cpp" />
    <ClCompile Include="Source\0_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1std_1403AFA30.cpp" />
    <ClCompile Include="Source\0_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEAAA_1403B0C60.cpp" />
    <ClCompile Include="Source\0_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEAAX_1403B18D0.cpp" />
    <ClCompile Include="Source\0_reged_char_result_zoneQEAAXZ_14011F680.cpp" />
    <ClCompile Include="Source\0_respawn_monster_act_dh_mission_mgrQEAAXZ_14026ECB0.cpp" />
    <ClCompile Include="Source\0_state_event_respawnQEAAXZ_1402A77C0.cpp" />
    <ClCompile Include="Source\0_state_monster_set_event_setQEAAXZ_1402A9ED0.cpp" />
    <ClCompile Include="Source\0_target_monster_aggro_inform_zoclQEAAXZ_1400F0010.cpp" />
    <ClCompile Include="Source\0_target_monster_contsf_allinform_zoclQEAAXZ_140073FF0.cpp" />
    <ClCompile Include="Source\0_Tmap_traitsVbasic_stringDUchar_traitsDstdValloca_140194290.cpp" />
    <ClCompile Include="Source\0_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDstdV_140193C40.cpp" />
    <ClCompile Include="Source\0_Tree_nodV_Tmap_traitsVbasic_stringDUchar_traitsD_1401941B0.cpp" />
    <ClCompile Include="Source\0_Tree_ptrV_Tmap_traitsVbasic_stringDUchar_traitsD_140194110.cpp" />
    <ClCompile Include="Source\0_Tree_valV_Tmap_traitsVbasic_stringDUchar_traitsD_140193DB0.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A7350.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A9C80.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AE5E0.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AF860.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403B0BF0.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403B1800.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorPEAVCMoveMapLimitInfoVallocatorPE_1403A8CF0.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorPEAVCMoveMapLimitInfoVallocatorPE_1403A8DB0.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403AF800.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403AF930.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorVCMoveMapLimitRightInfoVallocator_1403B0B90.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorVCMoveMapLimitRightInfoVallocator_1403B15E0.cpp" />
    <ClCompile Include="Source\0_Vector_valPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1403A2530.cpp" />
    <ClCompile Include="Source\0_Vector_valPEAVCMoveMapLimitRightVallocatorPEAVCM_1403AF560.cpp" />
    <ClCompile Include="Source\0_Vector_valVCMoveMapLimitRightInfoVallocatorVCMov_1403A2790.cpp" />
    <ClCompile Include="Source\0__add_monsterQEAAXZ_14027A3F0.cpp" />
    <ClCompile Include="Source\0__change_monsterQEAAXZ_14027A4D0.cpp" />
    <ClCompile Include="Source\0__monster_groupQEAAXZ_140279FB0.cpp" />
    <ClCompile Include="Source\0__respawn_monsterQEAAXZ_14027A450.cpp" />
    <ClCompile Include="Source\1BossSchedule_MapQEAAXZ_14041B430.cpp" />
    <ClCompile Include="Source\1CCircleZoneUEAAXZ_14012D6F0.cpp" />
    <ClCompile Include="Source\1CMapDataUEAAXZ_140180590.cpp" />
    <ClCompile Include="Source\1CMapDisplayUEAAXZ_14019D9C0.cpp" />
    <ClCompile Include="Source\1CMapExtendQEAAXZ_1401A1600.cpp" />
    <ClCompile Include="Source\1CMapOperationUEAAXZ_1401960C0.cpp" />
    <ClCompile Include="Source\1CMapTabUEAAXZ_14002E530.cpp" />
    <ClCompile Include="Source\1CMonsterAggroMgrQEAAXZ_14015DC90.cpp" />
    <ClCompile Include="Source\1CMonsterAIUEAAXZ_14014FA30.cpp" />
    <ClCompile Include="Source\1CMonsterEventRespawnUEAAXZ_1402A5DC0.cpp" />
    <ClCompile Include="Source\1CMonsterEventSetUEAAXZ_1402A79C0.cpp" />
    <ClCompile Include="Source\1CMonsterHierarchyUEAAXZ_140157350.cpp" />
    <ClCompile Include="Source\1CMonsterUEAAXZ_140141780.cpp" />
    <ClCompile Include="Source\1CMoveMapLimitInfoListQEAAXZ_1403A1FA0.cpp" />
    <ClCompile Include="Source\1CMoveMapLimitInfoPortalQEAAXZ_1403A3FD0.cpp" />
    <ClCompile Include="Source\1CMoveMapLimitInfoQEAAXZ_1403A3D60.cpp" />
    <ClCompile Include="Source\1CMoveMapLimitManagerQEAAXZ_1403A1F10.cpp" />
    <ClCompile Include="Source\1CMoveMapLimitRightInfoListQEAAXZ_1403A1E60.cpp" />
    <ClCompile Include="Source\1CMoveMapLimitRightInfoQEAAXZ_1403A3990.cpp" />
    <ClCompile Include="Source\1CMoveMapLimitRightQEAAXZ_1403AE740.cpp" />
    <ClCompile Include="Source\1const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_14018CA80.cpp" />
    <ClCompile Include="Source\1CRFMonsterAIMgrQEAAXZ_140203400.cpp" />
    <ClCompile Include="Source\1CWorldScheduleQEAAXZ_1403F4630.cpp" />
    <ClCompile Include="Source\1iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14018C8A0.cpp" />
    <ClCompile Include="Source\1mapVbasic_stringDUchar_traitsDstdVallocatorD2stdU_1401943D0.cpp" />
    <ClCompile Include="Source\1pairViterator_TreeV_Tmap_traitsVbasic_stringDUcha_14018EE50.cpp" />
    <ClCompile Include="Source\1ptr2userVCMonsterlua_tinkerUEAAXZ_14040BAC0.cpp" />
    <ClCompile Include="Source\1vectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveMap_1403A20E0.cpp" />
    <ClCompile Include="Source\1vectorPEAVCMoveMapLimitRightVallocatorPEAVCMoveMa_1403A3A20.cpp" />
    <ClCompile Include="Source\1vectorVCMoveMapLimitRightInfoVallocatorVCMoveMapL_1403A21A0.cpp" />
    <ClCompile Include="Source\1_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_tr_140195DD0.cpp" />
    <ClCompile Include="Source\1_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1stdQ_1403A7460.cpp" />
    <ClCompile Include="Source\1_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1std_1403AE5A0.cpp" />
    <ClCompile Include="Source\1_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEAAX_1403AFAC0.cpp" />
    <ClCompile Include="Source\1_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDstdV_140194410.cpp" />
    <ClCompile Include="Source\1_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A73C0.cpp" />
    <ClCompile Include="Source\1_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AE560.cpp" />
    <ClCompile Include="Source\1_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403AFA80.cpp" />
    <ClCompile Include="Source\1_Vector_iteratorPEAVCMoveMapLimitInfoVallocatorPE_1403A7310.cpp" />
    <ClCompile Include="Source\1_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403AE520.cpp" />
    <ClCompile Include="Source\1_Vector_iteratorVCMoveMapLimitRightInfoVallocator_1403AF9F0.cpp" />
    <ClCompile Include="Source\1__change_monsterQEAAXZ_140272E60.cpp" />
    <ClCompile Include="Source\4CMoveMapLimitRightInfoQEAAAEBV0AEBV0Z_1403AD590.cpp" />
    <ClCompile Include="Source\4const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_14018CB30.cpp" />
    <ClCompile Include="Source\4iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14018C9E0.cpp" />
    <ClCompile Include="Source\4_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1std_1403B2570.cpp" />
    <ClCompile Include="Source\4_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403B2500.cpp" />
    <ClCompile Include="Source\4_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B24A0.cpp" />
    <ClCompile Include="Source\8const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_14018ECA0.cpp" />
    <ClCompile Include="Source\8_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A8C80.cpp" />
    <ClCompile Include="Source\8_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AF8C0.cpp" />
    <ClCompile Include="Source\8_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403B1860.cpp" />
    <ClCompile Include="Source\9const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140195B00.cpp" />
    <ClCompile Include="Source\9MonsterStateDataQEBA_NAEBV0Z_14014C3E0.cpp" />
    <ClCompile Include="Source\9_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A7E70.cpp" />
    <ClCompile Include="Source\9_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AEDF0.cpp" />
    <ClCompile Include="Source\9_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403B1640.cpp" />
    <ClCompile Include="Source\AddMonsterCDarkHoleChannelQEAAXXZ_140268F40.cpp" />
    <ClCompile Include="Source\allocateallocatorPEAVCMoveMapLimitInfostdQEAAPEAPE_1403A2C90.cpp" />
    <ClCompile Include="Source\allocateallocatorPEAVCMoveMapLimitRightstdQEAAPEAP_1403B0610.cpp" />
    <ClCompile Include="Source\allocateallocatorU_Node_Tree_nodV_Tmap_traitsVbasi_140191E90.cpp" />
    <ClCompile Include="Source\allocateallocatorVCMoveMapLimitRightInfostdQEAAPEA_1403A2F00.cpp" />
    <ClCompile Include="Source\AmapVbasic_stringDUchar_traitsDstdVallocatorD2stdU_14018C400.cpp" />
    <ClCompile Include="Source\assignvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1403A7980.cpp" />
    <ClCompile Include="Source\assignvectorVCMoveMapLimitRightInfoVallocatorVCMov_1403AED40.cpp" />
    <ClCompile Include="Source\AutoRecoverCMonsterQEAAXXZ_140147440.cpp" />
    <ClCompile Include="Source\AvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveMap_1403A7950.cpp" />
    <ClCompile Include="Source\AvectorPEAVCMoveMapLimitRightVallocatorPEAVCMoveMa_1403AEAE0.cpp" />
    <ClCompile Include="Source\AvectorVCMoveMapLimitRightInfoVallocatorVCMoveMapL_1403A2260.cpp" />
    <ClCompile Include="Source\beginvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A77F0.cpp" />
    <ClCompile Include="Source\beginvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AE830.cpp" />
    <ClCompile Include="Source\beginvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AE8A0.cpp" />
    <ClCompile Include="Source\beginvectorVCMoveMapLimitRightInfoVallocatorVCMove_1403B0660.cpp" />
    <ClCompile Include="Source\begin_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_14018FF20.cpp" />
    <ClCompile Include="Source\CalcScheduleCursorCWorldScheduleQEAAHHHZ_1403F4180.cpp" />
    <ClCompile Include="Source\CalcStartNPCQuestCntCQuestMgrSA_NQEAKZ_14028B6E0.cpp" />
    <ClCompile Include="Source\capacityvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1403AA660.cpp" />
    <ClCompile Include="Source\capacityvectorPEAVCMoveMapLimitRightVallocatorPEAV_1403AEE60.cpp" />
    <ClCompile Include="Source\capacityvectorVCMoveMapLimitRightInfoVallocatorVCM_1403B16B0.cpp" />
    <ClCompile Include="Source\ChangeApparitionCMonsterQEAAX_NKZ_1401434E0.cpp" />
    <ClCompile Include="Source\ChangeLayerCMapDisplayQEAA_NGZ_14019F1C0.cpp" />
    <ClCompile Include="Source\ChangeMapCMapDisplayQEAA_NPEAVCMapDataZ_14019F130.cpp" />
    <ClCompile Include="Source\ChangeMonsterApparitionCDarkHoleChannelQEAAXHZ_140269700.cpp" />
    <ClCompile Include="Source\ChangeMonsterCDarkHoleChannelQEAAXXZ_140268570.cpp" />
    <ClCompile Include="Source\ChangeSchCursorCWorldScheduleQEAAXPEAU_WorldSchedu_1403F3EE0.cpp" />
    <ClCompile Include="Source\ChangeTargetPosDfAIMgrSAXPEAVCMonsterPEAMZ_140150E20.cpp" />
    <ClCompile Include="Source\ChatMapRequestCNetworkEXAEAA_NHPEADZ_1401C58C0.cpp" />
    <ClCompile Include="Source\CheckAlienationDfAIMgrSAHPEAVCMonsterZ_140151260.cpp" />
    <ClCompile Include="Source\CheckAutoRecoverHPCMonsterQEAAXXZ_140143370.cpp" />
    <ClCompile Include="Source\CheckCenterPosDummyCMapDataQEAA_NPEAU_dummy_positi_1401855D0.cpp" />
    <ClCompile Include="Source\CheckDelayDestroyCMonsterQEAA_NXZ_1401432F0.cpp" />
    <ClCompile Include="Source\CheckEmotionBadDfAIMgrSAHPEAVCMonsterPEAVCMonsterA_140151090.cpp" />
    <ClCompile Include="Source\CheckEmotionPresentationCMonsterQEAAXXZ_140147FD0.cpp" />
    <ClCompile Include="Source\CheckEventSetRespawnCMonsterEventSetQEAAXXZ_1402A8A90.cpp" />
    <ClCompile Include="Source\CheckGenDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_140152E40.cpp" />
    <ClCompile Include="Source\CheckMapPortalLinkCMapOperationAEAAXXZ_140197D40.cpp" />
    <ClCompile Include="Source\CheckMonArea_N_ChangeStateDfAIMgrSAHPEAVCMonsterAI_140153350.cpp" />
    <ClCompile Include="Source\CheckMonsterRotateCMonsterQEAAXXZ_140147B80.cpp" />
    <ClCompile Include="Source\CheckMonsterStateDataCMonsterQEAA_NXZ_1401435C0.cpp" />
    <ClCompile Include="Source\CheckNPCQuestListCQuestMgrQEAAXPEADEPEAU_NPCQuestI_140287ED0.cpp" />
    <ClCompile Include="Source\CheckRespawnEventCMonsterEventRespawnQEAAXXZ_1402A6FE0.cpp" />
    <ClCompile Include="Source\CheckRespawnMonsterCDarkHoleChannelQEAAXXZ_14026A0D0.cpp" />
    <ClCompile Include="Source\CheckRespawnProcessCMonsterQEAA_NXZ_140143070.cpp" />
    <ClCompile Include="Source\CheckSchCWorldScheduleQEAAXXZ_1403F3AA0.cpp" />
    <ClCompile Include="Source\CheckSPFDelayTimeDfAIMgrSAHPEAVCMonsterAIHKZ_140151850.cpp" />
    <ClCompile Include="Source\CheckSPFDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_140152150.cpp" />
    <ClCompile Include="Source\check_dummyAutominePersonalMgrQEAA_NPEAVCMapDataEP_1402DEBA0.cpp" />
    <ClCompile Include="Source\ChildKindCountCMonsterHierarchyQEAAEXZ_14014C320.cpp" />
    <ClCompile Include="Source\class_addVCMonsterlua_tinkerYAXPEAUlua_StatePEBDZ_140407BA0.cpp" />
    <ClCompile Include="Source\class_defVCMonsterP81EAAPEAVCLuaSignalReActorXZlua_140407D20.cpp" />
    <ClCompile Include="Source\CleanUpCMoveMapLimitInfoListAEAAXXZ_1403A6290.cpp" />
    <ClCompile Include="Source\CleanUpCMoveMapLimitRightInfoAEAAXXZ_1403AD710.cpp" />
    <ClCompile Include="Source\CleanUpCMoveMapLimitRightUEAAXXZ_1403AE4A0.cpp" />
    <ClCompile Include="Source\ClearBossSchedule_MapQEAAXXZ_14041B4D0.cpp" />
    <ClCompile Include="Source\ClearEmotionPresentationCMonsterQEAAXXZ_140147F80.cpp" />
    <ClCompile Include="Source\clearvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A79E0.cpp" />
    <ClCompile Include="Source\clearvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AEC20.cpp" />
    <ClCompile Include="Source\clear_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_1401958F0.cpp" />
    <ClCompile Include="Source\Command_ChildMonDestroyCMonsterQEAAXKZ_140143550.cpp" />
    <ClCompile Include="Source\constructallocatorPEAU_Node_Tree_nodV_Tmap_traitsV_140194230.cpp" />
    <ClCompile Include="Source\constructallocatorPEAVCMoveMapLimitRightstdQEAAXPE_1403B38B0.cpp" />
    <ClCompile Include="Source\constructallocatorVCMoveMapLimitRightInfostdQEAAXP_1403B3190.cpp" />
    <ClCompile Include="Source\ConvertLocalCMapDataQEAA_NPEAU_dummy_positionZ_140185100.cpp" />
    <ClCompile Include="Source\ConvertToMapCMapExtendQEAAXPEAVCSizeZ_1401A1DA0.cpp" />
    <ClCompile Include="Source\CreateAICMonsterQEAAHHZ_1401423D0.cpp" />
    <ClCompile Include="Source\CreateCCircleZoneQEAA_NPEAVCMapDataEZ_14012DA60.cpp" />
    <ClCompile Include="Source\CreateCGravityStoneRegenerQEAA_NPEAVCMapDataZ_14012E950.cpp" />
    <ClCompile Include="Source\CreateCMerchantQEAA_NPEAU_npc_create_setdataZ_140139140.cpp" />
    <ClCompile Include="Source\CreateCMonsterQEAA_NPEAU_monster_create_setdataZ_140141C50.cpp" />
    <ClCompile Include="Source\CreateCMoveMapLimitInfoSAPEAV1IHZ_1403A3DB0.cpp" />
    <ClCompile Include="Source\CreateCMoveMapLimitRightSAPEAV1HZ_1403AC5E0.cpp" />
    <ClCompile Include="Source\CreateFileMappingA_0_140676E22.cpp" />
    <ClCompile Include="Source\CreateMonsterCDarkHoleChannelQEAAXXZ_1402682C0.cpp" />
    <ClCompile Include="Source\CreateObjectCMapTabSAPEAVCObjectXZ_14002E350.cpp" />
    <ClCompile Include="Source\CreateObjSurfaceCMapDisplayAEAAJXZ_14019F690.cpp" />
    <ClCompile Include="Source\CreatePaletteFromBitmapCDisplayQEAAJPEAPEAUIDirect_1404346D0.cpp" />
    <ClCompile Include="Source\CreateRepMonsterYAPEAVCMonsterPEAVCMapDataGPEAMPEA_140148D90.cpp" />
    <ClCompile Include="Source\CreateRespawnMonsterYAPEAVCMonsterPEAVCMapDataGHPE_140148B90.cpp" />
    <ClCompile Include="Source\CreateSurfaceFromBitmapCDisplayQEAAJPEAPEAVCSurfac_140433B00.cpp" />
    <ClCompile Include="Source\D3DUtil_GetCubeMapViewMatrixYAAUD3DXMATRIXKZ_14052B5D0.cpp" />
    <ClCompile Include="Source\DataCheckCWorldScheduleQEAA_NXZ_1403F3FF0.cpp" />
    <ClCompile Include="Source\Dconst_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191210.cpp" />
    <ClCompile Include="Source\deallocateallocatorPEAVCMoveMapLimitInfostdQEAAXPE_1403A2C40.cpp" />
    <ClCompile Include="Source\deallocateallocatorPEAVCMoveMapLimitRightstdQEAAXP_1403A3BB0.cpp" />
    <ClCompile Include="Source\deallocateallocatorU_Node_Tree_nodV_Tmap_traitsVba_140191A00.cpp" />
    <ClCompile Include="Source\deallocateallocatorVCMoveMapLimitRightInfostdQEAAX_1403A2EB0.cpp" />
    <ClCompile Include="Source\defP6APEAVCMonsterPEAD0MMMZlua_tinkerYAXPEAUlua_St_140407ED0.cpp" />
    <ClCompile Include="Source\DestoryCRFMonsterAIMgrSAXXZ_140203300.cpp" />
    <ClCompile Include="Source\destroyallocatorPEAU_Node_Tree_nodV_Tmap_traitsVba_1401940C0.cpp" />
    <ClCompile Include="Source\destroyallocatorPEAVCMoveMapLimitRightstdQEAAXPEAP_1403B3910.cpp" />
    <ClCompile Include="Source\destroyallocatorU_Node_Tree_nodV_Tmap_traitsVbasic_140195C50.cpp" />
    <ClCompile Include="Source\destroyallocatorVCMoveMapLimitRightInfostdQEAAXPEA_1403A3880.cpp" />
    <ClCompile Include="Source\DestroyCCircleZoneQEAAXXZ_14012DB70.cpp" />
    <ClCompile Include="Source\DestroyCMonsterQEAA_NEPEAVCGameObjectZ_1401424F0.cpp" />
    <ClCompile Include="Source\DestroyCMoveMapLimitManagerSAXXZ_1403A16B0.cpp" />
    <ClCompile Include="Source\destroyerVCMonsterlua_tinkerYAHPEAUlua_StateZ_140408950.cpp" />
    <ClCompile Include="Source\Diterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14018EC60.cpp" />
    <ClCompile Include="Source\DoDataExchangeCMapTabMEAAXPEAVCDataExchangeZ_14002E5C0.cpp" />
    <ClCompile Include="Source\DrawBitmapCSurfaceQEAAJPEADKKZ_140435410.cpp" />
    <ClCompile Include="Source\DrawBitmapCSurfaceQEAAJPEAUHBITMAP__KKKKZ_140434FE0.cpp" />
    <ClCompile Include="Source\DrawCollisionLineCMapDisplayAEAAXXZ_1401A02A0.cpp" />
    <ClCompile Include="Source\DrawDisplayCMapDisplayQEAAXXZ_14019F260.cpp" />
    <ClCompile Include="Source\DrawDummyCMapDisplayAEAAXXZ_1401A0010.cpp" />
    <ClCompile Include="Source\DrawLightMapGroupYAXPEAVCVertexBufferPEAU_BSP_MAT__1404F1590.cpp" />
    <ClCompile Include="Source\DrawMapCMapDisplayAEAAXXZ_14019F450.cpp" />
    <ClCompile Include="Source\DrawMapEntitiesRenderCBspQEAAXXZ_1404FBBB0.cpp" />
    <ClCompile Include="Source\DrawObjectCMapDisplayAEAAXXZ_14019F4E0.cpp" />
    <ClCompile Include="Source\DrawRectCMapExtendQEAAXXZ_1401A1C40.cpp" />
    <ClCompile Include="Source\DrawSelectMonsterLookAtPosCMapDisplayAEAAJPEAVCMon_14019FA70.cpp" />
    <ClCompile Include="Source\DrawTextACMapDisplayAEAAXXZ_1401A0340.cpp" />
    <ClCompile Include="Source\D_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A7E20.cpp" />
    <ClCompile Include="Source\D_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AEDA0.cpp" />
    <ClCompile Include="Source\D_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B31F0.cpp" />
    <ClCompile Include="Source\Econst_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191C70.cpp" />
    <ClCompile Include="Source\Eiterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_140191170.cpp" />
    <ClCompile Include="Source\Eiterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1401959B0.cpp" />
    <ClCompile Include="Source\emptyvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AEA70.cpp" />
    <ClCompile Include="Source\EndScreenPointCMapExtendQEAAHPEAVCSizeZ_1401A1940.cpp" />
    <ClCompile Include="Source\endvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveM_1403A7860.cpp" />
    <ClCompile Include="Source\endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_1403AE910.cpp" />
    <ClCompile Include="Source\endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_1403AE980.cpp" />
    <ClCompile Include="Source\endvectorVCMoveMapLimitRightInfoVallocatorVCMoveMa_1403B06D0.cpp" />
    <ClCompile Include="Source\end_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDst_14018D6A0.cpp" />
    <ClCompile Include="Source\EnterMapCMapDataQEAAXPEAVCGameObjectKZ_140184D30.cpp" />
    <ClCompile Include="Source\EnterWorldRequestCNetworkEXAEAA_NHPEAU_MSG_HEADERP_1401D0D30.cpp" />
    <ClCompile Include="Source\EnterWorldResultCNetworkEXAEAA_NKPEADZ_1401C06D0.cpp" />
    <ClCompile Include="Source\erasevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A8310.cpp" />
    <ClCompile Include="Source\erasevectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AF210.cpp" />
    <ClCompile Include="Source\erasevectorVCMoveMapLimitRightInfoVallocatorVCMove_1403B0820.cpp" />
    <ClCompile Include="Source\erase_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140194660.cpp" />
    <ClCompile Include="Source\erase_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140194B20.cpp" />
    <ClCompile Include="Source\ExitMapCMapDataQEAAXPEAVCGameObjectKZ_140184EC0.cpp" />
    <ClCompile Include="Source\ExitWorldRequestCNetworkEXAEAA_NHPEADZ_1401C9D20.cpp" />
    <ClCompile Include="Source\E_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A7E40.cpp" />
    <ClCompile Include="Source\E_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AEDC0.cpp" />
    <ClCompile Include="Source\E_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B3230.cpp" />
    <ClCompile Include="Source\Fconst_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191CC0.cpp" />
    <ClCompile Include="Source\fillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVCMov_1403AAFE0.cpp" />
    <ClCompile Include="Source\fillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAVCMo_1403B1FD0.cpp" />
    <ClCompile Include="Source\fillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMoveMap_1403B22D0.cpp" />
    <ClCompile Include="Source\FindEmptyNPCYAPEAVCMerchantPEAV1HZ_140139CD0.cpp" />
    <ClCompile Include="Source\findV_Vector_iteratorPEAVCMoveMapLimitRightValloca_1403B1920.cpp" />
    <ClCompile Include="Source\Fiterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1401911C0.cpp" />
    <ClCompile Include="Source\FrameMoveMapEntitiesCBspQEAAXXZ_1404FB7D0.cpp" />
    <ClCompile Include="Source\GeEmotionImpStdTimeCMonsterQEAAMXZ_1401558D0.cpp" />
    <ClCompile Include="Source\GetAggroResetTimeCMonsterQEAAKXZ_1401617D0.cpp" />
    <ClCompile Include="Source\GetAggroShortTimeCMonsterQEAAKXZ_140161790.cpp" />
    <ClCompile Include="Source\GetAngleCMonsterHelperSAMQEAM0Z_1401597A0.cpp" />
    <ClCompile Include="Source\GetBonusInAreaAggroCMonsterQEAAMXZ_140161890.cpp" />
    <ClCompile Include="Source\GetBspInfoCMapDataQEAAPEAU_bsp_infoXZ_1401843C0.cpp" />
    <ClCompile Include="Source\GetChildCMonsterHierarchyQEAAPEAVCMonsterHHZ_140157DA0.cpp" />
    <ClCompile Include="Source\GetChildCountCMonsterHierarchyQEAAKHZ_140161530.cpp" />
    <ClCompile Include="Source\GetCMoveMapLimitInfoListAEAAPEAVCMoveMapLimitInfoH_1403A6020.cpp" />
    <ClCompile Include="Source\GetCMoveMapLimitRightInfoListQEAAPEAVCMoveMapLimit_1403A1FE0.cpp" />
    <ClCompile Include="Source\GetColorCCircleZoneQEAAEXZ_140034B20.cpp" />
    <ClCompile Include="Source\GetCommandMapCCmdTargetMEBAPEBUAFX_OLECMDMAPXZ_0_1404DBBC8.cpp" />
    <ClCompile Include="Source\GetCritical_Exception_RateCMonsterUEAAHXZ_14014BB70.cpp" />
    <ClCompile Include="Source\GetDefFacingCMonsterUEAAMHZ_14014BA70.cpp" />
    <ClCompile Include="Source\GetDefGapCMonsterUEAAMHZ_14014BA20.cpp" />
    <ClCompile Include="Source\GetDirectionCMonsterHelperSAXAEAY02M00MZ_1401598E0.cpp" />
    <ClCompile Include="Source\GetDispatchMapCCmdTargetMEBAPEBUAFX_DISPMAPXZ_0_1404DBBCE.cpp" />
    <ClCompile Include="Source\GetDummyPostionCMapDataQEAAPEAU_dummy_positionPEAD_140186440.cpp" />
    <ClCompile Include="Source\GetEmotionStateCMonsterQEAAEXZ_140143810.cpp" />
    <ClCompile Include="Source\GetEmptyEventSetCMonsterEventSetQEAAPEAU_event_set_1402A8FA0.cpp" />
    <ClCompile Include="Source\GetEvenSetLootingCMonsterEventSetQEAAPEAU_event_se_1402A90B0.cpp" />
    <ClCompile Include="Source\GetEventSinkMapCCmdTargetMEBAPEBUAFX_EVENTSINKMAPX_1404DBBE0.cpp" />
    <ClCompile Include="Source\GetExtendSizeCMapExtendQEAAPEAVCSizeXZ_14002D0B0.cpp" />
    <ClCompile Include="Source\GetFireTolCMonsterUEAAHXZ_140145620.cpp" />
    <ClCompile Include="Source\GetHelpMeCaseCMonsterQEAAHXZ_1401554F0.cpp" />
    <ClCompile Include="Source\GetHPCMonsterUEAAHXZ_1401461E0.cpp" />
    <ClCompile Include="Source\GetInterfaceMapCCmdTargetMEBAPEBUAFX_INTERFACEMAPX_1404DBF52.cpp" />
    <ClCompile Include="Source\GetInterfaceMapCWndMEBAPEBUAFX_INTERFACEMAPXZ_0_1404DBBDA.cpp" />
    <ClCompile Include="Source\GetInxCMoveMapLimitInfoQEAAIXZ_1403A74A0.cpp" />
    <ClCompile Include="Source\GetLightMapColorYAKQEAMHZ_140502530.cpp" />
    <ClCompile Include="Source\GetLightMapSurfaceYAPEAXHZ_1405025F0.cpp" />
    <ClCompile Include="Source\GetLightMapTexSizeYAKXZ_140500900.cpp" />
    <ClCompile Include="Source\GetLightMapUVFromPointCBspAEAAXQEAMH0Z_1404E3370.cpp" />
    <ClCompile Include="Source\GetLinkPortalCMapDataQEAAPEAU_portal_dummyPEADZ_1401846E0.cpp" />
    <ClCompile Include="Source\GetLocalFromWorldCExtDummyQEAAHPEAY02MKQEAMZ_1404DF180.cpp" />
    <ClCompile Include="Source\GetLostMonsterTargetDistanceMonsterSetInfoDataQEAA_140155670.cpp" />
    <ClCompile Include="Source\GetMapCMapOperationQEAAHPEAVCMapDataZ_1401979B0.cpp" />
    <ClCompile Include="Source\GetMapCMapOperationQEAAPEAVCMapDataHZ_140197970.cpp" />
    <ClCompile Include="Source\GetMapCMapOperationQEAAPEAVCMapDataPEADZ_140197A30.cpp" />
    <ClCompile Include="Source\GetMapCodeCMapDataQEAAEXZ_1400C2CD0.cpp" />
    <ClCompile Include="Source\GetMapCurDirectCTransportShipQEAAPEAVCMapDataXZ_140264DD0.cpp" />
    <ClCompile Include="Source\GetMapDataCGuildRoomInfoQEAAPEAVCMapDataXZ_1402EB250.cpp" />
    <ClCompile Include="Source\GetMapPosCGuildRoomInfoQEAA_NPEAMZ_1402E6690.cpp" />
    <ClCompile Include="Source\GetMaxDMGSFContCountCMonsterQEAAHXZ_140147050.cpp" />
    <ClCompile Include="Source\GetMaxHPCMonsterUEAAHXZ_1401462A0.cpp" />
    <ClCompile Include="Source\GetMaxToleranceProbMaxMonsterSetInfoDataQEAAMHZ_14014BDF0.cpp" />
    <ClCompile Include="Source\GetMipMapSkipSizeYAHPEAU_DDSURFACEDESC2KKKZ_1404FFE70.cpp" />
    <ClCompile Include="Source\GetMob_AsistTypeCMonsterQEAAHXZ_140161590.cpp" />
    <ClCompile Include="Source\GetMob_SubRaceCMonsterQEAAHXZ_140161570.cpp" />
    <ClCompile Include="Source\GetMonStateInfoCMonsterQEAAGXZ_140143720.cpp" />
    <ClCompile Include="Source\GetMonsterDropRateMonsterSetInfoDataQEAAKHZ_14015D510.cpp" />
    <ClCompile Include="Source\GetMonsterForcePowerRateMonsterSetInfoDataQEAAMXZ_140161640.cpp" />
    <ClCompile Include="Source\GetMonsterGradeCMonsterQEAAHXZ_14014BFD0.cpp" />
    <ClCompile Include="Source\GetMonsterNumInCurMissionAreaCDarkHoleChannelQEAAH_14026B170.cpp" />
    <ClCompile Include="Source\GetMonsterSetCMonsterEventSetQEAAPEAU_monster_set__1402A9030.cpp" />
    <ClCompile Include="Source\GetMoveSpeedCMonsterQEAAMXZ_140142D80.cpp" />
    <ClCompile Include="Source\GetMoveTypeCMonsterQEAAEXZ_1401437B0.cpp" />
    <ClCompile Include="Source\GetMyDMGSFContCountCMonsterQEAAHXZ_1401470B0.cpp" />
    <ClCompile Include="Source\GetNewMonSerialCMonsterSAKXZ_14014BFF0.cpp" />
    <ClCompile Include="Source\GetObjNameCMonsterUEAAPEADXZ_140142700.cpp" />
    <ClCompile Include="Source\GetObjRaceCMonsterUEAAHXZ_14014BB60.cpp" />
    <ClCompile Include="Source\GetOffensiveTypeCMonsterQEAAHXZ_1401554D0.cpp" />
    <ClCompile Include="Source\GetParentCMonsterHierarchyQEAAPEAVCMonsterXZ_14014C300.cpp" />
    <ClCompile Include="Source\GetPathFinderCMonsterAIQEAAPEAVCPathMgrXZ_1401555B0.cpp" />
    <ClCompile Include="Source\GetPortalCMapDataQEAAPEAU_portal_dummyHZ_1401846A0.cpp" />
    <ClCompile Include="Source\GetPortalCMapDataQEAAPEAU_portal_dummyPEADZ_140184550.cpp" />
    <ClCompile Include="Source\GetPortalInxCCircleZoneQEAAHXZ_140034B00.cpp" />
    <ClCompile Include="Source\GetPortalInxCMapDataQEAAHPEADZ_140184600.cpp" />
    <ClCompile Include="Source\GetPosStartMapCMapOperationQEAAPEAVCMapDataE_NPEAM_140197B90.cpp" />
    <ClCompile Include="Source\GetRaceTownCMapDataQEAAEPEAMEZ_1401862D0.cpp" />
    <ClCompile Include="Source\GetRandPosInDummyCMapDataQEAA_NPEAU_dummy_position_1401857A0.cpp" />
    <ClCompile Include="Source\GetRandPosInRangeCMapDataQEAA_NPEAMH0Z_140185B10.cpp" />
    <ClCompile Include="Source\GetRandPosVirtualDumCMapDataQEAA_NPEAMH0Z_140185C70.cpp" />
    <ClCompile Include="Source\GetRandPosVirtualDumExcludeStdRangeCMapDataQEAA_NP_140185EE0.cpp" />
    <ClCompile Include="Source\GetRectInRadiusCMapDataQEAAXPEAU_pnt_rectHHZ_1401843E0.cpp" />
    <ClCompile Include="Source\GetResDummySectorCMapDataQEAAHHPEAMZ_140184950.cpp" />
    <ClCompile Include="Source\GetRuntimeClassCMapTabUEBAPEAUCRuntimeClassXZ_14002E460.cpp" />
    <ClCompile Include="Source\GetSecInfoCMapDataQEAAPEAU_sec_infoXZ_1401843A0.cpp" />
    <ClCompile Include="Source\GetSectorIndexCMapDataQEAAHPEAMZ_140184790.cpp" />
    <ClCompile Include="Source\GetSectorListObjCMapDataQEAAPEAVCObjectListGKZ_140184890.cpp" />
    <ClCompile Include="Source\GetSectorListTowerCMapDataQEAAPEAVCObjectListGKZ_140184910.cpp" />
    <ClCompile Include="Source\GetSectorNumByLayerIndexCMapDataQEAAHGZ_1401866D0.cpp" />
    <ClCompile Include="Source\GetSettlementMapDataCMapOperationQEAAPEAVCMapDataH_1402D7960.cpp" />
    <ClCompile Include="Source\GetSignalReActorCMonsterQEAAPEAVCLuaSignalReActorX_140406790.cpp" />
    <ClCompile Include="Source\GetSoilTolCMonsterUEAAHXZ_140145820.cpp" />
    <ClCompile Include="Source\GetStartMapCMapOperationQEAAPEAVCMapDataEZ_140197AE0.cpp" />
    <ClCompile Include="Source\GetStateChunkMonsterStateDataQEBAGXZ_14014C450.cpp" />
    <ClCompile Include="Source\GetStateTBLCRFMonsterAIMgrQEAAAVUsPointVUsStateTBL_14014C040.cpp" />
    <ClCompile Include="Source\GetThisClassCMapTabSAPEAUCRuntimeClassXZ_14002E440.cpp" />
    <ClCompile Include="Source\GetTypeCMoveMapLimitInfoQEAAHXZ_1403A6F50.cpp" />
    <ClCompile Include="Source\GetTypeCMoveMapLimitRightQEBAHXZ_1403AE6B0.cpp" />
    <ClCompile Include="Source\GetViewAngleCapCMonsterQEAA_NHAEAHZ_140146C20.cpp" />
    <ClCompile Include="Source\GetVisualAngleCMonsterQEAAMXZ_14014CAA0.cpp" />
    <ClCompile Include="Source\GetVisualFieldCMonsterQEAAMXZ_14014BF80.cpp" />
    <ClCompile Include="Source\GetWaterTolCMonsterUEAAHXZ_140145720.cpp" />
    <ClCompile Include="Source\GetWidthCMonsterUEAAMXZ_140146610.cpp" />
    <ClCompile Include="Source\GetWindTolCMonsterUEAAHXZ_140145920.cpp" />
    <ClCompile Include="Source\GetWorldFromLocalCExtDummyQEAAHPEAY02MKQEAMZ_1404DF130.cpp" />
    <ClCompile Include="Source\GetYAngleByteCMonsterQEAAEXZ_14014CB50.cpp" />
    <ClCompile Include="Source\GetYAngleCMonsterQEAAMXZ_1401438D0.cpp" />
    <ClCompile Include="Source\gm_MapChangeCMainThreadQEAAXPEAVCMapDataZ_1401F79D0.cpp" />
    <ClCompile Include="Source\gm_UpdateMapCMainThreadQEAAXXZ_1401F7E60.cpp" />
    <ClCompile Include="Source\GoalCCircleZoneQEAAEPEAVCMapDataPEAMZ_14012DBE0.cpp" />
    <ClCompile Include="Source\G_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403B15B0.cpp" />
    <ClCompile Include="Source\G_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B0B40.cpp" />
    <ClCompile Include="Source\HearMapSoundCBspQEAAXXZ_1404FD620.cpp" />
    <ClCompile Include="Source\HierarcyHelpCastCMonsterHelperSAXPEAVCMonsterZ_14015A480.cpp" />
    <ClCompile Include="Source\H_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B09F0.cpp" />
    <ClCompile Include="Source\InsertNpcQuestHistoryCQuestMgrQEAAEPEADENZ_14028B320.cpp" />
    <ClCompile Include="Source\insertvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1403A91B0.cpp" />
    <ClCompile Include="Source\insertvectorPEAVCMoveMapLimitRightVallocatorPEAVCM_1403AEEE0.cpp" />
    <ClCompile Include="Source\insertvectorVCMoveMapLimitRightInfoVallocatorVCMov_1403B0740.cpp" />
    <ClCompile Include="Source\insert_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018D710.cpp" />
    <ClCompile Include="Source\insert_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FFC0.cpp" />
    <ClCompile Include="Source\InstanceCMoveMapLimitManagerSAPEAV1XZ_1403A15F0.cpp" />
    <ClCompile Include="Source\InstanceCRFMonsterAIMgrSAPEAV1XZ_14014C100.cpp" />
    <ClCompile Include="Source\invokelua2objectPEAVCMonsterlua_tinkerSAPEAVCMonst_14040B1C0.cpp" />
    <ClCompile Include="Source\invokeobject2luaPEAVCMonsterlua_tinkerSAXPEAUlua_S_14040B350.cpp" />
    <ClCompile Include="Source\invokePEAVCLuaSignalReActormem_functorVCMonsterXXX_140409390.cpp" />
    <ClCompile Include="Source\invokePEAVCMonsterfunctorPEADPEADMMMlua_tinkerSAHP_140409470.cpp" />
    <ClCompile Include="Source\invokeptr2luaVCMonsterlua_tinkerSAXPEAUlua_StatePE_14040B670.cpp" />
    <ClCompile Include="Source\invokeuser2typeP6APEAVCMonsterPEAD0MMMZlua_tinkerS_14040A830.cpp" />
    <ClCompile Include="Source\invokeuser2typeP8CMonsterEAAPEAVCLuaSignalReActorX_14040A770.cpp" />
    <ClCompile Include="Source\invokevoid2ptrA6APEAVCMonsterPEAD0MMMZlua_tinkerSA_14040AEE0.cpp" />
    <ClCompile Include="Source\invokevoid2ptrVCMonsterlua_tinkerSAPEAVCMonsterPEA_14040B790.cpp" />
    <ClCompile Include="Source\invokevoid2typeP6APEAVCMonsterPEAD0MMMZlua_tinkerS_14040ACB0.cpp" />
    <ClCompile Include="Source\invokevoid2typeP8CMonsterEAAPEAVCLuaSignalReActorX_14040AC30.cpp" />
    <ClCompile Include="Source\invokevoid2typePEAVCMonsterlua_tinkerSAPEAVCMonste_14040B410.cpp" />
    <ClCompile Include="Source\invokevoid2valP8CMonsterEAAPEAVCLuaSignalReActorXZ_14040AEB0.cpp" />
    <ClCompile Include="Source\IsBossMonsterCMonsterQEAA_NXZ_14007D4E0.cpp" />
    <ClCompile Include="Source\IsCompleteNpcQuestCQuestMgrQEAA_NPEADHZ_14028A850.cpp" />
    <ClCompile Include="Source\IsEqualLimitCMoveMapLimitInfoQEAA_NHHKZ_1403A3E70.cpp" />
    <ClCompile Include="Source\IsExistStdMapIDCMapOperationQEAA_NHZ_140120AB0.cpp" />
    <ClCompile Include="Source\IsHaveRightCMoveMapLimitRightInfoQEAA_NHZ_1403ACBA0.cpp" />
    <ClCompile Include="Source\IsHaveRightCMoveMapLimitRightPortalUEAA_NXZ_1403AC6A0.cpp" />
    <ClCompile Include="Source\IsHaveRightCMoveMapLimitRightUEAA_NXZ_1403AE4F0.cpp" />
    <ClCompile Include="Source\IsINIFileChangedCMonsterEventSetQEAA_NPEBDU_FILETI_1402A9150.cpp" />
    <ClCompile Include="Source\IsInRegionCMapOperationQEAA_NPEADPEAVCGameObjectZ_140197C50.cpp" />
    <ClCompile Include="Source\IsInSectorCMonsterHelperSAHQEAM00MMPEAMZ_140158160.cpp" />
    <ClCompile Include="Source\IsMapInCMapDataQEAA_NPEAMZ_140184B40.cpp" />
    <ClCompile Include="Source\IsMovableCMonsterQEAA_NXZ_140142E20.cpp" />
    <ClCompile Include="Source\IsNearPositionCCircleZoneAEAA_NPEBMZ_14012DE20.cpp" />
    <ClCompile Include="Source\IsPossibleRepeatNpcQuestCQuestMgrQEAA_NPEADHZ_14028A900.cpp" />
    <ClCompile Include="Source\IsProcLinkNpcQuestCQuestMgrQEAA_NPEADHZ_14028AA30.cpp" />
    <ClCompile Include="Source\IsProcNpcQuestCQuestMgrQEAA_NPEADZ_14028AB00.cpp" />
    <ClCompile Include="Source\IsRoateMonsterCMonsterQEAA_NXZ_1401555D0.cpp" />
    <ClCompile Include="Source\IsRotateBlockMonsterSetInfoDataQEAA_NPEAU_mon_bloc_14015D110.cpp" />
    <ClCompile Include="Source\IsSame_target_monster_contsf_allinform_zoclSA_NAEA_1400F01A0.cpp" />
    <ClCompile Include="Source\j_0allocatorPEAVCMoveMapLimitInfostdQEAAAEBV01Z_1400131B5.cpp" />
    <ClCompile Include="Source\j_0allocatorPEAVCMoveMapLimitInfostdQEAAXZ_1400106D1.cpp" />
    <ClCompile Include="Source\j_0allocatorPEAVCMoveMapLimitRightstdQEAAAEBV01Z_14000B320.cpp" />
    <ClCompile Include="Source\j_0allocatorPEAVCMoveMapLimitRightstdQEAAXZ_140012C42.cpp" />
    <ClCompile Include="Source\j_0allocatorVCMoveMapLimitRightInfostdQEAAAEBV01Z_14000F187.cpp" />
    <ClCompile Include="Source\j_0allocatorVCMoveMapLimitRightInfostdQEAAXZ_140006D89.cpp" />
    <ClCompile Include="Source\j_0BossSchedule_MapQEAAXZ_140011770.cpp" />
    <ClCompile Include="Source\j_0CCircleZoneQEAAXZ_140009C3C.cpp" />
    <ClCompile Include="Source\j_0CMapDataQEAAXZ_14000DF1C.cpp" />
    <ClCompile Include="Source\j_0CMapDisplayQEAAXZ_140013E08.cpp" />
    <ClCompile Include="Source\j_0CMapExtendQEAAPEAPEAVCSurfaceZ_140004011.cpp" />
    <ClCompile Include="Source\j_0CMapExtendQEAAXZ_14000D55D.cpp" />
    <ClCompile Include="Source\j_0CMapOperationQEAAXZ_14000E390.cpp" />
    <ClCompile Include="Source\j_0CMapTabQEAAXZ_14000C991.cpp" />
    <ClCompile Include="Source\j_0CMonsterAggroMgrQEAAXZ_14000FBD2.cpp" />
    <ClCompile Include="Source\j_0CMonsterAIQEAAXZ_140007CD4.cpp" />
    <ClCompile Include="Source\j_0CMonsterEventRespawnQEAAXZ_140002CED.cpp" />
    <ClCompile Include="Source\j_0CMonsterEventSetQEAAXZ_140007CCF.cpp" />
    <ClCompile Include="Source\j_0CMonsterHierarchyQEAAXZ_140007577.cpp" />
    <ClCompile Include="Source\j_0CMonsterQEAAXZ_140011DA6.cpp" />
    <ClCompile Include="Source\j_0CMoveMapLimitInfoListQEAAXZ_1400082B0.cpp" />
    <ClCompile Include="Source\j_0CMoveMapLimitInfoPortalQEAAIHZ_14000BDF2.cpp" />
    <ClCompile Include="Source\j_0CMoveMapLimitInfoQEAAIHZ_14000210D.cpp" />
    <ClCompile Include="Source\j_0CMoveMapLimitManagerQEAAXZ_1400114EB.cpp" />
    <ClCompile Include="Source\j_0CMoveMapLimitRightInfoListQEAAXZ_14000E12E.cpp" />
    <ClCompile Include="Source\j_0CMoveMapLimitRightInfoQEAAAEBV0Z_14000EDEA.cpp" />
    <ClCompile Include="Source\j_0CMoveMapLimitRightInfoQEAAXZ_140013C5A.cpp" />
    <ClCompile Include="Source\j_0CMoveMapLimitRightPortalQEAAHZ_140002F4A.cpp" />
    <ClCompile Include="Source\j_0CMoveMapLimitRightQEAAHZ_140003959.cpp" />
    <ClCompile Include="Source\j_0const_iterator_TreeV_Tmap_traitsVbasic_stringDU_140005BFF.cpp" />
    <ClCompile Include="Source\j_0const_iterator_TreeV_Tmap_traitsVbasic_stringDU_1400078D8.cpp" />
    <ClCompile Include="Source\j_0const_iterator_TreeV_Tmap_traitsVbasic_stringDU_14000A52E.cpp" />
    <ClCompile Include="Source\j_0CRFMonsterAIMgrQEAAXZ_14000A5FB.cpp" />
    <ClCompile Include="Source\j_0CWorldScheduleQEAAXZ_14000B0F0.cpp" />
    <ClCompile Include="Source\j_0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_14000375B.cpp" />
    <ClCompile Include="Source\j_0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_14000BD34.cpp" />
    <ClCompile Include="Source\j_0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_140013F48.cpp" />
    <ClCompile Include="Source\j_0mapVbasic_stringDUchar_traitsDstdVallocatorD2st_140013FED.cpp" />
    <ClCompile Include="Source\j_0MonsterSetInfoDataQEAAXZ_14000B389.cpp" />
    <ClCompile Include="Source\j_0MonsterStateDataQEAAXZ_14000BB54.cpp" />
    <ClCompile Include="Source\j_0pairViterator_TreeV_Tmap_traitsVbasic_stringDUc_14000E962.cpp" />
    <ClCompile Include="Source\j_0ptr2userVCMonsterlua_tinkerQEAAPEAVCMonsterZ_1400136F6.cpp" />
    <ClCompile Include="Source\j_0UpairCBVbasic_stringDUchar_traitsDstdVallocator_14000473C.cpp" />
    <ClCompile Include="Source\j_0UpairCBVbasic_stringDUchar_traitsDstdVallocator_14000911F.cpp" />
    <ClCompile Include="Source\j_0vectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveM_14000D1BB.cpp" />
    <ClCompile Include="Source\j_0vectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_140007DF1.cpp" />
    <ClCompile Include="Source\j_0vectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_140011180.cpp" />
    <ClCompile Include="Source\j_0vectorVCMoveMapLimitRightInfoVallocatorVCMoveMa_1400040CA.cpp" />
    <ClCompile Include="Source\j_0_event_respawnQEAAXZ_14000211C.cpp" />
    <ClCompile Include="Source\j_0_mapCGameStatisticsQEAAXZ_140012274.cpp" />
    <ClCompile Include="Source\j_0_map_fldQEAAXZ_14000EE26.cpp" />
    <ClCompile Include="Source\j_0_monster_create_setdataQEAAXZ_14001019A.cpp" />
    <ClCompile Include="Source\j_0_monster_set_event_setQEAAXZ_14000F89E.cpp" />
    <ClCompile Include="Source\j_0_monster_sp_groupQEAAXZ_14000353A.cpp" />
    <ClCompile Include="Source\j_0_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar__140010172.cpp" />
    <ClCompile Include="Source\j_0_notice_move_limit_map_msg_zoclQEAAXZ_14000B037.cpp" />
    <ClCompile Include="Source\j_0_NPCQuestIndexTempDataQEAAXZ_140013719.cpp" />
    <ClCompile Include="Source\j_0_npc_create_setdataQEAAXZ_140003922.cpp" />
    <ClCompile Include="Source\j_0_npc_quest_list_result_zoclQEAAXZ_1400139D5.cpp" />
    <ClCompile Include="Source\j_0_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1st_14000218A.cpp" />
    <ClCompile Include="Source\j_0_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1st_1400035E9.cpp" />
    <ClCompile Include="Source\j_0_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1s_140005B14.cpp" />
    <ClCompile Include="Source\j_0_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1s_1400130ED.cpp" />
    <ClCompile Include="Source\j_0_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEA_140005E52.cpp" />
    <ClCompile Include="Source\j_0_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEA_1400109F6.cpp" />
    <ClCompile Include="Source\j_0_reged_char_result_zoneQEAAXZ_14000DF9E.cpp" />
    <ClCompile Include="Source\j_0_respawn_monster_act_dh_mission_mgrQEAAXZ_14000722F.cpp" />
    <ClCompile Include="Source\j_0_state_event_respawnQEAAXZ_1400087D3.cpp" />
    <ClCompile Include="Source\j_0_state_monster_set_event_setQEAAXZ_140013AF2.cpp" />
    <ClCompile Include="Source\j_0_target_monster_aggro_inform_zoclQEAAXZ_140013FAC.cpp" />
    <ClCompile Include="Source\j_0_target_monster_contsf_allinform_zoclQEAAXZ_14000FFBA.cpp" />
    <ClCompile Include="Source\j_0_Tmap_traitsVbasic_stringDUchar_traitsDstdVallo_14000A80D.cpp" />
    <ClCompile Include="Source\j_0_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDst_14000AA97.cpp" />
    <ClCompile Include="Source\j_0_Tree_nodV_Tmap_traitsVbasic_stringDUchar_trait_14000DD73.cpp" />
    <ClCompile Include="Source\j_0_Tree_ptrV_Tmap_traitsVbasic_stringDUchar_trait_140013F93.cpp" />
    <ClCompile Include="Source\j_0_Tree_valV_Tmap_traitsVbasic_stringDUchar_trait_14000FC3B.cpp" />
    <ClCompile Include="Source\j_0_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000A5E2.cpp" />
    <ClCompile Include="Source\j_0_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000FA01.cpp" />
    <ClCompile Include="Source\j_0_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000CF59.cpp" />
    <ClCompile Include="Source\j_0_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000E0F7.cpp" />
    <ClCompile Include="Source\j_0_Vector_const_iteratorVCMoveMapLimitRightInfoVa_140009BF1.cpp" />
    <ClCompile Include="Source\j_0_Vector_const_iteratorVCMoveMapLimitRightInfoVa_140013449.cpp" />
    <ClCompile Include="Source\j_0_Vector_iteratorPEAVCMoveMapLimitInfoVallocator_140006D6B.cpp" />
    <ClCompile Include="Source\j_0_Vector_iteratorPEAVCMoveMapLimitInfoVallocator_1400092A5.cpp" />
    <ClCompile Include="Source\j_0_Vector_iteratorPEAVCMoveMapLimitRightVallocato_1400035A8.cpp" />
    <ClCompile Include="Source\j_0_Vector_iteratorPEAVCMoveMapLimitRightVallocato_14000A2C7.cpp" />
    <ClCompile Include="Source\j_0_Vector_iteratorVCMoveMapLimitRightInfoVallocat_140003C33.cpp" />
    <ClCompile Include="Source\j_0_Vector_iteratorVCMoveMapLimitRightInfoVallocat_14000992B.cpp" />
    <ClCompile Include="Source\j_0_Vector_valPEAVCMoveMapLimitInfoVallocatorPEAVC_14000162C.cpp" />
    <ClCompile Include="Source\j_0_Vector_valPEAVCMoveMapLimitRightVallocatorPEAV_140001DED.cpp" />
    <ClCompile Include="Source\j_0_Vector_valVCMoveMapLimitRightInfoVallocatorVCM_14000DA26.cpp" />
    <ClCompile Include="Source\j_0__add_monsterQEAAXZ_14000DAEE.cpp" />
    <ClCompile Include="Source\j_0__change_monsterQEAAXZ_1400027BB.cpp" />
    <ClCompile Include="Source\j_0__monster_groupQEAAXZ_14000BC26.cpp" />
    <ClCompile Include="Source\j_0__respawn_monsterQEAAXZ_14000A097.cpp" />
    <ClCompile Include="Source\j_1BossSchedule_MapQEAAXZ_140001FB4.cpp" />
    <ClCompile Include="Source\j_1CCircleZoneUEAAXZ_1400012B2.cpp" />
    <ClCompile Include="Source\j_1CMapDataUEAAXZ_1400102E9.cpp" />
    <ClCompile Include="Source\j_1CMapDisplayUEAAXZ_14000C892.cpp" />
    <ClCompile Include="Source\j_1CMapExtendQEAAXZ_140012C83.cpp" />
    <ClCompile Include="Source\j_1CMapOperationUEAAXZ_14000CB21.cpp" />
    <ClCompile Include="Source\j_1CMapTabUEAAXZ_140006762.cpp" />
    <ClCompile Include="Source\j_1CMonsterAggroMgrQEAAXZ_140008D1E.cpp" />
    <ClCompile Include="Source\j_1CMonsterAIUEAAXZ_14000A254.cpp" />
    <ClCompile Include="Source\j_1CMonsterEventRespawnUEAAXZ_14000C239.cpp" />
    <ClCompile Include="Source\j_1CMonsterEventSetUEAAXZ_1400063A7.cpp" />
    <ClCompile Include="Source\j_1CMonsterHierarchyUEAAXZ_1400020F4.cpp" />
    <ClCompile Include="Source\j_1CMonsterUEAAXZ_140012B9D.cpp" />
    <ClCompile Include="Source\j_1CMoveMapLimitInfoListQEAAXZ_140001D9D.cpp" />
    <ClCompile Include="Source\j_1CMoveMapLimitInfoPortalQEAAXZ_14000D88C.cpp" />
    <ClCompile Include="Source\j_1CMoveMapLimitInfoQEAAXZ_140002CE3.cpp" />
    <ClCompile Include="Source\j_1CMoveMapLimitManagerQEAAXZ_140012DBE.cpp" />
    <ClCompile Include="Source\j_1CMoveMapLimitRightInfoListQEAAXZ_14000F36C.cpp" />
    <ClCompile Include="Source\j_1CMoveMapLimitRightInfoQEAAXZ_14000AA88.cpp" />
    <ClCompile Include="Source\j_1CMoveMapLimitRightQEAAXZ_14001325A.cpp" />
    <ClCompile Include="Source\j_1const_iterator_TreeV_Tmap_traitsVbasic_stringDU_140010064.cpp" />
    <ClCompile Include="Source\j_1CRFMonsterAIMgrQEAAXZ_14000495D.cpp" />
    <ClCompile Include="Source\j_1CWorldScheduleQEAAXZ_14000240F.cpp" />
    <ClCompile Include="Source\j_1iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_1400127F1.cpp" />
    <ClCompile Include="Source\j_1mapVbasic_stringDUchar_traitsDstdVallocatorD2st_140010451.cpp" />
    <ClCompile Include="Source\j_1pairViterator_TreeV_Tmap_traitsVbasic_stringDUc_14000B500.cpp" />
    <ClCompile Include="Source\j_1ptr2userVCMonsterlua_tinkerUEAAXZ_140008FCB.cpp" />
    <ClCompile Include="Source\j_1vectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveM_14000FE8E.cpp" />
    <ClCompile Include="Source\j_1vectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_14000C478.cpp" />
    <ClCompile Include="Source\j_1vectorVCMoveMapLimitRightInfoVallocatorVCMoveMa_14000987C.cpp" />
    <ClCompile Include="Source\j_1_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar__14000F727.cpp" />
    <ClCompile Include="Source\j_1_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1st_140012675.cpp" />
    <ClCompile Include="Source\j_1_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1s_140003B2A.cpp" />
    <ClCompile Include="Source\j_1_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEA_140011F4F.cpp" />
    <ClCompile Include="Source\j_1_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDst_14000B569.cpp" />
    <ClCompile Include="Source\j_1_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000EC05.cpp" />
    <ClCompile Include="Source\j_1_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000B2D5.cpp" />
    <ClCompile Include="Source\j_1_Vector_const_iteratorVCMoveMapLimitRightInfoVa_140011C2F.cpp" />
    <ClCompile Include="Source\j_1_Vector_iteratorPEAVCMoveMapLimitInfoVallocator_14000DAB7.cpp" />
    <ClCompile Include="Source\j_1_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140003954.cpp" />
    <ClCompile Include="Source\j_1_Vector_iteratorVCMoveMapLimitRightInfoVallocat_14000B271.cpp" />
    <ClCompile Include="Source\j_1__change_monsterQEAAXZ_140003DB9.cpp" />
    <ClCompile Include="Source\j_4CMoveMapLimitRightInfoQEAAAEBV0AEBV0Z_140006C3F.cpp" />
    <ClCompile Include="Source\j_4const_iterator_TreeV_Tmap_traitsVbasic_stringDU_14001050F.cpp" />
    <ClCompile Include="Source\j_4iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_14000AC7C.cpp" />
    <ClCompile Include="Source\j_4_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1s_14000D4A4.cpp" />
    <ClCompile Include="Source\j_4_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000159B.cpp" />
    <ClCompile Include="Source\j_4_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140007ABD.cpp" />
    <ClCompile Include="Source\j_8const_iterator_TreeV_Tmap_traitsVbasic_stringDU_1400088FA.cpp" />
    <ClCompile Include="Source\j_8_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000EB88.cpp" />
    <ClCompile Include="Source\j_8_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000E908.cpp" />
    <ClCompile Include="Source\j_8_Vector_const_iteratorVCMoveMapLimitRightInfoVa_140004FD4.cpp" />
    <ClCompile Include="Source\j_9const_iterator_TreeV_Tmap_traitsVbasic_stringDU_140006DA2.cpp" />
    <ClCompile Include="Source\j_9MonsterStateDataQEBA_NAEBV0Z_14000E9B2.cpp" />
    <ClCompile Include="Source\j_9_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000A867.cpp" />
    <ClCompile Include="Source\j_9_Vector_const_iteratorPEAVCMoveMapLimitRightVal_140004A84.cpp" />
    <ClCompile Include="Source\j_9_Vector_const_iteratorVCMoveMapLimitRightInfoVa_14000A7D6.cpp" />
    <ClCompile Include="Source\j_AddMonsterCDarkHoleChannelQEAAXXZ_14000AC04.cpp" />
    <ClCompile Include="Source\j_allocateallocatorPEAVCMoveMapLimitInfostdQEAAPEA_14000984F.cpp" />
    <ClCompile Include="Source\j_allocateallocatorPEAVCMoveMapLimitRightstdQEAAPE_1400086DE.cpp" />
    <ClCompile Include="Source\j_allocateallocatorU_Node_Tree_nodV_Tmap_traitsVba_140006F28.cpp" />
    <ClCompile Include="Source\j_allocateallocatorVCMoveMapLimitRightInfostdQEAAP_14000CF6D.cpp" />
    <ClCompile Include="Source\j_AmapVbasic_stringDUchar_traitsDstdVallocatorD2st_14000B7F3.cpp" />
    <ClCompile Include="Source\j_assignvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_14000E5F2.cpp" />
    <ClCompile Include="Source\j_assignvectorVCMoveMapLimitRightInfoVallocatorVCM_1400059B6.cpp" />
    <ClCompile Include="Source\j_AutoRecoverCMonsterQEAAXXZ_140008017.cpp" />
    <ClCompile Include="Source\j_AvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveM_1400098E0.cpp" />
    <ClCompile Include="Source\j_AvectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_140014029.cpp" />
    <ClCompile Include="Source\j_AvectorVCMoveMapLimitRightInfoVallocatorVCMoveMa_14000C0EA.cpp" />
    <ClCompile Include="Source\j_beginvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140003F85.cpp" />
    <ClCompile Include="Source\j_beginvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000CEF0.cpp" />
    <ClCompile Include="Source\j_beginvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000D706.cpp" />
    <ClCompile Include="Source\j_beginvectorVCMoveMapLimitRightInfoVallocatorVCMo_1400132D7.cpp" />
    <ClCompile Include="Source\j_begin_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000965B.cpp" />
    <ClCompile Include="Source\j_CalcScheduleCursorCWorldScheduleQEAAHHHZ_14000BF9B.cpp" />
    <ClCompile Include="Source\j_CalcStartNPCQuestCntCQuestMgrSA_NQEAKZ_1400033E1.cpp" />
    <ClCompile Include="Source\j_capacityvectorPEAVCMoveMapLimitInfoVallocatorPEA_140006839.cpp" />
    <ClCompile Include="Source\j_capacityvectorPEAVCMoveMapLimitRightVallocatorPE_14001099C.cpp" />
    <ClCompile Include="Source\j_capacityvectorVCMoveMapLimitRightInfoVallocatorV_140001F50.cpp" />
    <ClCompile Include="Source\j_ChangeApparitionCMonsterQEAAX_NKZ_1400084A4.cpp" />
    <ClCompile Include="Source\j_ChangeLayerCMapDisplayQEAA_NGZ_140007AA9.cpp" />
    <ClCompile Include="Source\j_ChangeMapCMapDisplayQEAA_NPEAVCMapDataZ_14000C2AC.cpp" />
    <ClCompile Include="Source\j_ChangeMonsterApparitionCDarkHoleChannelQEAAXHZ_140007B94.cpp" />
    <ClCompile Include="Source\j_ChangeMonsterCDarkHoleChannelQEAAXXZ_14000EDC7.cpp" />
    <ClCompile Include="Source\j_ChangeSchCursorCWorldScheduleQEAAXPEAU_WorldSche_1400100F5.cpp" />
    <ClCompile Include="Source\j_ChangeTargetPosDfAIMgrSAXPEAVCMonsterPEAMZ_1400069DD.cpp" />
    <ClCompile Include="Source\j_ChatMapRequestCNetworkEXAEAA_NHPEADZ_140008AB7.cpp" />
    <ClCompile Include="Source\j_CheckAlienationDfAIMgrSAHPEAVCMonsterZ_14001119E.cpp" />
    <ClCompile Include="Source\j_CheckAutoRecoverHPCMonsterQEAAXXZ_14000F862.cpp" />
    <ClCompile Include="Source\j_CheckCenterPosDummyCMapDataQEAA_NPEAU_dummy_posi_1400097A5.cpp" />
    <ClCompile Include="Source\j_CheckDelayDestroyCMonsterQEAA_NXZ_14000A989.cpp" />
    <ClCompile Include="Source\j_CheckEmotionBadDfAIMgrSAHPEAVCMonsterPEAVCMonste_14000D675.cpp" />
    <ClCompile Include="Source\j_CheckEmotionPresentationCMonsterQEAAXXZ_140006E9C.cpp" />
    <ClCompile Include="Source\j_CheckEventSetRespawnCMonsterEventSetQEAAXXZ_14000BB6D.cpp" />
    <ClCompile Include="Source\j_CheckGenDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_14000D562.cpp" />
    <ClCompile Include="Source\j_CheckMapPortalLinkCMapOperationAEAAXXZ_140005885.cpp" />
    <ClCompile Include="Source\j_CheckMonArea_N_ChangeStateDfAIMgrSAHPEAVCMonster_140002B5D.cpp" />
    <ClCompile Include="Source\j_CheckMonsterRotateCMonsterQEAAXXZ_140010A8C.cpp" />
    <ClCompile Include="Source\j_CheckMonsterStateDataCMonsterQEAA_NXZ_1400048FE.cpp" />
    <ClCompile Include="Source\j_CheckNPCQuestListCQuestMgrQEAAXPEADEPEAU_NPCQues_1400013FC.cpp" />
    <ClCompile Include="Source\j_CheckRespawnEventCMonsterEventRespawnQEAAXXZ_1400100A5.cpp" />
    <ClCompile Include="Source\j_CheckRespawnMonsterCDarkHoleChannelQEAAXXZ_14000902F.cpp" />
    <ClCompile Include="Source\j_CheckRespawnProcessCMonsterQEAA_NXZ_140013A25.cpp" />
    <ClCompile Include="Source\j_CheckSchCWorldScheduleQEAAXXZ_140006E65.cpp" />
    <ClCompile Include="Source\j_CheckSPFDelayTimeDfAIMgrSAHPEAVCMonsterAIHKZ_14000F7EF.cpp" />
    <ClCompile Include="Source\j_CheckSPFDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_1400044EE.cpp" />
    <ClCompile Include="Source\j_check_dummyAutominePersonalMgrQEAA_NPEAVCMapData_140012DF0.cpp" />
    <ClCompile Include="Source\j_ChildKindCountCMonsterHierarchyQEAAEXZ_140012382.cpp" />
    <ClCompile Include="Source\j_class_addVCMonsterlua_tinkerYAXPEAUlua_StatePEBD_140002D8D.cpp" />
    <ClCompile Include="Source\j_class_defVCMonsterP81EAAPEAVCLuaSignalReActorXZl_140002AC2.cpp" />
    <ClCompile Include="Source\j_CleanUpCMoveMapLimitInfoListAEAAXXZ_140012422.cpp" />
    <ClCompile Include="Source\j_CleanUpCMoveMapLimitRightInfoAEAAXXZ_14000A213.cpp" />
    <ClCompile Include="Source\j_CleanUpCMoveMapLimitRightUEAAXXZ_140009BCE.cpp" />
    <ClCompile Include="Source\j_ClearBossSchedule_MapQEAAXXZ_1400034AE.cpp" />
    <ClCompile Include="Source\j_ClearEmotionPresentationCMonsterQEAAXXZ_140013B47.cpp" />
    <ClCompile Include="Source\j_clearvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_1400102D0.cpp" />
    <ClCompile Include="Source\j_clearvectorPEAVCMoveMapLimitRightVallocatorPEAVC_140013F8E.cpp" />
    <ClCompile Include="Source\j_clear_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000D657.cpp" />
    <ClCompile Include="Source\j_Command_ChildMonDestroyCMonsterQEAAXKZ_1400098F4.cpp" />
    <ClCompile Include="Source\j_constructallocatorPEAU_Node_Tree_nodV_Tmap_trait_140007EF5.cpp" />
    <ClCompile Include="Source\j_constructallocatorPEAVCMoveMapLimitRightstdQEAAX_14000DD3C.cpp" />
    <ClCompile Include="Source\j_constructallocatorVCMoveMapLimitRightInfostdQEAA_14000FE43.cpp" />
    <ClCompile Include="Source\j_ConvertLocalCMapDataQEAA_NPEAU_dummy_positionZ_1400056B9.cpp" />
    <ClCompile Include="Source\j_ConvertToMapCMapExtendQEAAXPEAVCSizeZ_1400035AD.cpp" />
    <ClCompile Include="Source\j_CreateAICMonsterQEAAHHZ_14000B104.cpp" />
    <ClCompile Include="Source\j_CreateCCircleZoneQEAA_NPEAVCMapDataEZ_140002D42.cpp" />
    <ClCompile Include="Source\j_CreateCGravityStoneRegenerQEAA_NPEAVCMapDataZ_14000AB0A.cpp" />
    <ClCompile Include="Source\j_CreateCMerchantQEAA_NPEAU_npc_create_setdataZ_140004750.cpp" />
    <ClCompile Include="Source\j_CreateCMonsterQEAA_NPEAU_monster_create_setdataZ_1400073D3.cpp" />
    <ClCompile Include="Source\j_CreateCMoveMapLimitInfoSAPEAV1IHZ_14001258A.cpp" />
    <ClCompile Include="Source\j_CreateCMoveMapLimitRightSAPEAV1HZ_140011CB1.cpp" />
    <ClCompile Include="Source\j_CreateMonsterCDarkHoleChannelQEAAXXZ_1400068A2.cpp" />
    <ClCompile Include="Source\j_CreateObjectCMapTabSAPEAVCObjectXZ_140002A54.cpp" />
    <ClCompile Include="Source\j_CreateObjSurfaceCMapDisplayAEAAJXZ_140007590.cpp" />
    <ClCompile Include="Source\j_CreatePaletteFromBitmapCDisplayQEAAJPEAPEAUIDire_140008DD2.cpp" />
    <ClCompile Include="Source\j_CreateRepMonsterYAPEAVCMonsterPEAVCMapDataGPEAMP_140009EDF.cpp" />
    <ClCompile Include="Source\j_CreateRespawnMonsterYAPEAVCMonsterPEAVCMapDataGH_14000BFDC.cpp" />
    <ClCompile Include="Source\j_CreateSurfaceFromBitmapCDisplayQEAAJPEAPEAVCSurf_1400108B6.cpp" />
    <ClCompile Include="Source\j_DataCheckCWorldScheduleQEAA_NXZ_140010AF0.cpp" />
    <ClCompile Include="Source\j_Dconst_iterator_TreeV_Tmap_traitsVbasic_stringDU_140001361.cpp" />
    <ClCompile Include="Source\j_deallocateallocatorPEAVCMoveMapLimitInfostdQEAAX_140001D52.cpp" />
    <ClCompile Include="Source\j_deallocateallocatorPEAVCMoveMapLimitRightstdQEAA_14001327D.cpp" />
    <ClCompile Include="Source\j_deallocateallocatorU_Node_Tree_nodV_Tmap_traitsV_14001329B.cpp" />
    <ClCompile Include="Source\j_deallocateallocatorVCMoveMapLimitRightInfostdQEA_140003391.cpp" />
    <ClCompile Include="Source\j_defP6APEAVCMonsterPEAD0MMMZlua_tinkerYAXPEAUlua__14000A8E4.cpp" />
    <ClCompile Include="Source\j_DestoryCRFMonsterAIMgrSAXXZ_14000CA8B.cpp" />
    <ClCompile Include="Source\j_destroyallocatorPEAU_Node_Tree_nodV_Tmap_traitsV_140007234.cpp" />
    <ClCompile Include="Source\j_destroyallocatorPEAVCMoveMapLimitRightstdQEAAXPE_140010226.cpp" />
    <ClCompile Include="Source\j_destroyallocatorU_Node_Tree_nodV_Tmap_traitsVbas_140013AAC.cpp" />
    <ClCompile Include="Source\j_destroyallocatorVCMoveMapLimitRightInfostdQEAAXP_14000D300.cpp" />
    <ClCompile Include="Source\j_DestroyCCircleZoneQEAAXXZ_140012373.cpp" />
    <ClCompile Include="Source\j_DestroyCMonsterQEAA_NEPEAVCGameObjectZ_14000AA29.cpp" />
    <ClCompile Include="Source\j_DestroyCMoveMapLimitManagerSAXXZ_140012715.cpp" />
    <ClCompile Include="Source\j_destroyerVCMonsterlua_tinkerYAHPEAUlua_StateZ_1400137A5.cpp" />
    <ClCompile Include="Source\j_Diterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_140004F7F.cpp" />
    <ClCompile Include="Source\j_DoDataExchangeCMapTabMEAAXPEAVCDataExchangeZ_140005452.cpp" />
    <ClCompile Include="Source\j_DrawBitmapCSurfaceQEAAJPEADKKZ_14000327E.cpp" />
    <ClCompile Include="Source\j_DrawBitmapCSurfaceQEAAJPEAUHBITMAP__KKKKZ_140002F0E.cpp" />
    <ClCompile Include="Source\j_DrawCollisionLineCMapDisplayAEAAXXZ_140001889.cpp" />
    <ClCompile Include="Source\j_DrawDisplayCMapDisplayQEAAXXZ_14000380F.cpp" />
    <ClCompile Include="Source\j_DrawDummyCMapDisplayAEAAXXZ_140010721.cpp" />
    <ClCompile Include="Source\j_DrawMapCMapDisplayAEAAXXZ_14000C77F.cpp" />
    <ClCompile Include="Source\j_DrawObjectCMapDisplayAEAAXXZ_1400114C3.cpp" />
    <ClCompile Include="Source\j_DrawRectCMapExtendQEAAXXZ_140011752.cpp" />
    <ClCompile Include="Source\j_DrawSelectMonsterLookAtPosCMapDisplayAEAAJPEAVCM_1400070E0.cpp" />
    <ClCompile Include="Source\j_DrawTextACMapDisplayAEAAXXZ_140002A5E.cpp" />
    <ClCompile Include="Source\j_D_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000F5AB.cpp" />
    <ClCompile Include="Source\j_D_Vector_const_iteratorPEAVCMoveMapLimitRightVal_140001BE5.cpp" />
    <ClCompile Include="Source\j_D_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140004908.cpp" />
    <ClCompile Include="Source\j_Econst_iterator_TreeV_Tmap_traitsVbasic_stringDU_140004525.cpp" />
    <ClCompile Include="Source\j_Eiterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_14000457A.cpp" />
    <ClCompile Include="Source\j_Eiterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_140012062.cpp" />
    <ClCompile Include="Source\j_emptyvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000AC63.cpp" />
    <ClCompile Include="Source\j_EndScreenPointCMapExtendQEAAHPEAVCSizeZ_14000B212.cpp" />
    <ClCompile Include="Source\j_endvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_140002E41.cpp" />
    <ClCompile Include="Source\j_endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_140001D8E.cpp" />
    <ClCompile Include="Source\j_endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_14000B8ED.cpp" />
    <ClCompile Include="Source\j_endvectorVCMoveMapLimitRightInfoVallocatorVCMove_14000EC91.cpp" />
    <ClCompile Include="Source\j_end_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_1400054BB.cpp" />
    <ClCompile Include="Source\j_EnterMapCMapDataQEAAXPEAVCGameObjectKZ_14000EDF9.cpp" />
    <ClCompile Include="Source\j_EnterWorldRequestCNetworkEXAEAA_NHPEAU_MSG_HEADE_1400053DA.cpp" />
    <ClCompile Include="Source\j_EnterWorldResultCNetworkEXAEAA_NKPEADZ_1400105AA.cpp" />
    <ClCompile Include="Source\j_erasevectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_14000B7B7.cpp" />
    <ClCompile Include="Source\j_erasevectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000D391.cpp" />
    <ClCompile Include="Source\j_erasevectorVCMoveMapLimitRightInfoVallocatorVCMo_140008A9E.cpp" />
    <ClCompile Include="Source\j_erase_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140003968.cpp" />
    <ClCompile Include="Source\j_erase_TreeV_Tmap_traitsVbasic_stringDUchar_trait_1400118AB.cpp" />
    <ClCompile Include="Source\j_ExitMapCMapDataQEAAXPEAVCGameObjectKZ_1400021E9.cpp" />
    <ClCompile Include="Source\j_ExitWorldRequestCNetworkEXAEAA_NHPEADZ_140001005.cpp" />
    <ClCompile Include="Source\j_E_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000BBCC.cpp" />
    <ClCompile Include="Source\j_E_Vector_const_iteratorPEAVCMoveMapLimitRightVal_1400045E8.cpp" />
    <ClCompile Include="Source\j_E_Vector_iteratorPEAVCMoveMapLimitRightVallocato_1400038FA.cpp" />
    <ClCompile Include="Source\j_Fconst_iterator_TreeV_Tmap_traitsVbasic_stringDU_14001361F.cpp" />
    <ClCompile Include="Source\j_fillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVCM_140002153.cpp" />
    <ClCompile Include="Source\j_fillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAVC_14001335E.cpp" />
    <ClCompile Include="Source\j_fillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMoveM_14001310B.cpp" />
    <ClCompile Include="Source\j_FindEmptyNPCYAPEAVCMerchantPEAV1HZ_140010E60.cpp" />
    <ClCompile Include="Source\j_findV_Vector_iteratorPEAVCMoveMapLimitRightVallo_1400103B1.cpp" />
    <ClCompile Include="Source\j_Fiterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_140001569.cpp" />
    <ClCompile Include="Source\j_GeEmotionImpStdTimeCMonsterQEAAMXZ_14000388C.cpp" />
    <ClCompile Include="Source\j_GetAggroResetTimeCMonsterQEAAKXZ_1400116D5.cpp" />
    <ClCompile Include="Source\j_GetAggroShortTimeCMonsterQEAAKXZ_140003B11.cpp" />
    <ClCompile Include="Source\j_GetAngleCMonsterHelperSAMQEAM0Z_140007D24.cpp" />
    <ClCompile Include="Source\j_GetBonusInAreaAggroCMonsterQEAAMXZ_1400050FB.cpp" />
    <ClCompile Include="Source\j_GetBspInfoCMapDataQEAAPEAU_bsp_infoXZ_14000885F.cpp" />
    <ClCompile Include="Source\j_GetChildCMonsterHierarchyQEAAPEAVCMonsterHHZ_140004A02.cpp" />
    <ClCompile Include="Source\j_GetChildCountCMonsterHierarchyQEAAKHZ_1400077F2.cpp" />
    <ClCompile Include="Source\j_GetCMoveMapLimitInfoListAEAAPEAVCMoveMapLimitInf_140007626.cpp" />
    <ClCompile Include="Source\j_GetCMoveMapLimitRightInfoListQEAAPEAVCMoveMapLim_1400059F2.cpp" />
    <ClCompile Include="Source\j_GetColorCCircleZoneQEAAEXZ_140011B58.cpp" />
    <ClCompile Include="Source\j_GetCritical_Exception_RateCMonsterUEAAHXZ_14000D6B6.cpp" />
    <ClCompile Include="Source\j_GetDefFacingCMonsterUEAAMHZ_1400048CC.cpp" />
    <ClCompile Include="Source\j_GetDefGapCMonsterUEAAMHZ_14000C950.cpp" />
    <ClCompile Include="Source\j_GetDirectionCMonsterHelperSAXAEAY02M00MZ_14000CCA2.cpp" />
    <ClCompile Include="Source\j_GetDummyPostionCMapDataQEAAPEAU_dummy_positionPE_14000ABD2.cpp" />
    <ClCompile Include="Source\j_GetEmotionStateCMonsterQEAAEXZ_140006730.cpp" />
    <ClCompile Include="Source\j_GetEmptyEventSetCMonsterEventSetQEAAPEAU_event_s_140012B70.cpp" />
    <ClCompile Include="Source\j_GetEvenSetLootingCMonsterEventSetQEAAPEAU_event__1400126DE.cpp" />
    <ClCompile Include="Source\j_GetExtendSizeCMapExtendQEAAPEAVCSizeXZ_14000270C.cpp" />
    <ClCompile Include="Source\j_GetFireTolCMonsterUEAAHXZ_14000A14B.cpp" />
    <ClCompile Include="Source\j_GetHelpMeCaseCMonsterQEAAHXZ_1400024A0.cpp" />
    <ClCompile Include="Source\j_GetHPCMonsterUEAAHXZ_1400032B0.cpp" />
    <ClCompile Include="Source\j_GetInxCMoveMapLimitInfoQEAAIXZ_140013291.cpp" />
    <ClCompile Include="Source\j_GetLinkPortalCMapDataQEAAPEAU_portal_dummyPEADZ_14000E624.cpp" />
    <ClCompile Include="Source\j_GetLostMonsterTargetDistanceMonsterSetInfoDataQE_140006460.cpp" />
    <ClCompile Include="Source\j_GetMapCMapOperationQEAAHPEAVCMapDataZ_14000CFEF.cpp" />
    <ClCompile Include="Source\j_GetMapCMapOperationQEAAPEAVCMapDataHZ_14000E4D0.cpp" />
    <ClCompile Include="Source\j_GetMapCMapOperationQEAAPEAVCMapDataPEADZ_14000A042.cpp" />
    <ClCompile Include="Source\j_GetMapCodeCMapDataQEAAEXZ_14000CB2B.cpp" />
    <ClCompile Include="Source\j_GetMapCurDirectCTransportShipQEAAPEAVCMapDataXZ_140013A89.cpp" />
    <ClCompile Include="Source\j_GetMapDataCGuildRoomInfoQEAAPEAVCMapDataXZ_14000786F.cpp" />
    <ClCompile Include="Source\j_GetMapPosCGuildRoomInfoQEAA_NPEAMZ_1400035F3.cpp" />
    <ClCompile Include="Source\j_GetMaxDMGSFContCountCMonsterQEAAHXZ_140003D32.cpp" />
    <ClCompile Include="Source\j_GetMaxHPCMonsterUEAAHXZ_1400044C1.cpp" />
    <ClCompile Include="Source\j_GetMaxToleranceProbMaxMonsterSetInfoDataQEAAMHZ_1400082C9.cpp" />
    <ClCompile Include="Source\j_GetMob_AsistTypeCMonsterQEAAHXZ_140012CBF.cpp" />
    <ClCompile Include="Source\j_GetMob_SubRaceCMonsterQEAAHXZ_140011C52.cpp" />
    <ClCompile Include="Source\j_GetMonStateInfoCMonsterQEAAGXZ_14000E7E1.cpp" />
    <ClCompile Include="Source\j_GetMonsterDropRateMonsterSetInfoDataQEAAKHZ_14000E543.cpp" />
    <ClCompile Include="Source\j_GetMonsterForcePowerRateMonsterSetInfoDataQEAAMX_14000DFF8.cpp" />
    <ClCompile Include="Source\j_GetMonsterGradeCMonsterQEAAHXZ_140006811.cpp" />
    <ClCompile Include="Source\j_GetMonsterNumInCurMissionAreaCDarkHoleChannelQEA_14000EE9E.cpp" />
    <ClCompile Include="Source\j_GetMonsterSetCMonsterEventSetQEAAPEAU_monster_se_140013403.cpp" />
    <ClCompile Include="Source\j_GetMoveSpeedCMonsterQEAAMXZ_14000DF62.cpp" />
    <ClCompile Include="Source\j_GetMoveTypeCMonsterQEAAEXZ_1400076DF.cpp" />
    <ClCompile Include="Source\j_GetMyDMGSFContCountCMonsterQEAAHXZ_14000D918.cpp" />
    <ClCompile Include="Source\j_GetNewMonSerialCMonsterSAKXZ_140012E45.cpp" />
    <ClCompile Include="Source\j_GetObjNameCMonsterUEAAPEADXZ_14000DC3D.cpp" />
    <ClCompile Include="Source\j_GetObjRaceCMonsterUEAAHXZ_140005317.cpp" />
    <ClCompile Include="Source\j_GetOffensiveTypeCMonsterQEAAHXZ_14000F448.cpp" />
    <ClCompile Include="Source\j_GetParentCMonsterHierarchyQEAAPEAVCMonsterXZ_140003828.cpp" />
    <ClCompile Include="Source\j_GetPathFinderCMonsterAIQEAAPEAVCPathMgrXZ_140007AF9.cpp" />
    <ClCompile Include="Source\j_GetPortalCMapDataQEAAPEAU_portal_dummyHZ_140009188.cpp" />
    <ClCompile Include="Source\j_GetPortalCMapDataQEAAPEAU_portal_dummyPEADZ_1400110B8.cpp" />
    <ClCompile Include="Source\j_GetPortalInxCCircleZoneQEAAHXZ_140010A1E.cpp" />
    <ClCompile Include="Source\j_GetPortalInxCMapDataQEAAHPEADZ_14000B825.cpp" />
    <ClCompile Include="Source\j_GetPosStartMapCMapOperationQEAAPEAVCMapDataE_NPE_1400137A0.cpp" />
    <ClCompile Include="Source\j_GetRaceTownCMapDataQEAAEPEAMEZ_140006C2B.cpp" />
    <ClCompile Include="Source\j_GetRandPosInDummyCMapDataQEAA_NPEAU_dummy_positi_14000279D.cpp" />
    <ClCompile Include="Source\j_GetRandPosInRangeCMapDataQEAA_NPEAMH0Z_14000A1B4.cpp" />
    <ClCompile Include="Source\j_GetRandPosVirtualDumCMapDataQEAA_NPEAMH0Z_140007B30.cpp" />
    <ClCompile Include="Source\j_GetRandPosVirtualDumExcludeStdRangeCMapDataQEAA__140004CD7.cpp" />
    <ClCompile Include="Source\j_GetRectInRadiusCMapDataQEAAXPEAU_pnt_rectHHZ_140005F97.cpp" />
    <ClCompile Include="Source\j_GetResDummySectorCMapDataQEAAHHPEAMZ_140009282.cpp" />
    <ClCompile Include="Source\j_GetRuntimeClassCMapTabUEBAPEAUCRuntimeClassXZ_1400039E5.cpp" />
    <ClCompile Include="Source\j_GetSecInfoCMapDataQEAAPEAU_sec_infoXZ_14000D0B7.cpp" />
    <ClCompile Include="Source\j_GetSectorIndexCMapDataQEAAHPEAMZ_14000920F.cpp" />
    <ClCompile Include="Source\j_GetSectorListObjCMapDataQEAAPEAVCObjectListGKZ_140008C92.cpp" />
    <ClCompile Include="Source\j_GetSectorListTowerCMapDataQEAAPEAVCObjectListGKZ_14000AB46.cpp" />
    <ClCompile Include="Source\j_GetSectorNumByLayerIndexCMapDataQEAAHGZ_1400102B2.cpp" />
    <ClCompile Include="Source\j_GetSettlementMapDataCMapOperationQEAAPEAVCMapDat_140011405.cpp" />
    <ClCompile Include="Source\j_GetSignalReActorCMonsterQEAAPEAVCLuaSignalReActo_14000489F.cpp" />
    <ClCompile Include="Source\j_GetSoilTolCMonsterUEAAHXZ_140007E7D.cpp" />
    <ClCompile Include="Source\j_GetStartMapCMapOperationQEAAPEAVCMapDataEZ_140009958.cpp" />
    <ClCompile Include="Source\j_GetStateChunkMonsterStateDataQEBAGXZ_14000CD74.cpp" />
    <ClCompile Include="Source\j_GetStateTBLCRFMonsterAIMgrQEAAAVUsPointVUsStateT_140010118.cpp" />
    <ClCompile Include="Source\j_GetThisClassCMapTabSAPEAUCRuntimeClassXZ_14000A5D3.cpp" />
    <ClCompile Include="Source\j_GetTypeCMoveMapLimitInfoQEAAHXZ_1400016C2.cpp" />
    <ClCompile Include="Source\j_GetTypeCMoveMapLimitRightQEBAHXZ_1400137F0.cpp" />
    <ClCompile Include="Source\j_GetViewAngleCapCMonsterQEAA_NHAEAHZ_140010280.cpp" />
    <ClCompile Include="Source\j_GetVisualAngleCMonsterQEAAMXZ_1400101CC.cpp" />
    <ClCompile Include="Source\j_GetVisualFieldCMonsterQEAAMXZ_14000227F.cpp" />
    <ClCompile Include="Source\j_GetWaterTolCMonsterUEAAHXZ_14000AD3A.cpp" />
    <ClCompile Include="Source\j_GetWidthCMonsterUEAAMXZ_140007FB8.cpp" />
    <ClCompile Include="Source\j_GetWindTolCMonsterUEAAHXZ_14000673F.cpp" />
    <ClCompile Include="Source\j_GetYAngleByteCMonsterQEAAEXZ_140011284.cpp" />
    <ClCompile Include="Source\j_GetYAngleCMonsterQEAAMXZ_14000371A.cpp" />
    <ClCompile Include="Source\j_gm_MapChangeCMainThreadQEAAXPEAVCMapDataZ_140008F6C.cpp" />
    <ClCompile Include="Source\j_gm_UpdateMapCMainThreadQEAAXXZ_1400050D3.cpp" />
    <ClCompile Include="Source\j_GoalCCircleZoneQEAAEPEAVCMapDataPEAMZ_1400043EA.cpp" />
    <ClCompile Include="Source\j_G_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000DCAB.cpp" />
    <ClCompile Include="Source\j_G_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140003E7C.cpp" />
    <ClCompile Include="Source\j_HierarcyHelpCastCMonsterHelperSAXPEAVCMonsterZ_140007176.cpp" />
    <ClCompile Include="Source\j_H_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140012751.cpp" />
    <ClCompile Include="Source\j_InsertNpcQuestHistoryCQuestMgrQEAAEPEADENZ_1400048E5.cpp" />
    <ClCompile Include="Source\j_insertvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1400024BE.cpp" />
    <ClCompile Include="Source\j_insertvectorPEAVCMoveMapLimitRightVallocatorPEAV_140006E60.cpp" />
    <ClCompile Include="Source\j_insertvectorVCMoveMapLimitRightInfoVallocatorVCM_14000DDFF.cpp" />
    <ClCompile Include="Source\j_insert_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400086ED.cpp" />
    <ClCompile Include="Source\j_insert_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140011527.cpp" />
    <ClCompile Include="Source\j_InstanceCMoveMapLimitManagerSAPEAV1XZ_14001131F.cpp" />
    <ClCompile Include="Source\j_InstanceCRFMonsterAIMgrSAPEAV1XZ_1400121CA.cpp" />
    <ClCompile Include="Source\j_invokelua2objectPEAVCMonsterlua_tinkerSAPEAVCMon_140012A80.cpp" />
    <ClCompile Include="Source\j_invokeobject2luaPEAVCMonsterlua_tinkerSAXPEAUlua_140003A3F.cpp" />
    <ClCompile Include="Source\j_invokePEAVCLuaSignalReActormem_functorVCMonsterX_14000309E.cpp" />
    <ClCompile Include="Source\j_invokePEAVCMonsterfunctorPEADPEADMMMlua_tinkerSA_14000BB2C.cpp" />
    <ClCompile Include="Source\j_invokeptr2luaVCMonsterlua_tinkerSAXPEAUlua_State_14000558D.cpp" />
    <ClCompile Include="Source\j_invokeuser2typeP6APEAVCMonsterPEAD0MMMZlua_tinke_14001023F.cpp" />
    <ClCompile Include="Source\j_invokeuser2typeP8CMonsterEAAPEAVCLuaSignalReActo_140009A7A.cpp" />
    <ClCompile Include="Source\j_invokevoid2ptrA6APEAVCMonsterPEAD0MMMZlua_tinker_1400024AA.cpp" />
    <ClCompile Include="Source\j_invokevoid2ptrVCMonsterlua_tinkerSAPEAVCMonsterP_14000FDAD.cpp" />
    <ClCompile Include="Source\j_invokevoid2typeP6APEAVCMonsterPEAD0MMMZlua_tinke_14000DDAF.cpp" />
    <ClCompile Include="Source\j_invokevoid2typeP8CMonsterEAAPEAVCLuaSignalReActo_14000A04C.cpp" />
    <ClCompile Include="Source\j_invokevoid2typePEAVCMonsterlua_tinkerSAPEAVCMons_140009070.cpp" />
    <ClCompile Include="Source\j_invokevoid2valP8CMonsterEAAPEAVCLuaSignalReActor_140013377.cpp" />
    <ClCompile Include="Source\j_IsBossMonsterCMonsterQEAA_NXZ_140008E1D.cpp" />
    <ClCompile Include="Source\j_IsCompleteNpcQuestCQuestMgrQEAA_NPEADHZ_14000D50D.cpp" />
    <ClCompile Include="Source\j_IsEqualLimitCMoveMapLimitInfoQEAA_NHHKZ_1400030D5.cpp" />
    <ClCompile Include="Source\j_IsExistStdMapIDCMapOperationQEAA_NHZ_140008A44.cpp" />
    <ClCompile Include="Source\j_IsHaveRightCMoveMapLimitRightInfoQEAA_NHZ_1400097A0.cpp" />
    <ClCompile Include="Source\j_IsHaveRightCMoveMapLimitRightPortalUEAA_NXZ_1400106B8.cpp" />
    <ClCompile Include="Source\j_IsHaveRightCMoveMapLimitRightUEAA_NXZ_140009219.cpp" />
    <ClCompile Include="Source\j_IsINIFileChangedCMonsterEventSetQEAA_NPEBDU_FILE_140008DAF.cpp" />
    <ClCompile Include="Source\j_IsInRegionCMapOperationQEAA_NPEADPEAVCGameObject_140013548.cpp" />
    <ClCompile Include="Source\j_IsInSectorCMonsterHelperSAHQEAM00MMPEAMZ_14000535D.cpp" />
    <ClCompile Include="Source\j_IsMapInCMapDataQEAA_NPEAMZ_14000DF3F.cpp" />
    <ClCompile Include="Source\j_IsMovableCMonsterQEAA_NXZ_14000E511.cpp" />
    <ClCompile Include="Source\j_IsNearPositionCCircleZoneAEAA_NPEBMZ_14000A3FD.cpp" />
    <ClCompile Include="Source\j_IsPossibleRepeatNpcQuestCQuestMgrQEAA_NPEADHZ_1400081E3.cpp" />
    <ClCompile Include="Source\j_IsProcLinkNpcQuestCQuestMgrQEAA_NPEADHZ_14000194C.cpp" />
    <ClCompile Include="Source\j_IsProcNpcQuestCQuestMgrQEAA_NPEADZ_14001364C.cpp" />
    <ClCompile Include="Source\j_IsRoateMonsterCMonsterQEAA_NXZ_140010D2F.cpp" />
    <ClCompile Include="Source\j_IsRotateBlockMonsterSetInfoDataQEAA_NPEAU_mon_bl_140007B0D.cpp" />
    <ClCompile Include="Source\j_IsSame_target_monster_contsf_allinform_zoclSA_NA_14000A501.cpp" />
    <ClCompile Include="Source\j_LinkEventRespawnCMonsterQEAAXPEAU_event_respawnZ_14000C85B.cpp" />
    <ClCompile Include="Source\j_LinkEventSetCMonsterQEAAXPEAU_event_setZ_14000970F.cpp" />
    <ClCompile Include="Source\j_LoadAllBossSchedule_MapQEAA_NXZ_140002725.cpp" />
    <ClCompile Include="Source\j_LoadDummyCMapDataQEAA_NPEADPEAU_dummy_positionZ_14000C7B6.cpp" />
    <ClCompile Include="Source\j_LoadEventSetCMonsterEventSetQEAA_NPEADZ_140010091.cpp" />
    <ClCompile Include="Source\j_LoadEventSetLootingCMonsterEventSetQEAA_NXZ_1400088E1.cpp" />
    <ClCompile Include="Source\j_LoadINICMoveMapLimitInfoPortalAEAA_NXZ_140006514.cpp" />
    <ClCompile Include="Source\j_LoadMapsCMapOperationAEAA_NXZ_1400110EA.cpp" />
    <ClCompile Include="Source\j_LoadMonsterSetInfoDataQEAAHPEBDZ_140006AB4.cpp" />
    <ClCompile Include="Source\j_LoadRegionCMapOperationAEAA_NXZ_14000FB2D.cpp" />
    <ClCompile Include="Source\j_LoadWorldInfoINICMainThreadAEAAHXZ_14000BB7C.cpp" />
    <ClCompile Include="Source\j_LoopCMonsterUEAAXXZ_140006249.cpp" />
    <ClCompile Include="Source\j_LoopCMoveMapLimitInfoListQEAAXXZ_14000F59C.cpp" />
    <ClCompile Include="Source\j_LoopCMoveMapLimitInfoPortalUEAAXXZ_140009BD3.cpp" />
    <ClCompile Include="Source\j_LoopCMoveMapLimitInfoUEAAXXZ_1400098AE.cpp" />
    <ClCompile Include="Source\j_LoopCMoveMapLimitManagerQEAAXXZ_140005475.cpp" />
    <ClCompile Include="Source\j_LoopCWorldScheduleQEAAXXZ_140013A84.cpp" />
    <ClCompile Include="Source\j_lower_bound_TreeV_Tmap_traitsVbasic_stringDUchar_14000B23F.cpp" />
    <ClCompile Include="Source\j_lua2typePEAVCMonsterlua_tinkerYAPEAVCMonsterPEAU_14000E35E.cpp" />
    <ClCompile Include="Source\j_max_sizeallocatorPEAVCMoveMapLimitInfostdQEBA_KX_1400129C2.cpp" />
    <ClCompile Include="Source\j_max_sizeallocatorPEAVCMoveMapLimitRightstdQEBA_K_14000A894.cpp" />
    <ClCompile Include="Source\j_max_sizeallocatorVCMoveMapLimitRightInfostdQEBA__140003788.cpp" />
    <ClCompile Include="Source\j_max_sizevectorPEAVCMoveMapLimitInfoVallocatorPEA_14000A26D.cpp" />
    <ClCompile Include="Source\j_max_sizevectorPEAVCMoveMapLimitRightVallocatorPE_14000991C.cpp" />
    <ClCompile Include="Source\j_max_sizevectorVCMoveMapLimitRightInfoVallocatorV_14000FFC4.cpp" />
    <ClCompile Include="Source\j_max_size_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140009B33.cpp" />
    <ClCompile Include="Source\j_mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeon_14001007D.cpp" />
    <ClCompile Include="Source\j_mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDung_14000C9F0.cpp" />
    <ClCompile Include="Source\j_mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkH_140004ABB.cpp" />
    <ClCompile Include="Source\j_mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDun_140002BC1.cpp" />
    <ClCompile Include="Source\j_MoveLimitMapZoneRequestCMoveMapLimitManagerQEAA__140003BC0.cpp" />
    <ClCompile Include="Source\j_MoveScreenPointCMapExtendQEAAXPEAVCPointZ_14001005A.cpp" />
    <ClCompile Include="Source\j_MoveToOwnStoneMapRequestCNetworkEXAEAA_NHPEADZ_140008C8D.cpp" />
    <ClCompile Include="Source\j_nameclass_nameVCMonsterlua_tinkerSAPEBDPEBDZ_140007130.cpp" />
    <ClCompile Include="Source\j_NPCDialogRequestCNetworkEXAEAA_NHPEADZ_1400047D2.cpp" />
    <ClCompile Include="Source\j_NPCQuestListRequestCNetworkEXAEAA_NHPEADZ_14000907F.cpp" />
    <ClCompile Include="Source\j_NPCQuestRequestCNetworkEXAEAA_NHPEADZ_1400128AF.cpp" />
    <ClCompile Include="Source\j_NPCWatchingRequestCNetworkEXAEAA_NHPEADZ_140004CE1.cpp" />
    <ClCompile Include="Source\j_OffDisplayCMapDisplayQEAA_NXZ_140001221.cpp" />
    <ClCompile Include="Source\j_OnButtonMapchangeCMapTabIEAAXXZ_140010028.cpp" />
    <ClCompile Include="Source\j_OnButtonMonsterCGameServerViewQEAAXXZ_14000A367.cpp" />
    <ClCompile Include="Source\j_OnChildMonsterCreateCMonsterHierarchyQEAAXPEAU_m_140002A09.cpp" />
    <ClCompile Include="Source\j_OnChildMonsterDestroyCMonsterHierarchyQEAAXXZ_14000B9FB.cpp" />
    <ClCompile Include="Source\j_OnChildRegenLoopCMonsterHierarchyQEAAXXZ_140013D95.cpp" />
    <ClCompile Include="Source\j_OnDisplayCMapDisplayQEAA_NPEAVCMapDataGZ_140011A81.cpp" />
    <ClCompile Include="Source\j_OnLoopCMapDataQEAAXXZ_14000EC87.cpp" />
    <ClCompile Include="Source\j_OnLoopCMapOperationQEAAXXZ_14000A2E5.cpp" />
    <ClCompile Include="Source\j_OnSetActiveCMapTabUEAAHXZ_140004507.cpp" />
    <ClCompile Include="Source\j_OpenMapCMapDataQEAA_NPEADPEAU_map_fld_NZ_14000B25D.cpp" />
    <ClCompile Include="Source\j_OpenWorldFailureResultCNetworkEXAEAA_NKPEADZ_140001235.cpp" />
    <ClCompile Include="Source\j_OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_140005B78.cpp" />
    <ClCompile Include="Source\j_OutOfSecCMonsterUEAAXXZ_14000CF2C.cpp" />
    <ClCompile Include="Source\j_PassOneStepCWorldScheduleQEAAXXZ_14000C3B5.cpp" />
    <ClCompile Include="Source\j_pc_AlterWorldServiceCMainThreadQEAAX_NZ_14000D15C.cpp" />
    <ClCompile Include="Source\j_pc_EnterWorldResultCMainThreadQEAAXEPEAU_CLIDZ_14000649C.cpp" />
    <ClCompile Include="Source\j_pc_OpenWorldFailureResultCMainThreadQEAAXPEADZ_1400082E7.cpp" />
    <ClCompile Include="Source\j_pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1400132C8.cpp" />
    <ClCompile Include="Source\j_PopChildMonAllCMonsterHierarchyQEAAXXZ_14000B956.cpp" />
    <ClCompile Include="Source\j_PopChildMonCMonsterHierarchyQEAAHPEAVCMonsterZ_14000A9A2.cpp" />
    <ClCompile Include="Source\j_ProcessCMonsterAggroMgrQEAAXXZ_14000D5FD.cpp" />
    <ClCompile Include="Source\j_ProcForceMoveHQCMoveMapLimitInfoPortalAEAAEHPEAD_140005781.cpp" />
    <ClCompile Include="Source\j_ProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAEHPE_14000FE1B.cpp" />
    <ClCompile Include="Source\j_ProcUseMoveScrollCMoveMapLimitInfoPortalAEAAEHPE_14000E5D4.cpp" />
    <ClCompile Include="Source\j_PushChildMonCMonsterHierarchyQEAAHHPEAVCMonsterZ_140010E5B.cpp" />
    <ClCompile Include="Source\j_pushPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAVCM_140004705.cpp" />
    <ClCompile Include="Source\j_push_backvectorPEAVCMoveMapLimitRightVallocatorP_140009917.cpp" />
    <ClCompile Include="Source\j_push_functorPEAVCLuaSignalReActorVCMonsterlua_ti_1400031B6.cpp" />
    <ClCompile Include="Source\j_push_functorPEAVCMonsterPEADPEADMMMlua_tinkerYAX_14000DA1C.cpp" />
    <ClCompile Include="Source\j_qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDunge_1400066DB.cpp" />
    <ClCompile Include="Source\j_qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140011D5B.cpp" />
    <ClCompile Include="Source\j_readPEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlua__140010726.cpp" />
    <ClCompile Include="Source\j_RegistCMoveMapLimitRightInfoQEAA_NHZ_140004EB7.cpp" />
    <ClCompile Include="Source\j_ReleaseDisplayCMapDisplayQEAAJXZ_140002586.cpp" />
    <ClCompile Include="Source\j_RequestCMoveMapLimitInfoListQEAAEHHHKHPEADPEAVCM_14000D9C2.cpp" />
    <ClCompile Include="Source\j_RequestCMoveMapLimitInfoPortalUEAAEHHPEADPEAVCMo_14000760D.cpp" />
    <ClCompile Include="Source\j_RequestCMoveMapLimitManagerQEAAEHHHKHPEADZ_140003184.cpp" />
    <ClCompile Include="Source\j_RequestElanMapUserForceMoveHQCMoveMapLimitManage_1400095BB.cpp" />
    <ClCompile Include="Source\j_ResetAggroCMonsterAggroMgrQEAAXXZ_140004ECB.cpp" />
    <ClCompile Include="Source\j_RespawnMonsterCMapOperationAEAAXXZ_140012B5C.cpp" />
    <ClCompile Include="Source\j_SaveAllBossSchedule_MapQEAA_NXZ_14000A08D.cpp" />
    <ClCompile Include="Source\j_ScrollMapDownCMapExtendQEAAXHHZ_140006D39.cpp" />
    <ClCompile Include="Source\j_ScrollMapLeftCMapExtendQEAAXHZ_140012797.cpp" />
    <ClCompile Include="Source\j_ScrollMapRightCMapExtendQEAAXHHZ_14000F57E.cpp" />
    <ClCompile Include="Source\j_ScrollMapUpCMapExtendQEAAXHZ_1400067FD.cpp" />
    <ClCompile Include="Source\j_SearchChildMonCMonsterHierarchyQEAAHPEAVCMonster_1400104E7.cpp" />
    <ClCompile Include="Source\j_SearchEmptyMonsterYAPEAVCMonster_NZ_140011F86.cpp" />
    <ClCompile Include="Source\j_SearchNearMonsterByDistanceCMonsterHelperSAPEAVC_140011BDA.cpp" />
    <ClCompile Include="Source\j_SearchNearMonsterCMonsterHelperSAKPEAVCMonsterPE_14000D125.cpp" />
    <ClCompile Include="Source\j_SearchPathACPathMgrQEAAHPEAVCMonsterQEAMHZ_140003D5A.cpp" />
    <ClCompile Include="Source\j_SearchPatrollPathDfAIMgrSAXPEAVCMonsterAIPEAVCMo_140003FE9.cpp" />
    <ClCompile Include="Source\j_SearchPatrolMovePosCMonsterHelperSAHPEAVCMonster_140007883.cpp" />
    <ClCompile Include="Source\j_SelectObjectCMapDisplayQEAAPEAVCGameObjectPEAVCP_140006217.cpp" />
    <ClCompile Include="Source\j_SetBlock_mon_blockQEAA_NPEAU_mon_block_fldPEAVCM_14000731F.cpp" />
    <ClCompile Include="Source\j_SetCurBspMapCGameObjectQEAA_NPEAVCMapDataZ_14000F1EB.cpp" />
    <ClCompile Include="Source\j_SetDefPartCMonsterQEAAXPEAU_monster_fldZ_140004269.cpp" />
    <ClCompile Include="Source\j_SetDummyPointCDummyDrawQEAAXPEAVCMapDataPEAMHPEA_1400077DE.cpp" />
    <ClCompile Include="Source\j_SetDummyRangeCDummyDrawQEAAXPEAVCMapDataPEAM111H_140011324.cpp" />
    <ClCompile Include="Source\j_SetEmotionStateCMonsterQEAAXEZ_14000709A.cpp" />
    <ClCompile Include="Source\j_SetEventRespawnCMonsterEventRespawnQEAA_NXZ_1400071D5.cpp" />
    <ClCompile Include="Source\j_SetFlagCMoveMapLimitRightInfoQEAAXHH_NZ_14000E052.cpp" />
    <ClCompile Include="Source\j_SetFlagCMoveMapLimitRightPortalUEAAXH_NZ_140004241.cpp" />
    <ClCompile Include="Source\j_SetFlagCMoveMapLimitRightUEAAXH_NZ_14000FD71.cpp" />
    <ClCompile Include="Source\j_SetGroupMapPointRequestCNetworkEXAEAA_NHPEADZ_140003201.cpp" />
    <ClCompile Include="Source\j_SetHPCMonsterUEAA_NH_NZ_1400129DB.cpp" />
    <ClCompile Include="Source\j_SetMoveTypeCMonsterQEAAXEZ_1400119DC.cpp" />
    <ClCompile Include="Source\j_SetMyDataCMonsterAIUEAAHPEAVUsStateTBLPEAXZ_140012EE0.cpp" />
    <ClCompile Include="Source\j_SetParentCMonsterHierarchyQEAAHPEAVCMonsterZ_140009B6F.cpp" />
    <ClCompile Include="Source\j_SetRoomMapInfoCGuildRoomInfoQEAAXPEAVCMapDataGEE_14000410B.cpp" />
    <ClCompile Include="Source\j_SetStunCMonsterUEAAX_NZ_140008E7C.cpp" />
    <ClCompile Include="Source\j_set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__140006654.cpp" />
    <ClCompile Include="Source\j_ShortRankDelayCMonsterAggroMgrQEAAXKZ_14000B2FD.cpp" />
    <ClCompile Include="Source\j_ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectD_14000AB8C.cpp" />
    <ClCompile Include="Source\j_sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1400080A3.cpp" />
    <ClCompile Include="Source\j_sizevectorPEAVCMoveMapLimitRightVallocatorPEAVCM_140004D81.cpp" />
    <ClCompile Include="Source\j_sizevectorVCMoveMapLimitRightInfoVallocatorVCMov_14000D3D7.cpp" />
    <ClCompile Include="Source\j_size_add_char_result_zoneQEAAHXZ_1400047E1.cpp" />
    <ClCompile Include="Source\j_size_del_char_result_zoneQEAAHXZ_14000C180.cpp" />
    <ClCompile Include="Source\j_size_enter_world_request_wracQEAAHXZ_14000B924.cpp" />
    <ClCompile Include="Source\j_size_enter_world_result_zoneQEAAHXZ_14000B06E.cpp" />
    <ClCompile Include="Source\j_size_moveout_user_result_zoneQEAAHXZ_140006D34.cpp" />
    <ClCompile Include="Source\j_size_move_to_own_stonemap_inform_zoclQEAAHXZ_1400015A5.cpp" />
    <ClCompile Include="Source\j_size_move_to_own_stonemap_result_zoclQEAAHXZ_140007C7F.cpp" />
    <ClCompile Include="Source\j_size_open_world_request_wracQEAAHXZ_140009F61.cpp" />
    <ClCompile Include="Source\j_size_reged_char_result_zoneQEAAHXZ_140003044.cpp" />
    <ClCompile Include="Source\j_size_sel_char_result_zoneQEAAHXZ_140012468.cpp" />
    <ClCompile Include="Source\j_size_server_notify_inform_zoneQEAAHXZ_140005187.cpp" />
    <ClCompile Include="Source\j_size_start_world_request_wracQEAAHXZ_14000BF55.cpp" />
    <ClCompile Include="Source\j_size_stop_world_request_wracQEAAHXZ_140005D3F.cpp" />
    <ClCompile Include="Source\j_size_target_monster_contsf_allinform_zoclQEAAHXZ_14000B2D0.cpp" />
    <ClCompile Include="Source\j_size_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14001127A.cpp" />
    <ClCompile Include="Source\j_size_world_account_ping_wracQEAAHXZ_14000276B.cpp" />
    <ClCompile Include="Source\j_StartRespawnEventCMonsterEventRespawnQEAA_NPEAD0_140008896.cpp" />
    <ClCompile Include="Source\j_StartScreenPointCMapExtendQEAAXPEAVCPointPEAVCMa_1400055EC.cpp" />
    <ClCompile Include="Source\j_StopEventSetCMonsterEventSetQEAA_NPEAD0Z_140012E54.cpp" />
    <ClCompile Include="Source\j_StopRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_140009377.cpp" />
    <ClCompile Include="Source\j_SubProcForceMoveHQCMoveMapLimitInfoPortalAEAAXXZ_140006555.cpp" />
    <ClCompile Include="Source\j_SubProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAE_140009994.cpp" />
    <ClCompile Include="Source\j_SubProcNotifyForceMoveHQCMoveMapLimitInfoPortalA_140009683.cpp" />
    <ClCompile Include="Source\j_TakeCGravityStoneRegenerQEAAEPEAVCMapDataPEAMZ_140007BDA.cpp" />
    <ClCompile Include="Source\j_TransPortCMonsterHelperSAXPEAVCMonsterQEAMZ_14000E589.cpp" />
    <ClCompile Include="Source\j_type2luaPEAVCMonsterlua_tinkerYAXPEAUlua_StatePE_140004C2D.cpp" />
    <ClCompile Include="Source\j_unchecked_copyPEAPEAVCMoveMapLimitInfoPEAPEAV1st_14000D84B.cpp" />
    <ClCompile Include="Source\j_unchecked_copyPEAPEAVCMoveMapLimitRightPEAPEAV1s_14000F281.cpp" />
    <ClCompile Include="Source\j_unchecked_copyPEAVCMoveMapLimitRightInfoPEAV1std_140001276.cpp" />
    <ClCompile Include="Source\j_unchecked_fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1s_140007C61.cpp" />
    <ClCompile Include="Source\j_unchecked_fill_nPEAPEAVCMoveMapLimitRight_KPEAV1_140004D5E.cpp" />
    <ClCompile Include="Source\j_unregist_from_mapAutominePersonalQEAA_NEZ_14000267B.cpp" />
    <ClCompile Include="Source\j_UpdateLookAtPosCMonsterQEAAXQEAMZ_140008F94.cpp" />
    <ClCompile Include="Source\j_UpdateLookAtPosCMonsterQEAAXXZ_140010514.cpp" />
    <ClCompile Include="Source\j_UpdateSecterListCMapDataQEAA_NPEAVCGameObjectKKZ_1400051EB.cpp" />
    <ClCompile Include="Source\j_UpdateSFContCMonsterUEAAXXZ_1400114AA.cpp" />
    <ClCompile Include="Source\j_UpdateTabCMapTabQEAAXXZ_14000A051.cpp" />
    <ClCompile Include="Source\j_upvalue_P6APEAVCMonsterPEAD0MMMZlua_tinkerYAP6AP_14000A439.cpp" />
    <ClCompile Include="Source\j_upvalue_P8CMonsterEAAPEAVCLuaSignalReActorXZlua__14000F0FB.cpp" />
    <ClCompile Include="Source\j_wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_14000B4BA.cpp" />
    <ClCompile Include="Source\j_wa_ExitWorldYAXPEAU_CLIDZ_14000FFAB.cpp" />
    <ClCompile Include="Source\j_WorldExitInformCNetworkEXAEAA_NKPEADZ_14000CDD8.cpp" />
    <ClCompile Include="Source\j_WorldMsgInformCNetworkEXAEAA_NKPEADZ_1400138DB.cpp" />
    <ClCompile Include="Source\j_WorldServiceInformCNetworkEXAEAA_NKPEADZ_14000FC36.cpp" />
    <ClCompile Include="Source\j_Y_Vector_const_iteratorPEAVCMoveMapLimitRightVal_1400092C3.cpp" />
    <ClCompile Include="Source\j_Y_Vector_iteratorPEAVCMoveMapLimitRightVallocato_14000622B.cpp" />
    <ClCompile Include="Source\j_ZoneAliveCheckRequestCNetworkEXAEAA_NHPEADZ_14000D19D.cpp" />
    <ClCompile Include="Source\j__AllocatePEAVCMoveMapLimitInfostdYAPEAPEAVCMoveM_14000F452.cpp" />
    <ClCompile Include="Source\j__AllocatePEAVCMoveMapLimitRightstdYAPEAPEAVCMove_1400131F6.cpp" />
    <ClCompile Include="Source\j__AllocateU_Node_Tree_nodV_Tmap_traitsVbasic_stri_14000FF60.cpp" />
    <ClCompile Include="Source\j__AllocateVCMoveMapLimitRightInfostdYAPEAVCMoveMa_14000F9BB.cpp" />
    <ClCompile Include="Source\j__Assign_nvectorPEAVCMoveMapLimitInfoVallocatorPE_1400091F1.cpp" />
    <ClCompile Include="Source\j__Assign_nvectorVCMoveMapLimitRightInfoVallocator_14000D90E.cpp" />
    <ClCompile Include="Source\j__BossBirthWriteLogCMonsterQEAAXXZ_14000B582.cpp" />
    <ClCompile Include="Source\j__BossDieWriteLog_EndCMonsterQEAAXXZ_140012710.cpp" />
    <ClCompile Include="Source\j__BossDieWriteLog_StartCMonsterQEAAXEPEAVCGameObj_140004D40.cpp" />
    <ClCompile Include="Source\j__Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140001E65.cpp" />
    <ClCompile Include="Source\j__Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140004FA7.cpp" />
    <ClCompile Include="Source\j__BuyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_140012251.cpp" />
    <ClCompile Include="Source\j__BuyvectorPEAVCMoveMapLimitRightVallocatorPEAVCM_14000AD8F.cpp" />
    <ClCompile Include="Source\j__BuyvectorVCMoveMapLimitRightInfoVallocatorVCMov_14000A38A.cpp" />
    <ClCompile Include="Source\j__CheckDestMonsterLimitLvYA_NHHEZ_140011400.cpp" />
    <ClCompile Include="Source\j__Color_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140002B85.cpp" />
    <ClCompile Include="Source\j__ConstructPEAU_Node_Tree_nodV_Tmap_traitsVbasic__14000A7B3.cpp" />
    <ClCompile Include="Source\j__ConstructPEAVCMoveMapLimitRightPEAV1stdYAXPEAPE_14000378D.cpp" />
    <ClCompile Include="Source\j__ConstructVCMoveMapLimitRightInfoV1stdYAXPEAVCMo_1400022D9.cpp" />
    <ClCompile Include="Source\j__Copy_backward_optPEAPEAVCMoveMapLimitInfoPEAPEA_140004BD8.cpp" />
    <ClCompile Include="Source\j__Copy_backward_optPEAPEAVCMoveMapLimitRightPEAPE_140010A5A.cpp" />
    <ClCompile Include="Source\j__Copy_backward_optPEAVCMoveMapLimitRightInfoPEAV_140010E3D.cpp" />
    <ClCompile Include="Source\j__Copy_optPEAPEAVCMoveMapLimitInfoPEAPEAV1Urandom_140003F5D.cpp" />
    <ClCompile Include="Source\j__Copy_optPEAPEAVCMoveMapLimitRightPEAPEAV1Urando_14000B1FE.cpp" />
    <ClCompile Include="Source\j__Copy_optPEAVCMoveMapLimitRightInfoPEAV1Urandom__14000F128.cpp" />
    <ClCompile Include="Source\j__CreateMonYAPEAVCMonsterPEAD0MMMZ_1400074BE.cpp" />
    <ClCompile Include="Source\j__Decconst_iterator_TreeV_Tmap_traitsVbasic_strin_140012B48.cpp" />
    <ClCompile Include="Source\j__DestroyPEAU_Node_Tree_nodV_Tmap_traitsVbasic_st_140011798.cpp" />
    <ClCompile Include="Source\j__DestroyPEAVCMoveMapLimitRightstdYAXPEAPEAVCMove_140006AAF.cpp" />
    <ClCompile Include="Source\j__DestroySDMCMonsterSAXXZ_14000CCA7.cpp" />
    <ClCompile Include="Source\j__DestroyU_Node_Tree_nodV_Tmap_traitsVbasic_strin_14000BD57.cpp" />
    <ClCompile Include="Source\j__DestroyVCMoveMapLimitRightInfostdYAXPEAVCMoveMa_140001451.cpp" />
    <ClCompile Include="Source\j__DestroyvectorPEAVCMoveMapLimitInfoVallocatorPEA_1400083FA.cpp" />
    <ClCompile Include="Source\j__DestroyvectorPEAVCMoveMapLimitRightVallocatorPE_14000D94A.cpp" />
    <ClCompile Include="Source\j__DestroyvectorVCMoveMapLimitRightInfoVallocatorV_140012C65.cpp" />
    <ClCompile Include="Source\j__Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEA_140004F20.cpp" />
    <ClCompile Include="Source\j__Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEA_14000F69B.cpp" />
    <ClCompile Include="Source\j__Destroy_rangePEAVCMoveMapLimitRightVallocatorPE_140008323.cpp" />
    <ClCompile Include="Source\j__Destroy_rangePEAVCMoveMapLimitRightVallocatorPE_1400090E3.cpp" />
    <ClCompile Include="Source\j__Destroy_rangeVCMoveMapLimitRightInfoVallocatorV_140005E98.cpp" />
    <ClCompile Include="Source\j__Destroy_rangeVCMoveMapLimitRightInfoVallocatorV_14000C0DB.cpp" />
    <ClCompile Include="Source\j__DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCS_140001799.cpp" />
    <ClCompile Include="Source\j__ECCircleZoneUEAAPEAXIZ_1400060EB.cpp" />
    <ClCompile Include="Source\j__ECMapDataUEAAPEAXIZ_140011BFD.cpp" />
    <ClCompile Include="Source\j__ECMapDisplayUEAAPEAXIZ_0_140007923.cpp" />
    <ClCompile Include="Source\j__ECMapDisplayUEAAPEAXIZ_140003332.cpp" />
    <ClCompile Include="Source\j__ECMapTabUEAAPEAXIZ_0_1400076B7.cpp" />
    <ClCompile Include="Source\j__ECMapTabUEAAPEAXIZ_140001B8B.cpp" />
    <ClCompile Include="Source\j__ECMonsterEventSetUEAAPEAXIZ_0_14000DA53.cpp" />
    <ClCompile Include="Source\j__ECMonsterEventSetUEAAPEAXIZ_14000D7C9.cpp" />
    <ClCompile Include="Source\j__ECMonsterUEAAPEAXIZ_14000546B.cpp" />
    <ClCompile Include="Source\j__Erase_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140005D94.cpp" />
    <ClCompile Include="Source\j__FillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVC_14000FA6F.cpp" />
    <ClCompile Include="Source\j__FillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAV_1400126CF.cpp" />
    <ClCompile Include="Source\j__FillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMove_14000FB8C.cpp" />
    <ClCompile Include="Source\j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1stdYAXPEAP_140001EF6.cpp" />
    <ClCompile Include="Source\j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1Urandom_ac_1400054C5.cpp" />
    <ClCompile Include="Source\j__Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1stdYAXPEA_140013C91.cpp" />
    <ClCompile Include="Source\j__Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1Urandom_a_140012F85.cpp" />
    <ClCompile Include="Source\j__FindV_Vector_iteratorPEAVCMoveMapLimitRightVall_14000AE43.cpp" />
    <ClCompile Include="Source\j__GBossSchedule_MapQEAAPEAXIZ_140002BFD.cpp" />
    <ClCompile Include="Source\j__GCCircleZoneUEAAPEAXIZ_140009CEB.cpp" />
    <ClCompile Include="Source\j__GCMapDataUEAAPEAXIZ_1400069B5.cpp" />
    <ClCompile Include="Source\j__GCMapOperationUEAAPEAXIZ_0_14001042E.cpp" />
    <ClCompile Include="Source\j__GCMapOperationUEAAPEAXIZ_14000A984.cpp" />
    <ClCompile Include="Source\j__GCMonsterAIUEAAPEAXIZ_0_140013697.cpp" />
    <ClCompile Include="Source\j__GCMonsterAIUEAAPEAXIZ_14000E4CB.cpp" />
    <ClCompile Include="Source\j__GCMonsterEventRespawnUEAAPEAXIZ_0_14000C635.cpp" />
    <ClCompile Include="Source\j__GCMonsterEventRespawnUEAAPEAXIZ_14000216C.cpp" />
    <ClCompile Include="Source\j__GCMonsterHierarchyUEAAPEAXIZ_0_14000E633.cpp" />
    <ClCompile Include="Source\j__GCMonsterHierarchyUEAAPEAXIZ_14000B5E1.cpp" />
    <ClCompile Include="Source\j__GCMonsterUEAAPEAXIZ_1400109BF.cpp" />
    <ClCompile Include="Source\j__GCMoveMapLimitInfoQEAAPEAXIZ_14000E200.cpp" />
    <ClCompile Include="Source\j__GCMoveMapLimitManagerQEAAPEAXIZ_1400042B9.cpp" />
    <ClCompile Include="Source\j__GCMoveMapLimitRightInfoQEAAPEAXIZ_140012FC6.cpp" />
    <ClCompile Include="Source\j__GCMoveMapLimitRightQEAAPEAXIZ_1400129E5.cpp" />
    <ClCompile Include="Source\j__GCRFMonsterAIMgrQEAAPEAXIZ_14000934A.cpp" />
    <ClCompile Include="Source\j__GetBaseClassCMapTabKAPEAUCRuntimeClassXZ_140006366.cpp" />
    <ClCompile Include="Source\j__GetBlinkNodeCMonsterAggroMgrIEAAPEAUCAggroNodeX_14000BFC3.cpp" />
    <ClCompile Include="Source\j__GetMonsterContTimeYAGEEZ_14000AF9C.cpp" />
    <ClCompile Include="Source\j__Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_0_14000E74B.cpp" />
    <ClCompile Include="Source\j__Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_140005C36.cpp" />
    <ClCompile Include="Source\j__G_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_1400011AE.cpp" />
    <ClCompile Include="Source\j__G__change_monsterQEAAPEAXIZ_140002572.cpp" />
    <ClCompile Include="Source\j__Incconst_iterator_TreeV_Tmap_traitsVbasic_strin_14000FA0B.cpp" />
    <ClCompile Include="Source\j__Insert_nvectorPEAVCMoveMapLimitInfoVallocatorPE_140012D5A.cpp" />
    <ClCompile Include="Source\j__Insert_nvectorPEAVCMoveMapLimitRightVallocatorP_140001749.cpp" />
    <ClCompile Include="Source\j__Insert_nvectorVCMoveMapLimitRightInfoVallocator_14000B460.cpp" />
    <ClCompile Include="Source\j__Insert_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14000ECAA.cpp" />
    <ClCompile Include="Source\j__Isnil_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400042E6.cpp" />
    <ClCompile Include="Source\j__Iter_catPEAPEAVCMoveMapLimitInfostdYAAUrandom_a_14001348A.cpp" />
    <ClCompile Include="Source\j__Iter_catPEAPEAVCMoveMapLimitRightstdYAAUrandom__14000A146.cpp" />
    <ClCompile Include="Source\j__Iter_randomPEAPEAVCMoveMapLimitInfoPEAPEAV1stdY_14000CC9D.cpp" />
    <ClCompile Include="Source\j__Iter_randomPEAPEAVCMoveMapLimitRightPEAPEAV1std_14000E4B2.cpp" />
    <ClCompile Include="Source\j__Iter_randomPEAVCMoveMapLimitRightInfoPEAV1stdYA_140008E72.cpp" />
    <ClCompile Include="Source\j__Key_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140011793.cpp" />
    <ClCompile Include="Source\j__Kfn_Tmap_traitsVbasic_stringDUchar_traitsDstdVa_14000D7D8.cpp" />
    <ClCompile Include="Source\j__Lbound_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1400019DD.cpp" />
    <ClCompile Include="Source\j__Left_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000543E.cpp" />
    <ClCompile Include="Source\j__Lmost_TreeV_Tmap_traitsVbasic_stringDUchar_trai_14000DEC7.cpp" />
    <ClCompile Include="Source\j__LoadMonBlkCMapDataAEAA_NPEADPEAU_map_fldZ_14000C162.cpp" />
    <ClCompile Include="Source\j__LoadPortalCMapDataAEAA_NPEADZ_14000ADCB.cpp" />
    <ClCompile Include="Source\j__LoadQuestCMapDataAEAA_NPEADZ_140009AB6.cpp" />
    <ClCompile Include="Source\j__LoadResourceCMapDataAEAA_NPEADZ_140001500.cpp" />
    <ClCompile Include="Source\j__LoadSafeCMapDataAEAA_NPEADZ_140001762.cpp" />
    <ClCompile Include="Source\j__LoadStartCMapDataAEAA_NPEADZ_1400059C0.cpp" />
    <ClCompile Include="Source\j__LoadStoreDummyCMapDataAEAA_NPEADZ_140003ACB.cpp" />
    <ClCompile Include="Source\j__Lrotate_TreeV_Tmap_traitsVbasic_stringDUchar_tr_14000BB27.cpp" />
    <ClCompile Include="Source\j__Max_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140009DC2.cpp" />
    <ClCompile Include="Source\j__Min_TreeV_Tmap_traitsVbasic_stringDUchar_traits_1400133A9.cpp" />
    <ClCompile Include="Source\j__Move_backward_optPEAPEAVCMoveMapLimitInfoPEAPEA_140002347.cpp" />
    <ClCompile Include="Source\j__Move_backward_optPEAPEAVCMoveMapLimitRightPEAPE_14000B2B2.cpp" />
    <ClCompile Include="Source\j__Move_backward_optPEAVCMoveMapLimitRightInfoPEAV_140004570.cpp" />
    <ClCompile Include="Source\j__Move_catPEAPEAVCMoveMapLimitInfostdYAAU_Undefin_14000BC71.cpp" />
    <ClCompile Include="Source\j__Move_catPEAPEAVCMoveMapLimitRightstdYAAU_Undefi_140008D19.cpp" />
    <ClCompile Include="Source\j__Move_catPEAVCMoveMapLimitRightInfostdYAAU_Undef_14000DADA.cpp" />
    <ClCompile Include="Source\j__Mynodeconst_iterator_TreeV_Tmap_traitsVbasic_st_140004E62.cpp" />
    <ClCompile Include="Source\j__Myval_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400099C6.cpp" />
    <ClCompile Include="Source\j__Parent_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14000E4DA.cpp" />
    <ClCompile Include="Source\j__Ptr_catPEAPEAVCMoveMapLimitInfoPEAPEAV1stdYAAU__140011D1A.cpp" />
    <ClCompile Include="Source\j__Ptr_catPEAPEAVCMoveMapLimitRightPEAPEAV1stdYAAU_14000409D.cpp" />
    <ClCompile Include="Source\j__Ptr_catPEAVCMoveMapLimitRightInfoPEAV1stdYAAU_N_140005272.cpp" />
    <ClCompile Include="Source\j__Ptr_catV_Vector_const_iteratorPEAVCMoveMapLimit_140010640.cpp" />
    <ClCompile Include="Source\j__Right_TreeV_Tmap_traitsVbasic_stringDUchar_trai_14000B753.cpp" />
    <ClCompile Include="Source\j__Rmost_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140003E18.cpp" />
    <ClCompile Include="Source\j__Root_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000949E.cpp" />
    <ClCompile Include="Source\j__Rrotate_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140013926.cpp" />
    <ClCompile Include="Source\j__ShortRankCMonsterAggroMgrIEAAXXZ_140004381.cpp" />
    <ClCompile Include="Source\j__TidyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140010DB6.cpp" />
    <ClCompile Include="Source\j__TidyvectorPEAVCMoveMapLimitRightVallocatorPEAVC_140013971.cpp" />
    <ClCompile Include="Source\j__TidyvectorVCMoveMapLimitRightInfoVallocatorVCMo_140013F25.cpp" />
    <ClCompile Include="Source\j__Tidy_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140007BAD.cpp" />
    <ClCompile Include="Source\j__UcopyV_Vector_const_iteratorPEAVCMoveMapLimitRi_14001024E.cpp" />
    <ClCompile Include="Source\j__UfillvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1400104D8.cpp" />
    <ClCompile Include="Source\j__UfillvectorPEAVCMoveMapLimitRightVallocatorPEAV_14000CF5E.cpp" />
    <ClCompile Include="Source\j__UfillvectorVCMoveMapLimitRightInfoVallocatorVCM_140013ACA.cpp" />
    <ClCompile Include="Source\j__UmovePEAPEAVCMoveMapLimitInfovectorPEAVCMoveMap_14000C469.cpp" />
    <ClCompile Include="Source\j__UmovePEAPEAVCMoveMapLimitRightvectorPEAVCMoveMa_14000E98A.cpp" />
    <ClCompile Include="Source\j__UmovePEAVCMoveMapLimitRightInfovectorVCMoveMapL_1400126D4.cpp" />
    <ClCompile Include="Source\j__Unchecked_move_backwardPEAPEAVCMoveMapLimitInfo_140012AA8.cpp" />
    <ClCompile Include="Source\j__Unchecked_move_backwardPEAPEAVCMoveMapLimitRigh_14000807B.cpp" />
    <ClCompile Include="Source\j__Unchecked_move_backwardPEAVCMoveMapLimitRightIn_14000C856.cpp" />
    <ClCompile Include="Source\j__XlenvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140008D91.cpp" />
    <ClCompile Include="Source\j__XlenvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000B005.cpp" />
    <ClCompile Include="Source\j__XlenvectorVCMoveMapLimitRightInfoVallocatorVCMo_140008DFA.cpp" />
    <ClCompile Include="Source\LightMappingTex1YAXPEAU_BSP_MAT_GROUPZ_1404EFAF0.cpp" />
    <ClCompile Include="Source\LinkEventRespawnCMonsterQEAAXPEAU_event_respawnZ_1402A78D0.cpp" />
    <ClCompile Include="Source\LinkEventSetCMonsterQEAAXPEAU_event_setZ_1402AA080.cpp" />
    <ClCompile Include="Source\LoadAllBossSchedule_MapQEAA_NXZ_14041A070.cpp" />
    <ClCompile Include="Source\LoadDummyCMapDataQEAA_NPEADPEAU_dummy_positionZ_140184250.cpp" />
    <ClCompile Include="Source\LoadEntitiesCBspQEAAXPEAU_READ_MAP_ENTITIES_LISTZ_1404F96C0.cpp" />
    <ClCompile Include="Source\LoadEventSetCMonsterEventSetQEAA_NPEADZ_1402A79E0.cpp" />
    <ClCompile Include="Source\LoadEventSetLootingCMonsterEventSetQEAA_NXZ_1402A91E0.cpp" />
    <ClCompile Include="Source\LoadINICMoveMapLimitInfoPortalAEAA_NXZ_1403A56F0.cpp" />
    <ClCompile Include="Source\LoadLightMapYAXPEADZ_1405023A0.cpp" />
    <ClCompile Include="Source\LoadMapsCMapOperationAEAA_NXZ_140196750.cpp" />
    <ClCompile Include="Source\LoadMonsterSetInfoDataQEAAHPEBDZ_14015C7E0.cpp" />
    <ClCompile Include="Source\LoadR3TLightMapYAPEAPEAU_LIGHTMAPPEAUR3TextureW4_D_140500910.cpp" />
    <ClCompile Include="Source\LoadRegionCMapOperationAEAA_NXZ_140196C40.cpp" />
    <ClCompile Include="Source\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.cpp" />
    <ClCompile Include="Source\LoopCMonsterUEAAXXZ_140147C90.cpp" />
    <ClCompile Include="Source\LoopCMoveMapLimitInfoListQEAAXXZ_1403A54D0.cpp" />
    <ClCompile Include="Source\LoopCMoveMapLimitInfoPortalUEAAXXZ_1403A43D0.cpp" />
    <ClCompile Include="Source\LoopCMoveMapLimitInfoUEAAXXZ_1403A6F40.cpp" />
    <ClCompile Include="Source\LoopCMoveMapLimitManagerQEAAXXZ_1403A1B40.cpp" />
    <ClCompile Include="Source\LoopCWorldScheduleQEAAXXZ_1403F3A30.cpp" />
    <ClCompile Include="Source\lower_bound_TreeV_Tmap_traitsVbasic_stringDUchar_t_14018EB80.cpp" />
    <ClCompile Include="Source\lua2typePEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlu_14040AFF0.cpp" />
    <ClCompile Include="Source\MakeMipMapYAXGGPEAG0Z_1405005B0.cpp" />
    <ClCompile Include="Source\MakeMipMapYAXGGPEAGPEAEZ_140500770.cpp" />
    <ClCompile Include="Source\MapChannelEqualityComparisonFilterCryptoPPAEBAIAEB_140654950.cpp" />
    <ClCompile Include="Source\MapViewOfFile_0_140676E1C.cpp" />
    <ClCompile Include="Source\max_sizeallocatorPEAVCMoveMapLimitInfostdQEBA_KXZ_1403A31C0.cpp" />
    <ClCompile Include="Source\max_sizeallocatorPEAVCMoveMapLimitRightstdQEBA_KXZ_1403B0CC0.cpp" />
    <ClCompile Include="Source\max_sizeallocatorVCMoveMapLimitRightInfostdQEBA_KX_1403A3230.cpp" />
    <ClCompile Include="Source\max_sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1403A2A70.cpp" />
    <ClCompile Include="Source\max_sizevectorPEAVCMoveMapLimitRightVallocatorPEAV_1403AFD20.cpp" />
    <ClCompile Include="Source\max_sizevectorVCMoveMapLimitRightInfoVallocatorVCM_1403A2CE0.cpp" />
    <ClCompile Include="Source\max_size_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1401913D0.cpp" />
    <ClCompile Include="Source\mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeonQu_140275730.cpp" />
    <ClCompile Include="Source\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.cpp" />
    <ClCompile Include="Source\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.cpp" />
    <ClCompile Include="Source\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.cpp" />
    <ClCompile Include="Source\MoveLimitMapZoneRequestCMoveMapLimitManagerQEAA_NH_1403A1A60.cpp" />
    <ClCompile Include="Source\MoveScreenPointCMapExtendQEAAXPEAVCPointZ_1401A1730.cpp" />
    <ClCompile Include="Source\MoveToOwnStoneMapRequestCNetworkEXAEAA_NHPEADZ_1401CF500.cpp" />
    <ClCompile Include="Source\nameclass_nameVCMonsterlua_tinkerSAPEBDPEBDZ_1404082A0.cpp" />
    <ClCompile Include="Source\NPCDialogRequestCNetworkEXAEAA_NHPEADZ_1401CF580.cpp" />
    <ClCompile Include="Source\NPCQuestListRequestCNetworkEXAEAA_NHPEADZ_1401CEF70.cpp" />
    <ClCompile Include="Source\NPCQuestRequestCNetworkEXAEAA_NHPEADZ_1401CEE40.cpp" />
    <ClCompile Include="Source\NPCWatchingRequestCNetworkEXAEAA_NHPEADZ_1401CF690.cpp" />
    <ClCompile Include="Source\OffDisplayCMapDisplayQEAA_NXZ_14019EF70.cpp" />
    <ClCompile Include="Source\OnButtonMapchangeCMapTabIEAAXXZ_14002EC30.cpp" />
    <ClCompile Include="Source\OnButtonMonsterCGameServerViewQEAAXXZ_14002B190.cpp" />
    <ClCompile Include="Source\OnChildMonsterCreateCMonsterHierarchyQEAAXPEAU_mon_140157450.cpp" />
    <ClCompile Include="Source\OnChildMonsterDestroyCMonsterHierarchyQEAAXXZ_140157870.cpp" />
    <ClCompile Include="Source\OnChildRegenLoopCMonsterHierarchyQEAAXXZ_140157590.cpp" />
    <ClCompile Include="Source\OnDisplayCMapDisplayQEAA_NPEAVCMapDataGZ_14019EEB0.cpp" />
    <ClCompile Include="Source\OnLoopCMapDataQEAAXXZ_140181510.cpp" />
    <ClCompile Include="Source\OnLoopCMapOperationQEAAXXZ_140196F30.cpp" />
    <ClCompile Include="Source\OnSetActiveCMapTabUEAAHXZ_14002E680.cpp" />
    <ClCompile Include="Source\OpenFileMappingA_0_140676E28.cpp" />
    <ClCompile Include="Source\OpenMapCMapDataQEAA_NPEADPEAU_map_fld_NZ_140180D80.cpp" />
    <ClCompile Include="Source\OpenWorldFailureResultCNetworkEXAEAA_NKPEADZ_1401C0420.cpp" />
    <ClCompile Include="Source\OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.cpp" />
    <ClCompile Include="Source\OutOfSecCMonsterUEAAXXZ_14014B990.cpp" />
    <ClCompile Include="Source\PassOneStepCWorldScheduleQEAAXXZ_1403F3E30.cpp" />
    <ClCompile Include="Source\pc_AlterWorldServiceCMainThreadQEAAX_NZ_1401F61B0.cpp" />
    <ClCompile Include="Source\pc_EnterWorldResultCMainThreadQEAAXEPEAU_CLIDZ_1401F5B30.cpp" />
    <ClCompile Include="Source\pc_OpenWorldFailureResultCMainThreadQEAAXPEADZ_1401F56F0.cpp" />
    <ClCompile Include="Source\pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.cpp" />
    <ClCompile Include="Source\PopChildMonAllCMonsterHierarchyQEAAXXZ_140157BE0.cpp" />
    <ClCompile Include="Source\PopChildMonCMonsterHierarchyQEAAHPEAVCMonsterZ_140157AA0.cpp" />
    <ClCompile Include="Source\ProcessCMonsterAggroMgrQEAAXXZ_14015E120.cpp" />
    <ClCompile Include="Source\ProcForceMoveHQCMoveMapLimitInfoPortalAEAAEHPEADPE_1403A44B0.cpp" />
    <ClCompile Include="Source\ProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAEHPEAD_1403A47B0.cpp" />
    <ClCompile Include="Source\ProcUseMoveScrollCMoveMapLimitInfoPortalAEAAEHPEAD_1403A4520.cpp" />
    <ClCompile Include="Source\PushChildMonCMonsterHierarchyQEAAHHPEAVCMonsterZ_140157990.cpp" />
    <ClCompile Include="Source\pushPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAVCMon_14040A3F0.cpp" />
    <ClCompile Include="Source\push_backvectorPEAVCMoveMapLimitRightVallocatorPEA_1403AEB10.cpp" />
    <ClCompile Include="Source\push_functorPEAVCLuaSignalReActorVCMonsterlua_tink_1404089C0.cpp" />
    <ClCompile Include="Source\push_functorPEAVCMonsterPEADPEADMMMlua_tinkerYAXPE_140408A80.cpp" />
    <ClCompile Include="Source\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.cpp" />
    <ClCompile Include="Source\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.cpp" />
    <ClCompile Include="Source\readPEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlua_St_14040A210.cpp" />
    <ClCompile Include="Source\RegistCMoveMapLimitRightInfoQEAA_NHZ_1403AC960.cpp" />
    <ClCompile Include="Source\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.cpp" />
    <ClCompile Include="Source\ReleaseLightMapYAXXZ_140502480.cpp" />
    <ClCompile Include="Source\RequestCMoveMapLimitInfoListQEAAEHHHKHPEADPEAVCMov_1403A5F80.cpp" />
    <ClCompile Include="Source\RequestCMoveMapLimitInfoPortalUEAAEHHPEADPEAVCMove_1403A4200.cpp" />
    <ClCompile Include="Source\RequestCMoveMapLimitManagerQEAAEHHHKHPEADZ_1403A19D0.cpp" />
    <ClCompile Include="Source\RequestElanMapUserForceMoveHQCMoveMapLimitManagerQ_140284700.cpp" />
    <ClCompile Include="Source\ResetAggroCMonsterAggroMgrQEAAXXZ_14015E900.cpp" />
    <ClCompile Include="Source\RespawnMonsterCMapOperationAEAAXXZ_140197190.cpp" />
    <ClCompile Include="Source\SaveAllBossSchedule_MapQEAA_NXZ_140419FB0.cpp" />
    <ClCompile Include="Source\ScrollMapDownCMapExtendQEAAXHHZ_1401A2000.cpp" />
    <ClCompile Include="Source\ScrollMapLeftCMapExtendQEAAXHZ_1401A2120.cpp" />
    <ClCompile Include="Source\ScrollMapRightCMapExtendQEAAXHHZ_1401A2220.cpp" />
    <ClCompile Include="Source\ScrollMapUpCMapExtendQEAAXHZ_1401A1EF0.cpp" />
    <ClCompile Include="Source\SearchChildMonCMonsterHierarchyQEAAHPEAVCMonsterZ_140157D00.cpp" />
    <ClCompile Include="Source\SearchEmptyMonsterYAPEAVCMonster_NZ_140148F20.cpp" />
    <ClCompile Include="Source\SearchNearMonsterByDistanceCMonsterHelperSAPEAVCMo_140159540.cpp" />
    <ClCompile Include="Source\SearchNearMonsterCMonsterHelperSAKPEAVCMonsterPEAU_140158DE0.cpp" />
    <ClCompile Include="Source\SearchPathACPathMgrQEAAHPEAVCMonsterQEAMHZ_140155C90.cpp" />
    <ClCompile Include="Source\SearchPatrollPathDfAIMgrSAXPEAVCMonsterAIPEAVCMons_1401532F0.cpp" />
    <ClCompile Include="Source\SearchPatrolMovePosCMonsterHelperSAHPEAVCMonsterAE_140159F20.cpp" />
    <ClCompile Include="Source\SelectObjectCMapDisplayQEAAPEAVCGameObjectPEAVCPoi_14019F340.cpp" />
    <ClCompile Include="Source\SetBlock_mon_blockQEAA_NPEAU_mon_block_fldPEAVCMap_140189E60.cpp" />
    <ClCompile Include="Source\SetCurBspMapCGameObjectQEAA_NPEAVCMapDataZ_14017AF30.cpp" />
    <ClCompile Include="Source\SetDefPartCMonsterQEAAXPEAU_monster_fldZ_140142B40.cpp" />
    <ClCompile Include="Source\SetDummyPointCDummyDrawQEAAXPEAVCMapDataPEAMHPEAVC_14019C560.cpp" />
    <ClCompile Include="Source\SetDummyRangeCDummyDrawQEAAXPEAVCMapDataPEAM111HPE_14019C7B0.cpp" />
    <ClCompile Include="Source\SetEmotionStateCMonsterQEAAXEZ_1401437D0.cpp" />
    <ClCompile Include="Source\SetEventRespawnCMonsterEventRespawnQEAA_NXZ_1402A5DE0.cpp" />
    <ClCompile Include="Source\SetFlagCMoveMapLimitRightInfoQEAAXHH_NZ_1403ACC50.cpp" />
    <ClCompile Include="Source\SetFlagCMoveMapLimitRightPortalUEAAXH_NZ_1403AC790.cpp" />
    <ClCompile Include="Source\SetFlagCMoveMapLimitRightUEAAXH_NZ_1403AE500.cpp" />
    <ClCompile Include="Source\SetGroupMapPointRequestCNetworkEXAEAA_NHPEADZ_1401D8360.cpp" />
    <ClCompile Include="Source\SetHPCMonsterUEAA_NH_NZ_140146200.cpp" />
    <ClCompile Include="Source\SetLightMapYAXJZ_1404EDF30.cpp" />
    <ClCompile Include="Source\SetMapMode_0_140676F48.cpp" />
    <ClCompile Include="Source\SetMoveTypeCMonsterQEAAXEZ_140143770.cpp" />
    <ClCompile Include="Source\SetMyDataCMonsterAIUEAAHPEAVUsStateTBLPEAXZ_14014FB70.cpp" />
    <ClCompile Include="Source\SetParentCMonsterHierarchyQEAAHPEAVCMonsterZ_140157960.cpp" />
    <ClCompile Include="Source\SetRoomMapInfoCGuildRoomInfoQEAAXPEAVCMapDataGEEZ_1402E5A80.cpp" />
    <ClCompile Include="Source\SetStunCMonsterUEAAX_NZ_140146130.cpp" />
    <ClCompile Include="Source\SetWorldViewMatrixVSYAXQEAY03MZ_140515870.cpp" />
    <ClCompile Include="Source\set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__r_14026F0F0.cpp" />
    <ClCompile Include="Source\ShortRankDelayCMonsterAggroMgrQEAAXKZ_14015E0C0.cpp" />
    <ClCompile Include="Source\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.cpp" />
    <ClCompile Include="Source\sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMove_1403A78D0.cpp" />
    <ClCompile Include="Source\sizevectorPEAVCMoveMapLimitRightVallocatorPEAVCMov_1403AE9F0.cpp" />
    <ClCompile Include="Source\sizevectorVCMoveMapLimitRightInfoVallocatorVCMoveM_1403A21E0.cpp" />
    <ClCompile Include="Source\size_add_char_result_zoneQEAAHXZ_14011F870.cpp" />
    <ClCompile Include="Source\size_del_char_result_zoneQEAAHXZ_14011F880.cpp" />
    <ClCompile Include="Source\size_enter_world_request_wracQEAAHXZ_14011F240.cpp" />
    <ClCompile Include="Source\size_enter_world_result_zoneQEAAHXZ_14011F250.cpp" />
    <ClCompile Include="Source\size_moveout_user_result_zoneQEAAHXZ_14011FBD0.cpp" />
    <ClCompile Include="Source\size_move_to_own_stonemap_inform_zoclQEAAHXZ_1400F03D0.cpp" />
    <ClCompile Include="Source\size_move_to_own_stonemap_result_zoclQEAAHXZ_1400F03C0.cpp" />
    <ClCompile Include="Source\size_open_world_request_wracQEAAHXZ_1402080C0.cpp" />
    <ClCompile Include="Source\size_reged_char_result_zoneQEAAHXZ_14011F6F0.cpp" />
    <ClCompile Include="Source\size_sel_char_result_zoneQEAAHXZ_14011F8D0.cpp" />
    <ClCompile Include="Source\size_server_notify_inform_zoneQEAAHXZ_14011F1E0.cpp" />
    <ClCompile Include="Source\size_start_world_request_wracQEAAHXZ_1402080D0.cpp" />
    <ClCompile Include="Source\size_stop_world_request_wracQEAAHXZ_1402080E0.cpp" />
    <ClCompile Include="Source\size_target_monster_contsf_allinform_zoclQEAAHXZ_1400F0140.cpp" />
    <ClCompile Include="Source\size_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_14018FFA0.cpp" />
    <ClCompile Include="Source\size_world_account_ping_wracQEAAHXZ_1402080F0.cpp" />
    <ClCompile Include="Source\StartRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6B90.cpp" />
    <ClCompile Include="Source\StartScreenPointCMapExtendQEAAXPEAVCPointPEAVCMapD_1401A1670.cpp" />
    <ClCompile Include="Source\StopEventSetCMonsterEventSetQEAA_NPEAD0Z_1402A8870.cpp" />
    <ClCompile Include="Source\StopRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6E50.cpp" />
    <ClCompile Include="Source\SubProcForceMoveHQCMoveMapLimitInfoPortalAEAAXXZ_1403A4A50.cpp" />
    <ClCompile Include="Source\SubProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAEHP_1403A4D10.cpp" />
    <ClCompile Include="Source\SubProcNotifyForceMoveHQCMoveMapLimitInfoPortalAEA_1403A4880.cpp" />
    <ClCompile Include="Source\TakeCGravityStoneRegenerQEAAEPEAVCMapDataPEAMZ_14012EB20.cpp" />
    <ClCompile Include="Source\TransPortCMonsterHelperSAXPEAVCMonsterQEAMZ_14015A310.cpp" />
    <ClCompile Include="Source\type2luaPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAV_14040B0E0.cpp" />
    <ClCompile Include="Source\unchecked_copyPEAPEAVCMoveMapLimitInfoPEAPEAV1stde_1403AAB40.cpp" />
    <ClCompile Include="Source\unchecked_copyPEAPEAVCMoveMapLimitRightPEAPEAV1std_1403B1C00.cpp" />
    <ClCompile Include="Source\unchecked_copyPEAVCMoveMapLimitRightInfoPEAV1stdex_1403B21A0.cpp" />
    <ClCompile Include="Source\unchecked_fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1std_1403ABFD0.cpp" />
    <ClCompile Include="Source\unchecked_fill_nPEAPEAVCMoveMapLimitRight_KPEAV1st_1403B3280.cpp" />
    <ClCompile Include="Source\UnLightMappingTex1YAXXZ_1404EFB90.cpp" />
    <ClCompile Include="Source\UnmapViewOfFile_0_140676E16.cpp" />
    <ClCompile Include="Source\unregist_from_mapAutominePersonalQEAA_NEZ_1402DB110.cpp" />
    <ClCompile Include="Source\UpdateLookAtPosCMonsterQEAAXQEAMZ_140148220.cpp" />
    <ClCompile Include="Source\UpdateLookAtPosCMonsterQEAAXXZ_140148090.cpp" />
    <ClCompile Include="Source\UpdateSecterListCMapDataQEAA_NPEAVCGameObjectKKZ_140184BE0.cpp" />
    <ClCompile Include="Source\UpdateSFContCMonsterUEAAXXZ_140147170.cpp" />
    <ClCompile Include="Source\UpdateTabCMapTabQEAAXXZ_14002E6E0.cpp" />
    <ClCompile Include="Source\upvalue_P6APEAVCMonsterPEAD0MMMZlua_tinkerYAP6APEA_14040A3A0.cpp" />
    <ClCompile Include="Source\upvalue_P8CMonsterEAAPEAVCLuaSignalReActorXZlua_ti_14040A260.cpp" />
    <ClCompile Include="Source\wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.cpp" />
    <ClCompile Include="Source\wa_ExitWorldYAXPEAU_CLIDZ_140046190.cpp" />
    <ClCompile Include="Source\WorldExitInformCNetworkEXAEAA_NKPEADZ_1401C09A0.cpp" />
    <ClCompile Include="Source\WorldMsgInformCNetworkEXAEAA_NKPEADZ_1401C09F0.cpp" />
    <ClCompile Include="Source\WorldServiceInformCNetworkEXAEAA_NKPEADZ_1401C0940.cpp" />
    <ClCompile Include="Source\Y_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403B17C0.cpp" />
    <ClCompile Include="Source\Y_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B1550.cpp" />
    <ClCompile Include="Source\ZoneAliveCheckRequestCNetworkEXAEAA_NHPEADZ_1401C3D90.cpp" />
    <ClCompile Include="Source\_AllocatePEAVCMoveMapLimitInfostdYAPEAPEAVCMoveMap_1403A3390.cpp" />
    <ClCompile Include="Source\_AllocatePEAVCMoveMapLimitRightstdYAPEAPEAVCMoveMa_1403B20F0.cpp" />
    <ClCompile Include="Source\_AllocateU_Node_Tree_nodV_Tmap_traitsVbasic_string_140192B40.cpp" />
    <ClCompile Include="Source\_AllocateVCMoveMapLimitRightInfostdYAPEAVCMoveMapL_1403A34C0.cpp" />
    <ClCompile Include="Source\_Assign_nvectorPEAVCMoveMapLimitInfoVallocatorPEAV_1403A84E0.cpp" />
    <ClCompile Include="Source\_Assign_nvectorVCMoveMapLimitRightInfoVallocatorVC_1403AF5E0.cpp" />
    <ClCompile Include="Source\_BossBirthWriteLogCMonsterQEAAXXZ_140143910.cpp" />
    <ClCompile Include="Source\_BossDieWriteLog_EndCMonsterQEAAXXZ_1401440C0.cpp" />
    <ClCompile Include="Source\_BossDieWriteLog_StartCMonsterQEAAXEPEAVCGameObjec_1401439D0.cpp" />
    <ClCompile Include="Source\_BossSchedule_Map_BossSchedule_Map__1_dtor0_14041B480.cpp" />
    <ClCompile Include="Source\_Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140191820.cpp" />
    <ClCompile Include="Source\_Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140193E70.cpp" />
    <ClCompile Include="Source\_BuyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMove_1403A2350.cpp" />
    <ClCompile Include="Source\_BuyvectorPEAVCMoveMapLimitRightVallocatorPEAVCMov_1403AF3E0.cpp" />
    <ClCompile Include="Source\_BuyvectorVCMoveMapLimitRightInfoVallocatorVCMoveM_1403A25B0.cpp" />
    <ClCompile Include="Source\_CDisplayCreateSurfaceFromBitmap__1_dtor0_140433DE0.cpp" />
    <ClCompile Include="Source\_CDisplayShowBitmap__1_dtor0_140434370.cpp" />
    <ClCompile Include="Source\_CheckDestMonsterLimitLvYA_NHHEZ_140099540.cpp" />
    <ClCompile Include="Source\_CMainThreadLoadWorldInfoINI__1_dtor0_1401E70F0.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor0_1401802F0.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor10_1401804D0.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor11_140180500.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor12_140180530.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor13_140180560.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor1_140180320.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor2_140180350.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor3_140180380.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor4_1401803B0.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor5_1401803E0.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor6_140180410.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor7_140180440.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor8_140180470.cpp" />
    <ClCompile Include="Source\_CMapDataCMapData__1_dtor9_1401804A0.cpp" />
    <ClCompile Include="Source\_CMapDataOpenMap__1_dtor0_140181480.cpp" />
    <ClCompile Include="Source\_CMapDataOpenMap__1_dtor1_1401814B0.cpp" />
    <ClCompile Include="Source\_CMapDataOpenMap__1_dtor2_1401814E0.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor0_140180A60.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor10_140180C40.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor11_140180C70.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor12_140180CA0.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor13_140180CD0.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor14_140180D00.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor1_140180A90.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor2_140180AC0.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor3_140180AF0.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor4_140180B20.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor5_140180B50.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor6_140180B80.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor7_140180BB0.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor8_140180BE0.cpp" />
    <ClCompile Include="Source\_CMapData_CMapData__1_dtor9_140180C10.cpp" />
    <ClCompile Include="Source\_CMapData_LoadMonBlk__1_dtor0_140182550.cpp" />
    <ClCompile Include="Source\_CMapData_LoadMonBlk__1_dtor1_140182580.cpp" />
    <ClCompile Include="Source\_CMapData_LoadPortal__1_dtor0_1401829E0.cpp" />
    <ClCompile Include="Source\_CMapData_LoadQuest__1_dtor0_140183E00.cpp" />
    <ClCompile Include="Source\_CMapData_LoadResource__1_dtor0_140183B30.cpp" />
    <ClCompile Include="Source\_CMapData_LoadSafe__1_dtor0_140184220.cpp" />
    <ClCompile Include="Source\_CMapData_LoadStart__1_dtor0_1401832B0.cpp" />
    <ClCompile Include="Source\_CMapData_LoadStoreDummy__1_dtor0_140182EB0.cpp" />
    <ClCompile Include="Source\_CMapDisplayCMapDisplay__1_dtor0_14019D8B0.cpp" />
    <ClCompile Include="Source\_CMapDisplayCMapDisplay__1_dtor1_14019D8E0.cpp" />
    <ClCompile Include="Source\_CMapDisplayCMapDisplay__1_dtor2_14019D920.cpp" />
    <ClCompile Include="Source\_CMapDisplayCMapDisplay__1_dtor3_14019D950.cpp" />
    <ClCompile Include="Source\_CMapDisplayCMapDisplay__1_dtor4_14019D980.cpp" />
    <ClCompile Include="Source\_CMapDisplay_CMapDisplay__1_dtor0_14019DB80.cpp" />
    <ClCompile Include="Source\_CMapDisplay_CMapDisplay__1_dtor1_14019DBB0.cpp" />
    <ClCompile Include="Source\_CMapDisplay_CMapDisplay__1_dtor2_14019DBF0.cpp" />
    <ClCompile Include="Source\_CMapDisplay_CMapDisplay__1_dtor3_14019DC20.cpp" />
    <ClCompile Include="Source\_CMapDisplay_CMapDisplay__1_dtor4_14019DC50.cpp" />
    <ClCompile Include="Source\_CMapOperationCMapOperation__1_dtor0_140195FD0.cpp" />
    <ClCompile Include="Source\_CMapOperationCMapOperation__1_dtor1_140196000.cpp" />
    <ClCompile Include="Source\_CMapOperationCMapOperation__1_dtor2_140196030.cpp" />
    <ClCompile Include="Source\_CMapOperationCMapOperation__1_dtor3_140196060.cpp" />
    <ClCompile Include="Source\_CMapOperationCMapOperation__1_dtor4_140196090.cpp" />
    <ClCompile Include="Source\_CMapOperationLoadRegion__1_dtor0_140196F00.cpp" />
    <ClCompile Include="Source\_CMapOperation_CMapOperation__1_dtor0_140196210.cpp" />
    <ClCompile Include="Source\_CMapOperation_CMapOperation__1_dtor1_140196240.cpp" />
    <ClCompile Include="Source\_CMapOperation_CMapOperation__1_dtor2_140196270.cpp" />
    <ClCompile Include="Source\_CMapOperation_CMapOperation__1_dtor3_1401962A0.cpp" />
    <ClCompile Include="Source\_CMapOperation_CMapOperation__1_dtor4_1401962D0.cpp" />
    <ClCompile Include="Source\_CMapTabCMapTab__1_dtor0_14002E500.cpp" />
    <ClCompile Include="Source\_CMapTabCreateObject__1_dtor0_14002E3D0.cpp" />
    <ClCompile Include="Source\_CMapTabUpdateTab__1_dtor0_14002EC00.cpp" />
    <ClCompile Include="Source\_CMapTab_CMapTab__1_dtor0_14002E590.cpp" />
    <ClCompile Include="Source\_CMonsterAICMonsterAI__1_dtor0_14014FA00.cpp" />
    <ClCompile Include="Source\_CMonsterAI_CMonsterAI__1_dtor0_14014FA90.cpp" />
    <ClCompile Include="Source\_CMonsterCMonster__1_dtor0_140141660.cpp" />
    <ClCompile Include="Source\_CMonsterCMonster__1_dtor1_140141690.cpp" />
    <ClCompile Include="Source\_CMonsterCMonster__1_dtor2_1401416C0.cpp" />
    <ClCompile Include="Source\_CMonsterCMonster__1_dtor3_1401416F0.cpp" />
    <ClCompile Include="Source\_CMonsterCMonster__1_dtor4_140141720.cpp" />
    <ClCompile Include="Source\_CMonsterCMonster__1_dtor5_140141750.cpp" />
    <ClCompile Include="Source\_CMonsterCreateAI__1_dtor0_1401424C0.cpp" />
    <ClCompile Include="Source\_CMonster_CMonster__1_dtor0_140141850.cpp" />
    <ClCompile Include="Source\_CMonster_CMonster__1_dtor1_140141880.cpp" />
    <ClCompile Include="Source\_CMonster_CMonster__1_dtor2_1401418B0.cpp" />
    <ClCompile Include="Source\_CMonster_CMonster__1_dtor3_1401418E0.cpp" />
    <ClCompile Include="Source\_CMonster_CMonster__1_dtor4_140141910.cpp" />
    <ClCompile Include="Source\_CMonster_CMonster__1_dtor5_140141940.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoCreate__1_dtor0_1403A3E40.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListCleanUp__1_dtor0_1403A6430.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListCleanUp__1_dtor1_1403A6460.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListCleanUp__1_dtor2_1403A6490.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListCleanUp__1_dtor3_1403A64C0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListGet__1_dtor0_1403A61D0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListGet__1_dtor1_1403A6200.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListGet__1_dtor2_1403A6230.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListGet__1_dtor3_1403A6260.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLoad__1_dtor0_1403A5A60.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLoad__1_dtor1_1403A5A90.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLoad__1_dtor2_1403A5AC0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLoad__1_dtor3_1403A5AF0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLogOut__1_dtor0_1403A5EC0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLogOut__1_dtor1_1403A5EF0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLogOut__1_dtor2_1403A5F20.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLogOut__1_dtor3_1403A5F50.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLoop__1_dtor0_1403A5630.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLoop__1_dtor1_1403A5660.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLoop__1_dtor2_1403A5690.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLoop__1_dtor3_1403A56C0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoPortalCMoveMapLimitInfoPortal__1_1403A3FA0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor0_1403A46F0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor1_1403A4720.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor2_1403A4750.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor3_1403A4780.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoPortal_CMoveMapLimitInfoPortal___1403A40C0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoPortal_CMoveMapLimitInfoPortal___1403A40F0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitManagerCMoveMapLimitManager__1_dtor0_1403A1D70.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitManagerInstance__1_dtor0_1403A1680.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitManager_CMoveMapLimitManager__1_dtor_1403A1F60.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightCreate__1_dtor0_1403AC670.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoCreateComplete__1_dtor0_1403AD2B0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoCreateComplete__1_dtor1_1403AD2E0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoCreateComplete__1_dtor2_1403AD310.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoCreateComplete__1_dtor3_1403AD340.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoLoad__1_dtor0_1403ACE70.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoLoad__1_dtor1_1403ACEA0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoLoad__1_dtor2_1403ACED0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoLoad__1_dtor3_1403ACF00.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoLogOut__1_dtor0_1403AD4D0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoLogOut__1_dtor1_1403AD500.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoLogOut__1_dtor2_1403AD530.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoLogOut__1_dtor3_1403AD560.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfooperator___1_dtor0_1403AD6B0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfooperator___1_dtor1_1403AD6E0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoRegist__1_dtor0_1403ACB10.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoRegist__1_dtor2_1403ACB40.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoRegist__1_dtor3_1403ACB70.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfo_CMoveMapLimitRightInfo__1__1403A39E0.cpp" />
    <ClCompile Include="Source\_Color_TreeV_Tmap_traitsVbasic_stringDUchar_traits_1401913B0.cpp" />
    <ClCompile Include="Source\_ConstructPEAU_Node_Tree_nodV_Tmap_traitsVbasic_st_140194330.cpp" />
    <ClCompile Include="Source\_ConstructPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAV_1403B3BA0.cpp" />
    <ClCompile Include="Source\_ConstructVCMoveMapLimitRightInfoV1stdYAXPEAVCMove_1403B37C0.cpp" />
    <ClCompile Include="Source\_Copy_backward_optPEAPEAVCMoveMapLimitInfoPEAPEAV1_1403ABF00.cpp" />
    <ClCompile Include="Source\_Copy_backward_optPEAPEAVCMoveMapLimitRightPEAPEAV_1403B35E0.cpp" />
    <ClCompile Include="Source\_Copy_backward_optPEAVCMoveMapLimitRightInfoPEAV1U_1403B3720.cpp" />
    <ClCompile Include="Source\_Copy_optPEAPEAVCMoveMapLimitInfoPEAPEAV1Urandom_a_1403AB540.cpp" />
    <ClCompile Include="Source\_Copy_optPEAPEAVCMoveMapLimitRightPEAPEAV1Urandom__1403B27E0.cpp" />
    <ClCompile Include="Source\_Copy_optPEAVCMoveMapLimitRightInfoPEAV1Urandom_ac_1403B2D90.cpp" />
    <ClCompile Include="Source\_CreateMonYAPEAVCMonsterPEAD0MMMZ_140406260.cpp" />
    <ClCompile Include="Source\_CRFMonsterAIMgrInstance__1_dtor0_14014C190.cpp" />
    <ClCompile Include="Source\_CWorldScheduleCWorldSchedule__1_dtor0_1403F3560.cpp" />
    <ClCompile Include="Source\_CWorldSchedule_CWorldSchedule__1_dtor0_1403F4690.cpp" />
    <ClCompile Include="Source\_Decconst_iterator_TreeV_Tmap_traitsVbasic_stringD_140191F90.cpp" />
    <ClCompile Include="Source\_DestroyPEAU_Node_Tree_nodV_Tmap_traitsVbasic_stri_1401942E0.cpp" />
    <ClCompile Include="Source\_DestroyPEAVCMoveMapLimitRightstdYAXPEAPEAVCMoveMa_1403B3C40.cpp" />
    <ClCompile Include="Source\_DestroySDMCMonsterSAXXZ_140149460.cpp" />
    <ClCompile Include="Source\_DestroyU_Node_Tree_nodV_Tmap_traitsVbasic_stringD_140195D10.cpp" />
    <ClCompile Include="Source\_DestroyVCMoveMapLimitRightInfostdYAXPEAVCMoveMapL_1403A38D0.cpp" />
    <ClCompile Include="Source\_DestroyvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1403A2AC0.cpp" />
    <ClCompile Include="Source\_DestroyvectorPEAVCMoveMapLimitRightVallocatorPEAV_1403A3B40.cpp" />
    <ClCompile Include="Source\_DestroyvectorVCMoveMapLimitRightInfoVallocatorVCM_1403A2D30.cpp" />
    <ClCompile Include="Source\_Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEAVC_1403A3310.cpp" />
    <ClCompile Include="Source\_Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEAVC_1403A3700.cpp" />
    <ClCompile Include="Source\_Destroy_rangePEAVCMoveMapLimitRightVallocatorPEAV_1403A3C00.cpp" />
    <ClCompile Include="Source\_Destroy_rangePEAVCMoveMapLimitRightVallocatorPEAV_1403A3CE0.cpp" />
    <ClCompile Include="Source\_Destroy_rangeVCMoveMapLimitRightInfoVallocatorVCM_1403A3440.cpp" />
    <ClCompile Include="Source\_Destroy_rangeVCMoveMapLimitRightInfoVallocatorVCM_1403A3780.cpp" />
    <ClCompile Include="Source\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CMonsters_logTrace_1406E8580.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CMonsters_logTrace_1406E85C0.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__g_MapDisplay___1406E8740.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__g_MapOper___1406E8700.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__g_MonsterEventResp_1406E8EA0.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__g_strLMapMap___1406E86C0.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__g_strMapMap___1406E8680.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__g_WorldSch___1406E9680.cpp" />
    <ClCompile Include="Source\_ECCircleZoneUEAAPEAXIZ_1403F0010.cpp" />
    <ClCompile Include="Source\_ECMapDataUEAAPEAXIZ_140199070.cpp" />
    <ClCompile Include="Source\_ECMapDisplayUEAAPEAXIZ_1401A1240.cpp" />
    <ClCompile Include="Source\_ECMapTabUEAAPEAXIZ_14002EFB0.cpp" />
    <ClCompile Include="Source\_ECMonsterEventSetUEAAPEAXIZ_1402AA010.cpp" />
    <ClCompile Include="Source\_ECMonsterUEAAPEAXIZ_140204E60.cpp" />
    <ClCompile Include="Source\_Erase_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140195B70.cpp" />
    <ClCompile Include="Source\_FillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVCMo_1403AB9A0.cpp" />
    <ClCompile Include="Source\_FillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAVCM_1403B2BD0.cpp" />
    <ClCompile Include="Source\_FillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMoveMa_1403B2EE0.cpp" />
    <ClCompile Include="Source\_Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1stdYAXPEAPEA_1403AC570.cpp" />
    <ClCompile Include="Source\_Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1Urandom_acce_1403AC310.cpp" />
    <ClCompile Include="Source\_Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1stdYAXPEAPE_1403B3C50.cpp" />
    <ClCompile Include="Source\_Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1Urandom_acc_1403B39C0.cpp" />
    <ClCompile Include="Source\_FindV_Vector_iteratorPEAVCMoveMapLimitRightValloc_1403B25D0.cpp" />
    <ClCompile Include="Source\_GBossSchedule_MapQEAAPEAXIZ_14041B3C0.cpp" />
    <ClCompile Include="Source\_GCCircleZoneUEAAPEAXIZ_14012E3C0.cpp" />
    <ClCompile Include="Source\_GCMapDataUEAAPEAXIZ_1401880E0.cpp" />
    <ClCompile Include="Source\_GCMapOperationUEAAPEAXIZ_140198890.cpp" />
    <ClCompile Include="Source\_GCMonsterAIUEAAPEAXIZ_14014FCA0.cpp" />
    <ClCompile Include="Source\_GCMonsterEventRespawnUEAAPEAXIZ_1402A7860.cpp" />
    <ClCompile Include="Source\_GCMonsterHierarchyUEAAPEAXIZ_14014B690.cpp" />
    <ClCompile Include="Source\_GCMonsterUEAAPEAXIZ_14014BB90.cpp" />
    <ClCompile Include="Source\_GCMoveMapLimitInfoQEAAPEAXIZ_1403A74C0.cpp" />
    <ClCompile Include="Source\_GCMoveMapLimitManagerQEAAPEAXIZ_1403A1EA0.cpp" />
    <ClCompile Include="Source\_GCMoveMapLimitRightInfoQEAAPEAXIZ_1403A3920.cpp" />
    <ClCompile Include="Source\_GCMoveMapLimitRightQEAAPEAXIZ_1403AE6D0.cpp" />
    <ClCompile Include="Source\_GCRFMonsterAIMgrQEAAPEAXIZ_140203390.cpp" />
    <ClCompile Include="Source\_GetBaseClassCMapTabKAPEAUCRuntimeClassXZ_14002E410.cpp" />
    <ClCompile Include="Source\_GetBlinkNodeCMonsterAggroMgrIEAAPEAUCAggroNodeXZ_14015E2E0.cpp" />
    <ClCompile Include="Source\_GetMonsterContTimeYAGEEZ_140147560.cpp" />
    <ClCompile Include="Source\_Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_14040B9D0.cpp" />
    <ClCompile Include="Source\_GrowmapdequeIVallocatorIstdstdIEAAX_KZ_140657610.cpp" />
    <ClCompile Include="Source\_Growmapdeque_KVallocator_KstdstdIEAAX_KZ_140656D90.cpp" />
    <ClCompile Include="Source\_G_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_t_140195D60.cpp" />
    <ClCompile Include="Source\_G__change_monsterQEAAPEAXIZ_140272DF0.cpp" />
    <ClCompile Include="Source\_Incconst_iterator_TreeV_Tmap_traitsVbasic_stringD_140192190.cpp" />
    <ClCompile Include="Source\_Insert_nvectorPEAVCMoveMapLimitInfoVallocatorPEAV_1403A9E40.cpp" />
    <ClCompile Include="Source\_Insert_nvectorPEAVCMoveMapLimitRightVallocatorPEA_1403AFD70.cpp" />
    <ClCompile Include="Source\_Insert_nvectorVCMoveMapLimitRightInfoVallocatorVC_1403B0D30.cpp" />
    <ClCompile Include="Source\_Insert_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140190690.cpp" />
    <ClCompile Include="Source\_Isnil_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FEC0.cpp" />
    <ClCompile Include="Source\_Iter_catPEAPEAVCMoveMapLimitInfostdYAAUrandom_acc_1403AC2B0.cpp" />
    <ClCompile Include="Source\_Iter_catPEAPEAVCMoveMapLimitRightstdYAAUrandom_ac_1403B3960.cpp" />
    <ClCompile Include="Source\_Iter_randomPEAPEAVCMoveMapLimitInfoPEAPEAV1stdYAA_1403AB4E0.cpp" />
    <ClCompile Include="Source\_Iter_randomPEAPEAVCMoveMapLimitRightPEAPEAV1stdYA_1403B2780.cpp" />
    <ClCompile Include="Source\_Iter_randomPEAVCMoveMapLimitRightInfoPEAV1stdYAAU_1403B2D30.cpp" />
    <ClCompile Include="Source\_Key_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_14018D650.cpp" />
    <ClCompile Include="Source\_Kfn_Tmap_traitsVbasic_stringDUchar_traitsDstdVall_140190EB0.cpp" />
    <ClCompile Include="Source\_Lbound_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140190D70.cpp" />
    <ClCompile Include="Source\_Left_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140190680.cpp" />
    <ClCompile Include="Source\_Lmost_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140191420.cpp" />
    <ClCompile Include="Source\_LoadMonBlkCMapDataAEAA_NPEADPEAU_map_fldZ_140181850.cpp" />
    <ClCompile Include="Source\_LoadPortalCMapDataAEAA_NPEADZ_1401825B0.cpp" />
    <ClCompile Include="Source\_LoadQuestCMapDataAEAA_NPEADZ_140183B60.cpp" />
    <ClCompile Include="Source\_LoadResourceCMapDataAEAA_NPEADZ_1401835B0.cpp" />
    <ClCompile Include="Source\_LoadSafeCMapDataAEAA_NPEADZ_140183F80.cpp" />
    <ClCompile Include="Source\_LoadStartCMapDataAEAA_NPEADZ_140182EE0.cpp" />
    <ClCompile Include="Source\_LoadStoreDummyCMapDataAEAA_NPEADZ_140182A10.cpp" />
    <ClCompile Include="Source\_Lrotate_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140191470.cpp" />
    <ClCompile Include="Source\_lua_tinkerptr2lua_CMonster_invoke__1_dtor0_14040B720.cpp" />
    <ClCompile Include="Source\_Max_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_140192110.cpp" />
    <ClCompile Include="Source\_mc_AddMonster__1_dtor0_1402759A0.cpp" />
    <ClCompile Include="Source\_mc_ChangeMonster__1_dtor0_140276380.cpp" />
    <ClCompile Include="Source\_mc_RespawnMonster__1_dtor0_140275E30.cpp" />
    <ClCompile Include="Source\_Min_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_1401922B0.cpp" />
    <ClCompile Include="Source\_Move_backward_optPEAPEAVCMoveMapLimitInfoPEAPEAV1_1403ABA50.cpp" />
    <ClCompile Include="Source\_Move_backward_optPEAPEAVCMoveMapLimitRightPEAPEAV_1403B2C80.cpp" />
    <ClCompile Include="Source\_Move_backward_optPEAVCMoveMapLimitRightInfoPEAV1U_1403B2FC0.cpp" />
    <ClCompile Include="Source\_Move_catPEAPEAVCMoveMapLimitInfostdYAAU_Undefined_1403AB9F0.cpp" />
    <ClCompile Include="Source\_Move_catPEAPEAVCMoveMapLimitRightstdYAAU_Undefine_1403B2C20.cpp" />
    <ClCompile Include="Source\_Move_catPEAVCMoveMapLimitRightInfostdYAAU_Undefin_1403B2F60.cpp" />
    <ClCompile Include="Source\_Mynodeconst_iterator_TreeV_Tmap_traitsVbasic_stri_14018ED10.cpp" />
    <ClCompile Include="Source\_Myval_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FF00.cpp" />
    <ClCompile Include="Source\_Parent_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140190D50.cpp" />
    <ClCompile Include="Source\_Ptr_catPEAPEAVCMoveMapLimitInfoPEAPEAV1stdYAAU_Sc_1403A36A0.cpp" />
    <ClCompile Include="Source\_Ptr_catPEAPEAVCMoveMapLimitRightPEAPEAV1stdYAAU_S_1403A3C80.cpp" />
    <ClCompile Include="Source\_Ptr_catPEAVCMoveMapLimitRightInfoPEAV1stdYAAU_Non_1403A3720.cpp" />
    <ClCompile Include="Source\_Ptr_catV_Vector_const_iteratorPEAVCMoveMapLimitRi_1403B3320.cpp" />
    <ClCompile Include="Source\_qc_monsterGroup__1_dtor0_140273E80.cpp" />
    <ClCompile Include="Source\_Right_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FEE0.cpp" />
    <ClCompile Include="Source\_Rmost_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140190E60.cpp" />
    <ClCompile Include="Source\_Root_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140191620.cpp" />
    <ClCompile Include="Source\_Rrotate_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140191670.cpp" />
    <ClCompile Include="Source\_ShortRankCMonsterAggroMgrIEAAXXZ_14015E370.cpp" />
    <ClCompile Include="Source\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1A70.cpp" />
    <ClCompile Include="Source\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1AA0.cpp" />
    <ClCompile Include="Source\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1AD0.cpp" />
    <ClCompile Include="Source\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1B00.cpp" />
    <ClCompile Include="Source\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1B30.cpp" />
    <ClCompile Include="Source\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C680.cpp" />
    <ClCompile Include="Source\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C6B0.cpp" />
    <ClCompile Include="Source\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C6F0.cpp" />
    <ClCompile Include="Source\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C720.cpp" />
    <ClCompile Include="Source\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C750.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A2BC0.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A7AA0.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A83F0.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A8420.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A8450.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A8620.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A9240.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403AA350.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403AA380.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403AA3E0.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403A2E30.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403AF740.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403AF770.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B07D0.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B0900.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B0930.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B0960.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B1280.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B12B0.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B12E0.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B1340.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AECE0.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF090.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF0C0.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF100.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF130.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF2F0.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF320.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF350.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AFC40.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AFC70.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B0280.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B02B0.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B0310.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B0590.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B1E70.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B1EA0.cpp" />
    <ClCompile Include="Source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B1ED0.cpp" />
    <ClCompile Include="Source\_std_Construct_CMoveMapLimitRightInfo_CMoveMapLimi_1403B3850.cpp" />
    <ClCompile Include="Source\_std_Find_std_Vector_iterator_CMoveMapLimitRight___1403B2690.cpp" />
    <ClCompile Include="Source\_std_Find_std_Vector_iterator_CMoveMapLimitRight___1403B26C0.cpp" />
    <ClCompile Include="Source\_std_Find_std_Vector_iterator_CMoveMapLimitRight___1403B26F0.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E560.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E590.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E5C0.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E600.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E630.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E660.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E6A0.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E6D0.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E700.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E740.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190400.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190430.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190470.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401904A0.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401904D0.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190500.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190BD0.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140191900.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140191930.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140193FB0.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401945D0.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140194910.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140194940.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140194970.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401949B0.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401949F0.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195430.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195460.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195490.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195A50.cpp" />
    <ClCompile Include="Source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195A80.cpp" />
    <ClCompile Include="Source\_std_Vector_iterator_CMoveMapLimitRight_____ptr64__1403B0A90.cpp" />
    <ClCompile Include="Source\_std_Vector_iterator_CMoveMapLimitRight_____ptr64__1403B0AC0.cpp" />
    <ClCompile Include="Source\_TidyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A2450.cpp" />
    <ClCompile Include="Source\_TidyvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403A3A60.cpp" />
    <ClCompile Include="Source\_TidyvectorVCMoveMapLimitRightInfoVallocatorVCMove_1403A26B0.cpp" />
    <ClCompile Include="Source\_Tidy_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140194450.cpp" />
    <ClCompile Include="Source\_UcopyV_Vector_const_iteratorPEAVCMoveMapLimitRigh_1403B1D70.cpp" />
    <ClCompile Include="Source\_UfillvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1403AA6E0.cpp" />
    <ClCompile Include="Source\_UfillvectorPEAVCMoveMapLimitRightVallocatorPEAVCM_1403AF4E0.cpp" />
    <ClCompile Include="Source\_UfillvectorVCMoveMapLimitRightInfoVallocatorVCMov_1403B1730.cpp" />
    <ClCompile Include="Source\_UmovePEAPEAVCMoveMapLimitInfovectorPEAVCMoveMapLi_1403AAF70.cpp" />
    <ClCompile Include="Source\_UmovePEAPEAVCMoveMapLimitRightvectorPEAVCMoveMapL_1403B1F60.cpp" />
    <ClCompile Include="Source\_UmovePEAVCMoveMapLimitRightInfovectorVCMoveMapLim_1403B2260.cpp" />
    <ClCompile Include="Source\_Unchecked_move_backwardPEAPEAVCMoveMapLimitInfoPE_1403AB040.cpp" />
    <ClCompile Include="Source\_Unchecked_move_backwardPEAPEAVCMoveMapLimitRightP_1403B2030.cpp" />
    <ClCompile Include="Source\_Unchecked_move_backwardPEAVCMoveMapLimitRightInfo_1403B2330.cpp" />
    <ClCompile Include="Source\_XlenvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A2B30.cpp" />
    <ClCompile Include="Source\_XlenvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403B0500.cpp" />
    <ClCompile Include="Source\_XlenvectorVCMoveMapLimitRightInfoVallocatorVCMove_1403A2DA0.cpp" />
  </ItemGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>