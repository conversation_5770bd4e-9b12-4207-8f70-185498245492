#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Set_Box_Count@CGoldenBoxItemMgr@@QEAAXE@Z
 * Address: 0x140413F10

void  CGoldenBoxItemMgr::Set_Box_Count(CGoldenBoxItemMgr *this, char byIndex)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-28h]@1
  CGoldenBoxItemMgr *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v5->m_golden_box_item.m_wBoxMax[(unsigned int8_t)byIndex] )
  {
    --v5->m_golden_box_item.m_wBoxMax[(unsigned int8_t)byIndex];
    CGoldenBoxItemMgr::Set_ToStruct(v5);
  }
}
