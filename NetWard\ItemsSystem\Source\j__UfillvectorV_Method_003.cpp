#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Ufill@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@IEAAPEAVCUnmannedTraderSchedule@@PEAV3@_KAEBV3@@Z
 * Address: 0x14000E412

CUnmannedTraderSchedule * std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Ufill(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, CUnmannedTraderSchedule *_Ptr, unsigned int64_t _Count, CUnmannedTraderSchedule *_Val)
{
  return std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Ufill(this, _Ptr, _Count, _<PERSON>);
}
