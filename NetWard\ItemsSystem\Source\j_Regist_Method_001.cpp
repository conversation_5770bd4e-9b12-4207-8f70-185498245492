#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?Regist@CUnmannedTraderUserInfo@@QEAAXEPEAU_a_trade_reg_item_request_clzo@@PEAVCLogFile@@@Z
 * Address: 0x140002EE1

void  CUnmannedTraderUserInfo::Regist(CUnmannedTraderUserInfo *this, char byType, _a_trade_reg_item_request_clzo *pRequest, CLogFile *pkLogger)
{
  CUnmannedTraderUserInfo::Regist(this, byType, pRequest, pkLogger);
}
