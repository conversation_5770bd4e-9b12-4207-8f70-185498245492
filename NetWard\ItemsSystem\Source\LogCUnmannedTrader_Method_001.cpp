#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Log@CUnmannedTraderController@@IEAAXPEADZZ
 * Address: 0x1403501E0

void CUnmannedTraderController::Log(CUnmannedTraderController *this, char *fmt, ...)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-48h]@1
  char *arg; // [sp+28h] [bp-20h]@5
  CUnmannedTraderController *v6; // [sp+50h] [bp+8h]@1
  va_list va; // [sp+60h] [bp+18h]@1

  va_start(va, fmt);
  v6 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v6->m_pkLogger )
  {
    arg = (char *)va;
    CLogFile::WriteFromArg(v6->m_pkLogger, fmt, (char *)va);
  }
}
