#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: lua_createtable
 * Address: 0x1405339C0

int  lua_createtable(int64_t a1, unsigned int a2, unsigned int a3)
{
  unsigned int v3; // esi@1
  unsigned int v4; // ebp@1
  int64_t v5; // rdi@1
  int64_t v6; // rbx@3
  int64_t v7; // rax@3

  v3 = a3;
  v4 = a2;
  v5 = a1;
  if ( *(uint64_t*)(*(uint64_t*)(a1 + 32) + 120i64) >= *(uint64_t*)(*(uint64_t*)(a1 + 32) + 112i64) )
    luaC_step(a1);
  v6 = *(uint64_t*)(v5 + 16);
  LODWORD(v7) = luaH_new(v5, v4, v3);
  *(uint64_t*)v6 = v7;
  *(uint32_t*)(v6 + 8) = 5;
  *(uint64_t*)(v5 + 16) += 16i64;
  return v7;
}
