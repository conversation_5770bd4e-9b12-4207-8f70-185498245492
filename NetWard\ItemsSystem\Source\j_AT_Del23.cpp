#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?destroy@?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@QEAAXPEAVCUnmannedTraderItemCodeInfo@@@Z
 * Address: 0x14000C1B7

void  std::allocator<CUnmannedTraderItemCodeInfo>::destroy(std::allocator<CUnmannedTraderItemCodeInfo> *this, CUnmannedTraderItemCodeInfo *_Ptr)
{
  std::allocator<CUnmannedTraderItemCodeInfo>::destroy(this, _Ptr);
}
