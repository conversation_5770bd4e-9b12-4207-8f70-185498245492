#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::_Insert_n_::_1_::catch$0
 * Address: 0x1403AA380

void  __noreturn std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::_Insert_n_::_1_::catch_0(int64_t a1, int64_t a2)
{
  int64_t v2; // rbp@1

  v2 = a2;
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Destroy(
    *(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > **)(a2 + 160),
    *(CMoveMapLimitInfo ***)(a2 + 64),
    *(CMoveMapLimitInfo ***)(a2 + 72));
  std::allocator<CMoveMapLimitInfo *>::deallocate(
    (std::allocator<CMoveMapLimitInfo *> *)(*(uint64_t*)(v2 + 160) + 8i64),
    *(CMoveMapLimitInfo ***)(v2 + 64),
    *(uint64_t*)(v2 + 56));
  CxxThrowException_0(0i64, 0i64);
}
