#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CLogTypeDBTaskPool::Destroy_::_1_::dtor$1
 * Address: 0x1402C2A60

void  CLogTypeDBTaskPool::Destroy_::_1_::dtor_1(int64_t a1, int64_t a2)
{
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>((std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)(a2 + 72));
}
