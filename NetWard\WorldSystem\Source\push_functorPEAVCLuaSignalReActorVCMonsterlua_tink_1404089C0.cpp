#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$push_functor@PEAVCLuaSignalReActor@@VCMonster@@@lua_tinker@@YAXPEAUlua_State@@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@Z
 * Address: 0x1404089C0

void  lua_tinker::push_functor<CLuaSignalReActor *,CMonster>(struct lua_State *L, CLuaSignalReActor *( *func)(CMonster *this))
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-28h]@1
  struct lua_State *v5; // [sp+30h] [bp+8h]@1

  v5 = L;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  lua_pushcclosure(v5, lua_tinker::mem_functor<CMonster,void,void,void,void,void>::invoke<CLuaSignalReActor *>, 1i64);
}
