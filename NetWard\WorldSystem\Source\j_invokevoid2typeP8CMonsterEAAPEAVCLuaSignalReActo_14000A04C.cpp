#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?invoke@?$void2type@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@SAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAX@Z
 * Address: 0x14000A04C

CLuaSignalReActor *( * lua_tinker::void2type<CLuaSignalReActor * (CMonster::*)(void)>::invoke(lua_tinker::void2type<CLuaSignalReActor * ( CMonster::*)(void)> *this, void *ptr))(CMonster *this)
{
  return lua_tinker::void2type<CLuaSignalReActor * (CMonster::*)(void)>::invoke(this, ptr);
}
