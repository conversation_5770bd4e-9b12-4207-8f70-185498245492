#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Load@CUnmannedTraderController@@QEAA_NGKAEAU_TRADE_DB_BASE@@@Z
 * Address: 0x140078C10

bool  CUnmannedTraderController::Load(CUnmannedTraderController *this, unsigned int16_t wInx, unsigned int dwSerial, _TRADE_DB_BASE *kInfo)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  CUnmannedTraderUserInfoTable *v6; // rax@4
  int64_t v8; // [sp+0h] [bp-38h]@1
  unsigned int16_t v9; // [sp+48h] [bp+10h]@1
  unsigned int dwSeriala; // [sp+50h] [bp+18h]@1
  _TRADE_DB_BASE *v11; // [sp+58h] [bp+20h]@1

  v11 = kInfo;
  dwSeriala = dwSerial;
  v9 = wInx;
  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v6 = CUnmannedTraderUserInfoTable::Instance();
  return CUnmannedTraderUserInfoTable::Load(v6, 0, v9, dwSeriala, v11);
}
