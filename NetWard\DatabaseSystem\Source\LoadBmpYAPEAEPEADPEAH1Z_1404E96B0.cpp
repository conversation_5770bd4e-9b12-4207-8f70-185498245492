#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?LoadBmp@@YAPEAEPEADPEAH1@Z
 * Address: 0x1404E96B0

unsigned int8_t * LoadBmp(char *a1, int *a2, int *a3)
{
  int *v3; // r12@1
  int *v4; // rsi@1
  FILE *v6; // rax@3
  FILE *v7; // rdi@3
  int v8; // ebx@7
  int64_t v9; // rbp@7
  void *v10; // rax@7
  int v11; // ecx@7
  void *v12; // r13@7
  size_t v13; // rsi@8
  int32_t v14; // er12@8
  char *v15; // rbx@8
  char DstBuf; // [sp+20h] [bp-68h]@4
  int v17; // [sp+32h] [bp-56h]@7
  unsigned int v18; // [sp+36h] [bp-52h]@7
  int16_t v19; // [sp+3Ch] [bp-4Ch]@4

  v3 = a3;
  v4 = a2;
  if ( !*a1 )
    return 0i64;
  v6 = fopen(a1, "rb");
  v7 = v6;
  if ( !v6 )
    return 0i64;
  fread(&DstBuf, 0x36ui64, 1ui64, v6);
  if ( v19 != 24 )
  {
    fclose(v7);
    return 0i64;
  }
  v8 = v17;
  v9 = v18;
  *v4 = v17;
  *v3 = v9;
  v10 = Dmalloc(3 * v8 * (signed int)v9);
  v11 = 3 * v8;
  v12 = v10;
  if ( (signed int)v9 > 0 )
  {
    v13 = v11;
    v14 = ((3 * v8 + 3) & 0xFFFFFFFC) - v11;
    v15 = (char *)v10;
    do
    {
      fread(v15, v13, 1ui64, v7);
      fseek(v7, v14, 1);
      v15 += v13;
      --v9;
    }
    while ( v9 );
  }
  fclose(v7);
  return (unsigned int8_t *)v12;
}
