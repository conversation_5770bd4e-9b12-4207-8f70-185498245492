#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ModifyPrice@CUnmannedTraderUserInfo@@QEAAXEPEAU_a_trade_adjust_price_request_clzo@@PEAVCLogFile@@@Z
 * Address: 0x140353A00

void  CUnmannedTraderUserInfo::ModifyPrice(CUnmannedTraderUserInfo *this, char byType, _a_trade_adjust_price_request_clzo *pRequest, CLogFile *pkLogger)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  CMoneySupplyMgr *v6; // rax@11
  int64_t v7; // [sp+0h] [bp-88h]@1
  unsigned int dwOldPrice; // [sp+38h] [bp-50h]@5
  unsigned int16_t v9; // [sp+3Ch] [bp-4Ch]@7
  unsigned int v10; // [sp+40h] [bp-48h]@7
  unsigned int16_t v11; // [sp+44h] [bp-44h]@7
  unsigned int dwSub; // [sp+48h] [bp-40h]@5
  char v13; // [sp+4Ch] [bp-3Ch]@7
  char v14; // [sp+4Dh] [bp-3Bh]@7
  unsigned int v15; // [sp+50h] [bp-38h]@7
  unsigned int v16; // [sp+54h] [bp-34h]@7
  char v17; // [sp+64h] [bp-24h]@5
  int v18; // [sp+68h] [bp-20h]@7
  int nLv; // [sp+6Ch] [bp-1Ch]@11
  int v20; // [sp+70h] [bp-18h]@11
  CUnmannedTraderUserInfo *v21; // [sp+90h] [bp+8h]@1
  char v22; // [sp+98h] [bp+10h]@1
  _a_trade_adjust_price_request_clzo *pRequesta; // [sp+A0h] [bp+18h]@1

  pRequesta = pRequest;
  v22 = byType;
  v21 = this;
  v4 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned int8_t)byType < 2 )
  {
    v17 = CUnmannedTraderUserInfo::CheckModifyPrice(v21, byType, pRequest, &dwOldPrice, pkLogger, &dwSub);
    if ( v17 )
    {
      CUnmannedTraderUserInfo::SendRepriceErrorResult(v21, &g_Player + v21->m_wInx, v17);
    }
    else
    {
      v9 = v21->m_wInx;
      v10 = v21->m_dwUserSerial;
      v11 = pRequesta->wItemSerial;
      v13 = 0;
      v14 = v22;
      v15 = pRequesta->dwRegistSerial;
      v16 = pRequesta->dwNewPrice;
      CPlayer::SubDalant(&g_Player + v21->m_wInx, dwSub);
      v18 = CPlayerDB::GetLevel((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * v21->m_wInx));
      if ( v18 == 30 || v18 == 40 || v18 == 50 || v18 == 60 )
      {
        nLv = CPlayerDB::GetLevel((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * v21->m_wInx));
        v20 = CPlayerDB::GetRaceCode((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * v21->m_wInx));
        v6 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateFeeMoneyData(v6, v20, nLv, dwSub);
      }
      CUnmannedTraderRequestLimiter::SetRequest(&v21->m_kRequestState, 1);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 63, (char *)&dwOldPrice, 32);
    }
  }
}
