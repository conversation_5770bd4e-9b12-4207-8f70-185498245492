#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLWriteDSNToIniW
 * Address: 0x1404DAFA8

int  SQLWriteDSNToIniW(const unsigned int16_t *lpszDSN, const unsigned int16_t *lpszDriver)
{
  const unsigned int16_t *v2; // rdi@1
  const unsigned int16_t *v3; // rbx@1
  int64_t ( *v4)(); // rax@1
  int result; // eax@2

  v2 = lpszDSN;
  v3 = lpszDriver;
  v4 = ODBC___GetSetupProc("SQLWriteDSNToIniW");
  if ( v4 )
    result = ((int ( *)(const unsigned int16_t *, const unsigned int16_t *))v4)(v2, v3);
  else
    result = 0;
  return result;
}
