#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Select_AllGuildNum@CRFWorldDatabase@@QEAAGXZ
 * Address: 0x140499340

unsigned int16_t  CRFWorldDatabase::Select_AllGuildNum(CRFWorldDatabase *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  unsigned int16_t result; // ax@8
  int64_t v4; // [sp+0h] [bp-1A8h]@1
  void *SQLStmt; // [sp+20h] [bp-188h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-180h]@21
  SQLLEN v7; // [sp+38h] [bp-170h]@21
  int16_t v8; // [sp+44h] [bp-164h]@9
  char Dest; // [sp+60h] [bp-148h]@4
  int TargetValue; // [sp+174h] [bp-34h]@4
  unsigned int64_t v11; // [sp+190h] [bp-18h]@4
  CRFWorldDatabase *v12; // [sp+1B0h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 104i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v11 = (unsigned int64_t)&v4 ^ _security_cookie;
  TargetValue = 0;
  sprintf(&Dest, "select count(*) from tbl_guild where dck=0");
  if ( v12->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v12->vfptr, &Dest);
  if ( v12->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v12->vfptr) )
  {
    v8 = SQLExecDirectA_0(v12->m_hStmtSelect, &Dest, -3);
    if ( v8 && v8 != 1 )
    {
      if ( v8 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v12->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v12->vfptr, v8, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v12->vfptr, v8, v12->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v8 = SQLFetch_0(v12->m_hStmtSelect);
      if ( v8 && v8 != 1 )
      {
        if ( v8 != 100 )
        {
          SQLStmt = v12->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v12->vfptr, v8, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v12->vfptr, v8, v12->m_hStmtSelect);
        }
        if ( v12->m_hStmtSelect )
          SQLCloseCursor_0(v12->m_hStmtSelect);
        result = 0;
      }
      else
      {
        StrLen_or_IndPtr = &v7;
        SQLStmt = 0i64;
        v8 = SQLGetData_0(v12->m_hStmtSelect, 1u, 5, &TargetValue, 0i64, &v7);
        if ( v8 && v8 != 1 )
        {
          SQLStmt = v12->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v12->vfptr, v8, &Dest, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v12->vfptr, v8, v12->m_hStmtSelect);
          if ( v12->m_hStmtSelect )
            SQLCloseCursor_0(v12->m_hStmtSelect);
          result = 0;
        }
        else
        {
          if ( v12->m_hStmtSelect )
            SQLCloseCursor_0(v12->m_hStmtSelect);
          if ( v12->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v12->vfptr, "%s Success", &Dest);
          result = TargetValue;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::FmtLog((CRFNewDatabase *)&v12->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
