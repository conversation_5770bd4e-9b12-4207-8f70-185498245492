#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Ufill@?$vector@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderItemCodeInfo@@PEAV3@_KAEBV3@@Z
 * Address: 0x14000FC22

CUnmannedTraderItemCodeInfo * std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Ufill(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this, CUnmannedTraderItemCodeInfo *_Ptr, unsigned int64_t _Count, CUnmannedTraderItemCodeInfo *_Val)
{
  return std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Ufill(
           this,
           _Ptr,
           _Count,
           _Val);
}
