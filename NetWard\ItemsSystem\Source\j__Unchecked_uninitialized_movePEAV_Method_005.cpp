#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAVCUnmannedTraderUserInfo@@PEAV1@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@stdext@@YAPEAVCUnmannedTraderUserInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderUserInfo@@@std@@@Z
 * Address: 0x14000F8F3

CUnmannedTraderUserInfo * stdext::_Unchecked_uninitialized_move<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *,std::allocator<CUnmannedTraderUserInfo>>(CUnmannedTraderUserInfo *_First, CUnmannedTraderUserInfo *_Last, CUnmannedTraderUserInfo *_Dest, std::allocator<CUnmannedTraderUserInfo> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *,std::allocator<CUnmannedTraderUserInfo>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
