#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Iter_cat@PEAPEAVCUnmannedTraderClassInfo@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCUnmannedTraderClassInfo@@@Z
 * Address: 0x14000F3F3

std::random_access_iterator_tag  std::_Iter_cat<CUnmannedTraderClassInfo * *>(CUnmannedTraderClassInfo **const *__formal)
{
  return std::_Iter_cat<CUnmannedTraderClassInfo * *>(__formal);
}
