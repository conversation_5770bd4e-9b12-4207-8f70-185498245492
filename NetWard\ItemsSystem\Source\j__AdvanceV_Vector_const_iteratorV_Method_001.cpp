#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Advance@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@_J@std@@YAXAEAV?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@0@_JUrandom_access_iterator_tag@0@@Z
 * Address: 0x1400036B1

void  std::_Advance<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,int64_t>(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Where, int64_t _Off, std::random_access_iterator_tag __formal)
{
  std::_Advance<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,int64_t>(
    _Where,
    _Off,
    __formal);
}
