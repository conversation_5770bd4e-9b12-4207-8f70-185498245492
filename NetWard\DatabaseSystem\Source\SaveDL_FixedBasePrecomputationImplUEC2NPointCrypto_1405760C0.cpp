#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Save@?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@CryptoPP@@UEBAXAEBV?$DL_GroupPrecomputation@UEC2NPoint@CryptoPP@@@2@AEAVBufferedTransformation@2@@Z
 * Address: 0x1405760C0

int  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::Save(int64_t a1, int64_t *a2, struct CryptoPP::BufferedTransformation *a3)
{
  unsigned int64_t v3; // rax@2
  signed int64_t v4; // rax@3
  CryptoPP::DERGeneralEncoder v6; // [sp+30h] [bp-98h]@1
  unsigned int i; // [sp+90h] [bp-38h]@1
  int64_t v8; // [sp+98h] [bp-30h]@1
  int64_t v9; // [sp+A0h] [bp-28h]@1
  unsigned int64_t v10; // [sp+A8h] [bp-20h]@2
  int64_t v11; // [sp+B0h] [bp-18h]@3
  int64_t v12; // [sp+D0h] [bp+8h]@1
  int64_t *v13; // [sp+D8h] [bp+10h]@1

  v13 = a2;
  v12 = a1;
  v8 = -2i64;
  CryptoPP::DERSequenceEncoder::DERSequenceEncoder((CryptoPP::DERSequenceEncoder *)&v6, a3, 48);
  CryptoPP::DEREncodeUnsigned<unsigned int>((CryptoPP::BufferedTransformation *)&v6.vfptr, 1u, 2u);
  v9 = *(uint64_t*)(v12 + 72);
  (*(void ( **)(signed int64_t, CryptoPP::DERGeneralEncoder *))(v9 + 16))(v12 + 72, &v6);
  for ( i = 0; ; ++i )
  {
    v10 = i;
    v3 = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::size(v12 + 112);
    if ( v10 >= v3 )
      break;
    v4 = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator[](v12 + 112, i);
    v11 = *v13;
    (*(void ( **)(int64_t *, CryptoPP::DERGeneralEncoder *, signed int64_t))(v11 + 40))(v13, &v6, v4);
  }
  CryptoPP::DERGeneralEncoder::MessageEnd(&v6);
  return CryptoPP::DERSequenceEncoder::~DERSequenceEncoder((CryptoPP::DERSequenceEncoder *)&v6);
}
