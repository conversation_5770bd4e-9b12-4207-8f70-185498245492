#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Allocate@VCUnmannedTraderItemCodeInfo@@@std@@YAPEAVCUnmannedTraderItemCodeInfo@@_KPEAV1@@Z
 * Address: 0x14000406B

CUnmannedTraderItemCodeInfo * std::_Allocate<CUnmannedTraderItemCodeInfo>(unsigned int64_t _Count, CUnmannedTraderItemCodeInfo *__formal)
{
  return std::_Allocate<CUnmannedTraderItemCodeInfo>(_Count, __formal);
}
