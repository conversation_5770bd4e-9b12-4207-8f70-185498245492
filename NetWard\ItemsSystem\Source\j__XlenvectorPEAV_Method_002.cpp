#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_<PERSON>len@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@KAXXZ
 * Address: 0x14001331D

void  __noreturn std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_<PERSON>len(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_<PERSON><PERSON>(this);
}
