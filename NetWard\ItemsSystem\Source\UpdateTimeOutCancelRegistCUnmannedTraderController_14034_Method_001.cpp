#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?UpdateTimeOutCancelRegist@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034CFA0

char  CUnmannedTraderController::UpdateTimeOutCancelRegist(CUnmannedTraderController *this, char *pData)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v5; // [sp+0h] [bp-D8h]@1
  char *byProcRet; // [sp+20h] [bp-B8h]@4
  char *v7; // [sp+30h] [bp-A8h]@4
  char byState; // [sp+44h] [bp-94h]@4
  char v9; // [sp+54h] [bp-84h]@4
  char Dst; // [sp+68h] [bp-70h]@6
  _unmannedtrader_buy_item_info kData; // [sp+98h] [bp-40h]@6
  CUnmannedTraderController *v12; // [sp+E0h] [bp+8h]@1

  v12 = this;
  v2 = &v5;
  for ( i = 52i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v7 = pData;
  pData[50] = 0;
  byState = -1;
  byProcRet = v7 + 50;
  v9 = CUnmannedTraderController::CheckDBItemState(v12, *v7, *((uint32_t*)v7 + 1), &byState, v7 + 50);
  if ( v7[50] )
  {
    result = v9;
  }
  else
  {
    memset_0(&Dst, 0, 0x10ui64);
    GetLocalTime((LPSYSTEMTIME)&Dst);
    memset_0(&kData, 0, 0x28ui64);
    v9 = CRFWorldDatabase::Select_UnmannedTraderBuySingleItemInfo(pkDB, *v7, *((uint32_t*)v7 + 1), &kData);
    if ( v9 == 1 )
    {
      v7[50] = 47;
      byProcRet = &Dst;
      CRFWorldDatabase::Update_UnmannedTraderItemState(pkDB, *v7, *((uint32_t*)v7 + 1), 9, (_SYSTEMTIME *)&Dst);
      result = 24;
    }
    else if ( v9 == 2 )
    {
      v7[50] = 48;
      result = 0;
    }
    else if ( kData.dwSeller == *((uint32_t*)v7 + 3) )
    {
      *((uint32_t*)v7 + 13) = kData.dwK;
      *((uint32_t*)v7 + 14) = kData.dwD;
      *((uint32_t*)v7 + 15) = kData.dwU;
      *((uint32_t*)v7 + 16) = kData.dwT;
      if ( kData.lnUID )
        *((uint64_t*)v7 + 9) = kData.lnUID;
      else
        *((uint64_t*)v7 + 9) = UIDGenerator::getuid(unk_1799C608C);
      if ( !v7[20] || !v7[33] )
        CRFWorldDatabase::Select_CharacterName(pkDB, *((uint32_t*)v7 + 3), v7 + 33, v7 + 20);
      byProcRet = &Dst;
      if ( CRFWorldDatabase::Update_UnmannedTraderItemState(pkDB, *v7, *((uint32_t*)v7 + 1), v7[8], (_SYSTEMTIME *)&Dst) )
      {
        result = 0;
      }
      else
      {
        v7[50] = 46;
        result = 24;
      }
    }
    else
    {
      v7[50] = 93;
      result = 0;
    }
  }
  return result;
}
