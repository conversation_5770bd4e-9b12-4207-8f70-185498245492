#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_UserGetScaner@CUserDB@@QEAA_NGG@Z
 * Address: 0x14011BC50

char  CUserDB::Update_UserGetScaner(CUserDB *this, unsigned int16_t wScanerCnt, unsigned int16_t wBattleTime)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-C8h]@1
  int v7; // [sp+20h] [bp-A8h]@4
  int v8; // [sp+28h] [bp-A0h]@4
  int v9; // [sp+30h] [bp-98h]@4
  _SYSTEMTIME SystemTime; // [sp+48h] [bp-80h]@4
  char DstBuf; // [sp+78h] [bp-50h]@4
  char v12; // [sp+79h] [bp-4Fh]@4
  unsigned int64_t v13; // [sp+B0h] [bp-18h]@4
  CUserDB *v14; // [sp+D0h] [bp+8h]@1
  unsigned int16_t v15; // [sp+D8h] [bp+10h]@1
  unsigned int16_t v16; // [sp+E0h] [bp+18h]@1

  v16 = wBattleTime;
  v15 = wScanerCnt;
  v14 = this;
  v3 = &v6;
  for ( i = 48i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v13 = (unsigned int64_t)&v6 ^ _security_cookie;
  GetLocalTime(&SystemTime);
  DstBuf = 0;
  memset(&v12, 0, 0x1Dui64);
  v14->m_AvatorData.dbSupplement.wScanerCnt += v15;
  v9 = v16;
  v8 = SystemTime.wDay;
  v7 = SystemTime.wMonth;
  sprintf_s(&DstBuf, 0x1Eui64, "%04d%02d%02d%d", SystemTime.wYear);
  v14->m_AvatorData.dbSupplement.dwScanerGetDate = atoi(&DstBuf);
  v14->m_bDataUpdate = 1;
  return 1;
}
