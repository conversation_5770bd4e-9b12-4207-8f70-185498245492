#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Construct@PEAVCUnmannedTraderClassInfo@@PEAV1@@std@@YAXPEAPEAVCUnmannedTraderClassInfo@@AEBQEAV1@@Z
 * Address: 0x1400032C9

void  std::_Construct<CUnmannedTraderClassInfo *,CUnmannedTraderClassInfo *>(CUnmannedTraderClassInfo **_Ptr, CUnmannedTraderClassInfo *const *_Val)
{
  std::_Construct<CUnmannedTraderClassInfo *,CUnmannedTraderClassInfo *>(_Ptr, _Val);
}
