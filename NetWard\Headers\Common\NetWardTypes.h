// NetWard RF Online Server - Core Type Definitions
// File: NetWardTypes.h - Common type definitions for Visual Studio 2022
// Compatible with C++20 standard

#pragma once

#include <cstdint>
#include <string>
#include <string_view>
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>

namespace NetWard {

// ============================================================================
// BASIC TYPE DEFINITIONS (RF Online Compatible)
// ============================================================================

// Unsigned integer types
using uint8  = std::uint8_t;   // unsigned char (1 byte)
using uint16 = std::uint16_t;  // unsigned short (2 bytes)  
using uint32 = std::uint32_t;  // unsigned int (4 bytes)
using uint64 = std::uint64_t;  // unsigned long long (8 bytes)

// Signed integer types
using int8  = std::int8_t;     // signed char (1 byte)
using int16 = std::int16_t;    // short (2 bytes)
using int32 = std::int32_t;    // int (4 bytes)
using int64 = std::int64_t;    // long long (8 bytes)

// Floating point types
using float32 = float;         // 32-bit float
using float64 = double;        // 64-bit double

// String types
using String = std::string;
using StringView = std::string_view;
using WString = std::wstring;

// ============================================================================
// RF ONLINE SPECIFIC TYPES
// ============================================================================

// Character and Race types
enum class RaceType : uint8 {
    Bellato = 0,
    Cora    = 1,
    Accretia = 2,
    Invalid = 0xFF
};

enum class ClassType : uint8 {
    Warrior  = 0,
    Ranger   = 1,
    Spiritualist = 2,
    Specialist = 3,
    Invalid  = 0xFF
};

// Item related types
using ItemCode = String;
using ItemSerial = uint64;
using ItemIndex = uint16;
using ItemCount = uint32;

struct ItemPosition {
    uint8 inventoryType;
    uint8 slotIndex;
    
    ItemPosition() : inventoryType(0), slotIndex(0) {}
    ItemPosition(uint8 invType, uint8 slot) : inventoryType(invType), slotIndex(slot) {}
};

// Money and trading types
using Money = uint64;
using TradePrice = uint64;
using TaxRate = float32;

// Network types
using PacketSize = uint16;
using PacketType = uint16;
using SessionID = uint32;
using AccountID = uint32;
using CharacterID = uint32;

// ============================================================================
// SMART POINTER ALIASES
// ============================================================================

template<typename T>
using UniquePtr = std::unique_ptr<T>;

template<typename T>
using SharedPtr = std::shared_ptr<T>;

template<typename T>
using WeakPtr = std::weak_ptr<T>;

// ============================================================================
// CONTAINER ALIASES
// ============================================================================

template<typename T>
using Vector = std::vector<T>;

template<typename Key, typename Value>
using HashMap = std::unordered_map<Key, Value>;

// ============================================================================
// FUNCTION TYPES
// ============================================================================

template<typename... Args>
using Function = std::function<Args...>;

using VoidCallback = Function<void()>;
using BoolCallback = Function<bool()>;

// ============================================================================
// RESULT AND ERROR HANDLING
// ============================================================================

enum class ResultCode : uint32 {
    Success = 0,
    Failed = 1,
    InvalidParameter = 2,
    OutOfMemory = 3,
    NotFound = 4,
    AccessDenied = 5,
    Timeout = 6,
    NetworkError = 7,
    DatabaseError = 8,
    InvalidState = 9
};

template<typename T>
struct Result {
    ResultCode code;
    T value;
    String errorMessage;
    
    Result() : code(ResultCode::Failed) {}
    Result(ResultCode c) : code(c) {}
    Result(T val) : code(ResultCode::Success), value(std::move(val)) {}
    Result(ResultCode c, String msg) : code(c), errorMessage(std::move(msg)) {}
    
    bool IsSuccess() const noexcept { return code == ResultCode::Success; }
    bool IsFailure() const noexcept { return code != ResultCode::Success; }
    
    explicit operator bool() const noexcept { return IsSuccess(); }
};

// ============================================================================
// UTILITY MACROS
// ============================================================================

#define NETWARD_SAFE_DELETE(ptr) \
    do { \
        delete (ptr); \
        (ptr) = nullptr; \
    } while(0)

#define NETWARD_SAFE_DELETE_ARRAY(ptr) \
    do { \
        delete[] (ptr); \
        (ptr) = nullptr; \
    } while(0)

#define NETWARD_UNUSED(x) ((void)(x))

// Disable copy constructor and assignment operator
#define NETWARD_DISABLE_COPY(ClassName) \
    ClassName(const ClassName&) = delete; \
    ClassName& operator=(const ClassName&) = delete;

// Disable move constructor and assignment operator  
#define NETWARD_DISABLE_MOVE(ClassName) \
    ClassName(ClassName&&) = delete; \
    ClassName& operator=(ClassName&&) = delete;

// Disable both copy and move
#define NETWARD_DISABLE_COPY_AND_MOVE(ClassName) \
    NETWARD_DISABLE_COPY(ClassName) \
    NETWARD_DISABLE_MOVE(ClassName)

} // namespace NetWard
