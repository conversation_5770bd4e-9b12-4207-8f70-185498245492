#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?NPCLinkCheckItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB9C0

char  CNetworkEX::NPCLinkCheckItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-38h]@1
  _STORAGE_POS_INDIV *pStorage; // [sp+20h] [bp-18h]@4
  CPlayer *v8; // [sp+28h] [bp-10h]@4

  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  pStorage = (_STORAGE_POS_INDIV *)pBuf;
  v8 = &g_Player + n;
  if ( v8->m_bOper )
  {
    CPlayer::pc_NPCLinkCheckItemRequest(v8, pStorage);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
