#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ReRangeClientIndex@CUserDB@@SAXPEAU_AVATOR_DATA@@@Z
 * Address: 0x140119ED0

void  CUserDB::ReRangeClientIndex(_AVATOR_DATA *pData)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-358h]@1
  CNetIndexList v4; // [sp+30h] [bp-328h]@4
  char Dst[116]; // [sp+F0h] [bp-268h]@4
  int nMax; // [sp+164h] [bp-1F4h]@4
  unsigned int pdwList[101]; // [sp+180h] [bp-1D8h]@12
  int v8; // [sp+314h] [bp-44h]@12
  int v9; // [sp+318h] [bp-40h]@4
  unsigned int dwIndex; // [sp+31Ch] [bp-3Ch]@4
  int j; // [sp+320h] [bp-38h]@12
  int k; // [sp+324h] [bp-34h]@14
  int l; // [sp+328h] [bp-30h]@30
  int m; // [sp+32Ch] [bp-2Ch]@46
  int64_t v15; // [sp+338h] [bp-20h]@4
  unsigned int64_t v16; // [sp+340h] [bp-18h]@4
  _AVATOR_DATA *v17; // [sp+360h] [bp+8h]@1

  v17 = pData;
  v1 = &v3;
  for ( i = 212i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v15 = -2i64;
  v16 = (unsigned int64_t)&v3 ^ _security_cookie;
  CNetIndexList::CNetIndexList(&v4);
  v9 = 0;
  CNetIndexList::SetList(&v4, 0x64u);
  nMax = 20 * v17->dbAvator.m_byBagNum;
  CNetIndexList::ResetList(&v4);
  memset_0(Dst, 0, 0x64ui64);
  v9 = 0;
  for ( dwIndex = 0; (signed int)dwIndex < nMax; ++dwIndex )
  {
    if ( _INVENKEY::IsFilled((_INVENKEY *)((char *)&v17->dbInven + 37 * (signed int)dwIndex)) )
    {
      if ( v17->dbInven.m_List[dwIndex].Key.bySlotIndex >= nMax || Dst[v17->dbInven.m_List[dwIndex].Key.bySlotIndex] )
        CNetIndexList::PushNode_Back(&v4, dwIndex);
      else
        Dst[v17->dbInven.m_List[dwIndex].Key.bySlotIndex] = 1;
    }
  }
  v9 = 0;
  v8 = CNetIndexList::CopyIndexList(&v4, pdwList, nMax);
  for ( j = 0; j < v8; ++j )
  {
    for ( k = v9; k < nMax; ++k )
    {
      if ( !Dst[k] )
      {
        v17->dbInven.m_List[pdwList[j]].Key.bySlotIndex = k;
        Dst[k] = 1;
        v9 = k;
        break;
      }
    }
  }
  CNetIndexList::ResetList(&v4);
  nMax = 7;
  memset_0(Dst, 0, 0x64ui64);
  v9 = 0;
  for ( dwIndex = 0; (signed int)dwIndex < nMax; ++dwIndex )
  {
    if ( _EMBELLKEY::IsFilled((_EMBELLKEY *)((char *)&v17->dbEquip + 27 * (signed int)dwIndex)) )
    {
      if ( v17->dbEquip.m_EmbellishList[dwIndex].Key.bySlotIndex >= nMax
        || Dst[v17->dbEquip.m_EmbellishList[dwIndex].Key.bySlotIndex] )
      {
        CNetIndexList::PushNode_Back(&v4, dwIndex);
      }
      else
      {
        Dst[v17->dbEquip.m_EmbellishList[dwIndex].Key.bySlotIndex] = 1;
      }
    }
  }
  v9 = 0;
  v8 = CNetIndexList::CopyIndexList(&v4, pdwList, nMax);
  for ( j = 0; j < v8; ++j )
  {
    for ( l = v9; l < nMax; ++l )
    {
      if ( !Dst[l] )
      {
        v17->dbEquip.m_EmbellishList[pdwList[j]].Key.bySlotIndex = l;
        Dst[l] = 1;
        v9 = l;
        break;
      }
    }
  }
  nMax = v17->dbTrunk.bySlotNum;
  CNetIndexList::ResetList(&v4);
  memset_0(Dst, 0, 0x64ui64);
  v9 = 0;
  for ( dwIndex = 0; (signed int)dwIndex < nMax; ++dwIndex )
  {
    if ( _INVENKEY::IsFilled(&v17->dbTrunk.m_List[dwIndex].Key) )
    {
      if ( v17->dbTrunk.m_List[dwIndex].Key.bySlotIndex >= nMax || Dst[v17->dbTrunk.m_List[dwIndex].Key.bySlotIndex] )
        CNetIndexList::PushNode_Back(&v4, dwIndex);
      else
        Dst[v17->dbTrunk.m_List[dwIndex].Key.bySlotIndex] = 1;
    }
  }
  v9 = 0;
  v8 = CNetIndexList::CopyIndexList(&v4, pdwList, nMax);
  for ( j = 0; j < v8; ++j )
  {
    for ( m = v9; m < nMax; ++m )
    {
      if ( !Dst[m] )
      {
        v17->dbTrunk.m_List[pdwList[j]].Key.bySlotIndex = m;
        Dst[m] = 1;
        v9 = m;
        break;
      }
    }
  }
  CNetIndexList::~CNetIndexList(&v4);
}
