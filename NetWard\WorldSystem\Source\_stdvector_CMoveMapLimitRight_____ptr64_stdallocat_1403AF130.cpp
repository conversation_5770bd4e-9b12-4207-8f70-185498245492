#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::insert_::_1_::dtor$4
 * Address: 0x1403AF130

void  std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::insert_::_1_::dtor_4(int64_t a1, int64_t a2)
{
  if ( *(uint32_t*)(a2 + 120) & 2 )
  {
    *(uint32_t*)(a2 + 120) &= 0xFFFFFFFD;
    std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(*(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 216));
  }
}
