#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_Insert_n@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAXV?$_Vector_iterator@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@2@_KAEBVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403B0D30

void  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Insert_n(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *_Where, unsigned int64_t _Count, CMoveMapLimitRightInfo *_Val)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  unsigned int64_t v6; // rax@5
  unsigned int64_t v7; // rax@7
  unsigned int64_t v8; // rax@8
  unsigned int64_t v9; // rax@11
  int64_t v10; // [sp+0h] [bp-B8h]@1
  CMoveMapLimitRightInfo _Vala; // [sp+28h] [bp-90h]@4
  unsigned int64_t _Counta; // [sp+58h] [bp-60h]@4
  CMoveMapLimitRightInfo *_Ptr; // [sp+60h] [bp-58h]@13
  CMoveMapLimitRightInfo *v14; // [sp+68h] [bp-50h]@13
  CMoveMapLimitRightInfo *_Last; // [sp+70h] [bp-48h]@18
  int64_t v16; // [sp+78h] [bp-40h]@4
  unsigned int64_t v17; // [sp+80h] [bp-38h]@5
  unsigned int64_t v18; // [sp+88h] [bp-30h]@8
  unsigned int64_t v19; // [sp+90h] [bp-28h]@9
  CMoveMapLimitRightInfo *v20; // [sp+98h] [bp-20h]@13
  CMoveMapLimitRightInfo *v21; // [sp+A0h] [bp-18h]@13
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v22; // [sp+C0h] [bp+8h]@1
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v23; // [sp+C8h] [bp+10h]@1
  unsigned int64_t v24; // [sp+D0h] [bp+18h]@1
  unsigned int64_t v25; // [sp+D0h] [bp+18h]@13

  v24 = _Count;
  v23 = _Where;
  v22 = this;
  v4 = &v10;
  for ( i = 44i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v16 = -2i64;
  CMoveMapLimitRightInfo::CMoveMapLimitRightInfo(&_Vala, _Val);
  _Counta = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::capacity(v22);
  if ( v24 )
  {
    v17 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::size(v22);
    v6 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::max_size(v22);
    if ( v6 - v17 < v24 )
      std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Xlen();
    v7 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::size(v22);
    if ( _Counta >= v24 + v7 )
    {
      if ( (unsigned int)((char *)v22->_Mylast - (char *)v23->_Myptr) / 40i64 >= v24 )
      {
        _Last = v22->_Mylast;
        v22->_Mylast = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Umove<CMoveMapLimitRightInfo *>(
                         v22,
                         &_Last[-v24],
                         _Last,
                         v22->_Mylast);
        stdext::_Unchecked_move_backward<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *>(
          v23->_Myptr,
          &_Last[-v24],
          _Last);
        std::fill<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo>(v23->_Myptr, &v23->_Myptr[v24], &_Vala);
      }
      else
      {
        std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Umove<CMoveMapLimitRightInfo *>(
          v22,
          v23->_Myptr,
          v22->_Mylast,
          &v23->_Myptr[v24]);
        std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Ufill(
          v22,
          v22->_Mylast,
          v24 - (unsigned int)((char *)v22->_Mylast - (char *)v23->_Myptr) / 40i64,
          &_Vala);
        v22->_Mylast += v24;
        std::fill<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo>(v23->_Myptr, &v22->_Mylast[-v24], &_Vala);
      }
    }
    else
    {
      v18 = _Counta / 2;
      v8 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::max_size(v22);
      if ( v8 - v18 >= _Counta )
        v19 = _Counta / 2 + _Counta;
      else
        v19 = 0i64;
      _Counta = v19;
      v9 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::size(v22);
      if ( _Counta < v24 + v9 )
        _Counta = v24 + std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::size(v22);
      _Ptr = std::allocator<CMoveMapLimitRightInfo>::allocate(&v22->_Alval, _Counta);
      v14 = _Ptr;
      v20 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Umove<CMoveMapLimitRightInfo *>(
              v22,
              v22->_Myfirst,
              v23->_Myptr,
              _Ptr);
      v14 = v20;
      v21 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Ufill(v22, v20, v24, &_Vala);
      v14 = v21;
      std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Umove<CMoveMapLimitRightInfo *>(
        v22,
        v23->_Myptr,
        v22->_Mylast,
        v21);
      v25 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::size(v22) + v24;
      if ( v22->_Myfirst )
      {
        std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Destroy(
          v22,
          v22->_Myfirst,
          v22->_Mylast);
        std::allocator<CMoveMapLimitRightInfo>::deallocate(
          &v22->_Alval,
          v22->_Myfirst,
          (unsigned int)((char *)v22->_Myend - (char *)v22->_Myfirst) / 40i64);
      }
      v22->_Myend = &_Ptr[_Counta];
      v22->_Mylast = &_Ptr[v25];
      v22->_Myfirst = _Ptr;
    }
  }
  CMoveMapLimitRightInfo::~CMoveMapLimitRightInfo(&_Vala);
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::~_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(v23);
}
