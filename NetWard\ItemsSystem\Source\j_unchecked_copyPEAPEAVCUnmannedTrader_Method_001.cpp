#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_copy@PEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@@stdext@@YAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@00@Z
 * Address: 0x140011F3B

CUnmannedTraderClassInfo ** stdext::unchecked_copy<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo **_Dest)
{
  return stdext::unchecked_copy<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *>(_First, _Last, _Dest);
}
