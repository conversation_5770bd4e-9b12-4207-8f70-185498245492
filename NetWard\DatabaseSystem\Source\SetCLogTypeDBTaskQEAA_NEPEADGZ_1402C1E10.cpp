#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Set@CLogTypeDBTask@@QEAA_NEPEADG@Z
 * Address: 0x1402C1E10

char  CLogTypeDBTask::Set(CLogTypeDBTask *this, char byQueryType, char *pcData, unsigned int16_t wSize)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@7
  int64_t v7; // [sp+0h] [bp-28h]@1
  CLogTypeDBTask *v8; // [sp+30h] [bp+8h]@1
  char v9; // [sp+38h] [bp+10h]@1

  v9 = byQueryType;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned int8_t)byQueryType < 3 && pcData && (signed int)wSize <= 1024 )
  {
    memcpy_0(v8->m_pcData, pcData, wSize);
    v8->m_eQueryType = (unsigned int8_t)v9;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
