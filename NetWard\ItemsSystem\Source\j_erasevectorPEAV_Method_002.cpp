#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?erase@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@2@V32@0@Z
 * Address: 0x140010FFF

std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > * std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::erase(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *result, std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *_First, std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *_Last)
{
  return std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::erase(
           this,
           result,
           _First,
           _Last);
}
