#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Push@_talk_crystal_matrial_combine_node@@QEAA_NPEAU_db_con@_STORAGE_LIST@@EE@Z
 * Address: 0x1404309C0

char  _talk_crystal_matrial_combine_node::Push(_talk_crystal_matrial_combine_node *this, _STORAGE_LIST::_db_con *pItem, char byUseCount, char byClientIndex)
{
  char result; // al@2

  if ( this->m_nMatrialCount < 24 )
  {
    this->m_matrialList[this->m_nMatrialCount].m_pMatrial = pItem;
    this->m_matrialList[this->m_nMatrialCount].m_byUseCount = byUseCount;
    this->m_matrialList[this->m_nMatrialCount].m_byClientIndex = byClientIndex;
    this->m_nMatrialOverlapCount += (unsigned int8_t)byUseCount;
    ++this->m_nMatrialCount;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
