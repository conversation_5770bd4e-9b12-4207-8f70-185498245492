#pragma once

#ifndef NETWARD_NETWORK_H
#define NETWARD_NETWORK_H

// NetWard Network System Header
// Generated from RF Online ZoneServerUD_x64 decompiled source

#include <windows.h>
#include <stdint.h>
#include <memory>
#include <vector>
#include <string>

// Forward declarations
struct CNetSocketVtbl;
struct CMsgData;
struct CMsgDataVtbl;
struct CNetProcessVtbl;

// Class definitions

// CNetSocketVtbl
struct CNetSocketVtbl
{
 void *( *__vecDelDtor)(CNetSocket *this, unsigned int);
};

// CMsgData
struct CMsgData
{
 CMsgDataVtbl *vfptr;
 int m_nObjNum;
 int m_nMaxBufNum;
 _message m_gmListHead;
 _message m_gmListTail;
 _message *m_gmBuf;
 _message m_gmListEmptyHead;
 _message m_gmListEmptyTail;
 CMyCriticalSection m_csList;
 CMyCriticalSection m_csEmpty;
};

// CMsgDataVtbl
struct CMsgDataVtbl
{
 void *( *__vecDelDtor)(CMsgData *this, unsigned int);
 void ( *ProcessMessage)(CMsgData *this, _message *);
};

// CNetProcessVtbl
struct CNetProcessVtbl
{
 void *( *__vecDelDtor)(CNetProcess *this, unsigned int);
};


#endif // NETWARD_NETWORK_H
