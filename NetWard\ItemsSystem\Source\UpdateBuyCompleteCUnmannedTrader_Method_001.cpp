#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?UpdateBuyComplete@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034E1C0

char  CUnmannedTraderController::UpdateBuyComplete(CUnmannedTraderController *this, char *pData)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-88h]@1
  _SYSTEMTIME *kCurTime; // [sp+20h] [bp-68h]@8
  unsigned int dwTax; // [sp+28h] [bp-60h]@11
  _SYSTEMTIME *v8; // [sp+30h] [bp-58h]@11
  char *v9; // [sp+40h] [bp-48h]@4
  char Dst; // [sp+58h] [bp-30h]@4
  int j; // [sp+74h] [bp-14h]@4
  int v12; // [sp+78h] [bp-10h]@7

  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t*)v2 = -*********;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v9 = pData;
  pData[9] = 1;
  memset_0(&Dst, 0, 0x10ui64);
  GetLocalTime((LPSYSTEMTIME)&Dst);
  for ( j = 0; j < (unsigned int8_t)v9[11]; ++j )
  {
    v9[16 * j + 12] = 0;
    v12 = (unsigned int8_t)v9[16 * j + 13];
    v12 -= 34;
    switch ( v12 )
    {
      case 47:
      case 56:
        kCurTime = (_SYSTEMTIME *)&Dst;
        if ( !CRFWorldDatabase::Update_UnmannedTraderItemState(
                pkDB,
                v9[10],
                *(uint32_t*)&v9[16 * j + 20],
                v9[16 * j + 24],
                (_SYSTEMTIME *)&Dst) )
        {
          v9[16 * j + 12] = 1;
          v9[9] = 0;
        }
        break;
      case 0:
      case 3:
      case 8:
      case 9:
      case 65:
      case 116:
      case 117:
        v8 = (_SYSTEMTIME *)&Dst;
        dwTax = 0;
        LODWORD(kCurTime) = 0;
        if ( !CRFWorldDatabase::Update_UnmannedTraderResutlInfo(
                pkDB,
                v9[10],
                *(uint32_t*)&v9[16 * j + 20],
                v9[16 * j + 24],
                0,
                0,
                (_SYSTEMTIME *)&Dst) )
        {
          v9[16 * j + 12] = 1;
          v9[9] = 0;
        }
        break;
      default:
        continue;
    }
  }
  return 0;
}
