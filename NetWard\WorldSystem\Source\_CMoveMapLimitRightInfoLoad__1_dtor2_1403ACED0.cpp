#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CMoveMapLimitRightInfo::Load_::_1_::dtor$2
 * Address: 0x1403ACED0

void  CMoveMapLimitRightInfo::Load_::_1_::dtor_2(int64_t a1, int64_t a2)
{
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>((std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)(a2 + 168));
}
