#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_CuttingTrans@CUserDB@@QEAA_NGG@Z
 * Address: 0x1401166A0

char  CUserDB::Update_CuttingTrans(CUserDB *this, unsigned int16_t wResItemIndex, unsigned int16_t wLeftAmt)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-38h]@1
  char v7; // [sp+20h] [bp-18h]@6
  int j; // [sp+24h] [bp-14h]@6
  int v9; // [sp+28h] [bp-10h]@4
  CUserDB *v10; // [sp+40h] [bp+8h]@1
  unsigned int16_t v11; // [sp+48h] [bp+10h]@1
  unsigned int16_t v12; // [sp+50h] [bp+18h]@1

  v12 = wLeftAmt;
  v11 = wResItemIndex;
  v10 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v9 = wResItemIndex;
  if ( wResItemIndex < GetMaxResKind() )
  {
    v7 = 0;
    for ( j = 0; j < 20; ++j )
    {
      if ( v10->m_AvatorData.dbCutting.m_List[j].Key.wItemIndex == v11 )
      {
        v10->m_AvatorData.dbCutting.m_List[j].dwDur = v12;
        if ( !v12 )
        {
          _CUTTING_DB_BASE::_LIST::Init(&v10->m_AvatorData.dbCutting.m_List[j]);
          --v10->m_AvatorData.dbCutting.m_byLeftNum;
        }
        v7 = 1;
        break;
      }
    }
    if ( v7 )
    {
      v10->m_bDataUpdate = 1;
      result = 1;
    }
    else
    {
      CLogFile::Write(&stru_1799C8E78, "%s:Update_TransRes(Idx:%d)", v10->m_aszAvatorName, v11);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_CuttingTrans(CODE) wResItemIndex (%d) => failed ",
      v10->m_aszAvatorName,
      v11);
    result = 0;
  }
  return result;
}
