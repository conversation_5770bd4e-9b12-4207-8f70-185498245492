#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$unchecked_uninitialized_fill_n@PEAPEAVCUnmannedTraderSubClassInfo@@_KPEAV1@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@stdext@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@_KAEBQEAV1@AEAV?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@Z
 * Address: 0x1403804E0

void  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderSubClassInfo * *,unsigned int64_t,CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(CUnmannedTraderSubClassInfo **_First, unsigned int64_t _Count, CUnmannedTraderSubClassInfo *const *_Val, std::allocator<CUnmannedTraderSubClassInfo *> *_Al)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+30h] [bp-18h]@4
  std::_Scalar_ptr_iterator_tag v8; // [sp+31h] [bp-17h]@4
  CUnmannedTraderSubClassInfo **__formal; // [sp+50h] [bp+8h]@1
  unsigned int64_t _Counta; // [sp+58h] [bp+10h]@1
  CUnmannedTraderSubClassInfo **_Vala; // [sp+60h] [bp+18h]@1
  std::allocator<CUnmannedTraderSubClassInfo *> *v12; // [sp+68h] [bp+20h]@1

  v12 = _Al;
  _Vala = (CUnmannedTraderSubClassInfo **)_Val;
  _Counta = _Count;
  __formal = _First;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  memset(&v7, 0, sizeof(v7));
  v8 = std::_Ptr_cat<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *>(&__formal, &__formal);
  std::_Uninit_fill_n<CUnmannedTraderSubClassInfo * *,unsigned int64_t,CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(
    __formal,
    _Counta,
    _Vala,
    v12,
    v8,
    v7);
}
