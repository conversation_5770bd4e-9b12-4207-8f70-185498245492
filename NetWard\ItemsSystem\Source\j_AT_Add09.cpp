#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?insert@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@2@V32@AEBQEAVTRC_AutoTrade@@@Z
 * Address: 0x140012553

std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > * std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::insert(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *result, std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *_Where, TRC_AutoTrade *const *_Val)
{
  return std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::insert(this, result, _Where, _Val);
}
