#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$fill@PEAPEAVCMoveMapLimitRight@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitRight@@0AEBQEAV1@@Z
 * Address: 0x14001335E

void  std::fill<CMoveMapLimitRight * *,CMoveMapLimitRight *>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight *const *_Val)
{
  std::fill<CMoveMapLimitRight * *,CMoveMapLimitRight *>(_First, _Last, _Val);
}
