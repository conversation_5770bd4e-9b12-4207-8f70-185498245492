#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?LogOut@CUnmannedTraderUserInfo@@QEAAXKPEAVCLogFile@@@Z
 * Address: 0x140353750

void  CUnmannedTraderUserInfo::LogOut(CUnmannedTraderUserInfo *this, unsigned int dwSerial, CLogFile *pkLogger)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  unsigned int v5; // eax@6
  int64_t v6; // [sp+0h] [bp-38h]@1
  unsigned int v7; // [sp+20h] [bp-18h]@6
  CUnmannedTraderUserInfo *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( v8->m_dwUserSerial != dwSerial && pkLogger )
  {
    v5 = v8->m_wInx;
    v7 = dwSerial;
    CLogFile::Write(
      pkLogger,
      "CUnmannedTraderUserInfo::LogOut( DWORD dwSerial )\r\n\t\tm_wInx(%u) m_dwSerial(%u) != dwSerial(%u)\r\n",
      v5,
      v8->m_dwUserSerial);
  }
  CUnmannedTraderUserInfo::Clear(v8);
  v8->m_eState = 0;
}
