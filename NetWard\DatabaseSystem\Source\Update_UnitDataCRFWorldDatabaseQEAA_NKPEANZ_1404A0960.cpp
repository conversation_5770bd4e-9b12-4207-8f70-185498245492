#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_UnitData@CRFWorldDatabase@@QEAA_NKPEAN@Z
 * Address: 0x1404A0960

bool  CRFWorldDatabase::Update_UnitData(CRFWorldDatabase *this, unsigned int dwSerial, long double *pUnitData)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-468h]@1
  int64_t v7; // [sp+20h] [bp-448h]@4
  char Dest; // [sp+40h] [bp-428h]@4
  unsigned int64_t v9; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+470h] [bp+8h]@1

  v10 = this;
  v3 = &v6;
  for ( i = 280i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v9 = (unsigned int64_t)&v6 ^ _security_cookie;
  v7 = *((uint64_t*)pUnitData + 1);
  sprintf(&Dest, "{ CALL pUpdate_UnitData (%u, %.0f, %.0f) }", dwSerial, *(uint64_t*)pUnitData);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &Dest, 1);
}
