#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Tidy@?$vector@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@IEAAXXZ
 * Address: 0x14001266B

void  std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Tidy(std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this)
{
  std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Tidy(this);
}
