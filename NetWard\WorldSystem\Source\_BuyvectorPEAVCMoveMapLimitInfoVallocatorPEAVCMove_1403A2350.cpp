#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_Buy@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAA_N_K@Z
 * Address: 0x1403A2350

char  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Buy(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, unsigned int64_t _Capacity)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v5; // rcx@6
  int64_t v6; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v7; // [sp+30h] [bp+8h]@1
  unsigned int64_t _Count; // [sp+38h] [bp+10h]@1

  _Count = _Capacity;
  v7 = this;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v7->_Myfirst = 0i64;
  v7->_Mylast = 0i64;
  v7->_Myend = 0i64;
  if ( _Capacity )
  {
    if ( std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::max_size(v7) < _Capacity )
      std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Xlen(v5);
    v7->_Myfirst = std::allocator<CMoveMapLimitInfo *>::allocate(&v7->_Alval, _Count);
    v7->_Mylast = v7->_Myfirst;
    v7->_Myend = &v7->_Myfirst[_Count];
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
