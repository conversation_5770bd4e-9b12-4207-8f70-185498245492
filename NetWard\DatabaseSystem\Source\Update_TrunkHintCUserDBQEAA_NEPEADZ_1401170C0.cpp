#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_TrunkHint@CUserDB@@QEAA_NEPEAD@Z
 * Address: 0x1401170C0

char  CUserDB::Update_TrunkHint(CUserDB *this, char byHintIndex, char *pwszHintAnswer)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-38h]@1
  _TRUNK_DB_BASE *v7; // [sp+20h] [bp-18h]@4
  CUserDB *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v7 = &v8->m_AvatorData.dbTrunk;
  v8->m_AvatorData.dbTrunk.byHintIndex = byHintIndex;
  strcpy_0(v7->wszHintAnswer, pwszHintAnswer);
  v8->m_bDataUpdate = 1;
  return 1;
}
