#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetProc@CLogTypeDBTaskPool@@QEAA_NPEAVCLogTypeDBTask@@AEAVCLogFile@@@Z
 * Address: 0x1402C2690

char  CLogTypeDBTaskPool::SetProc(CLogTypeDBTaskPool *this, CLogTypeDBTask *pTask, CLogFile *kLogger)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@6
  unsigned int v6; // eax@7
  int v7; // eax@8
  int64_t v8; // [sp+0h] [bp-38h]@1
  unsigned int v9; // [sp+20h] [bp-18h]@8
  CLogTypeDBTaskPool *v10; // [sp+40h] [bp+8h]@1
  CLogTypeDBTask *v11; // [sp+48h] [bp+10h]@1
  CLogFile *v12; // [sp+50h] [bp+18h]@1

  v12 = kLogger;
  v11 = pTask;
  v10 = this;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( v10->m_bInit && pTask )
  {
    v6 = CLogTypeDBTask::GetInx(pTask);
    if ( CNetIndexList::PushNode_Back(&v10->m_kInxProc, v6) )
    {
      CLogTypeDBTask::SetUse(v11);
      result = 1;
    }
    else
    {
      v9 = CLogTypeDBTask::GetInx(v11);
      v7 = CLogTypeDBTask::GetQueryType(v11);
      CLogFile::Write(
        v12,
        "CLogTypeDBTaskPool::SetProc(...) : QueryType(%d) m_kInxProc.PushNode_Back( pTask->GetInx()(%u) ) Fail!",
        (unsigned int)v7,
        v9);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
