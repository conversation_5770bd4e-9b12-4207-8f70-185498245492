#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?RequestCombineProcess@ItemCombineMgr@@QEAAEPEAU_combine_ex_item_request_clzo@@PEAU_combine_ex_item_result_zocl@@@Z
 * Address: 0x14000D427

char  ItemCombineMgr::RequestCombineProcess(ItemCombineMgr *this, _combine_ex_item_request_clzo *pRecv, _combine_ex_item_result_zocl *pSend)
{
  return ItemCombineMgr::RequestCombineProcess(this, pRecv, pSend);
}
