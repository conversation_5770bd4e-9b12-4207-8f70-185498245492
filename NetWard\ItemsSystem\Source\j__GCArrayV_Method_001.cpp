#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??_G?$CArray@VCLuaLooting_Novus_Item@@@US@@UEAAPEAXI@Z_0
 * Address: 0x14001344E

void * US::CArray<CLuaLooting_Novus_Item>::`scalar deleting destructor'(US::CArray<CLuaLooting_Novus_Item> *this, unsigned int a2)
{
  return US::CArray<CLuaLooting_Novus_Item>::`scalar deleting destructor'(this, a2);
}
