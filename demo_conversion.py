#!/usr/bin/env python3
"""
NetWard Conversion Demo
Demonstrates how C files are converted to C++ and linked with headers
"""

import os
import re
from pathlib import Path

def demo_conversion_process():
    """Demonstrate the C-to-CPP conversion process"""

    print("="*60)
    print("NETWARD C-TO-CPP CONVERSION DEMONSTRATION")
    print("="*60)

    # Example C file content (from your items folder)
    sample_c_content = '''/*
 * Function: ??0CItemStoreManager@@QEAA@XZ
 * Address: 0x140348020
 */

void __fastcall CItemStoreManager::CItemStoreManager(CItemStoreManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CItemStoreManager *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CMyTimer::CMyTimer(&v5->m_tmrCheckTime);
  CMyTimer::CMyTimer(&v5->m_tmrSaveTime);
  _qry_case_all_store_limit_item::_qry_case_all_store_limit_item(&v5->m_Sheet);
  v5->m_nInstanceItemStoreListNum = 0;
  v5->m_InstanceItemStoreList = 0i64;
  CRecordData::CRecordData(&v5->m_tblItemStore);
  v5->m_nMapItemStoreListNum = 0;
  v5->m_MapItemStoreList = 0i64;
}'''

    print("1. ORIGINAL C FILE CONTENT:")
    print("-" * 40)
    print(sample_c_content[:300] + "...")

    print("\n2. FUNCTION ANALYSIS:")
    print("-" * 40)

    # Extract function info
    func_match = re.search(r'/\*\s*Function:\s*(.+?)\s*\*/', sample_c_content)
    addr_match = re.search(r'/\*\s*Address:\s*(.+?)\s*\*/', sample_c_content)

    if func_match:
        mangled_name = func_match.group(1).strip()
        print(f"Mangled Name: {mangled_name}")

        # Parse class and method
        if '??0' in mangled_name:
            class_match = re.search(r'\?\?0([A-Za-z_][A-Za-z0-9_]*)@@', mangled_name)
            if class_match:
                class_name = class_match.group(1)
                method_name = class_name  # Constructor
                print(f"Class Name: {class_name}")
                print(f"Method Type: Constructor")
                print(f"Method Name: {method_name}()")

    if addr_match:
        address = addr_match.group(1).strip()
        print(f"Original Address: {address}")

    print("\n3. CONVERTED C++ CONTENT:")
    print("-" * 40)

    # Show converted content
    converted_content = convert_sample_content(sample_c_content)
    print(converted_content)

    print("\n4. FILE ORGANIZATION:")
    print("-" * 40)
    print("Original File: 0CItemStoreManagerQEAAXZ_140348020.c")
    print("Converted To: CItemStoreManager.cpp")
    print("Header Used: NetWardItems.h")
    print("Include Path: ../../Common/NetWardCore.h")

    print("\n5. COMPLETE CLASS FILE STRUCTURE:")
    print("-" * 40)
    show_complete_class_file()

    print("\n" + "="*60)
    print("CONVERSION PROCESS COMPLETE!")
    print("="*60)

def convert_sample_content(content):
    """Convert sample content to show the process"""
    lines = content.split('\n')
    converted_lines = []

    in_comment = False
    for line in lines:
        stripped = line.strip()

        # Skip IDA comments
        if stripped.startswith('/*') and ('Function:' in stripped or 'Address:' in stripped):
            in_comment = True
            continue
        if in_comment and stripped == '*/':
            in_comment = False
            continue
        if in_comment:
            continue

        # Convert types and calling conventions
        line = line.replace('__int64', 'int64_t')
        line = line.replace('__fastcall', '')
        line = re.sub(r'(\d+)i64', r'\1LL', line)
        line = re.sub(r'0i64', '0', line)

        converted_lines.append(line)

    return '\n'.join(converted_lines)

def show_complete_class_file():
    """Show what a complete class file would look like"""

    complete_file = '''// CItemStoreManager Implementation
// NetWard Server - RF Online Zone Server
// Generated from decompiled IDA Pro source

#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

// CItemStoreManager class implementation

// Function: ??0CItemStoreManager@@QEAA@XZ
// Address: 0x140348020
CItemStoreManager::CItemStoreManager()
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-38h]@1
  int64_t v4; // [sp+20h] [bp-18h]@4
  CItemStoreManager *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12LL; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4 = -2LL;
  CMyTimer::CMyTimer(&v5->m_tmrCheckTime);
  CMyTimer::CMyTimer(&v5->m_tmrSaveTime);
  _qry_case_all_store_limit_item::_qry_case_all_store_limit_item(&v5->m_Sheet);
  v5->m_nInstanceItemStoreListNum = 0;
  v5->m_InstanceItemStoreList = 0;
  CRecordData::CRecordData(&v5->m_tblItemStore);
  v5->m_nMapItemStoreListNum = 0;
  v5->m_MapItemStoreList = 0;
}

// Function: ??1CItemStoreManager@@QEAA@XZ
// Address: 0x140348170
CItemStoreManager::~CItemStoreManager()
{
  // Destructor implementation would be here
}

// Additional methods would be appended here...
'''

    print(complete_file)

if __name__ == "__main__":
    demo_conversion_process()