#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Select_Char_Complete@CUserDB@@QEAAXEPEAU_AVATOR_DATA@@PEA_NKKK1ENN_N1E@Z
 * Address: 0x1401129C0

void  CUserDB::Select_Char_Complete(CUserDB *this, char byRetCode, _AVATOR_DATA *pLoadData, bool *pbAddItem, unsigned int dwAddDalant, unsigned int dwAddGold, unsigned int dwCheckSum, bool *pbTrunkAddItem, char byTrunkOldSlot, long double dTrunkOldDalant, long double dTrunkOldGold, bool bCreateTrunkFree, bool *pbExtTrunkAddItem, char byExtTrunkOldSlot)
{
  int64_t *v14; // rdi@1
  signed int64_t i; // rcx@1
  unsigned int16_t v16; // ax@54
  unsigned int32_t v17; // eax@55
  unsigned int16_t v18; // ax@57
  int64_t v19; // [sp+0h] [bp-238h]@1
  char *pszLog; // [sp+20h] [bp-218h]@21
  char *pszFileName; // [sp+28h] [bp-210h]@55
  _CLID *v22; // [sp+30h] [bp-208h]@55
  DWORD v23; // [sp+40h] [bp-1F8h]@4
  char v24; // [sp+44h] [bp-1F4h]@6
  bool pDataUpdated; // [sp+54h] [bp-1E4h]@6
  unsigned int v26; // [sp+64h] [bp-1D4h]@7
  char v27; // [sp+68h] [bp-1D0h]@12
  int v28; // [sp+6Ch] [bp-1CCh]@13
  int j; // [sp+70h] [bp-1C8h]@13
  unsigned int8_t k; // [sp+74h] [bp-1C4h]@27
  unsigned int8_t l; // [sp+75h] [bp-1C3h]@33
  bool v32; // [sp+76h] [bp-1C2h]@46
  char Dest; // [sp+90h] [bp-1A8h]@53
  char Dst; // [sp+128h] [bp-110h]@54
  char v35; // [sp+130h] [bp-108h]@54
  char v36; // [sp+140h] [bp-F8h]@54
  unsigned int v37; // [sp+141h] [bp-F7h]@54
  char v38; // [sp+145h] [bp-F3h]@54
  char pbyType; // [sp+164h] [bp-D4h]@54
  char v40; // [sp+165h] [bp-D3h]@54
  void *v41; // [sp+178h] [bp-C0h]@55
  _socket *v42; // [sp+180h] [bp-B8h]@56
  _sel_char_result_zone v43; // [sp+198h] [bp-A0h]@57
  char v44; // [sp+1C4h] [bp-74h]@57
  char v45; // [sp+1C5h] [bp-73h]@57
  int __n[2]; // [sp+1E0h] [bp-58h]@7
  void *v47; // [sp+1E8h] [bp-50h]@10
  void *__t; // [sp+1F0h] [bp-48h]@7
  int64_t v49; // [sp+1F8h] [bp-40h]@4
  void *v50; // [sp+200h] [bp-38h]@8
  _CLID *v51; // [sp+208h] [bp-30h]@55
  size_t v52; // [sp+210h] [bp-28h]@55
  unsigned int8_t *v53; // [sp+218h] [bp-20h]@55
  unsigned int64_t v54; // [sp+220h] [bp-18h]@4
  CUserDB *pUser; // [sp+240h] [bp+8h]@1
  char v56; // [sp+248h] [bp+10h]@1
  _AVATOR_DATA *Src; // [sp+250h] [bp+18h]@1
  bool *v58; // [sp+258h] [bp+20h]@1

  v58 = pbAddItem;
  Src = pLoadData;
  v56 = byRetCode;
  pUser = this;
  v14 = &v19;
  for ( i = 140i64; i; --i )
  {
    *(uint32_t*)v14 = -858993460;
    v14 = (int64_t *)((char *)v14 + 4);
  }
  v49 = -2i64;
  v54 = (unsigned int64_t)&v19 ^ _security_cookie;
  pUser->m_bDBWaitState = 0;
  v23 = timeGetTime();
  pUser->m_dwOperLobbyTime = v23;
  pUser->m_bCreateTrunkFree = bCreateTrunkFree;
  if ( pUser->m_nTrans == 1 )
    pUser->m_bCreateTrunkFree = 1;
  v24 = -1;
  pDataUpdated = 0;
  if ( !v56 )
  {
    memcpy_0(&pUser->m_AvatorData, Src, 0x915Fui64);
    memcpy_0(&pUser->m_AvatorData_bk, Src, 0x915Fui64);
    v26 = *(&pdwCnt + (pUser->m_AvatorData_bk.dbAvator.m_byRaceSexCode >> 1));
    *(uint64_t*)__n = v26;
    __t = operator new[](saturated_mul(0x59ui64, v26));
    if ( __t )
    {
      `vector constructor iterator'(
        __t,
        0x59ui64,
        __n[0],
        (void *( *)(void *))_QUEST_DB_BASE::_START_NPC_QUEST_HISTORY::_START_NPC_QUEST_HISTORY);
      v50 = __t;
    }
    else
    {
      v50 = 0i64;
    }
    v47 = v50;
    pUser->m_AvatorData_bk.dbQuest.m_StartHistory = (_QUEST_DB_BASE::_START_NPC_QUEST_HISTORY *)v50;
    memcpy_0(pUser->m_AvatorData_bk.dbQuest.m_StartHistory, pUser->m_AvatorData.dbQuest.m_StartHistory, 89i64 * v26);
    pUser->m_AvatorData_bk.dbQuest.dwListCnt = pUser->m_AvatorData.dbQuest.dwListCnt;
    if ( Src->dbCutting.m_bOldDataLoad )
      _CUTTING_DB_BASE::Init(&pUser->m_AvatorData_bk.dbCutting);
    CUserDB::DataValidCheckRevise(&pUser->m_AvatorData, &pDataUpdated);
    v27 = 0;
    if ( v58 )
    {
      v28 = 20 * Src->dbAvator.m_byBagNum;
      for ( j = 0; j < v28; ++j )
      {
        if ( v58[j] )
        {
          _INVENKEY::SetRelease((_INVENKEY *)((char *)&pUser->m_AvatorData_bk.dbInven + 37 * j));
          v27 = 1;
        }
      }
    }
    if ( dwAddDalant )
    {
      if ( CanAddMoneyForMaxLimMoney(dwAddDalant, pUser->m_AvatorData.dbAvator.m_dwDalant) )
      {
        pUser->m_AvatorData.dbAvator.m_dwDalant += dwAddDalant;
        v27 = 1;
      }
      else
      {
        LODWORD(pszLog) = dwAddDalant;
        CLogFile::Write(
          &stru_1799C9718,
          "Push Dalant Fail >> Over Max : charserial( %d ), has( %d ), push( %d )",
          pUser->m_AvatorData.dbAvator.m_dwRecordNum,
          pUser->m_AvatorData.dbAvator.m_dwDalant);
      }
    }
    if ( dwAddGold )
    {
      if ( CanAddMoneyForMaxLimGold(dwAddGold, pUser->m_AvatorData.dbAvator.m_dwGold) )
      {
        pUser->m_AvatorData.dbAvator.m_dwGold += dwAddGold;
        v27 = 1;
      }
      else
      {
        LODWORD(pszLog) = dwAddGold;
        CLogFile::Write(
          &stru_1799C9718,
          "Push Gold Fail >> Over max : charserial( %d ), has( %d\t, push( %d\t)",
          pUser->m_AvatorData.dbAvator.m_dwRecordNum,
          pUser->m_AvatorData.dbAvator.m_dwGold);
      }
    }
    if ( pbTrunkAddItem )
    {
      for ( k = 0; k < (signed int)Src->dbTrunk.bySlotNum; ++k )
      {
        if ( pbTrunkAddItem[k] )
        {
          _INVENKEY::SetRelease(&pUser->m_AvatorData_bk.dbTrunk.m_List[k].Key);
          v27 = 1;
        }
      }
    }
    if ( pbExtTrunkAddItem )
    {
      for ( l = 0; l < (signed int)Src->dbTrunk.byExtSlotNum; ++l )
      {
        if ( pbExtTrunkAddItem[l] )
        {
          _INVENKEY::SetRelease(&pUser->m_AvatorData_bk.dbTrunk.m_ExtList[l].Key);
          v27 = 1;
        }
      }
    }
    if ( (unsigned int8_t)byTrunkOldSlot != pUser->m_AvatorData.dbTrunk.bySlotNum )
      v27 = 1;
    if ( dTrunkOldDalant != pUser->m_AvatorData.dbTrunk.dDalant )
      v27 = 1;
    if ( dTrunkOldGold != pUser->m_AvatorData.dbTrunk.dGold )
      v27 = 1;
    if ( (unsigned int8_t)byExtTrunkOldSlot != pUser->m_AvatorData.dbTrunk.byExtSlotNum )
      v27 = 1;
    pUser->m_AvatorData_bk.dbTrunk.bySlotNum = byTrunkOldSlot;
    pUser->m_AvatorData_bk.dbTrunk.dDalant = dTrunkOldDalant;
    pUser->m_AvatorData_bk.dbTrunk.dGold = dTrunkOldGold;
    pUser->m_AvatorData_bk.dbTrunk.byExtSlotNum = byExtTrunkOldSlot;
    strcpy_0(pUser->m_wszAvatorName, Src->dbAvator.m_wszAvatorName);
    W2M(Src->dbAvator.m_wszAvatorName, pUser->m_aszAvatorName, 0x11u);
    pUser->m_byNameLen = strlen_0(pUser->m_wszAvatorName);
    CMyTimer::TermTimeRun(&pUser->m_tmrCheckPlayMin);
    pUser->m_dwSerial = Src->dbAvator.m_dwRecordNum;
    v24 = Src->dbAvator.m_bySlotIndex;
    v32 = 0;
    if ( !pUser->m_AvatorData.dbAvator.m_byLevel )
    {
      v32 = 1;
      if ( !CUserDB::FirstSettingData(pUser) )
        CLogFile::Write(
          &stru_1799C8E78,
          "FirstSettingData() == false : char: %s, class: %s",
          pUser->m_aszAvatorName,
          pUser->m_AvatorData.dbAvator.m_szClassCode);
    }
    if ( v27 || pDataUpdated )
      CUserDB::UpdateContUserSave(pUser, 1);
    pUser->m_AvatorData.dbAvator.m_dwPvpRank = Src->dbAvator.m_dwPvpRank;
    pUser->m_AvatorData.dbAvator.m_bOverlapVote = Src->dbAvator.m_bOverlapVote;
    if ( !CPlayer::Load(&g_Player + pUser->m_idWorld.wIndex, pUser, v32) )
    {
      sprintf(&Dest, "CLOSE>> ɸ load , id:%s, char:%s", pUser->m_szAccountID, pUser->m_aszAvatorName);
      CNetworkEX::Close(&g_Network, 0, pUser->m_idWorld.wIndex, 0, &Dest);
      return;
    }
    memcpy_0(&Dst, &pUser->m_gidGlobal, 8ui64);
    strcpy_0(&v35, pUser->m_wszAvatorName);
    v36 = 0;
    v37 = pUser->m_dwSerial;
    v38 = Src->dbAvator.m_byLevel;
    pbyType = 1;
    v40 = 12;
    v16 = _select_avator_report_wrac::size((_select_avator_report_wrac *)&Dst);
    CNetProcess::LoadSendMsg(unk_1414F2090, 0, &pbyType, &Dst, v16);
    pUser->m_dwTermContSaveTime = v23;
    pUser->m_dwLastContSaveTime = v23;
    if ( unk_1414F20A8 )
    {
      v51 = &pUser->m_idWorld;
      v52 = strlen_0(pUser->m_wszAvatorName);
      v53 = (unsigned int8_t *)pUser->m_wszAvatorName;
      v17 = strlen_0(pUser->m_szAccountID);
      v22 = v51;
      LODWORD(pszFileName) = pUser->m_dwIP;
      LODWORD(pszLog) = v52;
      v41 = _CcrFG_rs_CreateUserContext(
              pUser->m_dwAccountSerial,
              (unsigned int8_t *)pUser->m_szAccountID,
              v17,
              v53,
              v52,
              (unsigned int32_t)pszFileName,
              v51);
      if ( v41 )
      {
        v42 = CNetWorking::GetSocket((CNetWorking *)&g_Network.vfptr, 0, pUser->m_idWorld.wIndex);
        v42->m_hFGContext = v41;
      }
    }
  }
  CMgrAccountLobbyHistory::sel_char_complete(
    &CUserDB::s_MgrLobbyHistory,
    v56,
    &pUser->m_AvatorData,
    dwAddDalant,
    dwAddGold,
    pUser->m_szLobbyHistoryFileName);
  v43.byRetCode = v56;
  v43.bySlotIndex = v24;
  v43.dwWorldSerial = pUser->m_AvatorData.dbAvator.m_dwRecordNum;
  v43.dwDalant = CPlayerDB::GetDalant((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * pUser->m_idWorld.wIndex));
  v43.dwGold = CPlayerDB::GetGold((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * pUser->m_idWorld.wIndex));
  v44 = 1;
  v45 = 15;
  v18 = _sel_char_result_zone::size(&v43);
  CNetProcess::LoadSendMsg(unk_1414F2088, pUser->m_idWorld.wIndex, &v44, &v43.byRetCode, v18);
}
