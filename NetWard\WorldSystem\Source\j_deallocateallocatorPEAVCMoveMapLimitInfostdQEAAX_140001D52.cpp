#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?deallocate@?$allocator@PEAVCMoveMapLimitInfo@@@std@@QEAAXPEAPEAVCMoveMapLimitInfo@@_K@Z
 * Address: 0x140001D52

void  std::allocator<CMoveMapLimitInfo *>::deallocate(std::allocator<CMoveMapLimitInfo *> *this, CMoveMapLimitInfo **_Ptr, unsigned int64_t __formal)
{
  std::allocator<CMoveMapLimitInfo *>::deallocate(this, _Ptr, __formal);
}
