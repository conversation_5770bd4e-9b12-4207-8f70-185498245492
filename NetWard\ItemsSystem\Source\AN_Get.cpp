// NetWard RF Online Server - Animus System
// File: AN_Get.cpp - Animus weapon adjustment calculation
// Compatible with Visual Studio 2022 (C++20)
// Original Function: CAnimus::GetWeaponAdjust
// Original Address: 0x14012CDA0

#pragma once
#include "../../Headers/Character/AnimusCore.h"
#include "../../Headers/Common/NetWardTypes.h"
#include "../../Headers/Data/RecordManager.h"
#include <memory>
#include <algorithm>

namespace NetWard::Character {

/**
 * @brief Calculates the weapon adjustment value for an Animus character
 * @return The weapon attack gap adjustment as a floating-point value
 *
 * @details This function retrieves the attack gap modifier from the character's
 *          record data. This value is used to adjust weapon damage calculations
 *          for Animus-type characters in the RF Online combat system.
 *
 * @note This function was reconstructed from decompiled RF Online server code.
 *       Original address: 0x14012CDA0
 *       Preserves the exact functionality of the original implementation.
 */
float32 AnimusCharacter::GetWeaponAdjustment() const noexcept
{
    // Validate that we have a valid record pointer
    if (!m_characterRecord) {
        return 0.0f;
    }

    // Return the attack gap value from the character record
    // This value represents the weapon damage adjustment for Animus characters
    return m_characterRecord->attackGapModifier;
}

/**
 * @brief Alternative implementation with additional safety checks
 * @return The weapon adjustment value, or default if invalid
 */
float32 AnimusCharacter::GetWeaponAdjustmentSafe() const noexcept
{
    try {
        // Additional validation for production use
        if (!m_characterRecord || !IsValidAnimus()) {
            return DEFAULT_ANIMUS_ATTACK_GAP;
        }

        const float32 adjustment = m_characterRecord->attackGapModifier;

        // Clamp to reasonable bounds to prevent exploits
        return std::clamp(adjustment, MIN_ATTACK_GAP, MAX_ATTACK_GAP);
    }
    catch (...) {
        // Fallback to default value on any exception
        return DEFAULT_ANIMUS_ATTACK_GAP;
    }
}

} // namespace NetWard::Character
