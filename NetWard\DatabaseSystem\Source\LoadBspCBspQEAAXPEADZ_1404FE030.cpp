#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?LoadBsp@CBsp@@QEAAXPEAD@Z
 * Address: 0x1404FE030

void  CBsp::LoadBsp(CBsp *this, char *a2)
{
  char *v2; // rbx@1
  CBsp *v3; // rdi@1
  FILE *v4; // rbp@3
  unsigned int v5; // esi@5
  int64_t v6; // rdx@7
  unsigned int v7; // er9@7
  unsigned int64_t v8; // r8@7
  int v9; // er12@7
  unsigned int v10; // ebx@7
  int v11; // er13@7
  int v12; // ebx@9
  int64_t v13; // rdx@9
  int v14; // ecx@9
  char *v15; // rax@9
  int v16; // ebx@9
  float (*v17)[3]; // rcx@9
  int v18; // ebx@9
  unsigned int *v19; // rcx@9
  int v20; // ebx@9
  _BSP_NODE *v21; // rcx@9
  int v22; // ebx@9
  _BSP_LEAF *v23; // rcx@9
  int v24; // ebx@9
  unsigned int16_t *v25; // rcx@9
  int v26; // ebx@9
  void *v27; // r12@9
  int v28; // ebx@9
  char *v29; // r13@9
  int v30; // ebx@9
  unsigned int16_t *v31; // rcx@9
  int v32; // ebx@9
  int32_t v33; // edx@9
  unsigned int *v34; // rcx@9
  signed int64_t v35; // r8@9
  char *v36; // r11@11
  unsigned int v37; // ebx@11
  int v38; // ecx@11
  unsigned int v39; // er13@11
  CParticle **v40; // rbp@11
  const signed int32_t *v41; // rbx@11
  char *v42; // r14@11
  signed int32_t v43; // eax@12
  char *v44; // r12@13
  void *v45; // rax@16
  CParticle *v46; // rax@17
  CParticle *v47; // r12@21
  void *v48; // rax@24
  CEntity *v49; // rax@25
  CParticle *v50; // r12@28
  int64_t v51; // rcx@32
  _ANI_OBJECT *v52; // rdx@33

  v2 = a2;
  v3 = this;
  if ( !(unsigned int)IsInitR3Engine() )
    Error("R3engine ʱȭ ϼ.", byte_140883769);
  SetMergeFileManager(0i64);
  v4 = fopen(v2, "rb");
  if ( !v4 )
    Error(v2, "<-  .");
  v5 = 0;
  v3->mNowCFaceId = 0;
  v3->mTotalAllocSize = 0;
  ResetTotalVertexBufferInfo();
  fread(&v3->mBSPHeader, 0x2ACui64, 1ui64, v4);
  if ( v3->mBSPHeader.version != 39 )
    Error("BSP  ʽ.", "");
  v6 = 5165088340638674453i64 * (unsigned __int128)v3->mBSPHeader.Leaf.size >> 64;
  v3->mLeafNum = (v6 + (((unsigned int64_t)v3->mBSPHeader.Leaf.size - v6) >> 1)) >> 4;
  v3->mNodeNum = (unsigned int64_t)(0x0AAAAAAAAAAAAAAABi64 * (unsigned __int128)v3->mBSPHeader.Node.size >> 64) >> 4;
  v7 = ((unsigned int64_t)(0x0AAAAAAAAAAAAAAABi64 * (unsigned __int128)v3->mBSPHeader.BVertex.size >> 64) >> 1)
     + ((unsigned int64_t)(0x0AAAAAAAAAAAAAAABi64 * (unsigned __int128)v3->mBSPHeader.WVertex.size >> 64) >> 2)
     + ((unsigned int64_t)(0x0AAAAAAAAAAAAAAABi64 * (unsigned __int128)v3->mBSPHeader.FVertex.size >> 64) >> 3);
  v3->mCVertexNum = v7;
  v8 = (unsigned int64_t)(0x0AAAAAAAAAAAAAAABi64 * (unsigned __int128)v3->mBSPHeader.Face.size >> 64) >> 2;
  v3->mCFaceNum = v8;
  v3->mObjectNum = (unsigned int64_t)(3353953467947191203i64 * (unsigned __int128)v3->mBSPHeader.Object.size >> 64) >> 4;
  v3->mMatGroupNum = v3->mBSPHeader.ReadMatGroup.size / 0;
  v9 = 12 * v7;
  v10 = v3->mBSPHeader.VertexId.size;
  v11 = 24 * v8;
  if ( (unsigned int)IsServerMode() )
  {
    v3->mCNEdgeNormal = (float (*)[4])Dmalloc(v3->mCFaceNum << 6);
    v3->mMapEntitiesListNum = 0;
    v3->mEntityIDNum = 0;
    v3->mEntityListNum = 0;
    v3->mLeafEntityListNum = 0;
  }
  v12 = v9 + v10;
  v13 = 0x08618618618618619i64 * (unsigned __int128)v3->mBSPHeader.ReadMatGroup.size >> 64;
  v14 = v11
      + v12
      + v3->mBSPHeader.CPlanes.size
      + v3->mBSPHeader.CFaceId.size
      + v3->mBSPHeader.Node.size
      + v3->mBSPHeader.Track.size
      + v3->mBSPHeader.Leaf.size
      + v3->mBSPHeader.LgtUV.size
      + v3->mBSPHeader.MatListInLeaf.size
      + v3->mBSPHeader.VertexColor.size
      + 361 * ((unsigned int64_t)(3353953467947191203i64 * (unsigned __int128)v3->mBSPHeader.Object.size >> 64) >> 4)
      + 2
      * (v3->mObjectNum
       + 29 * v3->mMapEntitiesListNum
       + 43 * ((v13 + (((unsigned int64_t)v3->mBSPHeader.ReadMatGroup.size - v13) >> 1)) >> 5));
  v3->mStaticAllocSize = v14;
  v3->mTotalAllocSize += v14;
  v15 = (char *)Dmalloc(v14);
  v3->mStaticAlloc = v15;
  v3->mCVertex = (float (*)[3])v15;
  v3->mCVertexId = (unsigned int *)&v15[v9];
  v3->mCFace = (_BSP_C_FACE *)&v15[v12];
  v16 = v11 + v12;
  v17 = (float (*)[3])&v15[v16];
  v3->mCNNormal = v17;
  fread(v17, v3->mBSPHeader.CPlanes.size, 1ui64, v4);
  v18 = v3->mBSPHeader.CPlanes.size + v16;
  v19 = (unsigned int *)&v3->mStaticAlloc[v18];
  v3->mCFaceId = v19;
  fread(v19, v3->mBSPHeader.CFaceId.size, 1ui64, v4);
  v20 = v3->mBSPHeader.CFaceId.size + v18;
  v21 = (_BSP_NODE *)&v3->mStaticAlloc[v20];
  v3->mNode = v21;
  fread(v21, v3->mBSPHeader.Node.size, 1ui64, v4);
  v22 = v3->mBSPHeader.Node.size + v20;
  v23 = (_BSP_LEAF *)&v3->mStaticAlloc[v22];
  v3->mLeaf = v23;
  fread(v23, v3->mBSPHeader.Leaf.size, 1ui64, v4);
  v24 = v3->mBSPHeader.Leaf.size + v22;
  v25 = (unsigned int16_t *)&v3->mStaticAlloc[v24];
  v3->MatListInLeafId = v25;
  fread(v25, v3->mBSPHeader.MatListInLeaf.size, 1ui64, v4);
  v26 = v3->mBSPHeader.MatListInLeaf.size + v24;
  v27 = Dmalloc(v3->mBSPHeader.Object.size);
  v3->mObject = (_ANI_OBJECT *)&v3->mStaticAlloc[v26];
  fread(v27, v3->mBSPHeader.Object.size, 1ui64, v4);
  v28 = 361 * ((unsigned int64_t)(3353953467947191203i64 * (unsigned __int128)v3->mBSPHeader.Object.size >> 64) >> 4)
      + v26;
  v29 = &v3->mStaticAlloc[v28];
  fread(v29, v3->mBSPHeader.Track.size, 1ui64, v4);
  v30 = v3->mBSPHeader.Track.size + v28;
  v31 = (unsigned int16_t *)&v3->mStaticAlloc[v30];
  v3->mEventObjectID = v31;
  fread(v31, v3->mBSPHeader.EventObjectID.size, 1ui64, v4);
  v32 = v30 + 2 * v3->mObjectNum;
  v33 = 0;
  v34 = &v3->mBSPHeader.ReadSpare[0].size;
  v35 = 35i64;
  do
  {
    v33 += *v34;
    v34 += 2;
    --v35;
  }
  while ( v35 );
  fseek(v4, v33, 1);
  ConvAniObject(
    (unsigned int64_t)(3353953467947191203i64 * (unsigned __int128)v3->mBSPHeader.Object.size >> 64) >> 4,
    (unsigned int8_t *)v29,
    (struct _READ_ANI_OBJECT *)v27,
    v3->mObject);
  Dfree(v27);
  v36 = v3->mStaticAlloc;
  v3->mMatGroup = (_BSP_MAT_GROUP *)&v36[v32];
  v37 = 86 * (v3->mBSPHeader.ReadMatGroup.size / 0) + v32;
  v3->mLgtUV = (int16_t (*)[2])&v36[v37];
  v3->mVertexColor = (unsigned int *)&v36[v3->mBSPHeader.LgtUV.size + v37];
  CBsp::ReadDynamicDataFillVertexBuffer(v3, v4);
  fclose(v4);
  v38 = v3->mBSPHeader.ReadMatGroup.size / 0 + 1;
  v3->mMatGroupCacheSize = v38;
  v3->mMatGroupCache = (char *)Dmalloc(v38);
  v3->mTotalAllocSize += v3->mMatGroupCacheSize;
  CAlpha::InitAlpha(&v3->mAlpha, v3);
  CMergeFileManager::InitMergeFile(&v3->mMapEntityMFM, byte_184A790F0);
  SetMergeFileManager(&v3->mMapEntityMFM);
  v39 = 0;
  v40 = (CParticle **)&v3->144;
  v41 = (const signed int32_t *)v3->mEnvID;
  v42 = (char *)(&unk_184A7999C - (_UNKNOWN *)v3);
  do
  {
    v43 = *(const signed int32_t *)((char *)v41 + (_QWORD)v42);
    *v41 = v43;
    if ( v43 )
    {
      v44 = &byte_184A79924[128 * (unsigned int64_t)v39];
      if ( (unsigned int)IsParticle(&byte_184A79924[128 * (unsigned int64_t)v39]) )
        *v41 |= 0x1000u;
      if ( _bittest(v41, 0xCu) )
      {
        v45 = operator new(0x490ui64);
        if ( v45 )
          LODWORD(v46) = CParticle::CParticle(v45);
        else
          v46 = 0i64;
        *v40 = v46;
        if ( (unsigned int)CParticle::LoadParticleSPT(v46, v44, 0) )
        {
          CParticle::InitParticle(*v40);
          CParticle::SetParticleState(*v40, 1u);
        }
        else
        {
          v47 = *v40;
          if ( *v40 )
          {
            CParticle::~CParticle(*v40);
            operator delete(v47);
          }
          *v41 = 0;
          *(const signed int32_t *)((char *)v41 + (_QWORD)v42) = 0;
        }
      }
      else
      {
        v48 = operator new(0xF4ui64);
        if ( v48 )
          LODWORD(v49) = CEntity::CEntity(v48);
        else
          v49 = 0i64;
        *v40 = (CParticle *)v49;
        if ( !(unsigned int)CEntity::LoadEntity(v49, v44, 0) )
        {
          v50 = *v40;
          if ( *v40 )
          {
            CEntity::~CEntity((CEntity *)*v40);
            operator delete(v50);
          }
        }
      }
    }
    ++v39;
    ++v41;
    ++v40;
  }
  while ( v39 < 2 );
  if ( v3->mObjectNum )
  {
    v51 = 0i64;
    do
    {
      v52 = v3->mObject;
      if ( v52[v51].parent >= v3->mObjectNum )
        v52[v51].parent = 0;
      ++v5;
      ++v51;
    }
    while ( v5 < v3->mObjectNum );
  }
}
