#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?QueryDoubleValue@TiXmlAttribute@@QEBAHPEAN@Z
 * Address: 0x14052D970

int64_t  TiXmlAttribute::QueryDoubleValue(TiXmlAttribute *this, double *a2)
{
  int v2; // eax@1
  signed int v3; // ecx@1

  v2 = sscanf(this->value.rep_->str, "%lf", a2);
  v3 = 2;
  if ( v2 == 1 )
    v3 = 0;
  return (unsigned int)v3;
}
