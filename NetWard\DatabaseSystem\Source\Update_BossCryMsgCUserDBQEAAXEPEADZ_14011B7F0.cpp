#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_BossCryMsg@CUserDB@@QEAAXEPEAD@Z
 * Address: 0x14011B7F0

void  CUserDB::Update_BossCryMsg(CUserDB *this, char bySlot, char *pwszCryMsg)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-28h]@1
  CUserDB *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  strcpy_0((char *)&v6->m_AvatorData.dbBossCry + 65 * (unsigned int8_t)bySlot, pwszCryMsg);
}
