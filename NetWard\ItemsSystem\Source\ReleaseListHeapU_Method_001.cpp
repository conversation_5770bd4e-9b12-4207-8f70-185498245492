#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Release@?$ListHeap@UCell@LendItemSheet@@@@QEAAXXZ
 * Address: 0x140310040

void  ListHeap<LendItemSheet::Cell>::Release(ListHeap<LendItemSheet::Cell> *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-48h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-24h]@4
  unsigned int64_t j; // [sp+38h] [bp-10h]@4
  ListHeap<LendItemSheet::Cell> *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  pdwOutIndex = 0;
  for ( j = 0i64;
        j < v6->_nMaxSize && CNetIndexList::PopNode_Front((CNetIndexList *)&v6->_listData.m_Head, &pdwOutIndex);
        ++j )
  {
    CNetIndexList::PushNode_Back((CNetIndexList *)&v6->_listEmpty.m_Head, pdwOutIndex);
  }
}
