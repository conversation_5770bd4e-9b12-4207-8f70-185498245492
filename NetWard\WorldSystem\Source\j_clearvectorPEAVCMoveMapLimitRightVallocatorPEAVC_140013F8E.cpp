#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?clear@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEAAXXZ
 * Address: 0x140013F8E

void  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::clear(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this)
{
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::clear(this);
}
