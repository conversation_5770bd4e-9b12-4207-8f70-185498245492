#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_fill_n@PEAPEAVCMoveMapLimitInfo@@_KPEAV1@@stdext@@YAXPEAPEAVCMoveMapLimitInfo@@_KAEBQEAV1@@Z
 * Address: 0x140007C61

void  stdext::unchecked_fill_n<CMoveMapLimitInfo * *,unsigned int64_t,CMoveMapLimitInfo *>(CMoveMapLimitInfo **_First, unsigned int64_t _Count, CMoveMapLimitInfo *const *_Val)
{
  stdext::unchecked_fill_n<CMoveMapLimitInfo * *,unsigned int64_t,CMoveMapLimitInfo *>(_First, _Count, _Val);
}
