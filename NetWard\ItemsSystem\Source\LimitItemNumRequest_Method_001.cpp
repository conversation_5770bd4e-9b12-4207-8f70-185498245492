#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?LimitItemNumRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D2F20

char  CNetworkEX::LimitItemNumRequest(CNetworkEX *this, int n, char *pBuf)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@6
  int64_t v6; // [sp+0h] [bp-38h]@1
  unsigned int *v7; // [sp+20h] [bp-18h]@4
  CPlayer *v8; // [sp+28h] [bp-10h]@4

  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v7 = (unsigned int *)pBuf;
  v8 = &g_Player + n;
  if ( v8->m_bOper && !v8->m_bCorpse )
  {
    CPlayer::pc_LimitItemNumRequest(v8, *v7);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
