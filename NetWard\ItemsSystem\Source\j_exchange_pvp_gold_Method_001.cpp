#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?exchange_pvp_gold@CMgrAvatorItemHistory@@QEAAXHKKKPEAD@Z
 * Address: 0x140007928

void  CMgrAvatorItemHistory::exchange_pvp_gold(CMgrAvatorItemHistory *this, int n, unsigned int dwPoint, unsigned int dwNewDalant, unsigned int dwNewGold, char *pszFileName)
{
  CMgrAvatorItemHistory::exchange_pvp_gold(this, n, dwPoint, dwNewDalant, dwNewGold, pszFileName);
}
