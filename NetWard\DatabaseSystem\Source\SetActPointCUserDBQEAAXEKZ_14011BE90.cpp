#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetActPoint@CUserDB@@QEAAXEK@Z
 * Address: 0x14011BE90

void  CUserDB::SetActPoint(CUserDB *this, char byCode, unsigned int dwLeftPoint)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-28h]@1
  CUserDB *v6; // [sp+30h] [bp+8h]@1
  signed int dwPoint; // [sp+40h] [bp+18h]@1

  dwPoint = dwLeftPoint;
  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( byCode == 2 )
  {
    if ( dwLeftPoint > 0xF423F )
      dwPoint = 999999;
  }
  else if ( dwLeftPoint > 0x98967F )
  {
    dwPoint = 9999999;
  }
  CUserDB::Update_User_Action_Point(v6, byCode, dwPoint);
}
