#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_PotionNextUseTime@CUserDB@@QEAAXEK@Z
 * Address: 0x14011BA90

void  CUserDB::Update_PotionNextUseTime(CUserDB *this, char byPotionClass, unsigned int dwNextUseTime)
{
  if ( (signed int)(unsigned int8_t)byPotionClass < 38 && dwNextUseTime != -1 )
    this->m_AvatorData.dbPotionNextUseTime.dwPotionNextUseTime[(unsigned int8_t)byPotionClass] = dwNextUseTime;
}
