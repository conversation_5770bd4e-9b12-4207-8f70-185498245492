#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$upvalue_@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@YAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAUlua_State@@@Z
 * Address: 0x14040A260

CLuaSignalReActor *( * lua_tinker::upvalue_<CLuaSignalReActor * (CMonster::*)(void)>(struct lua_State *L, int64_t a2, int a3))(CMonster *this)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-28h]@1
  lua_tinker::user2type<CLuaSignalReActor * ( CMonster::*)(void)> *v7; // [sp+30h] [bp+8h]@1

  v7 = (lua_tinker::user2type<CLuaSignalReActor * ( CMonster::*)(void)> *)L;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  return lua_tinker::user2type<CLuaSignalReActor * (CMonster::*)(void)>::invoke(v7, (struct lua_State *)0xFFFFD8ED, a3);
}
