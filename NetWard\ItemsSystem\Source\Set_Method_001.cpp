#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Set@CUnmannedTraderItemState@@QEAA_NE@Z
 * Address: 0x140352F80

char  CUnmannedTraderItemState::Set(CUnmannedTraderItemState *this, char byState)
{
  char result; // al@2

  if ( (signed int)(unsigned int8_t)byState < 14 )
  {
    this->m_eState = (unsigned int8_t)byState;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
