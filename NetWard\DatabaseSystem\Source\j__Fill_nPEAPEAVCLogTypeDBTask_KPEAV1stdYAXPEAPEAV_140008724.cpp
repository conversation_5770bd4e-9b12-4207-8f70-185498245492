#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Fill_n@PEAPEAVCLogTypeDBTask@@_KPEAV1@@std@@YAXPEAPEAVCLogTypeDBTask@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140008724

void  std::_Fill_n<CLogTypeDBTask * *,unsigned int64_t,CLogTypeDBTask *>(CLogTypeDBTask **_First, unsigned int64_t _Count, CLogTypeDBTask *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  std::_Fill_n<CLogTypeDBTask * *,unsigned int64_t,CLogTypeDBTask *>(_First, _Count, _Val, __formal);
}
