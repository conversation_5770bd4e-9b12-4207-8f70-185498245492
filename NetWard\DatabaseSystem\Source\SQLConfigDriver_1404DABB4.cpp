#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLConfigDriver
 * Address: 0x1404DABB4

int  SQLConfigDriver(HWND__ *hwndParent, unsigned int16_t fRequest, const char *lpszDriver, const char *lpszArgs, char *lpszMsg, unsigned int16_t cbMsgMax, unsigned int16_t *pcbMsgOut)
{
  HWND__ *v7; // rbp@1
  const char *v8; // rbx@1
  const char *v9; // rdi@1
  unsigned int16_t v10; // si@1
  int64_t ( *v11)(); // rax@1
  int result; // eax@2

  v7 = hwndParent;
  v8 = lpszArgs;
  v9 = lpszDriver;
  v10 = fRequest;
  v11 = ODBC___GetSetupProc("SQLConfigDriver");
  if ( v11 )
    result = ((int ( *)(HWND__ *, _QWORD, const char *, const char *))v11)(v7, v10, v9, v8);
  else
    result = 0;
  return result;
}
