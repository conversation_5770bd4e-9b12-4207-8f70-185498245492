#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?UseRadarItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB850

char  CNetworkEX::UseRadarItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  int64_t v7; // [sp+0h] [bp-38h]@1
  _STORAGE_POS_INDIV *pItem; // [sp+20h] [bp-18h]@4
  CPlayer *v9; // [sp+28h] [bp-10h]@4
  CNetworkEX *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  pItem = (_STORAGE_POS_INDIV *)pBuf;
  v9 = &g_Player + n;
  if ( !v9->m_bOper || v9->m_pmTrd.bDTradeMode || v9->m_bCorpse )
  {
    result = 1;
  }
  else if ( pItem->byStorageCode )
  {
    v6 = CPlayerDB::GetCharNameA(&v9->m_Param);
    CLogFile::Write(
      &v10->m_LogFile,
      "odd.. %s: UseRadarRequest()..  if(pRecv->Item.byStorageCode != _STORAGE_POS::INVEN)",
      v6);
    result = 0;
  }
  else
  {
    CPlayer::pc_UseRadarItem(v9, pItem, (unsigned int16_t *)&pItem[1]);
    result = 1;
  }
  return result;
}
