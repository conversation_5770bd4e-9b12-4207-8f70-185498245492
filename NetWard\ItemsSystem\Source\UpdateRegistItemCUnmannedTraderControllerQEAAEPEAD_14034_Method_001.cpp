#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?UpdateRegistItem@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034CDD0

char  CUnmannedTraderController::UpdateRegistItem(CUnmannedTraderController *this, char *pData)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-B8h]@1
  char *v6; // [sp+20h] [bp-98h]@4
  bool pbRecordInserted; // [sp+34h] [bp-84h]@4
  char v8; // [sp+44h] [bp-74h]@4
  _unmannedtrader_registsingleitem kInfo; // [sp+60h] [bp-58h]@5
  CUnmannedTraderController *v10; // [sp+C0h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v6 = pData;
  pbRecordInserted = 0;
  v8 = CUnmannedTraderController::GetEmptyRecordSerial(v10, (unsigned int *)pData + 5, &pbRecordInserted);
  if ( !v8 )
  {
    kInfo.byType = v6[25];
    kInfo.bySellTurm = v6[26];
    kInfo.byRace = v6[27];
    kInfo.dwOwnerSerial = *((uint32_t*)v6 + 7);
    kInfo.dwPrice = *((uint32_t*)v6 + 8);
    kInfo.byInveninx = v6[36];
    kInfo.dwK = *((uint32_t*)v6 + 10);
    kInfo.dwD = *((uint64_t*)v6 + 6);
    kInfo.dwU = *((uint32_t*)v6 + 14);
    kInfo.byLv = v6[60];
    kInfo.byGrade = v6[61];
    kInfo.byClass1 = v6[62];
    kInfo.byClass2 = v6[63];
    kInfo.byClass3 = v6[64];
    kInfo.dwT = *((uint32_t*)v6 + 17);
    kInfo.lnUID = *((uint64_t*)v6 + 9);
    kInfo.dwTax = *((uint32_t*)v6 + 20);
    if ( !CRFWorldDatabase::Regist_UnmannedTraderSingleItem(pkDB, *((uint32_t*)v6 + 5), &kInfo, pbRecordInserted) )
      return 24;
    v6[24] = pbRecordInserted;
  }
  return v8;
}
