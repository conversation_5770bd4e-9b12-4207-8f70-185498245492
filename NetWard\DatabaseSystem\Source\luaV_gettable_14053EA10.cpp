#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: luaV_gettable
 * Address: 0x14053EA10

int64_t  luaV_gettable(int64_t a1, int64_t a2, int64_t a3, int64_t a4)
{
  int64_t v4; // r13@1
  int64_t v5; // r14@1
  int64_t v6; // rbx@1
  int64_t v7; // r12@1
  signed int v8; // esi@1
  int64_t v9; // rdi@3
  int64_t v10; // rax@3
  int64_t v11; // rbp@3
  int64_t v12; // rcx@4
  int64_t v13; // rax@6
  int64_t v14; // r8@6
  int64_t v15; // r9@6
  int64_t v16; // rdi@6
  int64_t result; // rax@7
  int64_t v18; // rax@8

  v4 = a4;
  v5 = a3;
  v6 = a2;
  v7 = a1;
  v8 = 0;
  while ( 1 )
  {
    if ( *(uint32_t*)(v6 + 8) == 5 )
    {
      v9 = *(uint64_t*)v6;
      LODWORD(v10) = luaH_get(*(uint64_t*)v6, v5);
      v11 = v10;
      if ( *(uint32_t*)(v10 + 8)
        || (v12 = *(uint64_t*)(v9 + 16)) == 0
        || *(uint8_t*)(v12 + 10) & 1
        || (LODWORD(v13) = luaT_gettm(v12, 0, *(uint64_t*)(*(uint64_t*)(v7 + 32) + 296i64)), (v16 = v13) == 0) )
      {
        *(uint64_t*)v4 = *(uint64_t*)v11;
        result = *(uint32_t*)(v11 + 8);
        *(uint32_t*)(v4 + 8) = result;
        return result;
      }
    }
    else
    {
      LODWORD(v18) = luaT_gettmbyobj(v7, v6, 0);
      v16 = v18;
      if ( !*(uint32_t*)(v18 + 8) )
        luaG_typeerror(v7, v6, (int64_t)"index");
    }
    if ( *(uint32_t*)(v16 + 8) == 6 )
      break;
    ++v8;
    v6 = v16;
    if ( v8 >= 100 )
      luaG_runerror(v7, (int64_t)"loop in gettable", v14, v15);
  }
  return sub_14053E8D0(v7, v4, v16, v6, v5);
}
