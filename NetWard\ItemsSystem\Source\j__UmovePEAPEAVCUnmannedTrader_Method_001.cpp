#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Umove@PEAPEAVCUnmannedTraderClassInfo@@@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV2@00@Z
 * Address: 0x140002E87

CUnmannedTraderClassInfo ** std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Umove<CUnmannedTraderClassInfo * *>(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this, CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo **_Ptr)
{
  return std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Umove<CUnmannedTraderClassInfo * *>(
           this,
           _First,
           _Last,
           _Ptr);
}
