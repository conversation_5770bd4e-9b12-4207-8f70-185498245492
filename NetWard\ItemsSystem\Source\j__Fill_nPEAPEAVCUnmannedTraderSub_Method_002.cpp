#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Fill_n@PEAPEAVCUnmannedTraderSubClassInfo@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14001064F

void  std::_Fill_n<CUnmannedTraderSubClassInfo * *,unsigned int64_t,CUnmannedTraderSubClassInfo *,std::random_access_iterator_tag>(CUnmannedTraderSubClassInfo **_First, unsigned int64_t _Count, CUnmannedTraderSubClassInfo *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  std::_Fill_n<CUnmannedTraderSubClassInfo * *,unsigned int64_t,CUnmannedTraderSubClassInfo *,std::random_access_iterator_tag>(
    _First,
    _Count,
    _Val,
    __formal,
    a5);
}
