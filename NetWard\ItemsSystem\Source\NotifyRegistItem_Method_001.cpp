#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?NotifyRegistItem@CUnmannedTraderUserInfo@@AEAAXXZ
 * Address: 0x140358E50

void  CUnmannedTraderUserInfo::NotifyRegistItem(CUnmannedTraderUserInfo *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  CUnmannedTraderRegistItemInfo *v3; // rax@7
  CUnmannedTraderRegistItemInfo *v4; // rax@8
  CUnmannedTraderRegistItemInfo *v5; // rax@8
  CUnmannedTraderRegistItemInfo *v6; // rax@8
  CUnmannedTraderRegistItemInfo *v7; // rax@8
  CUnmannedTraderRegistItemInfo *v8; // rax@8
  CUnmannedTraderRegistItemInfo *v9; // rax@8
  CUnmannedTraderGroupItemInfoTable *v10; // rax@8
  unsigned int16_t v11; // ax@9
  int64_t v12; // [sp+0h] [bp-1C8h]@1
  char byDivision; // [sp+44h] [bp-184h]@4
  char v14; // [sp+64h] [bp-164h]@4
  char v15; // [sp+84h] [bp-144h]@4
  _unmannedtrader_Regist_item_inform_zocl v16; // [sp+B0h] [bp-118h]@4
  int j; // [sp+174h] [bp-54h]@4
  char pbyType; // [sp+184h] [bp-44h]@9
  char v19; // [sp+185h] [bp-43h]@9
  unsigned int *v20; // [sp+198h] [bp-30h]@8
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v21; // [sp+1A0h] [bp-28h]@8
  unsigned int16_t v22; // [sp+1A8h] [bp-20h]@8
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v23; // [sp+1B0h] [bp-18h]@8
  char v24; // [sp+1B8h] [bp-10h]@8
  CUnmannedTraderUserInfo *v25; // [sp+1D0h] [bp+8h]@1

  v25 = this;
  v1 = &v12;
  for ( i = 112i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  byDivision = -1;
  v14 = -1;
  v15 = -1;
  _unmannedtrader_Regist_item_inform_zocl::_unmannedtrader_Regist_item_inform_zocl(&v16);
  for ( j = 0; j < v25->m_byMaxRegistCnt; ++j )
  {
    v3 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
           &v25->m_vecRegistItemInfo,
           j);
    if ( CUnmannedTraderRegistItemInfo::IsRegist(v3) )
    {
      v4 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v25->m_vecRegistItemInfo,
             j);
      v16.List[(unsigned int8_t)v16.byNum].wItemSerial = CUnmannedTraderRegistItemInfo::GetItemSerial(v4);
      v5 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v25->m_vecRegistItemInfo,
             j);
      v16.List[(unsigned int8_t)v16.byNum].dwRegistSerial = CUnmannedTraderRegistItemInfo::GetRegistSerial(v5);
      v6 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v25->m_vecRegistItemInfo,
             j);
      v16.List[(unsigned int8_t)v16.byNum].dwPrice = CUnmannedTraderRegistItemInfo::GetPrice(v6);
      v7 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v25->m_vecRegistItemInfo,
             j);
      v16.List[(unsigned int8_t)v16.byNum].dwLeftSec = CUnmannedTraderRegistItemInfo::GetLeftSec(v7);
      v16.List[(unsigned int8_t)v16.byNum].dwListIndex = 0;
      v20 = &v16.List[(unsigned int8_t)v16.byNum].dwListIndex;
      v21 = &v25->m_vecRegistItemInfo;
      v8 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v25->m_vecRegistItemInfo,
             j);
      v22 = CUnmannedTraderRegistItemInfo::GetItemIndex(v8);
      v23 = &v25->m_vecRegistItemInfo;
      v9 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v25->m_vecRegistItemInfo,
             j);
      v24 = CUnmannedTraderRegistItemInfo::GetTableCode(v9);
      v10 = CUnmannedTraderGroupItemInfoTable::Instance();
      CUnmannedTraderGroupItemInfoTable::GetGroupID(v10, v24, v22, &byDivision, &v14, &v15, v20);
      ++v16.byNum;
    }
  }
  pbyType = 30;
  v19 = 25;
  v11 = _unmannedtrader_Regist_item_inform_zocl::size(&v16);
  CNetProcess::LoadSendMsg(unk_1414F2088, v25->m_wInx, &pbyType, &v16.byNum, v11);
}
