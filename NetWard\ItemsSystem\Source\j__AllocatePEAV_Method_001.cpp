#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Allocate@PEAVCUnmannedTraderDivisionInfo@@@std@@YAPEAPEAVCUnmannedTraderDivisionInfo@@_KPEAPEAV1@@Z
 * Address: 0x14001061D

CUnmannedTraderDivisionInfo ** std::_Allocate<CUnmannedTraderDivisionInfo *>(unsigned int64_t _Count, CUnmannedTraderDivisionInfo **__formal)
{
  return std::_Allocate<CUnmannedTraderDivisionInfo *>(_Count, __formal);
}
