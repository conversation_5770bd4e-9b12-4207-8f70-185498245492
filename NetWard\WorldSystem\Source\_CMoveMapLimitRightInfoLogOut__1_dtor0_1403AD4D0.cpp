#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CMoveMapLimitRightInfo::LogOut_::_1_::dtor$0
 * Address: 0x1403AD4D0

void  CMoveMapLimitRightInfo::LogOut_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>((std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)(a2 + 144));
}
