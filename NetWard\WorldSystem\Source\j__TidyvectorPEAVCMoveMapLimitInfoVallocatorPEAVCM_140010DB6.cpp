#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Tidy@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAAXXZ
 * Address: 0x140010DB6

void  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Tidy(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Tidy(this);
}
