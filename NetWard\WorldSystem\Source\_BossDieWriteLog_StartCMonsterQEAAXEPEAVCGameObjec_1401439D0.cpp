#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_BossDieWriteLog_Start@CMonster@@QEAAXEPEAVCGameObject@@@Z
 * Address: 0x1401439D0

void  CMonster::_BossDieWriteLog_Start(CMonster *this, char byDestroyCode, CGameObject *pAttObj)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  signed int v5; // ecx@5
  signed int v6; // edx@5
  char *v7; // r8@5
  signed int64_t v8; // r9@5
  signed int64_t v9; // r10@5
  int v10; // eax@6
  int v11; // ecx@6
  int v12; // edx@6
  int v13; // er8@6
  int v14; // er9@6
  signed int64_t v15; // r10@6
  signed int64_t v16; // r11@6
  char *v17; // rax@18
  char *v18; // rax@23
  int64_t v19; // [sp+0h] [bp-6F8h]@1
  char *v20; // [sp+20h] [bp-6D8h]@5
  int v21; // [sp+28h] [bp-6D0h]@5
  int v22; // [sp+30h] [bp-6C8h]@5
  int v23; // [sp+38h] [bp-6C0h]@5
  int v24; // [sp+40h] [bp-6B8h]@6
  int v25; // [sp+48h] [bp-6B0h]@6
  int v26; // [sp+50h] [bp-6A8h]@6
  int v27; // [sp+58h] [bp-6A0h]@6
  char *v28; // [sp+60h] [bp-698h]@6
  int v29; // [sp+68h] [bp-690h]@6
  int v30; // [sp+70h] [bp-688h]@6
  int v31; // [sp+78h] [bp-680h]@6
  int j; // [sp+80h] [bp-678h]@7
  CLootingMgr::_list *v33; // [sp+88h] [bp-670h]@10
  double v34; // [sp+90h] [bp-668h]@11
  char Dest; // [sp+B0h] [bp-648h]@11
  CPlayer *v36; // [sp+138h] [bp-5C0h]@23
  CPartyPlayer **v37; // [sp+140h] [bp-5B8h]@24
  char v38; // [sp+160h] [bp-598h]@25
  int k; // [sp+664h] [bp-94h]@25
  CPlayer *v40; // [sp+668h] [bp-90h]@28
  char Source; // [sp+678h] [bp-80h]@28
  int v42; // [sp+6B0h] [bp-48h]@6
  int v43; // [sp+6B4h] [bp-44h]@6
  int v44; // [sp+6B8h] [bp-40h]@6
  char *v45; // [sp+6C0h] [bp-38h]@6
  int v46; // [sp+6C8h] [bp-30h]@6
  int v47; // [sp+6CCh] [bp-2Ch]@6
  int v48; // [sp+6D0h] [bp-28h]@6
  DWORD v49; // [sp+6D4h] [bp-24h]@23
  unsigned int64_t v50; // [sp+6D8h] [bp-20h]@4
  CMonster *v51; // [sp+700h] [bp+8h]@1
  char v52; // [sp+708h] [bp+10h]@1
  CPlayer *v53; // [sp+710h] [bp+18h]@1

  v53 = (CPlayer *)pAttObj;
  v52 = byDestroyCode;
  v51 = this;
  v3 = &v19;
  for ( i = 442i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v50 = (unsigned int64_t)&v19 ^ _security_cookie;
  if ( v51->m_pMonRec->m_bMonsterCondition == 1 )
  {
    v5 = (signed int)ffloor(v51->m_fCurPos[1]);
    v6 = (signed int)ffloor(v51->m_fCurPos[0]);
    v7 = v51->m_pCurMap->m_pMapSet->m_strCode;
    v8 = (signed int64_t)v51->m_pMonRec->m_strName;
    v9 = (signed int64_t)v51->m_pMonRec->m_strCode;
    v23 = (signed int)ffloor(v51->m_fCurPos[2]);
    v22 = v5;
    v21 = v6;
    v20 = v7;
    CLogFile::Write(&CMonster::s_logTrace_Boss_BirthAndDeath, "Death %s >> %s Map(%s) Pos(%d, %d, %d)", v9, v8);
    if ( !v52 )
    {
      CLogFile::Write(&CMonster::s_logTrace_Boss_Looting, " ");
      v42 = (signed int)ffloor(v51->m_fCurPos[2]);
      v43 = (signed int)ffloor(v51->m_fCurPos[1]);
      v44 = (signed int)ffloor(v51->m_fCurPos[0]);
      v45 = v51->m_pCurMap->m_pMapSet->m_strCode;
      v46 = GetCurrentMin();
      v47 = GetCurrentHour();
      v48 = GetCurrentDay();
      v10 = GetCurrentMonth();
      v11 = v51->m_byCreateDate[3];
      v12 = v51->m_byCreateDate[2];
      v13 = v51->m_byCreateDate[1];
      v14 = v51->m_byCreateDate[0];
      v15 = (signed int64_t)v51->m_pMonRec->m_strName;
      v16 = (signed int64_t)v51->m_pMonRec->m_strCode;
      v31 = v42;
      v30 = v43;
      v29 = v44;
      v28 = v45;
      v27 = v46;
      v26 = v47;
      v25 = v48;
      v24 = v10;
      v23 = v11;
      v22 = v12;
      v21 = v13;
      LODWORD(v20) = v14;
      CLogFile::Write(
        &CMonster::s_logTrace_Boss_Looting,
        "%s >> %s CreatTime: %d/%d %d:%d DeadTime: %d/%d %d:%d (Map:%s, Pos:%d %d %d)",
        v16,
        v15);
      CLogFile::Write(&CMonster::s_logTrace_Boss_Looting, " ");
      if ( v53 )
      {
        for ( j = 0; j < v51->m_LootMgr.m_byUserNode; ++j )
        {
          v33 = &v51->m_LootMgr.m_AtterList[j];
          if ( v33->pAtter )
          {
            v34 = (double)(v33->dwDamage * v33->dwAttCount);
            v20 = *(char **)&v34;
            sprintf(&Dest, "%d * %d = %.0f", v33->dwAttCount, v33->dwDamage);
            if ( !j && v51->m_LootMgr.m_bFirst )
            {
              v34 = (double)(v33->dwDamage * v33->dwAttCount) * 1.2;
              v20 = *(char **)&v34;
              sprintf(&Dest, "%d * %d * 1.2 = %.0f", v33->dwAttCount, v33->dwDamage);
            }
            if ( v33->pAtter == &sPlayerDum )
            {
              CLogFile::Write(&CMonster::s_logTrace_Boss_Looting, "\t system guard tower : %s", &Dest);
            }
            else if ( v33->pAtter->m_bLive && v33->pAtter->m_dwObjSerial == v33->dwAtterSerial )
            {
              v17 = CPlayerDB::GetCharNameA(&v33->pAtter->m_Param);
              v20 = &Dest;
              CLogFile::Write(&CMonster::s_logTrace_Boss_Looting, "\t %s (%d) : %s", v17, v33->dwAtterSerial);
            }
            else
            {
              CLogFile::Write(
                &CMonster::s_logTrace_Boss_Looting,
                "\t disconnect!! (%d) : %s",
                v33->dwAtterSerial,
                &Dest);
            }
          }
        }
        CLogFile::Write(&CMonster::s_logTrace_Boss_Looting, " ");
        if ( v53 )
        {
          if ( v53 != &sPlayerDum )
          {
            v36 = v53;
            v49 = timeGetTime() / 0x3E8;
            v18 = CPlayerDB::GetCharNameA(&v36->m_Param);
            LODWORD(v20) = v49;
            CLogFile::Write(&CMonster::s_logTrace_Boss_Looting, "\t <<TAKER>> %s (%d) Sec: %d", v18, v36->m_dwObjSerial);
            if ( CPartyPlayer::IsPartyMode(v36->m_pPartyMgr) )
            {
              v37 = CPartyPlayer::GetPtrPartyMember(v36->m_pPartyMgr);
              if ( v37 )
              {
                sprintf(&v38, "<<PARTY>> ");
                for ( k = 0; k < 8 && v37[k]; ++k )
                {
                  v40 = &g_Player + v37[k]->m_wZoneIndex;
                  sprintf(&Source, " %d ", v40->m_dwObjSerial);
                  strcat_0(&v38, &Source);
                }
                CLogFile::Write(&CMonster::s_logTrace_Boss_Looting, "\t %s", &v38);
              }
            }
          }
        }
      }
      else
      {
        CLogFile::Write(&CMonster::s_logTrace_Boss_Looting, "\t NOBODY LOOT");
      }
      CLogFile::Write(&CMonster::s_logTrace_Boss_Looting, " ");
    }
  }
}
