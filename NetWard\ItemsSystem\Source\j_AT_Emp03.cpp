#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?empty@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEBA_NXZ
 * Address: 0x140007E9B

bool  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  return std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(this);
}
