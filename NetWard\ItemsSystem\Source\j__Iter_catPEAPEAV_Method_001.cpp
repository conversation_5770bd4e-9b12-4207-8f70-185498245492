#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Iter_cat@PEAPEAVCUnmannedTraderDivisionInfo@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCUnmannedTraderDivisionInfo@@@Z
 * Address: 0x14000F3C1

std::random_access_iterator_tag  std::_Iter_cat<CUnmannedTraderDivisionInfo * *>(CUnmannedTraderDivisionInfo **const *__formal)
{
  return std::_Iter_cat<CUnmannedTraderDivisionInfo * *>(__formal);
}
