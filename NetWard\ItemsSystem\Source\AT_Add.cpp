// NetWard RF Online Server - AutoTrader System
// File: AT_Add.cpp - AutoTrader Guild Dalant Addition
// Compatible with Visual Studio 2022 (C++20)
// Original Function: TRC_AutoTrade::AddGDalant
// Original Address: 0x1402D85C0

#pragma once
#include "../../Headers/AutoTrader/AutoTraderCore.h"
#include "../../Headers/Common/NetWardTypes.h"
#include "../../Headers/Guild/GuildManager.h"
#include "../../Headers/Logging/LogManager.h"
#include <memory>
#include <cstring>

namespace NetWard::AutoTrader {

/**
 * @brief Adds Dalant (guild currency) to the AutoTrader system
 * @param tradeData Pointer to the trade data containing guild and amount information
 *
 * @details This function processes guild Dalant additions from AutoTrader transactions.
 *          It validates the guild ownership, updates the guild's total Dalant,
 *          sends notifications to guild members, and logs the transaction.
 *
 * @note Original address: 0x1402D85C0
 *       This handles the RF Online guild currency system for automated trading.
 */
void AutoTraderController::AddGuildDalant(const uint8_t* tradeData) noexcept
{
    // Input validation
    if (!tradeData) {
        LogManager::WriteError("AutoTrader", "AddGuildDalant called with null trade data");
        return;
    }

    // Parse trade data structure
    // Offset 4: Guild Serial (uint32_t)
    // Offset 16: Total Dalant Amount (double)
    // Offset 12: Transaction Amount (int32_t)
    // Offset 32: Date string

    const uint32_t guildSerial = *reinterpret_cast<const uint32_t*>(tradeData + 4);
    const double totalDalant = *reinterpret_cast<const double*>(tradeData + 16);
    const int32_t transactionAmount = *reinterpret_cast<const int32_t*>(tradeData + 12);
    const char* dateString = reinterpret_cast<const char*>(tradeData + 32);

    // Validate guild ownership
    if (!m_ownerGuild || m_ownerGuild->GetGuildSerial() != guildSerial) {
        LogManager::WriteError("AutoTrader",
            "Failed AddGuildDalant - Guild mismatch. Expected: {}, Received: {}, Seller: {}, Amount: {}",
            m_ownerGuild ? m_ownerGuild->GetGuildSerial() : 0,
            guildSerial,
            *reinterpret_cast<const uint32_t*>(tradeData + 8),
            transactionAmount);
        return;
    }

    // Update guild's total Dalant
    m_ownerGuild->SetTotalDalant(totalDalant);

    // Notify all guild members of the update
    m_ownerGuild->BroadcastMemberUpdate();

    // Set money output type for guild transactions
    m_ownerGuild->SetMoneyOutputType(GuildMoneyOutputType::AutoTradeTax);

    // Send guild money I/O message
    const double transactionAmountDouble = static_cast<double>(transactionAmount);
    m_ownerGuild->SendMoneyIOMessage(
        guildSerial,
        transactionAmountDouble,
        0.0,  // No additional fees
        true, // Is input transaction
        dateString
    );

    // Log the transaction in guild history
    GuildHistoryManager::AddMoneyTransaction(
        "Auto Trade Tax",
        guildSerial,
        transactionAmount,
        0,  // No additional data
        totalDalant,
        transactionAmountDouble,
        m_ownerGuild->GetHistoryFileName()
    );

    LogManager::WriteInfo("AutoTrader",
        "Successfully added {} Dalant to guild {}. New total: {}",
        transactionAmount, guildSerial, totalDalant);
}

} // namespace NetWard::AutoTrader
