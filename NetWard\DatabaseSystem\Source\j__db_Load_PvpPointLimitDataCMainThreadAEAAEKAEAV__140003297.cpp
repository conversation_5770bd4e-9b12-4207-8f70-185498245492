#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_db_Load_PvpPointLimitData@CMainThread@@AEAAEKAEAV_PVPPOINT_LIMIT_DB_BASE@@@Z
 * Address: 0x140003297

char  CMainThread::_db_Load_PvpPointLimitData(CMainThread *this, unsigned int dwSerial, _PVPPOINT_LIMIT_DB_BASE *kData)
{
  return CMainThread::_db_Load_PvpPointLimitData(this, dwSerial, kData);
}
