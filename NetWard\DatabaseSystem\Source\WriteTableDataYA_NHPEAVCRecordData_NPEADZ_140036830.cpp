#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?WriteTableData@@YA_NHPEAVCRecordData@@_NPEAD@Z
 * Address: 0x140036830

char  WriteTableData(int nItemNum, CRecordData *pItemData, bool bUseHash, char *szErrCode)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@7
  int64_t v7; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@12
  __list *v9; // [sp+28h] [bp-20h]@14
  CRecordData *v10; // [sp+30h] [bp-18h]@14
  int v11; // [sp+50h] [bp+8h]@1
  CRecordData *v12; // [sp+58h] [bp+10h]@1
  bool v13; // [sp+60h] [bp+18h]@1
  char *Dest; // [sp+68h] [bp+20h]@1

  Dest = szErrCode;
  v13 = bUseHash;
  v12 = pItemData;
  v11 = nItemNum;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( s_ptblItemData )
  {
    if ( szErrCode )
      sprintf(szErrCode, "ι° ȣ");
    result = 0;
  }
  else if ( v11 == 37 )
  {
    for ( j = 0; j < 37; ++j )
    {
      v9 = ItemDataFile;
      v10 = &v12[j];
      if ( !CRecordData::ReadRecord(v10, ItemDataFile[j].pfilename, ItemDataFile[j].nstructsize, Dest) )
        return 0;
      if ( v13 && !CRecordData::MakeHashTable(&v12[j], 2, 5, Dest) )
        return 0;
    }
    s_ptblItemData = v12;
    result = 1;
  }
  else
  {
    if ( szErrCode )
      sprintf(szErrCode, "nItemMax<%d> != item_tbl_num<%d>", (unsigned int)v11, 37i64);
    result = 0;
  }
  return result;
}
