#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: luaV_settable
 * Address: 0x14053EB20

int  luaV_settable(int64_t a1, int64_t a2, int64_t a3, int64_t a4)
{
  int64_t v4; // r14@1
  int64_t v5; // r15@1
  int64_t v6; // rbx@1
  int64_t v7; // rsi@1
  signed int v8; // er12@1
  int64_t v9; // rbp@3
  int64_t v10; // rax@3
  int64_t v11; // r13@3
  int64_t v12; // rcx@4
  int64_t v13; // rax@6
  int64_t v14; // r8@6
  int64_t v15; // r9@6
  int64_t v16; // rdi@6
  int64_t v17; // rax@7
  int64_t v18; // rax@11

  v4 = a4;
  v5 = a3;
  v6 = a2;
  v7 = a1;
  v8 = 0;
  while ( *(uint32_t*)(v6 + 8) != 5 )
  {
    LODWORD(v18) = luaT_gettmbyobj(v7, v6, 1);
    v16 = v18;
    if ( !*(uint32_t*)(v18 + 8) )
      luaG_typeerror(v7, v6, (int64_t)"index");
LABEL_13:
    if ( *(uint32_t*)(v16 + 8) == 6 )
    {
      LODWORD(v17) = sub_14053E980(v7, v16, v6, v5, v4);
      return v17;
    }
    ++v8;
    v6 = v16;
    if ( v8 >= 100 )
      luaG_runerror(v7, (int64_t)"loop in settable", v14, v15);
  }
  v9 = *(uint64_t*)v6;
  LODWORD(v10) = luaH_set(v7, *(uint64_t*)v6, v5);
  v11 = v10;
  if ( !*(uint32_t*)(v10 + 8) )
  {
    v12 = *(uint64_t*)(v9 + 16);
    if ( v12 )
    {
      if ( !(*(uint8_t*)(v12 + 10) & 2) )
      {
        LODWORD(v13) = luaT_gettm(v12, 1, *(uint64_t*)(*(uint64_t*)(v7 + 32) + 304i64));
        v16 = v13;
        if ( v13 )
          goto LABEL_13;
      }
    }
  }
  *(uint64_t*)v11 = *(uint64_t*)v4;
  LODWORD(v17) = *(uint32_t*)(v4 + 8);
  *(uint32_t*)(v11 + 8) = v17;
  if ( *(uint32_t*)(v4 + 8) >= 4 )
  {
    v17 = *(uint64_t*)v4;
    if ( *(uint8_t*)(*(uint64_t*)v4 + 9i64) & 3 )
    {
      if ( *(uint8_t*)(v9 + 9) & 4 )
        LODWORD(v17) = luaC_barrierback(v7, v9);
    }
  }
  return v17;
}
