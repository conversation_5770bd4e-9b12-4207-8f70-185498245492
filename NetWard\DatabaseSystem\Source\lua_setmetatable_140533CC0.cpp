#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: lua_setmetatable
 * Address: 0x140533CC0

signed int64_t  lua_setmetatable(int64_t a1, signed int a2)
{
  int64_t v2; // rbx@1
  uint32_t*v3; // rax@1
  int64_t v4; // rdx@1
  uint32_t*v5; // r10@1
  int64_t v6; // rdx@2
  int64_t v7; // rax@4

  v2 = a1;
  v3 = sub_140532CD0(a1, a2);
  v4 = *(uint64_t*)(v2 + 16);
  v5 = v3;
  if ( *(uint32_t*)(v4 - 8) )
    v6 = *(uint64_t*)(v4 - 16);
  else
    v6 = 0i64;
  v7 = v3[2];
  if ( (_DWORD)v7 == 5 )
  {
    *(uint64_t*)(*(uint64_t*)v5 + 16i64) = v6;
    if ( v6 && *(uint8_t*)(v6 + 9) & 3 && *(uint8_t*)(*(uint64_t*)v5 + 9i64) & 4 )
      luaC_barrierback(v2);
  }
  else
  {
    if ( (_DWORD)v7 != 7 )
    {
      *(uint64_t*)(*(uint64_t*)(v2 + 32) + 8 * v7 + 224) = v6;
      *(uint64_t*)(v2 + 16) -= 16i64;
      return 1i64;
    }
    *(uint64_t*)(*(uint64_t*)v5 + 16i64) = v6;
    if ( v6 && *(uint8_t*)(v6 + 9) & 3 && *(uint8_t*)(*(uint64_t*)v5 + 9i64) & 4 )
    {
      luaC_barrierf(v2);
      *(uint64_t*)(v2 + 16) -= 16i64;
      return 1i64;
    }
  }
  *(uint64_t*)(v2 + 16) -= 16i64;
  return 1i64;
}
