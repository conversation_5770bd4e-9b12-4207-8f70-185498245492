#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Construct@VCUnmannedTraderItemCodeInfo@@V1@@std@@YAXPEAVCUnmannedTraderItemCodeInfo@@AEBV1@@Z
 * Address: 0x140006DED

void  std::_Construct<CUnmannedTraderItemCodeInfo,CUnmannedTraderItemCodeInfo>(CUnmannedTraderItemCodeInfo *_Ptr, CUnmannedTraderItemCodeInfo *_Val)
{
  std::_Construct<CUnmannedTraderItemCodeInfo,CUnmannedTraderItemCodeInfo>(_Ptr, _Val);
}
