#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?mc_ChangeMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x140276050

bool  mc_ChangeMonster(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  bool result; // al@5
  int64_t v6; // rax@17
  int64_t v7; // [sp+0h] [bp-258h]@1
  char poutszWord; // [sp+30h] [bp-228h]@4
  char szRecordCode; // [sp+D0h] [bp-188h]@6
  char Str[136]; // [sp+170h] [bp-E8h]@8
  _base_fld *v11; // [sp+1F8h] [bp-60h]@10
  _base_fld *v12; // [sp+200h] [bp-58h]@12
  int v13; // [sp+208h] [bp-50h]@14
  int v14; // [sp+20Ch] [bp-4Ch]@16
  int v15; // [sp+210h] [bp-48h]@16
  __change_monster *v16; // [sp+220h] [bp-38h]@19
  __change_monster *v17; // [sp+228h] [bp-30h]@16
  int64_t v18; // [sp+230h] [bp-28h]@4
  __change_monster *v19; // [sp+238h] [bp-20h]@17
  unsigned int64_t v20; // [sp+240h] [bp-18h]@4
  strFILE *fstra; // [sp+260h] [bp+8h]@1
  CDarkHoleDungeonQuestSetup *pSetupa; // [sp+268h] [bp+10h]@1

  pSetupa = pSetup;
  fstra = fstr;
  v3 = &v7;
  for ( i = 148i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v18 = -2i64;
  v20 = (unsigned int64_t)&v7 ^ _security_cookie;
  if ( strFILE::word(fstra, &poutszWord) )
  {
    if ( strFILE::word(fstra, &szRecordCode) )
    {
      if ( strFILE::word(fstra, Str) )
      {
        v11 = CRecordData::GetRecord(&stru_1799C6210, &poutszWord);
        if ( v11 )
        {
          v12 = CRecordData::GetRecord(&stru_1799C6210, &szRecordCode);
          if ( v12 )
          {
            v13 = strlen_0(Str);
            if ( Str[v13] == 37 )
              Str[v13] = 0;
            v14 = atoi(Str);
            v15 = pSetupa->m_pCurLoadMission->nChangeMonsterNum;
            v17 = (__change_monster *)operator new(0x28ui64);
            if ( v17 )
            {
              __change_monster::__change_monster(v17);
              v19 = (__change_monster *)v6;
            }
            else
            {
              v19 = 0i64;
            }
            v16 = v19;
            pSetupa->m_pCurLoadMission->pChangeMonster[v15] = v19;
            pSetupa->m_pCurLoadMission->pChangeMonster[v15]->pMonsterFldA = (_monster_fld *)v11;
            pSetupa->m_pCurLoadMission->pChangeMonster[v15]->pMonsterFldB = (_monster_fld *)v12;
            pSetupa->m_pCurLoadMission->pChangeMonster[v15]->nProb = v14;
            ++pSetupa->m_pCurLoadMission->nChangeMonsterNum;
            result = 1;
          }
          else
          {
            result = _false(fstra, pSetupa);
          }
        }
        else
        {
          result = _false(fstra, pSetupa);
        }
      }
      else
      {
        result = _false(fstra, pSetupa);
      }
    }
    else
    {
      result = _false(fstra, pSetupa);
    }
  }
  else
  {
    result = _false(fstra, pSetupa);
  }
  return result;
}
