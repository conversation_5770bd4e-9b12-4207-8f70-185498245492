#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Select_Char_Request@CUserDB@@QEAA_NE@Z
 * Address: 0x1401125E0

char  CUserDB::Select_Char_Request@<al>(CUserDB *this@<rcx>, char bySlotIndex@<dl>, signed int64_t a3@<rax>)
{
  void *v3; // rsp@1
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  unsigned int16_t v7; // ax@25
  int64_t v8; // [sp-20h] [bp-9378h]@1
  unsigned int16_t nLen[4]; // [sp+0h] [bp-9358h]@26
  int nSize; // [sp+8h] [bp-9350h]@26
  int j; // [sp+10h] [bp-9348h]@14
  CUserDB *v12; // [sp+18h] [bp-9340h]@17
  char v13; // [sp+20h] [bp-9338h]@21
  _sel_char_result_zone v14; // [sp+38h] [bp-9320h]@22
  char pbyType; // [sp+64h] [bp-92F4h]@25
  char v16; // [sp+65h] [bp-92F3h]@25
  _qry_sheet_load Dst; // [sp+90h] [bp-92C8h]@26
  char v18; // [sp+9330h] [bp-28h]@27
  char v19; // [sp+9331h] [bp-27h]@28
  int64_t v20; // [sp+9338h] [bp-20h]@4
  unsigned int64_t v21; // [sp+9340h] [bp-18h]@4
  CUserDB *v22; // [sp+9360h] [bp+8h]@1
  char v23; // [sp+9368h] [bp+10h]@1

  v23 = bySlotIndex;
  v22 = this;
  v3 = alloca(a3);
  v4 = &v8;
  for ( i = 9436i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v20 = -2i64;
  v21 = (unsigned int64_t)&v8 ^ _security_cookie;
  if ( v22->m_bActive )
  {
    if ( v22->m_ss.bReged )
    {
      if ( _SYNC_STATE::chk_select(&v22->m_ss) )
      {
        if ( v22->m_bDBWaitState )
        {
          result = 0;
        }
        else if ( v22->m_RegedList[(unsigned int8_t)v23].m_bySlotIndex == 255 )
        {
          result = 0;
        }
        else
        {
          for ( j = 0; j < 2532; ++j )
          {
            v12 = &g_UserDB[j];
            if ( v12->m_bActive && v22->m_RegedList[(unsigned int8_t)v23].m_dwRecordNum == v12->m_dwSerial )
              return 0;
          }
          v13 = CUserDB::IsExistRequestMoveCharacterList(v22, v22->m_RegedList[(unsigned int8_t)v23].m_dwRecordNum);
          if ( v13 )
          {
            v22->m_ss.bSelect = 0;
            v14.byRetCode = 0;
            memset(&v14.bySlotIndex, 0, 0xDui64);
            if ( v13 == 1 )
            {
              v14.byRetCode = 74;
            }
            else
            {
              v13 = 2;
              v14.byRetCode = 76;
            }
            pbyType = 1;
            v16 = 15;
            v7 = _sel_char_result_zone::size(&v14);
            CNetProcess::LoadSendMsg(unk_1414F2088, v22->m_idWorld.wIndex, &pbyType, &v14.byRetCode, v7);
            result = 1;
          }
          else
          {
            CMgrAccountLobbyHistory::sel_char_request(
              &CUserDB::s_MgrLobbyHistory,
              v23,
              v22->m_RegedList[(unsigned int8_t)v23].m_dwRecordNum,
              v22->m_szLobbyHistoryFileName);
            _qry_sheet_load::_qry_sheet_load(&Dst);
            Dst.dwAvatorSerial = v22->m_RegedList[(unsigned int8_t)v23].m_dwRecordNum;
            Dst.dwCheckSum = 0;
            memcpy_0(&Dst.LoadData, &v22->m_RegedList[(unsigned int8_t)v23], 0x10Dui64);
            nSize = _qry_sheet_load::size(&Dst);
            *(uint64_t*)nLen = &Dst;
            if ( CMainThread::PushDQSData(&g_Main, v22->m_dwAccountSerial, &v22->m_idWorld, 3, (char *)&Dst, nSize) )
            {
              v22->m_bDBWaitState = 1;
              v19 = 1;
              _qry_sheet_load::~_qry_sheet_load(&Dst);
              result = v19;
            }
            else
            {
              v18 = 0;
              _qry_sheet_load::~_qry_sheet_load(&Dst);
              result = v18;
            }
          }
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
