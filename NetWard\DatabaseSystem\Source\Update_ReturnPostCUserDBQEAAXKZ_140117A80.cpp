#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_ReturnPost@CUserDB@@QEAAXK@Z
 * Address: 0x140117A80

void  CUserDB::Update_ReturnPost(CUserDB *this, unsigned int dwSerial)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  bool *v4; // [sp+0h] [bp-18h]@1
  CUserDB *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v2 = (int64_t *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v4 = &v5->m_AvatorData.dbPostData.dbRetPost.m_bUpdate;
  v5->m_AvatorData.dbPostData.dbRetPost.m_RetSerials[v5->m_AvatorData.dbPostData.dbRetPost.m_nCum] = dwSerial;
  ++*(uint32_t*)(v4 + 5);
  *v4 = 1;
  v5->m_bDataUpdate = 1;
}
