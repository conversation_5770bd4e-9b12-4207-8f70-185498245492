#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::_Find_std::_Vector_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____CMoveMapLimitRight_____ptr64__::_1_::dtor$2
 * Address: 0x1403B26F0

void  std::_Find_std::_Vector_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____CMoveMapLimitRight_____ptr64__::_1_::dtor_2(int64_t a1, int64_t a2)
{
  if ( *(uint32_t*)(a2 + 32) & 1 )
  {
    *(uint32_t*)(a2 + 32) &= 0xFFFFFFFE;
    std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(*(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 64));
  }
}
