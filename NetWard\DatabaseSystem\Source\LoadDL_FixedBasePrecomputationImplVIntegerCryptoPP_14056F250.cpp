#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Load@?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@CryptoPP@@UEAAXAEBV?$DL_GroupPrecomputation@VInteger@CryptoPP@@@2@AEAVBufferedTransformation@2@@Z
 * Address: 0x14056F250

void  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::Load(int64_t a1, int64_t a2, struct CryptoPP::BufferedTransformation *a3)
{
  int64_t v3; // rax@3
  int64_t v4; // rax@6
  int64_t v5; // rax@6
  char v6; // [sp+30h] [bp-E8h]@1
  CryptoPP::BERGeneralDecoder v7; // [sp+40h] [bp-D8h]@1
  CryptoPP::Integer v8; // [sp+80h] [bp-98h]@3
  CryptoPP::Integer v9; // [sp+A8h] [bp-70h]@6
  int64_t v10; // [sp+D0h] [bp-48h]@1
  int64_t v11; // [sp+D8h] [bp-40h]@1
  int64_t v12; // [sp+E0h] [bp-38h]@3
  int64_t v13; // [sp+E8h] [bp-30h]@3
  int64_t v14; // [sp+F0h] [bp-28h]@6
  int64_t v15; // [sp+F8h] [bp-20h]@6
  int64_t v16; // [sp+100h] [bp-18h]@6
  int64_t v17; // [sp+120h] [bp+8h]@1
  int64_t v18; // [sp+128h] [bp+10h]@1

  v18 = a2;
  v17 = a1;
  v10 = -2i64;
  CryptoPP::BERSequenceDecoder::BERSequenceDecoder((CryptoPP::BERSequenceDecoder *)&v7, a3, 0x30u);
  CryptoPP::BERDecodeUnsigned<unsigned int>((CryptoPP *)&v7, (int *)&v6, 2u, 1u, 1u);
  v11 = *(uint64_t*)(v17 + 56);
  (*(void ( **)(signed int64_t, CryptoPP::BERGeneralDecoder *))(v11 + 8))(v17 + 56, &v7);
  *(uint32_t*)(v17 + 48) = CryptoPP::Integer::BitCount((CryptoPP::Integer *)(v17 + 56)) - 1;
  std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::clear(v17 + 96);
  while ( !CryptoPP::BERGeneralDecoder::EndReached(&v7) )
  {
    LODWORD(v3) = (*(int ( **)(int64_t, CryptoPP::Integer *, CryptoPP::BERGeneralDecoder *))(*(uint64_t*)v18 + 32i64))(
                    v18,
                    &v8,
                    &v7);
    v12 = v3;
    v13 = v3;
    std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::push_back(v17 + 96, v3);
    CryptoPP::Integer::~Integer(&v8);
  }
  if ( !std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::empty(v17 + 96)
    && (unsigned int8_t)(**(int ( ***)(_QWORD))v18)(v18) )
  {
    LODWORD(v4) = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator[](v17 + 96, 0i64);
    v14 = *(uint64_t*)v18;
    LODWORD(v5) = (*(int ( **)(int64_t, CryptoPP::Integer *, int64_t))(v14 + 16))(v18, &v9, v4);
    v15 = v5;
    v16 = v5;
    CryptoPP::Integer::operator=(v17 + 8);
    CryptoPP::Integer::~Integer(&v9);
  }
  CryptoPP::BERGeneralDecoder::MessageEnd(&v7);
  CryptoPP::BERSequenceDecoder::~BERSequenceDecoder((CryptoPP::BERSequenceDecoder *)&v7);
}
