<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>

  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{B2C3D4E5-F6G7-8901-BCDE-F23456789012}</ProjectGuid>
    <RootNamespace>AuthenticationSystem</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemGroup>
    <ClCompile Include="Source\0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAAEBV0_1403C5F40.cpp" />
    <ClCompile Include="Source\0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAXZ_1403C35F0.cpp" />
    <ClCompile Include="Source\0allocatorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C6C50.cpp" />
    <ClCompile Include="Source\0CAsyncLogInfoQEAAXZ_1403BC9F0.cpp" />
    <ClCompile Include="Source\0CBHPEAVCAsyncLogInfopairHPEAVCAsyncLogInfostdQEAA_1403C7590.cpp" />
    <ClCompile Include="Source\0hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHstd_1403C17E0.cpp" />
    <ClCompile Include="Source\0listUpairCBHPEAVCAsyncLogInfostdVallocatorUpairCB_1403C45B0.cpp" />
    <ClCompile Include="Source\0MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140465820.cpp" />
    <ClCompile Include="Source\0pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdV_1403C43F0.cpp" />
    <ClCompile Include="Source\0pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdQEAAAEBW4_1403C8010.cpp" />
    <ClCompile Include="Source\0UpairCBHPEAVCAsyncLogInfostdallocatorPEAU_Node_Li_1403C7E70.cpp" />
    <ClCompile Include="Source\0UpairCBHPEAVCAsyncLogInfostdallocatorU_Node_List__1403C7FF0.cpp" />
    <ClCompile Include="Source\0UpairCBHPEAVCAsyncLogInfostdallocatorV_Iterator0A_1403C7670.cpp" />
    <ClCompile Include="Source\0vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C4EF0.cpp" />
    <ClCompile Include="Source\0W4ASYNC_LOG_TYPEPEAVCAsyncLogInfopairCBHPEAVCAsyn_1403C7630.cpp" />
    <ClCompile Include="Source\0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C1560.cpp" />
    <ClCompile Include="Source\0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C5E20.cpp" />
    <ClCompile Include="Source\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C1480.cpp" />
    <ClCompile Include="Source\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C5B90.cpp" />
    <ClCompile Include="Source\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C6D10.cpp" />
    <ClCompile Include="Source\0_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compare_1403C2EC0.cpp" />
    <ClCompile Include="Source\0_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUless_1403C4520.cpp" />
    <ClCompile Include="Source\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2D20.cpp" />
    <ClCompile Include="Source\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C42C0.cpp" />
    <ClCompile Include="Source\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C5B30.cpp" />
    <ClCompile Include="Source\0_List_nodUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C7520.cpp" />
    <ClCompile Include="Source\0_List_ptrUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C6F70.cpp" />
    <ClCompile Include="Source\0_List_valUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C66F0.cpp" />
    <ClCompile Include="Source\0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C5EE0.cpp" />
    <ClCompile Include="Source\0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C74D0.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C5E70.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C73C0.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C5DC0.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6D70.cpp" />
    <ClCompile Include="Source\0_Vector_valV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C6BE0.cpp" />
    <ClCompile Include="Source\1CAsyncLogInfoQEAAXZ_1403BCA80.cpp" />
    <ClCompile Include="Source\1hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHstd_1403C1170.cpp" />
    <ClCompile Include="Source\1listUpairCBHPEAVCAsyncLogInfostdVallocatorUpairCB_1403C3630.cpp" />
    <ClCompile Include="Source\1MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140464F50.cpp" />
    <ClCompile Include="Source\1pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdV_1403C1670.cpp" />
    <ClCompile Include="Source\1vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C3EE0.cpp" />
    <ClCompile Include="Source\1_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C1230.cpp" />
    <ClCompile Include="Source\1_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C11F0.cpp" />
    <ClCompile Include="Source\1_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compare_1403C1860.cpp" />
    <ClCompile Include="Source\1_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C11B0.cpp" />
    <ClCompile Include="Source\1_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C44E0.cpp" />
    <ClCompile Include="Source\1_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C44A0.cpp" />
    <ClCompile Include="Source\1_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C4460.cpp" />
    <ClCompile Include="Source\4_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C2DF0.cpp" />
    <ClCompile Include="Source\4_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2D80.cpp" />
    <ClCompile Include="Source\4_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2CC0.cpp" />
    <ClCompile Include="Source\8UpairCBHPEAVCAsyncLogInfostdU01stdYA_NAEBVallocat_1403C7690.cpp" />
    <ClCompile Include="Source\8_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400A6CA0.cpp" />
    <ClCompile Include="Source\8_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2BE0.cpp" />
    <ClCompile Include="Source\8_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C7460.cpp" />
    <ClCompile Include="Source\9_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400CFE90.cpp" />
    <ClCompile Include="Source\9_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2C50.cpp" />
    <ClCompile Include="Source\9_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C6E30.cpp" />
    <ClCompile Include="Source\AccountServerLoginCMainThreadQEAAXXZ_1401F8140.cpp" />
    <ClCompile Include="Source\allocateallocatorU_Node_List_nodUpairCBHPEAVCAsync_1403C7000.cpp" />
    <ClCompile Include="Source\allocateallocatorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6CC0.cpp" />
    <ClCompile Include="Source\AuthLastCriTicketMiningTicketQEAAHGEEEEZ_1400D01D0.cpp" />
    <ClCompile Include="Source\AuthLastMentalTicketMiningTicketQEAAHGEEEEZ_1400CFDB0.cpp" />
    <ClCompile Include="Source\AuthMiningTicketCHolyStoneSystemQEAA_NIZ_14027DBD0.cpp" />
    <ClCompile Include="Source\auto_trade_login_sellCMgrAvatorItemHistoryQEAAXPEB_14023A3E0.cpp" />
    <ClCompile Include="Source\AvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C4290.cpp" />
    <ClCompile Include="Source\beginlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C3670.cpp" />
    <ClCompile Include="Source\beginvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C4F90.cpp" />
    <ClCompile Include="Source\begin_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_com_1403C1910.cpp" />
    <ClCompile Include="Source\CallFunc_RFOnline_AuthCEnglandBillingMgrQEAAHAEAU__1403198F0.cpp" />
    <ClCompile Include="Source\CallFunc_RFOnline_AuthCRusiaBillingMgrQEAAHAEAU_pa_1403213A0.cpp" />
    <ClCompile Include="Source\CallProc_RFOnlineAuthCRFCashItemDatabaseQEAAHAEAU__140482430.cpp" />
    <ClCompile Include="Source\CallProc_RFOnlineAuth_JapCRFCashItemDatabaseQEAAHA_1404836A0.cpp" />
    <ClCompile Include="Source\capacityvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C6920.cpp" />
    <ClCompile Include="Source\clearlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C6250.cpp" />
    <ClCompile Include="Source\CN_InvalidateNatureYAXXZ_140504ED0.cpp" />
    <ClCompile Include="Source\CompleteLogInCompeteCUnmannedTraderControllerQEAAX_14034EF80.cpp" />
    <ClCompile Include="Source\constructallocatorPEAU_Node_List_nodUpairCBHPEAVCA_1403C70A0.cpp" />
    <ClCompile Include="Source\constructallocatorUpairCBHPEAVCAsyncLogInfostdstdQ_1403C6EA0.cpp" />
    <ClCompile Include="Source\constructallocatorV_Iterator0AlistUpairCBHPEAVCAsy_1403C89B0.cpp" />
    <ClCompile Include="Source\C_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2AF0.cpp" />
    <ClCompile Include="Source\D3D_R3InvalidateDeviceYAJXZ_14050B040.cpp" />
    <ClCompile Include="Source\deallocateallocatorU_Node_List_nodUpairCBHPEAVCAsy_1403C6780.cpp" />
    <ClCompile Include="Source\deallocateallocatorV_Iterator0AlistUpairCBHPEAVCAs_1403C6C70.cpp" />
    <ClCompile Include="Source\destroyallocatorPEAU_Node_List_nodUpairCBHPEAVCAsy_1403C67D0.cpp" />
    <ClCompile Include="Source\destroyallocatorU_Node_List_nodUpairCBHPEAVCAsyncL_1403C7050.cpp" />
    <ClCompile Include="Source\destroyallocatorV_Iterator0AlistUpairCBHPEAVCAsync_1403C8A10.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_0_14057B120.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_10_14057B3A0.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_11_14057B3E0.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_12_14057B420.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_13_14057B460.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_14057B0E0.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_14_14057C3D0.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_15_14057C410.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_16_14057C450.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_17_14057C490.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_18_14057C4D0.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_19_14057C510.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_1_14057B160.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_20_14057C550.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_21_14057C590.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_22_14057C5D0.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_23_14057C610.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_24_14057C650.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_25_14057C690.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_26_14057C6D0.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_27_14057C710.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_28_14057C750.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_29_14057C790.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_2_14057B1A0.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_30_14057C7D0.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_31_14057C810.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_3_14057B1E0.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_4_14057B220.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_5_14057B260.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_6_14057B2A0.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_7_14057B2E0.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_8_14057B320.cpp" />
    <ClCompile Include="Source\dtor00__F_afxSessionMapYAXXZ4HA_9_14057B360.cpp" />
    <ClCompile Include="Source\D_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2B30.cpp" />
    <ClCompile Include="Source\D_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C4310.cpp" />
    <ClCompile Include="Source\endlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpair_1403C36F0.cpp" />
    <ClCompile Include="Source\endvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C5000.cpp" />
    <ClCompile Include="Source\end_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compa_1403C1990.cpp" />
    <ClCompile Include="Source\eraselistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C4790.cpp" />
    <ClCompile Include="Source\eraselistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C5FB0.cpp" />
    <ClCompile Include="Source\erasevectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C5070.cpp" />
    <ClCompile Include="Source\E_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2B80.cpp" />
    <ClCompile Include="Source\E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C4350.cpp" />
    <ClCompile Include="Source\E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C7270.cpp" />
    <ClCompile Include="Source\fillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C7AA0.cpp" />
    <ClCompile Include="Source\find_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_comp_1403C2A70.cpp" />
    <ClCompile Include="Source\F_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C5BF0.cpp" />
    <ClCompile Include="Source\F_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C43A0.cpp" />
    <ClCompile Include="Source\GenerateEphemeralKeyPairAuthenticatedKeyAgreementD_1405F6600.cpp" />
    <ClCompile Include="Source\GenerateStaticKeyPairAuthenticatedKeyAgreementDoma_1405F65A0.cpp" />
    <ClCompile Include="Source\GetCountCAsyncLogInfoQEAAKXZ_1403C16B0.cpp" />
    <ClCompile Include="Source\GetDirPathCAsyncLogInfoQEAAPEBDXZ_1403C1630.cpp" />
    <ClCompile Include="Source\GetFileNameCAsyncLogInfoQEAAPEBDXZ_1403C16D0.cpp" />
    <ClCompile Include="Source\GetOccDialogInfoCDialogMEAAPEAU_AFX_OCC_DIALOG_INF_1404DBD48.cpp" />
    <ClCompile Include="Source\GetOccDialogInfoCFormViewMEAAPEAU_AFX_OCC_DIALOG_I_1404DC15C.cpp" />
    <ClCompile Include="Source\GetOccDialogInfoCWndMEAAPEAU_AFX_OCC_DIALOG_INFOXZ_1404DBE20.cpp" />
    <ClCompile Include="Source\GetTypeNameCAsyncLogInfoQEAAPEBDXZ_1403C1650.cpp" />
    <ClCompile Include="Source\H_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C5C70.cpp" />
    <ClCompile Include="Source\IncreaseCountCAsyncLogInfoQEAAXXZ_1403C16F0.cpp" />
    <ClCompile Include="Source\InitCAsyncLogInfoQEAA_NW4ASYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.cpp" />
    <ClCompile Include="Source\Init_AuthKeyTicketMiningTicketQEAAXXZ_140073BC0.cpp" />
    <ClCompile Include="Source\insertlistUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C3760.cpp" />
    <ClCompile Include="Source\insertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C76B0.cpp" />
    <ClCompile Include="Source\insert_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_co_1403C1A10.cpp" />
    <ClCompile Include="Source\InvalidateDeviceObjectsCR3FontQEAAJXZ_140528820.cpp" />
    <ClCompile Include="Source\InvalidateSkySkyQEAAXXZ_1405229B0.cpp" />
    <ClCompile Include="Source\InvalidateSunSunQEAAXXZ_1405221E0.cpp" />
    <ClCompile Include="Source\IsLogInStateCUnmannedTraderUserInfoQEAA_NXZ_140366F20.cpp" />
    <ClCompile Include="Source\j_0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAAEB_14000EF34.cpp" />
    <ClCompile Include="Source\j_0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAXZ_14000C0BD.cpp" />
    <ClCompile Include="Source\j_0allocatorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_140012E9A.cpp" />
    <ClCompile Include="Source\j_0CAsyncLogInfoQEAAXZ_14000E4F8.cpp" />
    <ClCompile Include="Source\j_0CBHPEAVCAsyncLogInfopairHPEAVCAsyncLogInfostdQE_140013B1F.cpp" />
    <ClCompile Include="Source\j_0hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHs_14000DFBC.cpp" />
    <ClCompile Include="Source\j_0listUpairCBHPEAVCAsyncLogInfostdVallocatorUpair_140011635.cpp" />
    <ClCompile Include="Source\j_0MessageAuthenticationCodeImplVHMAC_BaseCryptoPP_14000FCA4.cpp" />
    <ClCompile Include="Source\j_0pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1400064BA.cpp" />
    <ClCompile Include="Source\j_0pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdQEAAAEB_140007469.cpp" />
    <ClCompile Include="Source\j_0UpairCBHPEAVCAsyncLogInfostdallocatorPEAU_Node__140004836.cpp" />
    <ClCompile Include="Source\j_0UpairCBHPEAVCAsyncLogInfostdallocatorU_Node_Lis_140013516.cpp" />
    <ClCompile Include="Source\j_0UpairCBHPEAVCAsyncLogInfostdallocatorV_Iterator_140002CB1.cpp" />
    <ClCompile Include="Source\j_0vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140002C1B.cpp" />
    <ClCompile Include="Source\j_0W4ASYNC_LOG_TYPEPEAVCAsyncLogInfopairCBHPEAVCAs_14000D4F4.cpp" />
    <ClCompile Include="Source\j_0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU1_140005678.cpp" />
    <ClCompile Include="Source\j_0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU1_14000FC2C.cpp" />
    <ClCompile Include="Source\j_0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_14000C6B2.cpp" />
    <ClCompile Include="Source\j_0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140010E06.cpp" />
    <ClCompile Include="Source\j_0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_14001213E.cpp" />
    <ClCompile Include="Source\j_0_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compa_14001253F.cpp" />
    <ClCompile Include="Source\j_0_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUle_14000B91A.cpp" />
    <ClCompile Include="Source\j_0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_14000269E.cpp" />
    <ClCompile Include="Source\j_0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_14000CA54.cpp" />
    <ClCompile Include="Source\j_0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140010D1B.cpp" />
    <ClCompile Include="Source\j_0_List_nodUpairCBHPEAVCAsyncLogInfostdVallocator_140002DC4.cpp" />
    <ClCompile Include="Source\j_0_List_ptrUpairCBHPEAVCAsyncLogInfostdVallocator_1400071CB.cpp" />
    <ClCompile Include="Source\j_0_List_valUpairCBHPEAVCAsyncLogInfostdVallocator_1400024CD.cpp" />
    <ClCompile Include="Source\j_0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_14000BAE1.cpp" />
    <ClCompile Include="Source\j_0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140011847.cpp" />
    <ClCompile Include="Source\j_0_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140003EC2.cpp" />
    <ClCompile Include="Source\j_0_Vector_const_iteratorV_Iterator0AlistUpairCBHP_14000D526.cpp" />
    <ClCompile Include="Source\j_0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_140007405.cpp" />
    <ClCompile Include="Source\j_0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_14000A8FD.cpp" />
    <ClCompile Include="Source\j_0_Vector_valV_Iterator0AlistUpairCBHPEAVCAsyncLo_1400107E4.cpp" />
    <ClCompile Include="Source\j_1CAsyncLogInfoQEAAXZ_14000F182.cpp" />
    <ClCompile Include="Source\j_1hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHs_14000900C.cpp" />
    <ClCompile Include="Source\j_1listUpairCBHPEAVCAsyncLogInfostdVallocatorUpair_140011950.cpp" />
    <ClCompile Include="Source\j_1MessageAuthenticationCodeImplVHMAC_BaseCryptoPP_1400097F5.cpp" />
    <ClCompile Include="Source\j_1pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_140005970.cpp" />
    <ClCompile Include="Source\j_1vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140009B47.cpp" />
    <ClCompile Include="Source\j_1_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU1_14000684D.cpp" />
    <ClCompile Include="Source\j_1_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_1400047BE.cpp" />
    <ClCompile Include="Source\j_1_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compa_14000839B.cpp" />
    <ClCompile Include="Source\j_1_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_14000788D.cpp" />
    <ClCompile Include="Source\j_1_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140006F73.cpp" />
    <ClCompile Include="Source\j_1_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140008B52.cpp" />
    <ClCompile Include="Source\j_1_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_140012580.cpp" />
    <ClCompile Include="Source\j_4_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU1_140009D6D.cpp" />
    <ClCompile Include="Source\j_4_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140001488.cpp" />
    <ClCompile Include="Source\j_4_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140005547.cpp" />
    <ClCompile Include="Source\j_8UpairCBHPEAVCAsyncLogInfostdU01stdYA_NAEBValloc_14000BDD9.cpp" />
    <ClCompile Include="Source\j_8_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_140010A91.cpp" />
    <ClCompile Include="Source\j_8_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140012F2B.cpp" />
    <ClCompile Include="Source\j_8_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140012F9E.cpp" />
    <ClCompile Include="Source\j_9_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400090B1.cpp" />
    <ClCompile Include="Source\j_9_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_14000C310.cpp" />
    <ClCompile Include="Source\j_9_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140006938.cpp" />
    <ClCompile Include="Source\j_AccountServerLoginCMainThreadQEAAXXZ_14001102C.cpp" />
    <ClCompile Include="Source\j_allocateallocatorU_Node_List_nodUpairCBHPEAVCAsy_140001794.cpp" />
    <ClCompile Include="Source\j_allocateallocatorV_Iterator0AlistUpairCBHPEAVCAs_140001E7E.cpp" />
    <ClCompile Include="Source\j_AuthLastCriTicketMiningTicketQEAAHGEEEEZ_14000DDAA.cpp" />
    <ClCompile Include="Source\j_AuthLastMentalTicketMiningTicketQEAAHGEEEEZ_14000F38A.cpp" />
    <ClCompile Include="Source\j_AuthMiningTicketCHolyStoneSystemQEAA_NIZ_140009EBC.cpp" />
    <ClCompile Include="Source\j_auto_trade_login_sellCMgrAvatorItemHistoryQEAAXP_140006FAA.cpp" />
    <ClCompile Include="Source\j_AvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140002306.cpp" />
    <ClCompile Include="Source\j_beginlistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1400096E2.cpp" />
    <ClCompile Include="Source\j_beginvectorV_Iterator0AlistUpairCBHPEAVCAsyncLog_14000CFE0.cpp" />
    <ClCompile Include="Source\j_begin_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_c_1400049AD.cpp" />
    <ClCompile Include="Source\j_CallFunc_RFOnline_AuthCEnglandBillingMgrQEAAHAEA_14000D4B3.cpp" />
    <ClCompile Include="Source\j_CallFunc_RFOnline_AuthCRusiaBillingMgrQEAAHAEAU__140001E24.cpp" />
    <ClCompile Include="Source\j_CallProc_RFOnlineAuthCRFCashItemDatabaseQEAAHAEA_14000C2F2.cpp" />
    <ClCompile Include="Source\j_CallProc_RFOnlineAuth_JapCRFCashItemDatabaseQEAA_14000B334.cpp" />
    <ClCompile Include="Source\j_capacityvectorV_Iterator0AlistUpairCBHPEAVCAsync_14000CB58.cpp" />
    <ClCompile Include="Source\j_clearlistUpairCBHPEAVCAsyncLogInfostdVallocatorU_140008D82.cpp" />
    <ClCompile Include="Source\j_CompleteLogInCompeteCUnmannedTraderControllerQEA_14000E66A.cpp" />
    <ClCompile Include="Source\j_constructallocatorPEAU_Node_List_nodUpairCBHPEAV_14000EF6B.cpp" />
    <ClCompile Include="Source\j_constructallocatorUpairCBHPEAVCAsyncLogInfostdst_14000ECC8.cpp" />
    <ClCompile Include="Source\j_constructallocatorV_Iterator0AlistUpairCBHPEAVCA_140006CB2.cpp" />
    <ClCompile Include="Source\j_C_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140009BB0.cpp" />
    <ClCompile Include="Source\j_deallocateallocatorU_Node_List_nodUpairCBHPEAVCA_140010910.cpp" />
    <ClCompile Include="Source\j_deallocateallocatorV_Iterator0AlistUpairCBHPEAVC_14000176C.cpp" />
    <ClCompile Include="Source\j_destroyallocatorPEAU_Node_List_nodUpairCBHPEAVCA_140001C6C.cpp" />
    <ClCompile Include="Source\j_destroyallocatorU_Node_List_nodUpairCBHPEAVCAsyn_14000A6EB.cpp" />
    <ClCompile Include="Source\j_destroyallocatorV_Iterator0AlistUpairCBHPEAVCAsy_140013A98.cpp" />
    <ClCompile Include="Source\j_D_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140003797.cpp" />
    <ClCompile Include="Source\j_D_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140002C70.cpp" />
    <ClCompile Include="Source\j_endlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_140008A76.cpp" />
    <ClCompile Include="Source\j_endvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_140004A57.cpp" />
    <ClCompile Include="Source\j_end_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_com_1400029EB.cpp" />
    <ClCompile Include="Source\j_eraselistUpairCBHPEAVCAsyncLogInfostdVallocatorU_140003D1E.cpp" />
    <ClCompile Include="Source\j_eraselistUpairCBHPEAVCAsyncLogInfostdVallocatorU_14000C559.cpp" />
    <ClCompile Include="Source\j_erasevectorV_Iterator0AlistUpairCBHPEAVCAsyncLog_140010C7B.cpp" />
    <ClCompile Include="Source\j_E_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140003A80.cpp" />
    <ClCompile Include="Source\j_E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140007F54.cpp" />
    <ClCompile Include="Source\j_E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_14000B758.cpp" />
    <ClCompile Include="Source\j_fillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140010B90.cpp" />
    <ClCompile Include="Source\j_find_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_co_14000F4A2.cpp" />
    <ClCompile Include="Source\j_F_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_14000A187.cpp" />
    <ClCompile Include="Source\j_F_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140011E87.cpp" />
    <ClCompile Include="Source\j_GetCountCAsyncLogInfoQEAAKXZ_14000B60E.cpp" />
    <ClCompile Include="Source\j_GetDirPathCAsyncLogInfoQEAAPEBDXZ_140011A77.cpp" />
    <ClCompile Include="Source\j_GetFileNameCAsyncLogInfoQEAAPEBDXZ_1400112A7.cpp" />
    <ClCompile Include="Source\j_GetTypeNameCAsyncLogInfoQEAAPEBDXZ_140003337.cpp" />
    <ClCompile Include="Source\j_H_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_14000B6F4.cpp" />
    <ClCompile Include="Source\j_IncreaseCountCAsyncLogInfoQEAAXXZ_140007B5D.cpp" />
    <ClCompile Include="Source\j_InitCAsyncLogInfoQEAA_NW4ASYNC_LOG_TYPEPEBD1_NKA_14000C3F1.cpp" />
    <ClCompile Include="Source\j_Init_AuthKeyTicketMiningTicketQEAAXXZ_140004B5B.cpp" />
    <ClCompile Include="Source\j_insertlistUpairCBHPEAVCAsyncLogInfostdVallocator_14000EC2D.cpp" />
    <ClCompile Include="Source\j_insertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1400133BD.cpp" />
    <ClCompile Include="Source\j_insert_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash__140008F53.cpp" />
    <ClCompile Include="Source\j_IsLogInStateCUnmannedTraderUserInfoQEAA_NXZ_140009F3E.cpp" />
    <ClCompile Include="Source\j_LoginCBillingIDUEAAXPEAVCUserDBZ_140002B76.cpp" />
    <ClCompile Include="Source\j_LoginCBillingJPUEAAXPEAVCUserDBZ_140003FDA.cpp" />
    <ClCompile Include="Source\j_LoginCBillingManagerQEAAXPEAVCUserDBZ_140011F54.cpp" />
    <ClCompile Include="Source\j_LoginCBillingNULLUEAAXPEAVCUserDBZ_140006D4D.cpp" />
    <ClCompile Include="Source\j_LoginCBillingUEAAXPEAVCUserDBZ_14000AFD8.cpp" />
    <ClCompile Include="Source\j_LogInCNormalGuildBattleGuildGUILD_BATTLEQEAAXHKE_1400127B5.cpp" />
    <ClCompile Include="Source\j_LoginCNormalGuildBattleGuildMemberGUILD_BATTLEQE_14000E304.cpp" />
    <ClCompile Include="Source\j_LogInCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_1400062BC.cpp" />
    <ClCompile Include="Source\j_LogInCNormalGuildBattleManagerGUILD_BATTLEQEAAXH_14001391C.cpp" />
    <ClCompile Include="Source\j_LogInControllServerCNetworkEXAEAA_NHPEADZ_14000E8DB.cpp" />
    <ClCompile Include="Source\j_LogInWebAgentServerCNetworkEXAEAA_NHPEADZ_1400026FD.cpp" />
    <ClCompile Include="Source\j_login_cancel_auto_tradeCMgrAvatorItemHistoryQEAA_140011B71.cpp" />
    <ClCompile Include="Source\j_lower_bound_HashV_Hmap_traitsHPEAVCAsyncLogInfoV_1400014EC.cpp" />
    <ClCompile Include="Source\j_make_pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdYAA_1400120E4.cpp" />
    <ClCompile Include="Source\j_max_sizeallocatorUpairCBHPEAVCAsyncLogInfostdstd_140006AAA.cpp" />
    <ClCompile Include="Source\j_max_sizeallocatorV_Iterator0AlistUpairCBHPEAVCAs_1400094B2.cpp" />
    <ClCompile Include="Source\j_max_sizelistUpairCBHPEAVCAsyncLogInfostdVallocat_140004098.cpp" />
    <ClCompile Include="Source\j_max_sizevectorV_Iterator0AlistUpairCBHPEAVCAsync_14000E9C6.cpp" />
    <ClCompile Include="Source\j_NotifyLogInSetBuffCRaceBuffInfoByHolyQuestAEAAXG_14000A1A5.cpp" />
    <ClCompile Include="Source\j_OnCheckSession_FirstVerifyCHackShieldExSystemUEA_1400114FA.cpp" />
    <ClCompile Include="Source\j_OnCheckSession_FirstVerifyCNationSettingManagerQ_140008558.cpp" />
    <ClCompile Include="Source\j_OnCheckSession_FirstVerifyHACKSHEILD_PARAM_ANTIC_140007789.cpp" />
    <ClCompile Include="Source\j_OnConnectSessionCHackShieldExSystemUEAAXHZ_14000FEA7.cpp" />
    <ClCompile Include="Source\j_OnConnectSessionCNationSettingManagerQEAAXHZ_1400046EC.cpp" />
    <ClCompile Include="Source\j_OnDisConnectSessionCHackShieldExSystemUEAAXHZ_1400017DF.cpp" />
    <ClCompile Include="Source\j_OnDisConnectSessionCNationSettingManagerQEAAXHZ_14000B2E4.cpp" />
    <ClCompile Include="Source\j_OnLoopSessionCHackShieldExSystemUEAAXHZ_1400137FA.cpp" />
    <ClCompile Include="Source\j_OnRecvSessionHACKSHEILD_PARAM_ANTICPUEAA_NPEAVCH_140008B89.cpp" />
    <ClCompile Include="Source\j_OnRecvSession_ClientCheckSum_ResponseHACKSHEILD__140004CA5.cpp" />
    <ClCompile Include="Source\j_OnRecvSession_ClientCrc_ResponseHACKSHEILD_PARAM_14000338C.cpp" />
    <ClCompile Include="Source\j_OnRecvSession_ServerCheckSum_RequestHACKSHEILD_P_14000C342.cpp" />
    <ClCompile Include="Source\j_resizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_140006C12.cpp" />
    <ClCompile Include="Source\j_SendMsg_CurAllUserLoginCBillingIEAAXXZ_140013D0E.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildMemberLoginCGuildQEAAXKGGZ_14000F11E.cpp" />
    <ClCompile Include="Source\j_SendMsg_LoginCBillingIDMEAA_NPEAD00FPEAU_SYSTEMT_1400078F1.cpp" />
    <ClCompile Include="Source\j_SendMsg_LoginCBillingJPMEAA_NPEAD00FPEAU_SYSTEMT_14001081B.cpp" />
    <ClCompile Include="Source\j_SendMsg_LoginCBillingMEAA_NPEAD00FPEAU_SYSTEMTIM_1400040BB.cpp" />
    <ClCompile Include="Source\j_SendMsg_LoginCBillingNULLMEAA_NPEAD00FPEAU_SYSTE_1400020D1.cpp" />
    <ClCompile Include="Source\j_SendMsg_UserLogInNotifyTaxRateTRC_AutoTradeQEAAX_14000DA17.cpp" />
    <ClCompile Include="Source\j_Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ_14000CA59.cpp" />
    <ClCompile Include="Source\j_Set_AuthKeyTicketMiningTicketQEAAXIZ_140002F18.cpp" />
    <ClCompile Include="Source\j_sizelistUpairCBHPEAVCAsyncLogInfostdVallocatorUp_140007054.cpp" />
    <ClCompile Include="Source\j_sizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_14000AABA.cpp" />
    <ClCompile Include="Source\j_size_apex_send_loginQEAAHXZ_140013B97.cpp" />
    <ClCompile Include="Source\j_size_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_co_14000896D.cpp" />
    <ClCompile Include="Source\j_unchecked_copyPEAV_Iterator0AlistUpairCBHPEAVCAs_14000ECDC.cpp" />
    <ClCompile Include="Source\j_unchecked_uninitialized_copyPEAV_Iterator0AlistU_140007C34.cpp" />
    <ClCompile Include="Source\j_unchecked_uninitialized_fill_nPEAV_Iterator0Alis_140003B7F.cpp" />
    <ClCompile Include="Source\j_UpdateLogFileNameCAsyncLogInfoQEAAXXZ_140006852.cpp" />
    <ClCompile Include="Source\j_UpdateLogInCompleteCUnmannedTraderControllerQEAA_14000CDE2.cpp" />
    <ClCompile Include="Source\j_Update_TrunkPasswordCUserDBQEAA_NPEADZ_1400018F7.cpp" />
    <ClCompile Include="Source\j_ValidateDL_GroupParametersUECPPointCryptoPPCrypt_1400018A2.cpp" />
    <ClCompile Include="Source\j_ValidateDL_GroupParametersUECPPointCryptoPPCrypt_140007478.cpp" />
    <ClCompile Include="Source\j_ValidateDL_GroupParametersUECPPointCryptoPPCrypt_14000C37E.cpp" />
    <ClCompile Include="Source\j_ValidateDL_PrivateKeyImplVDL_GroupParameters_ECV_140009D9F.cpp" />
    <ClCompile Include="Source\j_ValidateDL_PublicKeyImplVDL_GroupParameters_ECVE_14000AAEC.cpp" />
    <ClCompile Include="Source\j_validatetable_objlua_tinkerQEAA_NXZ_140009525.cpp" />
    <ClCompile Include="Source\j_Y_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140003E04.cpp" />
    <ClCompile Include="Source\j_Y_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_14000E15B.cpp" />
    <ClCompile Include="Source\j__AllocateU_Node_List_nodUpairCBHPEAVCAsyncLogInf_14000E174.cpp" />
    <ClCompile Include="Source\j__AllocateV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_140007CE3.cpp" />
    <ClCompile Include="Source\j__BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocat_1400123A5.cpp" />
    <ClCompile Include="Source\j__BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocat_140012C5B.cpp" />
    <ClCompile Include="Source\j__BuyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_140004F11.cpp" />
    <ClCompile Include="Source\j__ConstructPEAU_Node_List_nodUpairCBHPEAVCAsyncLo_140009EE4.cpp" />
    <ClCompile Include="Source\j__ConstructUpairCBHPEAVCAsyncLogInfostdU12stdYAXP_14000F3A3.cpp" />
    <ClCompile Include="Source\j__ConstructV_Iterator0AlistUpairCBHPEAVCAsyncLogI_140007D6F.cpp" />
    <ClCompile Include="Source\j__Construct_nvectorV_Iterator0AlistUpairCBHPEAVCA_14000467E.cpp" />
    <ClCompile Include="Source\j__Copy_backward_optPEAV_Iterator0AlistUpairCBHPEA_140001271.cpp" />
    <ClCompile Include="Source\j__Copy_optPEAV_Iterator0AlistUpairCBHPEAVCAsyncLo_14000AB64.cpp" />
    <ClCompile Include="Source\j__DestroyPEAU_Node_List_nodUpairCBHPEAVCAsyncLogI_140001541.cpp" />
    <ClCompile Include="Source\j__DestroyU_Node_List_nodUpairCBHPEAVCAsyncLogInfo_14000A9C0.cpp" />
    <ClCompile Include="Source\j__DestroyvectorV_Iterator0AlistUpairCBHPEAVCAsync_140002CA2.cpp" />
    <ClCompile Include="Source\j__DestroyV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_14000135C.cpp" />
    <ClCompile Include="Source\j__Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsync_140001177.cpp" />
    <ClCompile Include="Source\j__Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsync_14000AF8D.cpp" />
    <ClCompile Include="Source\j__EMessageAuthenticationCodeImplVHMAC_BaseCryptoP_140002B6C.cpp" />
    <ClCompile Include="Source\j__EMessageAuthenticationCodeImplVHMAC_BaseCryptoP_14000DE2C.cpp" />
    <ClCompile Include="Source\j__EMessageAuthenticationCodeImplVHMAC_BaseCryptoP_140013985.cpp" />
    <ClCompile Include="Source\j__FillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_14000F9D9.cpp" />
    <ClCompile Include="Source\j__GCAsyncLogInfoQEAAPEAXIZ_14000F1B9.cpp" />
    <ClCompile Include="Source\j__Get_iter_from_vec_HashV_Hmap_traitsHPEAVCAsyncL_1400066E5.cpp" />
    <ClCompile Include="Source\j__G_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVal_14000DEAE.cpp" />
    <ClCompile Include="Source\j__Hashval_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhas_140005F0B.cpp" />
    <ClCompile Include="Source\j__IncsizelistUpairCBHPEAVCAsyncLogInfostdVallocat_140004CA0.cpp" />
    <ClCompile Include="Source\j__InsertlistUpairCBHPEAVCAsyncLogInfostdVallocato_140013F34.cpp" />
    <ClCompile Include="Source\j__InsertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140002D29.cpp" />
    <ClCompile Include="Source\j__Insert_nvectorV_Iterator0AlistUpairCBHPEAVCAsyn_140011CD4.cpp" />
    <ClCompile Include="Source\j__Iter_catV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_14000AA3D.cpp" />
    <ClCompile Include="Source\j__Iter_randomPEAV_Iterator0AlistUpairCBHPEAVCAsyn_140008332.cpp" />
    <ClCompile Include="Source\j__Kfn_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareH_140011A90.cpp" />
    <ClCompile Include="Source\j__Move_backward_optPEAV_Iterator0AlistUpairCBHPEA_14000B69A.cpp" />
    <ClCompile Include="Source\j__Move_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLo_140003251.cpp" />
    <ClCompile Include="Source\j__Mynode_Const_iterator0AlistUpairCBHPEAVCAsyncLo_14000F380.cpp" />
    <ClCompile Include="Source\j__MyvallistUpairCBHPEAVCAsyncLogInfostdVallocator_140003DAF.cpp" />
    <ClCompile Include="Source\j__NextnodelistUpairCBHPEAVCAsyncLogInfostdValloca_140007F90.cpp" />
    <ClCompile Include="Source\j__PrevnodelistUpairCBHPEAVCAsyncLogInfostdValloca_1400055BF.cpp" />
    <ClCompile Include="Source\j__Ptr_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLog_14001394E.cpp" />
    <ClCompile Include="Source\j__SplicelistUpairCBHPEAVCAsyncLogInfostdVallocato_14000E534.cpp" />
    <ClCompile Include="Source\j__TidylistUpairCBHPEAVCAsyncLogInfostdVallocatorU_140001555.cpp" />
    <ClCompile Include="Source\j__TidyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLog_140012760.cpp" />
    <ClCompile Include="Source\j__UfillvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1400060F5.cpp" />
    <ClCompile Include="Source\j__UmovePEAV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_140005F74.cpp" />
    <ClCompile Include="Source\j__Unchecked_move_backwardPEAV_Iterator0AlistUpair_14000CF40.cpp" />
    <ClCompile Include="Source\j__Unchecked_uninitialized_movePEAV_Iterator0Alist_140005C68.cpp" />
    <ClCompile Include="Source\j__Uninit_copyPEAV_Iterator0AlistUpairCBHPEAVCAsyn_14000FCCC.cpp" />
    <ClCompile Include="Source\j__Uninit_fill_nPEAV_Iterator0AlistUpairCBHPEAVCAs_14000DAC1.cpp" />
    <ClCompile Include="Source\j__Uninit_movePEAV_Iterator0AlistUpairCBHPEAVCAsyn_140011509.cpp" />
    <ClCompile Include="Source\j__XlenvectorV_Iterator0AlistUpairCBHPEAVCAsyncLog_14001003C.cpp" />
    <ClCompile Include="Source\LoginCBillingIDUEAAXPEAVCUserDBZ_14028E0F0.cpp" />
    <ClCompile Include="Source\LoginCBillingJPUEAAXPEAVCUserDBZ_14028E910.cpp" />
    <ClCompile Include="Source\LoginCBillingManagerQEAAXPEAVCUserDBZ_140079030.cpp" />
    <ClCompile Include="Source\LoginCBillingNULLUEAAXPEAVCUserDBZ_14028DBD0.cpp" />
    <ClCompile Include="Source\LoginCBillingUEAAXPEAVCUserDBZ_14028CAC0.cpp" />
    <ClCompile Include="Source\LogInCNormalGuildBattleGuildGUILD_BATTLEQEAAXHKEPE_1403E0DD0.cpp" />
    <ClCompile Include="Source\LoginCNormalGuildBattleGuildMemberGUILD_BATTLEQEAA_1403DFA80.cpp" />
    <ClCompile Include="Source\LogInCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_1403E4050.cpp" />
    <ClCompile Include="Source\LogInCNormalGuildBattleManagerGUILD_BATTLEQEAAXHKK_1403D4360.cpp" />
    <ClCompile Include="Source\LogInControllServerCNetworkEXAEAA_NHPEADZ_1401C7250.cpp" />
    <ClCompile Include="Source\LogInWebAgentServerCNetworkEXAEAA_NHPEADZ_1401DA860.cpp" />
    <ClCompile Include="Source\login_cancel_auto_tradeCMgrAvatorItemHistoryQEAAXH_140239D60.cpp" />
    <ClCompile Include="Source\lower_bound_HashV_Hmap_traitsHPEAVCAsyncLogInfoVha_1403C30D0.cpp" />
    <ClCompile Include="Source\make_pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdYAAUp_1403C75D0.cpp" />
    <ClCompile Include="Source\max_sizeallocatorUpairCBHPEAVCAsyncLogInfostdstdQE_1403C6F00.cpp" />
    <ClCompile Include="Source\max_sizeallocatorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C7200.cpp" />
    <ClCompile Include="Source\max_sizelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C5F60.cpp" />
    <ClCompile Include="Source\max_sizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C69A0.cpp" />
    <ClCompile Include="Source\NotifyLogInSetBuffCRaceBuffInfoByHolyQuestAEAAXGZ_1403B42D0.cpp" />
    <ClCompile Include="Source\OnCheckSession_FirstVerifyCHackShieldExSystemUEAA__140417250.cpp" />
    <ClCompile Include="Source\OnCheckSession_FirstVerifyCNationSettingManagerQEA_140229470.cpp" />
    <ClCompile Include="Source\OnCheckSession_FirstVerifyHACKSHEILD_PARAM_ANTICPU_140417960.cpp" />
    <ClCompile Include="Source\OnConnectSessionCHackShieldExSystemUEAAXHZ_1404170D0.cpp" />
    <ClCompile Include="Source\OnConnectSessionCNationSettingManagerQEAAXHZ_140229400.cpp" />
    <ClCompile Include="Source\OnDisConnectSessionCHackShieldExSystemUEAAXHZ_140417140.cpp" />
    <ClCompile Include="Source\OnDisConnectSessionCNationSettingManagerQEAAXHZ_1402294F0.cpp" />
    <ClCompile Include="Source\OnLoopSessionCHackShieldExSystemUEAAXHZ_1404171A0.cpp" />
    <ClCompile Include="Source\OnRecvSessionHACKSHEILD_PARAM_ANTICPUEAA_NPEAVCHac_140417F10.cpp" />
    <ClCompile Include="Source\OnRecvSession_ClientCheckSum_ResponseHACKSHEILD_PA_140418120.cpp" />
    <ClCompile Include="Source\OnRecvSession_ClientCrc_ResponseHACKSHEILD_PARAM_A_140418290.cpp" />
    <ClCompile Include="Source\OnRecvSession_ServerCheckSum_RequestHACKSHEILD_PAR_140417FB0.cpp" />
    <ClCompile Include="Source\R3InvalidateDeviceYAJXZ_1404E9FC0.cpp" />
    <ClCompile Include="Source\resizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C3F20.cpp" />
    <ClCompile Include="Source\SendMsg_CurAllUserLoginCBillingIEAAXXZ_14028D610.cpp" />
    <ClCompile Include="Source\SendMsg_GuildMemberLoginCGuildQEAAXKGGZ_1402570F0.cpp" />
    <ClCompile Include="Source\SendMsg_LoginCBillingIDMEAA_NPEAD00FPEAU_SYSTEMTIM_14028E600.cpp" />
    <ClCompile Include="Source\SendMsg_LoginCBillingJPMEAA_NPEAD00FPEAU_SYSTEMTIM_14028ECC0.cpp" />
    <ClCompile Include="Source\SendMsg_LoginCBillingMEAA_NPEAD00FPEAU_SYSTEMTIMEJ_14028D3C0.cpp" />
    <ClCompile Include="Source\SendMsg_LoginCBillingNULLMEAA_NPEAD00FPEAU_SYSTEMT_14028DC10.cpp" />
    <ClCompile Include="Source\SendMsg_UserLogInNotifyTaxRateTRC_AutoTradeQEAAXHZ_1402D8540.cpp" />
    <ClCompile Include="Source\SetOccDialogInfoCDialogMEAAHPEAU_AFX_OCC_DIALOG_IN_1404DBD42.cpp" />
    <ClCompile Include="Source\SetOccDialogInfoCFormViewMEAAHPEAU_AFX_OCC_DIALOG__1404DC156.cpp" />
    <ClCompile Include="Source\SetOccDialogInfoCWndMEAAHPEAU_AFX_OCC_DIALOG_INFOZ_1404DBE1A.cpp" />
    <ClCompile Include="Source\Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ_1400A6BA0.cpp" />
    <ClCompile Include="Source\Set_AuthKeyTicketMiningTicketQEAAXIZ_140078ED0.cpp" />
    <ClCompile Include="Source\sizelistUpairCBHPEAVCAsyncLogInfostdVallocatorUpai_1403C4650.cpp" />
    <ClCompile Include="Source\sizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C4210.cpp" />
    <ClCompile Include="Source\size_apex_send_loginQEAAHXZ_140410BF0.cpp" />
    <ClCompile Include="Source\size_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_comp_1403C3080.cpp" />
    <ClCompile Include="Source\unchecked_copyPEAV_Iterator0AlistUpairCBHPEAVCAsyn_1403C7970.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_copyPEAV_Iterator0AlistUpa_1403C8D20.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_fill_nPEAV_Iterator0AlistU_1403C7C50.cpp" />
    <ClCompile Include="Source\UpdateLogFileNameCAsyncLogInfoQEAAXXZ_1403BD0F0.cpp" />
    <ClCompile Include="Source\UpdateLogInCompleteCUnmannedTraderControllerQEAAEP_14034E440.cpp" />
    <ClCompile Include="Source\Update_TrunkPasswordCUserDBQEAA_NPEADZ_140116FD0.cpp" />
    <ClCompile Include="Source\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAD0.cpp" />
    <ClCompile Include="Source\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAF0.cpp" />
    <ClCompile Include="Source\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADD90.cpp" />
    <ClCompile Include="Source\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046A900.cpp" />
    <ClCompile Include="Source\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046A920.cpp" />
    <ClCompile Include="Source\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046AD80.cpp" />
    <ClCompile Include="Source\ValidateDL_GroupParametersVIntegerCryptoPPCryptoPP_140551AC0.cpp" />
    <ClCompile Include="Source\ValidateDL_GroupParametersVIntegerCryptoPPCryptoPP_1405AD4F0.cpp" />
    <ClCompile Include="Source\ValidateDL_PrivateKeyImplVDL_GroupParameters_DSACr_140568460.cpp" />
    <ClCompile Include="Source\ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_1404515F0.cpp" />
    <ClCompile Include="Source\ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_140558CA0.cpp" />
    <ClCompile Include="Source\ValidateDL_PrivateKeyImplVDL_GroupParameters_GFPCr_140635EE0.cpp" />
    <ClCompile Include="Source\ValidateDL_PrivateKeyImplVDL_GroupParameters_GFP_D_140637C30.cpp" />
    <ClCompile Include="Source\ValidateDL_PublicKeyImplVDL_GroupParameters_DSACry_140568F30.cpp" />
    <ClCompile Include="Source\ValidateDL_PublicKeyImplVDL_GroupParameters_ECVEC2_140558420.cpp" />
    <ClCompile Include="Source\ValidateDL_PublicKeyImplVDL_GroupParameters_ECVECP_140450870.cpp" />
    <ClCompile Include="Source\ValidateDL_PublicKeyImplVDL_GroupParameters_GFPCry_1406369F0.cpp" />
    <ClCompile Include="Source\ValidateDL_PublicKeyImplVDL_GroupParameters_GFP_De_1406373A0.cpp" />
    <ClCompile Include="Source\ValidateElementDL_GroupParameters_ECVEC2NCryptoPPC_140583CF0.cpp" />
    <ClCompile Include="Source\ValidateElementDL_GroupParameters_ECVECPCryptoPPCr_14057FB10.cpp" />
    <ClCompile Include="Source\ValidateElementDL_GroupParameters_IntegerBasedCryp_140630AE0.cpp" />
    <ClCompile Include="Source\ValidateGroupDL_GroupParameters_DSACryptoPPUEBA_NA_140630230.cpp" />
    <ClCompile Include="Source\ValidateGroupDL_GroupParameters_ECVEC2NCryptoPPCry_1405835A0.cpp" />
    <ClCompile Include="Source\ValidateGroupDL_GroupParameters_ECVECPCryptoPPCryp_14057F300.cpp" />
    <ClCompile Include="Source\ValidateGroupDL_GroupParameters_IntegerBasedCrypto_140630680.cpp" />
    <ClCompile Include="Source\ValidateParametersEC2NCryptoPPQEBA_NAEAVRandomNumb_14062E210.cpp" />
    <ClCompile Include="Source\ValidateParametersECPCryptoPPQEBA_NAEAVRandomNumbe_14060E2A0.cpp" />
    <ClCompile Include="Source\validatetable_objlua_tinkerQEAA_NXZ_1404462F0.cpp" />
    <ClCompile Include="Source\Y_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C7420.cpp" />
    <ClCompile Include="Source\Y_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6DD0.cpp" />
    <ClCompile Include="Source\_AllocateU_Node_List_nodUpairCBHPEAVCAsyncLogInfos_1403C7E90.cpp" />
    <ClCompile Include="Source\_AllocateV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C7D00.cpp" />
    <ClCompile Include="Source\_BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C6370.cpp" />
    <ClCompile Include="Source\_BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C6500.cpp" />
    <ClCompile Include="Source\_BuyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C7100.cpp" />
    <ClCompile Include="Source\_CAsyncLogInfoInit__1_dtor0_1403BD0C0.cpp" />
    <ClCompile Include="Source\_CAsyncLogInfo_CAsyncLogInfo__1_dtor0_1403BCB50.cpp" />
    <ClCompile Include="Source\_CEnglandBillingMgrCallFunc_RFOnline_Auth__1_dtor0_140319C50.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLogIn__1_dtor0_1403A5C90.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLogIn__1_dtor1_1403A5CC0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLogIn__1_dtor2_1403A5CF0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitInfoListLogIn__1_dtor3_1403A5D20.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoLogIn__1_dtor0_1403AD090.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoLogIn__1_dtor1_1403AD0C0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoLogIn__1_dtor2_1403AD0F0.cpp" />
    <ClCompile Include="Source\_CMoveMapLimitRightInfoLogIn__1_dtor3_1403AD120.cpp" />
    <ClCompile Include="Source\_ConstructPEAU_Node_List_nodUpairCBHPEAVCAsyncLogI_1403C7F50.cpp" />
    <ClCompile Include="Source\_ConstructUpairCBHPEAVCAsyncLogInfostdU12stdYAXPEA_1403C7DB0.cpp" />
    <ClCompile Include="Source\_ConstructV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C8B70.cpp" />
    <ClCompile Include="Source\_Construct_nvectorV_Iterator0AlistUpairCBHPEAVCAsy_1403C6820.cpp" />
    <ClCompile Include="Source\_Copy_backward_optPEAV_Iterator0AlistUpairCBHPEAVC_1403C8AD0.cpp" />
    <ClCompile Include="Source\_Copy_optPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C8530.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_PrivateKeyImpl_CryptoPPDL_GroupParamet_140451850.cpp" />
    <ClCompile Include="Source\_DestroyPEAU_Node_List_nodUpairCBHPEAVCAsyncLogInf_1403C7BC0.cpp" />
    <ClCompile Include="Source\_DestroyU_Node_List_nodUpairCBHPEAVCAsyncLogInfost_1403C7F40.cpp" />
    <ClCompile Include="Source\_DestroyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C69F0.cpp" />
    <ClCompile Include="Source\_DestroyV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1403C8C60.cpp" />
    <ClCompile Include="Source\_Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C7BD0.cpp" />
    <ClCompile Include="Source\_Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C8810.cpp" />
    <ClCompile Include="Source\_EMessageAuthenticationCodeImplVHMAC_BaseCryptoPPV_140465900.cpp" />
    <ClCompile Include="Source\_EMessageAuthenticationCodeImplVHMAC_BaseCryptoPPV_14046AC80.cpp" />
    <ClCompile Include="Source\_FillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1403C8680.cpp" />
    <ClCompile Include="Source\_GCAsyncLogInfoQEAAPEAXIZ_1403C14F0.cpp" />
    <ClCompile Include="Source\_Get_iter_from_vec_HashV_Hmap_traitsHPEAVCAsyncLog_1403C2E50.cpp" />
    <ClCompile Include="Source\_G_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVallo_1403C8CB0.cpp" />
    <ClCompile Include="Source\_Hashval_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash__1403C3530.cpp" />
    <ClCompile Include="Source\_IncsizelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C4D90.cpp" />
    <ClCompile Include="Source\_InsertlistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1403C4670.cpp" />
    <ClCompile Include="Source\_InsertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C80C0.cpp" />
    <ClCompile Include="Source\_Insert_nvectorV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C5320.cpp" />
    <ClCompile Include="Source\_Iter_catV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C8060.cpp" />
    <ClCompile Include="Source\_Iter_randomPEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8470.cpp" />
    <ClCompile Include="Source\_Kfn_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUl_1403C35E0.cpp" />
    <ClCompile Include="Source\_Move_backward_optPEAV_Iterator0AlistUpairCBHPEAVC_1403C8760.cpp" />
    <ClCompile Include="Source\_Move_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C8700.cpp" />
    <ClCompile Include="Source\_Mynode_Const_iterator0AlistUpairCBHPEAVCAsyncLogI_1403C5C50.cpp" />
    <ClCompile Include="Source\_MyvallistUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C3610.cpp" />
    <ClCompile Include="Source\_NextnodelistUpairCBHPEAVCAsyncLogInfostdVallocato_1403C3600.cpp" />
    <ClCompile Include="Source\_PrevnodelistUpairCBHPEAVCAsyncLogInfostdVallocato_1403C4590.cpp" />
    <ClCompile Include="Source\_Ptr_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C84D0.cpp" />
    <ClCompile Include="Source\_SplicelistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1403C38D0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C18C0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2480.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C24B0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C24E0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2510.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2540.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2570.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C25A0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C25D0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2600.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2630.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2660.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2690.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C26C0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2700.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2FA0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2FD0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3000.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3370.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C33A0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C33D0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3410.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3820.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3850.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3C90.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3CC0.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3CF0.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D20.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D50.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D80.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4730.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4A70.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4AA0.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4AD0.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B10.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B50.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B90.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4E80.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6130.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6160.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6190.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6440.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6600.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7310.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7340.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7800.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7830.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7860.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7890.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C78C0.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8200.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8230.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8260.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8290.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C82C0.cpp" />
    <ClCompile Include="Source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8390.cpp" />
    <ClCompile Include="Source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C40F0.cpp" />
    <ClCompile Include="Source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C4120.cpp" />
    <ClCompile Include="Source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C4150.cpp" />
    <ClCompile Include="Source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5150.cpp" />
    <ClCompile Include="Source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5180.cpp" />
    <ClCompile Include="Source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C51B0.cpp" />
    <ClCompile Include="Source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5860.cpp" />
    <ClCompile Include="Source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5890.cpp" />
    <ClCompile Include="Source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C58C0.cpp" />
    <ClCompile Include="Source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5920.cpp" />
    <ClCompile Include="Source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C68B0.cpp" />
    <ClCompile Include="Source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C6B80.cpp" />
    <ClCompile Include="Source\_std_Construct_stdlist_stdpair_int_const__CAsyncLo_1403C8C00.cpp" />
    <ClCompile Include="Source\_std_Uninit_copy_stdlist_stdpair_int_const__CAsync_1403C8E60.cpp" />
    <ClCompile Include="Source\_std_Uninit_fill_n_stdlist_stdpair_int_const__CAsy_1403C8920.cpp" />
    <ClCompile Include="Source\_std_Vector_iterator_stdlist_stdpair_int_const__CA_1403C5D10.cpp" />
    <ClCompile Include="Source\_std_Vector_iterator_stdlist_stdpair_int_const__CA_1403C5D40.cpp" />
    <ClCompile Include="Source\_TidylistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C4CC0.cpp" />
    <ClCompile Include="Source\_TidyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C5240.cpp" />
    <ClCompile Include="Source\_UfillvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C6A60.cpp" />
    <ClCompile Include="Source\_UmovePEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C7A30.cpp" />
    <ClCompile Include="Source\_Unchecked_move_backwardPEAV_Iterator0AlistUpairCB_1403C7B00.cpp" />
    <ClCompile Include="Source\_Unchecked_uninitialized_movePEAV_Iterator0AlistUp_1403C85D0.cpp" />
    <ClCompile Include="Source\_Uninit_copyPEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8DD0.cpp" />
    <ClCompile Include="Source\_Uninit_fill_nPEAV_Iterator0AlistUpairCBHPEAVCAsyn_1403C8890.cpp" />
    <ClCompile Include="Source\_Uninit_movePEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8A60.cpp" />
    <ClCompile Include="Source\_ValidateImageBase_1404DE4C0.cpp" />
    <ClCompile Include="Source\_XlenvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C6AF0.cpp" />
  </ItemGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>