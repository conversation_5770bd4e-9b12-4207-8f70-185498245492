#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::erase_::_1_::dtor$2
 * Address: 0x1403B0960

void  std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::erase_::_1_::dtor_2(int64_t a1, int64_t a2)
{
  if ( *(uint32_t*)(a2 + 40) & 1 )
  {
    *(uint32_t*)(a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::~_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(*(std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > **)(a2 + 88));
  }
}
