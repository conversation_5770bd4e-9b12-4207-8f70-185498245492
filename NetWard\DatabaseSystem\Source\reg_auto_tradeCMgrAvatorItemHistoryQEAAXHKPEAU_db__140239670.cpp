#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?reg_auto_trade@CMgrAvatorItemHistory@@QEAAXHKPEAU_db_con@_STORAGE_LIST@@KKKPEAD@Z
 * Address: 0x140239670

void  CMgrAvatorItemHistory::reg_auto_trade(CMgrAvatorItemHistory *this, int n, unsigned int dwRegistSerial, _STORAGE_LIST::_db_con *pRegItem, unsigned int dwPrice, unsigned int dwfee, unsigned int dwLeftDalant, char *pszFileName)
{
  int64_t *v8; // rdi@1
  signed int64_t i; // rcx@1
  char *v10; // rax@4
  int64_t v11; // [sp+0h] [bp-88h]@1
  unsigned int64_t v12; // [sp+20h] [bp-68h]@4
  char *v13; // [sp+28h] [bp-60h]@4
  unsigned int64_t v14; // [sp+30h] [bp-58h]@4
  unsigned int v15; // [sp+38h] [bp-50h]@4
  unsigned int v16; // [sp+40h] [bp-48h]@4
  unsigned int v17; // [sp+48h] [bp-40h]@4
  char *v18; // [sp+50h] [bp-38h]@4
  char *v19; // [sp+58h] [bp-30h]@4
  _base_fld *v20; // [sp+60h] [bp-28h]@4
  char *v21; // [sp+68h] [bp-20h]@4
  char *v22; // [sp+70h] [bp-18h]@4
  int nTableCode; // [sp+78h] [bp-10h]@4
  CMgrAvatorItemHistory *v24; // [sp+90h] [bp+8h]@1
  unsigned int v25; // [sp+A0h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v26; // [sp+A8h] [bp+20h]@1

  v26 = pRegItem;
  v25 = dwRegistSerial;
  v24 = this;
  v8 = &v11;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t*)v8 = -*********;
    v8 = (int64_t *)((char *)v8 + 4);
  }
  v20 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pRegItem->m_byTableCode, pRegItem->m_wItemIndex);
  v21 = v24->m_szCurTime;
  v22 = v24->m_szCurDate;
  nTableCode = v26->m_byTableCode;
  v10 = DisplayItemUpgInfo(nTableCode, v26->m_dwLv);
  v19 = v21;
  v18 = v22;
  v17 = dwLeftDalant;
  v16 = dwfee;
  v15 = dwPrice;
  v14 = v26->m_lnUID;
  v13 = v10;
  v12 = v26->m_dwDur;
  sprintf(sData, "REG_AUTO_TRADE: reg(%u) %s_%u_@%s[%I64u] pr(D:%u), tax(D:%u) $D:%u [%s %s]\r\n", v25, v20->m_strCode);
  CMgrAvatorItemHistory::WriteFile(v24, pszFileName, sData);
}
