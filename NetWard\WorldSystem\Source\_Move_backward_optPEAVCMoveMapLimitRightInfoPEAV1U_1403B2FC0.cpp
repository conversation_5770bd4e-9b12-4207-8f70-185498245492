#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Move_backward_opt@PEAVCMoveMapLimitRightInfo@@PEAV1@Urandom_access_iterator_tag@std@@U_Undefined_move_tag@3@@std@@YAPEAVCMoveMapLimitRightInfo@@PEAV1@00Urandom_access_iterator_tag@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403B2FC0

CMoveMapLimitRightInfo * std::_Move_backward_opt<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::random_access_iterator_tag,std::_Undefined_move_tag>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Dest, std::random_access_iterator_tag _First_dest_cat, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  int64_t *v6; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v9; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v10; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v11; // [sp+31h] [bp-17h]@4
  CMoveMapLimitRightInfo *__formala; // [sp+50h] [bp+8h]@1
  CMoveMapLimitRightInfo *_Lasta; // [sp+58h] [bp+10h]@1
  CMoveMapLimitRightInfo *_Desta; // [sp+60h] [bp+18h]@1
  std::input_iterator_tag v15; // [sp+68h] [bp+20h]@1

  v15 = _First_dest_cat.0;
  _Desta = _Dest;
  _Lasta = _Last;
  __formala = _First;
  v6 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v6 = -858993460;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  memset(&v10, 0, sizeof(v10));
  v11 = std::_Ptr_cat<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *>(&__formala, &_Desta);
  return std::_Copy_backward_opt<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::random_access_iterator_tag>(
           __formala,
           _Lasta,
           _Desta,
           (std::random_access_iterator_tag)v15,
           v11,
           v10);
}
