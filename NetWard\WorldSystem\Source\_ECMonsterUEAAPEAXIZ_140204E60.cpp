#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??_ECMonster@@UEAAPEAXI@Z
 * Address: 0x140204E60

void * CMonster::`vector deleting destructor'(CMonster *this, int a2)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  void *result; // rax@7
  int64_t v5; // [sp+0h] [bp-28h]@1
  CMonster *ptr; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  ptr = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( a2 & 2 )
  {
    `eh vector destructor iterator'(
      ptr,
      0x1918ui64,
      (int)ptr[-1].m_LuaSignalReActor.m_ActionData[2].m_pLuaCommandEx,
      (void ( *)(void *))CMonster::~CMonster);
    if ( v7 & 1 )
      operator delete[](&ptr[-1].m_LuaSignalReActor.m_ActionData[2].m_pLuaCommandEx);
    result = &ptr[-1].m_LuaSignalReActor.m_ActionData[2].m_pLuaCommandEx;
  }
  else
  {
    CMonster::~CMonster(ptr);
    if ( v7 & 1 )
      operator delete(ptr);
    result = ptr;
  }
  return result;
}
