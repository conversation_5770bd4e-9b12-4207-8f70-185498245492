#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?Update_PostRegistry@CRFWorldDatabase@@QEAA_NKKPEAD000H_KKKEE1@Z
 * Address: 0x140002D2E

bool  CRFWorldDatabase::Update_PostRegistry(CRFWorldDatabase *this, unsigned int dwIndex, unsigned int dwSenderSerial, char *wszSendName, char *wszRecvName, char *wszTitle, char *wszContent, int nK, unsigned int64_t dwD, unsigned int dwU, unsigned int dwGold, char bySendRace, char bySenderDgr, unsigned int64_t lnUID)
{
  return CRFWorldDatabase::Update_PostRegistry(
           this,
           dwIndex,
           dwSenderSerial,
           wszSendName,
           wszRecvName,
           wszTitle,
           wszContent,
           nK,
           dwD,
           dwU,
           dwGold,
           bySendRace,
           bySenderDgr,
           lnUID);
}
