#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Ptr_cat@PEAPEAVCMoveMapLimitInfo@@PEAPEAV1@@std@@YA?AU_Scalar_ptr_iterator_tag@0@AEAPEAPEAVCMoveMapLimitInfo@@0@Z
 * Address: 0x140011D1A

std::_Scalar_ptr_iterator_tag  std::_Ptr_cat<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *>(CMoveMapLimitInfo ***__formal, CMoveMapLimitInfo ***a2)
{
  return std::_Ptr_cat<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *>(__formal, a2);
}
