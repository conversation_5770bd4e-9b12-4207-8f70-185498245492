#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CUnmannedTraderClassInfoTableCodeType::_CUnmannedTraderClassInfoTableCodeType_::_1_::dtor$0
 * Address: 0x140377160

void  CUnmannedTraderClassInfoTableCodeType::_CUnmannedTraderClassInfoTableCodeType_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  CUnmannedTraderClassInfoTableType::~CUnmannedTraderClassInfoTableType(*(CUnmannedTraderClassInfoTableType **)(a2 + 64));
}
