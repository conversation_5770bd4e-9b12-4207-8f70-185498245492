#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?deallocate@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAXPEAVCMoveMapLimitRightInfo@@_K@Z
 * Address: 0x140003391

void  std::allocator<CMoveMapLimitRightInfo>::deallocate(std::allocator<CMoveMapLimitRightInfo> *this, CMoveMapLimitRightInfo *_Ptr, unsigned int64_t __formal)
{
  std::allocator<CMoveMapLimitRightInfo>::deallocate(this, _Ptr, __formal);
}
