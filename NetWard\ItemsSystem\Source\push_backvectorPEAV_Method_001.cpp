#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?push_back@?$vector@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@QEAAXAEBQEAVCUnmannedTraderDivisionInfo@@@Z
 * Address: 0x140387D30

void  std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::push_back(std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this, CUnmannedTraderDivisionInfo *const *_Val)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  unsigned int64_t v4; // rax@4
  int64_t v5; // [sp+0h] [bp-78h]@1
  char v6; // [sp+20h] [bp-58h]@6
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *result; // [sp+38h] [bp-40h]@6
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > v8; // [sp+40h] [bp-38h]@6
  unsigned int64_t v9; // [sp+58h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *v10; // [sp+60h] [bp-18h]@6
  std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *v11; // [sp+80h] [bp+8h]@1
  CUnmannedTraderDivisionInfo **_Vala; // [sp+88h] [bp+10h]@1

  _Vala = (CUnmannedTraderDivisionInfo **)_Val;
  v11 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v9 = std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::size(v11);
  v4 = std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::capacity(v11);
  if ( v9 >= v4 )
  {
    result = (std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)&v6;
    v10 = std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::end(
            v11,
            (std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)&v6);
    std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::insert(
      v11,
      &v8,
      v10,
      _Vala);
    std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&v8);
  }
  else
  {
    v11->_Mylast = std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Ufill(
                     v11,
                     v11->_Mylast,
                     1ui64,
                     _Vala);
  }
}
