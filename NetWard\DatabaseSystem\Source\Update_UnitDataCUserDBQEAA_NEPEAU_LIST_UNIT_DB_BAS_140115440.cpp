#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_UnitData@CUserDB@@QEAA_NEPEAU_LIST@_UNIT_DB_BASE@@@Z
 * Address: 0x140115440

char  CUserDB::Update_UnitData(CUserDB *this, char bySlotIndex, _UNIT_DB_BASE::_LIST *pData)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-28h]@1
  CUserDB *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned int8_t)bySlotIndex < 4 )
  {
    if ( v7->m_AvatorData.dbUnit.m_List[(unsigned int8_t)bySlotIndex].byFrame == 255 )
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_UnitData(EXIST) : slot : %d",
        v7->m_aszAvatorName,
        (unsigned int8_t)bySlotIndex);
      result = 0;
    }
    else
    {
      memcpy_0((char *)&v7->m_AvatorData.dbUnit + 62 * (unsigned int8_t)bySlotIndex, pData, 0x3Eui64);
      v7->m_bDataUpdate = 1;
      result = 1;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_UnitData(SlotIndex OVER) : slot : %d",
      v7->m_aszAvatorName,
      (unsigned int8_t)bySlotIndex);
    result = 0;
  }
  return result;
}
