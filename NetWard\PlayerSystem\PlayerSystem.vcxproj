<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>

  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{H8I9J0K1-L2M3-4567-1234-************}</ProjectGuid>
    <RootNamespace>PlayerSystem</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemGroup>
    <ClCompile Include="Source\0allocatorUBaseAndExponentUEC2NPointCryptoPPVInteg_140594740.cpp" />
    <ClCompile Include="Source\0allocatorUBaseAndExponentUEC2NPointCryptoPPVInteg_1405973C0.cpp" />
    <ClCompile Include="Source\0allocatorUBaseAndExponentUECPPointCryptoPPVIntege_140594F20.cpp" />
    <ClCompile Include="Source\0allocatorUBaseAndExponentUECPPointCryptoPPVIntege_140597D60.cpp" />
    <ClCompile Include="Source\0allocatorUBaseAndExponentVIntegerCryptoPPV12Crypt_140593420.cpp" />
    <ClCompile Include="Source\0allocatorUBaseAndExponentVIntegerCryptoPPV12Crypt_1405965D0.cpp" />
    <ClCompile Include="Source\0AssignFromHelperClassVDL_GroupParameters_IntegerB_14058BD20.cpp" />
    <ClCompile Include="Source\0BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058E0E0.cpp" />
    <ClCompile Include="Source\0BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_140598240.cpp" />
    <ClCompile Include="Source\0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058E630.cpp" />
    <ClCompile Include="Source\0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_1405982D0.cpp" />
    <ClCompile Include="Source\0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_140613C00.cpp" />
    <ClCompile Include="Source\0BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEB_14058D760.cpp" />
    <ClCompile Include="Source\0BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEB_1405981B0.cpp" />
    <ClCompile Include="Source\0CashChangeStateFlagCPlayerQEAAHZ_140073F40.cpp" />
    <ClCompile Include="Source\0CAttackQEAAPEAVCCharacterZ_140169480.cpp" />
    <ClCompile Include="Source\0CCharacterQEAAXZ_140172230.cpp" />
    <ClCompile Include="Source\0CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E5E0.cpp" />
    <ClCompile Include="Source\0CLevelQEAAXZ_1404E2C40.cpp" />
    <ClCompile Include="Source\0CMonsterAttackQEAAPEAVCCharacterZ_14014F8E0.cpp" />
    <ClCompile Include="Source\0CMonsterSkillPoolQEAAXZ_14014B4B0.cpp" />
    <ClCompile Include="Source\0CMonsterSkillQEAAXZ_14014B5A0.cpp" />
    <ClCompile Include="Source\0CPartyModeKillMonsterExpNotifyQEAAXZ_14008E4F0.cpp" />
    <ClCompile Include="Source\0CPartyPlayerQEAAXZ_140044C10.cpp" />
    <ClCompile Include="Source\0CPlayerAttackQEAAPEAVCCharacterZ_14008EBF0.cpp" />
    <ClCompile Include="Source\0CPlayerDBQEAAXZ_1401087E0.cpp" />
    <ClCompile Include="Source\0CPlayerQEAAXZ_1400478B0.cpp" />
    <ClCompile Include="Source\0CRaceBuffInfoByHolyQuestQEAAPEAU_skill_fldEZ_1403B3EA0.cpp" />
    <ClCompile Include="Source\0CReturnGateCreateParamQEAAPEAVCPlayerZ_1401684E0.cpp" />
    <ClCompile Include="Source\0cStaticMember_PlayerAEAAXZ_14010E3D0.cpp" />
    <ClCompile Include="Source\0CUnmannedTraderSubClassInfoLevelQEAAKZ_140384070.cpp" />
    <ClCompile Include="Source\0DL_GroupParametersImplVModExpPrecomputationCrypto_14055E880.cpp" />
    <ClCompile Include="Source\0DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA90.cpp" />
    <ClCompile Include="Source\0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E270.cpp" />
    <ClCompile Include="Source\0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E330.cpp" />
    <ClCompile Include="Source\0GetValueHelperClassVDL_GroupParameters_IntegerBas_14058BA50.cpp" />
    <ClCompile Include="Source\0LendItemSheetAEAAPEAVCPlayerZ_14030EDD0.cpp" />
    <ClCompile Include="Source\0ModExpPrecomputationCryptoPPQEAAAEBV01Z_14055F500.cpp" />
    <ClCompile Include="Source\0ModExpPrecomputationCryptoPPQEAAXZ_14055F0A0.cpp" />
    <ClCompile Include="Source\0SKILLQEAAXZ_14012CC10.cpp" />
    <ClCompile Include="Source\0TimeLimitJadeAEAAPEAVCPlayerZ_1403FB400.cpp" />
    <ClCompile Include="Source\0vectorUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058DCF0.cpp" />
    <ClCompile Include="Source\0vectorUBaseAndExponentUECPPointCryptoPPVInteger2C_14058E240.cpp" />
    <ClCompile Include="Source\0vectorUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058D370.cpp" />
    <ClCompile Include="Source\0_attack_skill_result_zoclQEAAXZ_1400EED50.cpp" />
    <ClCompile Include="Source\0_be_damaged_playerQEAAXZ_14013E450.cpp" />
    <ClCompile Include="Source\0_character_create_setdataQEAAXZ_140078E20.cpp" />
    <ClCompile Include="Source\0_character_db_loadQEAAXZ_14010E010.cpp" />
    <ClCompile Include="Source\0_dh_player_mgrQEAAXZ_14026EA90.cpp" />
    <ClCompile Include="Source\0_nuclear_bomb_explosion_result_zoclQEAAXZ_14013E680.cpp" />
    <ClCompile Include="Source\0_nuclear_explosion_success_zoclQEAAXZ_14013E6E0.cpp" />
    <ClCompile Include="Source\0_qry_case_character_renameQEAAXZ_1400B85B0.cpp" />
    <ClCompile Include="Source\0_RanitUBaseAndExponentUEC2NPointCryptoPPVInteger2_140595570.cpp" />
    <ClCompile Include="Source\0_RanitUBaseAndExponentUEC2NPointCryptoPPVInteger2_1405982B0.cpp" />
    <ClCompile Include="Source\0_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_1405955A0.cpp" />
    <ClCompile Include="Source\0_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_140598340.cpp" />
    <ClCompile Include="Source\0_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_140595510.cpp" />
    <ClCompile Include="Source\0_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_140598220.cpp" />
    <ClCompile Include="Source\0_SKILL_IDX_PER_MASTERYQEAAXZ_14007F1E0.cpp" />
    <ClCompile Include="Source\0_target_player_damage_contsf_allinform_zoclQEAAXZ_1400740C0.cpp" />
    <ClCompile Include="Source\0_throw_skill_result_one_zoclQEAAXZ_1400EFD70.cpp" />
    <ClCompile Include="Source\0_throw_skill_result_other_zoclQEAAXZ_1400EFD90.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorUBaseAndExponentUEC2NPointC_140595460.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorUBaseAndExponentUEC2NPointC_140597500.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorUBaseAndExponentUECPPointCr_1405954A0.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorUBaseAndExponentUECPPointCr_140597EA0.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405953E0.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorUBaseAndExponentVIntegerCry_140596710.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_1405947A0.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_140595340.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_140594F80.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_140595370.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_140593480.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_1405952E0.cpp" />
    <ClCompile Include="Source\0_Vector_valUBaseAndExponentUEC2NPointCryptoPPVInt_140594700.cpp" />
    <ClCompile Include="Source\0_Vector_valUBaseAndExponentUECPPointCryptoPPVInte_140594EE0.cpp" />
    <ClCompile Include="Source\0_Vector_valUBaseAndExponentVIntegerCryptoPPV12Cry_1405933E0.cpp" />
    <ClCompile Include="Source\0__targetCPlayerQEAAXZ_140073F80.cpp" />
    <ClCompile Include="Source\1BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058A530.cpp" />
    <ClCompile Include="Source\1BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058A5B0.cpp" />
    <ClCompile Include="Source\1BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAXZ_14058A470.cpp" />
    <ClCompile Include="Source\1CCharacterUEAAXZ_140172360.cpp" />
    <ClCompile Include="Source\1CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E610.cpp" />
    <ClCompile Include="Source\1CLevelUEAAXZ_1404E2D90.cpp" />
    <ClCompile Include="Source\1CMonsterSkillPoolQEAAXZ_14014B600.cpp" />
    <ClCompile Include="Source\1CMonsterSkillQEAAXZ_14014B5F0.cpp" />
    <ClCompile Include="Source\1CPartyModeKillMonsterExpNotifyQEAAXZ_14008E580.cpp" />
    <ClCompile Include="Source\1CPlayerDBQEAAXZ_140108AA0.cpp" />
    <ClCompile Include="Source\1CPlayerUEAAXZ_140048050.cpp" />
    <ClCompile Include="Source\1cStaticMember_PlayerAEAAXZ_14010E3F0.cpp" />
    <ClCompile Include="Source\1CUnmannedTraderSubClassInfoLevelQEAAXZ_1403840F0.cpp" />
    <ClCompile Include="Source\1DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA00.cpp" />
    <ClCompile Include="Source\1DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E310.cpp" />
    <ClCompile Include="Source\1ModExpPrecomputationCryptoPPQEAAXZ_14055F280.cpp" />
    <ClCompile Include="Source\1vectorUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058DD30.cpp" />
    <ClCompile Include="Source\1vectorUBaseAndExponentUECPPointCryptoPPVInteger2C_14058E280.cpp" />
    <ClCompile Include="Source\1vectorUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058D3B0.cpp" />
    <ClCompile Include="Source\1_RanitUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058A6D0.cpp" />
    <ClCompile Include="Source\1_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_14058A6F0.cpp" />
    <ClCompile Include="Source\1_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058A6B0.cpp" />
    <ClCompile Include="Source\1_Vector_const_iteratorUBaseAndExponentUEC2NPointC_14058A670.cpp" />
    <ClCompile Include="Source\1_Vector_const_iteratorUBaseAndExponentUECPPointCr_14058A690.cpp" />
    <ClCompile Include="Source\1_Vector_const_iteratorUBaseAndExponentVIntegerCry_14058A650.cpp" />
    <ClCompile Include="Source\1_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_14058A510.cpp" />
    <ClCompile Include="Source\1_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_14058A590.cpp" />
    <ClCompile Include="Source\1_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_14058A450.cpp" />
    <ClCompile Include="Source\4BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_1405A5010.cpp" />
    <ClCompile Include="Source\4BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_1405A5060.cpp" />
    <ClCompile Include="Source\4BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEA_1405A4FC0.cpp" />
    <ClCompile Include="Source\4CUnmannedTraderSubClassInfoLevelQEAAAEBV0AEBV0Z_140384140.cpp" />
    <ClCompile Include="Source\4DL_GroupParametersImplVModExpPrecomputationCrypto_14055E150.cpp" />
    <ClCompile Include="Source\4DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055D470.cpp" />
    <ClCompile Include="Source\4ModExpPrecomputationCryptoPPQEAAAEAV01AEBV01Z_14055E670.cpp" />
    <ClCompile Include="Source\8DL_GroupParameters_IntegerBasedImplVModExpPrecomp_140552660.cpp" />
    <ClCompile Include="Source\8_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405A5680.cpp" />
    <ClCompile Include="Source\8_Vector_const_iteratorUBaseAndExponentUECPPointCr_1405A56C0.cpp" />
    <ClCompile Include="Source\8_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405A5640.cpp" />
    <ClCompile Include="Source\9DL_GroupParameters_IntegerBasedImplVModExpPrecomp_140552760.cpp" />
    <ClCompile Include="Source\9_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405A5400.cpp" />
    <ClCompile Include="Source\9_Vector_const_iteratorUBaseAndExponentUECPPointCr_1405A54E0.cpp" />
    <ClCompile Include="Source\9_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405A5280.cpp" />
    <ClCompile Include="Source\AccessBasePrecomputationDL_GroupParametersImplVMod_14055E9F0.cpp" />
    <ClCompile Include="Source\AccessBasePrecomputationDL_GroupParameters_Integer_140552450.cpp" />
    <ClCompile Include="Source\AddCPartyModeKillMonsterExpNotifyQEAA_NPEAVCPlayer_1401692F0.cpp" />
    <ClCompile Include="Source\AddDalantCPlayerQEAAXK_NZ_140055B00.cpp" />
    <ClCompile Include="Source\AddGoldCPlayerQEAAXK_NZ_140055D30.cpp" />
    <ClCompile Include="Source\AddTrunkDalantCPlayerDBQEAAXKZ_14010C200.cpp" />
    <ClCompile Include="Source\AddTrunkGoldCPlayerDBQEAAXKZ_14010C330.cpp" />
    <ClCompile Include="Source\adjust_effectCGuildMasterEffectAEAAXPEAVCPlayerE_N_1403F4B10.cpp" />
    <ClCompile Include="Source\allocateallocatorUBaseAndExponentUEC2NPointCryptoP_140594780.cpp" />
    <ClCompile Include="Source\allocateallocatorUBaseAndExponentUECPPointCryptoPP_140594F60.cpp" />
    <ClCompile Include="Source\allocateallocatorUBaseAndExponentVIntegerCryptoPPV_140593460.cpp" />
    <ClCompile Include="Source\AlterContDurSecCCharacterQEAAXEGKGZ_140174D50.cpp" />
    <ClCompile Include="Source\AlterDalantCPlayerQEAAXNZ_1400F7A70.cpp" />
    <ClCompile Include="Source\AlterExpCAnimusQEAAX_JZ_1401265A0.cpp" />
    <ClCompile Include="Source\AlterExpCPlayerQEAAXN_N00Z_14005BB50.cpp" />
    <ClCompile Include="Source\AlterExp_AnimusCPlayerQEAAX_JZ_1400D0E00.cpp" />
    <ClCompile Include="Source\AlterExp_MasterReportCAnimusQEAAX_JZ_1401292B0.cpp" />
    <ClCompile Include="Source\AlterExp_PotionCPlayerQEAAXNZ_14005C550.cpp" />
    <ClCompile Include="Source\AlterFP_AnimusCPlayerQEAAXHZ_1400D0D40.cpp" />
    <ClCompile Include="Source\AlterGoldCPlayerQEAAXNZ_1400F7B20.cpp" />
    <ClCompile Include="Source\AlterHP_AnimusCPlayerQEAAXHZ_1400D0C80.cpp" />
    <ClCompile Include="Source\AlterMaxLevelCPlayerQEAAXEZ_1400D11A0.cpp" />
    <ClCompile Include="Source\AlterMode_AnimusCPlayerQEAAXEZ_1400D0FD0.cpp" />
    <ClCompile Include="Source\AlterPvPCashBagCPlayerQEAAXNW4PVP_MONEY_ALTER_TYPE_14005F990.cpp" />
    <ClCompile Include="Source\AlterPvPPointCPlayerQEAAXNW4PVP_ALTER_TYPEKZ_14005F660.cpp" />
    <ClCompile Include="Source\AlterPvpPointLeakCPlayerQEAAXNZ_140068F30.cpp" />
    <ClCompile Include="Source\AlterSecCPlayerUEAAXXZ_1400554B0.cpp" />
    <ClCompile Include="Source\alter_pvpCMgrAvatorLvHistoryQEAAXHNPEAVCPartyPlaye_140245E00.cpp" />
    <ClCompile Include="Source\ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAVCPla_1403B4C70.cpp" />
    <ClCompile Include="Source\ApplyCRaceBuffInfoByHolyQuestListQEAA_NIHPEAVCPlay_1403B52B0.cpp" />
    <ClCompile Include="Source\ApplyCRaceBuffInfoByHolyQuestQEAA_NPEAVCPlayerZ_1403B4080.cpp" />
    <ClCompile Include="Source\ApplyEffectCRaceBuffInfoByHolyQuestAEAA_NPEAVCPlay_1403B4160.cpp" />
    <ClCompile Include="Source\ApplyEquipItemEffectCPlayerQEAA_NH_NZ_140062FC0.cpp" />
    <ClCompile Include="Source\ApplyPotionCPotionMgrQEAAHPEAVCPlayer0PEAU_skill_f_14039E6D0.cpp" />
    <ClCompile Include="Source\ApplySetItemEffectCPlayerQEAAXPEAVsi_interpretKEE__140063130.cpp" />
    <ClCompile Include="Source\apply_case_equip_std_effectCPlayerQEAAXPEAU_db_con_140061A60.cpp" />
    <ClCompile Include="Source\apply_case_equip_upgrade_effectCPlayerQEAAXPEAU_db_1400623C0.cpp" />
    <ClCompile Include="Source\apply_have_item_std_effectCPlayerQEAAXHM_NHZ_140051850.cpp" />
    <ClCompile Include="Source\apply_normal_item_std_effectCPlayerQEAAXHM_NZ_140061B10.cpp" />
    <ClCompile Include="Source\AppointPatriarchGroupCandidateMgrQEAA_NPEAVCPlayer_1402B4C20.cpp" />
    <ClCompile Include="Source\AppointSerialStorageItemCPlayerDBQEAAXXZ_14010B950.cpp" />
    <ClCompile Include="Source\AssignableGetValueHelperClassVDL_GroupParameters_I_1405616C0.cpp" />
    <ClCompile Include="Source\AssignFromDL_GroupParameters_IntegerBasedImplVModE_140552400.cpp" />
    <ClCompile Include="Source\AssignFromDL_GroupParameters_IntegerBasedImplVModE_1405ADEF0.cpp" />
    <ClCompile Include="Source\AssignFromHelperVDL_GroupParameters_IntegerBasedCr_1405894E0.cpp" />
    <ClCompile Include="Source\AssistForceCCharacterQEAA_NPEAV1PEAU_force_fldHPEA_140175630.cpp" />
    <ClCompile Include="Source\AssistForceToOneCCharacterQEAA_NPEAV1PEAU_force_fl_1401764F0.cpp" />
    <ClCompile Include="Source\AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14014CE30.cpp" />
    <ClCompile Include="Source\AssistSkillCCharacterQEAA_NPEAV1HPEAU_skill_fldHPE_140175D80.cpp" />
    <ClCompile Include="Source\AssistSkillToOneCCharacterQEAA_NPEAV1HPEAU_skill_f_140176970.cpp" />
    <ClCompile Include="Source\AttackableHeightCPlayerUEAAHXZ_140061490.cpp" />
    <ClCompile Include="Source\AttackCGuardTowerQEAAXPEAVCCharacterZ_14012F840.cpp" />
    <ClCompile Include="Source\AttackCMonsterQEAAHPEAVCCharacterPEAVCMonsterSkill_14014E4C0.cpp" />
    <ClCompile Include="Source\AttackCTrapQEAAXPEAVCCharacterZ_14013EBF0.cpp" />
    <ClCompile Include="Source\AttackMonsterSkillCMonsterAttackQEAAXPEAU_attack_p_14015ABD0.cpp" />
    <ClCompile Include="Source\AttackSkillCPlayerAttackQEAAXPEAU_attack_param_NZ_14016E140.cpp" />
    <ClCompile Include="Source\AttackSkillRequestCNetworkEXAEAA_NHPEADZ_1401C17F0.cpp" />
    <ClCompile Include="Source\AttackUnitCPlayerAttackQEAAXPEAU_attack_paramZ_14016F330.cpp" />
    <ClCompile Include="Source\AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAVCPlayerZ_14028F380.cpp" />
    <ClCompile Include="Source\AutoCharge_BoosterCPlayerQEAAXXZ_1400570B0.cpp" />
    <ClCompile Include="Source\AutoRecoverCPlayerQEAAXXZ_140056270.cpp" />
    <ClCompile Include="Source\AutoRecover_AnimusCPlayerQEAAXXZ_140056EE0.cpp" />
    <ClCompile Include="Source\AvectorUBaseAndExponentUECPPointCryptoPPVInteger2C_140612C00.cpp" />
    <ClCompile Include="Source\a_exp_b_mod_cCryptoPPYAAVInteger1AEBV2100Z_1405EB3C0.cpp" />
    <ClCompile Include="Source\beginvectorUBaseAndExponentUEC2NPointCryptoPPVInte_14058DFA0.cpp" />
    <ClCompile Include="Source\beginvectorUBaseAndExponentUECPPointCryptoPPVInteg_14058E4F0.cpp" />
    <ClCompile Include="Source\beginvectorUBaseAndExponentVIntegerCryptoPPV12Cryp_14058D620.cpp" />
    <ClCompile Include="Source\BeHaveBoxOfAMPCPlayerDBQEAA_NXZ_1400EF400.cpp" />
    <ClCompile Include="Source\BERDecodeElementModExpPrecomputationCryptoPPUEBAAV_14055F210.cpp" />
    <ClCompile Include="Source\BeTargetedCGameObjectUEAAXPEAVCCharacterZ_140072B50.cpp" />
    <ClCompile Include="Source\BeTargetedCMonsterUEAAXPEAVCCharacterZ_1401427B0.cpp" />
    <ClCompile Include="Source\BGetValueHelperClassVDL_GroupParameters_IntegerBas_1405616B0.cpp" />
    <ClCompile Include="Source\BillingExpireIPOverflowCNetworkEXAEAA_NHPEADZ_1401C3C80.cpp" />
    <ClCompile Include="Source\BillingExpirePCBangCNetworkEXAEAA_NHPEADZ_1401C3C20.cpp" />
    <ClCompile Include="Source\BillingExpirePersonalCNetworkEXAEAA_NHPEADZ_1401C3BC0.cpp" />
    <ClCompile Include="Source\Billing_LogoutCPlayerQEAAXXZ_140067CA0.cpp" />
    <ClCompile Include="Source\BreakCloakBoosterCPlayerQEAAXXZ_140064590.cpp" />
    <ClCompile Include="Source\BreakStealthCCharacterQEAAXXZ_140175460.cpp" />
    <ClCompile Include="Source\BuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCPlayer_1403568C0.cpp" />
    <ClCompile Include="Source\CalcAddPointByClassCPlayerQEAAXXZ_140064630.cpp" />
    <ClCompile Include="Source\CalcAttExpCAnimusQEAAXPEAVCAttackZ_140126F40.cpp" />
    <ClCompile Include="Source\CalcCharGradeCPlayerDBSAEEGZ_14010BC80.cpp" />
    <ClCompile Include="Source\CalcCirclePlayerNumCGameObjectQEAAHHP6A_NPEAV1ZZ_14017C720.cpp" />
    <ClCompile Include="Source\CalcCirclePlayerNumCGameObjectQEAAHHZ_14017C2E0.cpp" />
    <ClCompile Include="Source\CalcCurFPRateCPlayerQEAAGXZ_1400EFBD0.cpp" />
    <ClCompile Include="Source\CalcCurHPRateCPlayerUEAAGXZ_14005F560.cpp" />
    <ClCompile Include="Source\CalcCurSPRateCPlayerQEAAGXZ_1400EFC40.cpp" />
    <ClCompile Include="Source\CalcDefExpCAnimusQEAAXPEAVCCharacterHZ_140126DA0.cpp" />
    <ClCompile Include="Source\CalcDefTolCPlayerQEAAXXZ_140050720.cpp" />
    <ClCompile Include="Source\CalcDistForSecCCharacterQEAAMMMZ_140173360.cpp" />
    <ClCompile Include="Source\CalcDPRateCPlayerQEAAMXZ_14005FB70.cpp" />
    <ClCompile Include="Source\CalcEffectBitCCharacterQEAAGGGZ_14007B990.cpp" />
    <ClCompile Include="Source\CalcEquipAttackDelayCPlayerQEAAHXZ_1400575C0.cpp" />
    <ClCompile Include="Source\CalcEquipMaxDPCPlayerQEAAX_NZ_140057430.cpp" />
    <ClCompile Include="Source\CalcEquipSpeedCPlayerQEAAXXZ_140057220.cpp" />
    <ClCompile Include="Source\CalcExpCPlayerQEAAXPEAVCCharacterHAEAVCPartyModeKi_14005A600.cpp" />
    <ClCompile Include="Source\CalcPvPCPlayerQEAAXPEAV1EZ_14005B4E0.cpp" />
    <ClCompile Include="Source\CalcR3FogCLevelQEAAXXZ_1404E26F0.cpp" />
    <ClCompile Include="Source\CalcStrIndexPitInWidthACR3FontQEAAHPEBDHHZ_140527DD0.cpp" />
    <ClCompile Include="Source\CalcStrIndexPitInWidthWCR3FontQEAAHPEB_WHHZ_140527D80.cpp" />
    <ClCompile Include="Source\CalPvpCashPointCPlayerQEAANHHPEADZ_14005ACE0.cpp" />
    <ClCompile Include="Source\CalPvpTempCashCPlayerQEAAXPEAV1EZ_14005AD50.cpp" />
    <ClCompile Include="Source\CancelCRaceBossMsgControllerQEAA_NEKPEAVCPlayerZ_1402A0A00.cpp" />
    <ClCompile Include="Source\CancelPlayerRaceBuffCRaceBuffByHolyQuestProcedureQ_1403B63D0.cpp" />
    <ClCompile Include="Source\CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAVCPlay_14017FFE0.cpp" />
    <ClCompile Include="Source\CanDoEventRFEvent_ClassRefineEEAAHPEBVCPlayerZ_140328BC0.cpp" />
    <ClCompile Include="Source\CanYouEnterHoleCDarkHoleChannelQEAA_NPEAVCPlayerZ_14026A710.cpp" />
    <ClCompile Include="Source\capacityvectorUBaseAndExponentUEC2NPointCryptoPPVI_140594180.cpp" />
    <ClCompile Include="Source\capacityvectorUBaseAndExponentUECPPointCryptoPPVIn_140594960.cpp" />
    <ClCompile Include="Source\capacityvectorUBaseAndExponentVIntegerCryptoPPV12C_140592E60.cpp" />
    <ClCompile Include="Source\CascadeExponentiateAbstractRingVIntegerCryptoPPCry_14056EA20.cpp" />
    <ClCompile Include="Source\CascadeExponentiateAbstractRingVPolynomialMod2Cryp_140573B10.cpp" />
    <ClCompile Include="Source\CascadeExponentiateBaseAndPublicElementDL_PublicKe_140451380.cpp" />
    <ClCompile Include="Source\CascadeExponentiateBaseAndPublicElementDL_PublicKe_140569750.cpp" />
    <ClCompile Include="Source\CascadeExponentiateBaseAndPublicElementDL_PublicKe_14056A090.cpp" />
    <ClCompile Include="Source\CascadeExponentiateDL_FixedBasePrecomputationImplU_140576490.cpp" />
    <ClCompile Include="Source\CascadeExponentiateDL_FixedBasePrecomputationImplU_140578AF0.cpp" />
    <ClCompile Include="Source\CascadeExponentiateDL_FixedBasePrecomputationImplV_14056F8C0.cpp" />
    <ClCompile Include="Source\CascadeExponentiateDL_GroupParameters_ECVEC2NCrypt_1405842D0.cpp" />
    <ClCompile Include="Source\CascadeExponentiateDL_GroupParameters_ECVECPCrypto_140580280.cpp" />
    <ClCompile Include="Source\CascadeExponentiateDL_GroupParameters_GFPCryptoPPQ_1406320A0.cpp" />
    <ClCompile Include="Source\CascadeExponentiateModularArithmeticCryptoPPUEBAAV_1405ED170.cpp" />
    <ClCompile Include="Source\CascadeExponentiateMontgomeryRepresentationCryptoP_140561080.cpp" />
    <ClCompile Include="Source\ChangeOwnerCParkingUnitQEAAXPEAVCPlayerEZ_140167C30.cpp" />
    <ClCompile Include="Source\ChangeTarget_MasterCommandCAnimusQEAA_NPEAVCCharac_140128E40.cpp" />
    <ClCompile Include="Source\change_playerCGuildMasterEffectQEAA_NPEAVCPlayerEE_1403F4A00.cpp" />
    <ClCompile Include="Source\characteristic_two_fieldASN1CryptoPPYAAVOID2XZ_14062C5E0.cpp" />
    <ClCompile Include="Source\CharacterRenameCashCNetworkEXAEAA_NHPEADZ_1401CBBE0.cpp" />
    <ClCompile Include="Source\CheatCreateFieldObjectCGuildBattleControllerQEAA_N_1403D72D0.cpp" />
    <ClCompile Include="Source\CheatDestroyFieldObjectCGuildBattleControllerQEAA__1403D7380.cpp" />
    <ClCompile Include="Source\CheatDestroyStoneCGuildBattleControllerQEAA_NPEAVC_1403D7220.cpp" />
    <ClCompile Include="Source\CheatDropStoneCGuildBattleControllerQEAA_NPEAVCPla_1403D7780.cpp" />
    <ClCompile Include="Source\CheatDropStoneCNormalGuildBattleFieldGUILD_BATTLEQ_1403ED7C0.cpp" />
    <ClCompile Include="Source\CheatForceTakeStoneCGuildBattleControllerQEAA_NPEA_1403D7850.cpp" />
    <ClCompile Include="Source\CheatForceTakeStoneCNormalGuildBattleFieldGUILD_BA_1403ED8C0.cpp" />
    <ClCompile Include="Source\CheatGetCGravityStoneQEAAEPEAVCPlayerZ_1403F01B0.cpp" />
    <ClCompile Include="Source\CheatGetStoneCGuildBattleControllerQEAA_NPEAVCPlay_1403D75E0.cpp" />
    <ClCompile Include="Source\CheatGetStoneCNormalGuildBattleFieldGUILD_BATTLEQE_1403ED760.cpp" />
    <ClCompile Include="Source\CheatRegenStoneCGuildBattleControllerQEAAHPEAVCPla_1403D70C0.cpp" />
    <ClCompile Include="Source\CheatRegenStoneCNormalGuildBattleFieldGUILD_BATTLE_1403ED550.cpp" />
    <ClCompile Include="Source\CheatSetPatriarchPatriarchElectProcessorQEAA_NPEAV_1402BC3C0.cpp" />
    <ClCompile Include="Source\CheatTakeStoneCGuildBattleControllerQEAA_NHPEAVCPl_1403D7430.cpp" />
    <ClCompile Include="Source\CheatTakeStoneCNormalGuildBattleFieldGUILD_BATTLEQ_1403ED710.cpp" />
    <ClCompile Include="Source\CheckAlterMaxPointCPlayerQEAAXXZ_1400A2420.cpp" />
    <ClCompile Include="Source\CheckBattleModeCPlayerQEAAXXZ_1400685B0.cpp" />
    <ClCompile Include="Source\CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCP_140356460.cpp" />
    <ClCompile Include="Source\CheckBuyCUnmannedTraderUserInfoAEAAEEPEAU_unmanned_14035B7E0.cpp" />
    <ClCompile Include="Source\CheckCouponTypeCashItemRemoteStoreQEAAHPEAU_STORAG_1402F52A0.cpp" />
    <ClCompile Include="Source\CheckCRealMoveRequestDelayCheckerQEAA_NPEAVCPlayer_140122240.cpp" />
    <ClCompile Include="Source\CheckDBValidCharacterCandidateMgrQEAAHEZ_1402B4FB0.cpp" />
    <ClCompile Include="Source\CheckDQSLoadCharacterDataCUserDBSA_NPEAU_AVATOR_DA_140118860.cpp" />
    <ClCompile Include="Source\CheckEmotionStateEmotionPresentationCheckerQEAA_NP_14015D250.cpp" />
    <ClCompile Include="Source\CheckEventEmotionPresentationCMonsterQEAA_NEPEAVCC_140147F20.cpp" />
    <ClCompile Include="Source\CheckGetGravityStoneCGuildBattleControllerQEAAXGKP_1403D60F0.cpp" />
    <ClCompile Include="Source\CheckGoalCGuildBattleControllerQEAAXPEAVCPlayerHZ_1403D61B0.cpp" />
    <ClCompile Include="Source\CheckGroupMapPointCPlayerQEAAXXZ_1400FFA80.cpp" />
    <ClCompile Include="Source\CheckGroupTargetingCPlayerQEAAXXZ_1400FEBC0.cpp" />
    <ClCompile Include="Source\CheckHolyMasterCHolyStoneSystemQEAA_NPEAVCPlayerEZ_14027DD50.cpp" />
    <ClCompile Include="Source\CheckLootItemCMonsterQEAAXPEAVCPlayerZ_140144120.cpp" />
    <ClCompile Include="Source\CheckMentalTakeAndUpdateLastMetalTicketCPlayerQEAA_1400CE2C0.cpp" />
    <ClCompile Include="Source\CheckNameChangeCPlayerQEAAXXZ_140069080.cpp" />
    <ClCompile Include="Source\CheckNuclearStateCNuclearBombMgrQEAAXPEAVCPlayerZ_14013A850.cpp" />
    <ClCompile Include="Source\CheckPlayerStatusTimeLimitMgrQEAA_NGKPEAEPEAKZ_14040E860.cpp" />
    <ClCompile Include="Source\CheckPosInTownCPlayerQEAAXXZ_1400648F0.cpp" />
    <ClCompile Include="Source\CheckPos_RegionCPlayerQEAAXXZ_1400C7C50.cpp" />
    <ClCompile Include="Source\CheckPotionTimeCExtPotionBufQEAAXPEAVCPlayerZ_1403A0050.cpp" />
    <ClCompile Include="Source\CheckPreAttackRangeTargetAbleCharacterCMonsterHelp_140158430.cpp" />
    <ClCompile Include="Source\CheckPvpHaveConditionCPvpCashPointQEAA_NPEAVCPlaye_1403F54F0.cpp" />
    <ClCompile Include="Source\CheckPvpLoseConditionCPvpCashPointQEAA_NPEAVCPlaye_1403F5370.cpp" />
    <ClCompile Include="Source\CheckRegisterCPostSystemManagerQEAAEPEAVCPlayerPEA_140325A50.cpp" />
    <ClCompile Include="Source\CheckSellCompleteCUnmannedTraderUserInfoQEAAEPEAVC_1403564E0.cpp" />
    <ClCompile Include="Source\CheckSPF_MON_MOTIVE_ATTACK_MODE_PASSAGEDfAIMgrSAHP_140152C60.cpp" />
    <ClCompile Include="Source\CheckSPF_MON_MOTIVE_DFDfAIMgrSAHPEAVCMonsterSkillH_1401525F0.cpp" />
    <ClCompile Include="Source\CheckSPF_MON_MOTIVE_MY_HP_DOWNDfAIMgrSAHPEAVCMonst_1401527A0.cpp" />
    <ClCompile Include="Source\CheckSPF_MON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAVCMo_140152900.cpp" />
    <ClCompile Include="Source\CheckTakeGravityStoneCGuildBattleControllerQEAAXHP_1403D6020.cpp" />
    <ClCompile Include="Source\CheckTicket_KickCTransportShipQEAAXPEAVCPlayerHZ_1402642D0.cpp" />
    <ClCompile Include="Source\CheckTicket_PassCTransportShipQEAAXPEAVCPlayerHZ_140264170.cpp" />
    <ClCompile Include="Source\CheckUnitCutTimeCPlayerQEAAXXZ_140106B70.cpp" />
    <ClCompile Include="Source\Cheet_BufEffectEndCPlayerQEAAXXZ_1400A3A00.cpp" />
    <ClCompile Include="Source\ClassCodePasingCPcBangFavorQEAAKPEAU_AVATOR_DATAPE_14040BD90.cpp" />
    <ClCompile Include="Source\ClassSkillRecallTeleportRequestCNetworkEXAEAA_NHPE_1401C3090.cpp" />
    <ClCompile Include="Source\ClassSkillRequestCNetworkEXAEAA_NHPEADZ_1401C2360.cpp" />
    <ClCompile Include="Source\ClearCPvpPointLimiterQEAAX_JNPEAVCPlayerZ_140125850.cpp" />
    <ClCompile Include="Source\ClearGravityStoneCPlayerQEAAXXZ_1400A0520.cpp" />
    <ClCompile Include="Source\ClearMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPEAU_14026AB00.cpp" />
    <ClCompile Include="Source\CombinePreProcessCTalkCrystalCombineManagerIEAAEPE_140430F40.cpp" />
    <ClCompile Include="Source\CompleteUpdatePlayerVoteInfoCMainThreadQEAAXPEADZ_1401FB6E0.cpp" />
    <ClCompile Include="Source\constructallocatorUBaseAndExponentUEC2NPointCrypto_1405A53B0.cpp" />
    <ClCompile Include="Source\constructallocatorUBaseAndExponentUECPPointCryptoP_1405A5490.cpp" />
    <ClCompile Include="Source\constructallocatorUBaseAndExponentVIntegerCryptoPP_1405A5230.cpp" />
    <ClCompile Include="Source\ConsumeMeterial_And_CalculateNewItemsItemCombineMg_1402AC350.cpp" />
    <ClCompile Include="Source\ConvertAvatorDBCPlayerDBQEAA_NPEAU_AVATOR_DATAZ_140108EE0.cpp" />
    <ClCompile Include="Source\ConvertGeneralDBCPlayerDBQEAA_NPEAU_AVATOR_DATA0Z_140109450.cpp" />
    <ClCompile Include="Source\ConvertInModExpPrecomputationCryptoPPUEBAAVInteger_14055F0F0.cpp" />
    <ClCompile Include="Source\ConvertOutModExpPrecomputationCryptoPPUEBAAVIntege_14055F160.cpp" />
    <ClCompile Include="Source\ConvertTargetPlayerCMonsterQEAA_NPEAVCPlayerZ_1401428C0.cpp" />
    <ClCompile Include="Source\CopyCMonsterSkillIEAAXAEBV1Z_140156140.cpp" />
    <ClCompile Include="Source\CorpseCPlayerQEAA_NPEAVCCharacterZ_14004F140.cpp" />
    <ClCompile Include="Source\CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAVCPlayerZ_14012ADE0.cpp" />
    <ClCompile Include="Source\CreateCCharacterQEAA_NPEAU_character_create_setdat_1401725E0.cpp" />
    <ClCompile Include="Source\CreateCharacterSelectLogTableCRFWorldDatabaseQEAA__1404A0BE0.cpp" />
    <ClCompile Include="Source\CreateCompleteCMoveMapLimitManagerQEAAXPEAVCPlayer_1403A1910.cpp" />
    <ClCompile Include="Source\CreateCompleteCMoveMapLimitRightInfoListQEAAXPEAVC_1403ADD30.cpp" />
    <ClCompile Include="Source\CreateCompleteCMoveMapLimitRightInfoQEAAXPEAVCPlay_1403AD150.cpp" />
    <ClCompile Include="Source\CreateCompleteCMoveMapLimitRightPortalUEAAXPEAVCPl_1403AC890.cpp" />
    <ClCompile Include="Source\CreateCompleteCMoveMapLimitRightUEAAXPEAVCPlayerZ_1403AE4D0.cpp" />
    <ClCompile Include="Source\CreateCompleteCNationSettingDataCNUEAAXPEAVCPlayer_140230870.cpp" />
    <ClCompile Include="Source\CreateCompleteCNationSettingDataNULLUEAAXPEAVCPlay_140213090.cpp" />
    <ClCompile Include="Source\CreateCompleteCNationSettingDataUEAAXPEAVCPlayerZ_1402128F0.cpp" />
    <ClCompile Include="Source\CreateCompleteCNationSettingManagerQEAAXPEAVCPlaye_140079A60.cpp" />
    <ClCompile Include="Source\CreateCompleteCPlayerQEAAXXZ_14004B150.cpp" />
    <ClCompile Include="Source\CreateCompleteCRaceBuffByHolyQuestProcedureQEAA_NP_1403B6260.cpp" />
    <ClCompile Include="Source\CreateCompleteCRaceBuffInfoByHolyQuestfGroupQEAA_N_1403B4BD0.cpp" />
    <ClCompile Include="Source\CreateCompleteCRaceBuffInfoByHolyQuestListQEAA_NIH_1403B5230.cpp" />
    <ClCompile Include="Source\CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAVCP_1403B4010.cpp" />
    <ClCompile Include="Source\CreateCompleteCRaceBuffManagerQEAA_NPEAVCPlayerZ_140079E70.cpp" />
    <ClCompile Include="Source\CreateCPlayerQEAA_NXZ_140049AB0.cpp" />
    <ClCompile Include="Source\CreateCUnmannedTraderSubClassInfoLevelUEAAPEAVCUnm_1403844F0.cpp" />
    <ClCompile Include="Source\CreateGuardTowerYAPEAVCGuardTowerPEAVCMapDataGPEAM_1401313F0.cpp" />
    <ClCompile Include="Source\CreateItemBoxYAPEAVCItemBoxPEAU_db_con_STORAGE_LIS_140166AD0.cpp" />
    <ClCompile Include="Source\CreateItemBoxYAPEAVCItemBoxPEAU_db_con_STORAGE_LIS_140166CB0.cpp" />
    <ClCompile Include="Source\CreateMissileCNuclearBombMgrQEAA_NPEAVCPlayerPEAMK_14013B3F0.cpp" />
    <ClCompile Include="Source\CreateSelectCharacterLogTableCMainThreadAEAAXEZ_1401F9E50.cpp" />
    <ClCompile Include="Source\CreateTblLtd_ExpendCRFDBItemLogQEAA_NHZ_140485730.cpp" />
    <ClCompile Include="Source\CreateTrapYAPEAVCTrapPEAVCMapDataGPEAMPEAVCPlayerH_1401402C0.cpp" />
    <ClCompile Include="Source\ct_action_point_setYA_NPEAVCPlayerZ_140299D80.cpp" />
    <ClCompile Include="Source\ct_add_guild_scheduleYA_NPEAVCPlayerZ_140292B70.cpp" />
    <ClCompile Include="Source\ct_add_one_day_guild_scheduleYA_NPEAVCPlayerZ_140293670.cpp" />
    <ClCompile Include="Source\ct_all_item_muziYA_NPEAVCPlayerZ_14028FA80.cpp" />
    <ClCompile Include="Source\ct_all_mapYA_NPEAVCPlayerZ_14028FC40.cpp" />
    <ClCompile Include="Source\ct_alter_cashbagYA_NPEAVCPlayerZ_140290FD0.cpp" />
    <ClCompile Include="Source\ct_alter_dalantYA_NPEAVCPlayerZ_140291180.cpp" />
    <ClCompile Include="Source\ct_alter_expYA_NPEAVCPlayerZ_14028FBD0.cpp" />
    <ClCompile Include="Source\ct_alter_goldYA_NPEAVCPlayerZ_140291220.cpp" />
    <ClCompile Include="Source\ct_alter_inven_durYA_NPEAVCPlayerZ_140290B80.cpp" />
    <ClCompile Include="Source\ct_alter_lvYA_NPEAVCPlayerZ_140290CE0.cpp" />
    <ClCompile Include="Source\ct_alter_pvpYA_NPEAVCPlayerZ_140290F60.cpp" />
    <ClCompile Include="Source\ct_amp_fullYA_NPEAVCPlayerZ_140294050.cpp" />
    <ClCompile Include="Source\ct_amp_setYA_NPEAVCPlayerZ_140293F80.cpp" />
    <ClCompile Include="Source\ct_animusexpYA_NPEAVCPlayerZ_140298500.cpp" />
    <ClCompile Include="Source\ct_animus_attack_gradeYA_NPEAVCPlayerZ_140292430.cpp" />
    <ClCompile Include="Source\ct_animus_recall_termYA_NPEAVCPlayerZ_140291040.cpp" />
    <ClCompile Include="Source\ct_basemasteryYA_NPEAVCPlayerZ_140298460.cpp" />
    <ClCompile Include="Source\ct_boss_sms_cancelYA_NPEAVCPlayerZ_140291B70.cpp" />
    <ClCompile Include="Source\ct_buf_potion_useYA_NPEAVCPlayerZ_140297D10.cpp" />
    <ClCompile Include="Source\ct_CashEventStartYA_NPEAVCPlayerZ_140296170.cpp" />
    <ClCompile Include="Source\ct_cashitembuyYA_NPEAVCPlayerZ_140294590.cpp" />
    <ClCompile Include="Source\ct_CdeEndupYA_NPEAVCPlayerZ_140295FB0.cpp" />
    <ClCompile Include="Source\ct_CdeStartYA_NPEAVCPlayerZ_140295EA0.cpp" />
    <ClCompile Include="Source\ct_change_classYA_NPEAVCPlayerZ_140290600.cpp" />
    <ClCompile Include="Source\ct_change_degreeYA_NPEAVCPlayerZ_14028F5B0.cpp" />
    <ClCompile Include="Source\ct_change_masteryYA_NPEAVCPlayerZ_14028F930.cpp" />
    <ClCompile Include="Source\ct_change_master_electYA_NPEAVCPlayerZ_140296D50.cpp" />
    <ClCompile Include="Source\ct_chatsaveYA_NPEAVCPlayerZ_140296290.cpp" />
    <ClCompile Include="Source\ct_check_guild_batlle_goalYA_NPEAVCPlayerZ_140293470.cpp" />
    <ClCompile Include="Source\ct_circle_mon_killYA_NPEAVCPlayerZ_14028F890.cpp" />
    <ClCompile Include="Source\ct_circle_user_numYA_NPEAVCPlayerZ_140291870.cpp" />
    <ClCompile Include="Source\ct_ClassRefineEventYA_NPEAVCPlayerZ_140294730.cpp" />
    <ClCompile Include="Source\ct_ClearSettleOwnerGuildYA_NPEAVCPlayerZ_140295820.cpp" />
    <ClCompile Include="Source\ct_combine_ex_resultYA_NPEAVCPlayerZ_140293C30.cpp" />
    <ClCompile Include="Source\ct_complete_questYA_NPEAVCPlayerZ_140290740.cpp" />
    <ClCompile Include="Source\ct_complete_quest_otherYA_NPEAVCPlayerZ_1402925C0.cpp" />
    <ClCompile Include="Source\ct_ConEventStartYA_NPEAVCPlayerZ_140298CE0.cpp" />
    <ClCompile Include="Source\ct_continue_palytime_incYA_NPEAVCPlayerZ_140297260.cpp" />
    <ClCompile Include="Source\ct_cont_effet_clearYA_NPEAVCPlayerZ_1402916A0.cpp" />
    <ClCompile Include="Source\ct_cont_effet_timeYA_NPEAVCPlayerZ_140291630.cpp" />
    <ClCompile Include="Source\ct_copy_avatorYA_NPEAVCPlayerZ_140290920.cpp" />
    <ClCompile Include="Source\ct_create_guildbattle_field_objectYA_NPEAVCPlayerZ_140293080.cpp" />
    <ClCompile Include="Source\ct_cur_guildbattle_colorYA_NPEAVCPlayerZ_140292FA0.cpp" />
    <ClCompile Include="Source\ct_darkholerewardYA_NPEAVCPlayerZ_140298640.cpp" />
    <ClCompile Include="Source\ct_defense_item_graceYA_NPEAVCPlayerZ_1402920F0.cpp" />
    <ClCompile Include="Source\ct_defense_item_grace_JpYA_NPEAVCPlayerZ_140292290.cpp" />
    <ClCompile Include="Source\ct_destroy_gravitystoneYA_NPEAVCPlayerZ_140293290.cpp" />
    <ClCompile Include="Source\ct_destroy_guildbattle_field_objectYA_NPEAVCPlayer_1402930D0.cpp" />
    <ClCompile Include="Source\ct_destroy_system_towerYA_NPEAVCPlayerZ_140291820.cpp" />
    <ClCompile Include="Source\ct_dieYA_NPEAVCPlayerZ_140290A90.cpp" />
    <ClCompile Include="Source\ct_drop_gravitystoneYA_NPEAVCPlayerZ_1402933C0.cpp" />
    <ClCompile Include="Source\ct_drop_jadeYA_NPEAVCPlayerZ_1402969F0.cpp" />
    <ClCompile Include="Source\ct_elect_info_playerYA_NPEAVCPlayerZ_140299000.cpp" />
    <ClCompile Include="Source\ct_elect_set_envYA_NPEAVCPlayerZ_140298F60.cpp" />
    <ClCompile Include="Source\ct_elect_set_playerYA_NPEAVCPlayerZ_140298E90.cpp" />
    <ClCompile Include="Source\ct_eventset_startYA_NPEAVCPlayerZ_140297970.cpp" />
    <ClCompile Include="Source\ct_eventset_stopYA_NPEAVCPlayerZ_140297AE0.cpp" />
    <ClCompile Include="Source\ct_exceptionYA_NPEAVCPlayerZ_140296E30.cpp" />
    <ClCompile Include="Source\ct_exip_keeperYA_NPEAVCPlayerZ_1402903E0.cpp" />
    <ClCompile Include="Source\ct_exit_stoneYA_NPEAVCPlayerZ_140290510.cpp" />
    <ClCompile Include="Source\ct_expire_pcbangYA_NPEAVCPlayerZ_140298DC0.cpp" />
    <ClCompile Include="Source\ct_free_ride_shipYA_NPEAVCPlayerZ_1402902D0.cpp" />
    <ClCompile Include="Source\ct_free_sf_by_classYA_NPEAVCPlayerZ_140290C90.cpp" />
    <ClCompile Include="Source\ct_fullsetYA_NPEAVCPlayerZ_140291C50.cpp" />
    <ClCompile Include="Source\ct_full_animus_gaugeYA_NPEAVCPlayerZ_140295D20.cpp" />
    <ClCompile Include="Source\ct_full_forceYA_NPEAVCPlayerZ_1402912C0.cpp" />
    <ClCompile Include="Source\ct_full_gaugeYA_NPEAVCPlayerZ_140290B30.cpp" />
    <ClCompile Include="Source\ct_get_gravitystoneYA_NPEAVCPlayerZ_140293360.cpp" />
    <ClCompile Include="Source\ct_Gold_Age_Event_StatusYA_NPEAVCPlayerZ_14029A000.cpp" />
    <ClCompile Include="Source\ct_Gold_Age_Get_Box_CntYA_NPEAVCPlayerZ_14029A390.cpp" />
    <ClCompile Include="Source\ct_Gold_Age_Set_Event_StatusYA_NPEAVCPlayerZ_14029A1E0.cpp" />
    <ClCompile Include="Source\ct_goto_charYA_NPEAVCPlayerZ_140295B30.cpp" />
    <ClCompile Include="Source\ct_goto_mineYA_NPEAVCPlayerZ_1402908D0.cpp" />
    <ClCompile Include="Source\ct_goto_monsterYA_NPEAVCPlayerZ_140295DD0.cpp" />
    <ClCompile Include="Source\ct_goto_npcYA_NPEAVCPlayerZ_140296600.cpp" />
    <ClCompile Include="Source\ct_goto_shipport_ederYA_NPEAVCPlayerZ_140290260.cpp" />
    <ClCompile Include="Source\ct_goto_shipport_townYA_NPEAVCPlayerZ_1402901F0.cpp" />
    <ClCompile Include="Source\ct_goto_stoneYA_NPEAVCPlayerZ_1402904A0.cpp" />
    <ClCompile Include="Source\ct_guild_battle_force_stoneYA_NPEAVCPlayerZ_140293420.cpp" />
    <ClCompile Include="Source\ct_guild_callYA_NPEAVCPlayerZ_1402944A0.cpp" />
    <ClCompile Include="Source\ct_guild_infoYA_NPEAVCPlayerZ_140293A20.cpp" />
    <ClCompile Include="Source\ct_guild_suggestYA_NPEAVCPlayerZ_140293A00.cpp" />
    <ClCompile Include="Source\ct_half_gaugeYA_NPEAVCPlayerZ_140290AE0.cpp" />
    <ClCompile Include="Source\ct_HolyKeeperAttackYA_NPEAVCPlayerZ_140294DF0.cpp" />
    <ClCompile Include="Source\ct_HolySystemYA_NPEAVCPlayerZ_1402948F0.cpp" />
    <ClCompile Include="Source\ct_HolySystem_JpYA_NPEAVCPlayerZ_140294B70.cpp" />
    <ClCompile Include="Source\ct_InformCristalBattleBeforeAnHourYA_NPEAVCPlayerZ_1402955E0.cpp" />
    <ClCompile Include="Source\ct_InformPatriarchProcessorYA_NPEAVCPlayerZ_140295370.cpp" />
    <ClCompile Include="Source\ct_init_monsterYA_NPEAVCPlayerZ_140291130.cpp" />
    <ClCompile Include="Source\ct_inven_emptyYA_NPEAVCPlayerZ_140291540.cpp" />
    <ClCompile Include="Source\ct_itemlootYA_NPEAVCPlayerZ_140298280.cpp" />
    <ClCompile Include="Source\ct_jump_to_posYA_NPEAVCPlayerZ_14028FD00.cpp" />
    <ClCompile Include="Source\ct_kick_playerYA_NPEAVCPlayerZ_1402906B0.cpp" />
    <ClCompile Include="Source\ct_loadcashamountYA_NPEAVCPlayerZ_140294510.cpp" />
    <ClCompile Include="Source\ct_look_like_bossYA_NPEAVCPlayerZ_14028F8E0.cpp" />
    <ClCompile Include="Source\ct_loot_bagYA_NPEAVCPlayerZ_1402914A0.cpp" />
    <ClCompile Include="Source\ct_loot_dungeonYA_NPEAVCPlayerZ_140291F90.cpp" />
    <ClCompile Include="Source\ct_loot_itemYA_NPEAVCPlayerZ_140291360.cpp" />
    <ClCompile Include="Source\ct_loot_materialYA_NPEAVCPlayerZ_140291310.cpp" />
    <ClCompile Include="Source\ct_loot_mineYA_NPEAVCPlayerZ_1402914F0.cpp" />
    <ClCompile Include="Source\ct_loot_towerYA_NPEAVCPlayerZ_140291450.cpp" />
    <ClCompile Include="Source\ct_loot_upgradeYA_NPEAVCPlayerZ_140291D10.cpp" />
    <ClCompile Include="Source\ct_loot_upgrade_itemYA_NPEAVCPlayerZ_140292620.cpp" />
    <ClCompile Include="Source\ct_lua_commandYA_NPEAVCPlayerZ_140297D80.cpp" />
    <ClCompile Include="Source\ct_makeitem_need_matrialYA_NPEAVCPlayerZ_140292A20.cpp" />
    <ClCompile Include="Source\ct_makeitem_no_matrialYA_NPEAVCPlayerZ_1402929D0.cpp" />
    <ClCompile Include="Source\ct_make_system_towerYA_NPEAVCPlayerZ_140291770.cpp" />
    <ClCompile Include="Source\ct_manage_guildYA_NPEAVCPlayerZ_140296AE0.cpp" />
    <ClCompile Include="Source\ct_max_attackYA_NPEAVCPlayerZ_140290060.cpp" />
    <ClCompile Include="Source\ct_mepcbangYA_NPEAVCPlayerZ_140298AA0.cpp" />
    <ClCompile Include="Source\ct_minespeedYA_NPEAVCPlayerZ_140298320.cpp" />
    <ClCompile Include="Source\ct_min_attackYA_NPEAVCPlayerZ_1402900B0.cpp" />
    <ClCompile Include="Source\ct_mormal_attackYA_NPEAVCPlayerZ_140290100.cpp" />
    <ClCompile Include="Source\ct_NuAfterEffectYA_NPEAVCPlayerZ_140295D70.cpp" />
    <ClCompile Include="Source\ct_party_callYA_NPEAVCPlayerZ_140294430.cpp" />
    <ClCompile Include="Source\ct_pass_dungeonYA_NPEAVCPlayerZ_14028FF70.cpp" />
    <ClCompile Include="Source\ct_pass_schYA_NPEAVCPlayerZ_140290320.cpp" />
    <ClCompile Include="Source\ct_pcanimusexpYA_NPEAVCPlayerZ_140298960.cpp" />
    <ClCompile Include="Source\ct_PcBandPrimiumYA_NPEAVCPlayerZ_140294620.cpp" />
    <ClCompile Include="Source\ct_pcbangitemgetYA_NPEAVCPlayerZ_140298B70.cpp" />
    <ClCompile Include="Source\ct_pcbasemasteryYA_NPEAVCPlayerZ_1402988C0.cpp" />
    <ClCompile Include="Source\ct_pcitemlootYA_NPEAVCPlayerZ_1402986E0.cpp" />
    <ClCompile Include="Source\ct_pcminespeedYA_NPEAVCPlayerZ_140298780.cpp" />
    <ClCompile Include="Source\ct_pcplayerexpYA_NPEAVCPlayerZ_140298A00.cpp" />
    <ClCompile Include="Source\ct_pcroom_premiumYA_NPEAVCPlayerZ_140297870.cpp" />
    <ClCompile Include="Source\ct_pcsfmasteryYA_NPEAVCPlayerZ_140298820.cpp" />
    <ClCompile Include="Source\ct_period_time_setYA_NPEAVCPlayerZ_1402993E0.cpp" />
    <ClCompile Include="Source\ct_playerexpYA_NPEAVCPlayerZ_1402985A0.cpp" />
    <ClCompile Include="Source\ct_premium_rateYA_NPEAVCPlayerZ_1402974C0.cpp" />
    <ClCompile Include="Source\ct_PvpLimitInitYA_NPEAVCPlayerZ_140295C10.cpp" />
    <ClCompile Include="Source\ct_query_remain_oreYA_NPEAVCPlayerZ_140297020.cpp" />
    <ClCompile Include="Source\ct_recall_monsterYA_NPEAVCPlayerZ_1402907F0.cpp" />
    <ClCompile Include="Source\ct_recall_playerYA_NPEAVCPlayerZ_140290790.cpp" />
    <ClCompile Include="Source\ct_recv_change_atrad_taxrateYA_NPEAVCPlayerZ_140293B60.cpp" />
    <ClCompile Include="Source\ct_recv_current_battle_infoYA_NPEAVCPlayerZ_1402935F0.cpp" />
    <ClCompile Include="Source\ct_recv_pvp_guild_rankYA_NPEAVCPlayerZ_140293AD0.cpp" />
    <ClCompile Include="Source\ct_recv_reserved_schedulelistYA_NPEAVCPlayerZ_1402934F0.cpp" />
    <ClCompile Include="Source\ct_recv_total_guild_rankYA_NPEAVCPlayerZ_140293A40.cpp" />
    <ClCompile Include="Source\ct_regen_gravitystoneYA_NPEAVCPlayerZ_140293120.cpp" />
    <ClCompile Include="Source\ct_release_loot_freeYA_NPEAVCPlayerZ_1402905B0.cpp" />
    <ClCompile Include="Source\ct_release_make_succYA_NPEAVCPlayerZ_1402915E0.cpp" />
    <ClCompile Include="Source\ct_release_matchlessYA_NPEAVCPlayerZ_1402901A0.cpp" />
    <ClCompile Include="Source\ct_release_never_dieYA_NPEAVCPlayerZ_140290C40.cpp" />
    <ClCompile Include="Source\ct_release_punishmentYA_NPEAVCPlayerZ_140296EC0.cpp" />
    <ClCompile Include="Source\ct_remove_sf_delayYA_NPEAVCPlayerZ_140295FF0.cpp" />
    <ClCompile Include="Source\ct_report_cri_hpYA_NPEAVCPlayerZ_14028FAE0.cpp" />
    <ClCompile Include="Source\ct_report_positionYA_NPEAVCPlayerZ_14028FB00.cpp" />
    <ClCompile Include="Source\ct_ReqChangeHonorGuildYA_NPEAVCPlayerZ_140295AC0.cpp" />
    <ClCompile Include="Source\ct_ReqPunishmentYA_NPEAVCPlayerZ_140295A30.cpp" />
    <ClCompile Include="Source\ct_request_delete_questYA_NPEAVCPlayerZ_140296860.cpp" />
    <ClCompile Include="Source\ct_request_npc_questYA_NPEAVCPlayerZ_140296790.cpp" />
    <ClCompile Include="Source\ct_respawn_startYA_NPEAVCPlayerZ_14028F610.cpp" />
    <ClCompile Include="Source\ct_respawn_stopYA_NPEAVCPlayerZ_14028F750.cpp" />
    <ClCompile Include="Source\ct_resurrect_playerYA_NPEAVCPlayerZ_140292A70.cpp" />
    <ClCompile Include="Source\ct_server_rateYA_NPEAVCPlayerZ_1402972F0.cpp" />
    <ClCompile Include="Source\ct_server_timeYA_NPEAVCPlayerZ_140294260.cpp" />
    <ClCompile Include="Source\ct_SetGuildGradeByGuildSerialYA_NPEAVCPlayerZ_140294FA0.cpp" />
    <ClCompile Include="Source\ct_SetGuildGradeByNameYA_NPEAVCPlayerZ_140294F20.cpp" />
    <ClCompile Include="Source\ct_SetGuildGradeYA_NPEAVCPlayerZ_140294EA0.cpp" />
    <ClCompile Include="Source\ct_SetGuildMasterYA_NPEAVCPlayerZ_140295030.cpp" />
    <ClCompile Include="Source\ct_SetMaxLevelLimitYA_NPEAVCPlayerZ_1402950E0.cpp" />
    <ClCompile Include="Source\ct_SetPatriarchAutoYA_NPEAVCPlayerZ_140295150.cpp" />
    <ClCompile Include="Source\ct_SetPatriarchClearYA_NPEAVCPlayerZ_140295290.cpp" />
    <ClCompile Include="Source\ct_SetPatriarchGroupYA_NPEAVCPlayerZ_1402952F0.cpp" />
    <ClCompile Include="Source\ct_SetPatriarchProcessorYA_NPEAVCPlayerZ_1402951F0.cpp" />
    <ClCompile Include="Source\ct_SetSettleOwnerGuildYA_NPEAVCPlayerZ_140295660.cpp" />
    <ClCompile Include="Source\ct_set_animus_expYA_NPEAVCPlayerZ_1402910C0.cpp" />
    <ClCompile Include="Source\ct_set_animus_lvYA_NPEAVCPlayerZ_140295CC0.cpp" />
    <ClCompile Include="Source\ct_set_damage_partYA_NPEAVCPlayerZ_140297730.cpp" />
    <ClCompile Include="Source\ct_set_exp_rateYA_NPEAVCPlayerZ_1402964F0.cpp" />
    <ClCompile Include="Source\ct_set_guildbattle_colorYA_NPEAVCPlayerZ_140292E30.cpp" />
    <ClCompile Include="Source\ct_set_hfs_fullYA_NPEAVCPlayerZ_14029A630.cpp" />
    <ClCompile Include="Source\ct_set_hpYA_NPEAVCPlayerZ_14029A570.cpp" />
    <ClCompile Include="Source\ct_set_jade_effectYA_NPEAVCPlayerZ_140296900.cpp" />
    <ClCompile Include="Source\ct_set_kill_list_initYA_NPEAVCPlayerZ_140297CC0.cpp" />
    <ClCompile Include="Source\ct_set_loot_freeYA_NPEAVCPlayerZ_140290560.cpp" />
    <ClCompile Include="Source\ct_set_make_succYA_NPEAVCPlayerZ_140291590.cpp" />
    <ClCompile Include="Source\ct_set_matchlessYA_NPEAVCPlayerZ_140290150.cpp" />
    <ClCompile Include="Source\ct_set_never_dieYA_NPEAVCPlayerZ_140290BF0.cpp" />
    <ClCompile Include="Source\ct_set_ore_amountYA_NPEAVCPlayerZ_140296F50.cpp" />
    <ClCompile Include="Source\ct_set_temp_cash_pointYA_NPEAVCPlayerZ_140297C30.cpp" />
    <ClCompile Include="Source\ct_sfmasteryYA_NPEAVCPlayerZ_1402983C0.cpp" />
    <ClCompile Include="Source\ct_start_criYA_NPEAVCPlayerZ_140290370.cpp" />
    <ClCompile Include="Source\ct_start_keeperYA_NPEAVCPlayerZ_140290430.cpp" />
    <ClCompile Include="Source\ct_StateCHolyStoneSystemQEAA_NPEAVCPlayerZ_1402816B0.cpp" />
    <ClCompile Include="Source\ct_takeholymentalYA_NPEAVCPlayerZ_140294880.cpp" />
    <ClCompile Include="Source\ct_take_gravitystoneYA_NPEAVCPlayerZ_1402932F0.cpp" />
    <ClCompile Include="Source\ct_telekinesisYA_NPEAVCPlayerZ_140292500.cpp" />
    <ClCompile Include="Source\ct_tl_info_setYA_NPEAVCPlayerZ_140299600.cpp" />
    <ClCompile Include="Source\ct_tl_info_viewYA_NPEAVCPlayerZ_1402997C0.cpp" />
    <ClCompile Include="Source\ct_tl_system_settingYA_NPEAVCPlayerZ_140299B20.cpp" />
    <ClCompile Include="Source\ct_tracing_hideYA_NPEAVCPlayerZ_14028FFC0.cpp" />
    <ClCompile Include="Source\ct_tracing_showYA_NPEAVCPlayerZ_140290010.cpp" />
    <ClCompile Include="Source\ct_trap_attack_gradeYA_NPEAVCPlayerZ_140297660.cpp" />
    <ClCompile Include="Source\ct_trunk_initYA_NPEAVCPlayerZ_140291C00.cpp" />
    <ClCompile Include="Source\ct_up_allskillYA_NPEAVCPlayerZ_140290EF0.cpp" />
    <ClCompile Include="Source\ct_up_allskill_ptYA_NPEAVCPlayerZ_140292550.cpp" />
    <ClCompile Include="Source\ct_up_forceitemYA_NPEAVCPlayerZ_140290D50.cpp" />
    <ClCompile Include="Source\ct_up_forcemasteryYA_NPEAVCPlayerZ_140290E80.cpp" />
    <ClCompile Include="Source\ct_up_skillYA_NPEAVCPlayerZ_140290DC0.cpp" />
    <ClCompile Include="Source\ct_userchatbanYA_NPEAVCPlayerZ_140293D90.cpp" />
    <ClCompile Include="Source\ct_user_numYA_NPEAVCPlayerZ_140290980.cpp" />
    <ClCompile Include="Source\ct_ut_cancel_registlogoutYA_NPEAVCPlayerZ_140296070.cpp" />
    <ClCompile Include="Source\ct_ut_cancel_registYA_NPEAVCPlayerZ_1402960E0.cpp" />
    <ClCompile Include="Source\ct_view_methodYA_NPEAVCPlayerZ_1402916F0.cpp" />
    <ClCompile Include="Source\ct_vote_enableYA_NPEAVCPlayerZ_140298E30.cpp" />
    <ClCompile Include="Source\ct_whoamiYA_NPEAVCPlayerZ_140293CE0.cpp" />
    <ClCompile Include="Source\ct_Win_RaceWarYA_NPEAVCPlayerZ_140299F90.cpp" />
    <ClCompile Include="Source\C_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_14059F150.cpp" />
    <ClCompile Include="Source\C_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_14059F190.cpp" />
    <ClCompile Include="Source\C_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_14059F110.cpp" />
    <ClCompile Include="Source\db_Log_AvatorLevelCMainThreadQEAAEKKEZ_1401A6540.cpp" />
    <ClCompile Include="Source\deallocateallocatorUBaseAndExponentUEC2NPointCrypt_140594750.cpp" />
    <ClCompile Include="Source\deallocateallocatorUBaseAndExponentUECPPointCrypto_140594F30.cpp" />
    <ClCompile Include="Source\deallocateallocatorUBaseAndExponentVIntegerCryptoP_140593430.cpp" />
    <ClCompile Include="Source\DecHalfSFContDamCPlayerQEAA_NMZ_1400A1FA0.cpp" />
    <ClCompile Include="Source\DecideRecallCRecallEffectControllerQEAAXGEPEAVCPla_14024E430.cpp" />
    <ClCompile Include="Source\DeleteCouponItemCPlayerQEAAXPEAU_STORAGE_POS_INDIV_1400686F0.cpp" />
    <ClCompile Include="Source\DeleteExchangeEventItemCExchangeEventQEAAXPEAVCPla_140329CB0.cpp" />
    <ClCompile Include="Source\DeleteItemCountFromCodeCPlayerDBQEAA_NPEADHZ_14010C760.cpp" />
    <ClCompile Include="Source\DeleteUseConsumeItemCPlayerQEAA_NPEAPEAU_db_con_ST_140067F60.cpp" />
    <ClCompile Include="Source\Delete_CharacterDataCRFWorldDatabaseQEAA_NKZ_140491330.cpp" />
    <ClCompile Include="Source\DelPostDataCPlayerQEAAXKZ_1400C9BE0.cpp" />
    <ClCompile Include="Source\DEREncodeElementModExpPrecomputationCryptoPPUEBAXA_14055F250.cpp" />
    <ClCompile Include="Source\destroyallocatorUBaseAndExponentUEC2NPointCryptoPP_1405A53E0.cpp" />
    <ClCompile Include="Source\destroyallocatorUBaseAndExponentUECPPointCryptoPPV_1405A54C0.cpp" />
    <ClCompile Include="Source\destroyallocatorUBaseAndExponentVIntegerCryptoPPV1_1405A5260.cpp" />
    <ClCompile Include="Source\DestroyCCharacterQEAA_NXZ_140172700.cpp" />
    <ClCompile Include="Source\DestroyCHolyKeeperQEAA_NEPEAVCCharacterZ_1401332E0.cpp" />
    <ClCompile Include="Source\DestroyCHolyStoneQEAA_NEPEAVCCharacterZ_140137140.cpp" />
    <ClCompile Include="Source\dev_after_effectCPlayerQEAA_NXZ_1400C0370.cpp" />
    <ClCompile Include="Source\dev_all_killCPlayerQEAA_NXZ_1400BAF30.cpp" />
    <ClCompile Include="Source\dev_animus_recall_time_freeCPlayerQEAA_N_NZ_1400BC7E0.cpp" />
    <ClCompile Include="Source\dev_avator_copyCPlayerQEAA_NPEADZ_1400BDA50.cpp" />
    <ClCompile Include="Source\dev_change_classCPlayerQEAA_NPEADZ_1400BB930.cpp" />
    <ClCompile Include="Source\dev_cont_effect_delCPlayerQEAA_NXZ_1400BDC90.cpp" />
    <ClCompile Include="Source\dev_cont_effect_timeCPlayerQEAA_NKZ_1400BDC30.cpp" />
    <ClCompile Include="Source\dev_dalantCPlayerQEAA_NKZ_1400BCAD0.cpp" />
    <ClCompile Include="Source\dev_dieCPlayerQEAA_NXZ_1400BB330.cpp" />
    <ClCompile Include="Source\dev_drop_itemCPlayerQEAA_NPEADH0HZ_1400C0630.cpp" />
    <ClCompile Include="Source\dev_free_sf_by_classCPlayerQEAA_NXZ_1400BB910.cpp" />
    <ClCompile Include="Source\dev_full_animus_gaugeCPlayerQEAA_NXZ_1400C00C0.cpp" />
    <ClCompile Include="Source\dev_full_forceCPlayerQEAA_NXZ_1400BCD30.cpp" />
    <ClCompile Include="Source\dev_full_pointCPlayerQEAA_NXZ_1400BB710.cpp" />
    <ClCompile Include="Source\dev_goldCPlayerQEAA_NKZ_1400BCC00.cpp" />
    <ClCompile Include="Source\dev_goto_monsterCPlayerQEAA_NPEAVCMonsterZ_1400C0490.cpp" />
    <ClCompile Include="Source\dev_goto_npcCPlayerQEAA_NPEAVCMerchantZ_1400C0560.cpp" />
    <ClCompile Include="Source\dev_half_inven_amountCPlayerQEAA_N_KZ_1400BB7B0.cpp" />
    <ClCompile Include="Source\dev_half_pointCPlayerQEAA_NXZ_1400BB640.cpp" />
    <ClCompile Include="Source\dev_init_monsterCPlayerQEAA_NXZ_1400BCA80.cpp" />
    <ClCompile Include="Source\dev_inven_emptyCPlayerQEAA_NXZ_1400BD840.cpp" />
    <ClCompile Include="Source\dev_item_make_no_use_matrialCPlayerQEAA_N_NZ_1400BB470.cpp" />
    <ClCompile Include="Source\dev_loot_bagCPlayerQEAA_NXZ_1400BD540.cpp" />
    <ClCompile Include="Source\dev_loot_freeCPlayerQEAA_N_NZ_1400BD800.cpp" />
    <ClCompile Include="Source\dev_loot_fullitemCPlayerQEAA_NEZ_1400BE510.cpp" />
    <ClCompile Include="Source\dev_loot_itemCPlayerQEAA_NPEADH0HZ_1400BD2F0.cpp" />
    <ClCompile Include="Source\dev_loot_materialCPlayerQEAA_NXZ_1400BD000.cpp" />
    <ClCompile Include="Source\dev_loot_mineCPlayerQEAA_NXZ_1400BD630.cpp" />
    <ClCompile Include="Source\dev_loot_towerCPlayerQEAA_NXZ_1400BD350.cpp" />
    <ClCompile Include="Source\dev_lvCPlayerQEAA_NHZ_1400BB180.cpp" />
    <ClCompile Include="Source\dev_make_succCPlayerQEAA_N_NZ_1400BDBF0.cpp" />
    <ClCompile Include="Source\dev_max_level_extCPlayerQEAA_NEZ_1400BE670.cpp" />
    <ClCompile Include="Source\dev_never_dieCPlayerQEAA_N_NZ_1400BB430.cpp" />
    <ClCompile Include="Source\dev_quest_completeCPlayerQEAA_NXZ_1400BB4F0.cpp" />
    <ClCompile Include="Source\dev_quest_complete_otherCPlayerQEAA_NPEADZ_1400BA4F0.cpp" />
    <ClCompile Include="Source\dev_SetGuildGradeByGuildSerialCPlayerQEAA_NKEZ_1400BFBC0.cpp" />
    <ClCompile Include="Source\dev_SetGuildGradeByNameCPlayerQEAA_NPEADEZ_1400BFB20.cpp" />
    <ClCompile Include="Source\dev_SetGuildGradeCPlayerQEAA_NEZ_1400BFA90.cpp" />
    <ClCompile Include="Source\dev_set_animus_expCPlayerQEAA_N_KZ_1400BC8B0.cpp" />
    <ClCompile Include="Source\dev_set_animus_lvCPlayerQEAA_NHZ_1400BFC60.cpp" />
    <ClCompile Include="Source\dev_set_hpCPlayerQEAA_NMZ_1400C0960.cpp" />
    <ClCompile Include="Source\dev_trap_attack_gradeCPlayerQEAA_NHZ_1400C08B0.cpp" />
    <ClCompile Include="Source\dev_up_allCPlayerQEAA_NHZ_1400BC1C0.cpp" />
    <ClCompile Include="Source\dev_up_all_ptCPlayerQEAA_NHZ_1400B9C50.cpp" />
    <ClCompile Include="Source\dev_up_cashbagCPlayerQEAA_NNZ_1400BC760.cpp" />
    <ClCompile Include="Source\dev_up_forceitemCPlayerQEAA_NHZ_1400BB9A0.cpp" />
    <ClCompile Include="Source\dev_up_forcemasteryCPlayerQEAA_NHZ_1400BBB40.cpp" />
    <ClCompile Include="Source\dev_up_masteryCPlayerQEAA_NHHHZ_1400BBBB0.cpp" />
    <ClCompile Include="Source\dev_up_pvpCPlayerQEAA_NNZ_1400BC6F0.cpp" />
    <ClCompile Include="Source\dev_up_skillCPlayerQEAA_NPEADHZ_1400BBAC0.cpp" />
    <ClCompile Include="Source\dev_view_bossCPlayerQEAA_NXZ_1400BAD40.cpp" />
    <ClCompile Include="Source\dev_view_methodCPlayerQEAA_NPEADZ_1400BDEA0.cpp" />
    <ClCompile Include="Source\DE_AllContDamageForceRemoveYA_NPEAVCCharacter0MAEA_14017DB20.cpp" />
    <ClCompile Include="Source\DE_AllContHelpForceRemoveYA_NPEAVCCharacter0MAEAEZ_14017DAC0.cpp" />
    <ClCompile Include="Source\DE_AllContHelpSkillRemoveYA_NPEAVCCharacter0MAEAEZ_14017DA60.cpp" />
    <ClCompile Include="Source\DE_AttHPtoDstFPYA_NPEAVCCharacter0MAEAEZ_14017D6A0.cpp" />
    <ClCompile Include="Source\DE_BattleMode_RecallCommonPlayerYA_NPEAVCCharacter_14017ED80.cpp" />
    <ClCompile Include="Source\DE_ContDamageTimeIncYA_NPEAVCCharacter0MAEAEZ_14017D700.cpp" />
    <ClCompile Include="Source\DE_ContHelpTimeIncYA_NPEAVCCharacter0MAEAEZ_14017D880.cpp" />
    <ClCompile Include="Source\DE_ConvertMonsterTargetYA_NPEAVCCharacter0MAEAEZ_14017DC40.cpp" />
    <ClCompile Include="Source\DE_ConvertTargetDestYA_NPEAVCCharacter0MAEAEZ_14017E1C0.cpp" />
    <ClCompile Include="Source\DE_DamStunYA_NPEAVCCharacter0MAEAEZ_14017DF60.cpp" />
    <ClCompile Include="Source\DE_DetectTrapYA_NPEAVCCharacter0MAEAEZ_14017DDC0.cpp" />
    <ClCompile Include="Source\DE_FPDecYA_NPEAVCCharacter0MAEAEZ_14017DF00.cpp" />
    <ClCompile Include="Source\DE_HPIncYA_NPEAVCCharacter0MAEAEZ_14017D7C0.cpp" />
    <ClCompile Include="Source\DE_IncHPCirclePartyYA_NPEAVCCharacter0MAEAEZ_14017DDE0.cpp" />
    <ClCompile Include="Source\DE_IncreaseDPYA_NPEAVCCharacter0MAEAEZ_14017E160.cpp" />
    <ClCompile Include="Source\DE_LateContDamageRemoveYA_NPEAVCCharacter0MAEAEZ_14017DA00.cpp" />
    <ClCompile Include="Source\DE_LateContHelpForceRemoveYA_NPEAVCCharacter0MAEAE_14017D9A0.cpp" />
    <ClCompile Include="Source\DE_LateContHelpSkillRemoveYA_NPEAVCCharacter0MAEAE_14017D940.cpp" />
    <ClCompile Include="Source\DE_LayTrapYA_NPEAVCCharacter0MAEAEZ_14017DDA0.cpp" />
    <ClCompile Include="Source\DE_MakeGuardTowerYA_NPEAVCCharacter0MAEAEZ_14017DD80.cpp" />
    <ClCompile Include="Source\DE_MakePortalReturnBindPositionPartyMemberYA_NPEAV_14017E090.cpp" />
    <ClCompile Include="Source\DE_MakeZeroAnimusRecallTimeOnceYA_NPEAVCCharacter0_14017E280.cpp" />
    <ClCompile Include="Source\DE_OthersContHelpSFRemoveYA_NPEAVCCharacter0MAEAEZ_14017DB80.cpp" />
    <ClCompile Include="Source\DE_OverHealingYA_NPEAVCCharacter0MAEAEZ_14017D8E0.cpp" />
    <ClCompile Include="Source\DE_Potion_AllContHelpSkillRemove_OnceYA_NPEAVCChar_14017E700.cpp" />
    <ClCompile Include="Source\DE_Potion_Buf_ExtendYA_NPEAVCCharacter0MAEAEZ_14017F2C0.cpp" />
    <ClCompile Include="Source\DE_Potion_Chaos_Dec_TimeYA_NPEAVCCharacter0MAEAEZ_14017E8D0.cpp" />
    <ClCompile Include="Source\DE_Potion_Chaos_Inc_TimeYA_NPEAVCCharacter0MAEAEZ_14017E7F0.cpp" />
    <ClCompile Include="Source\DE_Potion_CharReNameYA_NPEAVCCharacter0MAEAEZ_14017EE00.cpp" />
    <ClCompile Include="Source\DE_Potion_Class_RefineYA_NPEAVCCharacter0MAEAEZ_14017E960.cpp" />
    <ClCompile Include="Source\DE_Potion_Cont_Damage_RemoveYA_NPEAVCCharacter0MAE_14017F750.cpp" />
    <ClCompile Include="Source\DE_Potion_DecHalfSFContDamYA_NPEAVCCharacter0MAEAE_14017E680.cpp" />
    <ClCompile Include="Source\DE_Potion_Exp_Increase_AbsoluteYA_NPEAVCCharacter0_14017F060.cpp" />
    <ClCompile Include="Source\DE_Potion_Exp_Increase_PercentageYA_NPEAVCCharacte_14017EF70.cpp" />
    <ClCompile Include="Source\DE_Potion_FP_In_ValueYA_NPEAVCCharacter0MAEAEZ_14017E4A0.cpp" />
    <ClCompile Include="Source\DE_Potion_Gold_PointYA_NPEAVCCharacter0MAEAEZ_14017F660.cpp" />
    <ClCompile Include="Source\DE_Potion_HFP_Full_RecoverYA_NPEAVCCharacter0MAEAE_14017F700.cpp" />
    <ClCompile Include="Source\DE_Potion_HP_In_ValueYA_NPEAVCCharacter0MAEAEZ_14017E3C0.cpp" />
    <ClCompile Include="Source\DE_Potion_Race_Debuff_Clear_OneYA_NPEAVCCharacter0_14017F3E0.cpp" />
    <ClCompile Include="Source\DE_Potion_Race_Debuff_Clear_TwoYA_NPEAVCCharacter0_14017F520.cpp" />
    <ClCompile Include="Source\DE_Potion_RemoveAfterEffectYA_NPEAVCCharacter0MAEA_14017EA70.cpp" />
    <ClCompile Include="Source\DE_Potion_RemoveAllContinousEffectYA_NPEAVCCharact_14017E770.cpp" />
    <ClCompile Include="Source\DE_Potion_Revival_Die_PositionYA_NPEAVCCharacter0M_14017F1A0.cpp" />
    <ClCompile Include="Source\DE_Potion_SP_In_ValueYA_NPEAVCCharacter0MAEAEZ_14017E590.cpp" />
    <ClCompile Include="Source\DE_Potion_Trunk_ExtendYA_NPEAVCCharacter0MAEAEZ_14017F350.cpp" />
    <ClCompile Include="Source\DE_Quick_Revival_Die_PositionYA_NPEAVCCharacter0MA_14017F230.cpp" />
    <ClCompile Include="Source\DE_RecallCommonPlayerYA_NPEAVCCharacter0MAEAEZ_14017EBC0.cpp" />
    <ClCompile Include="Source\DE_RecallPartyMemberYA_NPEAVCCharacter0MAEAEZ_14017E340.cpp" />
    <ClCompile Include="Source\DE_Recall_After_StoneYA_NPEAVCCharacter0MAEAEZ_14017ECA0.cpp" />
    <ClCompile Include="Source\DE_RecoverAllReturnStateAnimusHPFullYA_NPEAVCChara_14017E220.cpp" />
    <ClCompile Include="Source\DE_RecoveryYA_NPEAVCCharacter0MAEAEZ_14017D760.cpp" />
    <ClCompile Include="Source\DE_ReleaseMonsterTargetYA_NPEAVCCharacter0MAEAEZ_14017DD20.cpp" />
    <ClCompile Include="Source\DE_RemoveAllContHelpYA_NPEAVCCharacter0MAEAEZ_14017E030.cpp" />
    <ClCompile Include="Source\DE_ReturnBindPositionYA_NPEAVCCharacter0MAEAEZ_14017E100.cpp" />
    <ClCompile Include="Source\DE_SelfDestructionYA_NPEAVCCharacter0MAEAEZ_14017E2E0.cpp" />
    <ClCompile Include="Source\DE_SkillContHelpTimeIncYA_NPEAVCCharacter0MAEAEZ_14017DBE0.cpp" />
    <ClCompile Include="Source\DE_SPDecYA_NPEAVCCharacter0MAEAEZ_14017DEA0.cpp" />
    <ClCompile Include="Source\DE_STIncYA_NPEAVCCharacter0MAEAEZ_14017D820.cpp" />
    <ClCompile Include="Source\DE_StunYA_NPEAVCCharacter0MAEAEZ_14017DE40.cpp" />
    <ClCompile Include="Source\DE_TeleportCommonPlayerYA_NPEAVCCharacter0MAEAEZ_14017EC40.cpp" />
    <ClCompile Include="Source\DE_Teleport_After_StoneYA_NPEAVCCharacter0MAEAEZ_14017ED20.cpp" />
    <ClCompile Include="Source\DE_TransDestHPYA_NPEAVCCharacter0MAEAEZ_14017DFC0.cpp" />
    <ClCompile Include="Source\DE_TransMonsterHPYA_NPEAVCCharacter0MAEAEZ_14017DCA0.cpp" />
    <ClCompile Include="Source\DE_ViewWeakPointYA_NPEAVCCharacter0MAEAEZ_14017DD00.cpp" />
    <ClCompile Include="Source\DisconnectGuildWarCharacterRequestCNetworkEXAEAA_N_1401D1EB0.cpp" />
    <ClCompile Include="Source\DisjointPartyCPartyPlayerQEAA_NXZ_140045190.cpp" />
    <ClCompile Include="Source\DisplayErrorMsgCD3DApplicationIEAAJJKZ_140524D80.cpp" />
    <ClCompile Include="Source\DoEventRFEventBaseUEAAHPEAVCPlayerZ_1403294D0.cpp" />
    <ClCompile Include="Source\DoEventRFEvent_ClassRefineUEAAHPEAVCPlayerZ_140328CD0.cpp" />
    <ClCompile Include="Source\DoitCandidateRegisterUEAAHW4CmdPEAVCPlayerPEADZ_1402B68F0.cpp" />
    <ClCompile Include="Source\DoitClassOrderProcessorUEAAHW4CmdPEAVCPlayerPEADZ_1402B8360.cpp" />
    <ClCompile Include="Source\DoitCTalkCrystalCombineManagerQEAA_NPEAVCPlayerEPE_1404315B0.cpp" />
    <ClCompile Include="Source\DoitElectProcessorUEAAHW4CmdPEAVCPlayerPEADZ_1402B7C20.cpp" />
    <ClCompile Include="Source\DoitFinalDecisionApplyerUEAAHW4CmdPEAVCPlayerPEADZ_1402BD920.cpp" />
    <ClCompile Include="Source\DoitFinalDecisionProcessorUEAAHW4CmdPEAVCPlayerPEA_1402BDF90.cpp" />
    <ClCompile Include="Source\DoitPatriarchElectProcessorQEAA_NW4CmdPEAVCPlayerP_1402BADF0.cpp" />
    <ClCompile Include="Source\DoitSecondCandidateCrystallizerEEAAHW4CmdPEAVCPlay_1402BE6C0.cpp" />
    <ClCompile Include="Source\DoitVoterUEAAHW4CmdPEAVCPlayerPEADZ_1402BEA50.cpp" />
    <ClCompile Include="Source\down_animus_expCMgrAvatorLvHistoryQEAAX_K0_JPEADZ_1402460B0.cpp" />
    <ClCompile Include="Source\down_expCMgrAvatorLvHistoryQEAAXHNGNGPEAD0Z_140245BA0.cpp" />
    <ClCompile Include="Source\DrawBBoxCLevelQEAAXQEAF0KZ_1404E2670.cpp" />
    <ClCompile Include="Source\DrawBBoxCLevelQEAAXQEAM0KZ_1404E2250.cpp" />
    <ClCompile Include="Source\DrawLeafBBoxCLevelQEAAXXZ_1404E0B50.cpp" />
    <ClCompile Include="Source\DrawMapAlphaRenderCLevelQEAAXQEAMZ_1404E08B0.cpp" />
    <ClCompile Include="Source\DrawMapEntitiesRenderCLevelQEAAXXZ_1404E07D0.cpp" />
    <ClCompile Include="Source\DrawMapRenderCLevelQEAAXXZ_1404E0850.cpp" />
    <ClCompile Include="Source\DrawMatBBoxCLevelQEAAXXZ_1404E0B60.cpp" />
    <ClCompile Include="Source\DrawShadowRenderCLevelQEAAXQEAMPEAM1Z_1404E0820.cpp" />
    <ClCompile Include="Source\DrawSkyBoxRenderCLevelQEAAXXZ_1404E0700.cpp" />
    <ClCompile Include="Source\DrawTestBoxCLevelQEAAXQEAM0KZ_1404E09B0.cpp" />
    <ClCompile Include="Source\DropBallCNormalGuildBattleFieldGUILD_BATTLEQEAAEPE_1403ECE10.cpp" />
    <ClCompile Include="Source\DropCGravityStoneQEAAEPEAVCPlayerZ_140164B50.cpp" />
    <ClCompile Include="Source\DropGravityStoneCGuildBattleControllerQEAAXPEAVCPl_1403D63B0.cpp" />
    <ClCompile Include="Source\dtor00OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140563A30.cpp" />
    <ClCompile Include="Source\dtor00OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140563FF0.cpp" />
    <ClCompile Include="Source\dtor00OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_1405657D0.cpp" />
    <ClCompile Include="Source\dtor00OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140565D90.cpp" />
    <ClCompile Include="Source\dtor00OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140567470.cpp" />
    <ClCompile Include="Source\dtor00OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140567A30.cpp" />
    <ClCompile Include="Source\dtor00OnDrawSpinButtonsCMFCVisualManagerOfficeXPME_140643C10.cpp" />
    <ClCompile Include="Source\dtor10OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140563A50.cpp" />
    <ClCompile Include="Source\dtor10OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140564010.cpp" />
    <ClCompile Include="Source\dtor10OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_1405657F0.cpp" />
    <ClCompile Include="Source\dtor10OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140565DB0.cpp" />
    <ClCompile Include="Source\dtor10OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140567490.cpp" />
    <ClCompile Include="Source\dtor10OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140567A50.cpp" />
    <ClCompile Include="Source\dtor10OnDrawSpinButtonsCMFCVisualManagerOfficeXPME_140643C30.cpp" />
    <ClCompile Include="Source\dtor20OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140563A70.cpp" />
    <ClCompile Include="Source\dtor20OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140564030.cpp" />
    <ClCompile Include="Source\dtor20OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140565810.cpp" />
    <ClCompile Include="Source\dtor20OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140565DD0.cpp" />
    <ClCompile Include="Source\dtor20OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_1405674B0.cpp" />
    <ClCompile Include="Source\dtor20OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140567A70.cpp" />
    <ClCompile Include="Source\dtor30OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140564050.cpp" />
    <ClCompile Include="Source\dtor30OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140565DF0.cpp" />
    <ClCompile Include="Source\dtor30OnDrawMenuShadowCMFCVisualManagerOfficeXPMEA_140567A90.cpp" />
    <ClCompile Include="Source\DTradeEqualPersonYA_NPEAVCPlayerPEAPEAV1Z_1400F5D30.cpp" />
    <ClCompile Include="Source\DTradeInitCPlayerQEAAXXZ_1400538C0.cpp" />
    <ClCompile Include="Source\D_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405A0AF0.cpp" />
    <ClCompile Include="Source\D_Vector_const_iteratorUBaseAndExponentUECPPointCr_1405A0B00.cpp" />
    <ClCompile Include="Source\D_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405A0AE0.cpp" />
    <ClCompile Include="Source\D_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_1405A0A40.cpp" />
    <ClCompile Include="Source\D_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_1405A0A90.cpp" />
    <ClCompile Include="Source\D_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_1405A09F0.cpp" />
    <ClCompile Include="Source\Emb_AddStorageCPlayerQEAAPEAU_db_con_STORAGE_LISTE_140057D90.cpp" />
    <ClCompile Include="Source\Emb_AlterDurPointCPlayerQEAAKEEH_N0Z_140058A80.cpp" />
    <ClCompile Include="Source\Emb_AlterStatCPlayerQEAAXEEKEPEBD_NZ_140059430.cpp" />
    <ClCompile Include="Source\Emb_AlterStat_FCPlayerQEAAXEEMEZ_140059AF0.cpp" />
    <ClCompile Include="Source\Emb_CheckActForQuestCPlayerQEAA_NHPEADG_NZ_1400CAA70.cpp" />
    <ClCompile Include="Source\Emb_CheckActForQuestPartyCPlayerQEAAXHPEADGZ_1400CAD40.cpp" />
    <ClCompile Include="Source\Emb_CompleteQuestCPlayerQEAAXEEEZ_1400CAE50.cpp" />
    <ClCompile Include="Source\Emb_CreateNPCQuestCPlayerQEAA_NPEADKZ_1400CB890.cpp" />
    <ClCompile Include="Source\Emb_CreateQuestEventCPlayerQEAA_NW4QUEST_HAPPENPEA_1400CA830.cpp" />
    <ClCompile Include="Source\Emb_DelStorageCPlayerQEAA_NEE_N0PEBDZ_1400583B0.cpp" />
    <ClCompile Include="Source\Emb_EquipLinkCPlayerQEAAXXZ_14005A0F0.cpp" />
    <ClCompile Include="Source\Emb_ItemUpgradeCPlayerQEAAXEEEKZ_1400590D0.cpp" />
    <ClCompile Include="Source\Emb_RidindUnitCPlayerQEAAX_NPEAVCParkingUnitZ_140059E80.cpp" />
    <ClCompile Include="Source\Emb_StartQuestCPlayerQEAA_NEPEAU_happen_event_cont_1400CBE20.cpp" />
    <ClCompile Include="Source\Emb_UpdateStatCPlayerQEAAHKKKZ_140059340.cpp" />
    <ClCompile Include="Source\endvectorUBaseAndExponentUEC2NPointCryptoPPVIntege_14058DFE0.cpp" />
    <ClCompile Include="Source\endvectorUBaseAndExponentUECPPointCryptoPPVInteger_14058E530.cpp" />
    <ClCompile Include="Source\endvectorUBaseAndExponentVIntegerCryptoPPV12Crypto_14058D660.cpp" />
    <ClCompile Include="Source\EnterCReturnGateControllerQEAA_NIPEAVCPlayerZ_1402508B0.cpp" />
    <ClCompile Include="Source\EnterCReturnGateQEAAHPEAVCPlayerZ_140168930.cpp" />
    <ClCompile Include="Source\EnterMemberCTransportShipQEAAXPEAVCPlayerZ_140264830.cpp" />
    <ClCompile Include="Source\EnterPlayerCDarkHoleQEAA_NPEAVCPlayerPEAVCMapDataG_140163BC0.cpp" />
    <ClCompile Include="Source\EnterWorldCPartyPlayerQEAAXPEAU_WA_AVATOR_CODEGZ_140044CA0.cpp" />
    <ClCompile Include="Source\erasevectorUBaseAndExponentUECPPointCryptoPPVInteg_140614B90.cpp" />
    <ClCompile Include="Source\ExitMemberCTransportShipQEAAXPEAVCPlayer_NZ_1402649C0.cpp" />
    <ClCompile Include="Source\ExitUpdateDataToWorldCPlayerQEAAXXZ_14005A220.cpp" />
    <ClCompile Include="Source\ExitWorldCPartyPlayerQEAAXPEAPEAV1Z_140044D30.cpp" />
    <ClCompile Include="Source\ExpandCTreeCtrlQEAAHPEAU_TREEITEMIZ_0_1404DC3E4.cpp" />
    <ClCompile Include="Source\expf_0_140676C7E.cpp" />
    <ClCompile Include="Source\Expire_IPOverflowCBillingManagerQEAAXPEADZ_1401C4250.cpp" />
    <ClCompile Include="Source\Expire_IPOverflowCBillingQEAAXPEADZ_1401C42B0.cpp" />
    <ClCompile Include="Source\Expire_PCBangCBillingManagerQEAAXPEADZ_1401C41F0.cpp" />
    <ClCompile Include="Source\Expire_PCBangCBillingQEAAXPEADZ_14028D2B0.cpp" />
    <ClCompile Include="Source\Expire_PersonalCBillingManagerQEAAXPEADZ_1401C4180.cpp" />
    <ClCompile Include="Source\Expire_PersonalCBillingQEAAXPEADZ_1401C41E0.cpp" />
    <ClCompile Include="Source\ExponentiateAbstractRingVIntegerCryptoPPCryptoPPUE_14056E930.cpp" />
    <ClCompile Include="Source\ExponentiateAbstractRingVPolynomialMod2CryptoPPCry_140573A20.cpp" />
    <ClCompile Include="Source\ExponentiateBaseDL_GroupParametersUEC2NPointCrypto_140558100.cpp" />
    <ClCompile Include="Source\ExponentiateBaseDL_GroupParametersUECPPointCryptoP_140450410.cpp" />
    <ClCompile Include="Source\ExponentiateBaseDL_GroupParametersVIntegerCryptoPP_140551FF0.cpp" />
    <ClCompile Include="Source\ExponentiateDL_FixedBasePrecomputationImplUEC2NPoi_140576240.cpp" />
    <ClCompile Include="Source\ExponentiateDL_FixedBasePrecomputationImplUECPPoin_1405788A0.cpp" />
    <ClCompile Include="Source\ExponentiateDL_FixedBasePrecomputationImplVInteger_14056F670.cpp" />
    <ClCompile Include="Source\ExponentiateElementDL_GroupParametersUEC2NPointCry_140558180.cpp" />
    <ClCompile Include="Source\ExponentiateElementDL_GroupParametersUECPPointCryp_1404504D0.cpp" />
    <ClCompile Include="Source\ExponentiateElementDL_GroupParametersVIntegerCrypt_140552070.cpp" />
    <ClCompile Include="Source\ExponentiatePublicElementDL_PublicKeyUEC2NPointCry_14056A000.cpp" />
    <ClCompile Include="Source\ExponentiatePublicElementDL_PublicKeyUECPPointCryp_1404512A0.cpp" />
    <ClCompile Include="Source\ExponentiatePublicElementDL_PublicKeyVIntegerCrypt_1405696C0.cpp" />
    <ClCompile Include="Source\ExponentLengthDL_GroupParameters_ECVEC2NCryptoPPCr_140558010.cpp" />
    <ClCompile Include="Source\ExponentLengthDL_GroupParameters_ECVECPCryptoPPCry_140556DB0.cpp" />
    <ClCompile Include="Source\ExpulsionSocketCNetworkEXEEAA_NKKEPEAXZ_1401DAFA0.cpp" />
    <ClCompile Include="Source\ExpulsionSocketCNetWorkingUEAA_NKKEPEAXZ_1404821A0.cpp" />
    <ClCompile Include="Source\expYAMMZ_140521EC0.cpp" />
    <ClCompile Include="Source\exp_0_140676D0E.cpp" />
    <ClCompile Include="Source\exp_prof_logCMgrAvatorItemHistoryQEAAXHPEADZ_140240540.cpp" />
    <ClCompile Include="Source\ExtractStringToTimeCPlayerQEAAXKPEAU_SYSTEMTIMEZ_140069290.cpp" />
    <ClCompile Include="Source\E_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405ACC60.cpp" />
    <ClCompile Include="Source\E_Vector_const_iteratorUBaseAndExponentUECPPointCr_1405ACC90.cpp" />
    <ClCompile Include="Source\E_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405ACC30.cpp" />
    <ClCompile Include="Source\E_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_1405ACB70.cpp" />
    <ClCompile Include="Source\E_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_1405ACB90.cpp" />
    <ClCompile Include="Source\E_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_1405ACB50.cpp" />
    <ClCompile Include="Source\FastBBoShasiToFrameBufferYAXPEAVCLevelKZ_140515350.cpp" />
    <ClCompile Include="Source\fillPEAUBaseAndExponentUEC2NPointCryptoPPVInteger2_14059E920.cpp" />
    <ClCompile Include="Source\fillPEAUBaseAndExponentUECPPointCryptoPPVInteger2C_14059EAD0.cpp" />
    <ClCompile Include="Source\fillPEAUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14059E580.cpp" />
    <ClCompile Include="Source\FindEffectDstCCharacterQEAAHHHH_NPEAV1PEADPEAPEAV1_140177010.cpp" />
    <ClCompile Include="Source\FindFarChatPlayerWithTempCPlayerQEAAPEAV1PEADZ_140091160.cpp" />
    <ClCompile Include="Source\FindOwnerCUnmannedTraderUserInfoQEAAPEAVCPlayerXZ_140357930.cpp" />
    <ClCompile Include="Source\FindPotionEffectDstCCharacterQEAAHHH_NPEAV1PEADPEA_140177200.cpp" />
    <ClCompile Include="Source\Find_DataTimeLimitMgrQEAAPEAUPlayer_TL_StatusGZ_14040E360.cpp" />
    <ClCompile Include="Source\Find_DataTimeLimitMgrQEAAPEAUPlayer_TL_StatusKZ_14040E3B0.cpp" />
    <ClCompile Include="Source\FixTargetWhileCGameObjectUEAA_NPEAVCCharacterKZ_14012C6E0.cpp" />
    <ClCompile Include="Source\FixTargetWhileCMonsterUEAA_NPEAVCCharacterKZ_140146F20.cpp" />
    <ClCompile Include="Source\FixTargetWhileCPlayerUEAA_NPEAVCCharacterKZ_1400A2260.cpp" />
    <ClCompile Include="Source\ForcePullUnitCPlayerQEAAX_NZ_1401065A0.cpp" />
    <ClCompile Include="Source\FoundPartyCPartyPlayerQEAA_NPEAV1Z_1400450E0.cpp" />
    <ClCompile Include="Source\FrameMoveCLevelQEAAXXZ_1404E2E60.cpp" />
    <ClCompile Include="Source\frexp_0_140676D14.cpp" />
    <ClCompile Include="Source\F_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405A0A60.cpp" />
    <ClCompile Include="Source\F_Vector_const_iteratorUBaseAndExponentUECPPointCr_1405A0AB0.cpp" />
    <ClCompile Include="Source\F_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405A0A10.cpp" />
    <ClCompile Include="Source\F_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_14059F170.cpp" />
    <ClCompile Include="Source\F_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_14059F1B0.cpp" />
    <ClCompile Include="Source\F_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_14059F130.cpp" />
    <ClCompile Include="Source\GeneralCascadeMultiplicationUEC2NPointCryptoPPV_Ve_140599870.cpp" />
    <ClCompile Include="Source\GeneralCascadeMultiplicationUECPPointCryptoPPV_Vec_14059A140.cpp" />
    <ClCompile Include="Source\GeneralCascadeMultiplicationVIntegerCryptoPPV_Vect_140598C90.cpp" />
    <ClCompile Include="Source\GetAccumulationCountCMonsterSkillQEBAHXZ_140155730.cpp" />
    <ClCompile Include="Source\GetAddCountWithPlayerCHolyStoneQEAAGXZ_140137EE0.cpp" />
    <ClCompile Include="Source\GetAddSpeedCPlayerQEAAMXZ_1400644C0.cpp" />
    <ClCompile Include="Source\GetAfterEffectCPlayerQEAAPEAU_sf_continousXZ_14004EDF0.cpp" />
    <ClCompile Include="Source\GetAnimusFldFromExpYAPEAU_animus_fldH_KZ_14012AF00.cpp" />
    <ClCompile Include="Source\GetAttackDamPointCCharacterQEAAHHHHPEAV1_NZ_1401727A0.cpp" />
    <ClCompile Include="Source\GetAttackDistCMonsterSkillQEAAMXZ_1401556F0.cpp" />
    <ClCompile Include="Source\GetAttackDPCPlayerUEAAHXZ_140063600.cpp" />
    <ClCompile Include="Source\GetAttackFCCAttackQEAAMPEAVCPlayerE_N1Z_14016DD00.cpp" />
    <ClCompile Include="Source\GetAttackLevelCGameObjectUEAAHXZ_14012C720.cpp" />
    <ClCompile Include="Source\GetAttackLevelCPlayerUEAAHXZ_140063430.cpp" />
    <ClCompile Include="Source\GetAttackRandomPartCCharacterQEAAHXZ_140173630.cpp" />
    <ClCompile Include="Source\GetAttackRangeCPlayerUEAAMXZ_140060200.cpp" />
    <ClCompile Include="Source\GetAttackTargetCMonsterQEAAPEAVCCharacterXZ_140132A50.cpp" />
    <ClCompile Include="Source\GetAveSkillMasteryPerClass_MASTERY_PARAMQEAAMEZ_14007D7A0.cpp" />
    <ClCompile Include="Source\GetAvoidRateCPlayerUEAAHXZ_140063370.cpp" />
    <ClCompile Include="Source\GetBagNumCPlayerDBQEAAEXZ_140034910.cpp" />
    <ClCompile Include="Source\GetBallCNormalGuildBattleFieldGUILD_BATTLEQEAAEGKP_1403ECD90.cpp" />
    <ClCompile Include="Source\GetBallOwnerCNormalGuildBattleFieldGUILD_BATTLEQEA_1403ED210.cpp" />
    <ClCompile Include="Source\GetBasePrecomputationDL_GroupParametersImplVModExp_14055E9E0.cpp" />
    <ClCompile Include="Source\GetBasePrecomputationDL_GroupParameters_IntegerBas_140552440.cpp" />
    <ClCompile Include="Source\GetBeforeTimeCMonsterSkillQEAAKXZ_140155870.cpp" />
    <ClCompile Include="Source\GetBillingTypeCPlayerQEAAHXZ_1400600A0.cpp" />
    <ClCompile Include="Source\GetBindDummyCPlayerQEAAPEAU_dummy_positionXZ_1403EAC50.cpp" />
    <ClCompile Include="Source\GetBindMapCPlayerQEAAPEAVCMapDataPEAM_NZ_1400A0160.cpp" />
    <ClCompile Include="Source\GetBindMapDataCPlayerQEAAPEAVCMapDataXZ_1403EAC30.cpp" />
    <ClCompile Include="Source\GetCashAmountCPlayerQEAAHXZ_1402F2770.cpp" />
    <ClCompile Include="Source\GetCGravityStoneQEAAEGKPEAVCPlayerZ_140164CE0.cpp" />
    <ClCompile Include="Source\GetCharNameACPlayerDBQEAAPEADXZ_140034810.cpp" />
    <ClCompile Include="Source\GetCharNameWCPlayerDBQEAAPEADXZ_140078D40.cpp" />
    <ClCompile Include="Source\GetCharSerialCPlayerDBQEAAKXZ_140078D60.cpp" />
    <ClCompile Include="Source\GetClassInGuildCPlayerDBQEAAEXZ_1400AD430.cpp" />
    <ClCompile Include="Source\GetCloseItemForPassTimeUpdateInfoCUnmannedTraderUs_140357710.cpp" />
    <ClCompile Include="Source\GetCloseItemForPassTimeUpdateInfoCUnmannedTraderUs_1403979F0.cpp" />
    <ClCompile Include="Source\GetCurItemSerialCPlayerDBQEAAGXZ_14007BA10.cpp" />
    <ClCompile Include="Source\GetCurPosCPlayerDBQEAAPEAMXZ_14007D380.cpp" />
    <ClCompile Include="Source\GetD3D8TexPtrCSpriteQEAAPEAXKKZ_140521BB0.cpp" />
    <ClCompile Include="Source\GetDalantCPlayerDBQEAAKXZ_140034930.cpp" />
    <ClCompile Include="Source\GetDamageDPCPlayerQEAAHHZ_1400636F0.cpp" />
    <ClCompile Include="Source\GetDamageLevelCPlayerQEAAHHZ_140063520.cpp" />
    <ClCompile Include="Source\GetDefFacingCPlayerUEAAMHZ_140061340.cpp" />
    <ClCompile Include="Source\GetDefFCAutominePersonalUEAAHHPEAVCCharacterPEAHZ_1402DB7E0.cpp" />
    <ClCompile Include="Source\GetDefFCCAnimusUEAAHHPEAVCCharacterPEAHZ_140129610.cpp" />
    <ClCompile Include="Source\GetDefFCCGameObjectUEAAHHPEAVCCharacterPEAHZ_14012E140.cpp" />
    <ClCompile Include="Source\GetDefFCCGuardTowerUEAAHHPEAVCCharacterPEAHZ_1401326C0.cpp" />
    <ClCompile Include="Source\GetDefFCCHolyKeeperUEAAHHPEAVCCharacterPEAHZ_140134AC0.cpp" />
    <ClCompile Include="Source\GetDefFCCHolyStoneUEAAHHPEAVCCharacterPEAHZ_1401376F0.cpp" />
    <ClCompile Include="Source\GetDefFCCMonsterUEAAHHPEAVCCharacterPEAHZ_140146790.cpp" />
    <ClCompile Include="Source\GetDefFCCPlayerUEAAHHPEAVCCharacterPEAHZ_140060260.cpp" />
    <ClCompile Include="Source\GetDefFCCTrapUEAAHHPEAVCCharacterPEAHZ_1401410C0.cpp" />
    <ClCompile Include="Source\GetDefGapCPlayerUEAAMHZ_1400611F0.cpp" />
    <ClCompile Include="Source\GetDefSkillCAnimusUEAAH_NZ_140129730.cpp" />
    <ClCompile Include="Source\GetDefSkillCGameObjectUEAAH_NZ_14012E2A0.cpp" />
    <ClCompile Include="Source\GetDefSkillCGuardTowerUEAAH_NZ_140132710.cpp" />
    <ClCompile Include="Source\GetDefSkillCHolyKeeperUEAAH_NZ_140136900.cpp" />
    <ClCompile Include="Source\GetDefSkillCHolyStoneUEAAH_NZ_140138CB0.cpp" />
    <ClCompile Include="Source\GetDefSkillCMonsterUEAAH_NZ_1401466B0.cpp" />
    <ClCompile Include="Source\GetDefSkillCPlayerUEAAH_NZ_140060C00.cpp" />
    <ClCompile Include="Source\GetDefSkillCTrapUEAAH_NZ_140141110.cpp" />
    <ClCompile Include="Source\GetDmgSKILLQEAAHMZ_14012D250.cpp" />
    <ClCompile Include="Source\GetDPCPlayerDBQEAAHXZ_14007C4D0.cpp" />
    <ClCompile Include="Source\GetDPCPlayerQEAAHXZ_14007C480.cpp" />
    <ClCompile Include="Source\GetDstCaseTypeCMonsterSkillQEAAHXZ_140155850.cpp" />
    <ClCompile Include="Source\GetEffectEquipCodeCPlayerQEAAEEEZ_140057D00.cpp" />
    <ClCompile Include="Source\GetElementCMonsterSkillQEAAHXZ_14014F7E0.cpp" />
    <ClCompile Include="Source\GetEnvironmentCLevelQEAAKXZ_140074EA0.cpp" />
    <ClCompile Include="Source\GetExceptMotiveCMonsterSkillQEBAHXZ_1401557D0.cpp" />
    <ClCompile Include="Source\GetExceptMotiveValueCMonsterSkillQEBAHXZ_140155810.cpp" />
    <ClCompile Include="Source\GetExpCPlayerDBQEAANXZ_1400348B0.cpp" />
    <ClCompile Include="Source\GetExtTrunkSlotNumCPlayerDBQEAAEXZ_1400C2CB0.cpp" />
    <ClCompile Include="Source\GetExtTrunkSlotRaceCPlayerDBQEAAEKZ_14010C4D0.cpp" />
    <ClCompile Include="Source\GetFireTolCPlayerUEAAHXZ_140060D00.cpp" />
    <ClCompile Include="Source\GetFirstYposCLevelQEAAMQEAM00Z_1404E0B10.cpp" />
    <ClCompile Include="Source\GetFirstYposCLevelQEAAMQEAMHZ_1404E0AF0.cpp" />
    <ClCompile Include="Source\GetFirstYposCLevelQEAAMQEAMQEAF1Z_1404E0B30.cpp" />
    <ClCompile Include="Source\GetFldCMonsterSkillQEAAPEAU_base_fldXZ_14014DD80.cpp" />
    <ClCompile Include="Source\GetFPCPlayerDBQEAAHXZ_140034870.cpp" />
    <ClCompile Include="Source\GetFPCPlayerQEAAHXZ_14007C3E0.cpp" />
    <ClCompile Include="Source\GetGaugeCPlayerQEAAHHZ_14005F4E0.cpp" />
    <ClCompile Include="Source\GetGenAttackProbCAnimusUEAAHPEAVCCharacterH_NZ_1401298D0.cpp" />
    <ClCompile Include="Source\GetGenAttackProbCGameObjectUEAAHPEAVCCharacterH_NZ_14012E100.cpp" />
    <ClCompile Include="Source\GetGenAttackProbCGuardTowerUEAAHPEAVCCharacterH_NZ_1401302B0.cpp" />
    <ClCompile Include="Source\GetGenAttackProbCHolyKeeperUEAAHPEAVCCharacterH_NZ_140134B40.cpp" />
    <ClCompile Include="Source\GetGenAttackProbCMonsterUEAAHPEAVCCharacterH_NZ_140146DA0.cpp" />
    <ClCompile Include="Source\GetGenAttackProbCNuclearBombUEAAHPEAVCCharacterHZ_14013E4A0.cpp" />
    <ClCompile Include="Source\GetGenAttackProbCPlayerUEAAHPEAVCCharacterH_NZ_1400614C0.cpp" />
    <ClCompile Include="Source\GetGenAttackProbCTrapUEAAHPEAVCCharacterH_NZ_14013F290.cpp" />
    <ClCompile Include="Source\GetGeneratorDL_GroupParameters_IntegerBasedImplVMo_1405524D0.cpp" />
    <ClCompile Include="Source\GetGMRequestDataPtrGMCallMgrQEAAPEAVGMRequestDataP_1402AA5A0.cpp" />
    <ClCompile Include="Source\GetGoldCPlayerDBQEAAKXZ_140034950.cpp" />
    <ClCompile Include="Source\GetGroupIDCUnmannedTraderSubClassInfoLevelUEAA_NEG_140384380.cpp" />
    <ClCompile Include="Source\GetGroupModExpPrecomputationCryptoPPUEBAAEBVAbstra_14055F1D0.cpp" />
    <ClCompile Include="Source\GetGroupPrecomputationDL_GroupParametersImplVModEx_14055C840.cpp" />
    <ClCompile Include="Source\GetGroupTargetCPlayerQEAAPEAU__target1EZ_140047880.cpp" />
    <ClCompile Include="Source\GetGuildSerialCPlayerDBQEAAKXZ_14010C540.cpp" />
    <ClCompile Include="Source\GetHaveUnitNumCPlayerDBQEAAHXZ_140108E60.cpp" />
    <ClCompile Include="Source\GetHPCPlayerDBQEAAHXZ_140034850.cpp" />
    <ClCompile Include="Source\GetHPCPlayerUEAAHXZ_1400743A0.cpp" />
    <ClCompile Include="Source\GetInitClassCostCPlayerQEAAKXZ_140060110.cpp" />
    <ClCompile Include="Source\GetInvenItemCountFromCodeCPlayerDBQEAAHPEADZ_14010C630.cpp" />
    <ClCompile Include="Source\GetInvisibleCCharacterQEAA_NXZ_1401753E0.cpp" />
    <ClCompile Include="Source\GetItemCPlayerDBQEAAPEAU_db_con_STORAGE_LISTEZ_14010C5A0.cpp" />
    <ClCompile Include="Source\GetItemEquipLevelYAHHHZ_14003A650.cpp" />
    <ClCompile Include="Source\GetItemEquipUpLevelYAHHHZ_14003A8B0.cpp" />
    <ClCompile Include="Source\GetKingPowerDamageCharacterCMonsterAggroMgrQEAAPEA_14015E060.cpp" />
    <ClCompile Include="Source\GetLevelCAnimusUEAAHXZ_1401296C0.cpp" />
    <ClCompile Include="Source\GetLevelCGameObjectUEAAHXZ_14012E290.cpp" />
    <ClCompile Include="Source\GetLevelCGuardTowerUEAAHXZ_1401326F0.cpp" />
    <ClCompile Include="Source\GetLevelCHolyKeeperUEAAHXZ_1401368D0.cpp" />
    <ClCompile Include="Source\GetLevelCHolyStoneUEAAHXZ_140138C80.cpp" />
    <ClCompile Include="Source\GetLevelCMonsterUEAAHXZ_14014B9E0.cpp" />
    <ClCompile Include="Source\GetLevelContSFTimeMonsterSetInfoDataQEAAEEEZ_14015D4A0.cpp" />
    <ClCompile Include="Source\GetLevelCPlayerDBQEAAHXZ_1400348F0.cpp" />
    <ClCompile Include="Source\GetLevelCPlayerUEAAHXZ_140074420.cpp" />
    <ClCompile Include="Source\GetLevelCTrapUEAAHXZ_1401410F0.cpp" />
    <ClCompile Include="Source\GetLevelLimitCMapDataQEAAHXZ_140186690.cpp" />
    <ClCompile Include="Source\GetLimitExpcStaticMember_PlayerQEBANHZ_14010E5E0.cpp" />
    <ClCompile Include="Source\GetLootAuthorCPartyPlayerQEAAPEAVCPlayerXZ_140045C50.cpp" />
    <ClCompile Include="Source\GetLooterCLootingMgrQEAAPEAVCPlayerPEAVCMapDataPEA_14014C830.cpp" />
    <ClCompile Include="Source\GetLossExpCPlayerDBQEAANXZ_14007A520.cpp" />
    <ClCompile Include="Source\GetMapCodeCPlayerDBQEAAHXZ_14007D360.cpp" />
    <ClCompile Include="Source\GetMapLayerCGuildRoomInfoQEAAGXZ_1402EB270.cpp" />
    <ClCompile Include="Source\GetMapNameCLevelQEAAPEADXZ_1404E26C0.cpp" />
    <ClCompile Include="Source\GetMasteryCumAfterAttackCPlayerQEAAHHZ_140063800.cpp" />
    <ClCompile Include="Source\GetMaxDmgCMonsterSkillQEAAHXZ_14014F820.cpp" />
    <ClCompile Include="Source\GetMaxDPCPlayerQEAAHXZ_140078970.cpp" />
    <ClCompile Include="Source\GetMaxExponentDL_GroupParameters_ECVEC2NCryptoPPCr_140557BB0.cpp" />
    <ClCompile Include="Source\GetMaxExponentDL_GroupParameters_ECVECPCryptoPPCry_140450170.cpp" />
    <ClCompile Include="Source\GetMaxExponentDL_GroupParameters_IntegerBasedCrypt_1406321C0.cpp" />
    <ClCompile Include="Source\GetMaxFPCPlayerQEAAHXZ_14007A930.cpp" />
    <ClCompile Include="Source\GetMaxHPCPlayerUEAAHXZ_140074220.cpp" />
    <ClCompile Include="Source\GetMaxLevelCAnimusQEAAEXZ_1401296E0.cpp" />
    <ClCompile Include="Source\GetMaxLevelCPlayerDBQEAAHXZ_14007A890.cpp" />
    <ClCompile Include="Source\GetMaxLvcStaticMember_PlayerQEBAHXZ_14010E640.cpp" />
    <ClCompile Include="Source\GetMaxParamFromExpYAKH_KZ_14012B080.cpp" />
    <ClCompile Include="Source\GetMaxProbCMonsterSkillQEAAHXZ_14014F860.cpp" />
    <ClCompile Include="Source\GetMaxSPCPlayerQEAAHXZ_14007A9D0.cpp" />
    <ClCompile Include="Source\GetMeleeSkillIndexCAttackSAHHZ_14016D880.cpp" />
    <ClCompile Include="Source\GetMemberPlayerCNormalGuildBattleGuildGUILD_BATTLE_1403E0890.cpp" />
    <ClCompile Include="Source\GetMinDmgCMonsterSkillQEAAHXZ_14014F800.cpp" />
    <ClCompile Include="Source\GetMinProbCMonsterSkillQEAAHXZ_14014F840.cpp" />
    <ClCompile Include="Source\GetModulusDL_GroupParameters_IntegerBasedImplVModE_140552460.cpp" />
    <ClCompile Include="Source\GetModulusModExpPrecomputationCryptoPPQEBAAEBVInte_140552480.cpp" />
    <ClCompile Include="Source\GetMoneyCPlayerQEAAKEZ_140056200.cpp" />
    <ClCompile Include="Source\GetMonSkillCMonsterSkillPoolQEAAPEAVCMonsterSkillH_140156B00.cpp" />
    <ClCompile Include="Source\GetMonSkillKindCMonsterSkillPoolQEAAPEAVCMonsterSk_140156B50.cpp" />
    <ClCompile Include="Source\GetMotiveCMonsterSkillQEAAHXZ_1401556B0.cpp" />
    <ClCompile Include="Source\GetMotiveValueCMonsterSkillQEAAHXZ_140155690.cpp" />
    <ClCompile Include="Source\GetMoveSpeedCPlayerQEAAMXZ_14005D7C0.cpp" />
    <ClCompile Include="Source\GetMoveTargetCAnimusQEAA_NPEAVCCharacterMEZ_140127FE0.cpp" />
    <ClCompile Include="Source\GetNearEmptySlotCCharacterQEAAHHMQEAMPEAMZ_140173720.cpp" />
    <ClCompile Include="Source\GetNewItemSerialCPlayerDBQEAAGXZ_14010C100.cpp" />
    <ClCompile Include="Source\GetNextActionDelayTimeCMonsterSkillQEAAKXZ_14014C2E0.cpp" />
    <ClCompile Include="Source\GetNextGenAttTimeCCharacterQEAAKXZ_140132A00.cpp" />
    <ClCompile Include="Source\GetNextYposCLevelQEAAHQEAMPEAMZ_1404E0B70.cpp" />
    <ClCompile Include="Source\GetNextYposFarCLevelQEAAHQEAM0PEAMZ_1404E1100.cpp" />
    <ClCompile Include="Source\GetNextYposFarProgressCLevelQEAAHQEAM0PEAMZ_1404E15B0.cpp" />
    <ClCompile Include="Source\GetNextYposForServerCLevelQEAAHQEAMPEAMZ_1404E0F50.cpp" />
    <ClCompile Include="Source\GetNextYposForServerFarCLevelQEAAHQEAM0PEAMZ_1404E1B00.cpp" />
    <ClCompile Include="Source\GetNextYposNoAttrCLevelQEAAHQEAMPEAMZ_1404E0D60.cpp" />
    <ClCompile Include="Source\GetObjectExpandCMainThreadQEAAPEAVCGameObjectPEAU__1401EC900.cpp" />
    <ClCompile Include="Source\GetObjNameCPlayerUEAAPEADXZ_140061950.cpp" />
    <ClCompile Include="Source\GetObjRaceCPlayerUEAAHXZ_140074470.cpp" />
    <ClCompile Include="Source\GetOutOreInAutoMineAutoMineMachineQEAAXPEAVCPlayer_1402D1AD0.cpp" />
    <ClCompile Include="Source\GetOwnerAutominePersonalQEAAPEAVCPlayerXZ_1402DCFC0.cpp" />
    <ClCompile Include="Source\GetOwnerCGravityStoneQEAAPEAVCPlayerXZ_140034AC0.cpp" />
    <ClCompile Include="Source\GetOwnerCRecallRequestQEAAPEAVCPlayerXZ_14024FCA0.cpp" />
    <ClCompile Include="Source\GetOwnerCReturnGateCreateParamQEAAPEAVCPlayerXZ_1401692D0.cpp" />
    <ClCompile Include="Source\GetOwnerCReturnGateQEAAPEAVCPlayerXZ_140034AA0.cpp" />
    <ClCompile Include="Source\GetPartyExpDistributionRateCPlayerQEAAMHHHZ_14005D6A0.cpp" />
    <ClCompile Include="Source\GetPathCLevelQEAAKQEAM0QEAY02MPEAKHZ_1404E2110.cpp" />
    <ClCompile Include="Source\GetPathFromDepthCLevelQEAAKQEAM0HQEAY02MPEAKZ_1404E21B0.cpp" />
    <ClCompile Include="Source\GetPlayerCNormalGuildBattleGuildMemberGUILD_BATTLE_1403E0290.cpp" />
    <ClCompile Include="Source\GetPlayerDataTimeLimitMgrQEAAGGPEAEPEANZ_14040ED20.cpp" />
    <ClCompile Include="Source\GetPlayerInfoCDarkHoleChannelQEAAPEAV_dh_player_mg_14026ADB0.cpp" />
    <ClCompile Include="Source\GetPlayerPenaltyTimeLimitMgrQEAANGZ_14040EBF0.cpp" />
    <ClCompile Include="Source\GetPlayerStateRFEventBaseUEAAPEADKKZ_140329550.cpp" />
    <ClCompile Include="Source\GetPlayerStateRFEvent_ClassRefineUEAAPEADKKZ_1403296A0.cpp" />
    <ClCompile Include="Source\GetPlayerStatusTimeLimitMgrQEAAEGZ_14040ECA0.cpp" />
    <ClCompile Include="Source\GetPointFromScreenRayCLevelQEAAHJJPEAY02MZ_1404E0A70.cpp" />
    <ClCompile Include="Source\GetPointFromScreenRayFarCLevelQEAAHJJPEAY02MZ_1404E0AB0.cpp" />
    <ClCompile Include="Source\GetPopPartyMemberCPartyPlayerQEAAHXZ_140044EB0.cpp" />
    <ClCompile Include="Source\GetPrivateExponentDL_PrivateKeyImplVDL_GroupParame_140452310.cpp" />
    <ClCompile Include="Source\GetPrivateExponentDL_PrivateKeyImplVDL_GroupParame_1405593B0.cpp" />
    <ClCompile Include="Source\GetPrivateExponentDL_PrivateKeyImplVDL_GroupParame_1405689D0.cpp" />
    <ClCompile Include="Source\GetPrivateExponentDL_PrivateKeyImplVDL_GroupParame_140636650.cpp" />
    <ClCompile Include="Source\GetPrivateExponentDL_PrivateKeyImplVDL_GroupParame_1406383A0.cpp" />
    <ClCompile Include="Source\GetPtrBaseClassCPlayerDBQEAAPEAU_class_fldXZ_14007DC50.cpp" />
    <ClCompile Include="Source\GetPtrCurClassCPlayerDBQEAAPEAU_class_fldXZ_14007DC30.cpp" />
    <ClCompile Include="Source\GetPtrFromSerialCPartyPlayerQEAAPEAV1KZ_140044F80.cpp" />
    <ClCompile Include="Source\GetPtrItemStorageCPlayerDBQEAAPEAU_db_con_STORAGE__14010C160.cpp" />
    <ClCompile Include="Source\GetPtrPartyMemberCPartyPlayerQEAAPEAPEAV1XZ_140044F50.cpp" />
    <ClCompile Include="Source\GetPtrPlayerFromAccountSerialYAPEAVCPlayerPEAV1HKZ_140066860.cpp" />
    <ClCompile Include="Source\GetPtrPlayerFromAccountYAPEAVCPlayerPEAV1HPEADZ_140066690.cpp" />
    <ClCompile Include="Source\GetPtrPlayerFromCharSerialYAPEAVCPlayerHKZ_140066520.cpp" />
    <ClCompile Include="Source\GetPtrPlayerFromNameYAPEAVCPlayerPEAV1HPEADZ_140066750.cpp" />
    <ClCompile Include="Source\GetPtrPlayerFromSerialYAPEAVCPlayerPEAV1HKZ_1400665E0.cpp" />
    <ClCompile Include="Source\GetPvPCashBagCPlayerDBQEAANXZ_1400F7870.cpp" />
    <ClCompile Include="Source\GetPvpOrderViewCPlayerQEAAPEAVCPvpOrderViewXZ_14029D860.cpp" />
    <ClCompile Include="Source\GetPvPPointCPlayerDBQEAANXZ_140077F70.cpp" />
    <ClCompile Include="Source\GetPvpPointLeakCPlayerQEAANXZ_140068FB0.cpp" />
    <ClCompile Include="Source\GetPvpPointLimiterCPlayerQEAAAVCPvpPointLimiterXZ_14029D4D0.cpp" />
    <ClCompile Include="Source\GetPvpRankCPlayerDBQEAAKXZ_1402B63E0.cpp" />
    <ClCompile Include="Source\GetRaceBuffLevelCRaceBuffByHolyQuestProcedureQEAAH_1403B64F0.cpp" />
    <ClCompile Include="Source\GetRaceBuffLevelCRaceBuffManagerQEAAHPEAVCPlayerZ_1403A1080.cpp" />
    <ClCompile Include="Source\GetRaceCodeCPlayerDBQEAAHXZ_140034830.cpp" />
    <ClCompile Include="Source\GetRaceSexCodeCPlayerDBQEAAHXZ_1400790F0.cpp" />
    <ClCompile Include="Source\GetRecallAnimusCPlayerQEAAPEAVCAnimusXZ_140172210.cpp" />
    <ClCompile Include="Source\GetRequireSFSlotCEquipItemSFAgentIEAA_NPEAU_requir_140121290.cpp" />
    <ClCompile Include="Source\GetResBufferNumCPlayerDBQEAAEXZ_14010BDD0.cpp" />
    <ClCompile Include="Source\GetRewardItems_DarkDungeonCPlayerQEAAHPEAV_dh_rewa_1400CDA30.cpp" />
    <ClCompile Include="Source\GetRideLimLevelCTransportShipQEAAHXZ_140265140.cpp" />
    <ClCompile Include="Source\GetRideUpLimLevelCTransportShipQEAAHXZ_1402651F0.cpp" />
    <ClCompile Include="Source\GetSectorListPlayerCMapDataQEAAPEAVCObjectListGKZ_1401848D0.cpp" />
    <ClCompile Include="Source\GetSFLevelYAHHKZ_14003EF10.cpp" />
    <ClCompile Include="Source\GetSFLvCMonsterSkillQEAAHXZ_14014DDA0.cpp" />
    <ClCompile Include="Source\GetSkillDelayTimeCMonsterQEAAMPEAVCMonsterSkillZ_140142C90.cpp" />
    <ClCompile Include="Source\GetSkillLv_MASTERY_PARAMQEAAHEZ_140034A70.cpp" />
    <ClCompile Include="Source\GetSlotCCharacterQEAAHPEAV1Z_140173B40.cpp" />
    <ClCompile Include="Source\GetSoilTolCPlayerUEAAHXZ_140060EE0.cpp" />
    <ClCompile Include="Source\GetSPActionProbabilityCMonsterSkillQEBAHXZ_140155790.cpp" />
    <ClCompile Include="Source\GetSPCPlayerDBQEAAHXZ_140034890.cpp" />
    <ClCompile Include="Source\GetSPCPlayerQEAAHXZ_14007C430.cpp" />
    <ClCompile Include="Source\GetSPLimitCountCMonsterSkillQEBAHXZ_140155750.cpp" />
    <ClCompile Include="Source\GetStateFlagCPlayerQEAA_KXZ_14007C4F0.cpp" />
    <ClCompile Include="Source\GetStealthCCharacterQEAA_N_NZ_140175310.cpp" />
    <ClCompile Include="Source\GetTargetObjCPlayerQEAAPEAVCGameObjectXZ_14005FDA0.cpp" />
    <ClCompile Include="Source\GetTextExtentExPointA_0_140676F36.cpp" />
    <ClCompile Include="Source\GetTextExtentExPointW_0_140676F30.cpp" />
    <ClCompile Include="Source\GetThisObjectVDL_GroupParameters_IntegerBasedImplV_14059C200.cpp" />
    <ClCompile Include="Source\GetTLStatusPlayer_TL_StatusQEAAEXZ_1403947C0.cpp" />
    <ClCompile Include="Source\GetTopAggroCharacterCMonsterAggroMgrQEAAPEAVCChara_14015DFA0.cpp" />
    <ClCompile Include="Source\GetTopDamageCharacterCMonsterAggroMgrQEAAPEAVCChar_14015E000.cpp" />
    <ClCompile Include="Source\GetTotalTolCCharacterQEAAHEHZ_1401733B0.cpp" />
    <ClCompile Include="Source\GetTrunkPasswdWCPlayerDBQEAAPEADXZ_1400FC2D0.cpp" />
    <ClCompile Include="Source\GetTrunkSlotNumCPlayerDBQEAAEXZ_1400C2C90.cpp" />
    <ClCompile Include="Source\GetTrunkSlotRaceCPlayerDBQEAAEKZ_14010C460.cpp" />
    <ClCompile Include="Source\GetTypeCMonsterSkillQEAAHXZ_14014C2C0.cpp" />
    <ClCompile Include="Source\GetUseConsumeItemCPlayerQEAA_NPEAU_consume_item_li_140067D50.cpp" />
    <ClCompile Include="Source\GetUseSlotCPlayerDBQEAAEXZ_1400D3E50.cpp" />
    <ClCompile Include="Source\GetUseTypeCMonsterSkillQEAAHXZ_14014DD60.cpp" />
    <ClCompile Include="Source\GetValueHelperVDL_GroupParameters_IntegerBasedCryp_140589490.cpp" />
    <ClCompile Include="Source\GetValueVDL_GroupParameters_IntegerBasedImplVModEx_1405A23C0.cpp" />
    <ClCompile Include="Source\GetVisualVerCPlayerQEAAHXZ_140053A10.cpp" />
    <ClCompile Include="Source\GetVoidValueDL_GroupParameters_IntegerBasedImplVMo_140552380.cpp" />
    <ClCompile Include="Source\GetWaterTolCPlayerUEAAHXZ_140060DF0.cpp" />
    <ClCompile Include="Source\GetWeaponAdjustCPlayerUEAAMXZ_1400610C0.cpp" />
    <ClCompile Include="Source\GetWeaponClassCPlayerUEAAHXZ_1400744F0.cpp" />
    <ClCompile Include="Source\GetWeaponRangeCPlayerQEAAHXZ_140060240.cpp" />
    <ClCompile Include="Source\GetWidthCPlayerUEAAMXZ_1400601C0.cpp" />
    <ClCompile Include="Source\GetWindTolCPlayerUEAAHXZ_140060FD0.cpp" />
    <ClCompile Include="Source\GetWisdomTargetDfAIMgrSAPEAVCCharacterHPEAVCMonste_140152570.cpp" />
    <ClCompile Include="Source\get_localitemAutominePersonalMgrAEBAPEAU_db_con_ST_1402E1850.cpp" />
    <ClCompile Include="Source\get_ownerAutominePersonalQEAAPEAVCPlayerXZ_1402E1A50.cpp" />
    <ClCompile Include="Source\get_typeAutoMineMachineMngAEAAEPEAVCPlayerEZ_1402D6000.cpp" />
    <ClCompile Include="Source\GiveCGuildBattleRewardItemGUILD_BATTLEQEAAPEBV12PE_1403C90E0.cpp" />
    <ClCompile Include="Source\GiveCGuildBattleRewardItemManagerGUILD_BATTLEQEAAP_1403EAF00.cpp" />
    <ClCompile Include="Source\GiveEventItemCExchangeEventQEAAXPEAVCPlayerZ_14032A630.cpp" />
    <ClCompile Include="Source\gm_MonsterInitCMainThreadQEAA_NPEAVCCharacterZ_1401F7A40.cpp" />
    <ClCompile Include="Source\GoCCharacterQEAAXXZ_140173330.cpp" />
    <ClCompile Include="Source\Guild_Buy_Emblem_CompleteCPlayerSAXPEAU_DB_QRY_SYN_1400AA500.cpp" />
    <ClCompile Include="Source\Guild_Disjoint_CompleteCPlayerSAXPEAU_DB_QRY_SYN_D_1400AA820.cpp" />
    <ClCompile Include="Source\Guild_Force_Leave_CompleteCPlayerSAXPEAU_DB_QRY_SY_1400A9560.cpp" />
    <ClCompile Include="Source\Guild_Insert_CompleteCPlayerSAXPEAU_DB_QRY_SYN_DAT_1400A8A10.cpp" />
    <ClCompile Include="Source\Guild_Join_Accept_CompleteCPlayerSAXPEAU_DB_QRY_SY_1400A98B0.cpp" />
    <ClCompile Include="Source\Guild_Pop_Money_CompleteCPlayerSAXPEAU_DB_QRY_SYN__1400AA050.cpp" />
    <ClCompile Include="Source\Guild_Push_Money_CompleteCPlayerSAXPEAU_DB_QRY_SYN_1400A9CE0.cpp" />
    <ClCompile Include="Source\Guild_Self_Leave_CompleteCPlayerSAXPEAU_DB_QRY_SYN_1400A9120.cpp" />
    <ClCompile Include="Source\Guild_Update_GuildMater_CompleteCPlayerSAXPEAU_DB__1400AA960.cpp" />
    <ClCompile Include="Source\G_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405987F0.cpp" />
    <ClCompile Include="Source\G_Vector_const_iteratorUBaseAndExponentUECPPointCr_140598930.cpp" />
    <ClCompile Include="Source\G_Vector_const_iteratorUBaseAndExponentVIntegerCry_140598590.cpp" />
    <ClCompile Include="Source\G_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_1405974D0.cpp" />
    <ClCompile Include="Source\G_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_1405A8E60.cpp" />
    <ClCompile Include="Source\G_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_140597E70.cpp" />
    <ClCompile Include="Source\G_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_1405A8F20.cpp" />
    <ClCompile Include="Source\G_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_1405966E0.cpp" />
    <ClCompile Include="Source\G_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_1405A8C20.cpp" />
    <ClCompile Include="Source\HearMapSoundCLevelQEAAXXZ_1404E26E0.cpp" />
    <ClCompile Include="Source\HideNameEffectCPlayerQEAAX_NZ_1400532F0.cpp" />
    <ClCompile Include="Source\HSKQuestEnd_AttCPlayerQEAAXEPEAV1Z_1400CCBC0.cpp" />
    <ClCompile Include="Source\H_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_140597410.cpp" />
    <ClCompile Include="Source\H_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_140597DB0.cpp" />
    <ClCompile Include="Source\H_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_140596620.cpp" />
    <ClCompile Include="Source\id_characteristic_two_basisASN1CryptoPPYAAVOID2XZ_14062C760.cpp" />
    <ClCompile Include="Source\IncCriEffKillPointCPlayerQEAAXXZ_14005FFC0.cpp" />
    <ClCompile Include="Source\IncCriEffPvPCashBagCPlayerQEAAXNZ_14005FEC0.cpp" />
    <ClCompile Include="Source\IncPvPPointCPlayerQEAAXNW4PVP_ALTER_TYPEKZ_14005FB00.cpp" />
    <ClCompile Include="Source\InheritBossCPartyPlayerQEAA_NPEAV1Z_140045830.cpp" />
    <ClCompile Include="Source\InitAlterMasteryCPlayerDBQEAAXXZ_14007DB10.cpp" />
    <ClCompile Include="Source\InitCCharacterQEAAXPEAU_object_idZ_140172440.cpp" />
    <ClCompile Include="Source\InitCEquipItemSFAgentQEAAXPEAVCPlayerZ_140120FE0.cpp" />
    <ClCompile Include="Source\InitClassCPlayerDBQEAAXXZ_14010B7B0.cpp" />
    <ClCompile Include="Source\InitCMonsterSkillPoolQEAAXXZ_1401569B0.cpp" />
    <ClCompile Include="Source\InitCMonsterSkillQEAAXXZ_140156030.cpp" />
    <ClCompile Include="Source\InitCPartyPlayerQEAAXGZ_140044C30.cpp" />
    <ClCompile Include="Source\InitCPlayerQEAA_NPEAU_object_idZ_140048660.cpp" />
    <ClCompile Include="Source\InitCPotionParamQEAAXPEAVCPlayerZ_140078C90.cpp" />
    <ClCompile Include="Source\InitializecStaticMember_PlayerQEAA_NXZ_14010E590.cpp" />
    <ClCompile Include="Source\InitMgrCQuestMgrQEAAXPEAVCPlayerPEAU_QUEST_DB_BASE_140287820.cpp" />
    <ClCompile Include="Source\InitMgrItemCombineMgrQEAAXPEAVCPlayerZ_1402AB9D0.cpp" />
    <ClCompile Include="Source\InitPlayerDBCPlayerDBQEAAXPEAVCPlayerZ_140108B60.cpp" />
    <ClCompile Include="Source\InitResBufferCPlayerDBQEAAXXZ_14010BE50.cpp" />
    <ClCompile Include="Source\InitSKILLQEAAXHHHHHHHHZ_14012CF80.cpp" />
    <ClCompile Include="Source\Init_dh_player_mgrQEAAXXZ_14026EAE0.cpp" />
    <ClCompile Include="Source\init_pos_dh_player_mgrQEAAXXZ_14026EB50.cpp" />
    <ClCompile Include="Source\init_skill_lv_up_dataQEAAXXZ_140078950.cpp" />
    <ClCompile Include="Source\Init_target_player_damage_contsf_allinform_zoclQEA_140074110.cpp" />
    <ClCompile Include="Source\init__targetCPlayerQEAAXXZ_1400741B0.cpp" />
    <ClCompile Include="Source\InsertCharacterSelectLogCRFWorldDatabaseQEAA_NKPEA_1404A0D30.cpp" />
    <ClCompile Include="Source\InsertMovePotionStoneEffectCPotionMgrQEAAXPEAVCPla_14039EDF0.cpp" />
    <ClCompile Include="Source\InsertPartyMemberCPartyPlayerQEAA_NPEAV1Z_1400452F0.cpp" />
    <ClCompile Include="Source\InsertPlayerStatusTimeLimitMgrQEAAXGKEKK_NZ_14040EA70.cpp" />
    <ClCompile Include="Source\InsertPotionContEffectCPotionMgrIEAAHPEAVCPlayerAE_14039EC60.cpp" />
    <ClCompile Include="Source\InsertSFContEffectCCharacterUEAAEEEKGEPEA_NPEAV1Z_140173D90.cpp" />
    <ClCompile Include="Source\InsertSFContEffectCMonsterUEAAEEEKGEPEA_NPEAVCChar_1401475B0.cpp" />
    <ClCompile Include="Source\InsertSkillCMonsterSkillPoolIEAAHAEAVCMonsterSkill_140156A30.cpp" />
    <ClCompile Include="Source\InsertSlotCCharacterQEAAHPEAV1HZ_140173A10.cpp" />
    <ClCompile Include="Source\insertvectorUBaseAndExponentUEC2NPointCryptoPPVInt_140594240.cpp" />
    <ClCompile Include="Source\insertvectorUBaseAndExponentUECPPointCryptoPPVInte_140594A20.cpp" />
    <ClCompile Include="Source\insertvectorUBaseAndExponentVIntegerCryptoPPV12Cry_140592F20.cpp" />
    <ClCompile Include="Source\Insert_CharacterDataCRFWorldDatabaseQEAA_NPEAD0K0E_1404907C0.cpp" />
    <ClCompile Include="Source\insert_expendCRFDBItemLogQEAA_NPEAU_LTD_EXPENDZ_140485B80.cpp" />
    <ClCompile Include="Source\Insert_Level_LogCRFWorldDatabaseQEAA_NKEKZ_140493D90.cpp" />
    <ClCompile Include="Source\Insert_PlayerTimeLimitInfoCRFWorldDatabaseQEAA_NKZ_1404C7A00.cpp" />
    <ClCompile Include="Source\InstancecStaticMember_PlayerSAPEAV1XZ_14010E450.cpp" />
    <ClCompile Include="Source\IntoMapCPlayerQEAA_NEZ_1400505A0.cpp" />
    <ClCompile Include="Source\in_playerCGuildMasterEffectQEAA_NPEAVCPlayerEZ_1403F4850.cpp" />
    <ClCompile Include="Source\IsActableClassSkillCPlayerDBQEAA_NPEADPEAHZ_14010BF40.cpp" />
    <ClCompile Include="Source\IsActingSiegeModeCPlayerQEAA_NXZ_14008F230.cpp" />
    <ClCompile Include="Source\IsApplyPcbangPrimiumCNationSettingDataNULLUEAA_NQE_1402130D0.cpp" />
    <ClCompile Include="Source\IsApplyPcbangPrimiumCNationSettingDataUEAA_NQEBVCP_140211D00.cpp" />
    <ClCompile Include="Source\IsApplyPcbangPrimiumCNationSettingManagerQEAA_NQEB_14007E070.cpp" />
    <ClCompile Include="Source\IsApplyPcbangPrimiumCPlayerQEBA_NXZ_140067D00.cpp" />
    <ClCompile Include="Source\IsAttackAbleCMonsterSkillQEAA_NXZ_14014F880.cpp" />
    <ClCompile Include="Source\IsBeAttackedAbleCPlayerUEAA_N_NZ_140063290.cpp" />
    <ClCompile Include="Source\IsBeCirclePlayerCGameObjectQEAA_NHZ_14017C440.cpp" />
    <ClCompile Include="Source\IsBeDamagedAbleAutominePersonalUEAA_NPEAVCCharacte_1402DDCE0.cpp" />
    <ClCompile Include="Source\IsBeDamagedAbleCGameObjectUEAA_NPEAVCCharacterZ_14012C780.cpp" />
    <ClCompile Include="Source\IsBeDamagedAbleCGuardTowerUEAA_NPEAVCCharacterZ_140131A80.cpp" />
    <ClCompile Include="Source\IsBeDamagedAbleCHolyKeeperUEAA_NPEAVCCharacterZ_140133D90.cpp" />
    <ClCompile Include="Source\IsBeDamagedAbleCHolyStoneUEAA_NPEAVCCharacterZ_140137770.cpp" />
    <ClCompile Include="Source\IsBeDamagedAbleCMonsterUEAA_NPEAVCCharacterZ_140146C80.cpp" />
    <ClCompile Include="Source\IsBeDamagedAbleCPlayerUEAA_NPEAVCCharacterZ_1400F0DC0.cpp" />
    <ClCompile Include="Source\IsBeNearStoreYAPEAVCItemStorePEAVCPlayerHZ_140262C40.cpp" />
    <ClCompile Include="Source\IsBulletValidityCPlayerQEAAPEAU_db_con_STORAGE_LIS_14008A1E0.cpp" />
    <ClCompile Include="Source\IsBuyRaceBossGoldBoxCGoldenBoxItemMgrQEAAEPEAVCPla_140414A70.cpp" />
    <ClCompile Include="Source\IsChaosModeCPlayerQEBA_NXZ_14007A8D0.cpp" />
    <ClCompile Include="Source\IsClassChangeableLvCPlayerDBQEAA_NXZ_14010BED0.cpp" />
    <ClCompile Include="Source\IsCollisionRayAABBCLevelQEAAHJJQEAM0PEAY02MZ_1404E1F60.cpp" />
    <ClCompile Include="Source\IsDamageEffectCCharacterQEAA_NIGZ_140178690.cpp" />
    <ClCompile Include="Source\IsEffBulletValidityCPlayerQEAAPEAU_db_con_STORAGE__140089F60.cpp" />
    <ClCompile Include="Source\IsEffectableDstCCharacterQEAA_NPEADPEAV1Z_140177E60.cpp" />
    <ClCompile Include="Source\IsEffectableEquipCPlayerQEAA_NPEAU_storage_con_STO_140057870.cpp" />
    <ClCompile Include="Source\IsEnableSkillCEquipItemSFAgentQEAAEHPEAU_skill_fld_140121200.cpp" />
    <ClCompile Include="Source\IsEnableSkillCEquipItemSFAgentQEAAEPEAU_skill_fldZ_140121180.cpp" />
    <ClCompile Include="Source\IsEquipAbleGradeCPlayerQEAA_NEZ_140057AF0.cpp" />
    <ClCompile Include="Source\IsExistOwnerCReturnGateControllerIEAA_NPEAVCPlayer_140250CE0.cpp" />
    <ClCompile Include="Source\IsExistRequestMoveCharacterListCUserDBQEAAEKZ_14011BAE0.cpp" />
    <ClCompile Include="Source\IsExitCMonsterSkillQEAA_NXZ_1401556D0.cpp" />
    <ClCompile Include="Source\IsFill_dh_player_mgrQEAA_NXZ_14026EFA0.cpp" />
    <ClCompile Include="Source\IsGoalCNormalGuildBattleFieldGUILD_BATTLEQEAAEPEAV_1403ECE70.cpp" />
    <ClCompile Include="Source\IsHaveMentalTicketCPlayerQEAA_NXZ_1400CE610.cpp" />
    <ClCompile Include="Source\IsInTownCPlayerUEAA_NXZ_1400744C0.cpp" />
    <ClCompile Include="Source\IsItemLootAuthorityCHolyStoneSystemQEAA_NPEAVCPlay_1402811D0.cpp" />
    <ClCompile Include="Source\IsJoinPartyLevelCPartyPlayerQEAA_NHMZ_140045EA0.cpp" />
    <ClCompile Include="Source\IsLastAttBuffCPlayerQEAA_NXZ_140069060.cpp" />
    <ClCompile Include="Source\IsLoadedBspCLevelQEBAHXZ_140189120.cpp" />
    <ClCompile Include="Source\IsMapLoadingCPlayerQEAA_NXZ_1403EACD0.cpp" />
    <ClCompile Include="Source\IsMasterAutoMineMachineQEBA_NPEAVCPlayerZ_1402D1020.cpp" />
    <ClCompile Include="Source\IsMineModeCPlayerQEAA_NXZ_1402E1830.cpp" />
    <ClCompile Include="Source\IsMiningByMinigTicketCPlayerQEAA_NXZ_1400CE530.cpp" />
    <ClCompile Include="Source\IsNewEnterAblePlayerCDarkHoleQEAA_NPEAVCPlayerZ_140163C50.cpp" />
    <ClCompile Include="Source\IsOldMemberCTransportShipQEAA_NPEAVCPlayerZ_140264CC0.cpp" />
    <ClCompile Include="Source\IsOpenPartyMemberCDarkHoleChannelQEAA_NPEAVCPlayer_14026AF60.cpp" />
    <ClCompile Include="Source\IsOpenPartyMemberCDarkHoleQEAA_NPEAVCPlayerZ_140163CB0.cpp" />
    <ClCompile Include="Source\IsOutExtraStopPosCPlayerQEAA_NPEAMZ_140057790.cpp" />
    <ClCompile Include="Source\IsOverOneDayCPlayerQEAA_NXZ_14004CC20.cpp" />
    <ClCompile Include="Source\IsPartyBossCPartyPlayerQEAA_NXZ_140044E50.cpp" />
    <ClCompile Include="Source\IsPartyLockCPartyPlayerQEAA_NXZ_140044E80.cpp" />
    <ClCompile Include="Source\IsPartyMemberCPartyPlayerQEAA_NPEAVCPlayerZ_140045030.cpp" />
    <ClCompile Include="Source\IsPartyModeCPartyPlayerQEAA_NXZ_140044E30.cpp" />
    <ClCompile Include="Source\IsPassExpLimitLvDiffCPlayerQEAA_NHAEA_NZ_14007D3C0.cpp" />
    <ClCompile Include="Source\IsPassMasteryLimitLvDiffCPlayerQEAA_NHZ_14007D8F0.cpp" />
    <ClCompile Include="Source\IsPatriarchCNuclearBombMgrQEAA_NPEAVCPlayerZ_14013AAD0.cpp" />
    <ClCompile Include="Source\IsPotionEffectableDstCCharacterQEAA_NPEADPEAV1Z_140178270.cpp" />
    <ClCompile Include="Source\IsPunishedCPlayerQEAA_NE_NZ_140068150.cpp" />
    <ClCompile Include="Source\IsPvpMapCPvpCashPointQEAA_NPEAVCPlayerZ_1403F52D0.cpp" />
    <ClCompile Include="Source\IsRecallAnimusCPlayerQEAA_NXZ_14007BE20.cpp" />
    <ClCompile Include="Source\IsRecvableContEffectCPlayerUEAA_NXZ_140063200.cpp" />
    <ClCompile Include="Source\IsReturnPostUpdateCPlayerQEAA_NXZ_1400C9B50.cpp" />
    <ClCompile Include="Source\IsRewardExpCGameObjectUEAA_NXZ_140072B60.cpp" />
    <ClCompile Include="Source\IsRewardExpCMonsterUEAA_NXZ_14014BB40.cpp" />
    <ClCompile Include="Source\IsRideRightCParkingUnitQEAA_NPEAVCPlayerZ_140167CC0.cpp" />
    <ClCompile Include="Source\IsRidingShipCPlayerQEAA_NXZ_1400F0D40.cpp" />
    <ClCompile Include="Source\IsRidingUnitCPlayerQEAA_NXZ_140106DA0.cpp" />
    <ClCompile Include="Source\IsSame_target_player_damage_contsf_allinform_zoclS_1400F02B0.cpp" />
    <ClCompile Include="Source\IsSFActableByClassCPlayerQEAA_NEPEAU_base_fldZ_1400A1E40.cpp" />
    <ClCompile Include="Source\IsSFUsableGaugeCPlayerQEAA_NEGPEAGZ_1400A1250.cpp" />
    <ClCompile Include="Source\IsSFUsableSFMasteryCPlayerQEAA_NEHZ_1400A0FE0.cpp" />
    <ClCompile Include="Source\IsSFUseableRaceCPlayerQEAA_NEGZ_1400A1150.cpp" />
    <ClCompile Include="Source\IsSiegeModeCPlayerQEAA_NXZ_1400F0D10.cpp" />
    <ClCompile Include="Source\IsTakeRightCItemBoxQEAA_NPEAVCPlayerZ_140166180.cpp" />
    <ClCompile Include="Source\IsTargetObjCPlayerQEAA_NPEAVCGameObjectZ_14005FC30.cpp" />
    <ClCompile Include="Source\IsUsableAccountTypeCPlayerQEAA_NHZ_140069D20.cpp" />
    <ClCompile Include="Source\IsUsableCouponCashItemRemoteStoreQEAA_NPEAU_reques_1402F57A0.cpp" />
    <ClCompile Include="Source\IsUseCloakBoosterCPlayerQEAA_NXZ_1400645E0.cpp" />
    <ClCompile Include="Source\IsUseReleaseRaceBuffPotionCPlayerQEAA_NXZ_1400A3A50.cpp" />
    <ClCompile Include="Source\IsValidOwnerCGravityStoneQEAA_NPEAVCPlayerZ_140164E30.cpp" />
    <ClCompile Include="Source\IsValidPlayerCMonsterQEAAHXZ_140142E70.cpp" />
    <ClCompile Include="Source\IsViewAreaCMonsterQEAA_NPEAVCCharacterZ_140146A20.cpp" />
    <ClCompile Include="Source\Is_Battle_ModeCPlayerUEAA_NXZ_1400686C0.cpp" />
    <ClCompile Include="Source\JoinCGuildBattleControllerQEAAXPEAVCPlayerZ_1403D5ED0.cpp" />
    <ClCompile Include="Source\j_0CashChangeStateFlagCPlayerQEAAHZ_1400074EB.cpp" />
    <ClCompile Include="Source\j_0CAttackQEAAPEAVCCharacterZ_140009499.cpp" />
    <ClCompile Include="Source\j_0CCharacterQEAAXZ_14001193C.cpp" />
    <ClCompile Include="Source\j_0CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14000BB59.cpp" />
    <ClCompile Include="Source\j_0CMonsterAttackQEAAPEAVCCharacterZ_1400047C8.cpp" />
    <ClCompile Include="Source\j_0CMonsterSkillPoolQEAAXZ_140011FD6.cpp" />
    <ClCompile Include="Source\j_0CMonsterSkillQEAAXZ_14000132A.cpp" />
    <ClCompile Include="Source\j_0CPartyModeKillMonsterExpNotifyQEAAXZ_140001E10.cpp" />
    <ClCompile Include="Source\j_0CPartyPlayerQEAAXZ_14000F92A.cpp" />
    <ClCompile Include="Source\j_0CPlayerAttackQEAAPEAVCCharacterZ_140005CD6.cpp" />
    <ClCompile Include="Source\j_0CPlayerDBQEAAXZ_1400039DB.cpp" />
    <ClCompile Include="Source\j_0CPlayerQEAAXZ_140005B2D.cpp" />
    <ClCompile Include="Source\j_0CRaceBuffInfoByHolyQuestQEAAPEAU_skill_fldEZ_1400067CB.cpp" />
    <ClCompile Include="Source\j_0CReturnGateCreateParamQEAAPEAVCPlayerZ_140003936.cpp" />
    <ClCompile Include="Source\j_0cStaticMember_PlayerAEAAXZ_14000867A.cpp" />
    <ClCompile Include="Source\j_0CUnmannedTraderSubClassInfoLevelQEAAKZ_14000E14C.cpp" />
    <ClCompile Include="Source\j_0LendItemSheetAEAAPEAVCPlayerZ_14000C4B9.cpp" />
    <ClCompile Include="Source\j_0SKILLQEAAXZ_14000A317.cpp" />
    <ClCompile Include="Source\j_0TimeLimitJadeAEAAPEAVCPlayerZ_14000D8C3.cpp" />
    <ClCompile Include="Source\j_0_attack_skill_result_zoclQEAAXZ_140001686.cpp" />
    <ClCompile Include="Source\j_0_be_damaged_playerQEAAXZ_140013F0C.cpp" />
    <ClCompile Include="Source\j_0_character_create_setdataQEAAXZ_140004C78.cpp" />
    <ClCompile Include="Source\j_0_character_db_loadQEAAXZ_140004BD3.cpp" />
    <ClCompile Include="Source\j_0_dh_player_mgrQEAAXZ_14000A2BD.cpp" />
    <ClCompile Include="Source\j_0_nuclear_bomb_explosion_result_zoclQEAAXZ_140010154.cpp" />
    <ClCompile Include="Source\j_0_nuclear_explosion_success_zoclQEAAXZ_140006EC4.cpp" />
    <ClCompile Include="Source\j_0_qry_case_character_renameQEAAXZ_1400055B5.cpp" />
    <ClCompile Include="Source\j_0_SKILL_IDX_PER_MASTERYQEAAXZ_14000E4BC.cpp" />
    <ClCompile Include="Source\j_0_target_player_damage_contsf_allinform_zoclQEAA_140012A17.cpp" />
    <ClCompile Include="Source\j_0_throw_skill_result_one_zoclQEAAXZ_140007E82.cpp" />
    <ClCompile Include="Source\j_0_throw_skill_result_other_zoclQEAAXZ_140011D92.cpp" />
    <ClCompile Include="Source\j_0__targetCPlayerQEAAXZ_140004F93.cpp" />
    <ClCompile Include="Source\j_1CCharacterUEAAXZ_14000530D.cpp" />
    <ClCompile Include="Source\j_1CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_140013723.cpp" />
    <ClCompile Include="Source\j_1CMonsterSkillPoolQEAAXZ_14000FC90.cpp" />
    <ClCompile Include="Source\j_1CMonsterSkillQEAAXZ_140009967.cpp" />
    <ClCompile Include="Source\j_1CPartyModeKillMonsterExpNotifyQEAAXZ_1400052D1.cpp" />
    <ClCompile Include="Source\j_1CPlayerDBQEAAXZ_14000EE8F.cpp" />
    <ClCompile Include="Source\j_1CPlayerUEAAXZ_140013F39.cpp" />
    <ClCompile Include="Source\j_1cStaticMember_PlayerAEAAXZ_14000D229.cpp" />
    <ClCompile Include="Source\j_1CUnmannedTraderSubClassInfoLevelQEAAXZ_14000CB5D.cpp" />
    <ClCompile Include="Source\j_4CUnmannedTraderSubClassInfoLevelQEAAAEBV0AEBV0Z_14000BD20.cpp" />
    <ClCompile Include="Source\j_AddCPartyModeKillMonsterExpNotifyQEAA_NPEAVCPlay_1400069C4.cpp" />
    <ClCompile Include="Source\j_AddDalantCPlayerQEAAXK_NZ_14000E660.cpp" />
    <ClCompile Include="Source\j_AddGoldCPlayerQEAAXK_NZ_14000259A.cpp" />
    <ClCompile Include="Source\j_AddTrunkDalantCPlayerDBQEAAXKZ_14000BCA8.cpp" />
    <ClCompile Include="Source\j_AddTrunkGoldCPlayerDBQEAAXKZ_140009197.cpp" />
    <ClCompile Include="Source\j_adjust_effectCGuildMasterEffectAEAAXPEAVCPlayerE_1400059C5.cpp" />
    <ClCompile Include="Source\j_AlterContDurSecCCharacterQEAAXEGKGZ_14000C0A9.cpp" />
    <ClCompile Include="Source\j_AlterDalantCPlayerQEAAXNZ_14000D756.cpp" />
    <ClCompile Include="Source\j_AlterExpCAnimusQEAAX_JZ_14000582B.cpp" />
    <ClCompile Include="Source\j_AlterExpCPlayerQEAAXN_N00Z_14000BBBD.cpp" />
    <ClCompile Include="Source\j_AlterExp_AnimusCPlayerQEAAX_JZ_14000F98E.cpp" />
    <ClCompile Include="Source\j_AlterExp_MasterReportCAnimusQEAAX_JZ_140010D11.cpp" />
    <ClCompile Include="Source\j_AlterExp_PotionCPlayerQEAAXNZ_140003517.cpp" />
    <ClCompile Include="Source\j_AlterFP_AnimusCPlayerQEAAXHZ_140008C29.cpp" />
    <ClCompile Include="Source\j_AlterGoldCPlayerQEAAXNZ_14000CAFE.cpp" />
    <ClCompile Include="Source\j_AlterHP_AnimusCPlayerQEAAXHZ_1400067BC.cpp" />
    <ClCompile Include="Source\j_AlterMaxLevelCPlayerQEAAXEZ_140010EBF.cpp" />
    <ClCompile Include="Source\j_AlterMode_AnimusCPlayerQEAAXEZ_140013CD2.cpp" />
    <ClCompile Include="Source\j_AlterPvPCashBagCPlayerQEAAXNW4PVP_MONEY_ALTER_TY_14000459D.cpp" />
    <ClCompile Include="Source\j_AlterPvPPointCPlayerQEAAXNW4PVP_ALTER_TYPEKZ_14000862A.cpp" />
    <ClCompile Include="Source\j_AlterPvpPointLeakCPlayerQEAAXNZ_14000362F.cpp" />
    <ClCompile Include="Source\j_AlterSecCPlayerUEAAXXZ_14000CE0A.cpp" />
    <ClCompile Include="Source\j_alter_pvpCMgrAvatorLvHistoryQEAAXHNPEAVCPartyPla_14000CAC7.cpp" />
    <ClCompile Include="Source\j_ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAVCP_14000A1A0.cpp" />
    <ClCompile Include="Source\j_ApplyCRaceBuffInfoByHolyQuestListQEAA_NIHPEAVCPl_140011806.cpp" />
    <ClCompile Include="Source\j_ApplyCRaceBuffInfoByHolyQuestQEAA_NPEAVCPlayerZ_140013B33.cpp" />
    <ClCompile Include="Source\j_ApplyEffectCRaceBuffInfoByHolyQuestAEAA_NPEAVCPl_14000CF4F.cpp" />
    <ClCompile Include="Source\j_ApplyEquipItemEffectCPlayerQEAA_NH_NZ_140013E8F.cpp" />
    <ClCompile Include="Source\j_ApplyPotionCPotionMgrQEAAHPEAVCPlayer0PEAU_skill_140005065.cpp" />
    <ClCompile Include="Source\j_ApplySetItemEffectCPlayerQEAAXPEAVsi_interpretKE_140002202.cpp" />
    <ClCompile Include="Source\j_apply_case_equip_std_effectCPlayerQEAAXPEAU_db_c_1400024C3.cpp" />
    <ClCompile Include="Source\j_apply_case_equip_upgrade_effectCPlayerQEAAXPEAU__14000CFF9.cpp" />
    <ClCompile Include="Source\j_apply_have_item_std_effectCPlayerQEAAXHM_NHZ_14000897C.cpp" />
    <ClCompile Include="Source\j_apply_normal_item_std_effectCPlayerQEAAXHM_NZ_140013755.cpp" />
    <ClCompile Include="Source\j_AppointPatriarchGroupCandidateMgrQEAA_NPEAVCPlay_140005263.cpp" />
    <ClCompile Include="Source\j_AppointSerialStorageItemCPlayerDBQEAAXXZ_140008E9A.cpp" />
    <ClCompile Include="Source\j_AssistForceCCharacterQEAA_NPEAV1PEAU_force_fldHP_140006ECE.cpp" />
    <ClCompile Include="Source\j_AssistForceToOneCCharacterQEAA_NPEAV1PEAU_force__140012724.cpp" />
    <ClCompile Include="Source\j_AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterS_14000371F.cpp" />
    <ClCompile Include="Source\j_AssistSkillCCharacterQEAA_NPEAV1HPEAU_skill_fldH_140007937.cpp" />
    <ClCompile Include="Source\j_AssistSkillToOneCCharacterQEAA_NPEAV1HPEAU_skill_1400127A1.cpp" />
    <ClCompile Include="Source\j_AttackableHeightCPlayerUEAAHXZ_140012EAE.cpp" />
    <ClCompile Include="Source\j_AttackCGuardTowerQEAAXPEAVCCharacterZ_140011054.cpp" />
    <ClCompile Include="Source\j_AttackCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14000642E.cpp" />
    <ClCompile Include="Source\j_AttackCTrapQEAAXPEAVCCharacterZ_14000F6CD.cpp" />
    <ClCompile Include="Source\j_AttackMonsterSkillCMonsterAttackQEAAXPEAU_attack_140011E73.cpp" />
    <ClCompile Include="Source\j_AttackSkillCPlayerAttackQEAAXPEAU_attack_param_N_14000F01F.cpp" />
    <ClCompile Include="Source\j_AttackSkillRequestCNetworkEXAEAA_NHPEADZ_14000BFA5.cpp" />
    <ClCompile Include="Source\j_AttackUnitCPlayerAttackQEAAXPEAU_attack_paramZ_14000B0AA.cpp" />
    <ClCompile Include="Source\j_AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAVCPlayerZ_14000B7C6.cpp" />
    <ClCompile Include="Source\j_AutoCharge_BoosterCPlayerQEAAXXZ_1400093CC.cpp" />
    <ClCompile Include="Source\j_AutoRecoverCPlayerQEAAXXZ_140007DB0.cpp" />
    <ClCompile Include="Source\j_AutoRecover_AnimusCPlayerQEAAXXZ_140011491.cpp" />
    <ClCompile Include="Source\j_BeHaveBoxOfAMPCPlayerDBQEAA_NXZ_140005691.cpp" />
    <ClCompile Include="Source\j_BeTargetedCGameObjectUEAAXPEAVCCharacterZ_1400039A9.cpp" />
    <ClCompile Include="Source\j_BeTargetedCMonsterUEAAXPEAVCCharacterZ_140003FA8.cpp" />
    <ClCompile Include="Source\j_BillingExpireIPOverflowCNetworkEXAEAA_NHPEADZ_14000D6B1.cpp" />
    <ClCompile Include="Source\j_BillingExpirePCBangCNetworkEXAEAA_NHPEADZ_140006848.cpp" />
    <ClCompile Include="Source\j_BillingExpirePersonalCNetworkEXAEAA_NHPEADZ_140005D35.cpp" />
    <ClCompile Include="Source\j_Billing_LogoutCPlayerQEAAXXZ_140006FDC.cpp" />
    <ClCompile Include="Source\j_BreakCloakBoosterCPlayerQEAAXXZ_140002090.cpp" />
    <ClCompile Include="Source\j_BreakStealthCCharacterQEAAXXZ_14000E877.cpp" />
    <ClCompile Include="Source\j_BuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCPlay_1400080B2.cpp" />
    <ClCompile Include="Source\j_CalcAddPointByClassCPlayerQEAAXXZ_1400136CE.cpp" />
    <ClCompile Include="Source\j_CalcAttExpCAnimusQEAAXPEAVCAttackZ_14000F7D1.cpp" />
    <ClCompile Include="Source\j_CalcCharGradeCPlayerDBSAEEGZ_140012A8A.cpp" />
    <ClCompile Include="Source\j_CalcCirclePlayerNumCGameObjectQEAAHHP6A_NPEAV1ZZ_1400131BA.cpp" />
    <ClCompile Include="Source\j_CalcCirclePlayerNumCGameObjectQEAAHHZ_14000D8CD.cpp" />
    <ClCompile Include="Source\j_CalcCurFPRateCPlayerQEAAGXZ_140006D93.cpp" />
    <ClCompile Include="Source\j_CalcCurHPRateCPlayerUEAAGXZ_14001043D.cpp" />
    <ClCompile Include="Source\j_CalcCurSPRateCPlayerQEAAGXZ_14000100A.cpp" />
    <ClCompile Include="Source\j_CalcDefExpCAnimusQEAAXPEAVCCharacterHZ_14000A4A2.cpp" />
    <ClCompile Include="Source\j_CalcDefTolCPlayerQEAAXXZ_140004FC5.cpp" />
    <ClCompile Include="Source\j_CalcDistForSecCCharacterQEAAMMMZ_14000C112.cpp" />
    <ClCompile Include="Source\j_CalcDPRateCPlayerQEAAMXZ_1400052C2.cpp" />
    <ClCompile Include="Source\j_CalcEffectBitCCharacterQEAAGGGZ_14000C4AF.cpp" />
    <ClCompile Include="Source\j_CalcEquipAttackDelayCPlayerQEAAHXZ_140001456.cpp" />
    <ClCompile Include="Source\j_CalcEquipMaxDPCPlayerQEAAX_NZ_14000CB3F.cpp" />
    <ClCompile Include="Source\j_CalcEquipSpeedCPlayerQEAAXXZ_1400096FB.cpp" />
    <ClCompile Include="Source\j_CalcExpCPlayerQEAAXPEAVCCharacterHAEAVCPartyMode_1400026F8.cpp" />
    <ClCompile Include="Source\j_CalcPvPCPlayerQEAAXPEAV1EZ_14000A489.cpp" />
    <ClCompile Include="Source\j_CalPvpCashPointCPlayerQEAANHHPEADZ_14000D355.cpp" />
    <ClCompile Include="Source\j_CalPvpTempCashCPlayerQEAAXPEAV1EZ_140002617.cpp" />
    <ClCompile Include="Source\j_CancelCRaceBossMsgControllerQEAA_NEKPEAVCPlayerZ_140007ADB.cpp" />
    <ClCompile Include="Source\j_CancelPlayerRaceBuffCRaceBuffByHolyQuestProcedur_140005899.cpp" />
    <ClCompile Include="Source\j_CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAVCPl_1400032A6.cpp" />
    <ClCompile Include="Source\j_CanDoEventRFEvent_ClassRefineEEAAHPEBVCPlayerZ_1400084F9.cpp" />
    <ClCompile Include="Source\j_CanYouEnterHoleCDarkHoleChannelQEAA_NPEAVCPlayer_14000207C.cpp" />
    <ClCompile Include="Source\j_CascadeExponentiateBaseAndPublicElementDL_Public_140007572.cpp" />
    <ClCompile Include="Source\j_ChangeOwnerCParkingUnitQEAAXPEAVCPlayerEZ_14000DD50.cpp" />
    <ClCompile Include="Source\j_ChangeTarget_MasterCommandCAnimusQEAA_NPEAVCChar_14000A75E.cpp" />
    <ClCompile Include="Source\j_change_playerCGuildMasterEffectQEAA_NPEAVCPlayer_14000F52E.cpp" />
    <ClCompile Include="Source\j_CharacterRenameCashCNetworkEXAEAA_NHPEADZ_140004930.cpp" />
    <ClCompile Include="Source\j_CheatCreateFieldObjectCGuildBattleControllerQEAA_140012639.cpp" />
    <ClCompile Include="Source\j_CheatDestroyFieldObjectCGuildBattleControllerQEA_1400018D9.cpp" />
    <ClCompile Include="Source\j_CheatDestroyStoneCGuildBattleControllerQEAA_NPEA_14000B3C5.cpp" />
    <ClCompile Include="Source\j_CheatDropStoneCGuildBattleControllerQEAA_NPEAVCP_140011DCE.cpp" />
    <ClCompile Include="Source\j_CheatDropStoneCNormalGuildBattleFieldGUILD_BATTL_14000AE07.cpp" />
    <ClCompile Include="Source\j_CheatForceTakeStoneCGuildBattleControllerQEAA_NP_140010D3E.cpp" />
    <ClCompile Include="Source\j_CheatForceTakeStoneCNormalGuildBattleFieldGUILD__14000545C.cpp" />
    <ClCompile Include="Source\j_CheatGetCGravityStoneQEAAEPEAVCPlayerZ_140010F87.cpp" />
    <ClCompile Include="Source\j_CheatGetStoneCGuildBattleControllerQEAA_NPEAVCPl_14000A38F.cpp" />
    <ClCompile Include="Source\j_CheatGetStoneCNormalGuildBattleFieldGUILD_BATTLE_140002C25.cpp" />
    <ClCompile Include="Source\j_CheatRegenStoneCGuildBattleControllerQEAAHPEAVCP_140013575.cpp" />
    <ClCompile Include="Source\j_CheatRegenStoneCNormalGuildBattleFieldGUILD_BATT_1400071B7.cpp" />
    <ClCompile Include="Source\j_CheatSetPatriarchPatriarchElectProcessorQEAA_NPE_140002973.cpp" />
    <ClCompile Include="Source\j_CheatTakeStoneCGuildBattleControllerQEAA_NHPEAVC_140009EA3.cpp" />
    <ClCompile Include="Source\j_CheatTakeStoneCNormalGuildBattleFieldGUILD_BATTL_14000BD89.cpp" />
    <ClCompile Include="Source\j_CheckAlterMaxPointCPlayerQEAAXXZ_140004F1B.cpp" />
    <ClCompile Include="Source\j_CheckBattleModeCPlayerQEAAXXZ_140004C00.cpp" />
    <ClCompile Include="Source\j_CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAV_14000B13B.cpp" />
    <ClCompile Include="Source\j_CheckBuyCUnmannedTraderUserInfoAEAAEEPEAU_unmann_140004142.cpp" />
    <ClCompile Include="Source\j_CheckCouponTypeCashItemRemoteStoreQEAAHPEAU_STOR_14001317E.cpp" />
    <ClCompile Include="Source\j_CheckCRealMoveRequestDelayCheckerQEAA_NPEAVCPlay_14000A452.cpp" />
    <ClCompile Include="Source\j_CheckDBValidCharacterCandidateMgrQEAAHEZ_140005AE7.cpp" />
    <ClCompile Include="Source\j_CheckDQSLoadCharacterDataCUserDBSA_NPEAU_AVATOR__14000BA14.cpp" />
    <ClCompile Include="Source\j_CheckEmotionStateEmotionPresentationCheckerQEAA__14000BFA0.cpp" />
    <ClCompile Include="Source\j_CheckEventEmotionPresentationCMonsterQEAA_NEPEAV_140007991.cpp" />
    <ClCompile Include="Source\j_CheckGetGravityStoneCGuildBattleControllerQEAAXG_14000FC59.cpp" />
    <ClCompile Include="Source\j_CheckGoalCGuildBattleControllerQEAAXPEAVCPlayerH_140013E62.cpp" />
    <ClCompile Include="Source\j_CheckGroupMapPointCPlayerQEAAXXZ_140009B8D.cpp" />
    <ClCompile Include="Source\j_CheckGroupTargetingCPlayerQEAAXXZ_1400044DF.cpp" />
    <ClCompile Include="Source\j_CheckHolyMasterCHolyStoneSystemQEAA_NPEAVCPlayer_140010A41.cpp" />
    <ClCompile Include="Source\j_CheckLootItemCMonsterQEAAXPEAVCPlayerZ_14000797D.cpp" />
    <ClCompile Include="Source\j_CheckMentalTakeAndUpdateLastMetalTicketCPlayerQE_14000B8A2.cpp" />
    <ClCompile Include="Source\j_CheckNameChangeCPlayerQEAAXXZ_140010D57.cpp" />
    <ClCompile Include="Source\j_CheckNuclearStateCNuclearBombMgrQEAAXPEAVCPlayer_140005669.cpp" />
    <ClCompile Include="Source\j_CheckPlayerStatusTimeLimitMgrQEAA_NGKPEAEPEAKZ_140011617.cpp" />
    <ClCompile Include="Source\j_CheckPosInTownCPlayerQEAAXXZ_140007D9C.cpp" />
    <ClCompile Include="Source\j_CheckPos_RegionCPlayerQEAAXXZ_140003850.cpp" />
    <ClCompile Include="Source\j_CheckPotionTimeCExtPotionBufQEAAXPEAVCPlayerZ_14000447B.cpp" />
    <ClCompile Include="Source\j_CheckPreAttackRangeTargetAbleCharacterCMonsterHe_140006447.cpp" />
    <ClCompile Include="Source\j_CheckPvpHaveConditionCPvpCashPointQEAA_NPEAVCPla_1400114C8.cpp" />
    <ClCompile Include="Source\j_CheckPvpLoseConditionCPvpCashPointQEAA_NPEAVCPla_14001242C.cpp" />
    <ClCompile Include="Source\j_CheckRegisterCPostSystemManagerQEAAEPEAVCPlayerP_140007F2C.cpp" />
    <ClCompile Include="Source\j_CheckSellCompleteCUnmannedTraderUserInfoQEAAEPEA_140011D1F.cpp" />
    <ClCompile Include="Source\j_CheckSPF_MON_MOTIVE_ATTACK_MODE_PASSAGEDfAIMgrSA_14000E124.cpp" />
    <ClCompile Include="Source\j_CheckSPF_MON_MOTIVE_DFDfAIMgrSAHPEAVCMonsterSkil_14000B4A6.cpp" />
    <ClCompile Include="Source\j_CheckSPF_MON_MOTIVE_MY_HP_DOWNDfAIMgrSAHPEAVCMon_14000EC32.cpp" />
    <ClCompile Include="Source\j_CheckSPF_MON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAVC_14000192E.cpp" />
    <ClCompile Include="Source\j_CheckTakeGravityStoneCGuildBattleControllerQEAAX_140008157.cpp" />
    <ClCompile Include="Source\j_CheckTicket_KickCTransportShipQEAAXPEAVCPlayerHZ_140001E38.cpp" />
    <ClCompile Include="Source\j_CheckTicket_PassCTransportShipQEAAXPEAVCPlayerHZ_140007D38.cpp" />
    <ClCompile Include="Source\j_CheckUnitCutTimeCPlayerQEAAXXZ_1400130CF.cpp" />
    <ClCompile Include="Source\j_Cheet_BufEffectEndCPlayerQEAAXXZ_140001A4B.cpp" />
    <ClCompile Include="Source\j_ClassCodePasingCPcBangFavorQEAAKPEAU_AVATOR_DATA_1400089FE.cpp" />
    <ClCompile Include="Source\j_ClassSkillRecallTeleportRequestCNetworkEXAEAA_NH_14000D57B.cpp" />
    <ClCompile Include="Source\j_ClassSkillRequestCNetworkEXAEAA_NHPEADZ_14000A15A.cpp" />
    <ClCompile Include="Source\j_ClearCPvpPointLimiterQEAAX_JNPEAVCPlayerZ_14000E485.cpp" />
    <ClCompile Include="Source\j_ClearGravityStoneCPlayerQEAAXXZ_14000423C.cpp" />
    <ClCompile Include="Source\j_ClearMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPE_14000B4F1.cpp" />
    <ClCompile Include="Source\j_CombinePreProcessCTalkCrystalCombineManagerIEAAE_14000F164.cpp" />
    <ClCompile Include="Source\j_CompleteUpdatePlayerVoteInfoCMainThreadQEAAXPEAD_140013C0A.cpp" />
    <ClCompile Include="Source\j_ConsumeMeterial_And_CalculateNewItemsItemCombine_14000685C.cpp" />
    <ClCompile Include="Source\j_ConvertAvatorDBCPlayerDBQEAA_NPEAU_AVATOR_DATAZ_14001210C.cpp" />
    <ClCompile Include="Source\j_ConvertGeneralDBCPlayerDBQEAA_NPEAU_AVATOR_DATA0_140007E3C.cpp" />
    <ClCompile Include="Source\j_ConvertTargetPlayerCMonsterQEAA_NPEAVCPlayerZ_14000D2F6.cpp" />
    <ClCompile Include="Source\j_CopyCMonsterSkillIEAAXAEBV1Z_1400033D2.cpp" />
    <ClCompile Include="Source\j_CorpseCPlayerQEAA_NPEAVCCharacterZ_1400101B8.cpp" />
    <ClCompile Include="Source\j_CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAVCPlayer_1400056DC.cpp" />
    <ClCompile Include="Source\j_CreateCCharacterQEAA_NPEAU_character_create_setd_14000EE0D.cpp" />
    <ClCompile Include="Source\j_CreateCharacterSelectLogTableCRFWorldDatabaseQEA_1400037A6.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCMoveMapLimitManagerQEAAXPEAVCPlay_14000D107.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCMoveMapLimitRightInfoListQEAAXPEA_14001149B.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCMoveMapLimitRightInfoQEAAXPEAVCPl_14000A592.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCMoveMapLimitRightPortalUEAAXPEAVC_140001D7A.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCMoveMapLimitRightUEAAXPEAVCPlayer_140013D09.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCNationSettingDataCNUEAAXPEAVCPlay_14000F0A1.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCNationSettingDataNULLUEAAXPEAVCPl_140010FCD.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCNationSettingDataUEAAXPEAVCPlayer_14000D7B0.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCNationSettingManagerQEAAXPEAVCPla_14001349E.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCPlayerQEAAXXZ_1400107B7.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCRaceBuffByHolyQuestProcedureQEAA__140010983.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCRaceBuffInfoByHolyQuestfGroupQEAA_140007752.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCRaceBuffInfoByHolyQuestListQEAA_N_1400081DE.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAV_140010E1F.cpp" />
    <ClCompile Include="Source\j_CreateCompleteCRaceBuffManagerQEAA_NPEAVCPlayerZ_14000AE75.cpp" />
    <ClCompile Include="Source\j_CreateCPlayerQEAA_NXZ_14001125C.cpp" />
    <ClCompile Include="Source\j_CreateCUnmannedTraderSubClassInfoLevelUEAAPEAVCU_14000AEC5.cpp" />
    <ClCompile Include="Source\j_CreateGuardTowerYAPEAVCGuardTowerPEAVCMapDataGPE_1400046BA.cpp" />
    <ClCompile Include="Source\j_CreateItemBoxYAPEAVCItemBoxPEAU_db_con_STORAGE_L_140009D59.cpp" />
    <ClCompile Include="Source\j_CreateItemBoxYAPEAVCItemBoxPEAU_db_con_STORAGE_L_14000AE5C.cpp" />
    <ClCompile Include="Source\j_CreateMissileCNuclearBombMgrQEAA_NPEAVCPlayerPEA_14000A69B.cpp" />
    <ClCompile Include="Source\j_CreateSelectCharacterLogTableCMainThreadAEAAXEZ_14000BAF0.cpp" />
    <ClCompile Include="Source\j_CreateTblLtd_ExpendCRFDBItemLogQEAA_NHZ_14000496C.cpp" />
    <ClCompile Include="Source\j_CreateTrapYAPEAVCTrapPEAVCMapDataGPEAMPEAVCPlaye_140002383.cpp" />
    <ClCompile Include="Source\j_ct_action_point_setYA_NPEAVCPlayerZ_1400025CC.cpp" />
    <ClCompile Include="Source\j_ct_add_guild_scheduleYA_NPEAVCPlayerZ_140007FC7.cpp" />
    <ClCompile Include="Source\j_ct_add_one_day_guild_scheduleYA_NPEAVCPlayerZ_140008215.cpp" />
    <ClCompile Include="Source\j_ct_all_item_muziYA_NPEAVCPlayerZ_1400058E9.cpp" />
    <ClCompile Include="Source\j_ct_all_mapYA_NPEAVCPlayerZ_14000C6EE.cpp" />
    <ClCompile Include="Source\j_ct_alter_cashbagYA_NPEAVCPlayerZ_140003FBC.cpp" />
    <ClCompile Include="Source\j_ct_alter_dalantYA_NPEAVCPlayerZ_140002734.cpp" />
    <ClCompile Include="Source\j_ct_alter_expYA_NPEAVCPlayerZ_1400095ED.cpp" />
    <ClCompile Include="Source\j_ct_alter_goldYA_NPEAVCPlayerZ_140009C7D.cpp" />
    <ClCompile Include="Source\j_ct_alter_inven_durYA_NPEAVCPlayerZ_140002536.cpp" />
    <ClCompile Include="Source\j_ct_alter_lvYA_NPEAVCPlayerZ_14000AF10.cpp" />
    <ClCompile Include="Source\j_ct_alter_pvpYA_NPEAVCPlayerZ_14000DB8E.cpp" />
    <ClCompile Include="Source\j_ct_amp_fullYA_NPEAVCPlayerZ_140001F41.cpp" />
    <ClCompile Include="Source\j_ct_amp_setYA_NPEAVCPlayerZ_14000718F.cpp" />
    <ClCompile Include="Source\j_ct_animusexpYA_NPEAVCPlayerZ_140002D6A.cpp" />
    <ClCompile Include="Source\j_ct_animus_attack_gradeYA_NPEAVCPlayerZ_14000ADE9.cpp" />
    <ClCompile Include="Source\j_ct_animus_recall_termYA_NPEAVCPlayerZ_14000B2BC.cpp" />
    <ClCompile Include="Source\j_ct_basemasteryYA_NPEAVCPlayerZ_14000F655.cpp" />
    <ClCompile Include="Source\j_ct_boss_sms_cancelYA_NPEAVCPlayerZ_14000C82E.cpp" />
    <ClCompile Include="Source\j_ct_buf_potion_useYA_NPEAVCPlayerZ_14000917E.cpp" />
    <ClCompile Include="Source\j_ct_CashEventStartYA_NPEAVCPlayerZ_14000CE19.cpp" />
    <ClCompile Include="Source\j_ct_cashitembuyYA_NPEAVCPlayerZ_1400082B5.cpp" />
    <ClCompile Include="Source\j_ct_CdeEndupYA_NPEAVCPlayerZ_14000B4DD.cpp" />
    <ClCompile Include="Source\j_ct_CdeStartYA_NPEAVCPlayerZ_14001332C.cpp" />
    <ClCompile Include="Source\j_ct_change_classYA_NPEAVCPlayerZ_140004403.cpp" />
    <ClCompile Include="Source\j_ct_change_degreeYA_NPEAVCPlayerZ_14001130B.cpp" />
    <ClCompile Include="Source\j_ct_change_masteryYA_NPEAVCPlayerZ_140008413.cpp" />
    <ClCompile Include="Source\j_ct_change_master_electYA_NPEAVCPlayerZ_1400063F7.cpp" />
    <ClCompile Include="Source\j_ct_chatsaveYA_NPEAVCPlayerZ_140007A2C.cpp" />
    <ClCompile Include="Source\j_ct_check_guild_batlle_goalYA_NPEAVCPlayerZ_14000C171.cpp" />
    <ClCompile Include="Source\j_ct_circle_mon_killYA_NPEAVCPlayerZ_140003A4E.cpp" />
    <ClCompile Include="Source\j_ct_circle_user_numYA_NPEAVCPlayerZ_140005E2A.cpp" />
    <ClCompile Include="Source\j_ct_ClassRefineEventYA_NPEAVCPlayerZ_140008B5C.cpp" />
    <ClCompile Include="Source\j_ct_ClearSettleOwnerGuildYA_NPEAVCPlayerZ_1400024F0.cpp" />
    <ClCompile Include="Source\j_ct_combine_ex_resultYA_NPEAVCPlayerZ_140011CF2.cpp" />
    <ClCompile Include="Source\j_ct_complete_questYA_NPEAVCPlayerZ_140011ED2.cpp" />
    <ClCompile Include="Source\j_ct_complete_quest_otherYA_NPEAVCPlayerZ_140010659.cpp" />
    <ClCompile Include="Source\j_ct_ConEventStartYA_NPEAVCPlayerZ_140001730.cpp" />
    <ClCompile Include="Source\j_ct_continue_palytime_incYA_NPEAVCPlayerZ_14000F51F.cpp" />
    <ClCompile Include="Source\j_ct_cont_effet_clearYA_NPEAVCPlayerZ_14000F029.cpp" />
    <ClCompile Include="Source\j_ct_cont_effet_timeYA_NPEAVCPlayerZ_140007658.cpp" />
    <ClCompile Include="Source\j_ct_copy_avatorYA_NPEAVCPlayerZ_140012E18.cpp" />
    <ClCompile Include="Source\j_ct_create_guildbattle_field_objectYA_NPEAVCPlaye_14000784C.cpp" />
    <ClCompile Include="Source\j_ct_cur_guildbattle_colorYA_NPEAVCPlayerZ_140011C98.cpp" />
    <ClCompile Include="Source\j_ct_darkholerewardYA_NPEAVCPlayerZ_14000108C.cpp" />
    <ClCompile Include="Source\j_ct_defense_item_graceYA_NPEAVCPlayerZ_14000F678.cpp" />
    <ClCompile Include="Source\j_ct_defense_item_grace_JpYA_NPEAVCPlayerZ_14000185C.cpp" />
    <ClCompile Include="Source\j_ct_destroy_gravitystoneYA_NPEAVCPlayerZ_140002D74.cpp" />
    <ClCompile Include="Source\j_ct_destroy_guildbattle_field_objectYA_NPEAVCPlay_140009DA9.cpp" />
    <ClCompile Include="Source\j_ct_destroy_system_towerYA_NPEAVCPlayerZ_14000222F.cpp" />
    <ClCompile Include="Source\j_ct_dieYA_NPEAVCPlayerZ_14000977D.cpp" />
    <ClCompile Include="Source\j_ct_drop_gravitystoneYA_NPEAVCPlayerZ_14000B08C.cpp" />
    <ClCompile Include="Source\j_ct_drop_jadeYA_NPEAVCPlayerZ_140013BBF.cpp" />
    <ClCompile Include="Source\j_ct_elect_info_playerYA_NPEAVCPlayerZ_140005083.cpp" />
    <ClCompile Include="Source\j_ct_elect_set_envYA_NPEAVCPlayerZ_140011649.cpp" />
    <ClCompile Include="Source\j_ct_elect_set_playerYA_NPEAVCPlayerZ_140013C6E.cpp" />
    <ClCompile Include="Source\j_ct_eventset_startYA_NPEAVCPlayerZ_14000FD21.cpp" />
    <ClCompile Include="Source\j_ct_eventset_stopYA_NPEAVCPlayerZ_1400030AD.cpp" />
    <ClCompile Include="Source\j_ct_exceptionYA_NPEAVCPlayerZ_1400128EB.cpp" />
    <ClCompile Include="Source\j_ct_exip_keeperYA_NPEAVCPlayerZ_140002ECD.cpp" />
    <ClCompile Include="Source\j_ct_exit_stoneYA_NPEAVCPlayerZ_140011879.cpp" />
    <ClCompile Include="Source\j_ct_expire_pcbangYA_NPEAVCPlayerZ_1400063CF.cpp" />
    <ClCompile Include="Source\j_ct_free_ride_shipYA_NPEAVCPlayerZ_14000E4B7.cpp" />
    <ClCompile Include="Source\j_ct_free_sf_by_classYA_NPEAVCPlayerZ_140014001.cpp" />
    <ClCompile Include="Source\j_ct_fullsetYA_NPEAVCPlayerZ_140008E0E.cpp" />
    <ClCompile Include="Source\j_ct_full_animus_gaugeYA_NPEAVCPlayerZ_14000DFD0.cpp" />
    <ClCompile Include="Source\j_ct_full_forceYA_NPEAVCPlayerZ_140003562.cpp" />
    <ClCompile Include="Source\j_ct_full_gaugeYA_NPEAVCPlayerZ_140007FD6.cpp" />
    <ClCompile Include="Source\j_ct_get_gravitystoneYA_NPEAVCPlayerZ_1400088C8.cpp" />
    <ClCompile Include="Source\j_ct_Gold_Age_Event_StatusYA_NPEAVCPlayerZ_14000F533.cpp" />
    <ClCompile Include="Source\j_ct_Gold_Age_Get_Box_CntYA_NPEAVCPlayerZ_14000C7C0.cpp" />
    <ClCompile Include="Source\j_ct_Gold_Age_Set_Event_StatusYA_NPEAVCPlayerZ_14001289B.cpp" />
    <ClCompile Include="Source\j_ct_goto_charYA_NPEAVCPlayerZ_140011CDE.cpp" />
    <ClCompile Include="Source\j_ct_goto_mineYA_NPEAVCPlayerZ_140004147.cpp" />
    <ClCompile Include="Source\j_ct_goto_monsterYA_NPEAVCPlayerZ_140001A6E.cpp" />
    <ClCompile Include="Source\j_ct_goto_npcYA_NPEAVCPlayerZ_1400033BE.cpp" />
    <ClCompile Include="Source\j_ct_goto_shipport_ederYA_NPEAVCPlayerZ_1400122E7.cpp" />
    <ClCompile Include="Source\j_ct_goto_shipport_townYA_NPEAVCPlayerZ_140002860.cpp" />
    <ClCompile Include="Source\j_ct_goto_stoneYA_NPEAVCPlayerZ_14000C153.cpp" />
    <ClCompile Include="Source\j_ct_guild_battle_force_stoneYA_NPEAVCPlayerZ_14000DF8F.cpp" />
    <ClCompile Include="Source\j_ct_guild_callYA_NPEAVCPlayerZ_14000AE6B.cpp" />
    <ClCompile Include="Source\j_ct_guild_infoYA_NPEAVCPlayerZ_140011FAE.cpp" />
    <ClCompile Include="Source\j_ct_guild_suggestYA_NPEAVCPlayerZ_1400122C4.cpp" />
    <ClCompile Include="Source\j_ct_half_gaugeYA_NPEAVCPlayerZ_140003666.cpp" />
    <ClCompile Include="Source\j_ct_HolyKeeperAttackYA_NPEAVCPlayerZ_140003698.cpp" />
    <ClCompile Include="Source\j_ct_HolySystemYA_NPEAVCPlayerZ_14000B9D8.cpp" />
    <ClCompile Include="Source\j_ct_HolySystem_JpYA_NPEAVCPlayerZ_14000C4F0.cpp" />
    <ClCompile Include="Source\j_ct_InformCristalBattleBeforeAnHourYA_NPEAVCPlaye_140001F3C.cpp" />
    <ClCompile Include="Source\j_ct_InformPatriarchProcessorYA_NPEAVCPlayerZ_140002D7E.cpp" />
    <ClCompile Include="Source\j_ct_init_monsterYA_NPEAVCPlayerZ_140013F20.cpp" />
    <ClCompile Include="Source\j_ct_inven_emptyYA_NPEAVCPlayerZ_1400134A3.cpp" />
    <ClCompile Include="Source\j_ct_itemlootYA_NPEAVCPlayerZ_140003887.cpp" />
    <ClCompile Include="Source\j_ct_jump_to_posYA_NPEAVCPlayerZ_14000D95E.cpp" />
    <ClCompile Include="Source\j_ct_kick_playerYA_NPEAVCPlayerZ_140007D92.cpp" />
    <ClCompile Include="Source\j_ct_loadcashamountYA_NPEAVCPlayerZ_1400133FE.cpp" />
    <ClCompile Include="Source\j_ct_look_like_bossYA_NPEAVCPlayerZ_140008512.cpp" />
    <ClCompile Include="Source\j_ct_loot_bagYA_NPEAVCPlayerZ_14000D422.cpp" />
    <ClCompile Include="Source\j_ct_loot_dungeonYA_NPEAVCPlayerZ_14001324B.cpp" />
    <ClCompile Include="Source\j_ct_loot_itemYA_NPEAVCPlayerZ_1400033EB.cpp" />
    <ClCompile Include="Source\j_ct_loot_materialYA_NPEAVCPlayerZ_14000645B.cpp" />
    <ClCompile Include="Source\j_ct_loot_mineYA_NPEAVCPlayerZ_140006FC8.cpp" />
    <ClCompile Include="Source\j_ct_loot_towerYA_NPEAVCPlayerZ_14000C725.cpp" />
    <ClCompile Include="Source\j_ct_loot_upgradeYA_NPEAVCPlayerZ_140001DDE.cpp" />
    <ClCompile Include="Source\j_ct_loot_upgrade_itemYA_NPEAVCPlayerZ_140006BAE.cpp" />
    <ClCompile Include="Source\j_ct_lua_commandYA_NPEAVCPlayerZ_140003B25.cpp" />
    <ClCompile Include="Source\j_ct_makeitem_need_matrialYA_NPEAVCPlayerZ_1400110E5.cpp" />
    <ClCompile Include="Source\j_ct_makeitem_no_matrialYA_NPEAVCPlayerZ_140002B21.cpp" />
    <ClCompile Include="Source\j_ct_make_system_towerYA_NPEAVCPlayerZ_14000B087.cpp" />
    <ClCompile Include="Source\j_ct_manage_guildYA_NPEAVCPlayerZ_1400098B3.cpp" />
    <ClCompile Include="Source\j_ct_max_attackYA_NPEAVCPlayerZ_1400137EB.cpp" />
    <ClCompile Include="Source\j_ct_mepcbangYA_NPEAVCPlayerZ_14000A1DC.cpp" />
    <ClCompile Include="Source\j_ct_minespeedYA_NPEAVCPlayerZ_1400113E2.cpp" />
    <ClCompile Include="Source\j_ct_min_attackYA_NPEAVCPlayerZ_140003292.cpp" />
    <ClCompile Include="Source\j_ct_mormal_attackYA_NPEAVCPlayerZ_1400113A6.cpp" />
    <ClCompile Include="Source\j_ct_NuAfterEffectYA_NPEAVCPlayerZ_14000B938.cpp" />
    <ClCompile Include="Source\j_ct_party_callYA_NPEAVCPlayerZ_140012AA3.cpp" />
    <ClCompile Include="Source\j_ct_pass_dungeonYA_NPEAVCPlayerZ_140004C73.cpp" />
    <ClCompile Include="Source\j_ct_pass_schYA_NPEAVCPlayerZ_14000F6B9.cpp" />
    <ClCompile Include="Source\j_ct_pcanimusexpYA_NPEAVCPlayerZ_140008A67.cpp" />
    <ClCompile Include="Source\j_ct_PcBandPrimiumYA_NPEAVCPlayerZ_14000AC59.cpp" />
    <ClCompile Include="Source\j_ct_pcbangitemgetYA_NPEAVCPlayerZ_140010F50.cpp" />
    <ClCompile Include="Source\j_ct_pcbasemasteryYA_NPEAVCPlayerZ_14000B1DB.cpp" />
    <ClCompile Include="Source\j_ct_pcitemlootYA_NPEAVCPlayerZ_1400081F7.cpp" />
    <ClCompile Include="Source\j_ct_pcminespeedYA_NPEAVCPlayerZ_140003256.cpp" />
    <ClCompile Include="Source\j_ct_pcplayerexpYA_NPEAVCPlayerZ_14001400B.cpp" />
    <ClCompile Include="Source\j_ct_pcroom_premiumYA_NPEAVCPlayerZ_1400011CC.cpp" />
    <ClCompile Include="Source\j_ct_pcsfmasteryYA_NPEAVCPlayerZ_14000EEDF.cpp" />
    <ClCompile Include="Source\j_ct_period_time_setYA_NPEAVCPlayerZ_1400094A3.cpp" />
    <ClCompile Include="Source\j_ct_playerexpYA_NPEAVCPlayerZ_140010A19.cpp" />
    <ClCompile Include="Source\j_ct_premium_rateYA_NPEAVCPlayerZ_1400123AA.cpp" />
    <ClCompile Include="Source\j_ct_PvpLimitInitYA_NPEAVCPlayerZ_14000E93A.cpp" />
    <ClCompile Include="Source\j_ct_query_remain_oreYA_NPEAVCPlayerZ_14001032A.cpp" />
    <ClCompile Include="Source\j_ct_recall_monsterYA_NPEAVCPlayerZ_14000FD12.cpp" />
    <ClCompile Include="Source\j_ct_recall_playerYA_NPEAVCPlayerZ_14000EF43.cpp" />
    <ClCompile Include="Source\j_ct_recv_change_atrad_taxrateYA_NPEAVCPlayerZ_14000E6D8.cpp" />
    <ClCompile Include="Source\j_ct_recv_current_battle_infoYA_NPEAVCPlayerZ_140008D46.cpp" />
    <ClCompile Include="Source\j_ct_recv_pvp_guild_rankYA_NPEAVCPlayerZ_14000F1B4.cpp" />
    <ClCompile Include="Source\j_ct_recv_reserved_schedulelistYA_NPEAVCPlayerZ_14001107C.cpp" />
    <ClCompile Include="Source\j_ct_recv_total_guild_rankYA_NPEAVCPlayerZ_1400048DB.cpp" />
    <ClCompile Include="Source\j_ct_regen_gravitystoneYA_NPEAVCPlayerZ_14000EE44.cpp" />
    <ClCompile Include="Source\j_ct_release_loot_freeYA_NPEAVCPlayerZ_140011478.cpp" />
    <ClCompile Include="Source\j_ct_release_make_succYA_NPEAVCPlayerZ_140003607.cpp" />
    <ClCompile Include="Source\j_ct_release_matchlessYA_NPEAVCPlayerZ_14000480E.cpp" />
    <ClCompile Include="Source\j_ct_release_never_dieYA_NPEAVCPlayerZ_140013B01.cpp" />
    <ClCompile Include="Source\j_ct_release_punishmentYA_NPEAVCPlayerZ_14000B1EF.cpp" />
    <ClCompile Include="Source\j_ct_remove_sf_delayYA_NPEAVCPlayerZ_1400063FC.cpp" />
    <ClCompile Include="Source\j_ct_report_cri_hpYA_NPEAVCPlayerZ_140007040.cpp" />
    <ClCompile Include="Source\j_ct_report_positionYA_NPEAVCPlayerZ_14000F321.cpp" />
    <ClCompile Include="Source\j_ct_ReqChangeHonorGuildYA_NPEAVCPlayerZ_140001F87.cpp" />
    <ClCompile Include="Source\j_ct_ReqPunishmentYA_NPEAVCPlayerZ_1400092FF.cpp" />
    <ClCompile Include="Source\j_ct_request_delete_questYA_NPEAVCPlayerZ_1400070AE.cpp" />
    <ClCompile Include="Source\j_ct_request_npc_questYA_NPEAVCPlayerZ_140003C42.cpp" />
    <ClCompile Include="Source\j_ct_respawn_startYA_NPEAVCPlayerZ_140006A5A.cpp" />
    <ClCompile Include="Source\j_ct_respawn_stopYA_NPEAVCPlayerZ_14001389F.cpp" />
    <ClCompile Include="Source\j_ct_resurrect_playerYA_NPEAVCPlayerZ_140008134.cpp" />
    <ClCompile Include="Source\j_ct_server_rateYA_NPEAVCPlayerZ_14000ED4A.cpp" />
    <ClCompile Include="Source\j_ct_server_timeYA_NPEAVCPlayerZ_14000AB1E.cpp" />
    <ClCompile Include="Source\j_ct_SetGuildGradeByGuildSerialYA_NPEAVCPlayerZ_1400092A0.cpp" />
    <ClCompile Include="Source\j_ct_SetGuildGradeByNameYA_NPEAVCPlayerZ_140013C14.cpp" />
    <ClCompile Include="Source\j_ct_SetGuildGradeYA_NPEAVCPlayerZ_140011A0E.cpp" />
    <ClCompile Include="Source\j_ct_SetGuildMasterYA_NPEAVCPlayerZ_14000C1FD.cpp" />
    <ClCompile Include="Source\j_ct_SetMaxLevelLimitYA_NPEAVCPlayerZ_140013DA9.cpp" />
    <ClCompile Include="Source\j_ct_SetPatriarchAutoYA_NPEAVCPlayerZ_140007A63.cpp" />
    <ClCompile Include="Source\j_ct_SetPatriarchClearYA_NPEAVCPlayerZ_14000DC01.cpp" />
    <ClCompile Include="Source\j_ct_SetPatriarchGroupYA_NPEAVCPlayerZ_14000932C.cpp" />
    <ClCompile Include="Source\j_ct_SetPatriarchProcessorYA_NPEAVCPlayerZ_14000ABC8.cpp" />
    <ClCompile Include="Source\j_ct_SetSettleOwnerGuildYA_NPEAVCPlayerZ_14000DA3A.cpp" />
    <ClCompile Include="Source\j_ct_set_animus_expYA_NPEAVCPlayerZ_1400070B3.cpp" />
    <ClCompile Include="Source\j_ct_set_animus_lvYA_NPEAVCPlayerZ_14000E6BA.cpp" />
    <ClCompile Include="Source\j_ct_set_damage_partYA_NPEAVCPlayerZ_14000CD9C.cpp" />
    <ClCompile Include="Source\j_ct_set_exp_rateYA_NPEAVCPlayerZ_140001159.cpp" />
    <ClCompile Include="Source\j_ct_set_guildbattle_colorYA_NPEAVCPlayerZ_140013FA7.cpp" />
    <ClCompile Include="Source\j_ct_set_hfs_fullYA_NPEAVCPlayerZ_1400072BB.cpp" />
    <ClCompile Include="Source\j_ct_set_hpYA_NPEAVCPlayerZ_14000CD6F.cpp" />
    <ClCompile Include="Source\j_ct_set_jade_effectYA_NPEAVCPlayerZ_140013CC3.cpp" />
    <ClCompile Include="Source\j_ct_set_kill_list_initYA_NPEAVCPlayerZ_14000D99F.cpp" />
    <ClCompile Include="Source\j_ct_set_loot_freeYA_NPEAVCPlayerZ_140004C7D.cpp" />
    <ClCompile Include="Source\j_ct_set_make_succYA_NPEAVCPlayerZ_14000B339.cpp" />
    <ClCompile Include="Source\j_ct_set_matchlessYA_NPEAVCPlayerZ_140012C10.cpp" />
    <ClCompile Include="Source\j_ct_set_never_dieYA_NPEAVCPlayerZ_140004291.cpp" />
    <ClCompile Include="Source\j_ct_set_ore_amountYA_NPEAVCPlayerZ_14000272A.cpp" />
    <ClCompile Include="Source\j_ct_set_temp_cash_pointYA_NPEAVCPlayerZ_14000D396.cpp" />
    <ClCompile Include="Source\j_ct_sfmasteryYA_NPEAVCPlayerZ_14000BB31.cpp" />
    <ClCompile Include="Source\j_ct_start_criYA_NPEAVCPlayerZ_14000F4C0.cpp" />
    <ClCompile Include="Source\j_ct_start_keeperYA_NPEAVCPlayerZ_140010F3C.cpp" />
    <ClCompile Include="Source\j_ct_StateCHolyStoneSystemQEAA_NPEAVCPlayerZ_140003576.cpp" />
    <ClCompile Include="Source\j_ct_takeholymentalYA_NPEAVCPlayerZ_1400104DD.cpp" />
    <ClCompile Include="Source\j_ct_take_gravitystoneYA_NPEAVCPlayerZ_140013791.cpp" />
    <ClCompile Include="Source\j_ct_telekinesisYA_NPEAVCPlayerZ_140007EC3.cpp" />
    <ClCompile Include="Source\j_ct_tl_info_setYA_NPEAVCPlayerZ_14000407A.cpp" />
    <ClCompile Include="Source\j_ct_tl_info_viewYA_NPEAVCPlayerZ_14000B4B5.cpp" />
    <ClCompile Include="Source\j_ct_tl_system_settingYA_NPEAVCPlayerZ_14000D3B4.cpp" />
    <ClCompile Include="Source\j_ct_tracing_hideYA_NPEAVCPlayerZ_14000D49A.cpp" />
    <ClCompile Include="Source\j_ct_tracing_showYA_NPEAVCPlayerZ_1400132F0.cpp" />
    <ClCompile Include="Source\j_ct_trap_attack_gradeYA_NPEAVCPlayerZ_140010492.cpp" />
    <ClCompile Include="Source\j_ct_trunk_initYA_NPEAVCPlayerZ_1400017AD.cpp" />
    <ClCompile Include="Source\j_ct_up_allskillYA_NPEAVCPlayerZ_140006A55.cpp" />
    <ClCompile Include="Source\j_ct_up_allskill_ptYA_NPEAVCPlayerZ_14000D6E3.cpp" />
    <ClCompile Include="Source\j_ct_up_forceitemYA_NPEAVCPlayerZ_1400012DF.cpp" />
    <ClCompile Include="Source\j_ct_up_forcemasteryYA_NPEAVCPlayerZ_14001314C.cpp" />
    <ClCompile Include="Source\j_ct_up_skillYA_NPEAVCPlayerZ_14000CBA8.cpp" />
    <ClCompile Include="Source\j_ct_userchatbanYA_NPEAVCPlayerZ_14000B6E0.cpp" />
    <ClCompile Include="Source\j_ct_user_numYA_NPEAVCPlayerZ_14000735B.cpp" />
    <ClCompile Include="Source\j_ct_ut_cancel_registlogoutYA_NPEAVCPlayerZ_140002798.cpp" />
    <ClCompile Include="Source\j_ct_ut_cancel_registYA_NPEAVCPlayerZ_140007BE9.cpp" />
    <ClCompile Include="Source\j_ct_view_methodYA_NPEAVCPlayerZ_14000342C.cpp" />
    <ClCompile Include="Source\j_ct_vote_enableYA_NPEAVCPlayerZ_140006E1F.cpp" />
    <ClCompile Include="Source\j_ct_whoamiYA_NPEAVCPlayerZ_140012D46.cpp" />
    <ClCompile Include="Source\j_ct_Win_RaceWarYA_NPEAVCPlayerZ_14000E56B.cpp" />
    <ClCompile Include="Source\j_db_Log_AvatorLevelCMainThreadQEAAEKKEZ_14000893B.cpp" />
    <ClCompile Include="Source\j_DecHalfSFContDamCPlayerQEAA_NMZ_14000B802.cpp" />
    <ClCompile Include="Source\j_DecideRecallCRecallEffectControllerQEAAXGEPEAVCP_140009809.cpp" />
    <ClCompile Include="Source\j_DeleteCouponItemCPlayerQEAAXPEAU_STORAGE_POS_IND_1400090D9.cpp" />
    <ClCompile Include="Source\j_DeleteExchangeEventItemCExchangeEventQEAAXPEAVCP_140008981.cpp" />
    <ClCompile Include="Source\j_DeleteItemCountFromCodeCPlayerDBQEAA_NPEADHZ_1400121A2.cpp" />
    <ClCompile Include="Source\j_DeleteUseConsumeItemCPlayerQEAA_NPEAPEAU_db_con__14000B50A.cpp" />
    <ClCompile Include="Source\j_Delete_CharacterDataCRFWorldDatabaseQEAA_NKZ_140005790.cpp" />
    <ClCompile Include="Source\j_DelPostDataCPlayerQEAAXKZ_140011310.cpp" />
    <ClCompile Include="Source\j_DestroyCCharacterQEAA_NXZ_140007E14.cpp" />
    <ClCompile Include="Source\j_DestroyCHolyKeeperQEAA_NEPEAVCCharacterZ_140010348.cpp" />
    <ClCompile Include="Source\j_DestroyCHolyStoneQEAA_NEPEAVCCharacterZ_14000EE53.cpp" />
    <ClCompile Include="Source\j_dev_after_effectCPlayerQEAA_NXZ_14000D747.cpp" />
    <ClCompile Include="Source\j_dev_all_killCPlayerQEAA_NXZ_14000A23B.cpp" />
    <ClCompile Include="Source\j_dev_animus_recall_time_freeCPlayerQEAA_N_NZ_140007A68.cpp" />
    <ClCompile Include="Source\j_dev_avator_copyCPlayerQEAA_NPEADZ_140005E61.cpp" />
    <ClCompile Include="Source\j_dev_change_classCPlayerQEAA_NPEADZ_14000F330.cpp" />
    <ClCompile Include="Source\j_dev_cont_effect_delCPlayerQEAA_NXZ_1400010AA.cpp" />
    <ClCompile Include="Source\j_dev_cont_effect_timeCPlayerQEAA_NKZ_14000FECA.cpp" />
    <ClCompile Include="Source\j_dev_dalantCPlayerQEAA_NKZ_14000E048.cpp" />
    <ClCompile Include="Source\j_dev_dieCPlayerQEAA_NXZ_140007C16.cpp" />
    <ClCompile Include="Source\j_dev_drop_itemCPlayerQEAA_NPEADH0HZ_14000C243.cpp" />
    <ClCompile Include="Source\j_dev_free_sf_by_classCPlayerQEAA_NXZ_140004C64.cpp" />
    <ClCompile Include="Source\j_dev_full_animus_gaugeCPlayerQEAA_NXZ_14000A2B3.cpp" />
    <ClCompile Include="Source\j_dev_full_forceCPlayerQEAA_NXZ_140010474.cpp" />
    <ClCompile Include="Source\j_dev_full_pointCPlayerQEAA_NXZ_140007FF9.cpp" />
    <ClCompile Include="Source\j_dev_goldCPlayerQEAA_NKZ_14000F5FB.cpp" />
    <ClCompile Include="Source\j_dev_goto_monsterCPlayerQEAA_NPEAVCMonsterZ_140012107.cpp" />
    <ClCompile Include="Source\j_dev_goto_npcCPlayerQEAA_NPEAVCMerchantZ_14000C400.cpp" />
    <ClCompile Include="Source\j_dev_half_inven_amountCPlayerQEAA_N_KZ_14000C356.cpp" />
    <ClCompile Include="Source\j_dev_half_pointCPlayerQEAA_NXZ_14001279C.cpp" />
    <ClCompile Include="Source\j_dev_init_monsterCPlayerQEAA_NXZ_140010988.cpp" />
    <ClCompile Include="Source\j_dev_inven_emptyCPlayerQEAA_NXZ_14000F29A.cpp" />
    <ClCompile Include="Source\j_dev_item_make_no_use_matrialCPlayerQEAA_N_NZ_14000B811.cpp" />
    <ClCompile Include="Source\j_dev_loot_bagCPlayerQEAA_NXZ_14000434A.cpp" />
    <ClCompile Include="Source\j_dev_loot_freeCPlayerQEAA_N_NZ_14000925F.cpp" />
    <ClCompile Include="Source\j_dev_loot_fullitemCPlayerQEAA_NEZ_14000466F.cpp" />
    <ClCompile Include="Source\j_dev_loot_itemCPlayerQEAA_NPEADH0HZ_14000624E.cpp" />
    <ClCompile Include="Source\j_dev_loot_materialCPlayerQEAA_NXZ_14000208B.cpp" />
    <ClCompile Include="Source\j_dev_loot_mineCPlayerQEAA_NXZ_14000887D.cpp" />
    <ClCompile Include="Source\j_dev_loot_towerCPlayerQEAA_NXZ_14000DF3A.cpp" />
    <ClCompile Include="Source\j_dev_lvCPlayerQEAA_NHZ_1400138A4.cpp" />
    <ClCompile Include="Source\j_dev_make_succCPlayerQEAA_N_NZ_14000AFEC.cpp" />
    <ClCompile Include="Source\j_dev_max_level_extCPlayerQEAA_NEZ_1400078C4.cpp" />
    <ClCompile Include="Source\j_dev_never_dieCPlayerQEAA_N_NZ_14000B393.cpp" />
    <ClCompile Include="Source\j_dev_quest_completeCPlayerQEAA_NXZ_140013A66.cpp" />
    <ClCompile Include="Source\j_dev_quest_complete_otherCPlayerQEAA_NPEADZ_1400076DA.cpp" />
    <ClCompile Include="Source\j_dev_SetGuildGradeByGuildSerialCPlayerQEAA_NKEZ_1400028E2.cpp" />
    <ClCompile Include="Source\j_dev_SetGuildGradeByNameCPlayerQEAA_NPEADEZ_1400066BD.cpp" />
    <ClCompile Include="Source\j_dev_SetGuildGradeCPlayerQEAA_NEZ_14001075D.cpp" />
    <ClCompile Include="Source\j_dev_set_animus_expCPlayerQEAA_N_KZ_14000FB3C.cpp" />
    <ClCompile Include="Source\j_dev_set_animus_lvCPlayerQEAA_NHZ_140006B5E.cpp" />
    <ClCompile Include="Source\j_dev_set_hpCPlayerQEAA_NMZ_140006B63.cpp" />
    <ClCompile Include="Source\j_dev_trap_attack_gradeCPlayerQEAA_NHZ_140009192.cpp" />
    <ClCompile Include="Source\j_dev_up_allCPlayerQEAA_NHZ_14000BEEC.cpp" />
    <ClCompile Include="Source\j_dev_up_all_ptCPlayerQEAA_NHZ_140006686.cpp" />
    <ClCompile Include="Source\j_dev_up_cashbagCPlayerQEAA_NNZ_14000F272.cpp" />
    <ClCompile Include="Source\j_dev_up_forceitemCPlayerQEAA_NHZ_14000F3F8.cpp" />
    <ClCompile Include="Source\j_dev_up_forcemasteryCPlayerQEAA_NHZ_140009520.cpp" />
    <ClCompile Include="Source\j_dev_up_masteryCPlayerQEAA_NHHHZ_14000B5AA.cpp" />
    <ClCompile Include="Source\j_dev_up_pvpCPlayerQEAA_NNZ_140008003.cpp" />
    <ClCompile Include="Source\j_dev_up_skillCPlayerQEAA_NPEADHZ_1400125BC.cpp" />
    <ClCompile Include="Source\j_dev_view_bossCPlayerQEAA_NXZ_14000AE2F.cpp" />
    <ClCompile Include="Source\j_dev_view_methodCPlayerQEAA_NPEADZ_1400091C4.cpp" />
    <ClCompile Include="Source\j_DE_AllContDamageForceRemoveYA_NPEAVCCharacter0MA_140008A08.cpp" />
    <ClCompile Include="Source\j_DE_AllContHelpForceRemoveYA_NPEAVCCharacter0MAEA_140004629.cpp" />
    <ClCompile Include="Source\j_DE_AllContHelpSkillRemoveYA_NPEAVCCharacter0MAEA_140004FE3.cpp" />
    <ClCompile Include="Source\j_DE_AttHPtoDstFPYA_NPEAVCCharacter0MAEAEZ_14001115D.cpp" />
    <ClCompile Include="Source\j_DE_BattleMode_RecallCommonPlayerYA_NPEAVCCharact_140006C21.cpp" />
    <ClCompile Include="Source\j_DE_ContDamageTimeIncYA_NPEAVCCharacter0MAEAEZ_140003DDC.cpp" />
    <ClCompile Include="Source\j_DE_ContHelpTimeIncYA_NPEAVCCharacter0MAEAEZ_14000D823.cpp" />
    <ClCompile Include="Source\j_DE_ConvertMonsterTargetYA_NPEAVCCharacter0MAEAEZ_14000376A.cpp" />
    <ClCompile Include="Source\j_DE_ConvertTargetDestYA_NPEAVCCharacter0MAEAEZ_140006B95.cpp" />
    <ClCompile Include="Source\j_DE_DamStunYA_NPEAVCCharacter0MAEAEZ_14000B555.cpp" />
    <ClCompile Include="Source\j_DE_DetectTrapYA_NPEAVCCharacter0MAEAEZ_14000252C.cpp" />
    <ClCompile Include="Source\j_DE_FPDecYA_NPEAVCCharacter0MAEAEZ_1400066EF.cpp" />
    <ClCompile Include="Source\j_DE_HPIncYA_NPEAVCCharacter0MAEAEZ_140002FB3.cpp" />
    <ClCompile Include="Source\j_DE_IncHPCirclePartyYA_NPEAVCCharacter0MAEAEZ_1400061C2.cpp" />
    <ClCompile Include="Source\j_DE_IncreaseDPYA_NPEAVCCharacter0MAEAEZ_140004E08.cpp" />
    <ClCompile Include="Source\j_DE_LateContDamageRemoveYA_NPEAVCCharacter0MAEAEZ_14000FC72.cpp" />
    <ClCompile Include="Source\j_DE_LateContHelpForceRemoveYA_NPEAVCCharacter0MAE_140005C31.cpp" />
    <ClCompile Include="Source\j_DE_LateContHelpSkillRemoveYA_NPEAVCCharacter0MAE_140011A3B.cpp" />
    <ClCompile Include="Source\j_DE_LayTrapYA_NPEAVCCharacter0MAEAEZ_14000AE34.cpp" />
    <ClCompile Include="Source\j_DE_MakeGuardTowerYA_NPEAVCCharacter0MAEAEZ_1400080C6.cpp" />
    <ClCompile Include="Source\j_DE_MakePortalReturnBindPositionPartyMemberYA_NPE_14000CED2.cpp" />
    <ClCompile Include="Source\j_DE_MakeZeroAnimusRecallTimeOnceYA_NPEAVCCharacte_140010DDE.cpp" />
    <ClCompile Include="Source\j_DE_OthersContHelpSFRemoveYA_NPEAVCCharacter0MAEA_14000D25B.cpp" />
    <ClCompile Include="Source\j_DE_OverHealingYA_NPEAVCCharacter0MAEAEZ_140013421.cpp" />
    <ClCompile Include="Source\j_DE_Potion_AllContHelpSkillRemove_OnceYA_NPEAVCCh_14000A114.cpp" />
    <ClCompile Include="Source\j_DE_Potion_Buf_ExtendYA_NPEAVCCharacter0MAEAEZ_140008BA2.cpp" />
    <ClCompile Include="Source\j_DE_Potion_Chaos_Dec_TimeYA_NPEAVCCharacter0MAEAE_140010E51.cpp" />
    <ClCompile Include="Source\j_DE_Potion_Chaos_Inc_TimeYA_NPEAVCCharacter0MAEAE_14000B36B.cpp" />
    <ClCompile Include="Source\j_DE_Potion_CharReNameYA_NPEAVCCharacter0MAEAEZ_1400068ED.cpp" />
    <ClCompile Include="Source\j_DE_Potion_Class_RefineYA_NPEAVCCharacter0MAEAEZ_1400022CA.cpp" />
    <ClCompile Include="Source\j_DE_Potion_Cont_Damage_RemoveYA_NPEAVCCharacter0M_14000E219.cpp" />
    <ClCompile Include="Source\j_DE_Potion_DecHalfSFContDamYA_NPEAVCCharacter0MAE_140010A87.cpp" />
    <ClCompile Include="Source\j_DE_Potion_Exp_Increase_AbsoluteYA_NPEAVCCharacte_1400079C8.cpp" />
    <ClCompile Include="Source\j_DE_Potion_Exp_Increase_PercentageYA_NPEAVCCharac_14000199C.cpp" />
    <ClCompile Include="Source\j_DE_Potion_FP_In_ValueYA_NPEAVCCharacter0MAEAEZ_14000F6A5.cpp" />
    <ClCompile Include="Source\j_DE_Potion_Gold_PointYA_NPEAVCCharacter0MAEAEZ_1400028BA.cpp" />
    <ClCompile Include="Source\j_DE_Potion_HFP_Full_RecoverYA_NPEAVCCharacter0MAE_14000781F.cpp" />
    <ClCompile Include="Source\j_DE_Potion_HP_In_ValueYA_NPEAVCCharacter0MAEAEZ_14000FB0A.cpp" />
    <ClCompile Include="Source\j_DE_Potion_Race_Debuff_Clear_OneYA_NPEAVCCharacte_14000A67D.cpp" />
    <ClCompile Include="Source\j_DE_Potion_Race_Debuff_Clear_TwoYA_NPEAVCCharacte_14000486D.cpp" />
    <ClCompile Include="Source\j_DE_Potion_RemoveAfterEffectYA_NPEAVCCharacter0MA_14000914C.cpp" />
    <ClCompile Include="Source\j_DE_Potion_RemoveAllContinousEffectYA_NPEAVCChara_14000D666.cpp" />
    <ClCompile Include="Source\j_DE_Potion_Revival_Die_PositionYA_NPEAVCCharacter_1400111BC.cpp" />
    <ClCompile Include="Source\j_DE_Potion_SP_In_ValueYA_NPEAVCCharacter0MAEAEZ_140011199.cpp" />
    <ClCompile Include="Source\j_DE_Potion_Trunk_ExtendYA_NPEAVCCharacter0MAEAEZ_14001055A.cpp" />
    <ClCompile Include="Source\j_DE_Quick_Revival_Die_PositionYA_NPEAVCCharacter0_140011D10.cpp" />
    <ClCompile Include="Source\j_DE_RecallCommonPlayerYA_NPEAVCCharacter0MAEAEZ_14000D80A.cpp" />
    <ClCompile Include="Source\j_DE_RecallPartyMemberYA_NPEAVCCharacter0MAEAEZ_1400123FF.cpp" />
    <ClCompile Include="Source\j_DE_Recall_After_StoneYA_NPEAVCCharacter0MAEAEZ_14000B91F.cpp" />
    <ClCompile Include="Source\j_DE_RecoverAllReturnStateAnimusHPFullYA_NPEAVCCha_14000E89A.cpp" />
    <ClCompile Include="Source\j_DE_RecoveryYA_NPEAVCCharacter0MAEAEZ_1400087C9.cpp" />
    <ClCompile Include="Source\j_DE_ReleaseMonsterTargetYA_NPEAVCCharacter0MAEAEZ_14000DAA8.cpp" />
    <ClCompile Include="Source\j_DE_RemoveAllContHelpYA_NPEAVCCharacter0MAEAEZ_14000F56F.cpp" />
    <ClCompile Include="Source\j_DE_ReturnBindPositionYA_NPEAVCCharacter0MAEAEZ_14000453E.cpp" />
    <ClCompile Include="Source\j_DE_SelfDestructionYA_NPEAVCCharacter0MAEAEZ_14000C027.cpp" />
    <ClCompile Include="Source\j_DE_SkillContHelpTimeIncYA_NPEAVCCharacter0MAEAEZ_140009D8B.cpp" />
    <ClCompile Include="Source\j_DE_SPDecYA_NPEAVCCharacter0MAEAEZ_140004CBE.cpp" />
    <ClCompile Include="Source\j_DE_STIncYA_NPEAVCCharacter0MAEAEZ_14000F646.cpp" />
    <ClCompile Include="Source\j_DE_StunYA_NPEAVCCharacter0MAEAEZ_1400131E2.cpp" />
    <ClCompile Include="Source\j_DE_TeleportCommonPlayerYA_NPEAVCCharacter0MAEAEZ_140005A88.cpp" />
    <ClCompile Include="Source\j_DE_Teleport_After_StoneYA_NPEAVCCharacter0MAEAEZ_14000AB9B.cpp" />
    <ClCompile Include="Source\j_DE_TransDestHPYA_NPEAVCCharacter0MAEAEZ_14000F06A.cpp" />
    <ClCompile Include="Source\j_DE_TransMonsterHPYA_NPEAVCCharacter0MAEAEZ_14000ED6D.cpp" />
    <ClCompile Include="Source\j_DE_ViewWeakPointYA_NPEAVCCharacter0MAEAEZ_140003382.cpp" />
    <ClCompile Include="Source\j_DisconnectGuildWarCharacterRequestCNetworkEXAEAA_140004809.cpp" />
    <ClCompile Include="Source\j_DisjointPartyCPartyPlayerQEAA_NXZ_14000DB1B.cpp" />
    <ClCompile Include="Source\j_DoEventRFEventBaseUEAAHPEAVCPlayerZ_140006884.cpp" />
    <ClCompile Include="Source\j_DoEventRFEvent_ClassRefineUEAAHPEAVCPlayerZ_140012AB7.cpp" />
    <ClCompile Include="Source\j_DoitCandidateRegisterUEAAHW4CmdPEAVCPlayerPEADZ_14000587B.cpp" />
    <ClCompile Include="Source\j_DoitClassOrderProcessorUEAAHW4CmdPEAVCPlayerPEAD_14000E6A6.cpp" />
    <ClCompile Include="Source\j_DoitCTalkCrystalCombineManagerQEAA_NPEAVCPlayerE_140013476.cpp" />
    <ClCompile Include="Source\j_DoitElectProcessorUEAAHW4CmdPEAVCPlayerPEADZ_140001587.cpp" />
    <ClCompile Include="Source\j_DoitFinalDecisionApplyerUEAAHW4CmdPEAVCPlayerPEA_14000E8BD.cpp" />
    <ClCompile Include="Source\j_DoitFinalDecisionProcessorUEAAHW4CmdPEAVCPlayerP_14000D026.cpp" />
    <ClCompile Include="Source\j_DoitPatriarchElectProcessorQEAA_NW4CmdPEAVCPlaye_140005C9A.cpp" />
    <ClCompile Include="Source\j_DoitSecondCandidateCrystallizerEEAAHW4CmdPEAVCPl_14000EB29.cpp" />
    <ClCompile Include="Source\j_DoitVoterUEAAHW4CmdPEAVCPlayerPEADZ_14000D73D.cpp" />
    <ClCompile Include="Source\j_down_animus_expCMgrAvatorLvHistoryQEAAX_K0_JPEAD_1400109F1.cpp" />
    <ClCompile Include="Source\j_down_expCMgrAvatorLvHistoryQEAAXHNGNGPEAD0Z_14000518C.cpp" />
    <ClCompile Include="Source\j_DropBallCNormalGuildBattleFieldGUILD_BATTLEQEAAE_14000646F.cpp" />
    <ClCompile Include="Source\j_DropCGravityStoneQEAAEPEAVCPlayerZ_14000E88B.cpp" />
    <ClCompile Include="Source\j_DropGravityStoneCGuildBattleControllerQEAAXPEAVC_14000D42C.cpp" />
    <ClCompile Include="Source\j_DTradeEqualPersonYA_NPEAVCPlayerPEAPEAV1Z_140001807.cpp" />
    <ClCompile Include="Source\j_DTradeInitCPlayerQEAAXXZ_14000E5CF.cpp" />
    <ClCompile Include="Source\j_Emb_AddStorageCPlayerQEAAPEAU_db_con_STORAGE_LIS_140013160.cpp" />
    <ClCompile Include="Source\j_Emb_AlterDurPointCPlayerQEAAKEEH_N0Z_14000FE6B.cpp" />
    <ClCompile Include="Source\j_Emb_AlterStatCPlayerQEAAXEEKEPEBD_NZ_14000962E.cpp" />
    <ClCompile Include="Source\j_Emb_AlterStat_FCPlayerQEAAXEEMEZ_14000DD5A.cpp" />
    <ClCompile Include="Source\j_Emb_CheckActForQuestCPlayerQEAA_NHPEADG_NZ_14000C7F7.cpp" />
    <ClCompile Include="Source\j_Emb_CheckActForQuestPartyCPlayerQEAAXHPEADGZ_1400037F6.cpp" />
    <ClCompile Include="Source\j_Emb_CompleteQuestCPlayerQEAAXEEEZ_14000E408.cpp" />
    <ClCompile Include="Source\j_Emb_CreateNPCQuestCPlayerQEAA_NPEADKZ_14000B992.cpp" />
    <ClCompile Include="Source\j_Emb_CreateQuestEventCPlayerQEAA_NW4QUEST_HAPPENP_14000A3D5.cpp" />
    <ClCompile Include="Source\j_Emb_DelStorageCPlayerQEAA_NEE_N0PEBDZ_140011022.cpp" />
    <ClCompile Include="Source\j_Emb_EquipLinkCPlayerQEAAXXZ_1400125B2.cpp" />
    <ClCompile Include="Source\j_Emb_ItemUpgradeCPlayerQEAAXEEEKZ_140002653.cpp" />
    <ClCompile Include="Source\j_Emb_RidindUnitCPlayerQEAAX_NPEAVCParkingUnitZ_140003206.cpp" />
    <ClCompile Include="Source\j_Emb_StartQuestCPlayerQEAA_NEPEAU_happen_event_co_14000D71F.cpp" />
    <ClCompile Include="Source\j_Emb_UpdateStatCPlayerQEAAHKKKZ_14000455C.cpp" />
    <ClCompile Include="Source\j_EnterCReturnGateControllerQEAA_NIPEAVCPlayerZ_14000824C.cpp" />
    <ClCompile Include="Source\j_EnterCReturnGateQEAAHPEAVCPlayerZ_14000FDDF.cpp" />
    <ClCompile Include="Source\j_EnterMemberCTransportShipQEAAXPEAVCPlayerZ_14000790A.cpp" />
    <ClCompile Include="Source\j_EnterPlayerCDarkHoleQEAA_NPEAVCPlayerPEAVCMapDat_140006B40.cpp" />
    <ClCompile Include="Source\j_EnterWorldCPartyPlayerQEAAXPEAU_WA_AVATOR_CODEGZ_14000AE11.cpp" />
    <ClCompile Include="Source\j_ExitMemberCTransportShipQEAAXPEAVCPlayer_NZ_140006F0F.cpp" />
    <ClCompile Include="Source\j_ExitUpdateDataToWorldCPlayerQEAAXXZ_14000F72C.cpp" />
    <ClCompile Include="Source\j_ExitWorldCPartyPlayerQEAAXPEAPEAV1Z_14000B8D4.cpp" />
    <ClCompile Include="Source\j_Expire_IPOverflowCBillingManagerQEAAXPEADZ_14000229D.cpp" />
    <ClCompile Include="Source\j_Expire_IPOverflowCBillingQEAAXPEADZ_140007766.cpp" />
    <ClCompile Include="Source\j_Expire_PCBangCBillingManagerQEAAXPEADZ_140009F75.cpp" />
    <ClCompile Include="Source\j_Expire_PCBangCBillingQEAAXPEADZ_1400107D0.cpp" />
    <ClCompile Include="Source\j_Expire_PersonalCBillingManagerQEAAXPEADZ_140001316.cpp" />
    <ClCompile Include="Source\j_Expire_PersonalCBillingQEAAXPEADZ_140011446.cpp" />
    <ClCompile Include="Source\j_ExponentiateBaseDL_GroupParametersUECPPointCrypt_140006D1B.cpp" />
    <ClCompile Include="Source\j_ExponentiateElementDL_GroupParametersUECPPointCr_140011A72.cpp" />
    <ClCompile Include="Source\j_ExponentiatePublicElementDL_PublicKeyUECPPointCr_14000D38C.cpp" />
    <ClCompile Include="Source\j_ExpulsionSocketCNetworkEXEEAA_NKKEPEAXZ_140005FD8.cpp" />
    <ClCompile Include="Source\j_ExpulsionSocketCNetWorkingUEAA_NKKEPEAXZ_140009543.cpp" />
    <ClCompile Include="Source\j_exp_prof_logCMgrAvatorItemHistoryQEAAXHPEADZ_1400016B3.cpp" />
    <ClCompile Include="Source\j_ExtractStringToTimeCPlayerQEAAXKPEAU_SYSTEMTIMEZ_140005425.cpp" />
    <ClCompile Include="Source\j_FindEffectDstCCharacterQEAAHHHH_NPEAV1PEADPEAPEA_140011018.cpp" />
    <ClCompile Include="Source\j_FindFarChatPlayerWithTempCPlayerQEAAPEAV1PEADZ_14000F1AA.cpp" />
    <ClCompile Include="Source\j_FindOwnerCUnmannedTraderUserInfoQEAAPEAVCPlayerX_1400015AF.cpp" />
    <ClCompile Include="Source\j_FindPotionEffectDstCCharacterQEAAHHH_NPEAV1PEADP_14000DF99.cpp" />
    <ClCompile Include="Source\j_Find_DataTimeLimitMgrQEAAPEAUPlayer_TL_StatusGZ_140002257.cpp" />
    <ClCompile Include="Source\j_Find_DataTimeLimitMgrQEAAPEAUPlayer_TL_StatusKZ_14000228E.cpp" />
    <ClCompile Include="Source\j_FixTargetWhileCGameObjectUEAA_NPEAVCCharacterKZ_140012067.cpp" />
    <ClCompile Include="Source\j_FixTargetWhileCMonsterUEAA_NPEAVCCharacterKZ_1400017DA.cpp" />
    <ClCompile Include="Source\j_FixTargetWhileCPlayerUEAA_NPEAVCCharacterKZ_1400085FD.cpp" />
    <ClCompile Include="Source\j_ForcePullUnitCPlayerQEAAX_NZ_1400088AF.cpp" />
    <ClCompile Include="Source\j_FoundPartyCPartyPlayerQEAA_NPEAV1Z_140002680.cpp" />
    <ClCompile Include="Source\j_GetAccumulationCountCMonsterSkillQEBAHXZ_14000A079.cpp" />
    <ClCompile Include="Source\j_GetAddCountWithPlayerCHolyStoneQEAAGXZ_140003C51.cpp" />
    <ClCompile Include="Source\j_GetAddSpeedCPlayerQEAAMXZ_140006915.cpp" />
    <ClCompile Include="Source\j_GetAfterEffectCPlayerQEAAPEAU_sf_continousXZ_14000F754.cpp" />
    <ClCompile Include="Source\j_GetAnimusFldFromExpYAPEAU_animus_fldH_KZ_14000F84E.cpp" />
    <ClCompile Include="Source\j_GetAttackDamPointCCharacterQEAAHHHHPEAV1_NZ_140003BD4.cpp" />
    <ClCompile Include="Source\j_GetAttackDistCMonsterSkillQEAAMXZ_1400097DC.cpp" />
    <ClCompile Include="Source\j_GetAttackDPCPlayerUEAAHXZ_140001267.cpp" />
    <ClCompile Include="Source\j_GetAttackFCCAttackQEAAMPEAVCPlayerE_N1Z_140003B34.cpp" />
    <ClCompile Include="Source\j_GetAttackLevelCGameObjectUEAAHXZ_14000B39D.cpp" />
    <ClCompile Include="Source\j_GetAttackLevelCPlayerUEAAHXZ_140006091.cpp" />
    <ClCompile Include="Source\j_GetAttackRandomPartCCharacterQEAAHXZ_1400056E1.cpp" />
    <ClCompile Include="Source\j_GetAttackRangeCPlayerUEAAMXZ_14000562D.cpp" />
    <ClCompile Include="Source\j_GetAttackTargetCMonsterQEAAPEAVCCharacterXZ_1400120BC.cpp" />
    <ClCompile Include="Source\j_GetAveSkillMasteryPerClass_MASTERY_PARAMQEAAMEZ_1400038AA.cpp" />
    <ClCompile Include="Source\j_GetAvoidRateCPlayerUEAAHXZ_140002F7C.cpp" />
    <ClCompile Include="Source\j_GetBagNumCPlayerDBQEAAEXZ_14000825B.cpp" />
    <ClCompile Include="Source\j_GetBallCNormalGuildBattleFieldGUILD_BATTLEQEAAEG_14000EC41.cpp" />
    <ClCompile Include="Source\j_GetBallOwnerCNormalGuildBattleFieldGUILD_BATTLEQ_140002B3F.cpp" />
    <ClCompile Include="Source\j_GetBeforeTimeCMonsterSkillQEAAKXZ_14000AB00.cpp" />
    <ClCompile Include="Source\j_GetBillingTypeCPlayerQEAAHXZ_14000BAA0.cpp" />
    <ClCompile Include="Source\j_GetBindDummyCPlayerQEAAPEAU_dummy_positionXZ_140013AB1.cpp" />
    <ClCompile Include="Source\j_GetBindMapCPlayerQEAAPEAVCMapDataPEAM_NZ_14000C914.cpp" />
    <ClCompile Include="Source\j_GetBindMapDataCPlayerQEAAPEAVCMapDataXZ_14000C531.cpp" />
    <ClCompile Include="Source\j_GetCashAmountCPlayerQEAAHXZ_14000BDFC.cpp" />
    <ClCompile Include="Source\j_GetCGravityStoneQEAAEGKPEAVCPlayerZ_140008DEB.cpp" />
    <ClCompile Include="Source\j_GetCharNameACPlayerDBQEAAPEADXZ_1400111A3.cpp" />
    <ClCompile Include="Source\j_GetCharNameWCPlayerDBQEAAPEADXZ_14000DCF1.cpp" />
    <ClCompile Include="Source\j_GetCharSerialCPlayerDBQEAAKXZ_14000320B.cpp" />
    <ClCompile Include="Source\j_GetClassInGuildCPlayerDBQEAAEXZ_140001E29.cpp" />
    <ClCompile Include="Source\j_GetCloseItemForPassTimeUpdateInfoCUnmannedTrader_14000438B.cpp" />
    <ClCompile Include="Source\j_GetCloseItemForPassTimeUpdateInfoCUnmannedTrader_140011423.cpp" />
    <ClCompile Include="Source\j_GetCurItemSerialCPlayerDBQEAAGXZ_14000AB91.cpp" />
    <ClCompile Include="Source\j_GetCurPosCPlayerDBQEAAPEAMXZ_14000AD62.cpp" />
    <ClCompile Include="Source\j_GetDalantCPlayerDBQEAAKXZ_140007397.cpp" />
    <ClCompile Include="Source\j_GetDamageDPCPlayerQEAAHHZ_14000EE03.cpp" />
    <ClCompile Include="Source\j_GetDamageLevelCPlayerQEAAHHZ_1400102FD.cpp" />
    <ClCompile Include="Source\j_GetDefFacingCPlayerUEAAMHZ_140008A71.cpp" />
    <ClCompile Include="Source\j_GetDefFCAutominePersonalUEAAHHPEAVCCharacterPEAH_140001956.cpp" />
    <ClCompile Include="Source\j_GetDefFCCAnimusUEAAHHPEAVCCharacterPEAHZ_140011568.cpp" />
    <ClCompile Include="Source\j_GetDefFCCGameObjectUEAAHHPEAVCCharacterPEAHZ_14000777F.cpp" />
    <ClCompile Include="Source\j_GetDefFCCGuardTowerUEAAHHPEAVCCharacterPEAHZ_1400066F4.cpp" />
    <ClCompile Include="Source\j_GetDefFCCHolyKeeperUEAAHHPEAVCCharacterPEAHZ_14000AFF6.cpp" />
    <ClCompile Include="Source\j_GetDefFCCHolyStoneUEAAHHPEAVCCharacterPEAHZ_14000AB05.cpp" />
    <ClCompile Include="Source\j_GetDefFCCMonsterUEAAHHPEAVCCharacterPEAHZ_14000A420.cpp" />
    <ClCompile Include="Source\j_GetDefFCCPlayerUEAAHHPEAVCCharacterPEAHZ_140006334.cpp" />
    <ClCompile Include="Source\j_GetDefFCCTrapUEAAHHPEAVCCharacterPEAHZ_14000F21D.cpp" />
    <ClCompile Include="Source\j_GetDefGapCPlayerUEAAMHZ_140013A8E.cpp" />
    <ClCompile Include="Source\j_GetDefSkillCAnimusUEAAH_NZ_1400086D9.cpp" />
    <ClCompile Include="Source\j_GetDefSkillCGameObjectUEAAH_NZ_14000CA40.cpp" />
    <ClCompile Include="Source\j_GetDefSkillCGuardTowerUEAAH_NZ_140012535.cpp" />
    <ClCompile Include="Source\j_GetDefSkillCHolyKeeperUEAAH_NZ_140009976.cpp" />
    <ClCompile Include="Source\j_GetDefSkillCHolyStoneUEAAH_NZ_140002E64.cpp" />
    <ClCompile Include="Source\j_GetDefSkillCMonsterUEAAH_NZ_1400093FE.cpp" />
    <ClCompile Include="Source\j_GetDefSkillCPlayerUEAAH_NZ_14000B613.cpp" />
    <ClCompile Include="Source\j_GetDefSkillCTrapUEAAH_NZ_14000575E.cpp" />
    <ClCompile Include="Source\j_GetDmgSKILLQEAAHMZ_14000F12D.cpp" />
    <ClCompile Include="Source\j_GetDPCPlayerDBQEAAHXZ_140011E00.cpp" />
    <ClCompile Include="Source\j_GetDPCPlayerQEAAHXZ_140009A52.cpp" />
    <ClCompile Include="Source\j_GetDstCaseTypeCMonsterSkillQEAAHXZ_140008652.cpp" />
    <ClCompile Include="Source\j_GetEffectEquipCodeCPlayerQEAAEEEZ_140008DB4.cpp" />
    <ClCompile Include="Source\j_GetElementCMonsterSkillQEAAHXZ_140002E5F.cpp" />
    <ClCompile Include="Source\j_GetEnvironmentCLevelQEAAKXZ_1400095A2.cpp" />
    <ClCompile Include="Source\j_GetExceptMotiveCMonsterSkillQEBAHXZ_1400088CD.cpp" />
    <ClCompile Include="Source\j_GetExceptMotiveValueCMonsterSkillQEBAHXZ_1400127E7.cpp" />
    <ClCompile Include="Source\j_GetExpCPlayerDBQEAANXZ_14000E890.cpp" />
    <ClCompile Include="Source\j_GetExtTrunkSlotNumCPlayerDBQEAAEXZ_14000713F.cpp" />
    <ClCompile Include="Source\j_GetExtTrunkSlotRaceCPlayerDBQEAAEKZ_140006960.cpp" />
    <ClCompile Include="Source\j_GetFireTolCPlayerUEAAHXZ_1400056AA.cpp" />
    <ClCompile Include="Source\j_GetFldCMonsterSkillQEAAPEAU_base_fldXZ_14000C4DC.cpp" />
    <ClCompile Include="Source\j_GetFPCPlayerDBQEAAHXZ_140002D01.cpp" />
    <ClCompile Include="Source\j_GetFPCPlayerQEAAHXZ_14000CAF4.cpp" />
    <ClCompile Include="Source\j_GetGaugeCPlayerQEAAHHZ_14000A51A.cpp" />
    <ClCompile Include="Source\j_GetGenAttackProbCAnimusUEAAHPEAVCCharacterH_NZ_14000EC37.cpp" />
    <ClCompile Include="Source\j_GetGenAttackProbCGameObjectUEAAHPEAVCCharacterH__14000476E.cpp" />
    <ClCompile Include="Source\j_GetGenAttackProbCGuardTowerUEAAHPEAVCCharacterH__14000BA87.cpp" />
    <ClCompile Include="Source\j_GetGenAttackProbCHolyKeeperUEAAHPEAVCCharacterH__1400066F9.cpp" />
    <ClCompile Include="Source\j_GetGenAttackProbCMonsterUEAAHPEAVCCharacterH_NZ_1400100C8.cpp" />
    <ClCompile Include="Source\j_GetGenAttackProbCNuclearBombUEAAHPEAVCCharacterH_140010190.cpp" />
    <ClCompile Include="Source\j_GetGenAttackProbCPlayerUEAAHPEAVCCharacterH_NZ_140008373.cpp" />
    <ClCompile Include="Source\j_GetGenAttackProbCTrapUEAAHPEAVCCharacterH_NZ_1400090CA.cpp" />
    <ClCompile Include="Source\j_GetGMRequestDataPtrGMCallMgrQEAAPEAVGMRequestDat_1400025E5.cpp" />
    <ClCompile Include="Source\j_GetGoldCPlayerDBQEAAKXZ_14000B19F.cpp" />
    <ClCompile Include="Source\j_GetGroupIDCUnmannedTraderSubClassInfoLevelUEAA_N_14000BAE6.cpp" />
    <ClCompile Include="Source\j_GetGroupTargetCPlayerQEAAPEAU__target1EZ_14000BA32.cpp" />
    <ClCompile Include="Source\j_GetGuildSerialCPlayerDBQEAAKXZ_14000720C.cpp" />
    <ClCompile Include="Source\j_GetHaveUnitNumCPlayerDBQEAAHXZ_140008170.cpp" />
    <ClCompile Include="Source\j_GetHPCPlayerDBQEAAHXZ_14000E3B8.cpp" />
    <ClCompile Include="Source\j_GetHPCPlayerUEAAHXZ_14000BBD6.cpp" />
    <ClCompile Include="Source\j_GetInitClassCostCPlayerQEAAKXZ_140009E6C.cpp" />
    <ClCompile Include="Source\j_GetInvenItemCountFromCodeCPlayerDBQEAAHPEADZ_140011AE5.cpp" />
    <ClCompile Include="Source\j_GetInvisibleCCharacterQEAA_NXZ_140006BFE.cpp" />
    <ClCompile Include="Source\j_GetItemCPlayerDBQEAAPEAU_db_con_STORAGE_LISTEZ_14000CBEE.cpp" />
    <ClCompile Include="Source\j_GetItemEquipLevelYAHHHZ_14000C464.cpp" />
    <ClCompile Include="Source\j_GetItemEquipUpLevelYAHHHZ_14000126C.cpp" />
    <ClCompile Include="Source\j_GetKingPowerDamageCharacterCMonsterAggroMgrQEAAP_14000510A.cpp" />
    <ClCompile Include="Source\j_GetLevelCAnimusUEAAHXZ_14000DBAC.cpp" />
    <ClCompile Include="Source\j_GetLevelCGameObjectUEAAHXZ_140001AF5.cpp" />
    <ClCompile Include="Source\j_GetLevelCGuardTowerUEAAHXZ_140006569.cpp" />
    <ClCompile Include="Source\j_GetLevelCHolyKeeperUEAAHXZ_140007ED7.cpp" />
    <ClCompile Include="Source\j_GetLevelCHolyStoneUEAAHXZ_14000C351.cpp" />
    <ClCompile Include="Source\j_GetLevelCMonsterUEAAHXZ_140010609.cpp" />
    <ClCompile Include="Source\j_GetLevelContSFTimeMonsterSetInfoDataQEAAEEEZ_140012562.cpp" />
    <ClCompile Include="Source\j_GetLevelCPlayerDBQEAAHXZ_1400025EF.cpp" />
    <ClCompile Include="Source\j_GetLevelCPlayerUEAAHXZ_1400075CC.cpp" />
    <ClCompile Include="Source\j_GetLevelCTrapUEAAHXZ_140012017.cpp" />
    <ClCompile Include="Source\j_GetLevelLimitCMapDataQEAAHXZ_140012873.cpp" />
    <ClCompile Include="Source\j_GetLimitExpcStaticMember_PlayerQEBANHZ_14000D35F.cpp" />
    <ClCompile Include="Source\j_GetLootAuthorCPartyPlayerQEAAPEAVCPlayerXZ_140002018.cpp" />
    <ClCompile Include="Source\j_GetLooterCLootingMgrQEAAPEAVCPlayerPEAVCMapDataP_14000586C.cpp" />
    <ClCompile Include="Source\j_GetLossExpCPlayerDBQEAANXZ_140002225.cpp" />
    <ClCompile Include="Source\j_GetMapCodeCPlayerDBQEAAHXZ_140003CA6.cpp" />
    <ClCompile Include="Source\j_GetMapLayerCGuildRoomInfoQEAAGXZ_140003585.cpp" />
    <ClCompile Include="Source\j_GetMasteryCumAfterAttackCPlayerQEAAHHZ_140003D5F.cpp" />
    <ClCompile Include="Source\j_GetMaxDmgCMonsterSkillQEAAHXZ_140013E85.cpp" />
    <ClCompile Include="Source\j_GetMaxDPCPlayerQEAAHXZ_14000FB4B.cpp" />
    <ClCompile Include="Source\j_GetMaxExponentDL_GroupParameters_ECVECPCryptoPPC_14001294F.cpp" />
    <ClCompile Include="Source\j_GetMaxFPCPlayerQEAAHXZ_14000D3E6.cpp" />
    <ClCompile Include="Source\j_GetMaxHPCPlayerUEAAHXZ_14000F7FE.cpp" />
    <ClCompile Include="Source\j_GetMaxLevelCAnimusQEAAEXZ_140005803.cpp" />
    <ClCompile Include="Source\j_GetMaxLevelCPlayerDBQEAAHXZ_1400061AE.cpp" />
    <ClCompile Include="Source\j_GetMaxLvcStaticMember_PlayerQEBAHXZ_140009EA8.cpp" />
    <ClCompile Include="Source\j_GetMaxParamFromExpYAKH_KZ_140011176.cpp" />
    <ClCompile Include="Source\j_GetMaxProbCMonsterSkillQEAAHXZ_140003EE0.cpp" />
    <ClCompile Include="Source\j_GetMaxSPCPlayerQEAAHXZ_140008882.cpp" />
    <ClCompile Include="Source\j_GetMeleeSkillIndexCAttackSAHHZ_14000E255.cpp" />
    <ClCompile Include="Source\j_GetMemberPlayerCNormalGuildBattleGuildGUILD_BATT_14000C45F.cpp" />
    <ClCompile Include="Source\j_GetMinDmgCMonsterSkillQEAAHXZ_140004BBA.cpp" />
    <ClCompile Include="Source\j_GetMinProbCMonsterSkillQEAAHXZ_14000DC7E.cpp" />
    <ClCompile Include="Source\j_GetMoneyCPlayerQEAAKEZ_140001122.cpp" />
    <ClCompile Include="Source\j_GetMonSkillCMonsterSkillPoolQEAAPEAVCMonsterSkil_140001672.cpp" />
    <ClCompile Include="Source\j_GetMonSkillKindCMonsterSkillPoolQEAAPEAVCMonster_1400125CB.cpp" />
    <ClCompile Include="Source\j_GetMotiveCMonsterSkillQEAAHXZ_140003652.cpp" />
    <ClCompile Include="Source\j_GetMotiveValueCMonsterSkillQEAAHXZ_140002A95.cpp" />
    <ClCompile Include="Source\j_GetMoveSpeedCPlayerQEAAMXZ_1400107A8.cpp" />
    <ClCompile Include="Source\j_GetMoveTargetCAnimusQEAA_NPEAVCCharacterMEZ_14000F141.cpp" />
    <ClCompile Include="Source\j_GetNearEmptySlotCCharacterQEAAHHMQEAMPEAMZ_1400055C9.cpp" />
    <ClCompile Include="Source\j_GetNewItemSerialCPlayerDBQEAAGXZ_140008F99.cpp" />
    <ClCompile Include="Source\j_GetNextActionDelayTimeCMonsterSkillQEAAKXZ_140007B35.cpp" />
    <ClCompile Include="Source\j_GetNextGenAttTimeCCharacterQEAAKXZ_14000EE6C.cpp" />
    <ClCompile Include="Source\j_GetObjectExpandCMainThreadQEAAPEAVCGameObjectPEA_14000EB3D.cpp" />
    <ClCompile Include="Source\j_GetObjNameCPlayerUEAAPEADXZ_14000414C.cpp" />
    <ClCompile Include="Source\j_GetObjRaceCPlayerUEAAHXZ_140006D7F.cpp" />
    <ClCompile Include="Source\j_GetOutOreInAutoMineAutoMineMachineQEAAXPEAVCPlay_14000105F.cpp" />
    <ClCompile Include="Source\j_GetOwnerAutominePersonalQEAAPEAVCPlayerXZ_14000384B.cpp" />
    <ClCompile Include="Source\j_GetOwnerCGravityStoneQEAAPEAVCPlayerXZ_140008EC7.cpp" />
    <ClCompile Include="Source\j_GetOwnerCRecallRequestQEAAPEAVCPlayerXZ_14000A7EF.cpp" />
    <ClCompile Include="Source\j_GetOwnerCReturnGateCreateParamQEAAPEAVCPlayerXZ_140009124.cpp" />
    <ClCompile Include="Source\j_GetOwnerCReturnGateQEAAPEAVCPlayerXZ_14000FD6C.cpp" />
    <ClCompile Include="Source\j_GetPartyExpDistributionRateCPlayerQEAAMHHHZ_14000D85F.cpp" />
    <ClCompile Include="Source\j_GetPlayerCNormalGuildBattleGuildMemberGUILD_BATT_140007E28.cpp" />
    <ClCompile Include="Source\j_GetPlayerDataTimeLimitMgrQEAAGGPEAEPEANZ_140005150.cpp" />
    <ClCompile Include="Source\j_GetPlayerInfoCDarkHoleChannelQEAAPEAV_dh_player__1400047EB.cpp" />
    <ClCompile Include="Source\j_GetPlayerPenaltyTimeLimitMgrQEAANGZ_140007059.cpp" />
    <ClCompile Include="Source\j_GetPlayerStateRFEventBaseUEAAPEADKKZ_1400013A7.cpp" />
    <ClCompile Include="Source\j_GetPlayerStateRFEvent_ClassRefineUEAAPEADKKZ_1400058CB.cpp" />
    <ClCompile Include="Source\j_GetPlayerStatusTimeLimitMgrQEAAEGZ_140004390.cpp" />
    <ClCompile Include="Source\j_GetPopPartyMemberCPartyPlayerQEAAHXZ_140011379.cpp" />
    <ClCompile Include="Source\j_GetPrivateExponentDL_PrivateKeyImplVDL_GroupPara_14000844A.cpp" />
    <ClCompile Include="Source\j_GetPtrBaseClassCPlayerDBQEAAPEAU_class_fldXZ_140002AFE.cpp" />
    <ClCompile Include="Source\j_GetPtrCurClassCPlayerDBQEAAPEAU_class_fldXZ_140001307.cpp" />
    <ClCompile Include="Source\j_GetPtrFromSerialCPartyPlayerQEAAPEAV1KZ_140001A64.cpp" />
    <ClCompile Include="Source\j_GetPtrItemStorageCPlayerDBQEAAPEAU_db_con_STORAG_140009C00.cpp" />
    <ClCompile Include="Source\j_GetPtrPartyMemberCPartyPlayerQEAAPEAPEAV1XZ_14000579F.cpp" />
    <ClCompile Include="Source\j_GetPtrPlayerFromAccountSerialYAPEAVCPlayerPEAV1H_140013F84.cpp" />
    <ClCompile Include="Source\j_GetPtrPlayerFromAccountYAPEAVCPlayerPEAV1HPEADZ_140003F71.cpp" />
    <ClCompile Include="Source\j_GetPtrPlayerFromCharSerialYAPEAVCPlayerHKZ_1400115E0.cpp" />
    <ClCompile Include="Source\j_GetPtrPlayerFromNameYAPEAVCPlayerPEAV1HPEADZ_140012526.cpp" />
    <ClCompile Include="Source\j_GetPtrPlayerFromSerialYAPEAVCPlayerPEAV1HKZ_1400063E3.cpp" />
    <ClCompile Include="Source\j_GetPvPCashBagCPlayerDBQEAANXZ_14000312A.cpp" />
    <ClCompile Include="Source\j_GetPvpOrderViewCPlayerQEAAPEAVCPvpOrderViewXZ_140001CB2.cpp" />
    <ClCompile Include="Source\j_GetPvPPointCPlayerDBQEAANXZ_14000D8B9.cpp" />
    <ClCompile Include="Source\j_GetPvpPointLeakCPlayerQEAANXZ_14000286F.cpp" />
    <ClCompile Include="Source\j_GetPvpPointLimiterCPlayerQEAAAVCPvpPointLimiterX_14000A353.cpp" />
    <ClCompile Include="Source\j_GetPvpRankCPlayerDBQEAAKXZ_140005BAF.cpp" />
    <ClCompile Include="Source\j_GetRaceBuffLevelCRaceBuffByHolyQuestProcedureQEA_140006A5F.cpp" />
    <ClCompile Include="Source\j_GetRaceBuffLevelCRaceBuffManagerQEAAHPEAVCPlayer_140005A9C.cpp" />
    <ClCompile Include="Source\j_GetRaceCodeCPlayerDBQEAAHXZ_14000830A.cpp" />
    <ClCompile Include="Source\j_GetRaceSexCodeCPlayerDBQEAAHXZ_14000B1B8.cpp" />
    <ClCompile Include="Source\j_GetRecallAnimusCPlayerQEAAPEAVCAnimusXZ_14000C6A3.cpp" />
    <ClCompile Include="Source\j_GetRequireSFSlotCEquipItemSFAgentIEAA_NPEAU_requ_140005268.cpp" />
    <ClCompile Include="Source\j_GetResBufferNumCPlayerDBQEAAEXZ_140001857.cpp" />
    <ClCompile Include="Source\j_GetRewardItems_DarkDungeonCPlayerQEAAHPEAV_dh_re_140008E81.cpp" />
    <ClCompile Include="Source\j_GetRideLimLevelCTransportShipQEAAHXZ_140005632.cpp" />
    <ClCompile Include="Source\j_GetRideUpLimLevelCTransportShipQEAAHXZ_140009214.cpp" />
    <ClCompile Include="Source\j_GetSectorListPlayerCMapDataQEAAPEAVCObjectListGK_14000434F.cpp" />
    <ClCompile Include="Source\j_GetSFLevelYAHHKZ_14000DAFD.cpp" />
    <ClCompile Include="Source\j_GetSFLvCMonsterSkillQEAAHXZ_140003931.cpp" />
    <ClCompile Include="Source\j_GetSkillDelayTimeCMonsterQEAAMPEAVCMonsterSkillZ_14000AD53.cpp" />
    <ClCompile Include="Source\j_GetSkillLv_MASTERY_PARAMQEAAHEZ_1400015B4.cpp" />
    <ClCompile Include="Source\j_GetSlotCCharacterQEAAHPEAV1Z_1400042AA.cpp" />
    <ClCompile Include="Source\j_GetSoilTolCPlayerUEAAHXZ_1400025B3.cpp" />
    <ClCompile Include="Source\j_GetSPActionProbabilityCMonsterSkillQEBAHXZ_140001ED3.cpp" />
    <ClCompile Include="Source\j_GetSPCPlayerDBQEAAHXZ_140010AB4.cpp" />
    <ClCompile Include="Source\j_GetSPCPlayerQEAAHXZ_14000BBD1.cpp" />
    <ClCompile Include="Source\j_GetSPLimitCountCMonsterSkillQEBAHXZ_140005F51.cpp" />
    <ClCompile Include="Source\j_GetStateFlagCPlayerQEAA_KXZ_140004214.cpp" />
    <ClCompile Include="Source\j_GetStealthCCharacterQEAA_N_NZ_14000FDBC.cpp" />
    <ClCompile Include="Source\j_GetTargetObjCPlayerQEAAPEAVCGameObjectXZ_140004773.cpp" />
    <ClCompile Include="Source\j_GetTLStatusPlayer_TL_StatusQEAAEXZ_140001AC8.cpp" />
    <ClCompile Include="Source\j_GetTopAggroCharacterCMonsterAggroMgrQEAAPEAVCCha_140013EC6.cpp" />
    <ClCompile Include="Source\j_GetTopDamageCharacterCMonsterAggroMgrQEAAPEAVCCh_140005BC8.cpp" />
    <ClCompile Include="Source\j_GetTotalTolCCharacterQEAAHEHZ_140006C26.cpp" />
    <ClCompile Include="Source\j_GetTrunkPasswdWCPlayerDBQEAAPEADXZ_140007293.cpp" />
    <ClCompile Include="Source\j_GetTrunkSlotNumCPlayerDBQEAAEXZ_1400132E1.cpp" />
    <ClCompile Include="Source\j_GetTrunkSlotRaceCPlayerDBQEAAEKZ_140007428.cpp" />
    <ClCompile Include="Source\j_GetTypeCMonsterSkillQEAAHXZ_14000AC3B.cpp" />
    <ClCompile Include="Source\j_GetUseConsumeItemCPlayerQEAA_NPEAU_consume_item__14000206D.cpp" />
    <ClCompile Include="Source\j_GetUseSlotCPlayerDBQEAAEXZ_14000F975.cpp" />
    <ClCompile Include="Source\j_GetUseTypeCMonsterSkillQEAAHXZ_140010F7D.cpp" />
    <ClCompile Include="Source\j_GetVisualVerCPlayerQEAAHXZ_1400017FD.cpp" />
    <ClCompile Include="Source\j_GetWaterTolCPlayerUEAAHXZ_140004D6D.cpp" />
    <ClCompile Include="Source\j_GetWeaponAdjustCPlayerUEAAMXZ_140011004.cpp" />
    <ClCompile Include="Source\j_GetWeaponClassCPlayerUEAAHXZ_140001A37.cpp" />
    <ClCompile Include="Source\j_GetWeaponRangeCPlayerQEAAHXZ_140010B1D.cpp" />
    <ClCompile Include="Source\j_GetWidthCPlayerUEAAMXZ_140013651.cpp" />
    <ClCompile Include="Source\j_GetWindTolCPlayerUEAAHXZ_1400098E5.cpp" />
    <ClCompile Include="Source\j_GetWisdomTargetDfAIMgrSAPEAVCCharacterHPEAVCMons_140013ABB.cpp" />
    <ClCompile Include="Source\j_get_localitemAutominePersonalMgrAEBAPEAU_db_con__140006F50.cpp" />
    <ClCompile Include="Source\j_get_ownerAutominePersonalQEAAPEAVCPlayerXZ_140008378.cpp" />
    <ClCompile Include="Source\j_get_typeAutoMineMachineMngAEAAEPEAVCPlayerEZ_14001153B.cpp" />
    <ClCompile Include="Source\j_GiveCGuildBattleRewardItemGUILD_BATTLEQEAAPEBV12_14001218E.cpp" />
    <ClCompile Include="Source\j_GiveCGuildBattleRewardItemManagerGUILD_BATTLEQEA_1400019FB.cpp" />
    <ClCompile Include="Source\j_GiveEventItemCExchangeEventQEAAXPEAVCPlayerZ_14000D00D.cpp" />
    <ClCompile Include="Source\j_gm_MonsterInitCMainThreadQEAA_NPEAVCCharacterZ_140001F91.cpp" />
    <ClCompile Include="Source\j_GoCCharacterQEAAXXZ_1400055E7.cpp" />
    <ClCompile Include="Source\j_Guild_Buy_Emblem_CompleteCPlayerSAXPEAU_DB_QRY_S_140012F8A.cpp" />
    <ClCompile Include="Source\j_Guild_Disjoint_CompleteCPlayerSAXPEAU_DB_QRY_SYN_1400113E7.cpp" />
    <ClCompile Include="Source\j_Guild_Force_Leave_CompleteCPlayerSAXPEAU_DB_QRY__1400043E0.cpp" />
    <ClCompile Include="Source\j_Guild_Insert_CompleteCPlayerSAXPEAU_DB_QRY_SYN_D_140007FDB.cpp" />
    <ClCompile Include="Source\j_Guild_Join_Accept_CompleteCPlayerSAXPEAU_DB_QRY__1400021AD.cpp" />
    <ClCompile Include="Source\j_Guild_Pop_Money_CompleteCPlayerSAXPEAU_DB_QRY_SY_14000D7DD.cpp" />
    <ClCompile Include="Source\j_Guild_Push_Money_CompleteCPlayerSAXPEAU_DB_QRY_S_14000D012.cpp" />
    <ClCompile Include="Source\j_Guild_Self_Leave_CompleteCPlayerSAXPEAU_DB_QRY_S_14001288C.cpp" />
    <ClCompile Include="Source\j_Guild_Update_GuildMater_CompleteCPlayerSAXPEAU_D_14000B159.cpp" />
    <ClCompile Include="Source\j_HideNameEffectCPlayerQEAAX_NZ_140009FF2.cpp" />
    <ClCompile Include="Source\j_HSKQuestEnd_AttCPlayerQEAAXEPEAV1Z_140014015.cpp" />
    <ClCompile Include="Source\j_IncCriEffKillPointCPlayerQEAAXXZ_1400122D3.cpp" />
    <ClCompile Include="Source\j_IncCriEffPvPCashBagCPlayerQEAAXNZ_140013011.cpp" />
    <ClCompile Include="Source\j_IncPvPPointCPlayerQEAAXNW4PVP_ALTER_TYPEKZ_140012B57.cpp" />
    <ClCompile Include="Source\j_InheritBossCPartyPlayerQEAA_NPEAV1Z_14000FCE5.cpp" />
    <ClCompile Include="Source\j_InitAlterMasteryCPlayerDBQEAAXXZ_14000F781.cpp" />
    <ClCompile Include="Source\j_InitCCharacterQEAAXPEAU_object_idZ_14000C68F.cpp" />
    <ClCompile Include="Source\j_InitCEquipItemSFAgentQEAAXPEAVCPlayerZ_14000362A.cpp" />
    <ClCompile Include="Source\j_InitClassCPlayerDBQEAAXXZ_14000442B.cpp" />
    <ClCompile Include="Source\j_InitCMonsterSkillPoolQEAAXXZ_140005A0B.cpp" />
    <ClCompile Include="Source\j_InitCMonsterSkillQEAAXXZ_1400108C5.cpp" />
    <ClCompile Include="Source\j_InitCPartyPlayerQEAAXGZ_14000CF72.cpp" />
    <ClCompile Include="Source\j_InitCPlayerQEAA_NPEAU_object_idZ_140011C39.cpp" />
    <ClCompile Include="Source\j_InitCPotionParamQEAAXPEAVCPlayerZ_14000E6F6.cpp" />
    <ClCompile Include="Source\j_InitializecStaticMember_PlayerQEAA_NXZ_140010672.cpp" />
    <ClCompile Include="Source\j_InitMgrCQuestMgrQEAAXPEAVCPlayerPEAU_QUEST_DB_BA_14000117C.cpp" />
    <ClCompile Include="Source\j_InitMgrItemCombineMgrQEAAXPEAVCPlayerZ_140012C38.cpp" />
    <ClCompile Include="Source\j_InitPlayerDBCPlayerDBQEAAXPEAVCPlayerZ_1400127C9.cpp" />
    <ClCompile Include="Source\j_InitResBufferCPlayerDBQEAAXXZ_14000C9A5.cpp" />
    <ClCompile Include="Source\j_InitSKILLQEAAXHHHHHHHHZ_140007D97.cpp" />
    <ClCompile Include="Source\j_Init_dh_player_mgrQEAAXXZ_14001230A.cpp" />
    <ClCompile Include="Source\j_init_pos_dh_player_mgrQEAAXXZ_1400091D3.cpp" />
    <ClCompile Include="Source\j_init_skill_lv_up_dataQEAAXXZ_14000C0F4.cpp" />
    <ClCompile Include="Source\j_Init_target_player_damage_contsf_allinform_zoclQ_1400083E6.cpp" />
    <ClCompile Include="Source\j_init__targetCPlayerQEAAXXZ_14000B9DD.cpp" />
    <ClCompile Include="Source\j_InsertCharacterSelectLogCRFWorldDatabaseQEAA_NKP_140011E14.cpp" />
    <ClCompile Include="Source\j_InsertMovePotionStoneEffectCPotionMgrQEAAXPEAVCP_1400047D7.cpp" />
    <ClCompile Include="Source\j_InsertPartyMemberCPartyPlayerQEAA_NPEAV1Z_14000FE70.cpp" />
    <ClCompile Include="Source\j_InsertPlayerStatusTimeLimitMgrQEAAXGKEKK_NZ_140002EAA.cpp" />
    <ClCompile Include="Source\j_InsertPotionContEffectCPotionMgrIEAAHPEAVCPlayer_140001C3A.cpp" />
    <ClCompile Include="Source\j_InsertSFContEffectCCharacterUEAAEEEKGEPEA_NPEAV1_140008D78.cpp" />
    <ClCompile Include="Source\j_InsertSFContEffectCMonsterUEAAEEEKGEPEA_NPEAVCCh_140011C43.cpp" />
    <ClCompile Include="Source\j_InsertSkillCMonsterSkillPoolIEAAHAEAVCMonsterSki_14000F088.cpp" />
    <ClCompile Include="Source\j_InsertSlotCCharacterQEAAHPEAV1HZ_140006AB9.cpp" />
    <ClCompile Include="Source\j_Insert_CharacterDataCRFWorldDatabaseQEAA_NPEAD0K_140003E36.cpp" />
    <ClCompile Include="Source\j_insert_expendCRFDBItemLogQEAA_NPEAU_LTD_EXPENDZ_140013BC9.cpp" />
    <ClCompile Include="Source\j_Insert_Level_LogCRFWorldDatabaseQEAA_NKEKZ_14000E4C6.cpp" />
    <ClCompile Include="Source\j_Insert_PlayerTimeLimitInfoCRFWorldDatabaseQEAA_N_14001032F.cpp" />
    <ClCompile Include="Source\j_InstancecStaticMember_PlayerSAPEAV1XZ_140009697.cpp" />
    <ClCompile Include="Source\j_IntoMapCPlayerQEAA_NEZ_1400139FD.cpp" />
    <ClCompile Include="Source\j_in_playerCGuildMasterEffectQEAA_NPEAVCPlayerEZ_140004133.cpp" />
    <ClCompile Include="Source\j_IsActableClassSkillCPlayerDBQEAA_NPEADPEAHZ_140003378.cpp" />
    <ClCompile Include="Source\j_IsActingSiegeModeCPlayerQEAA_NXZ_140005F15.cpp" />
    <ClCompile Include="Source\j_IsApplyPcbangPrimiumCNationSettingDataNULLUEAA_N_14000EFFC.cpp" />
    <ClCompile Include="Source\j_IsApplyPcbangPrimiumCNationSettingDataUEAA_NQEBV_140002428.cpp" />
    <ClCompile Include="Source\j_IsApplyPcbangPrimiumCNationSettingManagerQEAA_NQ_14001398F.cpp" />
    <ClCompile Include="Source\j_IsApplyPcbangPrimiumCPlayerQEBA_NXZ_140008BDE.cpp" />
    <ClCompile Include="Source\j_IsAttackAbleCMonsterSkillQEAA_NXZ_14000179E.cpp" />
    <ClCompile Include="Source\j_IsBeAttackedAbleCPlayerUEAA_N_NZ_140011EEB.cpp" />
    <ClCompile Include="Source\j_IsBeCirclePlayerCGameObjectQEAA_NHZ_14000A727.cpp" />
    <ClCompile Include="Source\j_IsBeDamagedAbleAutominePersonalUEAA_NPEAVCCharac_140010942.cpp" />
    <ClCompile Include="Source\j_IsBeDamagedAbleCGameObjectUEAA_NPEAVCCharacterZ_14000DD0F.cpp" />
    <ClCompile Include="Source\j_IsBeDamagedAbleCGuardTowerUEAA_NPEAVCCharacterZ_140006681.cpp" />
    <ClCompile Include="Source\j_IsBeDamagedAbleCHolyKeeperUEAA_NPEAVCCharacterZ_140003166.cpp" />
    <ClCompile Include="Source\j_IsBeDamagedAbleCHolyStoneUEAA_NPEAVCCharacterZ_14000A6B4.cpp" />
    <ClCompile Include="Source\j_IsBeDamagedAbleCMonsterUEAA_NPEAVCCharacterZ_14000B9BF.cpp" />
    <ClCompile Include="Source\j_IsBeDamagedAbleCPlayerUEAA_NPEAVCCharacterZ_14000811B.cpp" />
    <ClCompile Include="Source\j_IsBeNearStoreYAPEAVCItemStorePEAVCPlayerHZ_14000BB5E.cpp" />
    <ClCompile Include="Source\j_IsBulletValidityCPlayerQEAAPEAU_db_con_STORAGE_L_140005F88.cpp" />
    <ClCompile Include="Source\j_IsBuyRaceBossGoldBoxCGoldenBoxItemMgrQEAAEPEAVCP_140010564.cpp" />
    <ClCompile Include="Source\j_IsChaosModeCPlayerQEBA_NXZ_140005F56.cpp" />
    <ClCompile Include="Source\j_IsClassChangeableLvCPlayerDBQEAA_NXZ_140012C92.cpp" />
    <ClCompile Include="Source\j_IsDamageEffectCCharacterQEAA_NIGZ_140007E73.cpp" />
    <ClCompile Include="Source\j_IsEffBulletValidityCPlayerQEAAPEAU_db_con_STORAG_140013273.cpp" />
    <ClCompile Include="Source\j_IsEffectableDstCCharacterQEAA_NPEADPEAV1Z_140004DEA.cpp" />
    <ClCompile Include="Source\j_IsEffectableEquipCPlayerQEAA_NPEAU_storage_con_S_140012792.cpp" />
    <ClCompile Include="Source\j_IsEnableSkillCEquipItemSFAgentQEAAEHPEAU_skill_f_140003864.cpp" />
    <ClCompile Include="Source\j_IsEnableSkillCEquipItemSFAgentQEAAEPEAU_skill_fl_140007EF0.cpp" />
    <ClCompile Include="Source\j_IsEquipAbleGradeCPlayerQEAA_NEZ_140012247.cpp" />
    <ClCompile Include="Source\j_IsExistOwnerCReturnGateControllerIEAA_NPEAVCPlay_140008E09.cpp" />
    <ClCompile Include="Source\j_IsExistRequestMoveCharacterListCUserDBQEAAEKZ_1400050BA.cpp" />
    <ClCompile Include="Source\j_IsExitCMonsterSkillQEAA_NXZ_1400063DE.cpp" />
    <ClCompile Include="Source\j_IsFill_dh_player_mgrQEAA_NXZ_14000B3DE.cpp" />
    <ClCompile Include="Source\j_IsGoalCNormalGuildBattleFieldGUILD_BATTLEQEAAEPE_140004FBB.cpp" />
    <ClCompile Include="Source\j_IsHaveMentalTicketCPlayerQEAA_NXZ_14000C095.cpp" />
    <ClCompile Include="Source\j_IsInTownCPlayerUEAA_NXZ_14000BCCB.cpp" />
    <ClCompile Include="Source\j_IsItemLootAuthorityCHolyStoneSystemQEAA_NPEAVCPl_14000CF9F.cpp" />
    <ClCompile Include="Source\j_IsJoinPartyLevelCPartyPlayerQEAA_NHMZ_140008DC3.cpp" />
    <ClCompile Include="Source\j_IsLastAttBuffCPlayerQEAA_NXZ_140010906.cpp" />
    <ClCompile Include="Source\j_IsLoadedBspCLevelQEBAHXZ_140006500.cpp" />
    <ClCompile Include="Source\j_IsMapLoadingCPlayerQEAA_NXZ_14000C59A.cpp" />
    <ClCompile Include="Source\j_IsMasterAutoMineMachineQEBA_NPEAVCPlayerZ_140004B38.cpp" />
    <ClCompile Include="Source\j_IsMineModeCPlayerQEAA_NXZ_1400017F3.cpp" />
    <ClCompile Include="Source\j_IsMiningByMinigTicketCPlayerQEAA_NXZ_14000BE97.cpp" />
    <ClCompile Include="Source\j_IsNewEnterAblePlayerCDarkHoleQEAA_NPEAVCPlayerZ_14000E6AB.cpp" />
    <ClCompile Include="Source\j_IsOldMemberCTransportShipQEAA_NPEAVCPlayerZ_140003CD3.cpp" />
    <ClCompile Include="Source\j_IsOpenPartyMemberCDarkHoleChannelQEAA_NPEAVCPlay_140012413.cpp" />
    <ClCompile Include="Source\j_IsOpenPartyMemberCDarkHoleQEAA_NPEAVCPlayerZ_14000F173.cpp" />
    <ClCompile Include="Source\j_IsOutExtraStopPosCPlayerQEAA_NPEAMZ_14000DF6C.cpp" />
    <ClCompile Include="Source\j_IsOverOneDayCPlayerQEAA_NXZ_14000C08B.cpp" />
    <ClCompile Include="Source\j_IsPartyBossCPartyPlayerQEAA_NXZ_140002842.cpp" />
    <ClCompile Include="Source\j_IsPartyLockCPartyPlayerQEAA_NXZ_140007AD6.cpp" />
    <ClCompile Include="Source\j_IsPartyMemberCPartyPlayerQEAA_NPEAVCPlayerZ_14000CA4F.cpp" />
    <ClCompile Include="Source\j_IsPartyModeCPartyPlayerQEAA_NXZ_14000DD00.cpp" />
    <ClCompile Include="Source\j_IsPassExpLimitLvDiffCPlayerQEAA_NHAEA_NZ_14000B0C3.cpp" />
    <ClCompile Include="Source\j_IsPassMasteryLimitLvDiffCPlayerQEAA_NHZ_1400137DC.cpp" />
    <ClCompile Include="Source\j_IsPatriarchCNuclearBombMgrQEAA_NPEAVCPlayerZ_14000B375.cpp" />
    <ClCompile Include="Source\j_IsPotionEffectableDstCCharacterQEAA_NPEADPEAV1Z_140003DAA.cpp" />
    <ClCompile Include="Source\j_IsPunishedCPlayerQEAA_NE_NZ_14000DC06.cpp" />
    <ClCompile Include="Source\j_IsPvpMapCPvpCashPointQEAA_NPEAVCPlayerZ_140010CA8.cpp" />
    <ClCompile Include="Source\j_IsRecallAnimusCPlayerQEAA_NXZ_140002A63.cpp" />
    <ClCompile Include="Source\j_IsRecvableContEffectCPlayerUEAA_NXZ_140006EEC.cpp" />
    <ClCompile Include="Source\j_IsReturnPostUpdateCPlayerQEAA_NXZ_14000687A.cpp" />
    <ClCompile Include="Source\j_IsRewardExpCGameObjectUEAA_NXZ_140006618.cpp" />
    <ClCompile Include="Source\j_IsRewardExpCMonsterUEAA_NXZ_140005B5F.cpp" />
    <ClCompile Include="Source\j_IsRideRightCParkingUnitQEAA_NPEAVCPlayerZ_14000335F.cpp" />
    <ClCompile Include="Source\j_IsRidingShipCPlayerQEAA_NXZ_14000297D.cpp" />
    <ClCompile Include="Source\j_IsRidingUnitCPlayerQEAA_NXZ_140009E3F.cpp" />
    <ClCompile Include="Source\j_IsSame_target_player_damage_contsf_allinform_zoc_14000E197.cpp" />
    <ClCompile Include="Source\j_IsSFActableByClassCPlayerQEAA_NEPEAU_base_fldZ_140001997.cpp" />
    <ClCompile Include="Source\j_IsSFUsableGaugeCPlayerQEAA_NEGPEAGZ_14000A33F.cpp" />
    <ClCompile Include="Source\j_IsSFUsableSFMasteryCPlayerQEAA_NEHZ_14000BFD2.cpp" />
    <ClCompile Include="Source\j_IsSFUseableRaceCPlayerQEAA_NEGZ_14000D82D.cpp" />
    <ClCompile Include="Source\j_IsSiegeModeCPlayerQEAA_NXZ_14000121C.cpp" />
    <ClCompile Include="Source\j_IsTakeRightCItemBoxQEAA_NPEAVCPlayerZ_140008E8B.cpp" />
    <ClCompile Include="Source\j_IsTargetObjCPlayerQEAA_NPEAVCGameObjectZ_140006E74.cpp" />
    <ClCompile Include="Source\j_IsUsableAccountTypeCPlayerQEAA_NHZ_140003323.cpp" />
    <ClCompile Include="Source\j_IsUsableCouponCashItemRemoteStoreQEAA_NPEAU_requ_140005D80.cpp" />
    <ClCompile Include="Source\j_IsUseCloakBoosterCPlayerQEAA_NXZ_140012D23.cpp" />
    <ClCompile Include="Source\j_IsUseReleaseRaceBuffPotionCPlayerQEAA_NXZ_140010CF8.cpp" />
    <ClCompile Include="Source\j_IsValidOwnerCGravityStoneQEAA_NPEAVCPlayerZ_140010271.cpp" />
    <ClCompile Include="Source\j_IsValidPlayerCMonsterQEAAHXZ_1400060DC.cpp" />
    <ClCompile Include="Source\j_IsViewAreaCMonsterQEAA_NPEAVCCharacterZ_140006E56.cpp" />
    <ClCompile Include="Source\j_Is_Battle_ModeCPlayerUEAA_NXZ_140011842.cpp" />
    <ClCompile Include="Source\j_JoinCGuildBattleControllerQEAAXPEAVCPlayerZ_14000E1CE.cpp" />
    <ClCompile Include="Source\j_KillCGuildBattleControllerQEAAXPEAVCPlayer0Z_14000C7C5.cpp" />
    <ClCompile Include="Source\j_LeaveGuildCGuildBattleControllerQEAAXPEAVCPlayer_14000ADF8.cpp" />
    <ClCompile Include="Source\j_LeaveGuildCNormalGuildBattleGUILD_BATTLEQEAAEPEA_14000CE8C.cpp" />
    <ClCompile Include="Source\j_LeaveGuildCNormalGuildBattleManagerGUILD_BATTLEQ_14000ED1D.cpp" />
    <ClCompile Include="Source\j_LimLvNpcQuestDeleteCPlayerQEAAXEZ_14000FB1E.cpp" />
    <ClCompile Include="Source\j_LoadCMoveMapLimitInfoListQEAAXPEAVCPlayerPEAVCMo_1400093E5.cpp" />
    <ClCompile Include="Source\j_LoadCMoveMapLimitInfoPortalUEAAXPEAVCPlayerPEAVC_14000B780.cpp" />
    <ClCompile Include="Source\j_LoadCMoveMapLimitInfoUEAAXPEAVCPlayerPEAVCMoveMa_140009359.cpp" />
    <ClCompile Include="Source\j_LoadCMoveMapLimitManagerQEAAXPEAVCPlayerZ_140010D34.cpp" />
    <ClCompile Include="Source\j_LoadCMoveMapLimitRightInfoListQEAAXPEAVCPlayerZ_14000F786.cpp" />
    <ClCompile Include="Source\j_LoadCMoveMapLimitRightInfoQEAAXPEAVCPlayerZ_140013822.cpp" />
    <ClCompile Include="Source\j_LoadCMoveMapLimitRightPortalUEAAXPEAVCPlayerZ_140013CF5.cpp" />
    <ClCompile Include="Source\j_LoadCMoveMapLimitRightUEAAXPEAVCPlayerZ_1400064E2.cpp" />
    <ClCompile Include="Source\j_LoadCPlayerQEAA_NPEAVCUserDB_NZ_1400057BD.cpp" />
    <ClCompile Include="Source\j_LoadINISubProcLoadCodeCRaceBuffInfoByHolyQuestCA_14001155E.cpp" />
    <ClCompile Include="Source\j_loadLimitExpDatacStaticMember_PlayerAEAA_NXZ_140007301.cpp" />
    <ClCompile Include="Source\j_LoadXMLCUnmannedTraderSubClassInfoLevelUEAA_NPEA_1400128AA.cpp" />
    <ClCompile Include="Source\j_LogInCGuildBattleControllerQEAAXPEAVCPlayerZ_14000F7D6.cpp" />
    <ClCompile Include="Source\j_LogInCMoveMapLimitInfoListQEAAXPEAVCPlayerPEAVCM_140010BB8.cpp" />
    <ClCompile Include="Source\j_LogInCMoveMapLimitInfoUEAAXPEAVCPlayerPEAVCMoveM_140003C7E.cpp" />
    <ClCompile Include="Source\j_LogInCMoveMapLimitManagerQEAAXPEAVCPlayerZ_140003E77.cpp" />
    <ClCompile Include="Source\j_LogInCMoveMapLimitRightInfoListQEAAXPEAVCPlayerZ_140012CBA.cpp" />
    <ClCompile Include="Source\j_LogInCMoveMapLimitRightInfoQEAAXPEAVCPlayerZ_14000CCCA.cpp" />
    <ClCompile Include="Source\j_LogInCMoveMapLimitRightUEAAXPEAVCPlayerZ_140005C18.cpp" />
    <ClCompile Include="Source\j_LoginMemberCGuildQEAAPEAU_guild_member_infoKPEAV_140010BC7.cpp" />
    <ClCompile Include="Source\j_LogOutCMoveMapLimitInfoListQEAAXPEAVCPlayerPEAVC_1400036C5.cpp" />
    <ClCompile Include="Source\j_LogOutCMoveMapLimitInfoUEAAXPEAVCPlayerPEAVCMove_14000F0C4.cpp" />
    <ClCompile Include="Source\j_LogOutCMoveMapLimitManagerQEAAXPEAVCPlayerZ_14000B2CB.cpp" />
    <ClCompile Include="Source\j_LogOutCMoveMapLimitRightInfoListQEAAXPEAVCPlayer_140010717.cpp" />
    <ClCompile Include="Source\j_LogOutCMoveMapLimitRightInfoQEAAXPEAVCPlayerZ_140010A55.cpp" />
    <ClCompile Include="Source\j_LogOutCMoveMapLimitRightPortalUEAAXPEAVCPlayerZ_14000B203.cpp" />
    <ClCompile Include="Source\j_LogOutCMoveMapLimitRightUEAAXPEAVCPlayerZ_14000ADC1.cpp" />
    <ClCompile Include="Source\j_LoopCPlayerUEAAXXZ_14000EBAB.cpp" />
    <ClCompile Include="Source\j_make_force_attack_paramCMonsterQEAAXPEAVCCharact_140007C84.cpp" />
    <ClCompile Include="Source\j_make_force_attack_paramCPlayerQEAAXPEAVCCharacte_140006A3C.cpp" />
    <ClCompile Include="Source\j_make_gen_attack_paramCAnimusQEAAXPEAVCCharacterE_140005BBE.cpp" />
    <ClCompile Include="Source\j_make_gen_attack_paramCMonsterQEAAXPEAVCCharacter_14000DF49.cpp" />
    <ClCompile Include="Source\j_make_gen_attack_paramCPlayerQEAAXPEAVCCharacterE_140006294.cpp" />
    <ClCompile Include="Source\j_make_siege_attack_paramCPlayerQEAAXPEAVCCharacte_14000307B.cpp" />
    <ClCompile Include="Source\j_make_skill_attack_paramCMonsterQEAA_NPEAVCCharac_1400075A9.cpp" />
    <ClCompile Include="Source\j_make_skill_attack_paramCPlayerQEAAXPEAVCCharacte_14000F38F.cpp" />
    <ClCompile Include="Source\j_make_unit_attack_paramCPlayerQEAAXPEAVCCharacter_14000DCD3.cpp" />
    <ClCompile Include="Source\j_make_wpactive_force_attack_paramCPlayerQEAAXPEAV_1400073F6.cpp" />
    <ClCompile Include="Source\j_make_wpactive_skill_attack_paramCPlayerQEAAXPEAV_140001F23.cpp" />
    <ClCompile Include="Source\j_ManageExpulseMemberCGuildQEAAEKZ_14000106E.cpp" />
    <ClCompile Include="Source\j_MasterAttack_MasterInformCAnimusQEAAXPEAVCCharac_140011969.cpp" />
    <ClCompile Include="Source\j_MasterBeAttacked_MasterInformCAnimusQEAAXPEAVCCh_140006C9E.cpp" />
    <ClCompile Include="Source\j_MasterReStartCTrapQEAAXPEAVCPlayerZ_14000D12A.cpp" />
    <ClCompile Include="Source\j_mgr_all_item_muziCPlayerQEAA_NHZ_14000713A.cpp" />
    <ClCompile Include="Source\j_mgr_change_degreeCPlayerQEAA_NHZ_140002E4B.cpp" />
    <ClCompile Include="Source\j_mgr_defense_item_graceCPlayerQEAA_NEHZ_140011DC4.cpp" />
    <ClCompile Include="Source\j_mgr_destroy_system_towerCPlayerQEAA_NXZ_140008C51.cpp" />
    <ClCompile Include="Source\j_mgr_dungeon_passCPlayerQEAA_NXZ_14000ACD1.cpp" />
    <ClCompile Include="Source\j_mgr_exit_keeperCPlayerQEAA_NXZ_140006EA1.cpp" />
    <ClCompile Include="Source\j_mgr_exit_stoneCPlayerQEAA_NXZ_1400045CF.cpp" />
    <ClCompile Include="Source\j_mgr_free_ride_shipCPlayerQEAA_NXZ_14000B3ED.cpp" />
    <ClCompile Include="Source\j_mgr_gotoCoordinatesCPlayerQEAA_NPEADMMMZ_14000F2EF.cpp" />
    <ClCompile Include="Source\j_mgr_gotoDstCoordinatesCPlayerQEAA_NPEAD0MMMZ_140010311.cpp" />
    <ClCompile Include="Source\j_mgr_goto_mineCPlayerQEAA_NXZ_14000F7AE.cpp" />
    <ClCompile Include="Source\j_mgr_goto_shipportCPlayerQEAA_NHHZ_14000FCEF.cpp" />
    <ClCompile Include="Source\j_mgr_goto_stoneCPlayerQEAA_NEZ_1400012D0.cpp" />
    <ClCompile Include="Source\j_mgr_goto_storeCPlayerQEAA_NHPEADZ_140005218.cpp" />
    <ClCompile Include="Source\j_mgr_holykeeper_startCPlayerQEAA_NHZ_14000C52C.cpp" />
    <ClCompile Include="Source\j_mgr_holystone_startCPlayerQEAA_NHZ_1400073E7.cpp" />
    <ClCompile Include="Source\j_mgr_item_telekinesisCPlayerQEAA_NXZ_140006221.cpp" />
    <ClCompile Include="Source\j_mgr_kickCPlayerQEAA_NPEADZ_140003F99.cpp" />
    <ClCompile Include="Source\j_mgr_make_system_towerCPlayerQEAA_NPEADZ_140011B8F.cpp" />
    <ClCompile Include="Source\j_mgr_matchlessCPlayerQEAA_N_NZ_14000CD42.cpp" />
    <ClCompile Include="Source\j_mgr_MaxAttackPointCPlayerQEAA_NHZ_140001596.cpp" />
    <ClCompile Include="Source\j_mgr_pass_sch_one_stepCPlayerQEAA_NXZ_140012387.cpp" />
    <ClCompile Include="Source\j_mgr_recall_guild_playerCPlayerQEAA_NPEADZ_14000165E.cpp" />
    <ClCompile Include="Source\j_mgr_recall_monCPlayerQEAA_NPEADHZ_140004034.cpp" />
    <ClCompile Include="Source\j_mgr_recall_party_playerCPlayerQEAA_NPEADZ_14000B44C.cpp" />
    <ClCompile Include="Source\j_mgr_recall_playerCPlayerQEAA_NPEADZ_1400118D8.cpp" />
    <ClCompile Include="Source\j_mgr_resurrect_playerCPlayerQEAA_NPEADZ_1400124D6.cpp" />
    <ClCompile Include="Source\j_mgr_set_animus_attack_pointCPlayerQEAA_NHZ_140009F48.cpp" />
    <ClCompile Include="Source\j_mgr_tracingCPlayerQEAA_N_NZ_14000CC2F.cpp" />
    <ClCompile Include="Source\j_mgr_TrunkInitCPlayerQEAA_NXZ_140007A59.cpp" />
    <ClCompile Include="Source\j_mgr_user_banCPlayerQEAA_NPEADH0EZ_1400115FE.cpp" />
    <ClCompile Include="Source\j_mgr_whisperCPlayerQEAA_NPEADZ_14000254F.cpp" />
    <ClCompile Include="Source\j_MonsterCritical_Exception_RateCAttackIEAAHPEAVCC_140010497.cpp" />
    <ClCompile Include="Source\j_MoveBreakCCharacterQEAAXMZ_14000F123.cpp" />
    <ClCompile Include="Source\j_MoveCCharacterQEAAXMZ_14000ABFA.cpp" />
    <ClCompile Include="Source\j_MoveStartPosCNormalGuildBattleFieldGUILD_BATTLEQ_140009EB7.cpp" />
    <ClCompile Include="Source\j_NetCloseCGuildBattleControllerQEAAXPEAVCPlayerZ_14000D6AC.cpp" />
    <ClCompile Include="Source\j_NetCloseCNationSettingDataCNUEAAXPEAVCPlayerZ_14000BE0B.cpp" />
    <ClCompile Include="Source\j_NetCloseCNationSettingDataNULLUEAAXPEAVCPlayerZ_1400087BF.cpp" />
    <ClCompile Include="Source\j_NetCloseCNationSettingDataUEAAXPEAVCPlayerZ_14000A448.cpp" />
    <ClCompile Include="Source\j_NetCloseCNationSettingManagerQEAAXPEAVCPlayerZ_14000629E.cpp" />
    <ClCompile Include="Source\j_NetCloseCNormalGuildBattleGUILD_BATTLEQEAAEKPEAV_14000B181.cpp" />
    <ClCompile Include="Source\j_NetCloseCNormalGuildBattleManagerGUILD_BATTLEQEA_14000F5CE.cpp" />
    <ClCompile Include="Source\j_NetCloseCPlayerQEAAX_NZ_1400041A6.cpp" />
    <ClCompile Include="Source\j_NewViewCircleObjectCPlayerQEAAXXZ_14000672B.cpp" />
    <ClCompile Include="Source\j_NextMissionOtherQuesterCDarkHoleChannelQEAAXPEAV_14000A9CF.cpp" />
    <ClCompile Include="Source\j_NextPassCMonsterSkillQEAAXXZ_140005D62.cpp" />
    <ClCompile Include="Source\j_NotifyCExpInfoCPartyModeKillMonsterExpNotifyQEAA_1400063B1.cpp" />
    <ClCompile Include="Source\j_NotifyCPartyModeKillMonsterExpNotifyQEAAXXZ_14000CA4A.cpp" />
    <ClCompile Include="Source\j_NotifyOwnerAttackInformCGuardTowerQEAAXPEAVCChar_14000F3AD.cpp" />
    <ClCompile Include="Source\j_NotifyOwnerAttackInform_TOWER_PARAMQEAAXPEAVCCha_14000F66E.cpp" />
    <ClCompile Include="Source\j_NotifySetBuffCRaceBuffInfoByHolyQuestAEAAXPEAVCP_140012A71.cpp" />
    <ClCompile Include="Source\j_OnButtonOffplayerCGameServerViewQEAAXXZ_1400022F2.cpp" />
    <ClCompile Include="Source\j_OnLoop_StaticCPlayerSAXXZ_14000C419.cpp" />
    <ClCompile Include="Source\j_OnPlayerCreateCompleteProcItemCombineMgrQEAAXXZ_14000D832.cpp" />
    <ClCompile Include="Source\j_ON__list_BUDDY_LISTQEAAXPEADPEAVCPlayerZ_1400023EC.cpp" />
    <ClCompile Include="Source\j_OpenChannelCDarkHoleDungeonQuestQEAAPEAVCDarkHol_1400031BB.cpp" />
    <ClCompile Include="Source\j_OpenCReturnGateControllerQEAA_NPEAVCPlayerZ_1400057E0.cpp" />
    <ClCompile Include="Source\j_OpenDungeonCDarkHoleChannelQEAAXPEAV_dh_quest_se_140010654.cpp" />
    <ClCompile Include="Source\j_OutOfMapCPlayerQEAA_NPEAVCMapDataGEPEAMZ_14000E2F0.cpp" />
    <ClCompile Include="Source\j_OutOfSecCPlayerUEAAXXZ_14000D6FC.cpp" />
    <ClCompile Include="Source\j_out_playerCGuildMasterEffectQEAA_NPEAVCPlayerEZ_14000E354.cpp" />
    <ClCompile Include="Source\j_PartyListInitCPartyPlayerQEAAXXZ_140012C1F.cpp" />
    <ClCompile Include="Source\j_PastWhisperInitCPlayerQEAAXXZ_140011298.cpp" />
    <ClCompile Include="Source\j_PcBangDeleteItemCPcBangFavorQEAAXPEAVCPlayerZ_140009BC4.cpp" />
    <ClCompile Include="Source\j_PcBangGiveItemCPcBangFavorQEAA_NPEAVCPlayerKPEAE_140011A4A.cpp" />
    <ClCompile Include="Source\j_pc_AddBagCPlayerQEAAXGZ_140010E33.cpp" />
    <ClCompile Include="Source\j_pc_AlterItemSlotRequestCPlayerQEAAXEPEAU__list_a_14000B479.cpp" />
    <ClCompile Include="Source\j_pc_AlterLinkBoardSlotRequestCPlayerQEAAXEPEAU__l_140010CBC.cpp" />
    <ClCompile Include="Source\j_pc_AlterWindowInfoRequestCPlayerQEAAXPEAK000K0Z_14000920A.cpp" />
    <ClCompile Include="Source\j_pc_AnimusCommandRequestCPlayerQEAAXEZ_140004A0C.cpp" />
    <ClCompile Include="Source\j_pc_AnimusInvenChangeCPlayerQEAAXPEAU_STORAGE_POS_14000A0CE.cpp" />
    <ClCompile Include="Source\j_pc_AnimusRecallRequestCPlayerQEAAXGGGZ_140005ADD.cpp" />
    <ClCompile Include="Source\j_pc_AnimusReturnRequestCPlayerQEAAXXZ_14000C2E8.cpp" />
    <ClCompile Include="Source\j_pc_AnimusTargetRequestCPlayerQEAAXEGKZ_140003152.cpp" />
    <ClCompile Include="Source\j_pc_AwaypartyInvitationRequestCPlayerQEAAXPEADZ_14000B546.cpp" />
    <ClCompile Include="Source\j_pc_AwayPartyJoinInvitationAnswerCPlayerQEAAXPEAU_140007513.cpp" />
    <ClCompile Include="Source\j_pc_BackTowerRequestCPlayerQEAAXKZ_140008D73.cpp" />
    <ClCompile Include="Source\j_pc_BackTrapRequestCPlayerQEAAXKGZ_14000A9E8.cpp" />
    <ClCompile Include="Source\j_pc_BillingInfoRequestCPlayerQEAAXXZ_1400026DF.cpp" />
    <ClCompile Include="Source\j_pc_BriefPassCPlayerQEAAXEZ_14000D1A2.cpp" />
    <ClCompile Include="Source\j_pc_BuddyAddAnswerCPlayerQEAAX_NGKZ_140005A6A.cpp" />
    <ClCompile Include="Source\j_pc_BuddyAddRequestCPlayerQEAAXGKPEADZ_14000EF02.cpp" />
    <ClCompile Include="Source\j_pc_BuddyDelRequestCPlayerQEAAXKZ_14000DFD5.cpp" />
    <ClCompile Include="Source\j_pc_BuddyDownloadRequestCPlayerQEAAXXZ_140012F3A.cpp" />
    <ClCompile Include="Source\j_pc_BuyItemStoreCPlayerQEAAXPEAVCItemStoreEPEAU_l_140011D97.cpp" />
    <ClCompile Include="Source\j_pc_CanSelectClassRequestCPlayerQEAAEPEAEZ_1400028AB.cpp" />
    <ClCompile Include="Source\j_pc_CastVoteRequestCPlayerQEAAXHEZ_14000B20D.cpp" />
    <ClCompile Include="Source\j_pc_ChangeModeTypeCPlayerQEAAXHHZ_140010140.cpp" />
    <ClCompile Include="Source\j_pc_CharacterRenameCashCPlayerQEAA_N_NPEAU_STORAG_140002A81.cpp" />
    <ClCompile Include="Source\j_pc_CharacterRenameCheckCPlayerQEAAEPEBDZ_140004458.cpp" />
    <ClCompile Include="Source\j_pc_ChatAllRequestCPlayerQEAAXPEADZ_140007A4A.cpp" />
    <ClCompile Include="Source\j_pc_ChatCircleRequestCPlayerQEAAXPEADZ_14000D9DB.cpp" />
    <ClCompile Include="Source\j_pc_ChatFarRequestCPlayerQEAAXPEAD0Z_14000BFFF.cpp" />
    <ClCompile Include="Source\j_pc_ChatGmNoticeRequestCPlayerQEAAXPEADZ_14000D602.cpp" />
    <ClCompile Include="Source\j_pc_ChatGuildEstSenRequestCPlayerQEAAXPEADZ_14000DA80.cpp" />
    <ClCompile Include="Source\j_pc_ChatGuildRequestCPlayerQEAAXKPEADZ_14000474B.cpp" />
    <ClCompile Include="Source\j_pc_ChatMapRequestCPlayerQEAAXPEADZ_140011937.cpp" />
    <ClCompile Include="Source\j_pc_ChatMgrWhisperRequestCPlayerQEAAXPEADZ_1400047C3.cpp" />
    <ClCompile Include="Source\j_pc_ChatMultiFarRequestCPlayerQEAAXEPEAU_w_namePE_140001014.cpp" />
    <ClCompile Include="Source\j_pc_ChatOperatorRequestCPlayerQEAAXEPEADZ_1400029DC.cpp" />
    <ClCompile Include="Source\j_pc_ChatPartyRequestCPlayerQEAAXPEADZ_1400028F1.cpp" />
    <ClCompile Include="Source\j_pc_ChatRaceBossCryRequestCPlayerQEAAXPEADZ_140006965.cpp" />
    <ClCompile Include="Source\j_pc_ChatRaceBossRequestCPlayerQEAAXPEADZ_140013B29.cpp" />
    <ClCompile Include="Source\j_pc_ChatRaceRequestCPlayerQEAAXPEADZ_140011757.cpp" />
    <ClCompile Include="Source\j_pc_ChatRePresentationRequestCPlayerQEAAXPEADZ_140004D68.cpp" />
    <ClCompile Include="Source\j_pc_ChatTradeRequestMsgCPlayerQEAAXEPEADZ_140004601.cpp" />
    <ClCompile Include="Source\j_pc_ClassSkillRequestCPlayerQEAAXGPEAU_CHRIDPEAGZ_140012EBD.cpp" />
    <ClCompile Include="Source\j_pc_CombineItemCPlayerQEAAXGEPEAU_STORAGE_POS_IND_140006C62.cpp" />
    <ClCompile Include="Source\j_pc_CombineItemExAcceptCPlayerQEAAXPEAU_combine_e_14000A8B2.cpp" />
    <ClCompile Include="Source\j_pc_CombineItemExCPlayerQEAAXPEAU_combine_ex_item_140006ADC.cpp" />
    <ClCompile Include="Source\j_pc_CuttingCompleteCPlayerQEAAXEZ_140011FC7.cpp" />
    <ClCompile Include="Source\j_pc_DarkHoleAnswerReenterRequestCPlayerQEAAX_NGKZ_140004BB5.cpp" />
    <ClCompile Include="Source\j_pc_DarkHoleClearOutRequestCPlayerQEAAXXZ_140003F30.cpp" />
    <ClCompile Include="Source\j_pc_DarkHoleEnterRequestCPlayerQEAAXGKZ_14000DB25.cpp" />
    <ClCompile Include="Source\j_pc_DarkHoleGiveupOutRequestCPlayerQEAAXXZ_14000D2D8.cpp" />
    <ClCompile Include="Source\j_pc_DarkHoleOpenRequestCPlayerQEAAXKZ_1400077C5.cpp" />
    <ClCompile Include="Source\j_pc_DowngradeItemCPlayerQEAAXPEAU_STORAGE_POS_IND_140009016.cpp" />
    <ClCompile Include="Source\j_pc_DTradeAddRequestCPlayerQEAAXEEKEZ_140004E3F.cpp" />
    <ClCompile Include="Source\j_pc_DTradeAnswerRequestCPlayerQEAAXPEAU_CLIDZ_1400133C7.cpp" />
    <ClCompile Include="Source\j_pc_DTradeAskRequestCPlayerQEAAXGZ_14000EEAD.cpp" />
    <ClCompile Include="Source\j_pc_DTradeBetRequestCPlayerQEAAXEKZ_14000DA30.cpp" />
    <ClCompile Include="Source\j_pc_DTradeCancleRequestCPlayerQEAAXXZ_140010947.cpp" />
    <ClCompile Include="Source\j_pc_DTradeDelRequestCPlayerQEAAXEZ_1400064E7.cpp" />
    <ClCompile Include="Source\j_pc_DTradeLockRequestCPlayerQEAAXXZ_140001334.cpp" />
    <ClCompile Include="Source\j_pc_DTradeOKRequestCPlayerQEAAXPEAKZ_140001DF7.cpp" />
    <ClCompile Include="Source\j_pc_EmbellishPartCPlayerQEAAXPEAU_STORAGE_POS_IND_140002941.cpp" />
    <ClCompile Include="Source\j_pc_EquipPartCPlayerQEAAXPEAU_STORAGE_POS_INDIVZ_140011FA4.cpp" />
    <ClCompile Include="Source\j_pc_ExchangeDalantForGoldCPlayerQEAAXKZ_1400122F1.cpp" />
    <ClCompile Include="Source\j_pc_ExchangeGoldForDalantCPlayerQEAAXKZ_14000D2AB.cpp" />
    <ClCompile Include="Source\j_pc_ExchangeGoldForPvPCPlayerQEAAXKZ_14000AE9D.cpp" />
    <ClCompile Include="Source\j_pc_ExchangeItemCPlayerQEAAXGGZ_14000344F.cpp" />
    <ClCompile Include="Source\j_pc_ExitWorldRequestCPlayerQEAAXXZ_140010E74.cpp" />
    <ClCompile Include="Source\j_pc_ForceInvenChangeCPlayerQEAAXPEAU_STORAGE_POS__14000EA89.cpp" />
    <ClCompile Include="Source\j_pc_ForceRequestCPlayerQEAAXGPEAU_CHRIDPEAGZ_14001150E.cpp" />
    <ClCompile Include="Source\j_pc_GestureRequestCPlayerQEAAXEZ_1400066E0.cpp" />
    <ClCompile Include="Source\j_pc_GiveItemCPlayerQEAA_NAEAU_db_con_STORAGE_LIST_140013935.cpp" />
    <ClCompile Include="Source\j_pc_GotoAvatorRequestCPlayerQEAAXPEADZ_140013FC0.cpp" />
    <ClCompile Include="Source\j_pc_GotoBasePortalRequestCPlayerQEAAXGZ_140007F22.cpp" />
    <ClCompile Include="Source\j_pc_GuildBattleBlockCPlayerQEAAX_NZ_1400096C9.cpp" />
    <ClCompile Include="Source\j_pc_GuildCancelSuggestRequestCPlayerQEAAXKZ_1400073B0.cpp" />
    <ClCompile Include="Source\j_pc_GuildDownLoadRequestCPlayerQEAAXXZ_140011BC6.cpp" />
    <ClCompile Include="Source\j_pc_GuildEstablishRequestCPlayerQEAAXPEADZ_140007004.cpp" />
    <ClCompile Include="Source\j_pc_GuildHonorListRequestCPlayerQEAAXEZ_140010258.cpp" />
    <ClCompile Include="Source\j_pc_GuildJoinAcceptRequestCPlayerQEAAXK_NZ_1400116E4.cpp" />
    <ClCompile Include="Source\j_pc_GuildJoinApplyCancelRequestCPlayerQEAAXXZ_14000C9F5.cpp" />
    <ClCompile Include="Source\j_pc_GuildJoinApplyRequestCPlayerQEAAXPEADZ_14000583A.cpp" />
    <ClCompile Include="Source\j_pc_GuildListRequestCPlayerQEAAXEZ_140004EE4.cpp" />
    <ClCompile Include="Source\j_pc_GuildManageRequestCPlayerQEAAXEKKKKZ_14000DA49.cpp" />
    <ClCompile Include="Source\j_pc_GuildNextHonorListRequestCPlayerQEAAXXZ_140007671.cpp" />
    <ClCompile Include="Source\j_pc_GuildOfferSuggestRequestCPlayerQEAAXEKPEADKKK_140007AA4.cpp" />
    <ClCompile Include="Source\j_pc_GuildPushMoneyRequestCPlayerQEAAXKKZ_14000F15F.cpp" />
    <ClCompile Include="Source\j_pc_GuildQueryInfoRequestCPlayerQEAAXKZ_14000BD2A.cpp" />
    <ClCompile Include="Source\j_pc_GuildRoomEnterRequestCPlayerQEAAXPEAU_guildro_140009264.cpp" />
    <ClCompile Include="Source\j_pc_GuildRoomOutRequestCPlayerQEAAXPEAU_guildroom_140012BFC.cpp" />
    <ClCompile Include="Source\j_pc_GuildRoomRentRequestCPlayerQEAAXPEAU_guildroo_140003C2E.cpp" />
    <ClCompile Include="Source\j_pc_GuildRoomRestTimeRequestCPlayerQEAAXPEAU_guil_1400043DB.cpp" />
    <ClCompile Include="Source\j_pc_GuildSelfLeaveRequestCPlayerQEAAXXZ_14000894A.cpp" />
    <ClCompile Include="Source\j_pc_GuildSetHonorRequestCPlayerQEAAXPEAU_guild_ho_14000ACB3.cpp" />
    <ClCompile Include="Source\j_pc_GuildVoteRequestCPlayerQEAAXKEZ_1400034A4.cpp" />
    <ClCompile Include="Source\j_pc_InitClassCPlayerQEAAEXZ_14000F0F6.cpp" />
    <ClCompile Include="Source\j_pc_InitClassRequestCPlayerQEAAEXZ_14000D968.cpp" />
    <ClCompile Include="Source\j_pc_LimitItemNumRequestCPlayerQEAAXKZ_140004372.cpp" />
    <ClCompile Include="Source\j_pc_LinkBoardRequestCPlayerQEAAXXZ_140005C09.cpp" />
    <ClCompile Include="Source\j_pc_MacroUpdateCPlayerQEAAXPEADZ_14000183E.cpp" />
    <ClCompile Include="Source\j_pc_MakeItemCPlayerQEAAXPEAU_STORAGE_POS_INDIVGE0_140003544.cpp" />
    <ClCompile Include="Source\j_pc_MakeTowerRequestCPlayerQEAAXGGEPEAU__material_14000B708.cpp" />
    <ClCompile Include="Source\j_pc_MakeTrapRequestCPlayerQEAAXGGPEAMPEAGZ_140005C54.cpp" />
    <ClCompile Include="Source\j_pc_MineCancleCPlayerQEAAXXZ_140002275.cpp" />
    <ClCompile Include="Source\j_pc_MineCompleteCPlayerQEAAXXZ_140004F2A.cpp" />
    <ClCompile Include="Source\j_pc_MineStartCPlayerQEAAXEEGZ_140005A1F.cpp" />
    <ClCompile Include="Source\j_pc_MoveModeChangeRequestCPlayerQEAAXEZ_14000CA09.cpp" />
    <ClCompile Include="Source\j_pc_MoveNextCPlayerQEAAXEPEAM0EZ_14000782E.cpp" />
    <ClCompile Include="Source\j_pc_MovePortalCPlayerQEAAXHPEAGZ_1400016CC.cpp" />
    <ClCompile Include="Source\j_pc_MoveStopCPlayerQEAAXPEAMZ_14000EF9D.cpp" />
    <ClCompile Include="Source\j_pc_MoveToOwnStoneMapRequestCPlayerQEAAXXZ_14000BC76.cpp" />
    <ClCompile Include="Source\j_pc_NewPosStartCPlayerQEAAXXZ_1400096D3.cpp" />
    <ClCompile Include="Source\j_pc_NotifyRaceBossCryMsgCPlayerQEAAXXZ_14000B794.cpp" />
    <ClCompile Include="Source\j_pc_NPCLinkCheckItemRequestCPlayerQEAA_NPEAU_STOR_14000DE54.cpp" />
    <ClCompile Include="Source\j_pc_NPCLinkCheckItemRequest_CheckCPlayerQEAAEPEAU_14000290A.cpp" />
    <ClCompile Include="Source\j_pc_NPCLinkCheckItemRequest_UseCPlayerQEAAEPEAU_S_140010D8E.cpp" />
    <ClCompile Include="Source\j_pc_NuclearAfterEffectCPlayerQEAAXXZ_140001E42.cpp" />
    <ClCompile Include="Source\j_pc_OffPartCPlayerQEAAXPEAU_STORAGE_POS_INDIVZ_140005051.cpp" />
    <ClCompile Include="Source\j_pc_OreCuttingCPlayerQEAAXGEZ_14000C27A.cpp" />
    <ClCompile Include="Source\j_pc_OreIntoBagCPlayerQEAAXGGEZ_1400027A2.cpp" />
    <ClCompile Include="Source\j_pc_PartyAlterLootShareReqeuestCPlayerQEAAXEZ_14000725C.cpp" />
    <ClCompile Include="Source\j_pc_PartyDisJointReqeuestCPlayerQEAAXXZ_1400122F6.cpp" />
    <ClCompile Include="Source\j_pc_PartyJoinApplicationAnswerCPlayerQEAAXPEAU_CL_140009944.cpp" />
    <ClCompile Include="Source\j_pc_PartyJoinApplicationCPlayerQEAAXGZ_14000B703.cpp" />
    <ClCompile Include="Source\j_pc_PartyJoinInvitationAnswerCPlayerQEAAXPEAU_CLI_140011A4F.cpp" />
    <ClCompile Include="Source\j_pc_PartyJoinInvitationCPlayerQEAAXGZ_140011775.cpp" />
    <ClCompile Include="Source\j_pc_PartyLeaveCompulsionReqeuestCPlayerQEAAXKZ_140001DB6.cpp" />
    <ClCompile Include="Source\j_pc_PartyLeaveSelfReqeuestCPlayerQEAAXXZ_1400014CE.cpp" />
    <ClCompile Include="Source\j_pc_PartyLockReqeuestCPlayerQEAAX_NZ_140006339.cpp" />
    <ClCompile Include="Source\j_pc_PartyReqBlockCPlayerQEAAX_NZ_14000E129.cpp" />
    <ClCompile Include="Source\j_pc_PartySuccessionReqeuestCPlayerQEAAXKZ_140011C89.cpp" />
    <ClCompile Include="Source\j_pc_PlayAttack_ForceCPlayerQEAAXPEAVCCharacterPEA_140005939.cpp" />
    <ClCompile Include="Source\j_pc_PlayAttack_GenCPlayerQEAAXPEAVCCharacterEGG_N_140012B25.cpp" />
    <ClCompile Include="Source\j_pc_PlayAttack_SelfDestructionCPlayerQEAAXXZ_14000B5A0.cpp" />
    <ClCompile Include="Source\j_pc_PlayAttack_SiegeCPlayerQEAAXPEAVCCharacterPEA_140003F67.cpp" />
    <ClCompile Include="Source\j_pc_PlayAttack_SkillCPlayerQEAAXPEAVCCharacterPEA_14000908E.cpp" />
    <ClCompile Include="Source\j_pc_PlayAttack_TestCPlayerQEAAXEEGEPEAFZ_140007C70.cpp" />
    <ClCompile Include="Source\j_pc_PlayAttack_UnitCPlayerQEAAXPEAVCCharacterEZ_140001DBB.cpp" />
    <ClCompile Include="Source\j_pc_PostContentRequestCPlayerQEAAXKZ_14000508D.cpp" />
    <ClCompile Include="Source\j_pc_PostDeleteRequestCPlayerQEAAXKZ_14000F3B2.cpp" />
    <ClCompile Include="Source\j_pc_PostItemGoldRequestCPlayerQEAAXKZ_140008D69.cpp" />
    <ClCompile Include="Source\j_pc_PostListRequestCPlayerQEAAXXZ_14000E769.cpp" />
    <ClCompile Include="Source\j_pc_PostReturnConfirmRequestCPlayerQEAAXKZ_1400121B6.cpp" />
    <ClCompile Include="Source\j_pc_PotionDivisionCPlayerQEAAXGGEZ_140004FB1.cpp" />
    <ClCompile Include="Source\j_pc_PotionSeparationCPlayerQEAAXGEZ_14000ADD0.cpp" />
    <ClCompile Include="Source\j_pc_PotionUseTrunkExtendCPlayerQEAAXXZ_140004877.cpp" />
    <ClCompile Include="Source\j_pc_ProposeVoteRequestCPlayerQEAAXEPEADZ_14000681B.cpp" />
    <ClCompile Include="Source\j_pc_PvpCashRecorverCPlayerQEAAXKEZ_1400078BF.cpp" />
    <ClCompile Include="Source\j_pc_QuestGiveupRequestCPlayerQEAAXEZ_14000C1F3.cpp" />
    <ClCompile Include="Source\j_pc_RadarCharInfoCPlayerQEAA_NXZ_1400089F9.cpp" />
    <ClCompile Include="Source\j_pc_RealMovPosCPlayerQEAAXPEAMZ_140003779.cpp" />
    <ClCompile Include="Source\j_pc_RefreshGroupTargetPositionCPlayerQEAAXEPEAVCG_140005C86.cpp" />
    <ClCompile Include="Source\j_pc_RegistBindCPlayerQEAAXPEAVCItemStoreZ_14001392B.cpp" />
    <ClCompile Include="Source\j_pc_ReleaseGroupTargetObjectRequestCPlayerQEAAXEZ_140011338.cpp" />
    <ClCompile Include="Source\j_pc_ReleaseSiegeModeRequestCPlayerQEAAXXZ_1400115AE.cpp" />
    <ClCompile Include="Source\j_pc_ReleaseTargetObjectRequestCPlayerQEAAXXZ_140012CEC.cpp" />
    <ClCompile Include="Source\j_pc_RenameItemNConditionCheckCPlayerQEAAEPEAU_STO_14000D5DF.cpp" />
    <ClCompile Include="Source\j_pc_RequestChangeTaxRateCPlayerQEAAXEZ_1400092E6.cpp" />
    <ClCompile Include="Source\j_pc_RequestDialogWithNPCCPlayerQEAAXPEAVCItemStor_140010FA0.cpp" />
    <ClCompile Include="Source\j_pc_RequestPatriarchPunishmentCPlayerQEAAXEPEAD0Z_14000BDD4.cpp" />
    <ClCompile Include="Source\j_pc_RequestQuestFromNPCCPlayerQEAAXPEAVCItemStore_14000A4D4.cpp" />
    <ClCompile Include="Source\j_pc_RequestQuestListFromNPCCPlayerQEAAXPEAVCItemS_140001389.cpp" />
    <ClCompile Include="Source\j_pc_RequestTaxRateCPlayerQEAAXXZ_14000A6C3.cpp" />
    <ClCompile Include="Source\j_pc_RequestUILockCertifyCPlayerQEAAXPEAVCUserDBPE_1400056F5.cpp" />
    <ClCompile Include="Source\j_pc_RequestUILockFindPWCPlayerQEAAXPEAVCUserDBPEA_14000B6DB.cpp" />
    <ClCompile Include="Source\j_pc_RequestUILockInitCPlayerQEAAXPEAVCUserDBPEAD1_14000B6CC.cpp" />
    <ClCompile Include="Source\j_pc_RequestUILockUpdateCPlayerQEAAXPEAD00E0Z_140011CFC.cpp" />
    <ClCompile Include="Source\j_pc_RequestWatchingWithNPCCPlayerQEAAXPEAVCItemSt_1400079E1.cpp" />
    <ClCompile Include="Source\j_pc_ResDivisionCPlayerQEAAXGGEZ_14000827E.cpp" />
    <ClCompile Include="Source\j_pc_ResSeparationCPlayerQEAAXGEZ_140004237.cpp" />
    <ClCompile Include="Source\j_pc_ResurrectCPlayerQEAA_N_NZ_14000B66D.cpp" />
    <ClCompile Include="Source\j_pc_RevivalCPlayerQEAAX_NZ_1400025FE.cpp" />
    <ClCompile Include="Source\j_pc_SelectClassRequestCPlayerQEAAXGEZ_140008AEE.cpp" />
    <ClCompile Include="Source\j_pc_SelectQuestAfterHappenEventCPlayerQEAAXEZ_14000809E.cpp" />
    <ClCompile Include="Source\j_pc_SelectQuestRewardCPlayerQEAAXEEEZ_14000B77B.cpp" />
    <ClCompile Include="Source\j_pc_SellItemStoreCPlayerQEAAXPEAVCItemStoreEPEAU__14000CD79.cpp" />
    <ClCompile Include="Source\j_pc_SetGroupMapPointRequestCPlayerQEAAXEPEAMZ_1400121D9.cpp" />
    <ClCompile Include="Source\j_pc_SetGroupTargetObjectRequestCPlayerQEAAXPEAVCG_14000D2E2.cpp" />
    <ClCompile Include="Source\j_pc_SetInGuildBattleCPlayerQEAAX_NEZ_140011F31.cpp" />
    <ClCompile Include="Source\j_pc_SetItemCheckRequestCPlayerQEAA_NKEE_NZ_140005628.cpp" />
    <ClCompile Include="Source\j_pc_SetRaceBossCryMsgCPlayerQEAAXEPEADZ_14000D60C.cpp" />
    <ClCompile Include="Source\j_pc_SetTargetObjectRequestCPlayerQEAAXPEAVCGameOb_14000F407.cpp" />
    <ClCompile Include="Source\j_pc_SkillRequestCPlayerQEAAXEPEAU_CHRIDPEAGZ_14000C6A8.cpp" />
    <ClCompile Include="Source\j_pc_StopCPlayerQEAAXXZ_14000A4F2.cpp" />
    <ClCompile Include="Source\j_pc_TakeGroundingItemCPlayerQEAAXPEAVCItemBoxGZ_140007CF7.cpp" />
    <ClCompile Include="Source\j_pc_TalikCrystalExchangeCPlayerQEAAXEPEAU_list_ta_14000AD99.cpp" />
    <ClCompile Include="Source\j_pc_ThrowSkillRequestCPlayerQEAAXGPEAU_CHRIDPEAGZ_14000DEFE.cpp" />
    <ClCompile Include="Source\j_pc_ThrowStorageItemCPlayerQEAAXPEAU_STORAGE_POS__140011978.cpp" />
    <ClCompile Include="Source\j_pc_ThrowUnitRequestCPlayerQEAAXPEAU_CHRIDPEAGZ_1400130AC.cpp" />
    <ClCompile Include="Source\j_pc_TradeBlockCPlayerQEAAX_NZ_140010456.cpp" />
    <ClCompile Include="Source\j_pc_TransformSiegeModeRequestCPlayerQEAAXGZ_140012620.cpp" />
    <ClCompile Include="Source\j_pc_TransShipRenewTicketRequestCPlayerQEAAXGZ_140013534.cpp" />
    <ClCompile Include="Source\j_pc_TrunkAlterItemSlotRequestCPlayerQEAAXKEEZ_140013A5C.cpp" />
    <ClCompile Include="Source\j_pc_TrunkChangePasswdRequestCPlayerQEAAXPEAD0E0Z_14000899A.cpp" />
    <ClCompile Include="Source\j_pc_TrunkCreateCostIsFreeRequestCPlayerQEAAEXZ_14000A9B6.cpp" />
    <ClCompile Include="Source\j_pc_TrunkDownloadRequestCPlayerQEAAXPEADZ_140005CC2.cpp" />
    <ClCompile Include="Source\j_pc_TrunkEstRequestCPlayerQEAAXPEADE0Z_140004D36.cpp" />
    <ClCompile Include="Source\j_pc_TrunkExtendRequestCPlayerQEAAXXZ_14000DEF9.cpp" />
    <ClCompile Include="Source\j_pc_TrunkHintAnswerRequestCPlayerQEAAXPEADZ_140005DB7.cpp" />
    <ClCompile Include="Source\j_pc_TrunkIoMergeRequestCPlayerQEAAXEEGGGZ_14000C603.cpp" />
    <ClCompile Include="Source\j_pc_TrunkIoMoneyRequestCPlayerQEAAXEKKZ_14000EF93.cpp" />
    <ClCompile Include="Source\j_pc_TrunkIoMoveRequestCPlayerQEAAXEEGEZ_1400025D1.cpp" />
    <ClCompile Include="Source\j_pc_TrunkIoSwapRequestCPlayerQEAAXEEGGZ_14000161D.cpp" />
    <ClCompile Include="Source\j_pc_TrunkPotionDivisionCPlayerQEAAXGGGEZ_140003D50.cpp" />
    <ClCompile Include="Source\j_pc_TrunkPwHintIndexRequestCPlayerQEAAXXZ_14000615E.cpp" />
    <ClCompile Include="Source\j_pc_TrunkResDivisionCPlayerQEAAXGGGEZ_14000D567.cpp" />
    <ClCompile Include="Source\j_pc_UnitBulletFillRequestCPlayerQEAAXEPEAGHZ_14000333C.cpp" />
    <ClCompile Include="Source\j_pc_UnitBulletReplaceRequestCPlayerQEAAXEEEZ_1400085A8.cpp" />
    <ClCompile Include="Source\j_pc_UnitDeliveryRequestCPlayerQEAAXEPEAVCItemStor_140005132.cpp" />
    <ClCompile Include="Source\j_pc_UnitFrameBuyRequestCPlayerQEAAXEHZ_140011932.cpp" />
    <ClCompile Include="Source\j_pc_UnitFrameRepairRequestCPlayerQEAAXEHZ_14000201D.cpp" />
    <ClCompile Include="Source\j_pc_UnitLeaveRequestCPlayerQEAAXPEAMZ_14000E94E.cpp" />
    <ClCompile Include="Source\j_pc_UnitPackFillRequestCPlayerQEAAXEEPEAU__list_u_14000D39B.cpp" />
    <ClCompile Include="Source\j_pc_UnitPartTuningRequestCPlayerQEAAXEEPEAU_tunin_140006488.cpp" />
    <ClCompile Include="Source\j_pc_UnitReturnRequestCPlayerQEAAXXZ_140011E32.cpp" />
    <ClCompile Include="Source\j_pc_UnitSellRequestCPlayerQEAAXEHZ_140004183.cpp" />
    <ClCompile Include="Source\j_pc_UnitTakeRequestCPlayerQEAAXXZ_14000B019.cpp" />
    <ClCompile Include="Source\j_pc_UpdateDataForPostSendCPlayerQEAAXXZ_140002F2C.cpp" />
    <ClCompile Include="Source\j_pc_UpdateDataForTradeCPlayerQEAAXPEAV1Z_14000AFE2.cpp" />
    <ClCompile Include="Source\j_pc_UpgradeItemCPlayerQEAAXPEAU_STORAGE_POS_INDIV_140006C5D.cpp" />
    <ClCompile Include="Source\j_pc_UseFireCrackerCPlayerQEAAHGZ_14000B519.cpp" />
    <ClCompile Include="Source\j_pc_UsePotionItemCPlayerQEAAXPEAV1PEAU_STORAGE_PO_140012ED6.cpp" />
    <ClCompile Include="Source\j_pc_UseRadarItemCPlayerQEAA_NPEAU_STORAGE_POS_IND_1400046FB.cpp" />
    <ClCompile Include="Source\j_pc_UseRecoverLossExpItemCPlayerQEAADGZ_14001359D.cpp" />
    <ClCompile Include="Source\j_pc_UserSoccerBallCPlayerQEAAEGAEAGZ_14000C941.cpp" />
    <ClCompile Include="Source\j_pc_WhisperBlockCPlayerQEAAX_NZ_14001164E.cpp" />
    <ClCompile Include="Source\j_pc_WPActiveAttack_ForceCPlayerQEAA_NPEAU_be_dama_14000B3A7.cpp" />
    <ClCompile Include="Source\j_pc_WPActiveAttack_SkillCPlayerQEAA_NPEAU_be_dama_14000BFEB.cpp" />
    <ClCompile Include="Source\j_PlayerInfoResultCNetworkEXAEAA_NHPEADZ_14000E3A4.cpp" />
    <ClCompile Include="Source\j_PlayerMacroUpdateCNetworkEXAEAA_NHPEADZ_140007716.cpp" />
    <ClCompile Include="Source\j_player_createCMgrAccountLobbyHistoryQEAAX_NPEAU__1400062FD.cpp" />
    <ClCompile Include="Source\j_player_create_complete_moneyCMgrAccountLobbyHist_140010E88.cpp" />
    <ClCompile Include="Source\j_player_money_fixCMgrAccountLobbyHistoryQEAAXKKPE_140010005.cpp" />
    <ClCompile Include="Source\j_PopBuddy_BUDDY_LISTQEAAHKPEAPEAVCPlayerZ_140006CE4.cpp" />
    <ClCompile Include="Source\j_PopLinkCPlayerDBQEAAXHZ_14000CD29.cpp" />
    <ClCompile Include="Source\j_PostSendRequestCPostSystemManagerQEAA_NPEAVCPlay_140008265.cpp" />
    <ClCompile Include="Source\j_Potion_Buf_ExtendCPlayerQEAAXXZ_14000AECF.cpp" />
    <ClCompile Include="Source\j_PreCheckPotionCPotionMgrQEAAHPEAVCPlayerAEAPEAVC_1400014A1.cpp" />
    <ClCompile Include="Source\j_PrivateExponentNameCryptoPPYAPEBDXZ_140009CA5.cpp" />
    <ClCompile Include="Source\j_ProcessCheatCommandYA_NPEAVCPlayerPEADZ_140008DF0.cpp" />
    <ClCompile Include="Source\j_ProcessEnterCReturnGateControllerIEAAHIPEAVCPlay_14000AF74.cpp" />
    <ClCompile Include="Source\j_ProcessRequestRecallCRecallEffectControllerIEAAE_14000B550.cpp" />
    <ClCompile Include="Source\j_PushApplierCGuildQEAA_NPEAVCPlayerZ_1400132D2.cpp" />
    <ClCompile Include="Source\j_PushBuddy_BUDDY_LISTQEAAHKPEADPEAVCPlayerZ_1400046A1.cpp" />
    <ClCompile Include="Source\j_PushDamageCLootingMgrQEAAXPEAVCPlayerGZ_140010087.cpp" />
    <ClCompile Include="Source\j_PushDQSCheatPlyerVoteInfoCPlayerQEAAXXZ_1400032C4.cpp" />
    <ClCompile Include="Source\j_PushDQSUpdatePlyerVoteInfoCPlayerQEAAXXZ_1400056A5.cpp" />
    <ClCompile Include="Source\j_PushDQSUpdateVoteAvilableCPlayerQEAAXXZ_14000B3CF.cpp" />
    <ClCompile Include="Source\j_PushLinkCPlayerDBQEAA_NHG_NZ_14000B9E2.cpp" />
    <ClCompile Include="Source\j_PushMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPEA_14000DCCE.cpp" />
    <ClCompile Include="Source\j_Push_DataTimeLimitMgrQEAAXPEAUPlayer_TL_StatusGZ_140013DFE.cpp" />
    <ClCompile Include="Source\j_qc_RewardExpYA_NPEAUstrFILEPEAVCDarkHoleDungeonQ_14000F4BB.cpp" />
    <ClCompile Include="Source\j_ReCalcMaxHFSPCPlayerQEAAX_N0Z_140009002.cpp" />
    <ClCompile Include="Source\j_RecallCRecallRequestQEAAEPEAVCPlayer_NZ_1400113F1.cpp" />
    <ClCompile Include="Source\j_RecallRandomPositionInRangeCPlayerQEAAXPEAVCMapD_140009B38.cpp" />
    <ClCompile Include="Source\j_ReceiveDestroyKeeperCHolyStoneSystemQEAAXPEAVCCh_14001074E.cpp" />
    <ClCompile Include="Source\j_recovery_expCMgrAvatorLvHistoryQEAAXHNGNGNHPEAD0_140012463.cpp" />
    <ClCompile Include="Source\j_RecvHSKQuestCPlayerQEAAXEEHGGEZ_140010F32.cpp" />
    <ClCompile Include="Source\j_RecvKillMessageCAnimusUEAAXPEAVCCharacterZ_140004B10.cpp" />
    <ClCompile Include="Source\j_RecvKillMessageCGameObjectUEAAXPEAVCCharacterZ_14000380A.cpp" />
    <ClCompile Include="Source\j_RecvKillMessageCGuardTowerUEAAXPEAVCCharacterZ_140011F5E.cpp" />
    <ClCompile Include="Source\j_RecvKillMessageCNuclearBombUEAAXPEAVCCharacterZ_140006CF3.cpp" />
    <ClCompile Include="Source\j_RecvKillMessageCPlayerUEAAXPEAVCCharacterZ_14000AB6E.cpp" />
    <ClCompile Include="Source\j_RecvKillMessageCTrapUEAAXPEAVCCharacterZ_14000F137.cpp" />
    <ClCompile Include="Source\j_ReEnterMemberCTransportShipQEAAXPEAVCPlayerZ_14000EB56.cpp" />
    <ClCompile Include="Source\j_RegistCandidateMgrQEAA_NPEAVCPlayerZ_14000E3EF.cpp" />
    <ClCompile Include="Source\j_RegistCheatCNationSettingFactoryIEAA_NPEAVCNatio_14000E49E.cpp" />
    <ClCompile Include="Source\j_RegistCRecallRequestQEAAEPEAVCPlayerPEAVCCharact_140003111.cpp" />
    <ClCompile Include="Source\j_regist_to_mapAutominePersonalQEAA_NPEAVCPlayerPE_14001137E.cpp" />
    <ClCompile Include="Source\j_ReleaseCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAV_14000AB5A.cpp" />
    <ClCompile Include="Source\j_ReleaseCRaceBuffInfoByHolyQuestListQEAA_NIHPEAVC_14000FD5D.cpp" />
    <ClCompile Include="Source\j_ReleaseCRaceBuffInfoByHolyQuestQEAA_NPEAVCPlayer_140001B68.cpp" />
    <ClCompile Include="Source\j_ReleasecStaticMember_PlayerSAXXZ_1400096A6.cpp" />
    <ClCompile Include="Source\j_RemoveAllContinousEffectCCharacterQEAA_NXZ_14000A916.cpp" />
    <ClCompile Include="Source\j_RemoveAllContinousEffectGroupCCharacterQEAA_NIZ_140010B54.cpp" />
    <ClCompile Include="Source\j_RemovePartyMemberCPartyPlayerQEAA_NPEAV1PEAPEAV1_140009732.cpp" />
    <ClCompile Include="Source\j_RemovePotionContEffectCPotionMgrIEAAHPEAVCPlayer_14000B58C.cpp" />
    <ClCompile Include="Source\j_RemoveSFContEffectCCharacterQEAAXEG_N0Z_14000DA62.cpp" />
    <ClCompile Include="Source\j_RemoveSFContHelpByEffectCCharacterQEAAXHHZ_1400094B7.cpp" />
    <ClCompile Include="Source\j_RemoveSlotCCharacterQEAAHPEAV1Z_140003EA4.cpp" />
    <ClCompile Include="Source\j_RenewOldMemberCTransportShipQEAA_NPEAVCPlayerZ_140009F7A.cpp" />
    <ClCompile Include="Source\j_RequestAcceptGMCallGMCallMgrQEAA_NPEAVCPlayerKZ_14000E82C.cpp" />
    <ClCompile Include="Source\j_RequestGMCallGMCallMgrQEAA_NPEAVCPlayerHZ_14000C93C.cpp" />
    <ClCompile Include="Source\j_RequestGMListGMCallMgrQEAA_NPEAVCPlayerHZ_140013467.cpp" />
    <ClCompile Include="Source\j_RequestRecallCRecallEffectControllerQEAA_NPEAVCP_14000EE21.cpp" />
    <ClCompile Include="Source\j_ReservationForceCloseCPlayerQEAAXXZ_140004935.cpp" />
    <ClCompile Include="Source\j_ResetSlotCCharacterQEAAXXZ_1400032D8.cpp" />
    <ClCompile Include="Source\j_ReStartCNormalGuildBattleGUILD_BATTLEQEAAEPEAVCP_1400044A8.cpp" />
    <ClCompile Include="Source\j_ResurrectCPlayerQEAAXXZ_14000BCF8.cpp" />
    <ClCompile Include="Source\j_Return_AnimusAskCPlayerQEAAXEZ_1400109FB.cpp" />
    <ClCompile Include="Source\j_RewardChangeClassCPlayerQEAAXPEAU_class_fldEZ_140002F5E.cpp" />
    <ClCompile Include="Source\j_RewardChangeClassMasteryCPlayerQEAAXPEAU_class_f_14000FA9C.cpp" />
    <ClCompile Include="Source\j_RewardChangeClassRewardItemCPlayerQEAAXPEAU_clas_14000871A.cpp" />
    <ClCompile Include="Source\j_RewardRaceWarPvpCashCPlayerQEAAXXZ_1400060D7.cpp" />
    <ClCompile Include="Source\j_Reward_DarkDungeonCPlayerQEAAXPEAV_dh_reward_sub_14000E084.cpp" />
    <ClCompile Include="Source\j_RobbedHPCAnimusUEAA_NPEAVCCharacterHZ_14000462E.cpp" />
    <ClCompile Include="Source\j_RobbedHPCGameObjectUEAA_NPEAVCCharacterHZ_140004C96.cpp" />
    <ClCompile Include="Source\j_RobbedHPCGuardTowerUEAA_NPEAVCCharacterHZ_14000DB66.cpp" />
    <ClCompile Include="Source\j_RobbedHPCMonsterUEAA_NPEAVCCharacterHZ_14000662C.cpp" />
    <ClCompile Include="Source\j_RobbedHPCPlayerUEAA_NPEAVCCharacterHZ_1400053CB.cpp" />
    <ClCompile Include="Source\j_SearchAggroNodeCMonsterAggroMgrQEAAPEAUCAggroNod_140001EE2.cpp" />
    <ClCompile Include="Source\j_SearchAttackTargetCHolyKeeperQEAAPEAVCCharacterX_1400037D8.cpp" />
    <ClCompile Include="Source\j_SearchBuddyLogin_BUDDY_LISTQEAA_NPEAVCPlayerKPEA_140013A0C.cpp" />
    <ClCompile Include="Source\j_SearchCharacterPathDfAIMgrSA_NPEAVCMonsterAIPEAV_14000DDFA.cpp" />
    <ClCompile Include="Source\j_SearchMoveTargetCHolyKeeperQEAAPEAVCPlayerXZ_140008788.cpp" />
    <ClCompile Include="Source\j_SearchNearEnemyCAnimusQEAAPEAVCCharacterXZ_14000F1C3.cpp" />
    <ClCompile Include="Source\j_SearchNearEnemyCGuardTowerQEAAPEAVCCharacterXZ_14000EFAC.cpp" />
    <ClCompile Include="Source\j_SearchNearEnemyCTrapQEAAPEAVCCharacterXZ_14000D283.cpp" />
    <ClCompile Include="Source\j_SearchNearPlayerAttackCAnimusQEAAPEAVCCharacterX_140011C93.cpp" />
    <ClCompile Include="Source\j_SearchNearPlayerCMonsterHelperSAPEAVCPlayerPEAVC_140008710.cpp" />
    <ClCompile Include="Source\j_SearchNearPlayerCMonsterQEAAPEAVCCharacterXZ_140011BE4.cpp" />
    <ClCompile Include="Source\j_SearchTargetMovePos_MovingTargetCMonsterHelperSA_140001037.cpp" />
    <ClCompile Include="Source\j_SearchTargetMovePos_StopTargetCMonsterHelperSAHP_140001564.cpp" />
    <ClCompile Include="Source\j_SelectClassCPlayerDBQEAAXEPEAU_class_fldZ_140001CCB.cpp" />
    <ClCompile Include="Source\j_SelectDeleteBufCPotionMgrQEAAHPEAVCPlayer_N1Z_14000EEBC.cpp" />
    <ClCompile Include="Source\j_Select_CharacterBaseInfoByNameCRFWorldDatabaseQE_140008571.cpp" />
    <ClCompile Include="Source\j_Select_CharacterBaseInfoBySerialCRFWorldDatabase_1400080AD.cpp" />
    <ClCompile Include="Source\j_Select_CharacterBaseInfoCRFWorldDatabaseQEAAEKPE_14000540C.cpp" />
    <ClCompile Include="Source\j_Select_CharacterGeneralInfoCRFWorldDatabaseQEAAE_140001294.cpp" />
    <ClCompile Include="Source\j_Select_CharacterNameCRFWorldDatabaseQEAA_NKPEAD0_14000C4E6.cpp" />
    <ClCompile Include="Source\j_Select_CharacterReNameCRFWorldDatabaseQEAA_NPEAD_14000BAF5.cpp" />
    <ClCompile Include="Source\j_Select_CharacterSerialCRFWorldDatabaseQEAA_NPEAD_1400067D0.cpp" />
    <ClCompile Include="Source\j_Select_CharactersInfoCRFWorldDatabaseQEAA_NKPEAU_14000F335.cpp" />
    <ClCompile Include="Source\j_Select_NotArrangeCharacterCRFWorldDatabaseQEAAEK_14000F9FC.cpp" />
    <ClCompile Include="Source\j_Select_PlayerTimeLimitInfoCRFWorldDatabaseQEAAHK_14000B96F.cpp" />
    <ClCompile Include="Source\j_Select_PlayerTimeLimitInfoCRFWorldDatabaseQEAAHK_140013976.cpp" />
    <ClCompile Include="Source\j_Select_Player_Last_LogoutTimeCRFWorldDatabaseQEA_14000295F.cpp" />
    <ClCompile Include="Source\j_Select_SupplementCRFWorldDatabaseQEAAHKPEAU_worl_140013430.cpp" />
    <ClCompile Include="Source\j_Select_Supplement_ActPointCRFWorldDatabaseQEAAHK_14000E2D7.cpp" />
    <ClCompile Include="Source\j_Select_Supplement_ExCRFWorldDatabaseQEAAHKPEAU_w_14000191F.cpp" />
    <ClCompile Include="Source\j_SellCompleteCUnmannedTraderUserInfoQEAAEPEAVCPla_140004E5D.cpp" />
    <ClCompile Include="Source\j_SendCRaceBossMsgControllerQEAA_NPEAVCPlayerPEBDZ_1400080A8.cpp" />
    <ClCompile Include="Source\j_SendCTotalGuildRankManagerQEAAXKEPEAVCPlayerZ_14000C5A9.cpp" />
    <ClCompile Include="Source\j_SendCWeeklyGuildRankManagerQEAAXKEPEAVCPlayerZ_14000B1E0.cpp" />
    <ClCompile Include="Source\j_SendData_ChatTransCPlayerQEAAXEKE_NPEADE1Z_1400039C7.cpp" />
    <ClCompile Include="Source\j_SendData_PartyMemberEffectCPlayerQEAAXEGEZ_14000997B.cpp" />
    <ClCompile Include="Source\j_SendData_PartyMemberFPCPlayerQEAAXXZ_1400112C5.cpp" />
    <ClCompile Include="Source\j_SendData_PartyMemberHPCPlayerQEAAXXZ_140007A27.cpp" />
    <ClCompile Include="Source\j_SendData_PartyMemberInfoCPlayerQEAAXGZ_14000CA0E.cpp" />
    <ClCompile Include="Source\j_SendData_PartyMemberInfoToMembersCPlayerQEAAXXZ_140013DD6.cpp" />
    <ClCompile Include="Source\j_SendData_PartyMemberLvCPlayerQEAAXXZ_14001134C.cpp" />
    <ClCompile Include="Source\j_SendData_PartyMemberMaxHFSPCPlayerQEAAXXZ_1400062B2.cpp" />
    <ClCompile Include="Source\j_SendData_PartyMemberPosCPlayerQEAAXXZ_14000C702.cpp" />
    <ClCompile Include="Source\j_SendData_PartyMemberSPCPlayerQEAAXXZ_14000FC81.cpp" />
    <ClCompile Include="Source\j_SendDecideRecallErrorResultToDestCRecallEffectCo_1400066A9.cpp" />
    <ClCompile Include="Source\j_SendEnterResultCReturnGateControllerIEAAXHPEAVCP_140006FCD.cpp" />
    <ClCompile Include="Source\j_SendGetGravityStoneCNormalGuildBattleGuildGUILD__140010C3F.cpp" />
    <ClCompile Include="Source\j_SendGoalMsgCNormalGuildBattleGUILD_BATTLEIEAAX_N_14000DA7B.cpp" />
    <ClCompile Include="Source\j_SendHolyStoneHPCHolyStoneSystemQEAAXPEAVCPlayerZ_1400092B4.cpp" />
    <ClCompile Include="Source\j_SendMsg_AddBagResultCPlayerQEAAXEZ_1400022E3.cpp" />
    <ClCompile Include="Source\j_SendMsg_AddEffectCPlayerQEAAXGEGKPEADZ_140003D64.cpp" />
    <ClCompile Include="Source\j_SendMsg_AdjustAmountInformCPlayerQEAAXEGKZ_1400122D8.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterBoosterCPlayerQEAAXXZ_140013B79.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterContEffectTimeCPlayerQEAAXEZ_140005B64.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterEquipSPInformCPlayerQEAAXXZ_140004273.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterExpInformCPlayerQEAAXXZ_1400046F6.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterGradeInformCPlayerQEAAXXZ_14000465B.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterHPInformCPlayerQEAAXXZ_14000DFA8.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterItemDurInformCPlayerQEAAXEG_KZ_14000B1D6.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterMaxDPCPlayerQEAAXXZ_14000B2C1.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterMoneyInformCPlayerQEAAXEZ_140006CD0.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterPvPCashCPlayerQEAAXHZ_140005A42.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterPvPPointCPlayerQEAAXXZ_1400126C5.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterPvPRankCPlayerQEAAXGKZ_1400101F4.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterRegionInformCPlayerQEAAXHZ_14000E09D.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterSPInformCPlayerQEAAXXZ_14000E854.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterTolCPlayerQEAAXXZ_140010131.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterTowerHPCPlayerQEAAXGGZ_14000ECCD.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterTownOrFieldCPlayerQEAAXXZ_140007D60.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterUnitBulletInformCPlayerQEAAXEGZ_14000CF81.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterUnitHPInformCPlayerQEAAXEKZ_1400070CC.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterWeaponBulletInformCPlayerQEAAXGGZ_14000D945.cpp" />
    <ClCompile Include="Source\j_SendMsg_Alter_Action_PointCPlayerQEAAXEKZ_14000640B.cpp" />
    <ClCompile Include="Source\j_SendMsg_AMPInvenDownloadResultCPlayerQEAAXXZ_14000AEE3.cpp" />
    <ClCompile Include="Source\j_SendMsg_AnimusExpInformCPlayerQEAAXXZ_140007FEF.cpp" />
    <ClCompile Include="Source\j_SendMsg_AnimusFPInformCPlayerQEAAXXZ_1400033A0.cpp" />
    <ClCompile Include="Source\j_SendMsg_AnimusHPInformCPlayerQEAAXXZ_1400093F9.cpp" />
    <ClCompile Include="Source\j_SendMsg_AnimusInvenChangeCPlayerQEAAXEZ_1400051D2.cpp" />
    <ClCompile Include="Source\j_SendMsg_AnimusModeInformCPlayerQEAAXEZ_140010C44.cpp" />
    <ClCompile Include="Source\j_SendMsg_AnimusRecallResultCPlayerQEAAXEGPEAVCAni_14000F943.cpp" />
    <ClCompile Include="Source\j_SendMsg_AnimusRecallWaitTimeFreeCPlayerQEAAX_NZ_140006069.cpp" />
    <ClCompile Include="Source\j_SendMsg_AnimusReturnResultCPlayerQEAAXEGEZ_14000440D.cpp" />
    <ClCompile Include="Source\j_SendMsg_AnimusTargetResultCPlayerQEAAXEZ_140010221.cpp" />
    <ClCompile Include="Source\j_SendMsg_ApexInformCPlayerQEAAXKPEADZ_140002293.cpp" />
    <ClCompile Include="Source\j_SendMsg_AskReEnterCDarkHoleChannelQEAAXPEAVCPlay_14000E480.cpp" />
    <ClCompile Include="Source\j_SendMsg_Assist_ForceCMonsterQEAAXEPEAVCCharacter_14000ADB2.cpp" />
    <ClCompile Include="Source\j_SendMsg_Assist_SkillCMonsterQEAAXEHPEAVCCharacte_140012008.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackActEffectCCharacterQEAAXEPEAV1Z_140006D52.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackResult_CountCPlayerQEAAXPEAVCAttac_140009BFB.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackResult_ErrorCPlayerQEAAXHZ_1400052A4.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackResult_ForceCPlayerQEAAXPEAVCAttac_140002DD3.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackResult_GenCPlayerQEAAXPEAVCAttackG_140012E27.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackResult_SelfDestructionCPlayerQEAAX_140011036.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackResult_SiegeCPlayerQEAAXPEAVCAttac_140008F30.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackResult_SkillCPlayerQEAAXEPEAVCPlay_14000CCC0.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackResult_UnitCPlayerQEAAXPEAVCAttack_140004A43.cpp" />
    <ClCompile Include="Source\j_SendMsg_Attack_SkillCMonsterQEAAXPEAVCMonsterAtt_1400044F3.cpp" />
    <ClCompile Include="Source\j_SendMsg_AwayPartyInvitationQuestionCPlayerQEAAXG_140006C80.cpp" />
    <ClCompile Include="Source\j_SendMsg_AwayPartyRequestResultCPlayerQEAAXEZ_140002BE4.cpp" />
    <ClCompile Include="Source\j_SendMsg_BackTowerResultCPlayerQEAAXEGGZ_140007F6D.cpp" />
    <ClCompile Include="Source\j_SendMsg_BackTrapResultCPlayerQEAAXEZ_140006D7A.cpp" />
    <ClCompile Include="Source\j_SendMsg_BaseDownloadResultCPlayerQEAAXXZ_140006154.cpp" />
    <ClCompile Include="Source\j_SendMsg_BillingExipreInformCPlayerQEAAXEGZ_140011BF8.cpp" />
    <ClCompile Include="Source\j_SendMsg_BillingTypeChangeInformCPlayerQEAAXFJPEA_14000329C.cpp" />
    <ClCompile Include="Source\j_SendMsg_BreakdownEquipItemCPlayerQEAAXEGZ_140012819.cpp" />
    <ClCompile Include="Source\j_SendMsg_BuddhaEventMsgCPlayerQEAAXEZ_140010C80.cpp" />
    <ClCompile Include="Source\j_SendMsg_BuddyAddAnswerResultCPlayerQEAAXE_NKGKPE_140007CB1.cpp" />
    <ClCompile Include="Source\j_SendMsg_BuddyAddAskCPlayerQEAAXGKPEADZ_140009D63.cpp" />
    <ClCompile Include="Source\j_SendMsg_BuddyAddFailCPlayerQEAAXEPEADZ_1400086F7.cpp" />
    <ClCompile Include="Source\j_SendMsg_BuddyDelResultCPlayerQEAAXEKZ_140007617.cpp" />
    <ClCompile Include="Source\j_SendMsg_BuddyLoginInformCPlayerQEAAXKEEZ_14000235B.cpp" />
    <ClCompile Include="Source\j_SendMsg_BuddyLogoffInformCPlayerQEAAXKZ_14000AB0F.cpp" />
    <ClCompile Include="Source\j_SendMsg_BuddyNameReNewalCPlayerQEAAXKPEADZ_140013C00.cpp" />
    <ClCompile Include="Source\j_SendMsg_BuddyPosInformCPlayerQEAAXKEEZ_14000C149.cpp" />
    <ClCompile Include="Source\j_SendMsg_BuyCashItemModeCPlayerQEAAXXZ_14000345E.cpp" />
    <ClCompile Include="Source\j_SendMsg_BuyItemStoreResultCPlayerQEAAXPEAVCItemS_14000EB38.cpp" />
    <ClCompile Include="Source\j_SendMsg_CancelSuggestResultCPlayerQEAAXEZ_14000DA4E.cpp" />
    <ClCompile Include="Source\j_SendMsg_CastVoteResultCPlayerQEAAXEZ_14000C045.cpp" />
    <ClCompile Include="Source\j_SendMsg_ChangeClassCommandCPlayerQEAAXXZ_1400123C8.cpp" />
    <ClCompile Include="Source\j_SendMsg_ChangeOwnerCParkingUnitQEAAXEPEAVCPlayer_1400031B1.cpp" />
    <ClCompile Include="Source\j_SendMsg_Change_MonsterTargetCMonsterQEAAXPEAVCCh_140003891.cpp" />
    <ClCompile Include="Source\j_SendMsg_CharacterRenameCashResultCPlayerQEAAX_NE_140010B95.cpp" />
    <ClCompile Include="Source\j_SendMsg_ChatFarFailureCPlayerQEAAX_NZ_1400118CE.cpp" />
    <ClCompile Include="Source\j_SendMsg_Circle_DelEffectCPlayerQEAAXEGE_NZ_1400116B2.cpp" />
    <ClCompile Include="Source\j_SendMsg_ClassSkillResultCPlayerQEAAXEPEAU_CHRIDG_1400096D8.cpp" />
    <ClCompile Include="Source\j_SendMsg_ClearDarkHoleCPlayerQEAAXEZ_14001109A.cpp" />
    <ClCompile Include="Source\j_SendMsg_CombineItemExAcceptResultCPlayerQEAAXPEA_14000C3BA.cpp" />
    <ClCompile Include="Source\j_SendMsg_CombineItemExResultCPlayerQEAAXPEAU_comb_140013CDC.cpp" />
    <ClCompile Include="Source\j_SendMsg_CombineItemResultCPlayerQEAAXEKPEAU_db_c_140007775.cpp" />
    <ClCompile Include="Source\j_SendMsg_CombineLendItemResultCPlayerQEAAXEKPEAU__14000C248.cpp" />
    <ClCompile Include="Source\j_SendMsg_ConnectNewUserPatriarchElectProcessorQEA_14000B857.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateHolyMasterCHolyStoneSystemQEAAXPEA_14000C2A7.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateTowerResultCPlayerQEAAXEKZ_140005A56.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateTrapResultCPlayerQEAAXEKZ_140004EA3.cpp" />
    <ClCompile Include="Source\j_SendMsg_CumDownloadResultCPlayerQEAAXXZ_14001045B.cpp" />
    <ClCompile Include="Source\j_SendMsg_CuttingCompleteResultCPlayerQEAAXEZ_14000CD7E.cpp" />
    <ClCompile Include="Source\j_SendMsg_DamageResultCPlayerQEAAXPEAU_db_con_STOR_1400036ED.cpp" />
    <ClCompile Include="Source\j_SendMsg_DarkHoleOpenFailCPlayerQEAAXHEZ_140009E62.cpp" />
    <ClCompile Include="Source\j_SendMsg_DarkHoleOpenResultCPlayerQEAAXHHEGKZ_14000F74F.cpp" />
    <ClCompile Include="Source\j_SendMsg_DarkHoleRewardMessageCPlayerQEAAXPEAU_db_140005759.cpp" />
    <ClCompile Include="Source\j_SendMsg_DelEffectCPlayerQEAAXEGEZ_14000AF79.cpp" />
    <ClCompile Include="Source\j_SendMsg_DeleteStorageInformCPlayerQEAAXEGZ_140013B4C.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCPlayerQEAAXXZ_14000E7CD.cpp" />
    <ClCompile Include="Source\j_SendMsg_DieCPlayerQEAAXXZ_1400103ED.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeAccomplishInformCPlayerQEAAX_NGZ_140006FEB.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeAddInformCPlayerQEAAXEPEAU_db_con__14000A709.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeAddResultCPlayerQEAAXEZ_140012184.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeAnswerResultCPlayerQEAAXEZ_1400090BB.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeAskInformCPlayerQEAAXPEAV1Z_14000E192.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeAskResultCPlayerQEAAXEZ_14000CDE7.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeBetInformCPlayerQEAAXEKZ_14000ED86.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeBetResultCPlayerQEAAXEZ_140011081.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeCancleInformCPlayerQEAAXXZ_14000A5B5.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeCancleResultCPlayerQEAAXEZ_14000E705.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeCloseInformCPlayerQEAAXEZ_140012FEE.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeDelInformCPlayerQEAAXEZ_14000CF4A.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeDelResultCPlayerQEAAXEZ_140006406.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeLockInformCPlayerQEAAXXZ_14000CA27.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeLockResultCPlayerQEAAXEZ_140009FA2.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeOKInformCPlayerQEAAXXZ_140007A22.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeOKResultCPlayerQEAAXEZ_140004584.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeStartInformCPlayerQEAAXPEAV10PEAKZ_14000E999.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeUnitAddInformCPlayerQEAAXGPEAU_LIS_140008508.cpp" />
    <ClCompile Include="Source\j_SendMsg_DTradeUnitInfoInformCPlayerQEAAXEPEAU_LI_140010A46.cpp" />
    <ClCompile Include="Source\j_SendMsg_EconomyHistoryInformCPlayerQEAAXXZ_14000B4F6.cpp" />
    <ClCompile Include="Source\j_SendMsg_EconomyRateInformCPlayerQEAAX_NZ_14000D035.cpp" />
    <ClCompile Include="Source\j_SendMsg_EmbellishResultCPlayerQEAAXEZ_1400051A5.cpp" />
    <ClCompile Include="Source\j_SendMsg_EnterDarkHoleCPlayerQEAAXEKZ_140011374.cpp" />
    <ClCompile Include="Source\j_SendMsg_EquipItemLevelLimitCPlayerQEAAXHZ_140004949.cpp" />
    <ClCompile Include="Source\j_SendMsg_EquipPartChangeCPlayerQEAAXEZ_140008C79.cpp" />
    <ClCompile Include="Source\j_SendMsg_EquipPartResultCPlayerQEAAXEZ_14000F8C6.cpp" />
    <ClCompile Include="Source\j_SendMsg_ExchangeItemResultCPlayerQEAAXEPEAU_db_c_140010055.cpp" />
    <ClCompile Include="Source\j_SendMsg_ExchangeLendItemResultCPlayerQEAAXEPEAU__140001B81.cpp" />
    <ClCompile Include="Source\j_SendMsg_ExchangeMoneyResultCPlayerQEAAXEZ_140009EE9.cpp" />
    <ClCompile Include="Source\j_SendMsg_ExitWorldResultCPlayerQEAAXEZ_140005858.cpp" />
    <ClCompile Include="Source\j_SendMsg_ExtTrunkExtendResultCPlayerQEAAXEEEZ_1400077BB.cpp" />
    <ClCompile Include="Source\j_SendMsg_FanfareItemCPlayerQEAAXEPEAU_db_con_STOR_140005DC6.cpp" />
    <ClCompile Include="Source\j_SendMsg_FcitemInformCPlayerQEAAXGKZ_140010F2D.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCPlayerUEAAXHZ_14000A4A7.cpp" />
    <ClCompile Include="Source\j_SendMsg_ForceDownloadResultCPlayerQEAAXXZ_14000F006.cpp" />
    <ClCompile Include="Source\j_SendMsg_ForceInvenChangeCPlayerQEAAXEZ_140005920.cpp" />
    <ClCompile Include="Source\j_SendMsg_ForceResultCPlayerQEAAXEPEAU_CHRIDPEAU_d_14000A425.cpp" />
    <ClCompile Include="Source\j_SendMsg_GestureInformCPlayerQEAAXEZ_140003C29.cpp" />
    <ClCompile Include="Source\j_SendMsg_GiveupDarkHoleCPlayerQEAAXEZ_14000374C.cpp" />
    <ClCompile Include="Source\j_SendMsg_GM_GreetingCPlayerQEAAXPEAD0Z_14000D0D0.cpp" />
    <ClCompile Include="Source\j_SendMsg_GotoBasePortalResultCPlayerQEAAXEZ_140005D21.cpp" />
    <ClCompile Include="Source\j_SendMsg_GotoRecallResultCPlayerQEAAXEEPEAMEZ_14000D31E.cpp" />
    <ClCompile Include="Source\j_SendMsg_GroupTargetInformCPlayerQEAAXEPEADZ_140011F90.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildEstablishFailCPlayerQEAAXEZ_14000831E.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildForceLeaveBoradoriCPlayerQEAAXXZ_140003927.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildJoinAcceptFailCPlayerQEAAXEKZ_140010DD9.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildJoinApplyCancelResultCPlayerQEAAXEZ_14000AE66.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildJoinApplyRejectInformCPlayerQEAAXXZ_14000A55B.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildJoinApplyResultCPlayerQEAAXEPEAVCGu_140012729.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildJoinOtherInformCPlayerQEAAXXZ_14000FD44.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildManageResultCPlayerQEAAXEZ_140009A1B.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildMasterEffectCPlayerQEAAXEEEEEEZ_140010032.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildPushMoneyResultCPlayerQEAAXEZ_140011A86.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildRoomEnterResultCPlayerQEAAXEEEGPEAM_14000C9E6.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildRoomOutResultCPlayerQEAAXEEGPEAMZ_1400033F0.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildRoomRentResultCPlayerQEAAXEEEZ_140011FF4.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildRoomRestTimeResultCPlayerQEAAXXZ_1400110BD.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildSelfLeaveResultCPlayerQEAAXEZ_14000DF67.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildSetHonorResultCPlayerQEAAXEZ_14000AE8E.cpp" />
    <ClCompile Include="Source\j_SendMsg_GUILD_GreetingCPlayerQEAAXPEAD0Z_1400118F6.cpp" />
    <ClCompile Include="Source\j_SendMsg_HonorGuildMarkCPlayerQEAAXEZ_140004A25.cpp" />
    <ClCompile Include="Source\j_SendMsg_HSKQuestActCumCPlayerQEAAXXZ_14000F795.cpp" />
    <ClCompile Include="Source\j_SendMsg_HSKQuestSuccCPlayerQEAAXE_NZ_140009557.cpp" />
    <ClCompile Include="Source\j_SendMsg_InformTaxIncomeCPlayerQEAAXEKPEADZ_140001258.cpp" />
    <ClCompile Include="Source\j_SendMsg_Init_Action_PointCPlayerQEAAXXZ_14000B915.cpp" />
    <ClCompile Include="Source\j_SendMsg_InsertItemInformCPlayerQEAAXEPEAU_db_con_1400125D5.cpp" />
    <ClCompile Include="Source\j_SendMsg_InsertNewQuestCPlayerQEAAXEPEAU_LIST_QUE_1400093C7.cpp" />
    <ClCompile Include="Source\j_SendMsg_InsertNextQuestCPlayerQEAAXEPEAU_LIST_QU_140001E3D.cpp" />
    <ClCompile Include="Source\j_SendMsg_InsertQuestFailureCPlayerQEAAXEKEZ_140009ACF.cpp" />
    <ClCompile Include="Source\j_SendMsg_InsertQuestItemInformCPlayerQEAAXPEAU_db_1400120DA.cpp" />
    <ClCompile Include="Source\j_SendMsg_InvenDownloadResultCPlayerQEAAXXZ_140012E63.cpp" />
    <ClCompile Include="Source\j_SendMsg_ItemDowngradeCPlayerQEAAXEZ_140010096.cpp" />
    <ClCompile Include="Source\j_SendMsg_ItemStorageRefreshCPlayerQEAAXEZ_140004C4B.cpp" />
    <ClCompile Include="Source\j_SendMsg_ItemUpgradeCPlayerQEAAXEZ_14000FF65.cpp" />
    <ClCompile Include="Source\j_SendMsg_JadeEffectErrCPlayerQEAAXEZ_140003233.cpp" />
    <ClCompile Include="Source\j_SendMsg_LastEffectChangeInformCCharacterQEAAXXZ_1400110A4.cpp" />
    <ClCompile Include="Source\j_SendMsg_LeaderChangeCDarkHoleChannelQEAAXPEAVCPl_140011108.cpp" />
    <ClCompile Include="Source\j_SendMsg_LendItemTimeExpiredCPlayerQEAAXEGZ_140005006.cpp" />
    <ClCompile Include="Source\j_SendMsg_LevelCPlayerQEAAXHZ_140005B28.cpp" />
    <ClCompile Include="Source\j_SendMsg_LevelUpCAnimusQEAAXXZ_1400096E7.cpp" />
    <ClCompile Include="Source\j_SendMsg_LinkBoardDownloadResultCPlayerQEAAXXZ_14001298B.cpp" />
    <ClCompile Include="Source\j_SendMsg_MacroRequestCPlayerQEAAXXZ_140003C01.cpp" />
    <ClCompile Include="Source\j_SendMsg_MadeTrapNumInformCPlayerQEAAXEZ_140013BE2.cpp" />
    <ClCompile Include="Source\j_SendMsg_MakeItemCheatSendButtonEnableCPlayerQEAA_1400028CE.cpp" />
    <ClCompile Include="Source\j_SendMsg_MakeItemResultCPlayerQEAAXEZ_140004E80.cpp" />
    <ClCompile Include="Source\j_SendMsg_MapEnvInformCPlayerQEAAXEKZ_14000516E.cpp" />
    <ClCompile Include="Source\j_SendMsg_MapOutCPlayerQEAAXEEZ_140004C05.cpp" />
    <ClCompile Include="Source\j_SendMsg_MaxHFSPCPlayerQEAAXXZ_140011063.cpp" />
    <ClCompile Include="Source\j_SendMsg_MaxPvpPointInformCPlayerQEAAXHZ_14000D116.cpp" />
    <ClCompile Include="Source\j_SendMsg_MemberInfoCDarkHoleChannelQEAAXPEAVCPlay_140002577.cpp" />
    <ClCompile Include="Source\j_SendMsg_MineCancleCPlayerQEAAXXZ_14000F871.cpp" />
    <ClCompile Include="Source\j_SendMsg_MineCompleteResultCPlayerQEAAXEEGEGZ_14000F6F5.cpp" />
    <ClCompile Include="Source\j_SendMsg_MineStartResultCPlayerQEAAXEZ_140006D5C.cpp" />
    <ClCompile Include="Source\j_SendMsg_MissionInfoCDarkHoleChannelQEAAXPEAVCPla_1400058B2.cpp" />
    <ClCompile Include="Source\j_SendMsg_MonsterAggroDataCPlayerQEAAXPEAVCCharact_14000B3E8.cpp" />
    <ClCompile Include="Source\j_SendMsg_MoveErrorCPlayerQEAAXEZ_140009AAC.cpp" />
    <ClCompile Include="Source\j_SendMsg_MoveNextCPlayerQEAAX_NZ_140001951.cpp" />
    <ClCompile Include="Source\j_SendMsg_MovePortalCPlayerQEAAXEEEPEAM_NZ_14000BFE6.cpp" />
    <ClCompile Include="Source\j_SendMsg_MovePortalCPlayerQEAAXEPEAMEZ_140006FD7.cpp" />
    <ClCompile Include="Source\j_SendMsg_MovePortalCReturnGateIEAAXPEAVCPlayerZ_14000AAC4.cpp" />
    <ClCompile Include="Source\j_SendMsg_MoveToOwnStoneMapInformCPlayerQEAAXEZ_14000A470.cpp" />
    <ClCompile Include="Source\j_SendMsg_MoveToOwnStoneMapResultCPlayerQEAAXEEPEA_14000F709.cpp" />
    <ClCompile Include="Source\j_SendMsg_NewMemberCDarkHoleChannelQEAAXPEAVCPlaye_14000AE16.cpp" />
    <ClCompile Include="Source\j_SendMsg_NewMovePotionResultCPlayerQEAAXXZ_140001E6F.cpp" />
    <ClCompile Include="Source\j_SendMsg_NewViewOtherCPlayerQEAAXEZ_14000F010.cpp" />
    <ClCompile Include="Source\j_SendMsg_NotifyEffectForGetItemCPlayerQEAAXEKPEAD_14000B8F7.cpp" />
    <ClCompile Include="Source\j_SendMsg_NotifyGetExpInfoCPlayerQEAAXNNNZ_14000A1F5.cpp" />
    <ClCompile Include="Source\j_SendMsg_Notify_ExceptFromRaceRankingCPlayerQEAAX_1400048D6.cpp" />
    <ClCompile Include="Source\j_SendMsg_Notify_Get_Golden_BoxCPlayerQEAAXEKPEADP_14000E3F4.cpp" />
    <ClCompile Include="Source\j_SendMsg_Notify_Gravity_Stone_Owner_DieCPlayerQEA_14000AE3E.cpp" />
    <ClCompile Include="Source\j_SendMsg_Notify_Me_Get_Golden_BoxCPlayerQEAAXEPEA_140005092.cpp" />
    <ClCompile Include="Source\j_SendMsg_NPCLinkItemCheckResultCPlayerQEAAXEPEAU__14000C0F9.cpp" />
    <ClCompile Include="Source\j_SendMsg_NpcQuestHistoryInformCPlayerQEAAXEZ_140001DCA.cpp" />
    <ClCompile Include="Source\j_SendMsg_NpcQuestListResultCPlayerQEAAXPEAU_NPCQu_14000D323.cpp" />
    <ClCompile Include="Source\j_SendMsg_OfferSuggestResultCPlayerQEAAXEZ_14000F9A2.cpp" />
    <ClCompile Include="Source\j_SendMsg_OffPartResultCPlayerQEAAXEZ_14000DEBD.cpp" />
    <ClCompile Include="Source\j_SendMsg_OreCuttingResultCPlayerQEAAXEEKZ_14000EACA.cpp" />
    <ClCompile Include="Source\j_SendMsg_OreIntoBagResultCPlayerQEAAXEGEKZ_14000989F.cpp" />
    <ClCompile Include="Source\j_SendMsg_OreTransferCountCPlayerQEAAXXZ_140008EC2.cpp" />
    <ClCompile Include="Source\j_SendMsg_OtherShapeAllCPlayerQEAAXPEAV1Z_14000369D.cpp" />
    <ClCompile Include="Source\j_SendMsg_OtherShapeErrorCPlayerQEAAXPEAV1EZ_14000C905.cpp" />
    <ClCompile Include="Source\j_SendMsg_OtherShapePartCPlayerQEAAXPEAV1Z_1400085F8.cpp" />
    <ClCompile Include="Source\j_SendMsg_OtherShapePartEx_CashChangeCPlayerQEAAXP_14000F42F.cpp" />
    <ClCompile Include="Source\j_SendMsg_PartyAlterLootShareResultCPlayerQEAAXEZ_140004561.cpp" />
    <ClCompile Include="Source\j_SendMsg_PartyDisjointResultCPlayerQEAAXEZ_14001101D.cpp" />
    <ClCompile Include="Source\j_SendMsg_PartyJoinApplicationQuestionCPlayerQEAAX_140009A3E.cpp" />
    <ClCompile Include="Source\j_SendMsg_PartyJoinFailLevelCPlayerQEAAXXZ_140006D2A.cpp" />
    <ClCompile Include="Source\j_SendMsg_PartyJoinInvitationQuestionCPlayerQEAAXG_140005948.cpp" />
    <ClCompile Include="Source\j_SendMsg_PartyJoinJoinerResultCPlayerQEAAXXZ_14001103B.cpp" />
    <ClCompile Include="Source\j_SendMsg_PartyJoinMemberResultCPlayerQEAAXPEAVCPa_140010A4B.cpp" />
    <ClCompile Include="Source\j_SendMsg_PartyLeaveCompulsionResultCPlayerQEAAXPE_14000D6BB.cpp" />
    <ClCompile Include="Source\j_SendMsg_PartyLeaveSelfResultCPlayerQEAAXPEAVCPar_14000817F.cpp" />
    <ClCompile Include="Source\j_SendMsg_PartyLockResultCPlayerQEAAXEZ_14000EE3F.cpp" />
    <ClCompile Include="Source\j_SendMsg_PartyLootItemInformCPlayerQEAAXKEGEZ_1400094F8.cpp" />
    <ClCompile Include="Source\j_SendMsg_PartySuccessResultCPlayerQEAAXPEAVCParty_140006E06.cpp" />
    <ClCompile Include="Source\j_SendMsg_PatriarchInformClassOrderProcessorQEAAXP_140007EBE.cpp" />
    <ClCompile Include="Source\j_SendMsg_PcRoomCharClassCPlayerQEAAXKZ_14000D058.cpp" />
    <ClCompile Include="Source\j_SendMsg_PcRoomErrorCPlayerQEAAXEZ_14000483B.cpp" />
    <ClCompile Include="Source\j_SendMsg_PopMemberCDarkHoleChannelQEAAXPEAVCPlaye_14000D1F7.cpp" />
    <ClCompile Include="Source\j_SendMsg_PostContentCPlayerQEAAXEKPEADEG_KKKZ_140009CC3.cpp" />
    <ClCompile Include="Source\j_SendMsg_PostDeleteCPlayerQEAAXEKZ_14000B177.cpp" />
    <ClCompile Include="Source\j_SendMsg_PostDeliveryCPlayerQEAAXEKPEAD0_N1EZ_14000C86A.cpp" />
    <ClCompile Include="Source\j_SendMsg_PostItemGoldCPlayerQEAAXEZ_1400084CC.cpp" />
    <ClCompile Include="Source\j_SendMsg_PostReturnConfirmCPlayerQEAAXEKZ_14000CFEA.cpp" />
    <ClCompile Include="Source\j_SendMsg_PostReturnCPlayerQEAAXEKPEAD00EG_KKKZ_14000DA44.cpp" />
    <ClCompile Include="Source\j_SendMsg_PostSendReplyCPlayerQEAAXEZ_140002784.cpp" />
    <ClCompile Include="Source\j_SendMsg_PotionDelayTimeCPlayerQEAAXPEAKKZ_14000E5F7.cpp" />
    <ClCompile Include="Source\j_SendMsg_PotionDivisionCPlayerQEAAXGEGEHZ_140004845.cpp" />
    <ClCompile Include="Source\j_SendMsg_PotionSeparationCPlayerQEAAXGEGEHZ_1400029D7.cpp" />
    <ClCompile Include="Source\j_SendMsg_PremiumCashItemUseCPlayerQEAAXGZ_140007536.cpp" />
    <ClCompile Include="Source\j_SendMsg_ProposeVoteResultCPlayerQEAAXEZ_14000CA68.cpp" />
    <ClCompile Include="Source\j_SendMsg_PvpRankListVersionUpCPlayerQEAAXEZ_140009D09.cpp" />
    <ClCompile Include="Source\j_SendMsg_QuestCompleteCPlayerQEAAXEZ_14000B050.cpp" />
    <ClCompile Include="Source\j_SendMsg_QuestDownloadResultCPlayerQEAAXXZ_14000EDFE.cpp" />
    <ClCompile Include="Source\j_SendMsg_QuestFailureCPlayerQEAAXEEZ_140001A00.cpp" />
    <ClCompile Include="Source\j_SendMsg_QuestGiveUpResultCPlayerQEAAXEZ_140002B35.cpp" />
    <ClCompile Include="Source\j_SendMsg_QuestHistoryDownloadResultCPlayerQEAAXXZ_14000C2A2.cpp" />
    <ClCompile Include="Source\j_SendMsg_QuestInfoCDarkHoleChannelQEAAXPEAVCPlaye_140006203.cpp" />
    <ClCompile Include="Source\j_SendMsg_QuestProcessCPlayerQEAAXEEGZ_14000BA0F.cpp" />
    <ClCompile Include="Source\j_SendMsg_RaceBattlePeneltyCPlayerQEAAXHEZ_14000F213.cpp" />
    <ClCompile Include="Source\j_SendMsg_RaceBossCryMsgCPlayerQEAAXXZ_1400041D8.cpp" />
    <ClCompile Include="Source\j_SendMsg_RaceChatCGoldenBoxItemMgrQEAAXPEAVCPlaye_1400103AC.cpp" />
    <ClCompile Include="Source\j_SendMsg_RaceTopInformCPlayerQEAAX_NZ_14000268A.cpp" />
    <ClCompile Include="Source\j_SendMsg_RACE_GreetingCPlayerQEAAXPEAD0Z_140009FA7.cpp" />
    <ClCompile Include="Source\j_SendMsg_RadarCharSearchResultCPlayerQEAAXXZ_1400073AB.cpp" />
    <ClCompile Include="Source\j_SendMsg_RadarDelayTimeCPlayerQEAAXKZ_140012F08.cpp" />
    <ClCompile Include="Source\j_SendMsg_RealMovePointCPlayerUEAAXHZ_140003A0D.cpp" />
    <ClCompile Include="Source\j_SendMsg_RecoverCPlayerQEAAXXZ_14000E19C.cpp" />
    <ClCompile Include="Source\j_SendMsg_RecvHSKQuestCPlayerQEAAXXZ_140011EA5.cpp" />
    <ClCompile Include="Source\j_SendMsg_ReEnterAskCPlayerQEAAXGKZ_14000E813.cpp" />
    <ClCompile Include="Source\j_SendMsg_ReEnterDarkHoleResultCPlayerQEAAXEZ_14000C577.cpp" />
    <ClCompile Include="Source\j_SendMsg_RefeshGroupTargetPositionCPlayerQEAAXEZ_14000E007.cpp" />
    <ClCompile Include="Source\j_SendMsg_RegistBindResultCPlayerQEAAXEZ_14000CBCB.cpp" />
    <ClCompile Include="Source\j_SendMsg_ReleaseGroupTargetObjectResultCPlayerQEA_140005C8B.cpp" />
    <ClCompile Include="Source\j_SendMsg_ReleaseSiegeModeResultCPlayerQEAAXEZ_140006FFF.cpp" />
    <ClCompile Include="Source\j_SendMsg_RemainOreRateCPlayerQEAAXXZ_140006E79.cpp" />
    <ClCompile Include="Source\j_SendMsg_RemainTimeInformCPlayerQEAAXFJPEAU_SYSTE_14001105E.cpp" />
    <ClCompile Include="Source\j_SendMsg_ResDivisionCPlayerQEAAXEPEAU_db_con_STOR_140011D15.cpp" />
    <ClCompile Include="Source\j_SendMsg_ResSeparationCPlayerQEAAXEPEAU_db_con_ST_14000531C.cpp" />
    <ClCompile Include="Source\j_SendMsg_ResultChangeTaxRateCPlayerQEAAXEEZ_140005079.cpp" />
    <ClCompile Include="Source\j_SendMsg_ResultNpcQuestCPlayerQEAAX_NZ_14000921E.cpp" />
    <ClCompile Include="Source\j_SendMsg_ResurrectCPlayerQEAAXE_NZ_140010244.cpp" />
    <ClCompile Include="Source\j_SendMsg_ResurrectInformCPlayerQEAAXXZ_14000DED1.cpp" />
    <ClCompile Include="Source\j_SendMsg_RevivalCPlayerQEAAXE_NZ_14000A1F0.cpp" />
    <ClCompile Include="Source\j_SendMsg_RevivalOfJadeCPlayerQEAAXGZ_14000879C.cpp" />
    <ClCompile Include="Source\j_SendMsg_RewardAddItemCPlayerQEAAXPEAU_db_con_STO_14000FBA5.cpp" />
    <ClCompile Include="Source\j_SendMsg_RobedHPCCharacterQEAAXPEBV1GZ_1400136C9.cpp" />
    <ClCompile Include="Source\j_SendMsg_SelectClassResultCPlayerQEAAXEGZ_1400129F9.cpp" />
    <ClCompile Include="Source\j_SendMsg_SelectQuestRewardCPlayerQEAAXEZ_14000BFE1.cpp" />
    <ClCompile Include="Source\j_SendMsg_SelectWaitedQuestCPlayerQEAAXEKEZ_140001906.cpp" />
    <ClCompile Include="Source\j_SendMsg_SellItemStoreResultCPlayerQEAAXPEAVCItem_140006113.cpp" />
    <ClCompile Include="Source\j_SendMsg_SetDPInformCPlayerQEAAXXZ_140003E4A.cpp" />
    <ClCompile Include="Source\j_SendMsg_SetFPInformCPlayerQEAAXXZ_14000F79A.cpp" />
    <ClCompile Include="Source\j_SendMsg_SetGroupMapPointCPlayerQEAAXEEEPEAMEZ_140012855.cpp" />
    <ClCompile Include="Source\j_SendMsg_SetGroupTargetObjectResultCPlayerQEAAXEE_1400070EF.cpp" />
    <ClCompile Include="Source\j_SendMsg_SetHPInformCPlayerUEAAXXZ_140010B13.cpp" />
    <ClCompile Include="Source\j_SendMsg_SetItemCheckResultCPlayerQEAAXEKEZ_14000DE36.cpp" />
    <ClCompile Include="Source\j_SendMsg_SetSPInformCPlayerQEAAXXZ_14000C4E1.cpp" />
    <ClCompile Include="Source\j_SendMsg_SetTargetObjectResultCPlayerQEAAXE_NZ_140001D11.cpp" />
    <ClCompile Include="Source\j_SendMsg_SFDelayRequestCPlayerQEAAXXZ_140007C07.cpp" />
    <ClCompile Include="Source\j_SendMsg_SkillResultCPlayerQEAAXEPEAU_CHRIDEHZ_14000E827.cpp" />
    <ClCompile Include="Source\j_SendMsg_SpecialDownloadResultCPlayerQEAAXXZ_140008148.cpp" />
    <ClCompile Include="Source\j_SendMsg_StartContSFCPlayerQEAAXPEAU_sf_continous_14000D684.cpp" />
    <ClCompile Include="Source\j_SendMsg_StartNewPosCPlayerQEAAXEZ_140011734.cpp" />
    <ClCompile Include="Source\j_SendMsg_StartShoppingCPlayerQEAAXXZ_140002234.cpp" />
    <ClCompile Include="Source\j_SendMsg_StateInformCPlayerQEAAX_KZ_1400035BC.cpp" />
    <ClCompile Include="Source\j_SendMsg_StatInformCPlayerQEAAXEKEZ_1400082D3.cpp" />
    <ClCompile Include="Source\j_SendMsg_StopCPlayerQEAAX_NZ_140001BC7.cpp" />
    <ClCompile Include="Source\j_SendMsg_StoreLimitItemAmountInfoCPlayerQEAAXKPEA_14000E287.cpp" />
    <ClCompile Include="Source\j_SendMsg_StoreListResultCPlayerQEAAXXZ_140007BF3.cpp" />
    <ClCompile Include="Source\j_SendMsg_StunInformCCharacterUEAAXXZ_140006785.cpp" />
    <ClCompile Include="Source\j_SendMsg_TakeAddResultCPlayerQEAAXEPEAU_db_con_ST_140011E69.cpp" />
    <ClCompile Include="Source\j_SendMsg_TakeNewResultCPlayerQEAAXEPEAU_db_con_ST_140007068.cpp" />
    <ClCompile Include="Source\j_SendMsg_TalikCrystalExchangeResultCPlayerQEAAXEE_140006631.cpp" />
    <ClCompile Include="Source\j_SendMsg_TargetObjectHPInformCPlayerQEAAXXZ_140006988.cpp" />
    <ClCompile Include="Source\j_SendMsg_TeleportErrorCPlayerQEAAXEKZ_140007CDE.cpp" />
    <ClCompile Include="Source\j_SendMsg_TestAttackResultCPlayerQEAAXEEGEEPEAFZ_14000795A.cpp" />
    <ClCompile Include="Source\j_SendMsg_ThrowSkillResultCPlayerQEAAXEPEAU_CHRIDE_140012300.cpp" />
    <ClCompile Include="Source\j_SendMsg_ThrowStorageResultCPlayerQEAAXEZ_140005123.cpp" />
    <ClCompile Include="Source\j_SendMsg_ThrowUnitResultCPlayerQEAAXEPEAU_CHRIDGZ_14000DB20.cpp" />
    <ClCompile Include="Source\j_SendMsg_TLStatusInfoCPlayerQEAAXKEZ_140012733.cpp" />
    <ClCompile Include="Source\j_SendMsg_TLStatusPenaltyCPlayerQEAAXEZ_14001117B.cpp" />
    <ClCompile Include="Source\j_SendMsg_TowerContinueCPlayerQEAAXGPEAVCGuardTowe_14000C27F.cpp" />
    <ClCompile Include="Source\j_SendMsg_to_webagent_about_last_attacker_for_keep_140003715.cpp" />
    <ClCompile Include="Source\j_SendMsg_TransformSiegeModeResultCPlayerQEAAXEZ_140007662.cpp" />
    <ClCompile Include="Source\j_SendMsg_TransShipRenewTicketResultCPlayerQEAAXEZ_140006D84.cpp" />
    <ClCompile Include="Source\j_SendMsg_TrunkChangPasswdResultCPlayerQEAAXEZ_140012364.cpp" />
    <ClCompile Include="Source\j_SendMsg_TrunkDownloadResultCPlayerQEAAXEZ_14000AD2B.cpp" />
    <ClCompile Include="Source\j_SendMsg_TrunkEstResultCPlayerQEAAXEKZ_140011B21.cpp" />
    <ClCompile Include="Source\j_SendMsg_TrunkExtendResultCPlayerQEAAXEEKKZ_140008233.cpp" />
    <ClCompile Include="Source\j_SendMsg_TrunkHintAnswerResultCPlayerQEAAXEPEADZ_14000F2A9.cpp" />
    <ClCompile Include="Source\j_SendMsg_TrunkIoMoneyResultCPlayerQEAAXENNKKKZ_1400105F5.cpp" />
    <ClCompile Include="Source\j_SendMsg_TrunkIoResultCPlayerQEAAXEEKKZ_14000C0D6.cpp" />
    <ClCompile Include="Source\j_SendMsg_TrunkPotionDivisionCPlayerQEAAXGGGGHZ_140011BA3.cpp" />
    <ClCompile Include="Source\j_SendMsg_TrunkPwHintIndexResultCPlayerQEAAXEEZ_14000DABC.cpp" />
    <ClCompile Include="Source\j_SendMsg_TrunkResDivisionCPlayerQEAAXEPEAU_db_con_140006A41.cpp" />
    <ClCompile Include="Source\j_SendMsg_UILock_FindPW_ResultCPlayerQEAAXEPEADEZ_1400103A2.cpp" />
    <ClCompile Include="Source\j_SendMsg_UILock_Init_Request_ToAccountCPlayerQEAA_1400069E2.cpp" />
    <ClCompile Include="Source\j_SendMsg_UILock_Init_ResultCPlayerQEAAXEZ_14000A86C.cpp" />
    <ClCompile Include="Source\j_SendMsg_UILock_Login_ResultCPlayerQEAAXEEZ_14000BC3A.cpp" />
    <ClCompile Include="Source\j_SendMsg_UILock_Update_Request_ToAccountCPlayerQE_14000C653.cpp" />
    <ClCompile Include="Source\j_SendMsg_UILock_Update_ResultCPlayerQEAAXEZ_140013B38.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitAlterFeeInformCPlayerQEAAXEKZ_140002770.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitBulletFillResultCPlayerQEAAXEEPEAGPE_14000F5BA.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitBulletReplaceResultCPlayerQEAAXEZ_140009BAB.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitDeliveryResultCPlayerQEAAXEEKKZ_140008521.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitDestroyCPlayerQEAAXEZ_140008F71.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitForceReturnInformCPlayerQEAAXEKZ_14000D6CA.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitFrameBuyResultCPlayerQEAAXEEEGGPEAKZ_1400018CF.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitFrameRepairResultCPlayerQEAAXEEKKZ_140003A5D.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitLeaveResultCPlayerQEAAXEZ_14000C063.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitPackFillResultCPlayerQEAAXEEEPEAU__l_140011A63.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitPartTuningResultCPlayerQEAAXEEPEAHZ_1400124A4.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitReturnResultCPlayerQEAAXEKZ_14000C07C.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitRideChangeCPlayerQEAAX_NPEAVCParking_14000A15F.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitSellResultCPlayerQEAAXEEGHKKKZ_140001B13.cpp" />
    <ClCompile Include="Source\j_SendMsg_UnitTakeResultCPlayerQEAAXEZ_140012116.cpp" />
    <ClCompile Include="Source\j_SendMsg_UpdateTLStatusInfoCPlayerQEAAXKEZ_14000A32B.cpp" />
    <ClCompile Include="Source\j_SendMsg_UseJadeResultCPlayerQEAAXEGZ_140003FC1.cpp" />
    <ClCompile Include="Source\j_SendMsg_UsePotionResultCPlayerQEAAXEGEZ_14000DCC9.cpp" />
    <ClCompile Include="Source\j_SendMsg_UseRadarResultCPlayerQEAAXEGKZ_1400084AE.cpp" />
    <ClCompile Include="Source\j_SendMsg_UsPotionResultOtherCPlayerQEAAXEGPEAV1_N_14000E25A.cpp" />
    <ClCompile Include="Source\j_SendMsg_VoteResultCPlayerQEAAXKEZ_1400082A1.cpp" />
    <ClCompile Include="Source\j_SendOhterNotifyCommitteeMemberPositionCNormalGui_140012D4B.cpp" />
    <ClCompile Include="Source\j_SendRecallReqeustResultCRecallEffectControllerQE_140005641.cpp" />
    <ClCompile Include="Source\j_SendRecallReqeustToDestCRecallEffectControllerIE_140010CB2.cpp" />
    <ClCompile Include="Source\j_SendRepriceErrorResultCUnmannedTraderUserInfoQEA_14000145B.cpp" />
    <ClCompile Include="Source\j_SendRepriceSuccessResultCUnmannedTraderUserInfoQ_14001236E.cpp" />
    <ClCompile Include="Source\j_SendResponseAcceptResultGMCallMgrIEAAXPEAVCPlaye_140013552.cpp" />
    <ClCompile Include="Source\j_SendResponseGMCallGMCallMgrIEAA_NPEAVCPlayerHZ_14000DD91.cpp" />
    <ClCompile Include="Source\j_SendResponseGMListGMCallMgrIEAA_NPEAVCPlayerHZ_140005C81.cpp" />
    <ClCompile Include="Source\j_SendSelfNotifyCommitteeMemberPositionListCNormal_1400031C5.cpp" />
    <ClCompile Include="Source\j_SendStealMsgCChatStealSystemAEAAXPEAVCPlayerEKPE_1400033B4.cpp" />
    <ClCompile Include="Source\j_SendTargetMonsterSFContInfoCPlayerQEAAXXZ_14000F510.cpp" />
    <ClCompile Include="Source\j_SendTargetPlayerDamageContInfoCPlayerQEAAXXZ_14000B04B.cpp" />
    <ClCompile Include="Source\j_Send_ClienInformCChiNetworkEXQEAAXPEAVCPlayerGPE_1400012A3.cpp" />
    <ClCompile Include="Source\j_Send_IPCChiNetworkEXQEAAXPEAVCPlayerZ_14000626C.cpp" />
    <ClCompile Include="Source\j_Send_LoginCChiNetworkEXQEAAXPEAVCPlayerZ_140006B59.cpp" />
    <ClCompile Include="Source\j_Send_LogoutCChiNetworkEXQEAAXPEAVCPlayerZ_140008049.cpp" />
    <ClCompile Include="Source\j_Send_TransCChiNetworkEXQEAAXPEAVCPlayerKZ_1400081D9.cpp" />
    <ClCompile Include="Source\j_SenseStateCPlayerQEAAXXZ_14000E9F8.cpp" />
    <ClCompile Include="Source\j_SetAccumulationCountAddCMonsterSkillQEAAXHZ_1400093D6.cpp" />
    <ClCompile Include="Source\j_SetAggroCMonsterAggroMgrQEAAXPEAVCCharacterHHKHH_140005D67.cpp" />
    <ClCompile Include="Source\j_SetAttackPartCPlayerUEAAXHZ_14000D5E4.cpp" />
    <ClCompile Include="Source\j_SetAttackTargetCMonsterQEAAXPEAVCCharacterZ_14000EB9C.cpp" />
    <ClCompile Include="Source\j_SetBagNumCPlayerDBQEAAXEZ_140007A7C.cpp" />
    <ClCompile Include="Source\j_SetBattleModeCPlayerQEAAX_NZ_140003A08.cpp" />
    <ClCompile Include="Source\j_SetBindDummyCPlayerQEAAXPEAU_dummy_positionZ_14000DCD8.cpp" />
    <ClCompile Include="Source\j_SetBindMapDataCPlayerQEAAXPEAVCMapDataZ_14000FED9.cpp" />
    <ClCompile Include="Source\j_SetBindPositionCPlayerQEAA_NPEAVCMapDataPEAU_dum_140007DDD.cpp" />
    <ClCompile Include="Source\j_SetCAggroNodeQEAAXPEAVCCharacterZ_14000EB8D.cpp" />
    <ClCompile Include="Source\j_SetCashAmountCPlayerQEAAXHZ_14000A0C9.cpp" />
    <ClCompile Include="Source\j_SetClassInGuildCPlayerDBQEAAXEZ_1400078B0.cpp" />
    <ClCompile Include="Source\j_SetCMonsterSkillPoolQEAAHPEAVCMonsterZ_14000B429.cpp" />
    <ClCompile Include="Source\j_SetCntEnableCPlayerQEAAX_NZ_14000DD2D.cpp" />
    <ClCompile Include="Source\j_SetCurPosCPlayerDBQEAAXPEAMZ_140013363.cpp" />
    <ClCompile Include="Source\j_SetDalantCPlayerDBQEAAXKZ_1400081C5.cpp" />
    <ClCompile Include="Source\j_SetDamageAutominePersonalUEAAHHPEAVCCharacterH_N_14000A718.cpp" />
    <ClCompile Include="Source\j_SetDamageCAnimusUEAAHHPEAVCCharacterH_NHK1Z_140008567.cpp" />
    <ClCompile Include="Source\j_SetDamageCGameObjectUEAAHHPEAVCCharacterH_NHK1Z_14000FA56.cpp" />
    <ClCompile Include="Source\j_SetDamageCGuardTowerUEAAHHPEAVCCharacterH_NHK1Z_140010CA3.cpp" />
    <ClCompile Include="Source\j_SetDamageCHolyKeeperUEAAHHPEAVCCharacterH_NHK1Z_140002199.cpp" />
    <ClCompile Include="Source\j_SetDamageCHolyStoneUEAAHHPEAVCCharacterH_NHK1Z_1400085B7.cpp" />
    <ClCompile Include="Source\j_SetDamageCMonsterUEAAHHPEAVCCharacterH_NHK1Z_1400021F3.cpp" />
    <ClCompile Include="Source\j_SetDamageCPlayerUEAAHHPEAVCCharacterH_NHK1Z_140004764.cpp" />
    <ClCompile Include="Source\j_SetDamageCTrapUEAAHHPEAVCCharacterH_NHK1Z_1400071D0.cpp" />
    <ClCompile Include="Source\j_SetDPCPlayerDBQEAAXKZ_14000CFBD.cpp" />
    <ClCompile Include="Source\j_SetDPCPlayerQEAA_NH_NZ_14000FE16.cpp" />
    <ClCompile Include="Source\j_SetEffectEquipCodeCPlayerQEAAXEEEZ_1400126F2.cpp" />
    <ClCompile Include="Source\j_SetEquipEffectCPlayerQEAAXPEAU_storage_con_STORA_14000F16E.cpp" />
    <ClCompile Include="Source\j_SetEquipJadeEffectCPlayerQEAAXHM_NZ_1400060E6.cpp" />
    <ClCompile Include="Source\j_SetExpCPlayerDBQEAAXNZ_14000E57A.cpp" />
    <ClCompile Include="Source\j_SetForceCMonsterSkillQEAAHPEAU_monster_fldPEAU_m_14001181F.cpp" />
    <ClCompile Include="Source\j_SetFPCPlayerDBQEAAXKZ_140010843.cpp" />
    <ClCompile Include="Source\j_SetFPCPlayerQEAA_NH_NZ_140009412.cpp" />
    <ClCompile Include="Source\j_SetGaugeCPlayerQEAAXHH_NZ_140009A8E.cpp" />
    <ClCompile Include="Source\j_SetGenCMonsterSkillQEAAHPEAU_monster_fldHKMKZ_140012F17.cpp" />
    <ClCompile Include="Source\j_SetGetExpCExpInfoCPartyModeKillMonsterExpNotifyQ_14000E4E4.cpp" />
    <ClCompile Include="Source\j_SetGmCChatStealSystemQEAA_NPEAVCPlayerZ_14000251D.cpp" />
    <ClCompile Include="Source\j_SetGoldCPlayerDBQEAAXKZ_140002987.cpp" />
    <ClCompile Include="Source\j_SetGradeCPlayerQEAAXEZ_140007379.cpp" />
    <ClCompile Include="Source\j_SetHaveBoxOfAMPCPlayerDBQEAAX_NZ_1400048C7.cpp" />
    <ClCompile Include="Source\j_SetHaveEffectCPlayerQEAAX_NZ_14000FFD8.cpp" />
    <ClCompile Include="Source\j_SetHaveEffectUseTimeCPlayerQEAAXPEAU_db_con_STOR_14000EAA7.cpp" />
    <ClCompile Include="Source\j_SetHPCPlayerDBQEAAXKZ_14000463D.cpp" />
    <ClCompile Include="Source\j_SetHPCPlayerUEAA_NH_NZ_140012F67.cpp" />
    <ClCompile Include="Source\j_SetKillMonsterFlagCPartyModeKillMonsterExpNotify_140011595.cpp" />
    <ClCompile Include="Source\j_SetLastAttBuffCPlayerQEAAX_NZ_1400132E6.cpp" />
    <ClCompile Include="Source\j_SetLevelCPlayerDBQEAAXHZ_14000BB18.cpp" />
    <ClCompile Include="Source\j_SetLevelCPlayerQEAAXEZ_14000A1AA.cpp" />
    <ClCompile Include="Source\j_SetLevelDCPlayerQEAAXEZ_14000F5D3.cpp" />
    <ClCompile Include="Source\j_SetLockModeCPartyPlayerQEAA_N_NZ_140010109.cpp" />
    <ClCompile Include="Source\j_SetLootShareModeCPartyPlayerQEAA_NEZ_140013B10.cpp" />
    <ClCompile Include="Source\j_SetLossExpCPlayerDBQEAAXNZ_140005EB6.cpp" />
    <ClCompile Include="Source\j_SetMapCodeCPlayerDBQEAAXEZ_1400106E0.cpp" />
    <ClCompile Include="Source\j_SetMaxLevelCPlayerDBQEAAXHZ_14000FFD3.cpp" />
    <ClCompile Include="Source\j_SetMstHaveEffectCPlayerQEAAXPEBU_ResourceItem_fl_14000B118.cpp" />
    <ClCompile Include="Source\j_SetMstPtCPlayerQEAAXHM_NHZ_140008B20.cpp" />
    <ClCompile Include="Source\j_SetNextGenAttTimeCCharacterQEAAXKZ_1400138B8.cpp" />
    <ClCompile Include="Source\j_SetNextLootAuthorCPartyPlayerQEAAXXZ_1400061DB.cpp" />
    <ClCompile Include="Source\j_SetOwnerCGravityStoneQEAAXPEAVCPlayerZ_140003A30.cpp" />
    <ClCompile Include="Source\j_SetPlayerOutCGuildRoomInfoQEAA_NHKHZ_140009485.cpp" />
    <ClCompile Include="Source\j_SetPlayerOutCGuildRoomSystemQEAAHKHKZ_14000143D.cpp" />
    <ClCompile Include="Source\j_SetPlayerStateRFEventBaseUEAA_NQEAXHZ_14000655F.cpp" />
    <ClCompile Include="Source\j_SetPlayerStateRFEvent_ClassRefineUEAA_NQEAXHZ_14000C685.cpp" />
    <ClCompile Include="Source\j_SetPotionActDelayCPlayerQEAAXEKKZ_1400105FA.cpp" />
    <ClCompile Include="Source\j_SetPrivateExponentDL_PrivateKeyImplVDL_GroupPara_140002DDD.cpp" />
    <ClCompile Include="Source\j_SetPvpOrderViewCPvpOrderViewQEAA_NNPEAU_PVP_ORDE_140001136.cpp" />
    <ClCompile Include="Source\j_SetPvPPointCPlayerDBQEAAXNZ_14000CBC1.cpp" />
    <ClCompile Include="Source\j_SetPvpPointLeakCPlayerQEAAXNZ_1400123E6.cpp" />
    <ClCompile Include="Source\j_SetRankRateCPlayerQEAAXGKZ_14000E60B.cpp" />
    <ClCompile Include="Source\j_SetShapeAllBufferCPlayerQEAAXXZ_140007BFD.cpp" />
    <ClCompile Include="Source\j_SetSiegeCPlayerQEAAXPEAU_db_con_STORAGE_LISTZ_140012350.cpp" />
    <ClCompile Include="Source\j_SetSkillCMonsterSkillQEAAHPEAU_monster_fldPEAU_m_14000ABA0.cpp" />
    <ClCompile Include="Source\j_SetSPCPlayerDBQEAAXKZ_140008297.cpp" />
    <ClCompile Include="Source\j_SetSPCPlayerQEAA_NH_NZ_140007B12.cpp" />
    <ClCompile Include="Source\j_SetStateFlagCPlayerQEAAXXZ_140007504.cpp" />
    <ClCompile Include="Source\j_SetStaticMemberCPlayerSAXXZ_140011DA1.cpp" />
    <ClCompile Include="Source\j_SetTargetInfoFromCharacterCChatStealSystemQEAA_N_140011B7B.cpp" />
    <ClCompile Include="Source\j_SetTarPosCCharacterQEAA_NPEAM_NZ_1400042C3.cpp" />
    <ClCompile Include="Source\j_SetTarPosCPlayerQEAA_NPEAM_NZ_1400089BD.cpp" />
    <ClCompile Include="Source\j_SetTopAggroCharacterCMonsterAggroMgrQEAAXPEAVCCh_14000D337.cpp" />
    <ClCompile Include="Source\j_SetUseRadarCRadarItemMgrQEAAXPEAY0EADPEAVCPlayer_140010023.cpp" />
    <ClCompile Include="Source\j_SetUseReleaseRaceBuffPotionCPlayerQEAAXXZ_140005B91.cpp" />
    <ClCompile Include="Source\j_SetVoteCPlayerQEAAXHZ_1400100E6.cpp" />
    <ClCompile Include="Source\j_set_pos_dh_player_mgrQEAAXPEAVCMapDataGPEAMZ_140006703.cpp" />
    <ClCompile Include="Source\j_set_skill_lv_up_dataQEAAXGEZ_14000401B.cpp" />
    <ClCompile Include="Source\j_SFContDelMessageCPlayerUEAAXEE_N0Z_14000DFC1.cpp" />
    <ClCompile Include="Source\j_SFContInitCCharacterQEAAXXZ_1400022C0.cpp" />
    <ClCompile Include="Source\j_SFContInsertMessageCGameObjectUEAAXEE_NPEAVCPlay_14000F77C.cpp" />
    <ClCompile Include="Source\j_SFContInsertMessageCPlayerUEAAXEE_NPEAV1Z_140006B0E.cpp" />
    <ClCompile Include="Source\j_SFContUpdateTimeMessageCPlayerUEAAXEEHZ_140013787.cpp" />
    <ClCompile Include="Source\j_SF_AllContDamageForceRemove_OnceCGameObjectUEAA__14000AAC9.cpp" />
    <ClCompile Include="Source\j_SF_AllContDamageForceRemove_OnceCPlayerUEAA_NPEA_14000E561.cpp" />
    <ClCompile Include="Source\j_SF_AllContDamageRemove_OnceCPlayerQEAA_NPEAVCCha_140006370.cpp" />
    <ClCompile Include="Source\j_SF_AllContHelpForceRemove_OnceCGameObjectUEAA_NP_14000AD9E.cpp" />
    <ClCompile Include="Source\j_SF_AllContHelpForceRemove_OnceCMonsterUEAA_NPEAV_14000F245.cpp" />
    <ClCompile Include="Source\j_SF_AllContHelpForceRemove_OnceCPlayerUEAA_NPEAVC_14000B50F.cpp" />
    <ClCompile Include="Source\j_SF_AllContHelpSkillRemove_OnceCGameObjectUEAA_NP_140005B5A.cpp" />
    <ClCompile Include="Source\j_SF_AllContHelpSkillRemove_OnceCMonsterUEAA_NPEAV_14000EEB2.cpp" />
    <ClCompile Include="Source\j_SF_AllContHelpSkillRemove_OnceCPlayerUEAA_NPEAVC_140013CD7.cpp" />
    <ClCompile Include="Source\j_SF_AttHPtoDstFP_OnceCGameObjectUEAA_NPEAVCCharac_140004D54.cpp" />
    <ClCompile Include="Source\j_SF_AttHPtoDstFP_OnceCPlayerUEAA_NPEAVCCharacterM_1400056FF.cpp" />
    <ClCompile Include="Source\j_SF_ContDamageTimeInc_OnceCGameObjectUEAA_NPEAVCC_14000AF33.cpp" />
    <ClCompile Include="Source\j_SF_ContDamageTimeInc_OnceCPlayerUEAA_NPEAVCChara_14000A907.cpp" />
    <ClCompile Include="Source\j_SF_ContHelpTimeInc_OnceCGameObjectUEAA_NPEAVCCha_1400134EE.cpp" />
    <ClCompile Include="Source\j_SF_ContHelpTimeInc_OnceCPlayerUEAA_NPEAVCCharact_140002B58.cpp" />
    <ClCompile Include="Source\j_SF_ConvertMonsterTargetCGameObjectUEAA_NPEAVCCha_14000CCC5.cpp" />
    <ClCompile Include="Source\j_SF_ConvertMonsterTargetCPlayerUEAA_NPEAVCCharact_140001A50.cpp" />
    <ClCompile Include="Source\j_SF_ConvertTargetDestCGameObjectUEAA_NPEAVCCharac_1400052F9.cpp" />
    <ClCompile Include="Source\j_SF_ConvertTargetDestCPlayerUEAA_NPEAVCCharacterM_140012FDF.cpp" />
    <ClCompile Include="Source\j_SF_DamageAndStunCGameObjectUEAA_NPEAVCCharacterM_14000BACD.cpp" />
    <ClCompile Include="Source\j_SF_DamageAndStunCPlayerUEAA_NPEAVCCharacterMZ_14000BCAD.cpp" />
    <ClCompile Include="Source\j_SF_FPDecCGameObjectUEAA_NPEAVCCharacterMZ_140007531.cpp" />
    <ClCompile Include="Source\j_SF_FPDecCPlayerUEAA_NPEAVCCharacterMZ_1400042D7.cpp" />
    <ClCompile Include="Source\j_SF_HFSInc_OnceCPlayerQEAA_NPEAVCCharacterZ_1400134FD.cpp" />
    <ClCompile Include="Source\j_SF_HPInc_OnceCGameObjectUEAA_NPEAVCCharacterMZ_14000B9F1.cpp" />
    <ClCompile Include="Source\j_SF_HPInc_OnceCMonsterUEAA_NPEAVCCharacterMZ_140002D5B.cpp" />
    <ClCompile Include="Source\j_SF_HPInc_OnceCPlayerUEAA_NPEAVCCharacterMZ_14000BF05.cpp" />
    <ClCompile Include="Source\j_SF_IncHPCirclePartyCGameObjectUEAA_NPEAVCCharact_140002626.cpp" />
    <ClCompile Include="Source\j_SF_IncHPCirclePartyCPlayerUEAA_NPEAVCCharacterMZ_140012C6F.cpp" />
    <ClCompile Include="Source\j_SF_IncreaseDPCGameObjectUEAA_NPEAVCCharacterMZ_14000C513.cpp" />
    <ClCompile Include="Source\j_SF_IncreaseDPCPlayerUEAA_NPEAVCCharacterMZ_140008922.cpp" />
    <ClCompile Include="Source\j_SF_LateContDamageRemove_OnceCGameObjectUEAA_NPEA_140001FE6.cpp" />
    <ClCompile Include="Source\j_SF_LateContDamageRemove_OnceCMonsterUEAA_NPEAVCC_140009A4D.cpp" />
    <ClCompile Include="Source\j_SF_LateContDamageRemove_OnceCPlayerUEAA_NPEAVCCh_14000D409.cpp" />
    <ClCompile Include="Source\j_SF_LateContHelpForceRemove_OnceCGameObjectUEAA_N_14000B406.cpp" />
    <ClCompile Include="Source\j_SF_LateContHelpForceRemove_OnceCMonsterUEAA_NPEA_14000E58E.cpp" />
    <ClCompile Include="Source\j_SF_LateContHelpForceRemove_OnceCPlayerUEAA_NPEAV_140013EDF.cpp" />
    <ClCompile Include="Source\j_SF_LateContHelpSkillRemove_OnceCGameObjectUEAA_N_1400077A7.cpp" />
    <ClCompile Include="Source\j_SF_LateContHelpSkillRemove_OnceCMonsterUEAA_NPEA_14000826F.cpp" />
    <ClCompile Include="Source\j_SF_LateContHelpSkillRemove_OnceCPlayerUEAA_NPEAV_1400052E5.cpp" />
    <ClCompile Include="Source\j_SF_MakePortalReturnBindPositionPartyMemberCGameO_14001284B.cpp" />
    <ClCompile Include="Source\j_SF_MakePortalReturnBindPositionPartyMemberCPlaye_14000507E.cpp" />
    <ClCompile Include="Source\j_SF_MakeZeroAnimusRecallTimeOnceCGameObjectUEAA_N_140013660.cpp" />
    <ClCompile Include="Source\j_SF_MakeZeroAnimusRecallTimeOnceCPlayerUEAA_NPEAV_14000FBA0.cpp" />
    <ClCompile Include="Source\j_SF_OthersContHelpSFRemove_OnceCPlayerUEAA_NMZ_1400098BD.cpp" />
    <ClCompile Include="Source\j_SF_OverHealing_OnceCGameObjectUEAA_NPEAVCCharact_140001A96.cpp" />
    <ClCompile Include="Source\j_SF_OverHealing_OnceCPlayerUEAA_NPEAVCCharacterMZ_14000132F.cpp" />
    <ClCompile Include="Source\j_SF_RecoverAllReturnStateAnimusHPFullCGameObjectU_1400013F2.cpp" />
    <ClCompile Include="Source\j_SF_RecoverAllReturnStateAnimusHPFullCPlayerUEAA__14000EF75.cpp" />
    <ClCompile Include="Source\j_SF_ReleaseMonsterTargetCGameObjectUEAA_NPEAVCCha_140003A58.cpp" />
    <ClCompile Include="Source\j_SF_ReleaseMonsterTargetCPlayerUEAA_NPEAVCCharact_140007185.cpp" />
    <ClCompile Include="Source\j_SF_RemoveAllContHelp_OnceCGameObjectUEAA_NPEAVCC_14000729D.cpp" />
    <ClCompile Include="Source\j_SF_RemoveAllContHelp_OnceCPlayerUEAA_NPEAVCChara_140003643.cpp" />
    <ClCompile Include="Source\j_SF_Resurrect_OnceCGameObjectUEAA_NPEAVCCharacter_14000A484.cpp" />
    <ClCompile Include="Source\j_SF_Resurrect_OnceCPlayerUEAA_NPEAVCCharacterZ_14000E818.cpp" />
    <ClCompile Include="Source\j_SF_ReturnBindPositionCGameObjectUEAA_NPEAVCChara_14000503D.cpp" />
    <ClCompile Include="Source\j_SF_ReturnBindPositionCPlayerUEAA_NPEAVCCharacter_140006311.cpp" />
    <ClCompile Include="Source\j_SF_SelfDestructionCGameObjectUEAA_NPEAVCCharacte_14000BB81.cpp" />
    <ClCompile Include="Source\j_SF_SelfDestructionCPlayerUEAA_NPEAVCCharacterMZ_140004A9D.cpp" />
    <ClCompile Include="Source\j_SF_SkillContHelpTimeInc_OnceCGameObjectUEAA_NPEA_140001D25.cpp" />
    <ClCompile Include="Source\j_SF_SkillContHelpTimeInc_OnceCPlayerUEAA_NPEAVCCh_14000A28B.cpp" />
    <ClCompile Include="Source\j_SF_SPDecCGameObjectUEAA_NPEAVCCharacterMZ_14000A84E.cpp" />
    <ClCompile Include="Source\j_SF_SPDecCPlayerUEAA_NPEAVCCharacterMZ_140009511.cpp" />
    <ClCompile Include="Source\j_SF_STInc_OnceCGameObjectUEAA_NPEAVCCharacterMZ_14000C987.cpp" />
    <ClCompile Include="Source\j_SF_STInc_OnceCPlayerUEAA_NPEAVCCharacterMZ_140006A73.cpp" />
    <ClCompile Include="Source\j_SF_StunCGameObjectUEAA_NPEAVCCharacterMZ_140010EAB.cpp" />
    <ClCompile Include="Source\j_SF_StunCPlayerUEAA_NPEAVCCharacterMZ_140006C58.cpp" />
    <ClCompile Include="Source\j_SF_TeleportToDestinationCGameObjectUEAA_NPEAVCCh_140001483.cpp" />
    <ClCompile Include="Source\j_SF_TeleportToDestinationCPlayerUEAA_NPEAVCCharac_14000CC6B.cpp" />
    <ClCompile Include="Source\j_SF_TransDestHPCGameObjectUEAA_NPEAVCCharacterMAE_140001EF1.cpp" />
    <ClCompile Include="Source\j_SF_TransDestHPCPlayerUEAA_NPEAVCCharacterMAEAEZ_14000F916.cpp" />
    <ClCompile Include="Source\j_SF_TransMonsterHPCGameObjectUEAA_NPEAVCCharacter_140008062.cpp" />
    <ClCompile Include="Source\j_SF_TransMonsterHPCPlayerUEAA_NPEAVCCharacterMZ_1400041C9.cpp" />
    <ClCompile Include="Source\j_show_to_allCGuildMasterEffectAEAAXPEAVCPlayerEEE_14000ACC7.cpp" />
    <ClCompile Include="Source\j_size_attack_skill_result_zoclQEAAHXZ_140008887.cpp" />
    <ClCompile Include="Source\j_size_character_disconnect_result_wracQEAAHXZ_140004CDC.cpp" />
    <ClCompile Include="Source\j_size_nuclear_bomb_explosion_result_zoclQEAAHXZ_14000616D.cpp" />
    <ClCompile Include="Source\j_size_nuclear_explosion_success_zoclQEAAHXZ_14000E039.cpp" />
    <ClCompile Include="Source\j_size_qry_case_character_renameQEAAHXZ_140010FB4.cpp" />
    <ClCompile Include="Source\j_size_qry_case_cheat_player_vote_infoQEAAHXZ_1400023FB.cpp" />
    <ClCompile Include="Source\j_size_qry_case_update_player_vote_infoQEAAHXZ_140004430.cpp" />
    <ClCompile Include="Source\j_size_target_player_damage_contsf_allinform_zoclQ_140011AF4.cpp" />
    <ClCompile Include="Source\j_SkillRecallTeleportRequestCNetworkEXAEAA_NHPEADZ_140008936.cpp" />
    <ClCompile Include="Source\j_SkillRequestCNetworkEXAEAA_NHPEADZ_1400010EB.cpp" />
    <ClCompile Include="Source\j_skill_processCPlayerQEAAEHHPEAU_CHRIDPEAGPEAHZ_14000B974.cpp" />
    <ClCompile Include="Source\j_skill_process_for_auraCPlayerQEAAXHZ_14001321E.cpp" />
    <ClCompile Include="Source\j_skill_process_for_itemCPlayerQEAAEHPEAU_CHRIDPEA_140002F27.cpp" />
    <ClCompile Include="Source\j_SortPostCPlayerQEAAXHZ_14000B89D.cpp" />
    <ClCompile Include="Source\j_StartCGuildBattleControllerQEAAEPEAVCPlayerZ_1400076BC.cpp" />
    <ClCompile Include="Source\j_StartCNormalGuildBattleFieldGUILD_BATTLEQEAA_NEP_140002329.cpp" />
    <ClCompile Include="Source\j_StartCNormalGuildBattleGUILD_BATTLEQEAAEPEAVCPla_140009458.cpp" />
    <ClCompile Include="Source\j_StartCNormalGuildBattleManagerGUILD_BATTLEQEAAEP_14000E5AC.cpp" />
    <ClCompile Include="Source\j_StarterBox_InsertToInvenCGoldenBoxItemMgrQEAA_NP_14000E0A2.cpp" />
    <ClCompile Include="Source\j_StartEventSetCMonsterEventSetQEAA_NPEAD0PEAVCPla_140010DCF.cpp" />
    <ClCompile Include="Source\j_StealChatMsgCChatStealSystemQEAAXPEAVCPlayerEPEA_14000B997.cpp" />
    <ClCompile Include="Source\j_StopCCharacterQEAAXXZ_14000387D.cpp" />
    <ClCompile Include="Source\j_SubActPointCPlayerQEAAXEKZ_14000ADB7.cpp" />
    <ClCompile Include="Source\j_SubCompleteBuyFindBuyerCUnmannedTraderUserInfoTa_1400051AF.cpp" />
    <ClCompile Include="Source\j_SubCompleteBuyProcBuyCUnmannedTraderUserInfoTabl_14000EAF7.cpp" />
    <ClCompile Include="Source\j_SubDalantCPlayerQEAAXKZ_140001DFC.cpp" />
    <ClCompile Include="Source\j_SubGoldCPlayerQEAAXKZ_14000D4AE.cpp" />
    <ClCompile Include="Source\j_SubPointCPlayerQEAAXKZ_140003026.cpp" />
    <ClCompile Include="Source\j_SubTrunkDalantCPlayerDBQEAAXKZ_1400062E9.cpp" />
    <ClCompile Include="Source\j_SubTrunkGoldCPlayerDBQEAAXKZ_140008A17.cpp" />
    <ClCompile Include="Source\j_SumMinuteBetweenCPlayerQEAAKPEAU_SYSTEMTIME0Z_140010168.cpp" />
    <ClCompile Include="Source\j_SumMinuteOneCPlayerQEAAKPEAU_SYSTEMTIMEZ_14000FAD3.cpp" />
    <ClCompile Include="Source\j_TakeBallCNormalGuildBattleFieldGUILD_BATTLEQEAAE_14000EE5D.cpp" />
    <ClCompile Include="Source\j_TakeGravityStoneCPlayerQEAAXXZ_140007CC5.cpp" />
    <ClCompile Include="Source\j_TakePvpPointCPvpPointLimiterQEAA_NAEANPEAVCPlaye_140002581.cpp" />
    <ClCompile Include="Source\j_ThrowSkillRequestCNetworkEXAEAA_NHPEADZ_140013CFA.cpp" />
    <ClCompile Include="Source\j_TickettingCTransportShipQEAA_NPEAVCPlayerZ_140010CD5.cpp" />
    <ClCompile Include="Source\j_UpdateAuraSFContCPlayerQEAAXXZ_14000CCF2.cpp" />
    <ClCompile Include="Source\j_UpdateChaosModeStateCPlayerAEAAXKZ_14000DB57.cpp" />
    <ClCompile Include="Source\j_UpdateDelPostCPlayerQEAA_NKHZ_14000942B.cpp" />
    <ClCompile Include="Source\j_UpdatedMasteryWriteHistoryCPlayerQEAAXXZ_14000CF95.cpp" />
    <ClCompile Include="Source\j_UpdateLastCriTicketCPlayerQEAAXGEEEEZ_1400120EE.cpp" />
    <ClCompile Include="Source\j_UpdateLastMetalTicketCPlayerQEAAXGEEEEZ_140007649.cpp" />
    <ClCompile Include="Source\j_UpdatePlayerStatusTimeLimitMgrQEAA_NGKEZ_14000D64D.cpp" />
    <ClCompile Include="Source\j_UpdatePostAddLogCPlayerQEAAXK_NHZ_14000478C.cpp" />
    <ClCompile Include="Source\j_UpdatePostCPlayerQEAAXKZ_14000CD1F.cpp" />
    <ClCompile Include="Source\j_UpdatePotionContEffectCPotionMgrQEAAXPEAVCPlayer_140005DF3.cpp" />
    <ClCompile Include="Source\j_UpdatePvpOrderViewCPlayerQEAAX_JZ_14000EFD4.cpp" />
    <ClCompile Include="Source\j_UpdatePvpPointLimiterCPlayerQEAAX_JZ_1400111DA.cpp" />
    <ClCompile Include="Source\j_UpdateReturnPostCPlayerQEAAXKZ_1400036BB.cpp" />
    <ClCompile Include="Source\j_UpdateSFContCCharacterUEAAXXZ_140005100.cpp" />
    <ClCompile Include="Source\j_UpdateVisualVerCPlayerQEAAXTCashChangeStateFlag1_140013AD4.cpp" />
    <ClCompile Include="Source\j_Update_CharacterDataCRFWorldDatabaseQEAA_NKPEAU__14000B90B.cpp" />
    <ClCompile Include="Source\j_Update_CharacterReNameCRFWorldDatabaseQEAA_NPEAD_140013408.cpp" />
    <ClCompile Include="Source\j_Update_ExpCUserDBQEAA_NNZ_14000B640.cpp" />
    <ClCompile Include="Source\j_Update_GoldPointCPlayerQEAA_NKZ_140010D16.cpp" />
    <ClCompile Include="Source\j_Update_LevelCRFWorldDatabaseQEAA_NKEZ_140007C89.cpp" />
    <ClCompile Include="Source\j_Update_LevelCUserDBQEAA_NENZ_14000E05C.cpp" />
    <ClCompile Include="Source\j_Update_LossExpCUserDBQEAA_NNZ_14000981D.cpp" />
    <ClCompile Include="Source\j_Update_MaxLevelCUserDBQEAAXEZ_14000D56C.cpp" />
    <ClCompile Include="Source\j_Update_Player_TimeLimit_InfoCRFWorldDatabaseQEAA_140003049.cpp" />
    <ClCompile Include="Source\j_Update_Player_Vote_InfoCRFWorldDatabaseQEAA_NKKE_140006B9A.cpp" />
    <ClCompile Include="Source\j_UseBuffPotionCExtPotionBufQEAAXPEAVCPlayerZ_140005830.cpp" />
    <ClCompile Include="Source\j_UseCMonsterSkillQEAAHK_NZ_14000CD06.cpp" />
    <ClCompile Include="Source\j_UseDiscountCouponCashItemRemoteStoreQEAA_NPEAU_p_14000A92A.cpp" />
    <ClCompile Include="Source\j_UsePotionCPotionMgrQEAAHPEAVCPlayerPEAVCCharacte_14000EBB5.cpp" />
    <ClCompile Include="Source\j_UseRecoverLossExpItemRequestCNetworkEXAEAA_NHPEA_1400099B2.cpp" />
    <ClCompile Include="Source\j_UseSkill_TargetDfAIMgrSAHPEAVCMonsterPEAVCCharac_140009A34.cpp" />
    <ClCompile Include="Source\j_WeaponSFActiveCPlayerQEAA_NPEAU_be_damaged_charP_14001127F.cpp" />
    <ClCompile Include="Source\j_WPActiveAttackForceCPlayerAttackQEAAXPEAU_attack_14000498F.cpp" />
    <ClCompile Include="Source\j_WPActiveAttackSkillCPlayerAttackQEAAXPEAU_attack_14000DC60.cpp" />
    <ClCompile Include="Source\j_WPActiveForceCPlayerQEAA_NPEAU_be_damaged_charHP_14000E075.cpp" />
    <ClCompile Include="Source\j_WPActiveSkillCPlayerQEAA_NPEAU_be_damaged_charHP_14000A9D4.cpp" />
    <ClCompile Include="Source\j_WriteCheatLogYAXPEADPEAVCPlayerZ_140004CAA.cpp" />
    <ClCompile Include="Source\j_WriteEventCouponLogCGoldenBoxItemMgrQEAAXPEAVCPl_140010C62.cpp" />
    <ClCompile Include="Source\j_WriteGetGoldBarLogCGoldenBoxItemMgrQEAAXPEAVCPla_140011982.cpp" />
    <ClCompile Include="Source\j_WriteLog_LevelCUserDBQEAAXEZ_14000B541.cpp" />
    <ClCompile Include="Source\j__AddToPacketCandidateRegisterAEAA_NPEAVCPlayerKZ_14000533F.cpp" />
    <ClCompile Include="Source\j__AnimusReturnCPlayerQEAAXEZ_140003521.cpp" />
    <ClCompile Include="Source\j__AssistSF_Cont_DmgCMonsterIEAAHPEAVCCharacterPEA_140003E6D.cpp" />
    <ClCompile Include="Source\j__AssistSF_Cont_SupportCMonsterIEAAHPEAVCCharacte_14001118A.cpp" />
    <ClCompile Include="Source\j__AssistSF_Cont_TempCMonsterIEAAHPEAVCCharacterPE_140012BE3.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_itemCashItemRemoteStoreAEA_14001016D.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_item_additional_processCas_140006D8E.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_item_calc_priceCashItemRem_14000DE45.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_item_calc_price_couponCash_140001834.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_item_check_itemCashItemRem_140010B45.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_item_give_itemCashItemRemo_1400063E8.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_item_proc_completeCashItem_140004CCD.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_item_proc_priceCashItemRem_140003B39.cpp" />
    <ClCompile Include="Source\j__buybygold_check_couponCashItemRemoteStoreAEAAAW_140013624.cpp" />
    <ClCompile Include="Source\j__buybygold_check_validCashItemRemoteStoreAEAAAW4_140011829.cpp" />
    <ClCompile Include="Source\j__buybygold_completeCashItemRemoteStoreAEAAXPEAVC_140001FCD.cpp" />
    <ClCompile Include="Source\j__buybygold_set_cashitem_dblog_sheetCashItemRemot_140009A61.cpp" />
    <ClCompile Include="Source\j__CalcMaxFPCPlayerQEAAHXZ_14000C2D9.cpp" />
    <ClCompile Include="Source\j__CalcMaxHPCPlayerQEAAHXZ_14000C8D3.cpp" />
    <ClCompile Include="Source\j__CalcMaxSPCPlayerQEAAHXZ_14000B9D3.cpp" />
    <ClCompile Include="Source\j__CalcMonSkillAttPntCMonsterAttackIEAAHXZ_14000B5CD.cpp" />
    <ClCompile Include="Source\j__CalcSkillAttPntCPlayerAttackAEAAH_NZ_14000979B.cpp" />
    <ClCompile Include="Source\j__CheckForcePullUnitCPlayerQEAAXXZ_140013412.cpp" />
    <ClCompile Include="Source\j__CheckPlayerInfoCandidateRegisterAEAAHPEAVCPlaye_140012954.cpp" />
    <ClCompile Include="Source\j__CheckPotionDataYA_NAEAU_CheckEffectCode_CheckPo_140002A68.cpp" />
    <ClCompile Include="Source\j__CheckUserInfoClassOrderProcessorAEAAHEEPEAVCPla_14000DF53.cpp" />
    <ClCompile Include="Source\j__check_dst_param_after_attackCPlayerQEAAXHPEAVCC_1400075C7.cpp" />
    <ClCompile Include="Source\j__check_embel_partCPlayerQEAA_NPEAU_db_con_STORAG_14000BBB8.cpp" />
    <ClCompile Include="Source\j__check_equipmastery_limCPlayerQEAAHHZ_14000468D.cpp" />
    <ClCompile Include="Source\j__check_equip_partCPlayerQEAA_NPEAU_db_con_STORAG_14000FF79.cpp" />
    <ClCompile Include="Source\j__check_exp_after_attackCPlayerQEAAHHPEAU_be_dama_140012481.cpp" />
    <ClCompile Include="Source\j__check_guild_target_objectCPlayerQEAAXXZ_14000A015.cpp" />
    <ClCompile Include="Source\j__check_hp_send_partyCPlayerQEAAXXZ_1400137AA.cpp" />
    <ClCompile Include="Source\j__check_mastery_cum_limCPlayerQEAAKEEZ_140007D29.cpp" />
    <ClCompile Include="Source\j__check_mastery_limCPlayerQEAAKEEZ_140007AAE.cpp" />
    <ClCompile Include="Source\j__check_party_target_objectCPlayerQEAAXXZ_1400110D6.cpp" />
    <ClCompile Include="Source\j__check_race_target_objectCPlayerQEAAXXZ_140010B5E.cpp" />
    <ClCompile Include="Source\j__check_target_objectCPlayerQEAAXXZ_14000FBB4.cpp" />
    <ClCompile Include="Source\j__DeleteUnitKeyCPlayerQEAAGEZ_14000412E.cpp" />
    <ClCompile Include="Source\j__delete_from_invenCashDbWorkerAEAAXQEAVCPlayerPE_140012CF1.cpp" />
    <ClCompile Include="Source\j__FailItemShortBufferYA_NPEAHEQEAU_material_ItemC_1400113A1.cpp" />
    <ClCompile Include="Source\j__GCCharacterUEAAPEAXIZ_0_14000C257.cpp" />
    <ClCompile Include="Source\j__GCCharacterUEAAPEAXIZ_140009EEE.cpp" />
    <ClCompile Include="Source\j__GCPlayerUEAAPEAXIZ_0_14001033E.cpp" />
    <ClCompile Include="Source\j__GCPlayerUEAAPEAXIZ_14000EF4D.cpp" />
    <ClCompile Include="Source\j__GcStaticMember_PlayerAEAAPEAXIZ_140002095.cpp" />
    <ClCompile Include="Source\j__GetAreaEffectMemberCCharacterQEAAHPEAV1_NHPEAMP_140002FD6.cpp" />
    <ClCompile Include="Source\j__GetFlashEffectMemberCCharacterQEAAHPEAV1_NHH0PE_140008E13.cpp" />
    <ClCompile Include="Source\j__GetItemEffectCPlayerQEAAPEAU_ITEM_EFFECTPEAU_db_140012909.cpp" />
    <ClCompile Include="Source\j__GetPartyEffectMemberCCharacterQEAAHPEAV1_NPEAPE_140001FDC.cpp" />
    <ClCompile Include="Source\j__GetPartyMemberInCircleCPlayerQEAAEPEAPEAV1H_NZ_140013200.cpp" />
    <ClCompile Include="Source\j__GetTempEffectValueYA_NPEAU_skill_fldHAEAMZ_140004AC0.cpp" />
    <ClCompile Include="Source\j__get_playerCashDbWorkerAEAAPEAVCPlayerGKZ_1400036E3.cpp" />
    <ClCompile Include="Source\j__insert_to_invenCashDbWorkerAEAA_NQEAVCPlayerPEA_1400100CD.cpp" />
    <ClCompile Include="Source\j__insert_to_invenCGoldenBoxItemMgrQEAA_NQEAVCPlay_140003FC6.cpp" />
    <ClCompile Include="Source\j__LockUnitKeyCPlayerQEAA_NE_NZ_140003F44.cpp" />
    <ClCompile Include="Source\j__LootItem_EventSetCMonsterQEAA_NPEAVCPlayerZ_1400079CD.cpp" />
    <ClCompile Include="Source\j__LootItem_QstCMonsterQEAA_NPEAVCPlayerZ_14000859E.cpp" />
    <ClCompile Include="Source\j__LootItem_RwpCMonsterQEAA_NPEAVCPlayerZ_1400049E4.cpp" />
    <ClCompile Include="Source\j__LootItem_StdCMonsterQEAA_NPEAVCPlayerZ_1400010E6.cpp" />
    <ClCompile Include="Source\j__pre_check_force_attackCPlayerQEAAHPEAVCCharacte_140007F45.cpp" />
    <ClCompile Include="Source\j__pre_check_in_guild_battleCPlayerQEAAHPEAVCChara_140007D83.cpp" />
    <ClCompile Include="Source\j__pre_check_in_guild_battle_raceCPlayerQEAA_NPEAV_140009AED.cpp" />
    <ClCompile Include="Source\j__pre_check_normal_attackCPlayerQEAAHPEAVCCharact_140003459.cpp" />
    <ClCompile Include="Source\j__pre_check_siege_attackCPlayerQEAAHPEAVCCharacte_140010780.cpp" />
    <ClCompile Include="Source\j__pre_check_skill_attackCPlayerQEAAHPEAVCCharacte_14000E7AF.cpp" />
    <ClCompile Include="Source\j__pre_check_skill_enableCPlayerQEAA_NPEAU_skill_f_140008391.cpp" />
    <ClCompile Include="Source\j__pre_check_skill_gradelimitCPlayerQEAA_NPEAU_ski_1400030EE.cpp" />
    <ClCompile Include="Source\j__pre_check_unit_attackCPlayerQEAAHPEAVCCharacter_14000D8FA.cpp" />
    <ClCompile Include="Source\j__pre_check_wpactive_force_attackCPlayerQEAA_NXZ_14000B7E4.cpp" />
    <ClCompile Include="Source\j__pre_check_wpactive_skill_attackCPlayerQEAA_NEPE_14000C054.cpp" />
    <ClCompile Include="Source\j__QueryAppointClassOrderProcessorAEAAHPEAVCPlayer_1400072DE.cpp" />
    <ClCompile Include="Source\j__RegistCandidateRegisterAEAAHPEAVCPlayerPEADZ_14000C964.cpp" />
    <ClCompile Include="Source\j__ReqNetFinalDecisionFinalDecisionProcessorAEAAXP_140008030.cpp" />
    <ClCompile Include="Source\j__RequestAppointClassOrderProcessorAEAAHPEAVCPlay_14000ED27.cpp" />
    <ClCompile Include="Source\j__RequestDischargeClassOrderProcessorAEAAHPEAVCPl_1400053E4.cpp" />
    <ClCompile Include="Source\j__ResetCirclePlayerCGameObjectQEAAXXZ_140006401.cpp" />
    <ClCompile Include="Source\j__ResponseAppointClassOrderProcessorAEAAHPEAVCPla_14000969C.cpp" />
    <ClCompile Include="Source\j__Reward_QuestCPlayerQEAAPEAU_Quest_fldPEAU2EZ_140008B48.cpp" />
    <ClCompile Include="Source\j__SearchAggroNodeCMonsterAggroMgrIEAAPEAUCAggroNo_1400105D2.cpp" />
    <ClCompile Include="Source\j__SearchPlayerYAKPEADZ_1400058B7.cpp" />
    <ClCompile Include="Source\j__SendVotePaperVoterAEAAHPEAVCPlayerZ_140013165.cpp" />
    <ClCompile Include="Source\j__SendVoteScoreVoterAEAAXPEAVCPlayerZ_1400024B9.cpp" />
    <ClCompile Include="Source\j__SetExpendLtdWriterAEAAXPEADPEAU_LTD_EXPENDZ_1400087D8.cpp" />
    <ClCompile Include="Source\j__set_db_sf_effectCPlayerQEAAXPEAU_SFCONT_DB_BASE_140013CCD.cpp" />
    <ClCompile Include="Source\j__set_sf_contCCharacterQEAAXPEAU_sf_continousEGEK_1400106F4.cpp" />
    <ClCompile Include="Source\j__TowerAllReturnCPlayerQEAAXE_NZ_140002446.cpp" />
    <ClCompile Include="Source\j__TowerDestroyCPlayerQEAAXPEAVCGuardTowerZ_14000D166.cpp" />
    <ClCompile Include="Source\j__TowerReturnCPlayerQEAAGPEAU_db_con_STORAGE_LIST_14000BCC6.cpp" />
    <ClCompile Include="Source\j__TrapDestroyCPlayerQEAAXPEAVCTrapEZ_140009674.cpp" />
    <ClCompile Include="Source\j__TrapReturnCPlayerQEAAXPEAVCTrapGZ_1400060FF.cpp" />
    <ClCompile Include="Source\j__UnitDestroyCPlayerQEAAXEZ_1400056D2.cpp" />
    <ClCompile Include="Source\j__UpdateRateSendToAllPlayerYAXXZ_1400071C1.cpp" />
    <ClCompile Include="Source\j__UpdateUnitDebtCPlayerQEAAXEKZ_140007324.cpp" />
    <ClCompile Include="Source\j__VoteVoterAEAAHPEAVCPlayerPEADZ_140005F83.cpp" />
    <ClCompile Include="Source\j___destroy_itemYA_NPEAVCPlayerPEAU__item_combine__140006A82.cpp" />
    <ClCompile Include="Source\j___make_itemYAEPEAVCPlayerPEAU__item_combine_ex_i_14000D7E7.cpp" />
    <ClCompile Include="Source\KillCGuildBattleControllerQEAAXPEAVCPlayer0Z_1403D6600.cpp" />
    <ClCompile Include="Source\ldexp_0_140676D1A.cpp" />
    <ClCompile Include="Source\LeaveGuildCGuildBattleControllerQEAAXPEAVCPlayerZ_1403D66D0.cpp" />
    <ClCompile Include="Source\LeaveGuildCNormalGuildBattleGUILD_BATTLEQEAAEPEAVC_1403E6070.cpp" />
    <ClCompile Include="Source\LeaveGuildCNormalGuildBattleManagerGUILD_BATTLEQEA_1403D4C60.cpp" />
    <ClCompile Include="Source\LimLvNpcQuestDeleteCPlayerQEAAXEZ_1400CB810.cpp" />
    <ClCompile Include="Source\LoadCMoveMapLimitInfoListQEAAXPEAVCPlayerPEAVCMove_1403A58F0.cpp" />
    <ClCompile Include="Source\LoadCMoveMapLimitInfoPortalUEAAXPEAVCPlayerPEAVCMo_1403A42B0.cpp" />
    <ClCompile Include="Source\LoadCMoveMapLimitInfoUEAAXPEAVCPlayerPEAVCMoveMapL_1403A6EE0.cpp" />
    <ClCompile Include="Source\LoadCMoveMapLimitManagerQEAAXPEAVCPlayerZ_1403A1830.cpp" />
    <ClCompile Include="Source\LoadCMoveMapLimitRightInfoListQEAAXPEAVCPlayerZ_1403ADC10.cpp" />
    <ClCompile Include="Source\LoadCMoveMapLimitRightInfoQEAAXPEAVCPlayerZ_1403ACD10.cpp" />
    <ClCompile Include="Source\LoadCMoveMapLimitRightPortalUEAAXPEAVCPlayerZ_1403AC860.cpp" />
    <ClCompile Include="Source\LoadCMoveMapLimitRightUEAAXPEAVCPlayerZ_1403AE4B0.cpp" />
    <ClCompile Include="Source\LoadCPlayerQEAA_NPEAVCUserDB_NZ_1400489B0.cpp" />
    <ClCompile Include="Source\LoadINISubProcLoadCodeCRaceBuffInfoByHolyQuestCA_N_1403B44B0.cpp" />
    <ClCompile Include="Source\LoadLevelCLevelQEAAXPEADZ_1404DFD00.cpp" />
    <ClCompile Include="Source\loadLimitExpDatacStaticMember_PlayerAEAA_NXZ_14010E660.cpp" />
    <ClCompile Include="Source\LoadXMLCUnmannedTraderSubClassInfoLevelUEAA_NPEAVT_1403841B0.cpp" />
    <ClCompile Include="Source\LogInCGuildBattleControllerQEAAXPEAVCPlayerZ_1403D5E40.cpp" />
    <ClCompile Include="Source\LogInCMoveMapLimitInfoListQEAAXPEAVCPlayerPEAVCMov_1403A5B20.cpp" />
    <ClCompile Include="Source\LogInCMoveMapLimitInfoUEAAXPEAVCPlayerPEAVCMoveMap_1403A6F00.cpp" />
    <ClCompile Include="Source\LogInCMoveMapLimitManagerQEAAXPEAVCPlayerZ_1403A18A0.cpp" />
    <ClCompile Include="Source\LogInCMoveMapLimitRightInfoListQEAAXPEAVCPlayerZ_1403ADCA0.cpp" />
    <ClCompile Include="Source\LogInCMoveMapLimitRightInfoQEAAXPEAVCPlayerZ_1403ACF30.cpp" />
    <ClCompile Include="Source\LogInCMoveMapLimitRightUEAAXPEAVCPlayerZ_1403AE4C0.cpp" />
    <ClCompile Include="Source\LoginMemberCGuildQEAAPEAU_guild_member_infoKPEAVCP_140253750.cpp" />
    <ClCompile Include="Source\LogOutCMoveMapLimitInfoListQEAAXPEAVCPlayerPEAVCMo_1403A5D50.cpp" />
    <ClCompile Include="Source\LogOutCMoveMapLimitInfoUEAAXPEAVCPlayerPEAVCMoveMa_1403A6F20.cpp" />
    <ClCompile Include="Source\LogOutCMoveMapLimitManagerQEAAXPEAVCPlayerZ_1403A1960.cpp" />
    <ClCompile Include="Source\LogOutCMoveMapLimitRightInfoListQEAAXPEAVCPlayerZ_1403ADDC0.cpp" />
    <ClCompile Include="Source\LogOutCMoveMapLimitRightInfoQEAAXPEAVCPlayerZ_1403AD370.cpp" />
    <ClCompile Include="Source\LogOutCMoveMapLimitRightPortalUEAAXPEAVCPlayerZ_1403AC940.cpp" />
    <ClCompile Include="Source\LogOutCMoveMapLimitRightUEAAXPEAVCPlayerZ_1403AE4E0.cpp" />
    <ClCompile Include="Source\LoopCPlayerUEAAXXZ_1400541A0.cpp" />
    <ClCompile Include="Source\luaK_exp2anyreg_140548280.cpp" />
    <ClCompile Include="Source\luaK_exp2nextreg_140548230.cpp" />
    <ClCompile Include="Source\luaK_exp2RK_140548310.cpp" />
    <ClCompile Include="Source\luaK_exp2val_1405482F0.cpp" />
    <ClCompile Include="Source\make_force_attack_paramCMonsterQEAAXPEAVCCharacter_14014E0B0.cpp" />
    <ClCompile Include="Source\make_force_attack_paramCPlayerQEAAXPEAVCCharacterP_140088EC0.cpp" />
    <ClCompile Include="Source\make_gen_attack_paramCAnimusQEAAXPEAVCCharacterEPE_14012AA70.cpp" />
    <ClCompile Include="Source\make_gen_attack_paramCMonsterQEAAXPEAVCCharacterPE_14014DE80.cpp" />
    <ClCompile Include="Source\make_gen_attack_paramCPlayerQEAAXPEAVCCharacterEPE_140087F40.cpp" />
    <ClCompile Include="Source\make_heapV_Vector_iteratorUBaseAndExponentUEC2NPoi_1405A1040.cpp" />
    <ClCompile Include="Source\make_heapV_Vector_iteratorUBaseAndExponentUECPPoin_1405A1540.cpp" />
    <ClCompile Include="Source\make_heapV_Vector_iteratorUBaseAndExponentVInteger_1405A0B10.cpp" />
    <ClCompile Include="Source\make_siege_attack_paramCPlayerQEAAXPEAVCCharacterP_1400893F0.cpp" />
    <ClCompile Include="Source\make_skill_attack_paramCMonsterQEAA_NPEAVCCharacte_14014E260.cpp" />
    <ClCompile Include="Source\make_skill_attack_paramCPlayerQEAAXPEAVCCharacterP_140088340.cpp" />
    <ClCompile Include="Source\make_unit_attack_paramCPlayerQEAAXPEAVCCharacterPE_140089230.cpp" />
    <ClCompile Include="Source\make_wpactive_force_attack_paramCPlayerQEAAXPEAVCC_14008AE80.cpp" />
    <ClCompile Include="Source\make_wpactive_skill_attack_paramCPlayerQEAAXPEAVCC_14008B0A0.cpp" />
    <ClCompile Include="Source\ManageExpulseMemberCGuildQEAAEKZ_140258DB0.cpp" />
    <ClCompile Include="Source\MasterAttack_MasterInformCAnimusQEAAXPEAVCCharacte_140129170.cpp" />
    <ClCompile Include="Source\MasterBeAttacked_MasterInformCAnimusQEAAXPEAVCChar_1401291B0.cpp" />
    <ClCompile Include="Source\MasterReStartCTrapQEAAXPEAVCPlayerZ_14013EFD0.cpp" />
    <ClCompile Include="Source\max_sizeallocatorUBaseAndExponentUEC2NPointCryptoP_1405973D0.cpp" />
    <ClCompile Include="Source\max_sizeallocatorUBaseAndExponentUECPPointCryptoPP_140597D70.cpp" />
    <ClCompile Include="Source\max_sizeallocatorUBaseAndExponentVIntegerCryptoPPV_1405965E0.cpp" />
    <ClCompile Include="Source\max_sizevectorUBaseAndExponentUEC2NPointCryptoPPVI_140594220.cpp" />
    <ClCompile Include="Source\max_sizevectorUBaseAndExponentUECPPointCryptoPPVIn_140594A00.cpp" />
    <ClCompile Include="Source\max_sizevectorUBaseAndExponentVIntegerCryptoPPV12C_140592F00.cpp" />
    <ClCompile Include="Source\MBaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_1405ABD00.cpp" />
    <ClCompile Include="Source\MBaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_1405ABD30.cpp" />
    <ClCompile Include="Source\MBaseAndExponentVIntegerCryptoPPV12CryptoPPQEBA_NA_1405ABCD0.cpp" />
    <ClCompile Include="Source\mgr_all_item_muziCPlayerQEAA_NHZ_1400B9070.cpp" />
    <ClCompile Include="Source\mgr_change_degreeCPlayerQEAA_NHZ_1400B8670.cpp" />
    <ClCompile Include="Source\mgr_defense_item_graceCPlayerQEAA_NEHZ_1400B9200.cpp" />
    <ClCompile Include="Source\mgr_destroy_system_towerCPlayerQEAA_NXZ_1400BEC30.cpp" />
    <ClCompile Include="Source\mgr_dungeon_passCPlayerQEAA_NXZ_1400BDDC0.cpp" />
    <ClCompile Include="Source\mgr_exit_keeperCPlayerQEAA_NXZ_1400B8DB0.cpp" />
    <ClCompile Include="Source\mgr_exit_stoneCPlayerQEAA_NXZ_1400B8DA0.cpp" />
    <ClCompile Include="Source\mgr_free_ride_shipCPlayerQEAA_NXZ_1400BAC60.cpp" />
    <ClCompile Include="Source\mgr_gotoCoordinatesCPlayerQEAA_NPEADMMMZ_1400B87B0.cpp" />
    <ClCompile Include="Source\mgr_gotoDstCoordinatesCPlayerQEAA_NPEAD0MMMZ_1400B88E0.cpp" />
    <ClCompile Include="Source\mgr_goto_mineCPlayerQEAA_NXZ_1400B8DC0.cpp" />
    <ClCompile Include="Source\mgr_goto_shipportCPlayerQEAA_NHHZ_1400BAA20.cpp" />
    <ClCompile Include="Source\mgr_goto_stoneCPlayerQEAA_NEZ_1400B8CE0.cpp" />
    <ClCompile Include="Source\mgr_goto_storeCPlayerQEAA_NHPEADZ_1400BA860.cpp" />
    <ClCompile Include="Source\mgr_holykeeper_startCPlayerQEAA_NHZ_1400B8CC0.cpp" />
    <ClCompile Include="Source\mgr_holystone_startCPlayerQEAA_NHZ_1400B8CA0.cpp" />
    <ClCompile Include="Source\mgr_item_telekinesisCPlayerQEAA_NXZ_1400B9480.cpp" />
    <ClCompile Include="Source\mgr_kickCPlayerQEAA_NPEADZ_1400B8B30.cpp" />
    <ClCompile Include="Source\mgr_make_system_towerCPlayerQEAA_NPEADZ_1400BE750.cpp" />
    <ClCompile Include="Source\mgr_matchlessCPlayerQEAA_N_NZ_1400B8770.cpp" />
    <ClCompile Include="Source\mgr_MaxAttackPointCPlayerQEAA_NHZ_1400B8E60.cpp" />
    <ClCompile Include="Source\mgr_pass_sch_one_stepCPlayerQEAA_NXZ_1400BAC20.cpp" />
    <ClCompile Include="Source\mgr_recall_guild_playerCPlayerQEAA_NPEADZ_1400BF8C0.cpp" />
    <ClCompile Include="Source\mgr_recall_monCPlayerQEAA_NPEADHZ_1400BA760.cpp" />
    <ClCompile Include="Source\mgr_recall_party_playerCPlayerQEAA_NPEADZ_1400BF5C0.cpp" />
    <ClCompile Include="Source\mgr_recall_playerCPlayerQEAA_NPEADZ_1400BA690.cpp" />
    <ClCompile Include="Source\mgr_resurrect_playerCPlayerQEAA_NPEADZ_1400BEF20.cpp" />
    <ClCompile Include="Source\mgr_set_animus_attack_pointCPlayerQEAA_NHZ_1400B9410.cpp" />
    <ClCompile Include="Source\mgr_tracingCPlayerQEAA_N_NZ_1400B86E0.cpp" />
    <ClCompile Include="Source\mgr_TrunkInitCPlayerQEAA_NXZ_1400B8EA0.cpp" />
    <ClCompile Include="Source\mgr_user_banCPlayerQEAA_NPEADH0EZ_1400BF3D0.cpp" />
    <ClCompile Include="Source\mgr_whisperCPlayerQEAA_NPEADZ_1400BAE10.cpp" />
    <ClCompile Include="Source\ModularExponentiationCryptoPPYAAVInteger1AEBV2100Z_14064CB90.cpp" />
    <ClCompile Include="Source\MonsterCritical_Exception_RateCAttackIEAAHPEAVCCha_14016A8F0.cpp" />
    <ClCompile Include="Source\MoveBreakCCharacterQEAAXMZ_1401730B0.cpp" />
    <ClCompile Include="Source\MoveCCharacterQEAAXMZ_140172C50.cpp" />
    <ClCompile Include="Source\MoveStartPosCNormalGuildBattleFieldGUILD_BATTLEQEA_1403ED280.cpp" />
    <ClCompile Include="Source\NeedConversionsModExpPrecomputationCryptoPPUEBA_NX_14055F0E0.cpp" />
    <ClCompile Include="Source\NetCloseCGuildBattleControllerQEAAXPEAVCPlayerZ_1403D64D0.cpp" />
    <ClCompile Include="Source\NetCloseCNationSettingDataCNUEAAXPEAVCPlayerZ_1402308D0.cpp" />
    <ClCompile Include="Source\NetCloseCNationSettingDataNULLUEAAXPEAVCPlayerZ_1402130A0.cpp" />
    <ClCompile Include="Source\NetCloseCNationSettingDataUEAAXPEAVCPlayerZ_140212900.cpp" />
    <ClCompile Include="Source\NetCloseCNationSettingManagerQEAAXPEAVCPlayerZ_14007A150.cpp" />
    <ClCompile Include="Source\NetCloseCNormalGuildBattleGUILD_BATTLEQEAAEKPEAVCP_1403E5940.cpp" />
    <ClCompile Include="Source\NetCloseCNormalGuildBattleManagerGUILD_BATTLEQEAAE_1403D4A90.cpp" />
    <ClCompile Include="Source\NetCloseCPlayerQEAAX_NZ_14004CEE0.cpp" />
    <ClCompile Include="Source\NewViewCircleObjectCPlayerQEAAXXZ_14005D4A0.cpp" />
    <ClCompile Include="Source\NextMissionOtherQuesterCDarkHoleChannelQEAAXPEAVCP_140267400.cpp" />
    <ClCompile Include="Source\NextPassCMonsterSkillQEAAXXZ_140156980.cpp" />
    <ClCompile Include="Source\NotifyCExpInfoCPartyModeKillMonsterExpNotifyQEAAXX_1401693B0.cpp" />
    <ClCompile Include="Source\NotifyCPartyModeKillMonsterExpNotifyQEAAXXZ_14008E620.cpp" />
    <ClCompile Include="Source\NotifyOwnerAttackInformCGuardTowerQEAAXPEAVCCharac_1401312E0.cpp" />
    <ClCompile Include="Source\NotifyOwnerAttackInform_TOWER_PARAMQEAAXPEAVCChara_14010E960.cpp" />
    <ClCompile Include="Source\NotifySetBuffCRaceBuffInfoByHolyQuestAEAAXPEAVCPla_1403B4380.cpp" />
    <ClCompile Include="Source\OnButtonOffplayerCGameServerViewQEAAXXZ_14002B290.cpp" />
    <ClCompile Include="Source\OnLoop_StaticCPlayerSAXXZ_140066350.cpp" />
    <ClCompile Include="Source\OnPlayerCreateCompleteProcItemCombineMgrQEAAXXZ_1402AB950.cpp" />
    <ClCompile Include="Source\ON__list_BUDDY_LISTQEAAXPEADPEAVCPlayerZ_140079330.cpp" />
    <ClCompile Include="Source\OpenChannelCDarkHoleDungeonQuestQEAAPEAVCDarkHoleC_140266330.cpp" />
    <ClCompile Include="Source\OpenCReturnGateControllerQEAA_NPEAVCPlayerZ_1402506A0.cpp" />
    <ClCompile Include="Source\OpenDungeonCDarkHoleChannelQEAAXPEAV_dh_quest_setu_1402678F0.cpp" />
    <ClCompile Include="Source\OutOfMapCPlayerQEAA_NPEAVCMapDataGEPEAMZ_1400501D0.cpp" />
    <ClCompile Include="Source\OutOfSecCPlayerUEAAXXZ_140055510.cpp" />
    <ClCompile Include="Source\out_playerCGuildMasterEffectQEAA_NPEAVCPlayerEZ_1403F4940.cpp" />
    <ClCompile Include="Source\PartyListInitCPartyPlayerQEAAXXZ_140044DB0.cpp" />
    <ClCompile Include="Source\PastWhisperInitCPlayerQEAAXXZ_140053860.cpp" />
    <ClCompile Include="Source\PcBangDeleteItemCPcBangFavorQEAAXPEAVCPlayerZ_14040C540.cpp" />
    <ClCompile Include="Source\PcBangGiveItemCPcBangFavorQEAA_NPEAVCPlayerKPEAEHZ_14040BFF0.cpp" />
    <ClCompile Include="Source\pc_AddBagCPlayerQEAAXGZ_1400B4340.cpp" />
    <ClCompile Include="Source\pc_AlterItemSlotRequestCPlayerQEAAXEPEAU__list_alt_1400FD570.cpp" />
    <ClCompile Include="Source\pc_AlterLinkBoardSlotRequestCPlayerQEAAXEPEAU__lis_1400FD690.cpp" />
    <ClCompile Include="Source\pc_AlterWindowInfoRequestCPlayerQEAAXPEAK000K0Z_1400FD900.cpp" />
    <ClCompile Include="Source\pc_AnimusCommandRequestCPlayerQEAAXEZ_1400D0AE0.cpp" />
    <ClCompile Include="Source\pc_AnimusInvenChangeCPlayerQEAAXPEAU_STORAGE_POS_I_1400FC8E0.cpp" />
    <ClCompile Include="Source\pc_AnimusRecallRequestCPlayerQEAAXGGGZ_1400D02E0.cpp" />
    <ClCompile Include="Source\pc_AnimusReturnRequestCPlayerQEAAXXZ_1400D0A50.cpp" />
    <ClCompile Include="Source\pc_AnimusTargetRequestCPlayerQEAAXEGKZ_1400D0B60.cpp" />
    <ClCompile Include="Source\pc_AwaypartyInvitationRequestCPlayerQEAAXPEADZ_1400C3D70.cpp" />
    <ClCompile Include="Source\pc_AwayPartyJoinInvitationAnswerCPlayerQEAAXPEAU_C_1400C4130.cpp" />
    <ClCompile Include="Source\pc_BackTowerRequestCPlayerQEAAXKZ_14009D050.cpp" />
    <ClCompile Include="Source\pc_BackTrapRequestCPlayerQEAAXKGZ_14009D8B0.cpp" />
    <ClCompile Include="Source\pc_BillingInfoRequestCPlayerQEAAXXZ_14008F4D0.cpp" />
    <ClCompile Include="Source\pc_BriefPassCPlayerQEAAXEZ_1400CC500.cpp" />
    <ClCompile Include="Source\pc_BuddyAddAnswerCPlayerQEAAX_NGKZ_14008F750.cpp" />
    <ClCompile Include="Source\pc_BuddyAddRequestCPlayerQEAAXGKPEADZ_14008F4E0.cpp" />
    <ClCompile Include="Source\pc_BuddyDelRequestCPlayerQEAAXKZ_14008FBC0.cpp" />
    <ClCompile Include="Source\pc_BuddyDownloadRequestCPlayerQEAAXXZ_14008FC80.cpp" />
    <ClCompile Include="Source\pc_BuyItemStoreCPlayerQEAAXPEAVCItemStoreEPEAU_lis_1400F1140.cpp" />
    <ClCompile Include="Source\pc_CanSelectClassRequestCPlayerQEAAEPEAEZ_140097770.cpp" />
    <ClCompile Include="Source\pc_CastVoteRequestCPlayerQEAAXHEZ_140108590.cpp" />
    <ClCompile Include="Source\pc_ChangeModeTypeCPlayerQEAAXHHZ_1400FD840.cpp" />
    <ClCompile Include="Source\pc_CharacterRenameCashCPlayerQEAA_N_NPEAU_STORAGE__1400B5770.cpp" />
    <ClCompile Include="Source\pc_CharacterRenameCheckCPlayerQEAAEPEBDZ_1400B5C40.cpp" />
    <ClCompile Include="Source\pc_ChatAllRequestCPlayerQEAAXPEADZ_140092860.cpp" />
    <ClCompile Include="Source\pc_ChatCircleRequestCPlayerQEAAXPEADZ_140090C20.cpp" />
    <ClCompile Include="Source\pc_ChatFarRequestCPlayerQEAAXPEAD0Z_140091500.cpp" />
    <ClCompile Include="Source\pc_ChatGmNoticeRequestCPlayerQEAAXPEADZ_140092410.cpp" />
    <ClCompile Include="Source\pc_ChatGuildEstSenRequestCPlayerQEAAXPEADZ_1400936C0.cpp" />
    <ClCompile Include="Source\pc_ChatGuildRequestCPlayerQEAAXKPEADZ_140091B30.cpp" />
    <ClCompile Include="Source\pc_ChatMapRequestCPlayerQEAAXPEADZ_140092480.cpp" />
    <ClCompile Include="Source\pc_ChatMgrWhisperRequestCPlayerQEAAXPEADZ_140091AD0.cpp" />
    <ClCompile Include="Source\pc_ChatMultiFarRequestCPlayerQEAAXEPEAU_w_namePEAD_1400939E0.cpp" />
    <ClCompile Include="Source\pc_ChatOperatorRequestCPlayerQEAAXEPEADZ_140090B00.cpp" />
    <ClCompile Include="Source\pc_ChatPartyRequestCPlayerQEAAXPEADZ_140091880.cpp" />
    <ClCompile Include="Source\pc_ChatRaceBossCryRequestCPlayerQEAAXPEADZ_140092150.cpp" />
    <ClCompile Include="Source\pc_ChatRaceBossRequestCPlayerQEAAXPEADZ_140092CD0.cpp" />
    <ClCompile Include="Source\pc_ChatRaceRequestCPlayerQEAAXPEADZ_140091E70.cpp" />
    <ClCompile Include="Source\pc_ChatRePresentationRequestCPlayerQEAAXPEADZ_140093380.cpp" />
    <ClCompile Include="Source\pc_ChatTradeRequestMsgCPlayerQEAAXEPEADZ_140093000.cpp" />
    <ClCompile Include="Source\pc_ClassSkillRequestCPlayerQEAAXGPEAU_CHRIDPEAGZ_14009A590.cpp" />
    <ClCompile Include="Source\pc_CombineItemCPlayerQEAAXGEPEAU_STORAGE_POS_INDIV_1400AF6C0.cpp" />
    <ClCompile Include="Source\pc_CombineItemExAcceptCPlayerQEAAXPEAU_combine_ex__1400B0600.cpp" />
    <ClCompile Include="Source\pc_CombineItemExCPlayerQEAAXPEAU_combine_ex_item_r_1400B0560.cpp" />
    <ClCompile Include="Source\pc_CuttingCompleteCPlayerQEAAXEZ_1400D32B0.cpp" />
    <ClCompile Include="Source\pc_DarkHoleAnswerReenterRequestCPlayerQEAAX_NGKZ_140098DA0.cpp" />
    <ClCompile Include="Source\pc_DarkHoleClearOutRequestCPlayerQEAAXXZ_140098CA0.cpp" />
    <ClCompile Include="Source\pc_DarkHoleEnterRequestCPlayerQEAAXGKZ_140098780.cpp" />
    <ClCompile Include="Source\pc_DarkHoleGiveupOutRequestCPlayerQEAAXXZ_140098BA0.cpp" />
    <ClCompile Include="Source\pc_DarkHoleOpenRequestCPlayerQEAAXKZ_1400982C0.cpp" />
    <ClCompile Include="Source\pc_DowngradeItemCPlayerQEAAXPEAU_STORAGE_POS_INDIV_1400B2B70.cpp" />
    <ClCompile Include="Source\pc_DTradeAddRequestCPlayerQEAAXEEKEZ_1400F4080.cpp" />
    <ClCompile Include="Source\pc_DTradeAnswerRequestCPlayerQEAAXPEAU_CLIDZ_1400F3A20.cpp" />
    <ClCompile Include="Source\pc_DTradeAskRequestCPlayerQEAAXGZ_1400F3790.cpp" />
    <ClCompile Include="Source\pc_DTradeBetRequestCPlayerQEAAXEKZ_1400F4670.cpp" />
    <ClCompile Include="Source\pc_DTradeCancleRequestCPlayerQEAAXXZ_1400F3E60.cpp" />
    <ClCompile Include="Source\pc_DTradeDelRequestCPlayerQEAAXEZ_1400F4420.cpp" />
    <ClCompile Include="Source\pc_DTradeLockRequestCPlayerQEAAXXZ_1400F3F60.cpp" />
    <ClCompile Include="Source\pc_DTradeOKRequestCPlayerQEAAXPEAKZ_1400F4810.cpp" />
    <ClCompile Include="Source\pc_EmbellishPartCPlayerQEAAXPEAU_STORAGE_POS_INDIV_1400ADE20.cpp" />
    <ClCompile Include="Source\pc_EquipPartCPlayerQEAAXPEAU_STORAGE_POS_INDIVZ_1400AD960.cpp" />
    <ClCompile Include="Source\pc_ExchangeDalantForGoldCPlayerQEAAXKZ_1400F2ED0.cpp" />
    <ClCompile Include="Source\pc_ExchangeGoldForDalantCPlayerQEAAXKZ_1400F3190.cpp" />
    <ClCompile Include="Source\pc_ExchangeGoldForPvPCPlayerQEAAXKZ_1400F3420.cpp" />
    <ClCompile Include="Source\pc_ExchangeItemCPlayerQEAAXGGZ_1400B0690.cpp" />
    <ClCompile Include="Source\pc_ExitWorldRequestCPlayerQEAAXXZ_14004E630.cpp" />
    <ClCompile Include="Source\pc_ForceInvenChangeCPlayerQEAAXPEAU_STORAGE_POS_IN_1400FC2F0.cpp" />
    <ClCompile Include="Source\pc_ForceRequestCPlayerQEAAXGPEAU_CHRIDPEAGZ_1400995B0.cpp" />
    <ClCompile Include="Source\pc_GestureRequestCPlayerQEAAXEZ_1400FD8B0.cpp" />
    <ClCompile Include="Source\pc_GiveItemCPlayerQEAA_NAEAU_db_con_STORAGE_LISTPE_14004E750.cpp" />
    <ClCompile Include="Source\pc_GotoAvatorRequestCPlayerQEAAXPEADZ_1400C7440.cpp" />
    <ClCompile Include="Source\pc_GotoBasePortalRequestCPlayerQEAAXGZ_1400C6CF0.cpp" />
    <ClCompile Include="Source\pc_GuildBattleBlockCPlayerQEAAX_NZ_1400FEF90.cpp" />
    <ClCompile Include="Source\pc_GuildCancelSuggestRequestCPlayerQEAAXKZ_1400A8350.cpp" />
    <ClCompile Include="Source\pc_GuildDownLoadRequestCPlayerQEAAXXZ_1400A7890.cpp" />
    <ClCompile Include="Source\pc_GuildEstablishRequestCPlayerQEAAXPEADZ_1400A6D10.cpp" />
    <ClCompile Include="Source\pc_GuildHonorListRequestCPlayerQEAAXEZ_1400AB9E0.cpp" />
    <ClCompile Include="Source\pc_GuildJoinAcceptRequestCPlayerQEAAXK_NZ_1400A7BF0.cpp" />
    <ClCompile Include="Source\pc_GuildJoinApplyCancelRequestCPlayerQEAAXXZ_1400A7AF0.cpp" />
    <ClCompile Include="Source\pc_GuildJoinApplyRequestCPlayerQEAAXPEADZ_1400A7910.cpp" />
    <ClCompile Include="Source\pc_GuildListRequestCPlayerQEAAXEZ_1400AB850.cpp" />
    <ClCompile Include="Source\pc_GuildManageRequestCPlayerQEAAXEKKKKZ_1400ABB10.cpp" />
    <ClCompile Include="Source\pc_GuildNextHonorListRequestCPlayerQEAAXXZ_1400ABA50.cpp" />
    <ClCompile Include="Source\pc_GuildOfferSuggestRequestCPlayerQEAAXEKPEADKKKZ_1400A8190.cpp" />
    <ClCompile Include="Source\pc_GuildPushMoneyRequestCPlayerQEAAXKKZ_1400A8620.cpp" />
    <ClCompile Include="Source\pc_GuildQueryInfoRequestCPlayerQEAAXKZ_1400A8550.cpp" />
    <ClCompile Include="Source\pc_GuildRoomEnterRequestCPlayerQEAAXPEAU_guildroom_1400AAFF0.cpp" />
    <ClCompile Include="Source\pc_GuildRoomOutRequestCPlayerQEAAXPEAU_guildroom_o_1400AB560.cpp" />
    <ClCompile Include="Source\pc_GuildRoomRentRequestCPlayerQEAAXPEAU_guildroom__1400AAA60.cpp" />
    <ClCompile Include="Source\pc_GuildRoomRestTimeRequestCPlayerQEAAXPEAU_guildr_1400AB780.cpp" />
    <ClCompile Include="Source\pc_GuildSelfLeaveRequestCPlayerQEAAXXZ_1400A7F50.cpp" />
    <ClCompile Include="Source\pc_GuildSetHonorRequestCPlayerQEAAXPEAU_guild_hono_1400AB910.cpp" />
    <ClCompile Include="Source\pc_GuildVoteRequestCPlayerQEAAXKEZ_1400A8410.cpp" />
    <ClCompile Include="Source\pc_InitClassCPlayerQEAAEXZ_1400972F0.cpp" />
    <ClCompile Include="Source\pc_InitClassRequestCPlayerQEAAEXZ_1400976C0.cpp" />
    <ClCompile Include="Source\pc_LimitItemNumRequestCPlayerQEAAXKZ_1400F3580.cpp" />
    <ClCompile Include="Source\pc_LinkBoardRequestCPlayerQEAAXXZ_1400FEFC0.cpp" />
    <ClCompile Include="Source\pc_MacroUpdateCPlayerQEAAXPEADZ_1400FF920.cpp" />
    <ClCompile Include="Source\pc_MakeItemCPlayerQEAAXPEAU_STORAGE_POS_INDIVGE0Z_1400AE750.cpp" />
    <ClCompile Include="Source\pc_MakeTowerRequestCPlayerQEAAXGGEPEAU__material_m_14009C6E0.cpp" />
    <ClCompile Include="Source\pc_MakeTrapRequestCPlayerQEAAXGGPEAMPEAGZ_14009D1C0.cpp" />
    <ClCompile Include="Source\pc_MineCancleCPlayerQEAAXXZ_1400D1ED0.cpp" />
    <ClCompile Include="Source\pc_MineCompleteCPlayerQEAAXXZ_1400D1F40.cpp" />
    <ClCompile Include="Source\pc_MineStartCPlayerQEAAXEEGZ_1400D18E0.cpp" />
    <ClCompile Include="Source\pc_MoveModeChangeRequestCPlayerQEAAXEZ_1400C7810.cpp" />
    <ClCompile Include="Source\pc_MoveNextCPlayerQEAAXEPEAM0EZ_1400C6230.cpp" />
    <ClCompile Include="Source\pc_MovePortalCPlayerQEAAXHPEAGZ_1400C54A0.cpp" />
    <ClCompile Include="Source\pc_MoveStopCPlayerQEAAXPEAMZ_1400C6AB0.cpp" />
    <ClCompile Include="Source\pc_MoveToOwnStoneMapRequestCPlayerQEAAXXZ_1400CE6D0.cpp" />
    <ClCompile Include="Source\pc_NewPosStartCPlayerQEAAXXZ_1400C4BC0.cpp" />
    <ClCompile Include="Source\pc_NotifyRaceBossCryMsgCPlayerQEAAXXZ_1400FFA30.cpp" />
    <ClCompile Include="Source\pc_NPCLinkCheckItemRequestCPlayerQEAA_NPEAU_STORAG_1400B5710.cpp" />
    <ClCompile Include="Source\pc_NPCLinkCheckItemRequest_CheckCPlayerQEAAEPEAU_S_1400B54E0.cpp" />
    <ClCompile Include="Source\pc_NPCLinkCheckItemRequest_UseCPlayerQEAAEPEAU_STO_1400B5340.cpp" />
    <ClCompile Include="Source\pc_NuclearAfterEffectCPlayerQEAAXXZ_1400C5160.cpp" />
    <ClCompile Include="Source\pc_OffPartCPlayerQEAAXPEAU_STORAGE_POS_INDIVZ_1400AE4D0.cpp" />
    <ClCompile Include="Source\pc_OreCuttingCPlayerQEAAXGEZ_1400D26B0.cpp" />
    <ClCompile Include="Source\pc_OreIntoBagCPlayerQEAAXGGEZ_1400D2EB0.cpp" />
    <ClCompile Include="Source\pc_PartyAlterLootShareReqeuestCPlayerQEAAXEZ_1400C3CF0.cpp" />
    <ClCompile Include="Source\pc_PartyDisJointReqeuestCPlayerQEAAXXZ_1400C3B30.cpp" />
    <ClCompile Include="Source\pc_PartyJoinApplicationAnswerCPlayerQEAAXPEAU_CLID_1400C3800.cpp" />
    <ClCompile Include="Source\pc_PartyJoinApplicationCPlayerQEAAXGZ_1400C3580.cpp" />
    <ClCompile Include="Source\pc_PartyJoinInvitationAnswerCPlayerQEAAXPEAU_CLIDZ_1400C32C0.cpp" />
    <ClCompile Include="Source\pc_PartyJoinInvitationCPlayerQEAAXGZ_1400C2FD0.cpp" />
    <ClCompile Include="Source\pc_PartyLeaveCompulsionReqeuestCPlayerQEAAXKZ_1400C3A80.cpp" />
    <ClCompile Include="Source\pc_PartyLeaveSelfReqeuestCPlayerQEAAXXZ_1400C3A10.cpp" />
    <ClCompile Include="Source\pc_PartyLockReqeuestCPlayerQEAAX_NZ_1400C3C50.cpp" />
    <ClCompile Include="Source\pc_PartyReqBlockCPlayerQEAAX_NZ_1400FEED0.cpp" />
    <ClCompile Include="Source\pc_PartySuccessionReqeuestCPlayerQEAAXKZ_1400C3BA0.cpp" />
    <ClCompile Include="Source\pc_PlayAttack_ForceCPlayerQEAAXPEAVCCharacterPEAMG_140082210.cpp" />
    <ClCompile Include="Source\pc_PlayAttack_GenCPlayerQEAAXPEAVCCharacterEGG_NZ_1400804D0.cpp" />
    <ClCompile Include="Source\pc_PlayAttack_SelfDestructionCPlayerQEAAXXZ_140084E10.cpp" />
    <ClCompile Include="Source\pc_PlayAttack_SiegeCPlayerQEAAXPEAVCCharacterPEAME_140083A20.cpp" />
    <ClCompile Include="Source\pc_PlayAttack_SkillCPlayerQEAAXPEAVCCharacterPEAME_140081190.cpp" />
    <ClCompile Include="Source\pc_PlayAttack_TestCPlayerQEAAXEEGEPEAFZ_1400835A0.cpp" />
    <ClCompile Include="Source\pc_PlayAttack_UnitCPlayerQEAAXPEAVCCharacterEZ_1400830D0.cpp" />
    <ClCompile Include="Source\pc_PostContentRequestCPlayerQEAAXKZ_1400C8DB0.cpp" />
    <ClCompile Include="Source\pc_PostDeleteRequestCPlayerQEAAXKZ_1400C9320.cpp" />
    <ClCompile Include="Source\pc_PostItemGoldRequestCPlayerQEAAXKZ_1400C8F90.cpp" />
    <ClCompile Include="Source\pc_PostListRequestCPlayerQEAAXXZ_1400C8CF0.cpp" />
    <ClCompile Include="Source\pc_PostReturnConfirmRequestCPlayerQEAAXKZ_1400C9450.cpp" />
    <ClCompile Include="Source\pc_PotionDivisionCPlayerQEAAXGGEZ_1400FD0D0.cpp" />
    <ClCompile Include="Source\pc_PotionSeparationCPlayerQEAAXGEZ_1400FD060.cpp" />
    <ClCompile Include="Source\pc_PotionUseTrunkExtendCPlayerQEAAXXZ_1400FB310.cpp" />
    <ClCompile Include="Source\pc_ProposeVoteRequestCPlayerQEAAXEPEADZ_140108450.cpp" />
    <ClCompile Include="Source\pc_PvpCashRecorverCPlayerQEAAXKEZ_1400F5FD0.cpp" />
    <ClCompile Include="Source\pc_QuestGiveupRequestCPlayerQEAAXEZ_1400CC5B0.cpp" />
    <ClCompile Include="Source\pc_RadarCharInfoCPlayerQEAA_NXZ_1400B5280.cpp" />
    <ClCompile Include="Source\pc_RealMovPosCPlayerQEAAXPEAMZ_1400C6820.cpp" />
    <ClCompile Include="Source\pc_RefreshGroupTargetPositionCPlayerQEAAXEPEAVCGam_1400FE7D0.cpp" />
    <ClCompile Include="Source\pc_RegistBindCPlayerQEAAXPEAVCItemStoreZ_1400C7640.cpp" />
    <ClCompile Include="Source\pc_ReleaseGroupTargetObjectRequestCPlayerQEAAXEZ_1400FE3D0.cpp" />
    <ClCompile Include="Source\pc_ReleaseSiegeModeRequestCPlayerQEAAXXZ_1400F0B20.cpp" />
    <ClCompile Include="Source\pc_ReleaseTargetObjectRequestCPlayerQEAAXXZ_1400FDBA0.cpp" />
    <ClCompile Include="Source\pc_RenameItemNConditionCheckCPlayerQEAAEPEAU_STORA_1400B5D60.cpp" />
    <ClCompile Include="Source\pc_RequestChangeTaxRateCPlayerQEAAXEZ_140100A00.cpp" />
    <ClCompile Include="Source\pc_RequestDialogWithNPCCPlayerQEAAXPEAVCItemStoreZ_1400CC410.cpp" />
    <ClCompile Include="Source\pc_RequestPatriarchPunishmentCPlayerQEAAXEPEAD0Z_140100720.cpp" />
    <ClCompile Include="Source\pc_RequestQuestFromNPCCPlayerQEAAXPEAVCItemStoreKZ_1400CC330.cpp" />
    <ClCompile Include="Source\pc_RequestQuestListFromNPCCPlayerQEAAXPEAVCItemSto_1400CC720.cpp" />
    <ClCompile Include="Source\pc_RequestTaxRateCPlayerQEAAXXZ_140100950.cpp" />
    <ClCompile Include="Source\pc_RequestUILockCertifyCPlayerQEAAXPEAVCUserDBPEAD_140100E20.cpp" />
    <ClCompile Include="Source\pc_RequestUILockFindPWCPlayerQEAAXPEAVCUserDBPEADZ_140101300.cpp" />
    <ClCompile Include="Source\pc_RequestUILockInitCPlayerQEAAXPEAVCUserDBPEAD1E1_140100BA0.cpp" />
    <ClCompile Include="Source\pc_RequestUILockUpdateCPlayerQEAAXPEAD00E0Z_140100FD0.cpp" />
    <ClCompile Include="Source\pc_RequestWatchingWithNPCCPlayerQEAAXPEAVCItemStor_1400CC650.cpp" />
    <ClCompile Include="Source\pc_ResDivisionCPlayerQEAAXGGEZ_1400FD320.cpp" />
    <ClCompile Include="Source\pc_ResSeparationCPlayerQEAAXGEZ_1400FD2D0.cpp" />
    <ClCompile Include="Source\pc_ResurrectCPlayerQEAA_N_NZ_1400C7A80.cpp" />
    <ClCompile Include="Source\pc_RevivalCPlayerQEAAX_NZ_1400C4E90.cpp" />
    <ClCompile Include="Source\pc_SelectClassRequestCPlayerQEAAXGEZ_1400951C0.cpp" />
    <ClCompile Include="Source\pc_SelectQuestAfterHappenEventCPlayerQEAAXEZ_1400CA9B0.cpp" />
    <ClCompile Include="Source\pc_SelectQuestRewardCPlayerQEAAXEEEZ_1400CBC80.cpp" />
    <ClCompile Include="Source\pc_SellItemStoreCPlayerQEAAXPEAVCItemStoreEPEAU_li_1400F2530.cpp" />
    <ClCompile Include="Source\pc_SetGroupMapPointRequestCPlayerQEAAXEPEAMZ_1400FFD00.cpp" />
    <ClCompile Include="Source\pc_SetGroupTargetObjectRequestCPlayerQEAAXPEAVCGam_1400FDBC0.cpp" />
    <ClCompile Include="Source\pc_SetInGuildBattleCPlayerQEAAX_NEZ_1400AA8F0.cpp" />
    <ClCompile Include="Source\pc_SetItemCheckRequestCPlayerQEAA_NKEE_NZ_1400B47F0.cpp" />
    <ClCompile Include="Source\pc_SetRaceBossCryMsgCPlayerQEAAXEPEADZ_1400FF970.cpp" />
    <ClCompile Include="Source\pc_SetTargetObjectRequestCPlayerQEAAXPEAVCGameObje_1400FDA40.cpp" />
    <ClCompile Include="Source\pc_SkillRequestCPlayerQEAAXEPEAU_CHRIDPEAGZ_14009A4B0.cpp" />
    <ClCompile Include="Source\pc_StopCPlayerQEAAXXZ_1400C7BF0.cpp" />
    <ClCompile Include="Source\pc_TakeGroundingItemCPlayerQEAAXPEAVCItemBoxGZ_1400B3240.cpp" />
    <ClCompile Include="Source\pc_TalikCrystalExchangeCPlayerQEAAXEPEAU_list_tali_1400B5EE0.cpp" />
    <ClCompile Include="Source\pc_ThrowSkillRequestCPlayerQEAAXGPEAU_CHRIDPEAGZ_14009A660.cpp" />
    <ClCompile Include="Source\pc_ThrowStorageItemCPlayerQEAAXPEAU_STORAGE_POS_IN_1400B3F10.cpp" />
    <ClCompile Include="Source\pc_ThrowUnitRequestCPlayerQEAAXPEAU_CHRIDPEAGZ_14009B030.cpp" />
    <ClCompile Include="Source\pc_TradeBlockCPlayerQEAAX_NZ_1400FEF50.cpp" />
    <ClCompile Include="Source\pc_TransformSiegeModeRequestCPlayerQEAAXGZ_1400F0880.cpp" />
    <ClCompile Include="Source\pc_TransShipRenewTicketRequestCPlayerQEAAXGZ_1400C7880.cpp" />
    <ClCompile Include="Source\pc_TrunkAlterItemSlotRequestCPlayerQEAAXKEEZ_1400FA560.cpp" />
    <ClCompile Include="Source\pc_TrunkChangePasswdRequestCPlayerQEAAXPEAD0E0Z_1400F8240.cpp" />
    <ClCompile Include="Source\pc_TrunkCreateCostIsFreeRequestCPlayerQEAAEXZ_1400FB2A0.cpp" />
    <ClCompile Include="Source\pc_TrunkDownloadRequestCPlayerQEAAXPEADZ_1400F7C30.cpp" />
    <ClCompile Include="Source\pc_TrunkEstRequestCPlayerQEAAXPEADE0Z_1400F7D00.cpp" />
    <ClCompile Include="Source\pc_TrunkExtendRequestCPlayerQEAAXXZ_1400F84D0.cpp" />
    <ClCompile Include="Source\pc_TrunkHintAnswerRequestCPlayerQEAAXPEADZ_1400FB170.cpp" />
    <ClCompile Include="Source\pc_TrunkIoMergeRequestCPlayerQEAAXEEGGGZ_1400F9E70.cpp" />
    <ClCompile Include="Source\pc_TrunkIoMoneyRequestCPlayerQEAAXEKKZ_1400FAB80.cpp" />
    <ClCompile Include="Source\pc_TrunkIoMoveRequestCPlayerQEAAXEEGEZ_1400F8830.cpp" />
    <ClCompile Include="Source\pc_TrunkIoSwapRequestCPlayerQEAAXEEGGZ_1400F9130.cpp" />
    <ClCompile Include="Source\pc_TrunkPotionDivisionCPlayerQEAAXGGGEZ_1400FA920.cpp" />
    <ClCompile Include="Source\pc_TrunkPwHintIndexRequestCPlayerQEAAXXZ_1400FB0E0.cpp" />
    <ClCompile Include="Source\pc_TrunkResDivisionCPlayerQEAAXGGGEZ_1400FA610.cpp" />
    <ClCompile Include="Source\pc_UnitBulletFillRequestCPlayerQEAAXEPEAGHZ_1401047E0.cpp" />
    <ClCompile Include="Source\pc_UnitBulletReplaceRequestCPlayerQEAAXEEEZ_140105630.cpp" />
    <ClCompile Include="Source\pc_UnitDeliveryRequestCPlayerQEAAXEPEAVCItemStore__140105750.cpp" />
    <ClCompile Include="Source\pc_UnitFrameBuyRequestCPlayerQEAAXEHZ_140102920.cpp" />
    <ClCompile Include="Source\pc_UnitFrameRepairRequestCPlayerQEAAXEHZ_140104240.cpp" />
    <ClCompile Include="Source\pc_UnitLeaveRequestCPlayerQEAAXPEAMZ_140106310.cpp" />
    <ClCompile Include="Source\pc_UnitPackFillRequestCPlayerQEAAXEEPEAU__list_uni_140104F00.cpp" />
    <ClCompile Include="Source\pc_UnitPartTuningRequestCPlayerQEAAXEEPEAU_tuning__140103740.cpp" />
    <ClCompile Include="Source\pc_UnitReturnRequestCPlayerQEAAXXZ_140105E20.cpp" />
    <ClCompile Include="Source\pc_UnitSellRequestCPlayerQEAAXEHZ_140103140.cpp" />
    <ClCompile Include="Source\pc_UnitTakeRequestCPlayerQEAAXXZ_140105FC0.cpp" />
    <ClCompile Include="Source\pc_UpdateDataForPostSendCPlayerQEAAXXZ_1400C9820.cpp" />
    <ClCompile Include="Source\pc_UpdateDataForTradeCPlayerQEAAXPEAV1Z_1400F5BA0.cpp" />
    <ClCompile Include="Source\pc_UpgradeItemCPlayerQEAAXPEAU_STORAGE_POS_INDIV00_1400B13E0.cpp" />
    <ClCompile Include="Source\pc_UseFireCrackerCPlayerQEAAHGZ_1400B45E0.cpp" />
    <ClCompile Include="Source\pc_UsePotionItemCPlayerQEAAXPEAV1PEAU_STORAGE_POS__1400AD4D0.cpp" />
    <ClCompile Include="Source\pc_UseRadarItemCPlayerQEAA_NPEAU_STORAGE_POS_INDIV_1400B4E10.cpp" />
    <ClCompile Include="Source\pc_UseRecoverLossExpItemCPlayerQEAADGZ_14004E910.cpp" />
    <ClCompile Include="Source\pc_UserSoccerBallCPlayerQEAAEGAEAGZ_1400B4BD0.cpp" />
    <ClCompile Include="Source\pc_WhisperBlockCPlayerQEAAX_NZ_1400FEF10.cpp" />
    <ClCompile Include="Source\pc_WPActiveAttack_ForceCPlayerQEAA_NPEAU_be_damage_14008A6F0.cpp" />
    <ClCompile Include="Source\pc_WPActiveAttack_SkillCPlayerQEAA_NPEAU_be_damage_14008AA30.cpp" />
    <ClCompile Include="Source\PlayerInfoResultCNetworkEXAEAA_NHPEADZ_1401CE1A0.cpp" />
    <ClCompile Include="Source\PlayerMacroUpdateCNetworkEXAEAA_NHPEADZ_1401D7FD0.cpp" />
    <ClCompile Include="Source\player_createCMgrAccountLobbyHistoryQEAAX_NPEAU_AV_1402346B0.cpp" />
    <ClCompile Include="Source\player_create_complete_moneyCMgrAccountLobbyHistor_1402347F0.cpp" />
    <ClCompile Include="Source\player_money_fixCMgrAccountLobbyHistoryQEAAXKKPEAU_1402348A0.cpp" />
    <ClCompile Include="Source\PopBuddy_BUDDY_LISTQEAAHKPEAPEAVCPlayerZ_1400908E0.cpp" />
    <ClCompile Include="Source\PopLinkCPlayerDBQEAAXHZ_14010B900.cpp" />
    <ClCompile Include="Source\pop_heapV_Vector_iteratorUBaseAndExponentUEC2NPoin_1405A1200.cpp" />
    <ClCompile Include="Source\pop_heapV_Vector_iteratorUBaseAndExponentUECPPoint_1405A1700.cpp" />
    <ClCompile Include="Source\pop_heapV_Vector_iteratorUBaseAndExponentVIntegerC_1405A0CD0.cpp" />
    <ClCompile Include="Source\PostSendRequestCPostSystemManagerQEAA_NPEAVCPlayer_1403252A0.cpp" />
    <ClCompile Include="Source\Potion_Buf_ExtendCPlayerQEAAXXZ_1400A39B0.cpp" />
    <ClCompile Include="Source\PreCheckPotionCPotionMgrQEAAHPEAVCPlayerAEAPEAVCCh_14039CDD0.cpp" />
    <ClCompile Include="Source\PrepareCascadeDL_FixedBasePrecomputationImplUEC2NP_140576730.cpp" />
    <ClCompile Include="Source\PrepareCascadeDL_FixedBasePrecomputationImplUECPPo_140578D90.cpp" />
    <ClCompile Include="Source\PrepareCascadeDL_FixedBasePrecomputationImplVInteg_14056FB60.cpp" />
    <ClCompile Include="Source\PrepareShadowRenderCLevelQEAAXQEAMPEAXMKMMZ_1404E07E0.cpp" />
    <ClCompile Include="Source\PrivateExponentNameCryptoPPYAPEBDXZ_1404661D0.cpp" />
    <ClCompile Include="Source\ProcessCheatCommandYA_NPEAVCPlayerPEADZ_14028EF70.cpp" />
    <ClCompile Include="Source\ProcessEnterCReturnGateControllerIEAAHIPEAVCPlayer_140250DD0.cpp" />
    <ClCompile Include="Source\ProcessRequestRecallCRecallEffectControllerIEAAEPE_14024EF50.cpp" />
    <ClCompile Include="Source\ProgressConsoleYAXPEAVCLevelZ_140512830.cpp" />
    <ClCompile Include="Source\PushApplierCGuildQEAA_NPEAVCPlayerZ_140253BF0.cpp" />
    <ClCompile Include="Source\PushBuddy_BUDDY_LISTQEAAHKPEADPEAVCPlayerZ_1400904C0.cpp" />
    <ClCompile Include="Source\PushDamageCLootingMgrQEAAXPEAVCPlayerGZ_14014C470.cpp" />
    <ClCompile Include="Source\PushDQSCheatPlyerVoteInfoCPlayerQEAAXXZ_140069B90.cpp" />
    <ClCompile Include="Source\PushDQSUpdatePlyerVoteInfoCPlayerQEAAXXZ_1400699F0.cpp" />
    <ClCompile Include="Source\PushDQSUpdateVoteAvilableCPlayerQEAAXXZ_140069900.cpp" />
    <ClCompile Include="Source\PushLinkCPlayerDBQEAA_NHG_NZ_14010B830.cpp" />
    <ClCompile Include="Source\PushMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPEAVC_14026A8C0.cpp" />
    <ClCompile Include="Source\push_backvectorUBaseAndExponentUEC2NPointCryptoPPV_14058E020.cpp" />
    <ClCompile Include="Source\push_backvectorUBaseAndExponentUECPPointCryptoPPVI_14058E570.cpp" />
    <ClCompile Include="Source\push_backvectorUBaseAndExponentVIntegerCryptoPPV12_14058D6A0.cpp" />
    <ClCompile Include="Source\Push_DataTimeLimitMgrQEAAXPEAUPlayer_TL_StatusGZ_14040E1F0.cpp" />
    <ClCompile Include="Source\push_heapV_Vector_iteratorUBaseAndExponentUEC2NPoi_1405A1370.cpp" />
    <ClCompile Include="Source\push_heapV_Vector_iteratorUBaseAndExponentUECPPoin_1405A1870.cpp" />
    <ClCompile Include="Source\push_heapV_Vector_iteratorUBaseAndExponentVInteger_1405A0E40.cpp" />
    <ClCompile Include="Source\qc_RewardExpYA_NPEAUstrFILEPEAVCDarkHoleDungeonQue_1402743F0.cpp" />
    <ClCompile Include="Source\R3CalcStrIndexPitInWidthAYAHPEBDHHKZ_140511560.cpp" />
    <ClCompile Include="Source\R3CalcStrIndexPitInWidthWYAHPEB_WHHKZ_1405114E0.cpp" />
    <ClCompile Include="Source\ReCalcMaxHFSPCPlayerQEAAX_N0Z_14005C9A0.cpp" />
    <ClCompile Include="Source\RecallCRecallRequestQEAAEPEAVCPlayer_NZ_14024DAC0.cpp" />
    <ClCompile Include="Source\RecallRandomPositionInRangeCPlayerQEAAXPEAVCMapDat_1400BF7A0.cpp" />
    <ClCompile Include="Source\ReceiveDestroyKeeperCHolyStoneSystemQEAAXPEAVCChar_14027CD30.cpp" />
    <ClCompile Include="Source\recovery_expCMgrAvatorLvHistoryQEAAXHNGNGNHPEAD0Z_140245C70.cpp" />
    <ClCompile Include="Source\RecvHSKQuestCPlayerQEAAXEEHGGEZ_1400CC840.cpp" />
    <ClCompile Include="Source\RecvKillMessageCAnimusUEAAXPEAVCCharacterZ_140129B80.cpp" />
    <ClCompile Include="Source\RecvKillMessageCGameObjectUEAAXPEAVCCharacterZ_14012E0D0.cpp" />
    <ClCompile Include="Source\RecvKillMessageCGuardTowerUEAAXPEAVCCharacterZ_140130610.cpp" />
    <ClCompile Include="Source\RecvKillMessageCNuclearBombUEAAXPEAVCCharacterZ_14013D300.cpp" />
    <ClCompile Include="Source\RecvKillMessageCPlayerUEAAXPEAVCCharacterZ_140053A30.cpp" />
    <ClCompile Include="Source\RecvKillMessageCTrapUEAAXPEAVCCharacterZ_14013F4F0.cpp" />
    <ClCompile Include="Source\ReEnterMemberCTransportShipQEAAXPEAVCPlayerZ_140264B30.cpp" />
    <ClCompile Include="Source\RegistCandidateMgrQEAA_NPEAVCPlayerZ_1402B1BA0.cpp" />
    <ClCompile Include="Source\RegistCheatCNationSettingFactoryIEAA_NPEAVCNationS_1402171C0.cpp" />
    <ClCompile Include="Source\RegistCRecallRequestQEAAEPEAVCPlayerPEAVCCharacter_14024D530.cpp" />
    <ClCompile Include="Source\regist_to_mapAutominePersonalQEAA_NPEAVCPlayerPEAU_1402DABC0.cpp" />
    <ClCompile Include="Source\ReleaseCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAVCP_1403B4D10.cpp" />
    <ClCompile Include="Source\ReleaseCRaceBuffInfoByHolyQuestListQEAA_NIHPEAVCPl_1403B5330.cpp" />
    <ClCompile Include="Source\ReleaseCRaceBuffInfoByHolyQuestQEAA_NPEAVCPlayerZ_1403B40F0.cpp" />
    <ClCompile Include="Source\ReleasecStaticMember_PlayerSAXXZ_14010E510.cpp" />
    <ClCompile Include="Source\ReleaseLevelCLevelQEAAXXZ_1404DFC40.cpp" />
    <ClCompile Include="Source\ReLoadAllMaterialCLevelQEAAXXZ_1404E0580.cpp" />
    <ClCompile Include="Source\RemoveAllContinousEffectCCharacterQEAA_NXZ_1401787C0.cpp" />
    <ClCompile Include="Source\RemoveAllContinousEffectGroupCCharacterQEAA_NIZ_140178830.cpp" />
    <ClCompile Include="Source\RemovePartyMemberCPartyPlayerQEAA_NPEAV1PEAPEAV1Z_1400453A0.cpp" />
    <ClCompile Include="Source\RemovePotionContEffectCPotionMgrIEAAHPEAVCPlayerAE_14039EE70.cpp" />
    <ClCompile Include="Source\RemoveSFContEffectCCharacterQEAAXEG_N0Z_140174930.cpp" />
    <ClCompile Include="Source\RemoveSFContHelpByEffectCCharacterQEAAXHHZ_140174BD0.cpp" />
    <ClCompile Include="Source\RemoveSlotCCharacterQEAAHPEAV1Z_140173AA0.cpp" />
    <ClCompile Include="Source\RenewOldMemberCTransportShipQEAA_NPEAVCPlayerZ_140264BC0.cpp" />
    <ClCompile Include="Source\RequestAcceptGMCallGMCallMgrQEAA_NPEAVCPlayerKZ_1402AA810.cpp" />
    <ClCompile Include="Source\RequestGMCallGMCallMgrQEAA_NPEAVCPlayerHZ_1402AA6A0.cpp" />
    <ClCompile Include="Source\RequestGMListGMCallMgrQEAA_NPEAVCPlayerHZ_1402AA7A0.cpp" />
    <ClCompile Include="Source\RequestRecallCRecallEffectControllerQEAA_NPEAVCPla_14024E340.cpp" />
    <ClCompile Include="Source\ReservationForceCloseCPlayerQEAAXXZ_1400663B0.cpp" />
    <ClCompile Include="Source\reservevectorUBaseAndExponentUEC2NPointCryptoPPVIn_14058DD50.cpp" />
    <ClCompile Include="Source\reservevectorUBaseAndExponentUECPPointCryptoPPVInt_14058E2A0.cpp" />
    <ClCompile Include="Source\reservevectorUBaseAndExponentVIntegerCryptoPPV12Cr_14058D3D0.cpp" />
    <ClCompile Include="Source\ResetSlotCCharacterQEAAXXZ_140173BB0.cpp" />
    <ClCompile Include="Source\resizevectorUBaseAndExponentUECPPointCryptoPPVInte_140612BA0.cpp" />
    <ClCompile Include="Source\resizevectorUBaseAndExponentUECPPointCryptoPPVInte_1406139F0.cpp" />
    <ClCompile Include="Source\ReStartCNormalGuildBattleGUILD_BATTLEQEAAEPEAVCPla_1403E42A0.cpp" />
    <ClCompile Include="Source\ResurrectCPlayerQEAAXXZ_1400500A0.cpp" />
    <ClCompile Include="Source\Return_AnimusAskCPlayerQEAAXEZ_1400D1030.cpp" />
    <ClCompile Include="Source\RewardChangeClassCPlayerQEAAXPEAU_class_fldEZ_140096550.cpp" />
    <ClCompile Include="Source\RewardChangeClassMasteryCPlayerQEAAXPEAU_class_fld_140095880.cpp" />
    <ClCompile Include="Source\RewardChangeClassRewardItemCPlayerQEAAXPEAU_class__140096100.cpp" />
    <ClCompile Include="Source\RewardRaceWarPvpCashCPlayerQEAAXXZ_140056070.cpp" />
    <ClCompile Include="Source\Reward_DarkDungeonCPlayerQEAAXPEAV_dh_reward_sub_s_1400CDC00.cpp" />
    <ClCompile Include="Source\RobbedHPCAnimusUEAA_NPEAVCCharacterHZ_14012A7E0.cpp" />
    <ClCompile Include="Source\RobbedHPCGameObjectUEAA_NPEAVCCharacterHZ_14012E0E0.cpp" />
    <ClCompile Include="Source\RobbedHPCGuardTowerUEAA_NPEAVCCharacterHZ_14012FB50.cpp" />
    <ClCompile Include="Source\RobbedHPCMonsterUEAA_NPEAVCCharacterHZ_140142800.cpp" />
    <ClCompile Include="Source\RobbedHPCPlayerUEAA_NPEAVCCharacterHZ_1400A2190.cpp" />
    <ClCompile Include="Source\ScreenShotCLevelQEAAXXZ_1404E0910.cpp" />
    <ClCompile Include="Source\SearchAggroNodeCMonsterAggroMgrQEAAPEAUCAggroNodeP_14017A520.cpp" />
    <ClCompile Include="Source\SearchAttackTargetCHolyKeeperQEAAPEAVCCharacterXZ_140133F70.cpp" />
    <ClCompile Include="Source\SearchBuddyLogin_BUDDY_LISTQEAA_NPEAVCPlayerKPEADZ_1400793A0.cpp" />
    <ClCompile Include="Source\SearchCharacterPathDfAIMgrSA_NPEAVCMonsterAIPEAVCM_140153070.cpp" />
    <ClCompile Include="Source\SearchMoveTargetCHolyKeeperQEAAPEAVCPlayerXZ_140134660.cpp" />
    <ClCompile Include="Source\SearchNearEnemyCAnimusQEAAPEAVCCharacterXZ_140127E90.cpp" />
    <ClCompile Include="Source\SearchNearEnemyCGuardTowerQEAAPEAVCCharacterXZ_140130B10.cpp" />
    <ClCompile Include="Source\SearchNearEnemyCTrapQEAAPEAVCCharacterXZ_14013FE20.cpp" />
    <ClCompile Include="Source\SearchNearPlayerAttackCAnimusQEAAPEAVCCharacterXZ_140127D20.cpp" />
    <ClCompile Include="Source\SearchNearPlayerCMonsterHelperSAPEAVCPlayerPEAVCMo_140158540.cpp" />
    <ClCompile Include="Source\SearchNearPlayerCMonsterQEAAPEAVCCharacterXZ_14026F340.cpp" />
    <ClCompile Include="Source\SearchTargetMovePos_MovingTargetCMonsterHelperSAHP_140159E50.cpp" />
    <ClCompile Include="Source\SearchTargetMovePos_StopTargetCMonsterHelperSAHPEA_1401599B0.cpp" />
    <ClCompile Include="Source\SelectClassCPlayerDBQEAAXEPEAU_class_fldZ_14010B700.cpp" />
    <ClCompile Include="Source\SelectDeleteBufCPotionMgrQEAAHPEAVCPlayer_N1Z_14039FA20.cpp" />
    <ClCompile Include="Source\Select_CharacterBaseInfoByNameCRFWorldDatabaseQEAA_14048AAB0.cpp" />
    <ClCompile Include="Source\Select_CharacterBaseInfoBySerialCRFWorldDatabaseQE_14048E5B0.cpp" />
    <ClCompile Include="Source\Select_CharacterBaseInfoCRFWorldDatabaseQEAAEKPEAU_14048EE20.cpp" />
    <ClCompile Include="Source\Select_CharacterGeneralInfoCRFWorldDatabaseQEAAEKP_14048B290.cpp" />
    <ClCompile Include="Source\Select_CharacterNameCRFWorldDatabaseQEAA_NKPEAD0Z_14048A720.cpp" />
    <ClCompile Include="Source\Select_CharacterReNameCRFWorldDatabaseQEAA_NPEADPE_1404C4340.cpp" />
    <ClCompile Include="Source\Select_CharacterSerialCRFWorldDatabaseQEAA_NPEADPE_140489C00.cpp" />
    <ClCompile Include="Source\Select_CharactersInfoCRFWorldDatabaseQEAA_NKPEAU_w_140489720.cpp" />
    <ClCompile Include="Source\Select_NotArrangeCharacterCRFWorldDatabaseQEAAEKPE_14049E5A0.cpp" />
    <ClCompile Include="Source\Select_PlayerTimeLimitInfoCRFWorldDatabaseQEAAHKPE_1404C7AB0.cpp" />
    <ClCompile Include="Source\Select_PlayerTimeLimitInfoCRFWorldDatabaseQEAAHKPE_1404C7F10.cpp" />
    <ClCompile Include="Source\Select_Player_Last_LogoutTimeCRFWorldDatabaseQEAAH_1404C8320.cpp" />
    <ClCompile Include="Source\Select_SupplementCRFWorldDatabaseQEAAHKPEAU_worldd_1404C47A0.cpp" />
    <ClCompile Include="Source\Select_Supplement_ActPointCRFWorldDatabaseQEAAHKPE_1404C8DF0.cpp" />
    <ClCompile Include="Source\Select_Supplement_ExCRFWorldDatabaseQEAAHKPEAU_wor_1404C6CB0.cpp" />
    <ClCompile Include="Source\SellCompleteCUnmannedTraderUserInfoQEAAEPEAVCPlaye_140356AF0.cpp" />
    <ClCompile Include="Source\SendCRaceBossMsgControllerQEAA_NPEAVCPlayerPEBDZ_1402A05E0.cpp" />
    <ClCompile Include="Source\SendCTotalGuildRankManagerQEAAXKEPEAVCPlayerZ_1402C99B0.cpp" />
    <ClCompile Include="Source\SendCWeeklyGuildRankManagerQEAAXKEPEAVCPlayerZ_1402CD030.cpp" />
    <ClCompile Include="Source\SendData_ChatTransCPlayerQEAAXEKE_NPEADE1Z_1400DCAB0.cpp" />
    <ClCompile Include="Source\SendData_PartyMemberEffectCPlayerQEAAXEGEZ_1400DE2A0.cpp" />
    <ClCompile Include="Source\SendData_PartyMemberFPCPlayerQEAAXXZ_1400DDBB0.cpp" />
    <ClCompile Include="Source\SendData_PartyMemberHPCPlayerQEAAXXZ_1400DDA70.cpp" />
    <ClCompile Include="Source\SendData_PartyMemberInfoCPlayerQEAAXGZ_1400DD7E0.cpp" />
    <ClCompile Include="Source\SendData_PartyMemberInfoToMembersCPlayerQEAAXXZ_1400DD4A0.cpp" />
    <ClCompile Include="Source\SendData_PartyMemberLvCPlayerQEAAXXZ_1400DDE30.cpp" />
    <ClCompile Include="Source\SendData_PartyMemberMaxHFSPCPlayerQEAAXXZ_1400DE120.cpp" />
    <ClCompile Include="Source\SendData_PartyMemberPosCPlayerQEAAXXZ_1400DDF80.cpp" />
    <ClCompile Include="Source\SendData_PartyMemberSPCPlayerQEAAXXZ_1400DDCF0.cpp" />
    <ClCompile Include="Source\SendDecideRecallErrorResultToDestCRecallEffectCont_14024F3A0.cpp" />
    <ClCompile Include="Source\SendEnterResultCReturnGateControllerIEAAXHPEAVCPla_140251000.cpp" />
    <ClCompile Include="Source\SendGetGravityStoneCNormalGuildBattleGuildGUILD_BA_1403E2550.cpp" />
    <ClCompile Include="Source\SendGoalMsgCNormalGuildBattleGUILD_BATTLEIEAAX_NPE_1403E71C0.cpp" />
    <ClCompile Include="Source\SendHolyStoneHPCHolyStoneSystemQEAAXPEAVCPlayerZ_14027FFC0.cpp" />
    <ClCompile Include="Source\SendMsg_AddBagResultCPlayerQEAAXEZ_1400DC320.cpp" />
    <ClCompile Include="Source\SendMsg_AddEffectCPlayerQEAAXGEGKPEADZ_1400E0A80.cpp" />
    <ClCompile Include="Source\SendMsg_AdjustAmountInformCPlayerQEAAXEGKZ_1400DC450.cpp" />
    <ClCompile Include="Source\SendMsg_AlterBoosterCPlayerQEAAXXZ_1400D6630.cpp" />
    <ClCompile Include="Source\SendMsg_AlterContEffectTimeCPlayerQEAAXEZ_1400E07F0.cpp" />
    <ClCompile Include="Source\SendMsg_AlterEquipSPInformCPlayerQEAAXXZ_1400DEE10.cpp" />
    <ClCompile Include="Source\SendMsg_AlterExpInformCPlayerQEAAXXZ_1400DE5B0.cpp" />
    <ClCompile Include="Source\SendMsg_AlterGradeInformCPlayerQEAAXXZ_1400DEF40.cpp" />
    <ClCompile Include="Source\SendMsg_AlterHPInformCPlayerQEAAXXZ_1400DEEA0.cpp" />
    <ClCompile Include="Source\SendMsg_AlterItemDurInformCPlayerQEAAXEG_KZ_1400DE7A0.cpp" />
    <ClCompile Include="Source\SendMsg_AlterMaxDPCPlayerQEAAXXZ_1400DF280.cpp" />
    <ClCompile Include="Source\SendMsg_AlterMoneyInformCPlayerQEAAXEZ_1400DF070.cpp" />
    <ClCompile Include="Source\SendMsg_AlterPvPCashCPlayerQEAAXHZ_1400DECD0.cpp" />
    <ClCompile Include="Source\SendMsg_AlterPvPPointCPlayerQEAAXXZ_1400DEC30.cpp" />
    <ClCompile Include="Source\SendMsg_AlterPvPRankCPlayerQEAAXGKZ_1400DEFD0.cpp" />
    <ClCompile Include="Source\SendMsg_AlterRegionInformCPlayerQEAAXHZ_1400D7030.cpp" />
    <ClCompile Include="Source\SendMsg_AlterSPInformCPlayerQEAAXXZ_1400DED80.cpp" />
    <ClCompile Include="Source\SendMsg_AlterTolCPlayerQEAAXXZ_1400DF130.cpp" />
    <ClCompile Include="Source\SendMsg_AlterTowerHPCPlayerQEAAXGGZ_1400E0750.cpp" />
    <ClCompile Include="Source\SendMsg_AlterTownOrFieldCPlayerQEAAXXZ_1400D6FA0.cpp" />
    <ClCompile Include="Source\SendMsg_AlterUnitBulletInformCPlayerQEAAXEGZ_1400DEB90.cpp" />
    <ClCompile Include="Source\SendMsg_AlterUnitHPInformCPlayerQEAAXEKZ_1400DB830.cpp" />
    <ClCompile Include="Source\SendMsg_AlterWeaponBulletInformCPlayerQEAAXGGZ_1400DEAF0.cpp" />
    <ClCompile Include="Source\SendMsg_Alter_Action_PointCPlayerQEAAXEKZ_1400E8E60.cpp" />
    <ClCompile Include="Source\SendMsg_AMPInvenDownloadResultCPlayerQEAAXXZ_1400D91A0.cpp" />
    <ClCompile Include="Source\SendMsg_AnimusExpInformCPlayerQEAAXXZ_1400DBCC0.cpp" />
    <ClCompile Include="Source\SendMsg_AnimusFPInformCPlayerQEAAXXZ_1400DBBF0.cpp" />
    <ClCompile Include="Source\SendMsg_AnimusHPInformCPlayerQEAAXXZ_1400DBB20.cpp" />
    <ClCompile Include="Source\SendMsg_AnimusInvenChangeCPlayerQEAAXEZ_1400DC0B0.cpp" />
    <ClCompile Include="Source\SendMsg_AnimusModeInformCPlayerQEAAXEZ_1400DBDA0.cpp" />
    <ClCompile Include="Source\SendMsg_AnimusRecallResultCPlayerQEAAXEGPEAVCAnimu_1400DB8D0.cpp" />
    <ClCompile Include="Source\SendMsg_AnimusRecallWaitTimeFreeCPlayerQEAAX_NZ_1400DBE30.cpp" />
    <ClCompile Include="Source\SendMsg_AnimusReturnResultCPlayerQEAAXEGEZ_1400DB9E0.cpp" />
    <ClCompile Include="Source\SendMsg_AnimusTargetResultCPlayerQEAAXEZ_1400DBA90.cpp" />
    <ClCompile Include="Source\SendMsg_ApexInformCPlayerQEAAXKPEADZ_1400E9460.cpp" />
    <ClCompile Include="Source\SendMsg_AskReEnterCDarkHoleChannelQEAAXPEAVCPlayer_14026C9B0.cpp" />
    <ClCompile Include="Source\SendMsg_Assist_ForceCMonsterQEAAXEPEAVCCharacterPE_14014D690.cpp" />
    <ClCompile Include="Source\SendMsg_Assist_SkillCMonsterQEAAXEHPEAVCCharacterP_14014D800.cpp" />
    <ClCompile Include="Source\SendMsg_AttackActEffectCCharacterQEAAXEPEAV1Z_140176F40.cpp" />
    <ClCompile Include="Source\SendMsg_AttackResult_CountCPlayerQEAAXPEAVCAttackZ_1400D4BB0.cpp" />
    <ClCompile Include="Source\SendMsg_AttackResult_ErrorCPlayerQEAAXHZ_1400D4210.cpp" />
    <ClCompile Include="Source\SendMsg_AttackResult_ForceCPlayerQEAAXPEAVCAttackZ_1400D4760.cpp" />
    <ClCompile Include="Source\SendMsg_AttackResult_GenCPlayerQEAAXPEAVCAttackGZ_1400D42A0.cpp" />
    <ClCompile Include="Source\SendMsg_AttackResult_SelfDestructionCPlayerQEAAXPE_1400D4DB0.cpp" />
    <ClCompile Include="Source\SendMsg_AttackResult_SiegeCPlayerQEAAXPEAVCAttackG_1400D5080.cpp" />
    <ClCompile Include="Source\SendMsg_AttackResult_SkillCPlayerQEAAXEPEAVCPlayer_1400D44C0.cpp" />
    <ClCompile Include="Source\SendMsg_AttackResult_UnitCPlayerQEAAXPEAVCAttackEG_1400D49E0.cpp" />
    <ClCompile Include="Source\SendMsg_Attack_SkillCMonsterQEAAXPEAVCMonsterAttac_14014F040.cpp" />
    <ClCompile Include="Source\SendMsg_AwayPartyInvitationQuestionCPlayerQEAAXGZ_1400E7160.cpp" />
    <ClCompile Include="Source\SendMsg_AwayPartyRequestResultCPlayerQEAAXEZ_1400E70D0.cpp" />
    <ClCompile Include="Source\SendMsg_BackTowerResultCPlayerQEAAXEGGZ_1400E06A0.cpp" />
    <ClCompile Include="Source\SendMsg_BackTrapResultCPlayerQEAAXEZ_1400E0F80.cpp" />
    <ClCompile Include="Source\SendMsg_BaseDownloadResultCPlayerQEAAXXZ_1400D8820.cpp" />
    <ClCompile Include="Source\SendMsg_BillingExipreInformCPlayerQEAAXEGZ_1400E4730.cpp" />
    <ClCompile Include="Source\SendMsg_BillingTypeChangeInformCPlayerQEAAXFJPEAU__1400E4640.cpp" />
    <ClCompile Include="Source\SendMsg_BreakdownEquipItemCPlayerQEAAXEGZ_1400DBF50.cpp" />
    <ClCompile Include="Source\SendMsg_BuddhaEventMsgCPlayerQEAAXEZ_1400E7C20.cpp" />
    <ClCompile Include="Source\SendMsg_BuddyAddAnswerResultCPlayerQEAAXE_NKGKPEAD_1400E4AC0.cpp" />
    <ClCompile Include="Source\SendMsg_BuddyAddAskCPlayerQEAAXGKPEADZ_1400E4910.cpp" />
    <ClCompile Include="Source\SendMsg_BuddyAddFailCPlayerQEAAXEPEADZ_1400E49F0.cpp" />
    <ClCompile Include="Source\SendMsg_BuddyDelResultCPlayerQEAAXEKZ_1400E4BD0.cpp" />
    <ClCompile Include="Source\SendMsg_BuddyLoginInformCPlayerQEAAXKEEZ_1400E47D0.cpp" />
    <ClCompile Include="Source\SendMsg_BuddyLogoffInformCPlayerQEAAXKZ_1400E4880.cpp" />
    <ClCompile Include="Source\SendMsg_BuddyNameReNewalCPlayerQEAAXKPEADZ_1400E8500.cpp" />
    <ClCompile Include="Source\SendMsg_BuddyPosInformCPlayerQEAAXKEEZ_1400E4C70.cpp" />
    <ClCompile Include="Source\SendMsg_BuyCashItemModeCPlayerQEAAXXZ_1400E96A0.cpp" />
    <ClCompile Include="Source\SendMsg_BuyItemStoreResultCPlayerQEAAXPEAVCItemSto_1400D70C0.cpp" />
    <ClCompile Include="Source\SendMsg_CancelSuggestResultCPlayerQEAAXEZ_1400E3ED0.cpp" />
    <ClCompile Include="Source\SendMsg_CastVoteResultCPlayerQEAAXEZ_1400E2D90.cpp" />
    <ClCompile Include="Source\SendMsg_ChangeClassCommandCPlayerQEAAXXZ_1400DE860.cpp" />
    <ClCompile Include="Source\SendMsg_ChangeOwnerCParkingUnitQEAAXEPEAVCPlayerZ_140167EC0.cpp" />
    <ClCompile Include="Source\SendMsg_Change_MonsterTargetCMonsterQEAAXPEAVCChar_140148820.cpp" />
    <ClCompile Include="Source\SendMsg_CharacterRenameCashResultCPlayerQEAAX_NEZ_1400E8460.cpp" />
    <ClCompile Include="Source\SendMsg_ChatFarFailureCPlayerQEAAX_NZ_1400DCC30.cpp" />
    <ClCompile Include="Source\SendMsg_Circle_DelEffectCPlayerQEAAXEGE_NZ_1400E0CA0.cpp" />
    <ClCompile Include="Source\SendMsg_ClassSkillResultCPlayerQEAAXEPEAU_CHRIDGZ_1400DFB00.cpp" />
    <ClCompile Include="Source\SendMsg_ClearDarkHoleCPlayerQEAAXEZ_1400DA9F0.cpp" />
    <ClCompile Include="Source\SendMsg_CombineItemExAcceptResultCPlayerQEAAXPEAU__1400D82D0.cpp" />
    <ClCompile Include="Source\SendMsg_CombineItemExResultCPlayerQEAAXPEAU_combin_1400D8250.cpp" />
    <ClCompile Include="Source\SendMsg_CombineItemResultCPlayerQEAAXEKPEAU_db_con_1400D8000.cpp" />
    <ClCompile Include="Source\SendMsg_CombineLendItemResultCPlayerQEAAXEKPEAU_db_1400D8100.cpp" />
    <ClCompile Include="Source\SendMsg_ConnectNewUserPatriarchElectProcessorQEAAX_1402BB050.cpp" />
    <ClCompile Include="Source\SendMsg_CreateHolyMasterCHolyStoneSystemQEAAXPEAVC_14027F280.cpp" />
    <ClCompile Include="Source\SendMsg_CreateTowerResultCPlayerQEAAXEKZ_1400E05F0.cpp" />
    <ClCompile Include="Source\SendMsg_CreateTrapResultCPlayerQEAAXEKZ_1400E0D70.cpp" />
    <ClCompile Include="Source\SendMsg_CumDownloadResultCPlayerQEAAXXZ_1400D9360.cpp" />
    <ClCompile Include="Source\SendMsg_CuttingCompleteResultCPlayerQEAAXEZ_1400DCA10.cpp" />
    <ClCompile Include="Source\SendMsg_DamageResultCPlayerQEAAXPEAU_db_con_STORAG_1400D5290.cpp" />
    <ClCompile Include="Source\SendMsg_DarkHoleOpenFailCPlayerQEAAXHEZ_1400DA760.cpp" />
    <ClCompile Include="Source\SendMsg_DarkHoleOpenResultCPlayerQEAAXHHEGKZ_1400DA5B0.cpp" />
    <ClCompile Include="Source\SendMsg_DarkHoleRewardMessageCPlayerQEAAXPEAU_db_c_1400E7330.cpp" />
    <ClCompile Include="Source\SendMsg_DelEffectCPlayerQEAAXEGEZ_1400E0BB0.cpp" />
    <ClCompile Include="Source\SendMsg_DeleteStorageInformCPlayerQEAAXEGZ_1400DC3B0.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCPlayerQEAAXXZ_1400D5950.cpp" />
    <ClCompile Include="Source\SendMsg_DieCPlayerQEAAXXZ_1400D59F0.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeAccomplishInformCPlayerQEAAX_NGZ_1400E1A30.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeAddInformCPlayerQEAAXEPEAU_db_con_ST_1400E15A0.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeAddResultCPlayerQEAAXEZ_1400E1510.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeAnswerResultCPlayerQEAAXEZ_1400E1140.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeAskInformCPlayerQEAAXPEAV1Z_1400E10A0.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeAskResultCPlayerQEAAXEZ_1400E1010.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeBetInformCPlayerQEAAXEKZ_1400E1880.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeBetResultCPlayerQEAAXEZ_1400E17F0.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeCancleInformCPlayerQEAAXXZ_1400E1380.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeCancleResultCPlayerQEAAXEZ_1400E12F0.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeCloseInformCPlayerQEAAXEZ_1400E1B10.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeDelInformCPlayerQEAAXEZ_1400E1750.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeDelResultCPlayerQEAAXEZ_1400E16C0.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeLockInformCPlayerQEAAXXZ_1400E1490.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeLockResultCPlayerQEAAXEZ_1400E1400.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeOKInformCPlayerQEAAXXZ_1400E19B0.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeOKResultCPlayerQEAAXEZ_1400E1920.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeStartInformCPlayerQEAAXPEAV10PEAKZ_1400E11D0.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeUnitAddInformCPlayerQEAAXGPEAU_LIST__1400E1CF0.cpp" />
    <ClCompile Include="Source\SendMsg_DTradeUnitInfoInformCPlayerQEAAXEPEAU_LIST_1400E1BA0.cpp" />
    <ClCompile Include="Source\SendMsg_EconomyHistoryInformCPlayerQEAAXXZ_1400D77C0.cpp" />
    <ClCompile Include="Source\SendMsg_EconomyRateInformCPlayerQEAAX_NZ_1400D7900.cpp" />
    <ClCompile Include="Source\SendMsg_EmbellishResultCPlayerQEAAXEZ_1400D7DC0.cpp" />
    <ClCompile Include="Source\SendMsg_EnterDarkHoleCPlayerQEAAXEKZ_1400DA800.cpp" />
    <ClCompile Include="Source\SendMsg_EquipItemLevelLimitCPlayerQEAAXHZ_1400E8930.cpp" />
    <ClCompile Include="Source\SendMsg_EquipPartChangeCPlayerQEAAXEZ_1400D5340.cpp" />
    <ClCompile Include="Source\SendMsg_EquipPartResultCPlayerQEAAXEZ_1400D7D30.cpp" />
    <ClCompile Include="Source\SendMsg_ExchangeItemResultCPlayerQEAAXEPEAU_db_con_1400D8350.cpp" />
    <ClCompile Include="Source\SendMsg_ExchangeLendItemResultCPlayerQEAAXEPEAU_db_1400D8440.cpp" />
    <ClCompile Include="Source\SendMsg_ExchangeMoneyResultCPlayerQEAAXEZ_1400D7700.cpp" />
    <ClCompile Include="Source\SendMsg_ExitWorldResultCPlayerQEAAXEZ_1400D6C50.cpp" />
    <ClCompile Include="Source\SendMsg_ExtTrunkExtendResultCPlayerQEAAXEEEZ_1400E35E0.cpp" />
    <ClCompile Include="Source\SendMsg_FanfareItemCPlayerQEAAXEPEAU_db_con_STORAG_1400E6BC0.cpp" />
    <ClCompile Include="Source\SendMsg_FcitemInformCPlayerQEAAXGKZ_1400D6D80.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCPlayerUEAAXHZ_1400D5A80.cpp" />
    <ClCompile Include="Source\SendMsg_ForceDownloadResultCPlayerQEAAXXZ_1400D9510.cpp" />
    <ClCompile Include="Source\SendMsg_ForceInvenChangeCPlayerQEAAXEZ_1400DC020.cpp" />
    <ClCompile Include="Source\SendMsg_ForceResultCPlayerQEAAXEPEAU_CHRIDPEAU_db__1400DF770.cpp" />
    <ClCompile Include="Source\SendMsg_GestureInformCPlayerQEAAXEZ_1400DA520.cpp" />
    <ClCompile Include="Source\SendMsg_GiveupDarkHoleCPlayerQEAAXEZ_1400DA900.cpp" />
    <ClCompile Include="Source\SendMsg_GM_GreetingCPlayerQEAAXPEAD0Z_1400E60E0.cpp" />
    <ClCompile Include="Source\SendMsg_GotoBasePortalResultCPlayerQEAAXEZ_1400D6A90.cpp" />
    <ClCompile Include="Source\SendMsg_GotoRecallResultCPlayerQEAAXEEPEAMEZ_1400D6B80.cpp" />
    <ClCompile Include="Source\SendMsg_GroupTargetInformCPlayerQEAAXEPEADZ_1400E2C30.cpp" />
    <ClCompile Include="Source\SendMsg_GuildEstablishFailCPlayerQEAAXEZ_1400E3B90.cpp" />
    <ClCompile Include="Source\SendMsg_GuildForceLeaveBoradoriCPlayerQEAAXXZ_1400E7740.cpp" />
    <ClCompile Include="Source\SendMsg_GuildJoinAcceptFailCPlayerQEAAXEKZ_1400E4000.cpp" />
    <ClCompile Include="Source\SendMsg_GuildJoinApplyCancelResultCPlayerQEAAXEZ_1400E3D20.cpp" />
    <ClCompile Include="Source\SendMsg_GuildJoinApplyRejectInformCPlayerQEAAXXZ_1400E40A0.cpp" />
    <ClCompile Include="Source\SendMsg_GuildJoinApplyResultCPlayerQEAAXEPEAVCGuil_1400E3C20.cpp" />
    <ClCompile Include="Source\SendMsg_GuildJoinOtherInformCPlayerQEAAXXZ_1400E41E0.cpp" />
    <ClCompile Include="Source\SendMsg_GuildManageResultCPlayerQEAAXEZ_1400E5D70.cpp" />
    <ClCompile Include="Source\SendMsg_GuildMasterEffectCPlayerQEAAXEEEEEEZ_1400E7650.cpp" />
    <ClCompile Include="Source\SendMsg_GuildPushMoneyResultCPlayerQEAAXEZ_1400E4120.cpp" />
    <ClCompile Include="Source\SendMsg_GuildRoomEnterResultCPlayerQEAAXEEEGPEAMJZ_1400E59E0.cpp" />
    <ClCompile Include="Source\SendMsg_GuildRoomOutResultCPlayerQEAAXEEGPEAMZ_1400E5AF0.cpp" />
    <ClCompile Include="Source\SendMsg_GuildRoomRentResultCPlayerQEAAXEEEZ_1400E5930.cpp" />
    <ClCompile Include="Source\SendMsg_GuildRoomRestTimeResultCPlayerQEAAXXZ_1400E5BF0.cpp" />
    <ClCompile Include="Source\SendMsg_GuildSelfLeaveResultCPlayerQEAAXEZ_1400E3DB0.cpp" />
    <ClCompile Include="Source\SendMsg_GuildSetHonorResultCPlayerQEAAXEZ_1400E75C0.cpp" />
    <ClCompile Include="Source\SendMsg_GUILD_GreetingCPlayerQEAAXPEAD0Z_1400E63E0.cpp" />
    <ClCompile Include="Source\SendMsg_HonorGuildMarkCPlayerQEAAXEZ_1400E7860.cpp" />
    <ClCompile Include="Source\SendMsg_HSKQuestActCumCPlayerQEAAXXZ_1400DF310.cpp" />
    <ClCompile Include="Source\SendMsg_HSKQuestSuccCPlayerQEAAXE_NZ_1400DF450.cpp" />
    <ClCompile Include="Source\SendMsg_InformTaxIncomeCPlayerQEAAXEKPEADZ_1400E74A0.cpp" />
    <ClCompile Include="Source\SendMsg_Init_Action_PointCPlayerQEAAXXZ_1400E8F00.cpp" />
    <ClCompile Include="Source\SendMsg_InsertItemInformCPlayerQEAAXEPEAU_db_con_S_1400E0440.cpp" />
    <ClCompile Include="Source\SendMsg_InsertNewQuestCPlayerQEAAXEPEAU_LIST_QUEST_1400E1ED0.cpp" />
    <ClCompile Include="Source\SendMsg_InsertNextQuestCPlayerQEAAXEPEAU_LIST_QUES_1400E1FB0.cpp" />
    <ClCompile Include="Source\SendMsg_InsertQuestFailureCPlayerQEAAXEKEZ_1400E2140.cpp" />
    <ClCompile Include="Source\SendMsg_InsertQuestItemInformCPlayerQEAAXPEAU_db_c_1400E0530.cpp" />
    <ClCompile Include="Source\SendMsg_InvenDownloadResultCPlayerQEAAXXZ_1400D8F70.cpp" />
    <ClCompile Include="Source\SendMsg_ItemDowngradeCPlayerQEAAXEZ_1400D85F0.cpp" />
    <ClCompile Include="Source\SendMsg_ItemStorageRefreshCPlayerQEAAXEZ_1400D6E20.cpp" />
    <ClCompile Include="Source\SendMsg_ItemUpgradeCPlayerQEAAXEZ_1400D8560.cpp" />
    <ClCompile Include="Source\SendMsg_JadeEffectErrCPlayerQEAAXEZ_1400E8330.cpp" />
    <ClCompile Include="Source\SendMsg_LastEffectChangeInformCCharacterQEAAXXZ_140176EA0.cpp" />
    <ClCompile Include="Source\SendMsg_LeaderChangeCDarkHoleChannelQEAAXPEAVCPlay_14026B540.cpp" />
    <ClCompile Include="Source\SendMsg_LendItemTimeExpiredCPlayerQEAAXEGZ_1400E8AF0.cpp" />
    <ClCompile Include="Source\SendMsg_LevelCPlayerQEAAXHZ_1400D63F0.cpp" />
    <ClCompile Include="Source\SendMsg_LevelUpCAnimusQEAAXXZ_14012A670.cpp" />
    <ClCompile Include="Source\SendMsg_LinkBoardDownloadResultCPlayerQEAAXXZ_1400DA1C0.cpp" />
    <ClCompile Include="Source\SendMsg_MacroRequestCPlayerQEAAXXZ_1400E4F00.cpp" />
    <ClCompile Include="Source\SendMsg_MadeTrapNumInformCPlayerQEAAXEZ_1400E0E20.cpp" />
    <ClCompile Include="Source\SendMsg_MakeItemCheatSendButtonEnableCPlayerQEAAX__1400D7EE0.cpp" />
    <ClCompile Include="Source\SendMsg_MakeItemResultCPlayerQEAAXEZ_1400D7F70.cpp" />
    <ClCompile Include="Source\SendMsg_MapEnvInformCPlayerQEAAXEKZ_1400E4D20.cpp" />
    <ClCompile Include="Source\SendMsg_MapOutCPlayerQEAAXEEZ_1400D6950.cpp" />
    <ClCompile Include="Source\SendMsg_MaxHFSPCPlayerQEAAXXZ_1400D6480.cpp" />
    <ClCompile Include="Source\SendMsg_MaxPvpPointInformCPlayerQEAAXHZ_1400E9500.cpp" />
    <ClCompile Include="Source\SendMsg_MemberInfoCDarkHoleChannelQEAAXPEAVCPlayer_14026BB00.cpp" />
    <ClCompile Include="Source\SendMsg_MineCancleCPlayerQEAAXXZ_1400DC620.cpp" />
    <ClCompile Include="Source\SendMsg_MineCompleteResultCPlayerQEAAXEEGEGZ_1400DC6A0.cpp" />
    <ClCompile Include="Source\SendMsg_MineStartResultCPlayerQEAAXEZ_1400DC590.cpp" />
    <ClCompile Include="Source\SendMsg_MissionInfoCDarkHoleChannelQEAAXPEAVCPlaye_14026B7C0.cpp" />
    <ClCompile Include="Source\SendMsg_MonsterAggroDataCPlayerQEAAXPEAVCCharacter_1400E5270.cpp" />
    <ClCompile Include="Source\SendMsg_MoveErrorCPlayerQEAAXEZ_1400D6340.cpp" />
    <ClCompile Include="Source\SendMsg_MoveNextCPlayerQEAAX_NZ_1400D5DD0.cpp" />
    <ClCompile Include="Source\SendMsg_MovePortalCPlayerQEAAXEEEPEAM_NZ_1400D9F90.cpp" />
    <ClCompile Include="Source\SendMsg_MovePortalCPlayerQEAAXEPEAMEZ_1400DA070.cpp" />
    <ClCompile Include="Source\SendMsg_MovePortalCReturnGateIEAAXPEAVCPlayerZ_140168EE0.cpp" />
    <ClCompile Include="Source\SendMsg_MoveToOwnStoneMapInformCPlayerQEAAXEZ_1400E5CE0.cpp" />
    <ClCompile Include="Source\SendMsg_MoveToOwnStoneMapResultCPlayerQEAAXEEPEAMZ_1400E5C00.cpp" />
    <ClCompile Include="Source\SendMsg_NewMemberCDarkHoleChannelQEAAXPEAVCPlayer__14026B2E0.cpp" />
    <ClCompile Include="Source\SendMsg_NewMovePotionResultCPlayerQEAAXXZ_1400E9620.cpp" />
    <ClCompile Include="Source\SendMsg_NewViewOtherCPlayerQEAAXEZ_1400D40C0.cpp" />
    <ClCompile Include="Source\SendMsg_NotifyEffectForGetItemCPlayerQEAAXEKPEADPE_1400E92B0.cpp" />
    <ClCompile Include="Source\SendMsg_NotifyGetExpInfoCPlayerQEAAXNNNZ_1400DE640.cpp" />
    <ClCompile Include="Source\SendMsg_Notify_ExceptFromRaceRankingCPlayerQEAAXHZ_1400E7D50.cpp" />
    <ClCompile Include="Source\SendMsg_Notify_Get_Golden_BoxCPlayerQEAAXEKPEADPEA_1400E9100.cpp" />
    <ClCompile Include="Source\SendMsg_Notify_Gravity_Stone_Owner_DieCPlayerQEAAX_1400E42B0.cpp" />
    <ClCompile Include="Source\SendMsg_Notify_Me_Get_Golden_BoxCPlayerQEAAXEPEAU__1400E8FD0.cpp" />
    <ClCompile Include="Source\SendMsg_NPCLinkItemCheckResultCPlayerQEAAXEPEAU_ST_1400E7B40.cpp" />
    <ClCompile Include="Source\SendMsg_NpcQuestHistoryInformCPlayerQEAAXEZ_1400DF580.cpp" />
    <ClCompile Include="Source\SendMsg_NpcQuestListResultCPlayerQEAAXPEAU_NPCQues_1400DF670.cpp" />
    <ClCompile Include="Source\SendMsg_OfferSuggestResultCPlayerQEAAXEZ_1400E3E40.cpp" />
    <ClCompile Include="Source\SendMsg_OffPartResultCPlayerQEAAXEZ_1400D7E50.cpp" />
    <ClCompile Include="Source\SendMsg_OreCuttingResultCPlayerQEAAXEEKZ_1400DC790.cpp" />
    <ClCompile Include="Source\SendMsg_OreIntoBagResultCPlayerQEAAXEGEKZ_1400DC950.cpp" />
    <ClCompile Include="Source\SendMsg_OreTransferCountCPlayerQEAAXXZ_1400E8780.cpp" />
    <ClCompile Include="Source\SendMsg_OtherShapeAllCPlayerQEAAXPEAV1Z_1400D55B0.cpp" />
    <ClCompile Include="Source\SendMsg_OtherShapeErrorCPlayerQEAAXPEAV1EZ_1400D58B0.cpp" />
    <ClCompile Include="Source\SendMsg_OtherShapePartCPlayerQEAAXPEAV1Z_1400D5690.cpp" />
    <ClCompile Include="Source\SendMsg_OtherShapePartEx_CashChangeCPlayerQEAAXPEA_1400D5770.cpp" />
    <ClCompile Include="Source\SendMsg_PartyAlterLootShareResultCPlayerQEAAXEZ_1400DE400.cpp" />
    <ClCompile Include="Source\SendMsg_PartyDisjointResultCPlayerQEAAXEZ_1400DD2E0.cpp" />
    <ClCompile Include="Source\SendMsg_PartyJoinApplicationQuestionCPlayerQEAAXPE_1400DD070.cpp" />
    <ClCompile Include="Source\SendMsg_PartyJoinFailLevelCPlayerQEAAXXZ_1400DD110.cpp" />
    <ClCompile Include="Source\SendMsg_PartyJoinInvitationQuestionCPlayerQEAAXGZ_1400DCCC0.cpp" />
    <ClCompile Include="Source\SendMsg_PartyJoinJoinerResultCPlayerQEAAXXZ_1400DCE50.cpp" />
    <ClCompile Include="Source\SendMsg_PartyJoinMemberResultCPlayerQEAAXPEAVCPart_1400DCD60.cpp" />
    <ClCompile Include="Source\SendMsg_PartyLeaveCompulsionResultCPlayerQEAAXPEAV_1400DD240.cpp" />
    <ClCompile Include="Source\SendMsg_PartyLeaveSelfResultCPlayerQEAAXPEAVCParty_1400DD190.cpp" />
    <ClCompile Include="Source\SendMsg_PartyLockResultCPlayerQEAAXEZ_1400DD410.cpp" />
    <ClCompile Include="Source\SendMsg_PartyLootItemInformCPlayerQEAAXKEGEZ_1400DE490.cpp" />
    <ClCompile Include="Source\SendMsg_PartySuccessResultCPlayerQEAAXPEAVCPartyPl_1400DD370.cpp" />
    <ClCompile Include="Source\SendMsg_PatriarchInformClassOrderProcessorQEAAXPEA_1402B8F50.cpp" />
    <ClCompile Include="Source\SendMsg_PcRoomCharClassCPlayerQEAAXKZ_1400E8810.cpp" />
    <ClCompile Include="Source\SendMsg_PcRoomErrorCPlayerQEAAXEZ_1400E88A0.cpp" />
    <ClCompile Include="Source\SendMsg_PopMemberCDarkHoleChannelQEAAXPEAVCPlayer__14026B430.cpp" />
    <ClCompile Include="Source\SendMsg_PostContentCPlayerQEAAXEKPEADEG_KKKZ_1400E68A0.cpp" />
    <ClCompile Include="Source\SendMsg_PostDeleteCPlayerQEAAXEKZ_1400E6A80.cpp" />
    <ClCompile Include="Source\SendMsg_PostDeliveryCPlayerQEAAXEKPEAD0_N1EZ_1400E6620.cpp" />
    <ClCompile Include="Source\SendMsg_PostItemGoldCPlayerQEAAXEZ_1400E69F0.cpp" />
    <ClCompile Include="Source\SendMsg_PostReturnConfirmCPlayerQEAAXEKZ_1400E6B20.cpp" />
    <ClCompile Include="Source\SendMsg_PostReturnCPlayerQEAAXEKPEAD00EG_KKKZ_1400E6740.cpp" />
    <ClCompile Include="Source\SendMsg_PostSendReplyCPlayerQEAAXEZ_1400E6560.cpp" />
    <ClCompile Include="Source\SendMsg_PotionDelayTimeCPlayerQEAAXPEAKKZ_1400E8660.cpp" />
    <ClCompile Include="Source\SendMsg_PotionDivisionCPlayerQEAAXGEGEHZ_1400D8750.cpp" />
    <ClCompile Include="Source\SendMsg_PotionSeparationCPlayerQEAAXGEGEHZ_1400D8680.cpp" />
    <ClCompile Include="Source\SendMsg_PremiumCashItemUseCPlayerQEAAXGZ_1400E9590.cpp" />
    <ClCompile Include="Source\SendMsg_ProposeVoteResultCPlayerQEAAXEZ_1400E2D00.cpp" />
    <ClCompile Include="Source\SendMsg_PvpRankListVersionUpCPlayerQEAAXEZ_1400E2640.cpp" />
    <ClCompile Include="Source\SendMsg_QuestCompleteCPlayerQEAAXEZ_1400E21F0.cpp" />
    <ClCompile Include="Source\SendMsg_QuestDownloadResultCPlayerQEAAXXZ_1400D96C0.cpp" />
    <ClCompile Include="Source\SendMsg_QuestFailureCPlayerQEAAXEEZ_1400E2330.cpp" />
    <ClCompile Include="Source\SendMsg_QuestGiveUpResultCPlayerQEAAXEZ_1400E23D0.cpp" />
    <ClCompile Include="Source\SendMsg_QuestHistoryDownloadResultCPlayerQEAAXXZ_1400D9860.cpp" />
    <ClCompile Include="Source\SendMsg_QuestInfoCDarkHoleChannelQEAAXPEAVCPlayerZ_14026B640.cpp" />
    <ClCompile Include="Source\SendMsg_QuestProcessCPlayerQEAAXEEGZ_1400E2280.cpp" />
    <ClCompile Include="Source\SendMsg_RaceBattlePeneltyCPlayerQEAAXHEZ_1400E77C0.cpp" />
    <ClCompile Include="Source\SendMsg_RaceBossCryMsgCPlayerQEAAXXZ_1400E6FF0.cpp" />
    <ClCompile Include="Source\SendMsg_RaceChatCGoldenBoxItemMgrQEAAXPEAVCPlayerP_140414F60.cpp" />
    <ClCompile Include="Source\SendMsg_RaceTopInformCPlayerQEAAX_NZ_1400DF1F0.cpp" />
    <ClCompile Include="Source\SendMsg_RACE_GreetingCPlayerQEAAXPEAD0Z_1400E6260.cpp" />
    <ClCompile Include="Source\SendMsg_RadarCharSearchResultCPlayerQEAAXXZ_1400E5EB0.cpp" />
    <ClCompile Include="Source\SendMsg_RadarDelayTimeCPlayerQEAAXKZ_1400E6050.cpp" />
    <ClCompile Include="Source\SendMsg_RealMovePointCPlayerUEAAXHZ_1400D5C00.cpp" />
    <ClCompile Include="Source\SendMsg_RecoverCPlayerQEAAXXZ_1400D6540.cpp" />
    <ClCompile Include="Source\SendMsg_RecvHSKQuestCPlayerQEAAXXZ_1400E24F0.cpp" />
    <ClCompile Include="Source\SendMsg_ReEnterAskCPlayerQEAAXGKZ_1400DAB70.cpp" />
    <ClCompile Include="Source\SendMsg_ReEnterDarkHoleResultCPlayerQEAAXEZ_1400DAAE0.cpp" />
    <ClCompile Include="Source\SendMsg_RefeshGroupTargetPositionCPlayerQEAAXEZ_1400E2B00.cpp" />
    <ClCompile Include="Source\SendMsg_RegistBindResultCPlayerQEAAXEZ_1400DA130.cpp" />
    <ClCompile Include="Source\SendMsg_ReleaseGroupTargetObjectResultCPlayerQEAAX_1400E2A70.cpp" />
    <ClCompile Include="Source\SendMsg_ReleaseSiegeModeResultCPlayerQEAAXEZ_1400E4480.cpp" />
    <ClCompile Include="Source\SendMsg_RemainOreRateCPlayerQEAAXXZ_1400E85D0.cpp" />
    <ClCompile Include="Source\SendMsg_RemainTimeInformCPlayerQEAAXFJPEAU_SYSTEMT_1400E4570.cpp" />
    <ClCompile Include="Source\SendMsg_ResDivisionCPlayerQEAAXEPEAU_db_con_STORAG_1400DC220.cpp" />
    <ClCompile Include="Source\SendMsg_ResSeparationCPlayerQEAAXEPEAU_db_con_STOR_1400DC140.cpp" />
    <ClCompile Include="Source\SendMsg_ResultChangeTaxRateCPlayerQEAAXEEZ_1400E7400.cpp" />
    <ClCompile Include="Source\SendMsg_ResultNpcQuestCPlayerQEAAX_NZ_1400DF4F0.cpp" />
    <ClCompile Include="Source\SendMsg_ResurrectCPlayerQEAAXE_NZ_1400D60B0.cpp" />
    <ClCompile Include="Source\SendMsg_ResurrectInformCPlayerQEAAXXZ_1400D61C0.cpp" />
    <ClCompile Include="Source\SendMsg_RevivalCPlayerQEAAXE_NZ_1400D5F30.cpp" />
    <ClCompile Include="Source\SendMsg_RevivalOfJadeCPlayerQEAAXGZ_1400E82B0.cpp" />
    <ClCompile Include="Source\SendMsg_RewardAddItemCPlayerQEAAXPEAU_db_con_STORA_1400DE9E0.cpp" />
    <ClCompile Include="Source\SendMsg_RobedHPCCharacterQEAAXPEBV1GZ_140178910.cpp" />
    <ClCompile Include="Source\SendMsg_SelectClassResultCPlayerQEAAXEGZ_1400DE8F0.cpp" />
    <ClCompile Include="Source\SendMsg_SelectQuestRewardCPlayerQEAAXEZ_1400E2460.cpp" />
    <ClCompile Include="Source\SendMsg_SelectWaitedQuestCPlayerQEAAXEKEZ_1400E2090.cpp" />
    <ClCompile Include="Source\SendMsg_SellItemStoreResultCPlayerQEAAXPEAVCItemSt_1400D7490.cpp" />
    <ClCompile Include="Source\SendMsg_SetDPInformCPlayerQEAAXXZ_1400E03A0.cpp" />
    <ClCompile Include="Source\SendMsg_SetFPInformCPlayerQEAAXXZ_1400E01C0.cpp" />
    <ClCompile Include="Source\SendMsg_SetGroupMapPointCPlayerQEAAXEEEPEAMEZ_1400E7250.cpp" />
    <ClCompile Include="Source\SendMsg_SetGroupTargetObjectResultCPlayerQEAAXEEZ_1400E28F0.cpp" />
    <ClCompile Include="Source\SendMsg_SetHPInformCPlayerUEAAXXZ_1400E0260.cpp" />
    <ClCompile Include="Source\SendMsg_SetItemCheckResultCPlayerQEAAXEKEZ_1400E51C0.cpp" />
    <ClCompile Include="Source\SendMsg_SetSPInformCPlayerQEAAXXZ_1400E0300.cpp" />
    <ClCompile Include="Source\SendMsg_SetTargetObjectResultCPlayerQEAAXE_NZ_1400E27A0.cpp" />
    <ClCompile Include="Source\SendMsg_SFDelayRequestCPlayerQEAAXXZ_1400E78F0.cpp" />
    <ClCompile Include="Source\SendMsg_SkillResultCPlayerQEAAXEPEAU_CHRIDEHZ_1400DF950.cpp" />
    <ClCompile Include="Source\SendMsg_SpecialDownloadResultCPlayerQEAAXXZ_1400D99F0.cpp" />
    <ClCompile Include="Source\SendMsg_StartContSFCPlayerQEAAXPEAU_sf_continousZ_1400E09B0.cpp" />
    <ClCompile Include="Source\SendMsg_StartNewPosCPlayerQEAAXEZ_1400DBEC0.cpp" />
    <ClCompile Include="Source\SendMsg_StartShoppingCPlayerQEAAXXZ_1400E1E50.cpp" />
    <ClCompile Include="Source\SendMsg_StateInformCPlayerQEAAX_KZ_1400D69F0.cpp" />
    <ClCompile Include="Source\SendMsg_StatInformCPlayerQEAAXEKEZ_1400D6CD0.cpp" />
    <ClCompile Include="Source\SendMsg_StopCPlayerQEAAX_NZ_1400D6240.cpp" />
    <ClCompile Include="Source\SendMsg_StoreLimitItemAmountInfoCPlayerQEAAXKPEAU__1400D75C0.cpp" />
    <ClCompile Include="Source\SendMsg_StoreListResultCPlayerQEAAXXZ_1400D9DB0.cpp" />
    <ClCompile Include="Source\SendMsg_StunInformCCharacterUEAAXXZ_140176E00.cpp" />
    <ClCompile Include="Source\SendMsg_TakeAddResultCPlayerQEAAXEPEAU_db_con_STOR_1400D6800.cpp" />
    <ClCompile Include="Source\SendMsg_TakeNewResultCPlayerQEAAXEPEAU_db_con_STOR_1400D66E0.cpp" />
    <ClCompile Include="Source\SendMsg_TalikCrystalExchangeResultCPlayerQEAAXEEPE_1400E94E0.cpp" />
    <ClCompile Include="Source\SendMsg_TargetObjectHPInformCPlayerQEAAXXZ_1400E26D0.cpp" />
    <ClCompile Include="Source\SendMsg_TeleportErrorCPlayerQEAAXEKZ_1400E7CB0.cpp" />
    <ClCompile Include="Source\SendMsg_TestAttackResultCPlayerQEAAXEEGEEPEAFZ_1400D4F60.cpp" />
    <ClCompile Include="Source\SendMsg_ThrowSkillResultCPlayerQEAAXEPEAU_CHRIDEZ_1400DFCA0.cpp" />
    <ClCompile Include="Source\SendMsg_ThrowStorageResultCPlayerQEAAXEZ_1400D68C0.cpp" />
    <ClCompile Include="Source\SendMsg_ThrowUnitResultCPlayerQEAAXEPEAU_CHRIDGZ_1400DFF30.cpp" />
    <ClCompile Include="Source\SendMsg_TLStatusInfoCPlayerQEAAXKEZ_1400E8B90.cpp" />
    <ClCompile Include="Source\SendMsg_TLStatusPenaltyCPlayerQEAAXEZ_1400E8DD0.cpp" />
    <ClCompile Include="Source\SendMsg_TowerContinueCPlayerQEAAXGPEAVCGuardTowerZ_1400E0EB0.cpp" />
    <ClCompile Include="Source\SendMsg_to_webagent_about_last_attacker_for_keeper_14027C8A0.cpp" />
    <ClCompile Include="Source\SendMsg_TransformSiegeModeResultCPlayerQEAAXEZ_1400E4340.cpp" />
    <ClCompile Include="Source\SendMsg_TransShipRenewTicketResultCPlayerQEAAXEZ_1400DC500.cpp" />
    <ClCompile Include="Source\SendMsg_TrunkChangPasswdResultCPlayerQEAAXEZ_1400E3490.cpp" />
    <ClCompile Include="Source\SendMsg_TrunkDownloadResultCPlayerQEAAXEZ_1400E2EC0.cpp" />
    <ClCompile Include="Source\SendMsg_TrunkEstResultCPlayerQEAAXEKZ_1400E3390.cpp" />
    <ClCompile Include="Source\SendMsg_TrunkExtendResultCPlayerQEAAXEEKKZ_1400E3520.cpp" />
    <ClCompile Include="Source\SendMsg_TrunkHintAnswerResultCPlayerQEAAXEPEADZ_1400E3AB0.cpp" />
    <ClCompile Include="Source\SendMsg_TrunkIoMoneyResultCPlayerQEAAXENNKKKZ_1400E3930.cpp" />
    <ClCompile Include="Source\SendMsg_TrunkIoResultCPlayerQEAAXEEKKZ_1400E3870.cpp" />
    <ClCompile Include="Source\SendMsg_TrunkPotionDivisionCPlayerQEAAXGGGGHZ_1400E37A0.cpp" />
    <ClCompile Include="Source\SendMsg_TrunkPwHintIndexResultCPlayerQEAAXEEZ_1400E3A10.cpp" />
    <ClCompile Include="Source\SendMsg_TrunkResDivisionCPlayerQEAAXEPEAU_db_con_S_1400E36A0.cpp" />
    <ClCompile Include="Source\SendMsg_UILock_FindPW_ResultCPlayerQEAAXEPEADEZ_1400E7FB0.cpp" />
    <ClCompile Include="Source\SendMsg_UILock_Init_Request_ToAccountCPlayerQEAAXK_1400E80B0.cpp" />
    <ClCompile Include="Source\SendMsg_UILock_Init_ResultCPlayerQEAAXEZ_1400E7DE0.cpp" />
    <ClCompile Include="Source\SendMsg_UILock_Login_ResultCPlayerQEAAXEEZ_1400E7E80.cpp" />
    <ClCompile Include="Source\SendMsg_UILock_Update_Request_ToAccountCPlayerQEAA_1400E81B0.cpp" />
    <ClCompile Include="Source\SendMsg_UILock_Update_ResultCPlayerQEAAXEZ_1400E7F20.cpp" />
    <ClCompile Include="Source\SendMsg_UnitAlterFeeInformCPlayerQEAAXEKZ_1400DB5D0.cpp" />
    <ClCompile Include="Source\SendMsg_UnitBulletFillResultCPlayerQEAAXEEPEAGPEAK_1400DB090.cpp" />
    <ClCompile Include="Source\SendMsg_UnitBulletReplaceResultCPlayerQEAAXEZ_1400DB670.cpp" />
    <ClCompile Include="Source\SendMsg_UnitDeliveryResultCPlayerQEAAXEEKKZ_1400DB310.cpp" />
    <ClCompile Include="Source\SendMsg_UnitDestroyCPlayerQEAAXEZ_1400DB700.cpp" />
    <ClCompile Include="Source\SendMsg_UnitForceReturnInformCPlayerQEAAXEKZ_1400DB790.cpp" />
    <ClCompile Include="Source\SendMsg_UnitFrameBuyResultCPlayerQEAAXEEEGGPEAKZ_1400DAC10.cpp" />
    <ClCompile Include="Source\SendMsg_UnitFrameRepairResultCPlayerQEAAXEEKKZ_1400DAFB0.cpp" />
    <ClCompile Include="Source\SendMsg_UnitLeaveResultCPlayerQEAAXEZ_1400DB540.cpp" />
    <ClCompile Include="Source\SendMsg_UnitPackFillResultCPlayerQEAAXEEEPEAU__lis_1400DB1D0.cpp" />
    <ClCompile Include="Source\SendMsg_UnitPartTuningResultCPlayerQEAAXEEPEAHZ_1400DAE30.cpp" />
    <ClCompile Include="Source\SendMsg_UnitReturnResultCPlayerQEAAXEKZ_1400DB3F0.cpp" />
    <ClCompile Include="Source\SendMsg_UnitRideChangeCPlayerQEAAX_NPEAVCParkingUn_1400D54A0.cpp" />
    <ClCompile Include="Source\SendMsg_UnitSellResultCPlayerQEAAXEEGHKKKZ_1400DAD40.cpp" />
    <ClCompile Include="Source\SendMsg_UnitTakeResultCPlayerQEAAXEZ_1400DB4B0.cpp" />
    <ClCompile Include="Source\SendMsg_UpdateTLStatusInfoCPlayerQEAAXKEZ_1400E8CB0.cpp" />
    <ClCompile Include="Source\SendMsg_UseJadeResultCPlayerQEAAXEGZ_1400E83C0.cpp" />
    <ClCompile Include="Source\SendMsg_UsePotionResultCPlayerQEAAXEGEZ_1400D7A70.cpp" />
    <ClCompile Include="Source\SendMsg_UseRadarResultCPlayerQEAAXEGKZ_1400E5E00.cpp" />
    <ClCompile Include="Source\SendMsg_UsPotionResultOtherCPlayerQEAAXEGPEAV1_NZ_1400D7B80.cpp" />
    <ClCompile Include="Source\SendMsg_VoteResultCPlayerQEAAXKEZ_1400E3F60.cpp" />
    <ClCompile Include="Source\SendOhterNotifyCommitteeMemberPositionCNormalGuild_1403E22D0.cpp" />
    <ClCompile Include="Source\SendRecallReqeustResultCRecallEffectControllerQEAA_14024F1E0.cpp" />
    <ClCompile Include="Source\SendRecallReqeustToDestCRecallEffectControllerIEAA_14024F2C0.cpp" />
    <ClCompile Include="Source\SendRepriceErrorResultCUnmannedTraderUserInfoQEAAX_140357CF0.cpp" />
    <ClCompile Include="Source\SendRepriceSuccessResultCUnmannedTraderUserInfoQEA_140357DB0.cpp" />
    <ClCompile Include="Source\SendResponseAcceptResultGMCallMgrIEAAXPEAVCPlayer0_1402AAE40.cpp" />
    <ClCompile Include="Source\SendResponseGMCallGMCallMgrIEAA_NPEAVCPlayerHZ_1402AAA60.cpp" />
    <ClCompile Include="Source\SendResponseGMListGMCallMgrIEAA_NPEAVCPlayerHZ_1402AAB10.cpp" />
    <ClCompile Include="Source\SendSelfNotifyCommitteeMemberPositionListCNormalGu_1403E23D0.cpp" />
    <ClCompile Include="Source\SendStealMsgCChatStealSystemAEAAXPEAVCPlayerEKPEAD_1403F8A30.cpp" />
    <ClCompile Include="Source\SendTargetMonsterSFContInfoCPlayerQEAAXXZ_1400E5520.cpp" />
    <ClCompile Include="Source\SendTargetPlayerDamageContInfoCPlayerQEAAXXZ_1400E5740.cpp" />
    <ClCompile Include="Source\Send_ClienInformCChiNetworkEXQEAAXPEAVCPlayerGPEAD_1404102D0.cpp" />
    <ClCompile Include="Source\Send_IPCChiNetworkEXQEAAXPEAVCPlayerZ_14040FFC0.cpp" />
    <ClCompile Include="Source\Send_LoginCChiNetworkEXQEAAXPEAVCPlayerZ_14040FE90.cpp" />
    <ClCompile Include="Source\Send_LogoutCChiNetworkEXQEAAXPEAVCPlayerZ_140410190.cpp" />
    <ClCompile Include="Source\Send_TransCChiNetworkEXQEAAXPEAVCPlayerKZ_1404100B0.cpp" />
    <ClCompile Include="Source\SenseStateCPlayerQEAAXXZ_1400577F0.cpp" />
    <ClCompile Include="Source\SetAccumulationCountAddCMonsterSkillQEAAXHZ_140161500.cpp" />
    <ClCompile Include="Source\SetAggroCMonsterAggroMgrQEAAXPEAVCCharacterHHKHHZ_14015DDA0.cpp" />
    <ClCompile Include="Source\SetAttackPartCPlayerUEAAXHZ_1400743F0.cpp" />
    <ClCompile Include="Source\SetAttackTargetCMonsterQEAAXPEAVCCharacterZ_1401429C0.cpp" />
    <ClCompile Include="Source\SetBagNumCPlayerDBQEAAXEZ_1400B8590.cpp" />
    <ClCompile Include="Source\SetBattleModeCPlayerQEAAX_NZ_140068650.cpp" />
    <ClCompile Include="Source\SetBindDummyCPlayerQEAAXPEAU_dummy_positionZ_1403EACA0.cpp" />
    <ClCompile Include="Source\SetBindMapDataCPlayerQEAAXPEAVCMapDataZ_1403EAC70.cpp" />
    <ClCompile Include="Source\SetBindPositionCPlayerQEAA_NPEAVCMapDataPEAU_dummy_1400A03C0.cpp" />
    <ClCompile Include="Source\SetCAggroNodeQEAAXPEAVCCharacterZ_140161810.cpp" />
    <ClCompile Include="Source\SetCameraPosCLevelQEAAXQEAMZ_1404E0830.cpp" />
    <ClCompile Include="Source\SetCashAmountCPlayerQEAAXHZ_1402F2740.cpp" />
    <ClCompile Include="Source\SetClassInGuildCPlayerDBQEAAXEZ_1400AD320.cpp" />
    <ClCompile Include="Source\SetCMonsterSkillPoolQEAAHPEAVCMonsterZ_140156BF0.cpp" />
    <ClCompile Include="Source\SetCntEnableCPlayerQEAAX_NZ_1400CFD80.cpp" />
    <ClCompile Include="Source\SetCurPosCPlayerDBQEAAXPEAMZ_140079EE0.cpp" />
    <ClCompile Include="Source\SetDalantCPlayerDBQEAAXKZ_14007C270.cpp" />
    <ClCompile Include="Source\SetDamageAutominePersonalUEAAHHPEAVCCharacterH_NHK_1402DB890.cpp" />
    <ClCompile Include="Source\SetDamageCAnimusUEAAHHPEAVCCharacterH_NHK1Z_140129420.cpp" />
    <ClCompile Include="Source\SetDamageCGameObjectUEAAHHPEAVCCharacterH_NHK1Z_14012E120.cpp" />
    <ClCompile Include="Source\SetDamageCGuardTowerUEAAHHPEAVCCharacterH_NHK1Z_14012FE50.cpp" />
    <ClCompile Include="Source\SetDamageCHolyKeeperUEAAHHPEAVCCharacterH_NHK1Z_1401349B0.cpp" />
    <ClCompile Include="Source\SetDamageCHolyStoneUEAAHHPEAVCCharacterH_NHK1Z_140137590.cpp" />
    <ClCompile Include="Source\SetDamageCMonsterUEAAHHPEAVCCharacterH_NHK1Z_1401462D0.cpp" />
    <ClCompile Include="Source\SetDamageCPlayerUEAAHHPEAVCCharacterH_NHK1Z_14005DA70.cpp" />
    <ClCompile Include="Source\SetDamageCTrapUEAAHHPEAVCCharacterH_NHK1Z_14013F1C0.cpp" />
    <ClCompile Include="Source\SetDPCPlayerDBQEAAXKZ_14007D9D0.cpp" />
    <ClCompile Include="Source\SetDPCPlayerQEAA_NH_NZ_14005F340.cpp" />
    <ClCompile Include="Source\SetEffectEquipCodeCPlayerQEAAXEEEZ_140057C70.cpp" />
    <ClCompile Include="Source\SetEnvironmentCLevelQEAAXKZ_1404DFCE0.cpp" />
    <ClCompile Include="Source\SetEquipEffectCPlayerQEAAXPEAU_storage_con_STORAGE_140050CC0.cpp" />
    <ClCompile Include="Source\SetEquipJadeEffectCPlayerQEAAXHM_NZ_140052280.cpp" />
    <ClCompile Include="Source\SetExpCPlayerDBQEAAXNZ_14007D6C0.cpp" />
    <ClCompile Include="Source\SetForceCMonsterSkillQEAAHPEAU_monster_fldPEAU_mon_140156700.cpp" />
    <ClCompile Include="Source\SetFPCPlayerDBQEAAXKZ_14007D990.cpp" />
    <ClCompile Include="Source\SetFPCPlayerQEAA_NH_NZ_14005F140.cpp" />
    <ClCompile Include="Source\SetGaugeCPlayerQEAAXHH_NZ_14005F430.cpp" />
    <ClCompile Include="Source\SetGenCMonsterSkillQEAAHPEAU_monster_fldHKMKZ_1401562B0.cpp" />
    <ClCompile Include="Source\SetGetExpCExpInfoCPartyModeKillMonsterExpNotifyQEA_1401721D0.cpp" />
    <ClCompile Include="Source\SetGmCChatStealSystemQEAA_NPEAVCPlayerZ_1403F88B0.cpp" />
    <ClCompile Include="Source\SetGoldCPlayerDBQEAAXKZ_14007C290.cpp" />
    <ClCompile Include="Source\SetGradeCPlayerQEAAXEZ_1400647D0.cpp" />
    <ClCompile Include="Source\SetHaveBoxOfAMPCPlayerDBQEAAX_NZ_1402E1C40.cpp" />
    <ClCompile Include="Source\SetHaveEffectCPlayerQEAAX_NZ_140050D30.cpp" />
    <ClCompile Include="Source\SetHaveEffectUseTimeCPlayerQEAAXPEAU_db_con_STORAG_140068840.cpp" />
    <ClCompile Include="Source\SetHPCPlayerDBQEAAXKZ_14007D970.cpp" />
    <ClCompile Include="Source\SetHPCPlayerUEAA_NH_NZ_14005EFA0.cpp" />
    <ClCompile Include="Source\SetKillMonsterFlagCPartyModeKillMonsterExpNotifyQE_14007D3A0.cpp" />
    <ClCompile Include="Source\SetLastAttBuffCPlayerQEAAX_NZ_140068FF0.cpp" />
    <ClCompile Include="Source\SetLevelCPlayerDBQEAAXHZ_14007C250.cpp" />
    <ClCompile Include="Source\SetLevelCPlayerQEAAXEZ_140055630.cpp" />
    <ClCompile Include="Source\SetLevelDCPlayerQEAAXEZ_140055930.cpp" />
    <ClCompile Include="Source\SetLockModeCPartyPlayerQEAA_N_NZ_140045B70.cpp" />
    <ClCompile Include="Source\SetLootShareModeCPartyPlayerQEAA_NEZ_140045BE0.cpp" />
    <ClCompile Include="Source\SetLossExpCPlayerDBQEAAXNZ_14007A560.cpp" />
    <ClCompile Include="Source\SetMapCodeCPlayerDBQEAAXEZ_140079EC0.cpp" />
    <ClCompile Include="Source\SetMaxLevelCPlayerDBQEAAXHZ_1400C2CF0.cpp" />
    <ClCompile Include="Source\SetModulusAndSubgroupGeneratorDL_GroupParameters_I_140552530.cpp" />
    <ClCompile Include="Source\SetModulusModExpPrecomputationCryptoPPQEAAXAEBVInt_1405525C0.cpp" />
    <ClCompile Include="Source\SetMstHaveEffectCPlayerQEAAXPEBU_ResourceItem_fldP_140052970.cpp" />
    <ClCompile Include="Source\SetMstPtCPlayerQEAAXHM_NHZ_140052C20.cpp" />
    <ClCompile Include="Source\SetNextGenAttTimeCCharacterQEAAXKZ_140132A20.cpp" />
    <ClCompile Include="Source\SetNextLootAuthorCPartyPlayerQEAAXXZ_140045D80.cpp" />
    <ClCompile Include="Source\SetOwnerCGravityStoneQEAAXPEAVCPlayerZ_140164A50.cpp" />
    <ClCompile Include="Source\SetPlayerOutCGuildRoomInfoQEAA_NHKHZ_1402E6310.cpp" />
    <ClCompile Include="Source\SetPlayerOutCGuildRoomSystemQEAAHKHKZ_1402EA8D0.cpp" />
    <ClCompile Include="Source\SetPlayerStateRFEventBaseUEAA_NQEAXHZ_140329530.cpp" />
    <ClCompile Include="Source\SetPlayerStateRFEvent_ClassRefineUEAA_NQEAXHZ_140328A60.cpp" />
    <ClCompile Include="Source\SetPotionActDelayCPlayerQEAAXEKKZ_1400A3920.cpp" />
    <ClCompile Include="Source\SetPrivateExponentDL_PrivateKeyImplVDL_GroupParame_140452330.cpp" />
    <ClCompile Include="Source\SetPrivateExponentDL_PrivateKeyImplVDL_GroupParame_1405593D0.cpp" />
    <ClCompile Include="Source\SetPrivateExponentDL_PrivateKeyImplVDL_GroupParame_1405689F0.cpp" />
    <ClCompile Include="Source\SetPrivateExponentDL_PrivateKeyImplVDL_GroupParame_140636670.cpp" />
    <ClCompile Include="Source\SetPrivateExponentDL_PrivateKeyImplVDL_GroupParame_1406383C0.cpp" />
    <ClCompile Include="Source\SetPvpOrderViewCPvpOrderViewQEAA_NNPEAU_PVP_ORDER__1403F71C0.cpp" />
    <ClCompile Include="Source\SetPvPPointCPlayerDBQEAAXNZ_14007D9F0.cpp" />
    <ClCompile Include="Source\SetPvpPointLeakCPlayerQEAAXNZ_140068ED0.cpp" />
    <ClCompile Include="Source\SetRankRateCPlayerQEAAXGKZ_140064850.cpp" />
    <ClCompile Include="Source\SetShapeAllBufferCPlayerQEAAXXZ_140064A40.cpp" />
    <ClCompile Include="Source\SetSiegeCPlayerQEAAXPEAU_db_con_STORAGE_LISTZ_1400F0BC0.cpp" />
    <ClCompile Include="Source\SetSkillCMonsterSkillQEAAHPEAU_monster_fldPEAU_mon_1401564A0.cpp" />
    <ClCompile Include="Source\SetSPCPlayerDBQEAAXKZ_14007D9B0.cpp" />
    <ClCompile Include="Source\SetSPCPlayerQEAA_NH_NZ_14005F240.cpp" />
    <ClCompile Include="Source\SetStateFlagCPlayerQEAAXXZ_140063A90.cpp" />
    <ClCompile Include="Source\SetStaticMemberCPlayerSAXXZ_140065EE0.cpp" />
    <ClCompile Include="Source\SetTargetInfoFromCharacterCChatStealSystemQEAA_NEP_1403F87C0.cpp" />
    <ClCompile Include="Source\SetTarPosCCharacterQEAA_NPEAM_NZ_140173190.cpp" />
    <ClCompile Include="Source\SetTarPosCPlayerQEAA_NPEAM_NZ_14005D760.cpp" />
    <ClCompile Include="Source\SetTopAggroCharacterCMonsterAggroMgrQEAAXPEAVCChar_14014C2A0.cpp" />
    <ClCompile Include="Source\SetUseRadarCRadarItemMgrQEAAXPEAY0EADPEAVCPlayerKK_1402E4C20.cpp" />
    <ClCompile Include="Source\SetUseReleaseRaceBuffPotionCPlayerQEAAXXZ_1400A3B40.cpp" />
    <ClCompile Include="Source\SetViewMatrixCLevelQEAAXPEAUD3DXMATRIXZ_1404E0610.cpp" />
    <ClCompile Include="Source\SetVoteCPlayerQEAAXHZ_1401087B0.cpp" />
    <ClCompile Include="Source\SetVPIPTex1IndexPrimitiveTLYAXPEAX0Z_14050C120.cpp" />
    <ClCompile Include="Source\SetVPIPTex1IndexPrimitiveYAXPEAX0Z_14050C150.cpp" />
    <ClCompile Include="Source\set_pos_dh_player_mgrQEAAXPEAVCMapDataGPEAMZ_14026F390.cpp" />
    <ClCompile Include="Source\set_skill_lv_up_dataQEAAXGEZ_14007B800.cpp" />
    <ClCompile Include="Source\SFContDelMessageCPlayerUEAAXEE_N0Z_140053F70.cpp" />
    <ClCompile Include="Source\SFContInitCCharacterQEAAXXZ_140175500.cpp" />
    <ClCompile Include="Source\SFContInsertMessageCGameObjectUEAAXEE_NPEAVCPlayer_14012C680.cpp" />
    <ClCompile Include="Source\SFContInsertMessageCPlayerUEAAXEE_NPEAV1Z_140053D30.cpp" />
    <ClCompile Include="Source\SFContUpdateTimeMessageCPlayerUEAAXEEHZ_140054120.cpp" />
    <ClCompile Include="Source\SF_AllContDamageForceRemove_OnceCGameObjectUEAA_NP_14012C920.cpp" />
    <ClCompile Include="Source\SF_AllContDamageForceRemove_OnceCPlayerUEAA_NPEAVC_14009EA60.cpp" />
    <ClCompile Include="Source\SF_AllContDamageRemove_OnceCPlayerQEAA_NPEAVCChara_1400A3E00.cpp" />
    <ClCompile Include="Source\SF_AllContHelpForceRemove_OnceCGameObjectUEAA_NPEA_14012C900.cpp" />
    <ClCompile Include="Source\SF_AllContHelpForceRemove_OnceCMonsterUEAA_NPEAVCC_140145F20.cpp" />
    <ClCompile Include="Source\SF_AllContHelpForceRemove_OnceCPlayerUEAA_NPEAVCCh_14009E950.cpp" />
    <ClCompile Include="Source\SF_AllContHelpSkillRemove_OnceCGameObjectUEAA_NPEA_14012C8E0.cpp" />
    <ClCompile Include="Source\SF_AllContHelpSkillRemove_OnceCMonsterUEAA_NPEAVCC_140145E10.cpp" />
    <ClCompile Include="Source\SF_AllContHelpSkillRemove_OnceCPlayerUEAA_NPEAVCCh_14009E840.cpp" />
    <ClCompile Include="Source\SF_AttHPtoDstFP_OnceCGameObjectUEAA_NPEAVCCharacte_14012C7A0.cpp" />
    <ClCompile Include="Source\SF_AttHPtoDstFP_OnceCPlayerUEAA_NPEAVCCharacterMZ_14009DAB0.cpp" />
    <ClCompile Include="Source\SF_ContDamageTimeInc_OnceCGameObjectUEAA_NPEAVCCha_14012C7C0.cpp" />
    <ClCompile Include="Source\SF_ContDamageTimeInc_OnceCPlayerUEAA_NPEAVCCharact_14009DBD0.cpp" />
    <ClCompile Include="Source\SF_ContHelpTimeInc_OnceCGameObjectUEAA_NPEAVCChara_14012C840.cpp" />
    <ClCompile Include="Source\SF_ContHelpTimeInc_OnceCPlayerUEAA_NPEAVCCharacter_14009E0A0.cpp" />
    <ClCompile Include="Source\SF_ConvertMonsterTargetCGameObjectUEAA_NPEAVCChara_14012C980.cpp" />
    <ClCompile Include="Source\SF_ConvertMonsterTargetCPlayerUEAA_NPEAVCCharacter_14009F040.cpp" />
    <ClCompile Include="Source\SF_ConvertTargetDestCGameObjectUEAA_NPEAVCCharacte_14012CB20.cpp" />
    <ClCompile Include="Source\SF_ConvertTargetDestCPlayerUEAA_NPEAVCCharacterMZ_1400A0720.cpp" />
    <ClCompile Include="Source\SF_DamageAndStunCGameObjectUEAA_NPEAVCCharacterMZ_14012CA60.cpp" />
    <ClCompile Include="Source\SF_DamageAndStunCPlayerUEAA_NPEAVCCharacterMZ_14009FCC0.cpp" />
    <ClCompile Include="Source\SF_FPDecCGameObjectUEAA_NPEAVCCharacterMZ_14012CA40.cpp" />
    <ClCompile Include="Source\SF_FPDecCPlayerUEAA_NPEAVCCharacterMZ_14009FC10.cpp" />
    <ClCompile Include="Source\SF_HFSInc_OnceCPlayerQEAA_NPEAVCCharacterZ_1400A3C60.cpp" />
    <ClCompile Include="Source\SF_HPInc_OnceCGameObjectUEAA_NPEAVCCharacterMZ_14012C800.cpp" />
    <ClCompile Include="Source\SF_HPInc_OnceCMonsterUEAA_NPEAVCCharacterMZ_140145A20.cpp" />
    <ClCompile Include="Source\SF_HPInc_OnceCPlayerUEAA_NPEAVCCharacterMZ_14009DE60.cpp" />
    <ClCompile Include="Source\SF_IncHPCirclePartyCGameObjectUEAA_NPEAVCCharacter_14012C9E0.cpp" />
    <ClCompile Include="Source\SF_IncHPCirclePartyCPlayerUEAA_NPEAVCCharacterMZ_14009F820.cpp" />
    <ClCompile Include="Source\SF_IncreaseDPCGameObjectUEAA_NPEAVCCharacterMZ_14012CB00.cpp" />
    <ClCompile Include="Source\SF_IncreaseDPCPlayerUEAA_NPEAVCCharacterMZ_1400A0670.cpp" />
    <ClCompile Include="Source\SF_LateContDamageRemove_OnceCGameObjectUEAA_NPEAVC_14012C8C0.cpp" />
    <ClCompile Include="Source\SF_LateContDamageRemove_OnceCMonsterUEAA_NPEAVCCha_140146030.cpp" />
    <ClCompile Include="Source\SF_LateContDamageRemove_OnceCPlayerUEAA_NPEAVCChar_14009E6A0.cpp" />
    <ClCompile Include="Source\SF_LateContHelpForceRemove_OnceCGameObjectUEAA_NPE_14012C8A0.cpp" />
    <ClCompile Include="Source\SF_LateContHelpForceRemove_OnceCMonsterUEAA_NPEAVC_140145CC0.cpp" />
    <ClCompile Include="Source\SF_LateContHelpForceRemove_OnceCPlayerUEAA_NPEAVCC_14009E550.cpp" />
    <ClCompile Include="Source\SF_LateContHelpSkillRemove_OnceCGameObjectUEAA_NPE_14012C880.cpp" />
    <ClCompile Include="Source\SF_LateContHelpSkillRemove_OnceCMonsterUEAA_NPEAVC_140145B70.cpp" />
    <ClCompile Include="Source\SF_LateContHelpSkillRemove_OnceCPlayerUEAA_NPEAVCC_14009E400.cpp" />
    <ClCompile Include="Source\SF_MakePortalReturnBindPositionPartyMemberCGameObj_14012CAC0.cpp" />
    <ClCompile Include="Source\SF_MakePortalReturnBindPositionPartyMemberCPlayerU_1400A00E0.cpp" />
    <ClCompile Include="Source\SF_MakeZeroAnimusRecallTimeOnceCGameObjectUEAA_NPE_14012CB60.cpp" />
    <ClCompile Include="Source\SF_MakeZeroAnimusRecallTimeOnceCPlayerUEAA_NPEAVCC_1400A0920.cpp" />
    <ClCompile Include="Source\SF_OthersContHelpSFRemove_OnceCPlayerUEAA_NMZ_14009EBA0.cpp" />
    <ClCompile Include="Source\SF_OverHealing_OnceCGameObjectUEAA_NPEAVCCharacter_14012C860.cpp" />
    <ClCompile Include="Source\SF_OverHealing_OnceCPlayerUEAA_NPEAVCCharacterMZ_14009E2C0.cpp" />
    <ClCompile Include="Source\SF_RecoverAllReturnStateAnimusHPFullCGameObjectUEA_14012CB40.cpp" />
    <ClCompile Include="Source\SF_RecoverAllReturnStateAnimusHPFullCPlayerUEAA_NP_1400A0790.cpp" />
    <ClCompile Include="Source\SF_ReleaseMonsterTargetCGameObjectUEAA_NPEAVCChara_14012C9C0.cpp" />
    <ClCompile Include="Source\SF_ReleaseMonsterTargetCPlayerUEAA_NPEAVCCharacter_14009F610.cpp" />
    <ClCompile Include="Source\SF_RemoveAllContHelp_OnceCGameObjectUEAA_NPEAVCCha_14012CAA0.cpp" />
    <ClCompile Include="Source\SF_RemoveAllContHelp_OnceCPlayerUEAA_NPEAVCCharact_14009FF50.cpp" />
    <ClCompile Include="Source\SF_Resurrect_OnceCGameObjectUEAA_NPEAVCCharacterZ_14012C7E0.cpp" />
    <ClCompile Include="Source\SF_Resurrect_OnceCPlayerUEAA_NPEAVCCharacterZ_14009DDE0.cpp" />
    <ClCompile Include="Source\SF_ReturnBindPositionCGameObjectUEAA_NPEAVCCharact_14012CAE0.cpp" />
    <ClCompile Include="Source\SF_ReturnBindPositionCPlayerUEAA_NPEAVCCharacterMZ_1400A0590.cpp" />
    <ClCompile Include="Source\SF_SelfDestructionCGameObjectUEAA_NPEAVCCharacterM_14012CB80.cpp" />
    <ClCompile Include="Source\SF_SelfDestructionCPlayerUEAA_NPEAVCCharacterMZ_1400A0990.cpp" />
    <ClCompile Include="Source\SF_SkillContHelpTimeInc_OnceCGameObjectUEAA_NPEAVC_14012C960.cpp" />
    <ClCompile Include="Source\SF_SkillContHelpTimeInc_OnceCPlayerUEAA_NPEAVCChar_14009EE20.cpp" />
    <ClCompile Include="Source\SF_SPDecCGameObjectUEAA_NPEAVCCharacterMZ_14012CA20.cpp" />
    <ClCompile Include="Source\SF_SPDecCPlayerUEAA_NPEAVCCharacterMZ_14009FB60.cpp" />
    <ClCompile Include="Source\SF_STInc_OnceCGameObjectUEAA_NPEAVCCharacterMZ_14012C820.cpp" />
    <ClCompile Include="Source\SF_STInc_OnceCPlayerUEAA_NPEAVCCharacterMZ_14009DFA0.cpp" />
    <ClCompile Include="Source\SF_StunCGameObjectUEAA_NPEAVCCharacterMZ_14012CA00.cpp" />
    <ClCompile Include="Source\SF_StunCPlayerUEAA_NPEAVCCharacterMZ_14009FAD0.cpp" />
    <ClCompile Include="Source\SF_TeleportToDestinationCGameObjectUEAA_NPEAVCChar_14012CBA0.cpp" />
    <ClCompile Include="Source\SF_TeleportToDestinationCPlayerUEAA_NPEAVCCharacte_1400A0A10.cpp" />
    <ClCompile Include="Source\SF_TransDestHPCGameObjectUEAA_NPEAVCCharacterMAEAE_14012CA80.cpp" />
    <ClCompile Include="Source\SF_TransDestHPCPlayerUEAA_NPEAVCCharacterMAEAEZ_14009FD50.cpp" />
    <ClCompile Include="Source\SF_TransMonsterHPCGameObjectUEAA_NPEAVCCharacterMZ_14012C9A0.cpp" />
    <ClCompile Include="Source\SF_TransMonsterHPCPlayerUEAA_NPEAVCCharacterMZ_14009F260.cpp" />
    <ClCompile Include="Source\show_to_allCGuildMasterEffectAEAAXPEAVCPlayerEEEZ_1403F4CF0.cpp" />
    <ClCompile Include="Source\SimultaneousExponentiateAbstractRingVIntegerCrypto_14056EAA0.cpp" />
    <ClCompile Include="Source\SimultaneousExponentiateAbstractRingVPolynomialMod_140573B90.cpp" />
    <ClCompile Include="Source\SimultaneousExponentiateDL_GroupParameters_ECVEC2N_1405841C0.cpp" />
    <ClCompile Include="Source\SimultaneousExponentiateDL_GroupParameters_ECVECPC_14057FFF0.cpp" />
    <ClCompile Include="Source\SimultaneousExponentiateDL_GroupParameters_GFPCryp_140631F60.cpp" />
    <ClCompile Include="Source\SimultaneousExponentiateModularArithmeticCryptoPPU_1405ED3F0.cpp" />
    <ClCompile Include="Source\SimultaneousExponentiateMontgomeryRepresentationCr_1405610F0.cpp" />
    <ClCompile Include="Source\sizevectorUBaseAndExponentUEC2NPointCryptoPPVInteg_1405941D0.cpp" />
    <ClCompile Include="Source\sizevectorUBaseAndExponentUECPPointCryptoPPVIntege_1405949B0.cpp" />
    <ClCompile Include="Source\sizevectorUBaseAndExponentVIntegerCryptoPPV12Crypt_140592EB0.cpp" />
    <ClCompile Include="Source\size_attack_skill_result_zoclQEAAHXZ_1400EED70.cpp" />
    <ClCompile Include="Source\size_character_disconnect_result_wracQEAAHXZ_1401D24E0.cpp" />
    <ClCompile Include="Source\size_nuclear_bomb_explosion_result_zoclQEAAHXZ_14013E6D0.cpp" />
    <ClCompile Include="Source\size_nuclear_explosion_success_zoclQEAAHXZ_14013E730.cpp" />
    <ClCompile Include="Source\size_qry_case_character_renameQEAAHXZ_1400B8600.cpp" />
    <ClCompile Include="Source\size_qry_case_cheat_player_vote_infoQEAAHXZ_14007E150.cpp" />
    <ClCompile Include="Source\size_qry_case_update_player_vote_infoQEAAHXZ_14007E140.cpp" />
    <ClCompile Include="Source\size_target_player_damage_contsf_allinform_zoclQEA_1400F0250.cpp" />
    <ClCompile Include="Source\SkillRecallTeleportRequestCNetworkEXAEAA_NHPEADZ_1401C2F00.cpp" />
    <ClCompile Include="Source\SkillRequestCNetworkEXAEAA_NHPEADZ_1401C21F0.cpp" />
    <ClCompile Include="Source\skill_processCPlayerQEAAEHHPEAU_CHRIDPEAGPEAHZ_14009B750.cpp" />
    <ClCompile Include="Source\skill_process_for_auraCPlayerQEAAXHZ_14009C450.cpp" />
    <ClCompile Include="Source\skill_process_for_itemCPlayerQEAAEHPEAU_CHRIDPEAHZ_14009C430.cpp" />
    <ClCompile Include="Source\SortPostCPlayerQEAAXHZ_1400C9A60.cpp" />
    <ClCompile Include="Source\StartCGuildBattleControllerQEAAEPEAVCPlayerZ_1403D5F80.cpp" />
    <ClCompile Include="Source\StartCNormalGuildBattleFieldGUILD_BATTLEQEAA_NEPEA_1403ED410.cpp" />
    <ClCompile Include="Source\StartCNormalGuildBattleGUILD_BATTLEQEAAEPEAVCPlaye_1403E4640.cpp" />
    <ClCompile Include="Source\StartCNormalGuildBattleManagerGUILD_BATTLEQEAAEPEA_1403D44E0.cpp" />
    <ClCompile Include="Source\StarterBox_InsertToInvenCGoldenBoxItemMgrQEAA_NPEA_140414D70.cpp" />
    <ClCompile Include="Source\StartEventSetCMonsterEventSetQEAA_NPEAD0PEAVCPlaye_1402A8210.cpp" />
    <ClCompile Include="Source\StealChatMsgCChatStealSystemQEAAXPEAVCPlayerEPEADZ_1403F8900.cpp" />
    <ClCompile Include="Source\StopCCharacterQEAAXXZ_1401732C0.cpp" />
    <ClCompile Include="Source\SubActPointCPlayerQEAAXEKZ_140056140.cpp" />
    <ClCompile Include="Source\SubCompleteBuyFindBuyerCUnmannedTraderUserInfoTabl_140364BC0.cpp" />
    <ClCompile Include="Source\SubCompleteBuyProcBuyCUnmannedTraderUserInfoTableA_140364F10.cpp" />
    <ClCompile Include="Source\SubDalantCPlayerQEAAXKZ_140055C40.cpp" />
    <ClCompile Include="Source\SubGoldCPlayerQEAAXKZ_140055E70.cpp" />
    <ClCompile Include="Source\SubPointCPlayerQEAAXKZ_140055F60.cpp" />
    <ClCompile Include="Source\SubTrunkDalantCPlayerDBQEAAXKZ_14010C2A0.cpp" />
    <ClCompile Include="Source\SubTrunkGoldCPlayerDBQEAAXKZ_14010C3D0.cpp" />
    <ClCompile Include="Source\SumMinuteBetweenCPlayerQEAAKPEAU_SYSTEMTIME0Z_140069600.cpp" />
    <ClCompile Include="Source\SumMinuteOneCPlayerQEAAKPEAU_SYSTEMTIMEZ_140069480.cpp" />
    <ClCompile Include="Source\TakeBallCNormalGuildBattleFieldGUILD_BATTLEQEAAEHP_1403ECC60.cpp" />
    <ClCompile Include="Source\TakeGravityStoneCPlayerQEAAXXZ_1400A0480.cpp" />
    <ClCompile Include="Source\TakePvpPointCPvpPointLimiterQEAA_NAEANPEAVCPlayer1_140125300.cpp" />
    <ClCompile Include="Source\ThrowSkillRequestCNetworkEXAEAA_NHPEADZ_1401C24E0.cpp" />
    <ClCompile Include="Source\TickettingCTransportShipQEAA_NPEAVCPlayerZ_140264450.cpp" />
    <ClCompile Include="Source\unchecked_copyPEAUBaseAndExponentUECPPointCryptoPP_140616900.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_copyPEAUBaseAndExponentUEC_1405AB7B0.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_copyPEAUBaseAndExponentUEC_1405AB820.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_copyPEAUBaseAndExponentVIn_1405AB740.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_copyV_Vector_iteratorUBase_1405AB210.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_copyV_Vector_iteratorUBase_1405AB380.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_copyV_Vector_iteratorUBase_1405AB4F0.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_fill_nPEAUBaseAndExponentU_14059E180.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_fill_nPEAUBaseAndExponentU_14059E2B0.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_fill_nPEAUBaseAndExponentV_14059DEE0.cpp" />
    <ClCompile Include="Source\UpdateAuraSFContCPlayerQEAAXXZ_1400A3620.cpp" />
    <ClCompile Include="Source\UpdateChaosModeStateCPlayerAEAAXKZ_14007BE90.cpp" />
    <ClCompile Include="Source\UpdateDelPostCPlayerQEAA_NKHZ_1400C9A00.cpp" />
    <ClCompile Include="Source\UpdatedMasteryWriteHistoryCPlayerQEAAXXZ_1400638C0.cpp" />
    <ClCompile Include="Source\UpdateLastCriTicketCPlayerQEAAXGEEEEZ_1400CE390.cpp" />
    <ClCompile Include="Source\UpdateLastMetalTicketCPlayerQEAAXGEEEEZ_1400CE440.cpp" />
    <ClCompile Include="Source\UpdatePlayerStatusTimeLimitMgrQEAA_NGKEZ_14040EB20.cpp" />
    <ClCompile Include="Source\UpdatePostAddLogCPlayerQEAAXK_NHZ_1400C9C40.cpp" />
    <ClCompile Include="Source\UpdatePostCPlayerQEAAXKZ_1400C98F0.cpp" />
    <ClCompile Include="Source\UpdatePotionContEffectCPotionMgrQEAAXPEAVCPlayerZ_14039F030.cpp" />
    <ClCompile Include="Source\UpdatePvpOrderViewCPlayerQEAAX_JZ_140068390.cpp" />
    <ClCompile Include="Source\UpdatePvpPointLimiterCPlayerQEAAX_JZ_1402081C0.cpp" />
    <ClCompile Include="Source\UpdateReturnPostCPlayerQEAAXKZ_1400C9B90.cpp" />
    <ClCompile Include="Source\UpdateSFContCCharacterUEAAXXZ_140174DE0.cpp" />
    <ClCompile Include="Source\UpdateVisualVerCPlayerQEAAXTCashChangeStateFlag1Z_140053970.cpp" />
    <ClCompile Include="Source\Update_CharacterDataCRFWorldDatabaseQEAA_NKPEAU_wo_1404916B0.cpp" />
    <ClCompile Include="Source\Update_CharacterReNameCRFWorldDatabaseQEAA_NPEADKZ_1404C46C0.cpp" />
    <ClCompile Include="Source\Update_ExpCUserDBQEAA_NNZ_140115F00.cpp" />
    <ClCompile Include="Source\Update_GoldPointCPlayerQEAA_NKZ_140069C80.cpp" />
    <ClCompile Include="Source\Update_LevelCRFWorldDatabaseQEAA_NKEZ_1404970E0.cpp" />
    <ClCompile Include="Source\Update_LevelCUserDBQEAA_NENZ_140115D60.cpp" />
    <ClCompile Include="Source\Update_LossExpCUserDBQEAA_NNZ_140115F40.cpp" />
    <ClCompile Include="Source\Update_MaxLevelCUserDBQEAAXEZ_14011B850.cpp" />
    <ClCompile Include="Source\Update_Player_TimeLimit_InfoCRFWorldDatabaseQEAA_N_1404C86F0.cpp" />
    <ClCompile Include="Source\Update_Player_Vote_InfoCRFWorldDatabaseQEAA_NKKEEK_1404C75F0.cpp" />
    <ClCompile Include="Source\UseBuffPotionCExtPotionBufQEAAXPEAVCPlayerZ_14039FBD0.cpp" />
    <ClCompile Include="Source\UseCMonsterSkillQEAAHK_NZ_140156920.cpp" />
    <ClCompile Include="Source\UseDiscountCouponCashItemRemoteStoreQEAA_NPEAU_par_1402F54B0.cpp" />
    <ClCompile Include="Source\UsePotionCPotionMgrQEAAHPEAVCPlayerPEAVCCharacterP_14039DDC0.cpp" />
    <ClCompile Include="Source\UseRecoverLossExpItemRequestCNetworkEXAEAA_NHPEADZ_1401CB3B0.cpp" />
    <ClCompile Include="Source\UseSkill_TargetDfAIMgrSAHPEAVCMonsterPEAVCCharacte_140152460.cpp" />
    <ClCompile Include="Source\WeaponSFActiveCPlayerQEAA_NPEAU_be_damaged_charPEA_14008A400.cpp" />
    <ClCompile Include="Source\WPActiveAttackForceCPlayerAttackQEAAXPEAU_attack_p_14016FD90.cpp" />
    <ClCompile Include="Source\WPActiveAttackSkillCPlayerAttackQEAAXPEAU_attack_p_1401700F0.cpp" />
    <ClCompile Include="Source\WPActiveForceCPlayerQEAA_NPEAU_be_damaged_charHPEA_1400A25B0.cpp" />
    <ClCompile Include="Source\WPActiveSkillCPlayerQEAA_NPEAU_be_damaged_charHPEA_1400A2B60.cpp" />
    <ClCompile Include="Source\WriteCheatLogYAXPEADPEAVCPlayerZ_14028F480.cpp" />
    <ClCompile Include="Source\WriteEventCouponLogCGoldenBoxItemMgrQEAAXPEAVCPlay_1404158A0.cpp" />
    <ClCompile Include="Source\WriteGetGoldBarLogCGoldenBoxItemMgrQEAAXPEAVCPlaye_140415730.cpp" />
    <ClCompile Include="Source\WriteLog_LevelCUserDBQEAAXEZ_140113B40.cpp" />
    <ClCompile Include="Source\Y_Vector_const_iteratorUBaseAndExponentUEC2NPointC_140598BC0.cpp" />
    <ClCompile Include="Source\Y_Vector_const_iteratorUBaseAndExponentUECPPointCr_140598C30.cpp" />
    <ClCompile Include="Source\Y_Vector_const_iteratorUBaseAndExponentVIntegerCry_140598B10.cpp" />
    <ClCompile Include="Source\Y_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_1405987C0.cpp" />
    <ClCompile Include="Source\Y_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_140598900.cpp" />
    <ClCompile Include="Source\Y_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_140598560.cpp" />
    <ClCompile Include="Source\Z_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_1405A9190.cpp" />
    <ClCompile Include="Source\Z_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_1405A91C0.cpp" />
    <ClCompile Include="Source\Z_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_1405A90E0.cpp" />
    <ClCompile Include="Source\_AddToPacketCandidateRegisterAEAA_NPEAVCPlayerKZ_1402B6E40.cpp" />
    <ClCompile Include="Source\_Adjust_heapV_Vector_iteratorUBaseAndExponentUEC2N_1405A9BC0.cpp" />
    <ClCompile Include="Source\_Adjust_heapV_Vector_iteratorUBaseAndExponentUECPP_1405AA5A0.cpp" />
    <ClCompile Include="Source\_Adjust_heapV_Vector_iteratorUBaseAndExponentVInte_1405A91F0.cpp" />
    <ClCompile Include="Source\_AllocateUBaseAndExponentUEC2NPointCryptoPPVIntege_14059E1F0.cpp" />
    <ClCompile Include="Source\_AllocateUBaseAndExponentUECPPointCryptoPPVInteger_14059E320.cpp" />
    <ClCompile Include="Source\_AllocateUBaseAndExponentVIntegerCryptoPPV12Crypto_14059DF50.cpp" />
    <ClCompile Include="Source\_AnimusReturnCPlayerQEAAXEZ_1400D1080.cpp" />
    <ClCompile Include="Source\_AssistSF_Cont_DmgCMonsterIEAAHPEAVCCharacterPEAVC_14014CF60.cpp" />
    <ClCompile Include="Source\_AssistSF_Cont_SupportCMonsterIEAAHPEAVCCharacterP_14014CF10.cpp" />
    <ClCompile Include="Source\_AssistSF_Cont_TempCMonsterIEAAHPEAVCCharacterPEAV_14014D330.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_itemCashItemRemoteStoreAEAAA_1402FF340.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_item_additional_processCashI_140300110.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_item_calc_priceCashItemRemot_1402FF8A0.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_item_calc_price_couponCashIt_1402FFA70.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_item_check_itemCashItemRemot_1402FF5C0.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_item_give_itemCashItemRemote_1402FFEF0.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_item_proc_completeCashItemRe_1403004C0.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_item_proc_priceCashItemRemot_1402FF7C0.cpp" />
    <ClCompile Include="Source\_buybygold_check_couponCashItemRemoteStoreAEAAAW4C_1402FF190.cpp" />
    <ClCompile Include="Source\_buybygold_check_validCashItemRemoteStoreAEAAAW4CS_1402FEFA0.cpp" />
    <ClCompile Include="Source\_buybygold_completeCashItemRemoteStoreAEAAXPEAVCPl_140300840.cpp" />
    <ClCompile Include="Source\_buybygold_set_cashitem_dblog_sheetCashItemRemoteS_1402FF0E0.cpp" />
    <ClCompile Include="Source\_BuyvectorUBaseAndExponentUEC2NPointCryptoPPVInteg_140594470.cpp" />
    <ClCompile Include="Source\_BuyvectorUBaseAndExponentUECPPointCryptoPPVIntege_140594C50.cpp" />
    <ClCompile Include="Source\_BuyvectorUBaseAndExponentVIntegerCryptoPPV12Crypt_140593150.cpp" />
    <ClCompile Include="Source\_CalcMaxFPCPlayerQEAAHXZ_14005CDF0.cpp" />
    <ClCompile Include="Source\_CalcMaxHPCPlayerQEAAHXZ_14005CD30.cpp" />
    <ClCompile Include="Source\_CalcMaxSPCPlayerQEAAHXZ_14005D1C0.cpp" />
    <ClCompile Include="Source\_CalcMonSkillAttPntCMonsterAttackIEAAHXZ_14015A6A0.cpp" />
    <ClCompile Include="Source\_CalcSkillAttPntCPlayerAttackAEAAH_NZ_14016EC00.cpp" />
    <ClCompile Include="Source\_CCharacterCCharacter__1_dtor0_140172300.cpp" />
    <ClCompile Include="Source\_CCharacterCCharacter__1_dtor1_140172330.cpp" />
    <ClCompile Include="Source\_CCharacter_CCharacter__1_dtor0_1401723E0.cpp" />
    <ClCompile Include="Source\_CCharacter_CCharacter__1_dtor1_140172410.cpp" />
    <ClCompile Include="Source\_CheckForcePullUnitCPlayerQEAAXXZ_140106A10.cpp" />
    <ClCompile Include="Source\_CheckPlayerInfoCandidateRegisterAEAAHPEAVCPlayerZ_1402B6D70.cpp" />
    <ClCompile Include="Source\_CheckPotionDataYA_NAEAU_CheckEffectCode_CheckPoti_14039E2B0.cpp" />
    <ClCompile Include="Source\_CheckUserInfoClassOrderProcessorAEAAHEEPEAVCPlaye_1402B9060.cpp" />
    <ClCompile Include="Source\_check_dst_param_after_attackCPlayerQEAAXHPEAVCCha_140089A60.cpp" />
    <ClCompile Include="Source\_check_embel_partCPlayerQEAA_NPEAU_db_con_STORAGE__1400FF4C0.cpp" />
    <ClCompile Include="Source\_check_equipmastery_limCPlayerQEAAHHZ_140065E00.cpp" />
    <ClCompile Include="Source\_check_equip_partCPlayerQEAA_NPEAU_db_con_STORAGE__1400FF2D0.cpp" />
    <ClCompile Include="Source\_check_exp_after_attackCPlayerQEAAHHPEAU_be_damage_140089930.cpp" />
    <ClCompile Include="Source\_check_guild_target_objectCPlayerQEAAXXZ_1400FF6D0.cpp" />
    <ClCompile Include="Source\_check_hp_send_partyCPlayerQEAAXXZ_1400FF190.cpp" />
    <ClCompile Include="Source\_check_mastery_cum_limCPlayerQEAAKEEZ_140065140.cpp" />
    <ClCompile Include="Source\_check_mastery_limCPlayerQEAAKEEZ_1400657A0.cpp" />
    <ClCompile Include="Source\_check_party_target_objectCPlayerQEAAXXZ_1400FF5B0.cpp" />
    <ClCompile Include="Source\_check_race_target_objectCPlayerQEAAXXZ_1400FF7F0.cpp" />
    <ClCompile Include="Source\_check_target_objectCPlayerQEAAXXZ_1400FF020.cpp" />
    <ClCompile Include="Source\_CMonsterSkillPoolCMonsterSkillPool__1_dtor0_14014B530.cpp" />
    <ClCompile Include="Source\_CMonsterSkillPoolSet__1_dtor0_1401572D0.cpp" />
    <ClCompile Include="Source\_ConstructUBaseAndExponentUEC2NPointCryptoPPVInteg_1405A8250.cpp" />
    <ClCompile Include="Source\_ConstructUBaseAndExponentUECPPointCryptoPPVIntege_1405A83B0.cpp" />
    <ClCompile Include="Source\_ConstructUBaseAndExponentVIntegerCryptoPPV12Crypt_1405A7F90.cpp" />
    <ClCompile Include="Source\_Copy_backward_optPEAUBaseAndExponentUEC2NPointCry_1405A7B90.cpp" />
    <ClCompile Include="Source\_Copy_backward_optPEAUBaseAndExponentUECPPointCryp_1405A7C30.cpp" />
    <ClCompile Include="Source\_Copy_backward_optPEAUBaseAndExponentVIntegerCrypt_1405A7AF0.cpp" />
    <ClCompile Include="Source\_Copy_optPEAUBaseAndExponentUECPPointCryptoPPVInte_140617D20.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor0_140047BF0.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor10_140047DD0.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor11_140047E00.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor12_140047E30.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor13_140047E60.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor14_140047E90.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor15_140047EC0.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor16_140047EF0.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor17_140047F20.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor18_140047F50.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor19_140047F80.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor1_140047C20.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor20_140047FB0.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor21_140047FE0.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor2_140047C50.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor3_140047C80.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor4_140047CB0.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor5_140047CE0.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor6_140047D10.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor7_140047D40.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor8_140047D70.cpp" />
    <ClCompile Include="Source\_CPlayerCPlayer__1_dtor9_140047DA0.cpp" />
    <ClCompile Include="Source\_CPlayerDBCPlayerDB__1_dtor0_140108A40.cpp" />
    <ClCompile Include="Source\_CPlayerDBCPlayerDB__1_dtor1_140108A70.cpp" />
    <ClCompile Include="Source\_CPlayerDB_CPlayerDB__1_dtor0_140108B30.cpp" />
    <ClCompile Include="Source\_CPlayerdev_view_method__1_dtor0_1400BE4E0.cpp" />
    <ClCompile Include="Source\_CPlayerLoad__1_dtor0_140049A80.cpp" />
    <ClCompile Include="Source\_CPlayerpc_PlayAttack_Force__1_dtor0_1400830A0.cpp" />
    <ClCompile Include="Source\_CPlayerpc_PlayAttack_Gen__1_dtor0_140081160.cpp" />
    <ClCompile Include="Source\_CPlayerpc_PlayAttack_SelfDestruction__1_dtor0_1400853F0.cpp" />
    <ClCompile Include="Source\_CPlayerpc_PlayAttack_Siege__1_dtor0_140084640.cpp" />
    <ClCompile Include="Source\_CPlayerpc_PlayAttack_Skill__1_dtor0_1400821E0.cpp" />
    <ClCompile Include="Source\_CPlayerpc_PlayAttack_Unit__1_dtor0_140083570.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor0_140048270.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor10_140048450.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor11_140048480.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor12_1400484B0.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor13_1400484E0.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor14_140048510.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor15_140048540.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor16_140048570.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor17_1400485A0.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor18_1400485D0.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor19_140048600.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor1_1400482A0.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor20_140048630.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor2_1400482D0.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor3_140048300.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor4_140048330.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor5_140048360.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor6_140048390.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor7_1400483C0.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor8_1400483F0.cpp" />
    <ClCompile Include="Source\_CPlayer_CPlayer__1_dtor9_140048420.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_GroupParameters_CryptoPPECPPoint_Expon_1404505C0.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_GroupParameters_CryptoPPECPPoint_Expon_1404505F0.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_GroupParameters_EC_CryptoPPECP_GetMaxE_140450200.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_GroupParameters_EC_CryptoPPECP_GetMaxE_140450230.cpp" />
    <ClCompile Include="Source\_cStaticMember_PlayerInstance__1_dtor0_14010E4E0.cpp" />
    <ClCompile Include="Source\_ct_resurrect_player__1_dtor0_140292B40.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderSubClassInfoLevelCreate__1_dtor0_140384570.cpp" />
    <ClCompile Include="Source\_DDL_GroupParametersImplVModExpPrecomputationCrypt_14055F890.cpp" />
    <ClCompile Include="Source\_DDL_GroupParameters_IntegerBasedImplVModExpPrecom_14055EF00.cpp" />
    <ClCompile Include="Source\_DeleteUnitKeyCPlayerQEAAGEZ_1401067D0.cpp" />
    <ClCompile Include="Source\_delete_from_invenCashDbWorkerAEAAXQEAVCPlayerPEBU_1402F1370.cpp" />
    <ClCompile Include="Source\_DestroyUBaseAndExponentUEC2NPointCryptoPPVInteger_1405A82E0.cpp" />
    <ClCompile Include="Source\_DestroyUBaseAndExponentUECPPointCryptoPPVInteger2_1405A8440.cpp" />
    <ClCompile Include="Source\_DestroyUBaseAndExponentVIntegerCryptoPPV12CryptoP_1405A8020.cpp" />
    <ClCompile Include="Source\_DestroyvectorUBaseAndExponentUEC2NPointCryptoPPVI_140594520.cpp" />
    <ClCompile Include="Source\_DestroyvectorUBaseAndExponentUECPPointCryptoPPVIn_140594D10.cpp" />
    <ClCompile Include="Source\_DestroyvectorUBaseAndExponentVIntegerCryptoPPV12C_140593200.cpp" />
    <ClCompile Include="Source\_Destroy_rangeUBaseAndExponentUEC2NPointCryptoPPVI_14059E130.cpp" />
    <ClCompile Include="Source\_Destroy_rangeUBaseAndExponentUEC2NPointCryptoPPVI_1405A3DC0.cpp" />
    <ClCompile Include="Source\_Destroy_rangeUBaseAndExponentUECPPointCryptoPPVIn_14059E260.cpp" />
    <ClCompile Include="Source\_Destroy_rangeUBaseAndExponentUECPPointCryptoPPVIn_1405A3EF0.cpp" />
    <ClCompile Include="Source\_Destroy_rangeUBaseAndExponentVIntegerCryptoPPV12C_14059DE90.cpp" />
    <ClCompile Include="Source\_Destroy_rangeUBaseAndExponentVIntegerCryptoPPV12C_1405A3AA0.cpp" />
    <ClCompile Include="Source\_Dist_typeV_Vector_iteratorUBaseAndExponentUEC2NPo_1405A5D60.cpp" />
    <ClCompile Include="Source\_Dist_typeV_Vector_iteratorUBaseAndExponentUECPPoi_1405A63C0.cpp" />
    <ClCompile Include="Source\_Dist_typeV_Vector_iteratorUBaseAndExponentVIntege_1405A5700.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CPlayers_AnimusRet_1406E8130.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CPlayers_BillingFo_1406E8170.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CPlayers_MgrItemHi_1406E80B0.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CPlayers_MgrLvHist_1406E80F0.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CPlayers_tblLimMas_1406E81B0.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CPlayers_tblLimMas_1406E8200.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CPlayers_tblLimMas_1406E8250.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CPlayers_tblLimMas_1406E82A0.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__g_Player___1406E8940.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__sPlayerDum___1406E8600.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CPlayers_AnimusReturnDel_1406DA310.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CPlayers_BillingForceClo_1406DA360.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CPlayers_MgrItemHistory__1406DA270.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CPlayers_MgrLvHistory___1406DA2C0.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CPlayers_SkillIndexPerMa_1406DA220.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CPlayers_tblLimMasteryCo_1406DA500.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CPlayers_tblLimMasteryCu_1406DA3B0.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CPlayers_tblLimMasteryCu_1406DA420.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CPlayers_tblLimMastery___1406DA490.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__g_PartyPlayer___1406DE0B0.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__g_Player___1406DE100.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__sPlayerDum___1406DBEC0.cpp" />
    <ClCompile Include="Source\_ECLevelUEAAPEAXIZ_1404E2F80.cpp" />
    <ClCompile Include="Source\_EDL_GroupParametersImplVModExpPrecomputationCrypt_1405ADF30.cpp" />
    <ClCompile Include="Source\_FailItemShortBufferYA_NPEAHEQEAU_material_ItemCom_1402AC160.cpp" />
    <ClCompile Include="Source\_FillPEAUBaseAndExponentUEC2NPointCryptoPPVInteger_1405A47F0.cpp" />
    <ClCompile Include="Source\_FillPEAUBaseAndExponentUECPPointCryptoPPVInteger2_1405A4A90.cpp" />
    <ClCompile Include="Source\_FillPEAUBaseAndExponentVIntegerCryptoPPV12CryptoP_1405A4310.cpp" />
    <ClCompile Include="Source\_GBaseAndExponentUEC2NPointCryptoPPVInteger2Crypto_1405A8860.cpp" />
    <ClCompile Include="Source\_GBaseAndExponentUECPPointCryptoPPVInteger2CryptoP_1405A88E0.cpp" />
    <ClCompile Include="Source\_GBaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAPE_1405A8760.cpp" />
    <ClCompile Include="Source\_GCCharacterUEAAPEAXIZ_14017A4B0.cpp" />
    <ClCompile Include="Source\_GCPlayerUEAAPEAXIZ_140074510.cpp" />
    <ClCompile Include="Source\_GcStaticMember_PlayerAEAAPEAXIZ_14010E8F0.cpp" />
    <ClCompile Include="Source\_GDL_GroupParametersImplVModExpPrecomputationCrypt_14055EF30.cpp" />
    <ClCompile Include="Source\_GDL_GroupParameters_IntegerBasedImplVModExpPrecom_14055E840.cpp" />
    <ClCompile Include="Source\_GetAreaEffectMemberCCharacterQEAAHPEAV1_NHPEAMPEA_140177440.cpp" />
    <ClCompile Include="Source\_GetFlashEffectMemberCCharacterQEAAHPEAV1_NHH0PEAD_140177970.cpp" />
    <ClCompile Include="Source\_GetItemEffectCPlayerQEAAPEAU_ITEM_EFFECTPEAU_db_c_14005CA20.cpp" />
    <ClCompile Include="Source\_GetPartyEffectMemberCCharacterQEAAHPEAV1_NPEAPEAV_140177E40.cpp" />
    <ClCompile Include="Source\_GetPartyMemberInCircleCPlayerQEAAEPEAPEAV1H_NZ_14005D2D0.cpp" />
    <ClCompile Include="Source\_GetTempEffectValueYA_NPEAU_skill_fldHAEAMZ_14039E250.cpp" />
    <ClCompile Include="Source\_get_playerCashDbWorkerAEAAPEAVCPlayerGKZ_1402F2F10.cpp" />
    <ClCompile Include="Source\_Insert_nvectorUBaseAndExponentUEC2NPointCryptoPPV_140596D20.cpp" />
    <ClCompile Include="Source\_Insert_nvectorUBaseAndExponentUECPPointCryptoPPVI_1405976C0.cpp" />
    <ClCompile Include="Source\_Insert_nvectorUBaseAndExponentVIntegerCryptoPPV12_140595F70.cpp" />
    <ClCompile Include="Source\_insert_to_invenCashDbWorkerAEAA_NQEAVCPlayerPEAU__1402F0FA0.cpp" />
    <ClCompile Include="Source\_insert_to_invenCGoldenBoxItemMgrQEAA_NQEAVCPlayer_140414B50.cpp" />
    <ClCompile Include="Source\_Iter_randomPEAUBaseAndExponentUEC2NPointCryptoPPV_1405A4840.cpp" />
    <ClCompile Include="Source\_Iter_randomPEAUBaseAndExponentUECPPointCryptoPPVI_1405A4AE0.cpp" />
    <ClCompile Include="Source\_Iter_randomPEAUBaseAndExponentVIntegerCryptoPPV12_1405A4360.cpp" />
    <ClCompile Include="Source\_LockUnitKeyCPlayerQEAA_NE_NZ_1401068B0.cpp" />
    <ClCompile Include="Source\_LootItem_EventSetCMonsterQEAA_NPEAVCPlayerZ_140144FF0.cpp" />
    <ClCompile Include="Source\_LootItem_QstCMonsterQEAA_NPEAVCPlayerZ_140145390.cpp" />
    <ClCompile Include="Source\_LootItem_RwpCMonsterQEAA_NPEAVCPlayerZ_140144D90.cpp" />
    <ClCompile Include="Source\_LootItem_StdCMonsterQEAA_NPEAVCPlayerZ_140144240.cpp" />
    <ClCompile Include="Source\_Make_heapV_Vector_iteratorUBaseAndExponentUEC2NPo_1405A5DC0.cpp" />
    <ClCompile Include="Source\_Make_heapV_Vector_iteratorUBaseAndExponentUECPPoi_1405A6420.cpp" />
    <ClCompile Include="Source\_Make_heapV_Vector_iteratorUBaseAndExponentVIntege_1405A5760.cpp" />
    <ClCompile Include="Source\_Move_backward_optPEAUBaseAndExponentUEC2NPointCry_1405A4880.cpp" />
    <ClCompile Include="Source\_Move_backward_optPEAUBaseAndExponentUECPPointCryp_1405A4B20.cpp" />
    <ClCompile Include="Source\_Move_backward_optPEAUBaseAndExponentVIntegerCrypt_1405A43A0.cpp" />
    <ClCompile Include="Source\_Move_catPEAUBaseAndExponentUEC2NPointCryptoPPVInt_1405A4860.cpp" />
    <ClCompile Include="Source\_Move_catPEAUBaseAndExponentUECPPointCryptoPPVInte_1405A4B00.cpp" />
    <ClCompile Include="Source\_Move_catPEAUBaseAndExponentVIntegerCryptoPPV12Cry_1405A4380.cpp" />
    <ClCompile Include="Source\_Pop_heapV_Vector_iteratorUBaseAndExponentUEC2NPoi_1405AA070.cpp" />
    <ClCompile Include="Source\_Pop_heapV_Vector_iteratorUBaseAndExponentUECPPoin_1405AAA50.cpp" />
    <ClCompile Include="Source\_Pop_heapV_Vector_iteratorUBaseAndExponentVInteger_1405A96A0.cpp" />
    <ClCompile Include="Source\_Pop_heap_0V_Vector_iteratorUBaseAndExponentUEC2NP_1405A5FB0.cpp" />
    <ClCompile Include="Source\_Pop_heap_0V_Vector_iteratorUBaseAndExponentUECPPo_1405A6610.cpp" />
    <ClCompile Include="Source\_Pop_heap_0V_Vector_iteratorUBaseAndExponentVInteg_1405A5950.cpp" />
    <ClCompile Include="Source\_pre_check_force_attackCPlayerQEAAHPEAVCCharacterP_1400861E0.cpp" />
    <ClCompile Include="Source\_pre_check_in_guild_battleCPlayerQEAAHPEAVCCharact_140087670.cpp" />
    <ClCompile Include="Source\_pre_check_in_guild_battle_raceCPlayerQEAA_NPEAVCC_1400878B0.cpp" />
    <ClCompile Include="Source\_pre_check_normal_attackCPlayerQEAAHPEAVCCharacter_140084670.cpp" />
    <ClCompile Include="Source\_pre_check_siege_attackCPlayerQEAAHPEAVCCharacterP_140086DE0.cpp" />
    <ClCompile Include="Source\_pre_check_skill_attackCPlayerQEAAHPEAVCCharacterP_140085420.cpp" />
    <ClCompile Include="Source\_pre_check_skill_enableCPlayerQEAA_NPEAU_skill_fld_1400A3C30.cpp" />
    <ClCompile Include="Source\_pre_check_skill_gradelimitCPlayerQEAA_NPEAU_skill_1400A3100.cpp" />
    <ClCompile Include="Source\_pre_check_unit_attackCPlayerQEAAHPEAVCCharacterEP_1400869E0.cpp" />
    <ClCompile Include="Source\_pre_check_wpactive_force_attackCPlayerQEAA_NXZ_14008B780.cpp" />
    <ClCompile Include="Source\_pre_check_wpactive_skill_attackCPlayerQEAA_NEPEAU_14008B5C0.cpp" />
    <ClCompile Include="Source\_Ptr_catPEAUBaseAndExponentUEC2NPointCryptoPPVInte_1405A3DA0.cpp" />
    <ClCompile Include="Source\_Ptr_catPEAUBaseAndExponentUECPPointCryptoPPVInteg_1405A3ED0.cpp" />
    <ClCompile Include="Source\_Ptr_catPEAUBaseAndExponentVIntegerCryptoPPV12Cryp_1405A3A80.cpp" />
    <ClCompile Include="Source\_Ptr_catV_Vector_iteratorUBaseAndExponentUEC2NPoin_1405AC020.cpp" />
    <ClCompile Include="Source\_Ptr_catV_Vector_iteratorUBaseAndExponentUECPPoint_1405AC180.cpp" />
    <ClCompile Include="Source\_Ptr_catV_Vector_iteratorUBaseAndExponentVIntegerC_1405ABEC0.cpp" />
    <ClCompile Include="Source\_Push_heapV_Vector_iteratorUBaseAndExponentUEC2NPo_1405AA260.cpp" />
    <ClCompile Include="Source\_Push_heapV_Vector_iteratorUBaseAndExponentUECPPoi_1405AAC40.cpp" />
    <ClCompile Include="Source\_Push_heapV_Vector_iteratorUBaseAndExponentVIntege_1405A9880.cpp" />
    <ClCompile Include="Source\_Push_heap_0V_Vector_iteratorUBaseAndExponentUEC2N_1405A6260.cpp" />
    <ClCompile Include="Source\_Push_heap_0V_Vector_iteratorUBaseAndExponentUECPP_1405A68C0.cpp" />
    <ClCompile Include="Source\_Push_heap_0V_Vector_iteratorUBaseAndExponentVInte_1405A5C00.cpp" />
    <ClCompile Include="Source\_QueryAppointClassOrderProcessorAEAAHPEAVCPlayerPE_1402B8460.cpp" />
    <ClCompile Include="Source\_RegistCandidateRegisterAEAAHPEAVCPlayerPEADZ_1402B6A40.cpp" />
    <ClCompile Include="Source\_ReqNetFinalDecisionFinalDecisionProcessorAEAAXPEA_1402BE030.cpp" />
    <ClCompile Include="Source\_RequestAppointClassOrderProcessorAEAAHPEAVCPlayer_1402B85B0.cpp" />
    <ClCompile Include="Source\_RequestDischargeClassOrderProcessorAEAAHPEAVCPlay_1402B8AC0.cpp" />
    <ClCompile Include="Source\_ResetCirclePlayerCGameObjectQEAAXXZ_14017C8E0.cpp" />
    <ClCompile Include="Source\_ResponseAppointClassOrderProcessorAEAAHPEAVCPlaye_1402B87C0.cpp" />
    <ClCompile Include="Source\_Reward_QuestCPlayerQEAAPEAU_Quest_fldPEAU2EZ_1400CCE90.cpp" />
    <ClCompile Include="Source\_SearchAggroNodeCMonsterAggroMgrIEAAPEAUCAggroNode_14015E210.cpp" />
    <ClCompile Include="Source\_SearchPlayerYAKPEADZ_1403F8710.cpp" />
    <ClCompile Include="Source\_SendVotePaperVoterAEAAHPEAVCPlayerZ_1402C0140.cpp" />
    <ClCompile Include="Source\_SendVoteScoreVoterAEAAXPEAVCPlayerZ_1402BEDE0.cpp" />
    <ClCompile Include="Source\_SetExpendLtdWriterAEAAXPEADPEAU_LTD_EXPENDZ_14024C110.cpp" />
    <ClCompile Include="Source\_set_db_sf_effectCPlayerQEAAXPEAU_SFCONT_DB_BASEZ_14004E2C0.cpp" />
    <ClCompile Include="Source\_set_sf_contCCharacterQEAAXPEAU_sf_continousEGEKGH_1401750D0.cpp" />
    <ClCompile Include="Source\_TidyvectorUBaseAndExponentUEC2NPointCryptoPPVInte_140594560.cpp" />
    <ClCompile Include="Source\_TidyvectorUBaseAndExponentUECPPointCryptoPPVInteg_140594D50.cpp" />
    <ClCompile Include="Source\_TidyvectorUBaseAndExponentVIntegerCryptoPPV12Cryp_140593240.cpp" />
    <ClCompile Include="Source\_TowerAllReturnCPlayerQEAAXE_NZ_1400A1BD0.cpp" />
    <ClCompile Include="Source\_TowerDestroyCPlayerQEAAXPEAVCGuardTowerZ_1400A1480.cpp" />
    <ClCompile Include="Source\_TowerReturnCPlayerQEAAGPEAU_db_con_STORAGE_LISTZ_1400A1A50.cpp" />
    <ClCompile Include="Source\_TrapDestroyCPlayerQEAAXPEAVCTrapEZ_1400A15D0.cpp" />
    <ClCompile Include="Source\_TrapReturnCPlayerQEAAXPEAVCTrapGZ_1400A1650.cpp" />
    <ClCompile Include="Source\_UfillvectorUBaseAndExponentUEC2NPointCryptoPPVInt_140594600.cpp" />
    <ClCompile Include="Source\_UfillvectorUBaseAndExponentUECPPointCryptoPPVInte_140594DE0.cpp" />
    <ClCompile Include="Source\_UfillvectorUBaseAndExponentVIntegerCryptoPPV12Cry_1405932E0.cpp" />
    <ClCompile Include="Source\_UmovePEAUBaseAndExponentUEC2NPointCryptoPPVIntege_14059E8E0.cpp" />
    <ClCompile Include="Source\_UmovePEAUBaseAndExponentUECPPointCryptoPPVInteger_14059EA90.cpp" />
    <ClCompile Include="Source\_UmovePEAUBaseAndExponentVIntegerCryptoPPV12Crypto_14059E540.cpp" />
    <ClCompile Include="Source\_UmoveV_Vector_iteratorUBaseAndExponentUEC2NPointC_14059CC60.cpp" />
    <ClCompile Include="Source\_UmoveV_Vector_iteratorUBaseAndExponentUECPPointCr_14059CDA0.cpp" />
    <ClCompile Include="Source\_UmoveV_Vector_iteratorUBaseAndExponentVIntegerCry_14059CB20.cpp" />
    <ClCompile Include="Source\_Unchecked_move_backwardPEAUBaseAndExponentUEC2NPo_14059E950.cpp" />
    <ClCompile Include="Source\_Unchecked_move_backwardPEAUBaseAndExponentUECPPoi_14059EB00.cpp" />
    <ClCompile Include="Source\_Unchecked_move_backwardPEAUBaseAndExponentVIntege_14059E5B0.cpp" />
    <ClCompile Include="Source\_Unchecked_uninitialized_movePEAUBaseAndExponentUE_1405A4780.cpp" />
    <ClCompile Include="Source\_Unchecked_uninitialized_movePEAUBaseAndExponentUE_1405A4A20.cpp" />
    <ClCompile Include="Source\_Unchecked_uninitialized_movePEAUBaseAndExponentVI_1405A42A0.cpp" />
    <ClCompile Include="Source\_Unchecked_uninitialized_moveV_Vector_iteratorUBas_1405A2ED0.cpp" />
    <ClCompile Include="Source\_Unchecked_uninitialized_moveV_Vector_iteratorUBas_1405A3040.cpp" />
    <ClCompile Include="Source\_Unchecked_uninitialized_moveV_Vector_iteratorUBas_1405A31B0.cpp" />
    <ClCompile Include="Source\_Uninit_copyPEAUBaseAndExponentUEC2NPointCryptoPPV_1405AC560.cpp" />
    <ClCompile Include="Source\_Uninit_copyPEAUBaseAndExponentUECPPointCryptoPPVI_1405AC630.cpp" />
    <ClCompile Include="Source\_Uninit_copyPEAUBaseAndExponentVIntegerCryptoPPV12_1405AC490.cpp" />
    <ClCompile Include="Source\_Uninit_copyV_Vector_iteratorUBaseAndExponentUEC2N_1405AC040.cpp" />
    <ClCompile Include="Source\_Uninit_copyV_Vector_iteratorUBaseAndExponentUECPP_1405AC1A0.cpp" />
    <ClCompile Include="Source\_Uninit_copyV_Vector_iteratorUBaseAndExponentVInte_1405ABEE0.cpp" />
    <ClCompile Include="Source\_Uninit_fill_nPEAUBaseAndExponentUEC2NPointCryptoP_1405A3E10.cpp" />
    <ClCompile Include="Source\_Uninit_fill_nPEAUBaseAndExponentUECPPointCryptoPP_1405A3F40.cpp" />
    <ClCompile Include="Source\_Uninit_fill_nPEAUBaseAndExponentVIntegerCryptoPPV_1405A3AF0.cpp" />
    <ClCompile Include="Source\_Uninit_movePEAUBaseAndExponentUEC2NPointCryptoPPV_1405A7B50.cpp" />
    <ClCompile Include="Source\_Uninit_movePEAUBaseAndExponentUECPPointCryptoPPVI_1405A7BF0.cpp" />
    <ClCompile Include="Source\_Uninit_movePEAUBaseAndExponentVIntegerCryptoPPV12_1405A7AB0.cpp" />
    <ClCompile Include="Source\_Uninit_moveV_Vector_iteratorUBaseAndExponentUEC2N_1405A7530.cpp" />
    <ClCompile Include="Source\_Uninit_moveV_Vector_iteratorUBaseAndExponentUECPP_1405A7660.cpp" />
    <ClCompile Include="Source\_Uninit_moveV_Vector_iteratorUBaseAndExponentVInte_1405A7400.cpp" />
    <ClCompile Include="Source\_UnitDestroyCPlayerQEAAXEZ_140106DE0.cpp" />
    <ClCompile Include="Source\_UpdateRateSendToAllPlayerYAXXZ_1402A4FB0.cpp" />
    <ClCompile Include="Source\_UpdateUnitDebtCPlayerQEAAXEKZ_140106970.cpp" />
    <ClCompile Include="Source\_Val_typeV_Vector_iteratorUBaseAndExponentUEC2NPoi_1405A5D90.cpp" />
    <ClCompile Include="Source\_Val_typeV_Vector_iteratorUBaseAndExponentUECPPoin_1405A63F0.cpp" />
    <ClCompile Include="Source\_Val_typeV_Vector_iteratorUBaseAndExponentVInteger_1405A5730.cpp" />
    <ClCompile Include="Source\_VoteVoterAEAAHPEAVCPlayerPEADZ_1402BEFD0.cpp" />
    <ClCompile Include="Source\_XlenvectorUBaseAndExponentUEC2NPointCryptoPPVInte_140594660.cpp" />
    <ClCompile Include="Source\_XlenvectorUBaseAndExponentUECPPointCryptoPPVInteg_140594E40.cpp" />
    <ClCompile Include="Source\_XlenvectorUBaseAndExponentVIntegerCryptoPPV12Cryp_140593340.cpp" />
    <ClCompile Include="Source\__destroy_itemYA_NPEAVCPlayerPEAU__item_combine_ex_1402AE150.cpp" />
    <ClCompile Include="Source\__make_itemYAEPEAVCPlayerPEAU__item_combine_ex_ite_1402ADED0.cpp" />
  </ItemGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>