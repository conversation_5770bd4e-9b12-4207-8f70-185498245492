#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?assign@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAAX_KAEBVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1400059B6

void  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::assign(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, unsigned int64_t _Count, CMoveMapLimitRightInfo *_Val)
{
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::assign(this, _Count, _Val);
}
