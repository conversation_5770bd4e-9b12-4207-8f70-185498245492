#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Select_WeeklyGuildRankOwnerGuild@CRFWorldDatabase@@QEAAEPEADEEPEAU_weeklyguildrank_owner_info@@@Z
 * Address: 0x1404A7AC0

char  CRFWorldDatabase::Select_WeeklyGuildRankOwnerGuild(CRFWorldDatabase *this, char *szDate, char byRace, char byLimitCnt, _weeklyguildrank_owner_info *pkInfo)
{
  int64_t *v5; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@6
  char *v8; // rax@20
  char *v9; // rax@20
  char *v10; // rax@20
  char *v11; // rax@20
  char *v12; // rax@20
  char *v13; // rax@20
  char *v14; // rax@20
  char *v15; // rax@20
  char *v16; // rax@20
  int64_t v17; // rax@20
  int64_t v18; // [sp+0h] [bp-488h]@1
  void *SQLStmt; // [sp+20h] [bp-468h]@7
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-460h]@20
  char Dest; // [sp+40h] [bp-448h]@7
  SQLLEN v22; // [sp+458h] [bp-30h]@20
  int16_t v23; // [sp+464h] [bp-24h]@12
  int v24; // [sp+468h] [bp-20h]@7
  unsigned int64_t v25; // [sp+478h] [bp-10h]@4
  CRFWorldDatabase *v26; // [sp+490h] [bp+8h]@1
  char v27; // [sp+4A0h] [bp+18h]@1

  v27 = byRace;
  v26 = this;
  v5 = &v18;
  for ( i = 288i64; i; --i )
  {
    *(uint32_t*)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  v25 = (unsigned int64_t)&v18 ^ _security_cookie;
  if ( pkInfo && (signed int)(unsigned int8_t)byRace < 3 )
  {
    v24 = 0;
    LODWORD(SQLStmt) = (unsigned int8_t)byRace;
    sprintf(
      &Dest,
      "select top %u serial, id, race, rank, grade, killpvppoint, guildbattlepvppoint, sumlv from [dbo].[tbl_PvpPointGuil"
      "dRank%s] as p where sumlv > 0 and race=%u order by sumpvppoint desc, killpvppoint desc, sumlv desc",
      (unsigned int8_t)byLimitCnt,
      szDate);
    if ( v26->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v26->vfptr, &Dest);
    if ( v26->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v26->vfptr) )
    {
      v23 = SQLExecDirect_0(v26->m_hStmtSelect, &Dest, -3);
      if ( v23 && v23 != 1 )
      {
        if ( v23 == 100 )
        {
          result = 2;
        }
        else
        {
          SQLStmt = v26->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v26->vfptr, v23, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v26->vfptr, v23, v26->m_hStmtSelect);
          result = 1;
        }
      }
      else
      {
        for ( pkInfo->wCount = 2 * (unsigned int8_t)v27; ; ++pkInfo->wCount )
        {
          v23 = SQLFetch_0(v26->m_hStmtSelect);
          if ( v23 )
          {
            if ( v23 != 1 )
              break;
          }
          v8 = (char *)&pkInfo->list[pkInfo->wCount];
          StrLen_or_IndPtr = &v22;
          SQLStmt = 0i64;
          v23 = SQLGetData_0(v26->m_hStmtSelect, 1u, -18, v8, 0i64, &v22);
          v9 = pkInfo->list[pkInfo->wCount].wszGuildName;
          StrLen_or_IndPtr = &v22;
          SQLStmt = (void *)17;
          v23 = SQLGetData_0(v26->m_hStmtSelect, 2u, 1, v9, 17i64, &v22);
          v10 = pkInfo->list[pkInfo->wCount].wszGuildName;
          StrLen_or_IndPtr = &v22;
          SQLStmt = (void *)17;
          v23 = SQLGetData_0(v26->m_hStmtSelect, 2u, 1, v10, 17i64, &v22);
          v11 = &pkInfo->list[pkInfo->wCount].byRace;
          StrLen_or_IndPtr = &v22;
          SQLStmt = 0i64;
          v23 = SQLGetData_0(v26->m_hStmtSelect, 3u, -6, v11, 0i64, &v22);
          v12 = (char *)&pkInfo->list[pkInfo->wCount].wRank;
          StrLen_or_IndPtr = &v22;
          SQLStmt = 0i64;
          v23 = SQLGetData_0(v26->m_hStmtSelect, 4u, -17, v12, 0i64, &v22);
          v13 = &pkInfo->list[pkInfo->wCount].byGrade;
          StrLen_or_IndPtr = &v22;
          SQLStmt = 0i64;
          v23 = SQLGetData_0(v26->m_hStmtSelect, 5u, -6, v13, 0i64, &v22);
          v14 = (char *)&pkInfo->list[pkInfo->wCount].dKillPvpPoint;
          StrLen_or_IndPtr = &v22;
          SQLStmt = 0i64;
          v23 = SQLGetData_0(v26->m_hStmtSelect, 6u, 8, v14, 0i64, &v22);
          v15 = (char *)&pkInfo->list[pkInfo->wCount].dGuildBattlePvpPoint;
          StrLen_or_IndPtr = &v22;
          SQLStmt = 0i64;
          v23 = SQLGetData_0(v26->m_hStmtSelect, 7u, 8, v15, 0i64, &v22);
          v16 = (char *)&pkInfo->list[pkInfo->wCount].dwSumLv;
          StrLen_or_IndPtr = &v22;
          SQLStmt = 0i64;
          v23 = SQLGetData_0(v26->m_hStmtSelect, 8u, -18, v16, 0i64, &v22);
          v17 = pkInfo->list[pkInfo->wCount].byRace;
          if ( pkInfo->list[pkInfo->wCount].byRace >= 3 )
            ++pkInfo->wRaceCnt[3];
          else
            ++pkInfo->wRaceCnt[pkInfo->list[pkInfo->wCount].byRace];
        }
        if ( v26->m_hStmtSelect )
          SQLCloseCursor_0(v26->m_hStmtSelect);
        if ( v26->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v26->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
    else
    {
      CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v26->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
