#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_fill_n@PEAPEAVCLogTypeDBTask@@_KPEAV1@@stdext@@YAXPEAPEAVCLogTypeDBTask@@_KAEBQEAV1@@Z
 * Address: 0x140008A4E

void  stdext::unchecked_fill_n<CLogTypeDBTask * *,unsigned int64_t,CLogTypeDBTask *>(CLogTypeDBTask **_First, unsigned int64_t _Count, CLogTypeDBTask *const *_Val)
{
  stdext::unchecked_fill_n<CLogTypeDBTask * *,unsigned int64_t,CLogTypeDBTask *>(_First, _Count, _Val);
}
