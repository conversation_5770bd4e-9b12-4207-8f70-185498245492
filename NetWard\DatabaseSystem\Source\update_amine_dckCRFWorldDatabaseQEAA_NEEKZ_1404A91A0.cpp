#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?update_amine_dck@CRFWorldDatabase@@QEAA_NEEK@Z
 * Address: 0x1404A91A0

bool  CRFWorldDatabase::update_amine_dck(CRFWorldDatabase *this, char byType, char byRace, unsigned int dwSerial)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-E8h]@1
  unsigned int v8; // [sp+20h] [bp-C8h]@4
  char Dest; // [sp+40h] [bp-A8h]@4
  char v10; // [sp+41h] [bp-A7h]@4
  unsigned int64_t v11; // [sp+D0h] [bp-18h]@4
  CRFWorldDatabase *v12; // [sp+F0h] [bp+8h]@1

  v12 = this;
  v4 = &v7;
  for ( i = 56i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v11 = (unsigned int64_t)&v7 ^ _security_cookie;
  Dest = 0;
  memset(&v10, 0, 0x7Fui64);
  v8 = dwSerial;
  sprintf(&Dest, "{ CALL pupdate_automine_dck(%d,%d,%d) }", (unsigned int8_t)byType, (unsigned int8_t)byRace);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v12->vfptr, &Dest, 0);
}
