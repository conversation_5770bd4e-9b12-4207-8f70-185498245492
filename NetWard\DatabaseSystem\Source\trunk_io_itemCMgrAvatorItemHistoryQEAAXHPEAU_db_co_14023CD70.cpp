#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?trunk_io_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@_NKKPEAD@Z
 * Address: 0x14023CD70

void  CMgrAvatorItemHistory::trunk_io_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pIOItem, bool bInput, unsigned int dwFeeDalant, unsigned int dwNewDalant, char *pszFileName)
{
  int64_t *v7; // rdi@1
  signed int64_t i; // rcx@1
  char *v9; // rax@4
  int64_t v10; // [sp+0h] [bp-B8h]@1
  unsigned int64_t v11; // [sp+20h] [bp-98h]@4
  char *v12; // [sp+28h] [bp-90h]@4
  unsigned int64_t v13; // [sp+30h] [bp-88h]@4
  unsigned int v14; // [sp+38h] [bp-80h]@4
  unsigned int v15; // [sp+40h] [bp-78h]@4
  char *v16; // [sp+48h] [bp-70h]@4
  char *v17; // [sp+50h] [bp-68h]@4
  _base_fld *v18; // [sp+60h] [bp-58h]@4
  const char *v19; // [sp+78h] [bp-40h]@4
  const char *v20; // [sp+80h] [bp-38h]@4
  int v21; // [sp+94h] [bp-24h]@4
  char *v22; // [sp+98h] [bp-20h]@4
  char *v23; // [sp+A0h] [bp-18h]@4
  int nTableCode; // [sp+A8h] [bp-10h]@4
  CMgrAvatorItemHistory *v25; // [sp+C0h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v26; // [sp+D0h] [bp+18h]@1
  bool v27; // [sp+D8h] [bp+20h]@1

  v27 = bInput;
  v26 = pIOItem;
  v25 = this;
  v7 = &v10;
  for ( i = 44i64; i; --i )
  {
    *(uint32_t*)v7 = -858993460;
    v7 = (int64_t *)((char *)v7 + 4);
  }
  v18 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pIOItem->m_byTableCode, pIOItem->m_wItemIndex);
  v19 = "OUT";
  v20 = "IN";
  v21 = v27 != 0;
  v22 = v25->m_szCurTime;
  v23 = v25->m_szCurDate;
  nTableCode = v26->m_byTableCode;
  v9 = DisplayItemUpgInfo(nTableCode, v26->m_dwLv);
  v17 = v22;
  v16 = v23;
  v15 = dwNewDalant;
  v14 = dwFeeDalant;
  v13 = v26->m_lnUID;
  v12 = v9;
  v11 = v26->m_dwDur;
  sprintf(sData, "TRUNK ITEM %s: %s_%u_@%s[%I64u] pay(%d) $D:%d [%s %s]\r\n", (&v19)[8 * v21], v18->m_strCode);
  CMgrAvatorItemHistory::WriteFile(v25, pszFileName, sData);
}
