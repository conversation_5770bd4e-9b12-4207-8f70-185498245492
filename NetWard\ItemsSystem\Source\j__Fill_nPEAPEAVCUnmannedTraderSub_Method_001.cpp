#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Fill_n@PEAPEAVCUnmannedTraderSubClassInfo@@_KPEAV1@@std@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140008B7A

void  std::_Fill_n<CUnmannedTraderSubClassInfo * *,unsigned int64_t,CUnmannedTraderSubClassInfo *>(CUnmannedTraderSubClassInfo **_First, unsigned int64_t _Count, CUnmannedTraderSubClassInfo *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  std::_Fill_n<CUnmannedTraderSubClassInfo * *,unsigned int64_t,CUnmannedTraderSubClassInfo *>(
    _First,
    _Count,
    _Val,
    __formal);
}
