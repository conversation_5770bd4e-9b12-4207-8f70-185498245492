#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ODBC___GetSetupProc
 * Address: 0x1404DB8B8

int64_t ( * ODBC___GetSetupProc(char *szFuncName))()
{
  HMODULE v1; // rax@1
  char *v2; // rdi@1
  int64_t ( *result)(); // rax@3
  int64_t ( *v4)(); // rbx@4
  HMODULE v5; // rax@5
  char v6; // [sp+30h] [bp-548h]@5
  char Filename; // [sp+140h] [bp-438h]@5
  char Text; // [sp+250h] [bp-328h]@5

  v1 = g_hInstSetup;
  v2 = szFuncName;
  if ( g_hInstSetup || (v1 = LoadProperSetupDLL(), (g_hInstSetup = v1) != 0i64) )
  {
    v4 = GetProcAddress(v1, v2);
    if ( !v4 )
    {
      GetModuleFileNameA(g_hInstSetup, &Filename, 0x105u);
      v5 = GetModuleHandleA(0i64);
      GetModuleFileNameA(v5, &v6, 0x105u);
      wsprintfA(
        &Text,
        "The program %s, or one of its DLLs attempted to call the function %s which is not supported in the loaded ODBC i"
        "nstaller DLL (%s).  Press OK to proceed.",
        &v6,
        v2);
      MessageBoxA(0i64, &Text, "ODBC Installer Error", 0x40u);
    }
    result = v4;
  }
  else
  {
    MessageBoxA(
      0i64,
      "The ODBC installer DLL (ODBCCP32.DLL) is not installed on this system.",
      "ODBC Installer Error",
      0x40u);
    result = 0i64;
  }
  return result;
}
