#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Set_FromINIToStruct@CGoldenBoxItemMgr@@QEAAXPEAU_golden_box_item_ini@@@Z
 * Address: 0x140413900

void  CGoldenBoxItemMgr::Set_FromINIToStruct(CGoldenBoxItemMgr *this, _golden_box_item_ini *pIni)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@5
  unsigned int8_t v6; // [sp+24h] [bp-24h]@7
  _base_fld *v7; // [sp+28h] [bp-20h]@8
  int k; // [sp+30h] [bp-18h]@9
  _base_fld *v9; // [sp+38h] [bp-10h]@12
  CGoldenBoxItemMgr *v10; // [sp+50h] [bp+8h]@1
  _golden_box_item_ini *v11; // [sp+58h] [bp+10h]@1

  v11 = pIni;
  v10 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( pIni )
  {
    v10->m_golden_box_item.m_dwStarterBoxCnt = pIni->m_dwStarterBoxCnt;
    for ( j = 0; j < v10->m_golden_box_event.m_ini.m_byLoopCnt; ++j )
    {
      v6 = GetItemTableCode(v11->m_szGoldenBoxcode[(signed int64_t)j]);
      if ( v6 == 255 )
        return;
      v7 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v6, v11->m_szGoldenBoxcode[(signed int64_t)j]);
      if ( !v7 )
        return;
      v10->m_golden_box_item.m_byBoxTableCode[j] = v6;
      v10->m_golden_box_item.m_dwBoxIndex[j] = v7->m_dwIndex;
      v10->m_golden_box_item.m_wBoxMax[j] = v11->m_wGoldenBoxmax[j];
      v10->m_golden_box_item.m_bygolden_item_num[j] = v11->m_bygolden_item_num[j];
      for ( k = 0; k < v10->m_golden_box_item.m_bygolden_item_num[j]; ++k )
      {
        v10->m_golden_box_item.m_golden_box_item_info[j][k].m_byTableCode = -1;
        v10->m_golden_box_item.m_golden_box_item_info[j][k].m_dwIndex = 255;
        v10->m_golden_box_item.m_golden_box_item_info[j][k].m_wNum = v11->m_golden_box_item_list[j][k].m_wLimcount;
        v10->m_golden_box_item.m_golden_box_item_info[j][k].m_wRate = v11->m_golden_box_item_list[j][k].m_wRate;
        v6 = GetItemTableCode(v11->m_golden_box_item_list[j][k].m_szLimcode);
        if ( v6 == 255 )
          return;
        v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v6, v11->m_golden_box_item_list[j][k].m_szLimcode);
        if ( !v9 )
          return;
        v10->m_golden_box_item.m_golden_box_item_info[j][k].m_byTableCode = v6;
        v10->m_golden_box_item.m_golden_box_item_info[j][k].m_dwIndex = v9->m_dwIndex;
      }
    }
    CGoldenBoxItemMgr::Set_ToStruct(v10);
  }
}
