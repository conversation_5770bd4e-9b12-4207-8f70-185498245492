#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetPatriarchTaxMoney@CUnmannedTraderTaxRateManager@@QEAAXEK@Z
 * Address: 0x14038E300

void  CUnmannedTraderTaxRateManager::SetPatriarchTaxMoney(CUnmannedTraderTaxRateManager *this, char byRace, unsigned int dwTax)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  TRC_AutoTrade **v5; // rax@7
  int64_t v6; // [sp+0h] [bp-28h]@1
  CUnmannedTraderTaxRateManager *v7; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1
  unsigned int dwTaxa; // [sp+40h] [bp+18h]@1

  dwTaxa = dwTax;
  v8 = byRace;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -*********;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( !std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v7->m_vecTRC)
    && std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(&v7->m_vecTRC) > (unsigned int8_t)v8 )
  {
    v5 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v7->m_vecTRC, (unsigned int8_t)v8);
    TRC_AutoTrade::SetPatriarchTaxMoney(*v5, dwTaxa);
  }
}
