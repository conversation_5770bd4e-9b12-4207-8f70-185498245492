#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetKey@_LIST@_SFCONT_DB_BASE@@QEAAXEEGEG@Z
 * Address: 0x140120B60

void  _SFCONT_DB_BASE::_LIST::SetKey(_SFCONT_DB_BASE::_LIST *this, char pl_byOrder, char pl_byEffectCode, unsigned int16_t pl_wEffectIndex, char pl_byLv, unsigned int16_t pl_wLeftTime)
{
  this->dwKey = 0;
  this->dwKey |= (unsigned int8_t)pl_byOrder << 28;
  this->dwKey |= (unsigned int8_t)pl_byEffectCode << 26;
  this->dwKey |= pl_wEffectIndex << 16;
  this->dwKey |= (unsigned int8_t)pl_byLv << 12;
  this->dwKey |= pl_wLeftTime;
}
