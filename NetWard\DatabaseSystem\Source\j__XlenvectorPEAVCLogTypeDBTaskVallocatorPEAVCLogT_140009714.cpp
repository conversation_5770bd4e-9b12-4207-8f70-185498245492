#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_<PERSON>len@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@KAXXZ
 * Address: 0x140009714

void  __noreturn std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Xlen(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_<PERSON>len(this);
}
