#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_Class@CUserDB@@QEAA_NPEADEG@Z
 * Address: 0x140116000

char  CUserDB::Update_Class(CUserDB *this, char *pszClassCode, char byHistoryRecordNum, unsigned int16_t wHistoryClassIndex)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v7; // [sp+0h] [bp-28h]@1
  CUserDB *v8; // [sp+30h] [bp+8h]@1
  char v9; // [sp+40h] [bp+18h]@1
  unsigned int16_t v10; // [sp+48h] [bp+20h]@1

  v10 = wHistoryClassIndex;
  v9 = byHistoryRecordNum;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned int8_t)byHistoryRecordNum < 3 )
  {
    strcpy_0(v8->m_AvatorData.dbAvator.m_szClassCode, pszClassCode);
    v8->m_AvatorData.dbAvator.m_zClassHistory[(unsigned int8_t)v9] = v10;
    if ( (unsigned int8_t)v9 + 1 > v8->m_AvatorData.dbAvator.m_byLastClassGrade )
      v8->m_AvatorData.dbAvator.m_byLastClassGrade = v9 + 1;
    v8->m_bDataUpdate = 1;
    result = 1;
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_Class(): byHistoryRecordNum (%d) => failed ",
      v8->m_aszAvatorName,
      (unsigned int8_t)byHistoryRecordNum);
    result = 0;
  }
  return result;
}
