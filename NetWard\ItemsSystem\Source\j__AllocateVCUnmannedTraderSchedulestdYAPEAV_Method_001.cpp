#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Allocate@VCUnmannedTraderSchedule@@@std@@YAPEAVCUnmannedTraderSchedule@@_KPEAV1@@Z
 * Address: 0x140004E17

CUnmannedTraderSchedule * std::_Allocate<CUnmannedTraderSchedule>(unsigned int64_t _Count, CUnmannedTraderSchedule *__formal)
{
  return std::_Allocate<CUnmannedTraderSchedule>(_Count, __formal);
}
