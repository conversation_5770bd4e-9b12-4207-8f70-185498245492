#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Iter_cat@PEAPEAVCUnmannedTraderSubClassInfo@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCUnmannedTraderSubClassInfo@@@Z
 * Address: 0x140012D0F

std::random_access_iterator_tag  std::_Iter_cat<CUnmannedTraderSubClassInfo * *>(CUnmannedTraderSubClassInfo **const *__formal)
{
  return std::_Iter_cat<CUnmannedTraderSubClassInfo * *>(__formal);
}
