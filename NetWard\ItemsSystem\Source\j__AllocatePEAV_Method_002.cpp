#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Allocate@PEAVCUnmannedTraderSortType@@@std@@YAPEAPEAVCUnmannedTraderSortType@@_KPEAPEAV1@@Z
 * Address: 0x14000A36C

CUnmannedTraderSortType ** std::_Allocate<CUnmannedTraderSortType *>(unsigned int64_t _Count, CUnmannedTraderSortType **__formal)
{
  return std::_Allocate<CUnmannedTraderSortType *>(_Count, __formal);
}
