#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_SetItemInfo@LtdWriter@@AEAAXEPEAU_db_con@_STORAGE_LIST@@EPEAU_LTD_ITEMINFO@@H@Z
 * Address: 0x14000D7CE

void  LtdWriter::_SetItemInfo(LtdWriter *this, char byIndex, _STORAGE_LIST::_db_con *pItem, char byOver<PERSON><PERSON><PERSON>, _LTD_ITEMINFO *pi, int nMoveType)
{
  LtdWriter::_SetItemInfo(this, byIndex, pItem, byOverlapNum, pi, nMoveType);
}
