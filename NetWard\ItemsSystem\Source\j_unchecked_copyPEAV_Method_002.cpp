#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_copy@PEAVCUnmannedTraderSchedule@@PEAV1@@stdext@@YAPEAVCUnmannedTraderSchedule@@PEAV1@00@Z
 * Address: 0x1400082F1

CUnmannedTraderSchedule * stdext::unchecked_copy<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Dest)
{
  return stdext::unchecked_copy<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *>(_First, _Last, _Dest);
}
