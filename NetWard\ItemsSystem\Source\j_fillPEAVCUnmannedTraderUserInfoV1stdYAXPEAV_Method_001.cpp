#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$fill@PEAVCUnmannedTraderUserInfo@@V1@@std@@YAXPEAVCUnmannedTraderUserInfo@@0AEBV1@@Z
 * Address: 0x14000AA1F

void  std::fill<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo>(CUnmannedTraderUserInfo *_First, CUnmannedTraderUserInfo *_Last, CUnmannedTraderUserInfo *_Val)
{
  std::fill<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo>(_First, _Last, _Val);
}
