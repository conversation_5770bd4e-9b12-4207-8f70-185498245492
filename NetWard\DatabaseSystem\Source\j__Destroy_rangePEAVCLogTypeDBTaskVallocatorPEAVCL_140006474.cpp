#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Destroy_range@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@YAXPEAPEAVCLogTypeDBTask@@0AEAV?$allocator@PEAVCLogTypeDBTask@@@0@@Z
 * Address: 0x140006474

void  std::_Destroy_range<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(CLogTypeDBTask **_First, CLogTypeDBTask **_Last, std::allocator<CLogTypeDBTask *> *_Al)
{
  std::_Destroy_range<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(_First, _Last, _Al);
}
