#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Allocate@PEAVCMoveMapLimitInfo@@@std@@YAPEAPEAVCMoveMapLimitInfo@@_KPEAPEAV1@@Z
 * Address: 0x1403A3390

CMoveMapLimitInfo ** std::_Allocate<CMoveMapLimitInfo *>(unsigned int64_t _Count, CMoveMapLimitInfo **__formal)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-48h]@1
  std::bad_alloc v6; // [sp+20h] [bp-28h]@7
  unsigned int64_t v7; // [sp+50h] [bp+8h]@1

  v7 = _Count;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v7 )
  {
    if ( 0xFFFFFFFFFFFFFFFFui64 / v7 < 8 )
    {
      std::bad_alloc::bad_alloc(&v6, 0i64);
      CxxThrowException_0(&v6, &TI2_AVbad_alloc_std__);
    }
  }
  else
  {
    v7 = 0i64;
  }
  return (CMoveMapLimitInfo **)operator new(8 * v7);
}
