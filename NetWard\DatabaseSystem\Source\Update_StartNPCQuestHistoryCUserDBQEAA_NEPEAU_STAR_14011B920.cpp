#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_StartNPCQuestHistory@CUserDB@@QEAA_NEPEAU_START_NPC_QUEST_HISTORY@_QUEST_DB_BASE@@@Z
 * Address: 0x14011B920

char  CUserDB::Update_StartNPCQuestHistory(CUserDB *this, char byIndex, _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY *pHisData)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  CUserDB *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY *Source; // [sp+50h] [bp+18h]@1

  Source = pHisData;
  v9 = byIndex;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v7 = (unsigned int8_t)byIndex;
  if ( (unsigned int8_t)byIndex < *(&pdwCnt + (v8->m_AvatorData.dbAvator.m_byRaceSexCode >> 1)) )
  {
    strcpy_0(v8->m_AvatorData.dbQuest.m_StartHistory[(unsigned int8_t)byIndex].szQuestCode, pHisData->szQuestCode);
    v8->m_AvatorData.dbQuest.m_StartHistory[(unsigned int8_t)v9].byLevel = Source->byLevel;
    GetLocalTime(&v8->m_AvatorData.dbQuest.m_StartHistory[(unsigned int8_t)v9].tmStartTime);
    v8->m_AvatorData.dbQuest.m_StartHistory[(unsigned int8_t)v9].nEndTime = Source->nEndTime;
    ++v8->m_AvatorData.dbQuest.dwListCnt;
    result = 1;
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_NPCQuestHistory(Index OVER) : %d",
      v8->m_aszAvatorName,
      (unsigned int8_t)byIndex);
    result = 0;
  }
  return result;
}
