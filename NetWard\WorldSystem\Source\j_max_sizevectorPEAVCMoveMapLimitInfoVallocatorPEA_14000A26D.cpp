#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?max_size@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x14000A26D

unsigned int64_t  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::max_size(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  return std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::max_size(this);
}
