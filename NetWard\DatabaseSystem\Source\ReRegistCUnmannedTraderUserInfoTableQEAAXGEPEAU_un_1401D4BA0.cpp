#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ReRegist@CUnmannedTraderUserInfoTable@@QEAAXGEPEAU_unmannedtrader_re_regist_request_clzo@@@Z
 * Address: 0x1401D4BA0

void  CUnmannedTraderUserInfoTable::ReRegist(CUnmannedTraderUserInfoTable *this, unsigned int16_t wInx, char byType, _unmannedtrader_re_regist_request_clzo *pRequest)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  CUnmannedTraderUserInfo *v6; // rax@5
  int64_t v7; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfoTable *v8; // [sp+30h] [bp+8h]@1
  unsigned int16_t v9; // [sp+38h] [bp+10h]@1
  char v10; // [sp+40h] [bp+18h]@1
  _unmannedtrader_re_regist_request_clzo *pRequesta; // [sp+48h] [bp+20h]@1

  pRequesta = pRequest;
  v10 = byType;
  v9 = wInx;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( CUnmannedTraderUserInfoTable::CheckwIndexAndType(v8, wInx, byType, "CUnmannedTraderUserInfoTable::ReRegist(...)") )
  {
    v6 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](&v8->m_veckInfo, v9);
    CUnmannedTraderUserInfo::ReRegist(v6, v10, pRequesta, v8->m_pkLogger);
  }
}
