#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Move_cat@PEAPEAVCUnmannedTraderClassInfo@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAPEAVCUnmannedTraderClassInfo@@@Z
 * Address: 0x140003CC4

std::_Undefined_move_tag  std::_Move_cat<CUnmannedTraderClassInfo * *>(CUnmannedTraderClassInfo **const *__formal)
{
  return std::_Move_cat<CUnmannedTraderClassInfo * *>(__formal);
}
