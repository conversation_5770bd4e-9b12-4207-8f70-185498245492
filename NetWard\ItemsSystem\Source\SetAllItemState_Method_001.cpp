#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetAllItemState@CUnmannedTraderUserInfo@@AEAAXEE@Z
 * Address: 0x14035FF90

void  CUnmannedTraderUserInfo::SetAllItemState(CUnmannedTraderUserInfo *this, char byState, char byMaxCnt)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  CUnmannedTraderRegistItemInfo *v5; // rax@7
  int64_t v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  CUnmannedTraderUserInfo *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  char v10; // [sp+50h] [bp+18h]@1

  v10 = byMaxCnt;
  v9 = byState;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(&v8->m_vecRegistItemInfo) >= (unsigned int8_t)byMaxCnt )
  {
    for ( j = 0; j < (unsigned int8_t)v10; ++j )
    {
      v5 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v8->m_vecRegistItemInfo,
             j);
      CUnmannedTraderRegistItemInfo::SetState(v5, v9);
    }
  }
}
