#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Regist@CMoveMapLimitRightInfo@@QEAA_NH@Z
 * Address: 0x1403AC960

char  CMoveMapLimitRightInfo::Regist(CMoveMapLimitRightInfo *this, int iType)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char v4; // al@5
  int64_t v5; // [sp+0h] [bp-108h]@1
  CMoveMapLimitRight *_Val; // [sp+28h] [bp-E0h]@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > v7; // [sp+48h] [bp-C0h]@6
  char v8; // [sp+68h] [bp-A0h]@6
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *result; // [sp+80h] [bp-88h]@6
  char v10; // [sp+88h] [bp-80h]@6
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v11; // [sp+A0h] [bp-68h]@6
  bool v12; // [sp+A8h] [bp-60h]@6
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > v13; // [sp+B0h] [bp-58h]@6
  char v14; // [sp+C8h] [bp-40h]@7
  char v15; // [sp+C9h] [bp-3Fh]@8
  int64_t v16; // [sp+D0h] [bp-38h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v17; // [sp+D8h] [bp-30h]@6
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v18; // [sp+E0h] [bp-28h]@6
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v19; // [sp+E8h] [bp-20h]@6
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v20; // [sp+F0h] [bp-18h]@6
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Right; // [sp+F8h] [bp-10h]@6
  CMoveMapLimitRightInfo *v22; // [sp+110h] [bp+8h]@1

  v22 = this;
  v2 = &v5;
  for ( i = 64i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v16 = -2i64;
  _Val = CMoveMapLimitRight::Create(iType);
  if ( _Val )
  {
    result = (std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v8;
    v11 = (std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v10;
    v17 = std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::end(
            &v22->m_vecRight,
            (std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v8);
    v18 = v17;
    v19 = std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::begin(&v22->m_vecRight, v11);
    std::find<std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>,CMoveMapLimitRight *>(
      (std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v7,
      v19,
      v18,
      &_Val);
    v20 = std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::end(&v22->m_vecRight, &v13);
    _Right = (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)v20;
    v12 = std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator!=(
            &v7,
            (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v20->_Mycont);
    std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(&v13);
    if ( v12 )
    {
      v14 = 0;
      std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>((std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v7);
      v4 = v14;
    }
    else
    {
      std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::push_back(&v22->m_vecRight, &_Val);
      v15 = 1;
      std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>((std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v7);
      v4 = v15;
    }
  }
  else
  {
    v4 = 0;
  }
  return v4;
}
