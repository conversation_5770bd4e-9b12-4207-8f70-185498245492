#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?size@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEBA_KXZ
 * Address: 0x14000F5B5

unsigned int64_t  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::size(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  return std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::size(this);
}
