#pragma once

#ifndef NETWARD_SECURITY_H
#define NETWARD_SECURITY_H

// NetWard Security System Header
// Generated from RF Online ZoneServerUD_x64 decompiled source

#include <windows.h>
#include <stdint.h>
#include <memory>
#include <vector>
#include <string>

// Forward declarations
struct ATL::CSecurityDescriptor;

// Class definitions

// ATL::CSecurityDescriptor
struct ATL::CSecurityDescriptor
{
 void *m_pSD;
 void *m_pOwner;
 void *m_pGroup;
 _ACL *m_pDACL;
 _ACL *m_pSACL;
};


#endif // NETWARD_SECURITY_H
