#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?ProcForceMoveHQ@CMoveMapLimitInfoPortal@@AEAAEHPEADPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x140005781

char  CMoveMapLimitInfoPortal::ProcForceMoveHQ(CMoveMapLimitInfoPortal *this, int iUserInx, char *pRequest, CMoveMapLimitRightInfo *pkRight)
{
  return CMoveMapLimitInfoPortal::ProcForceMoveHQ(this, iUserInx, pRequest, pkRight);
}
