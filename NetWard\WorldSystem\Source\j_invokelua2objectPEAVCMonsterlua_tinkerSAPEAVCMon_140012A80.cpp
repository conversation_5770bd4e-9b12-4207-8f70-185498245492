#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?invoke@?$lua2object@PEAVCMonster@@@lua_tinker@@SAPEAVCMonster@@PEAUlua_State@@H@Z
 * Address: 0x140012A80

CMonster * lua_tinker::lua2object<CMonster *>::invoke(lua_tinker::lua2object<CMonster *> *this, struct lua_State *L, int index)
{
  return lua_tinker::lua2object<CMonster *>::invoke(this, L, index);
}
