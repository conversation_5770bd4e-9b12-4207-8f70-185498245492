#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?erase@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEAA?AV?$_Vector_iterator@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@2@V32@0@Z
 * Address: 0x140001CAD

std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > * std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::erase(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *result, std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *_First, std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *_Last)
{
  return std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::erase(
           this,
           result,
           _First,
           _Last);
}
