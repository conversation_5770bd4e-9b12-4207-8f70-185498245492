#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Move_cat@PEAVCUnmannedTraderRegistItemInfo@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAVCUnmannedTraderRegistItemInfo@@@Z
 * Address: 0x140007720

std::_Undefined_move_tag  std::_Move_cat<CUnmannedTraderRegistItemInfo *>(CUnmannedTraderRegistItemInfo *const *__formal)
{
  return std::_Move_cat<CUnmannedTraderRegistItemInfo *>(__formal);
}
