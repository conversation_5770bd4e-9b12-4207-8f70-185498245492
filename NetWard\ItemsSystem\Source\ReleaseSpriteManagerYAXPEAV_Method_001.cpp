#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ReleaseSpriteManager@@YAXPEAVCSprite@@@Z
 * Address: 0x140501DC0

void  ReleaseSpriteManager(struct CSprite *a1)
{
  int v1; // ebx@2
  int v2; // er9@2
  struct CSprite **v3; // rax@3

  if ( qword_184A79D78 )
  {
    v1 = dword_184A79D80;
    v2 = 0;
    if ( dword_184A79D80 > 0 )
    {
      v3 = (struct CSprite **)((char *)qword_184A79D78 + 128);
      while ( *v3 != a1 )
      {
        ++v2;
        v3 += 17;
        if ( v2 >= dword_184A79D80 )
          return;
      }
      memcpy_0(
        (char *)qword_184A79D78 + 136 * v2,
        (char *)qword_184A79D78 + 136 * (v2 + 1),
        136i64 * (dword_184A79D80 - v2 - 1));
      dword_184A79D80 = v1 - 1;
    }
  }
}
