// NetWard RF Online Server - AutoTrader System
// File: AT_Add03.cpp - AutoTrader SubClass Info Vector Insertion
// Compatible with Visual Studio 2022 (C++20)
// Original Function: std::vector<CUnmannedTraderSubClassInfo*>::_Insert
// Original Address: 0x1403808B0

#pragma once
#include "../../Headers/AutoTrader/AutoTraderCore.h"
#include "../../Headers/Common/NetWardTypes.h"
#include "../../Headers/Logging/LogManager.h"
#include <vector>
#include <iterator>
#include <algorithm>
#include <memory>
#include <stdexcept>

namespace NetWard::AutoTrader {

/**
 * @brief Inserts a range of AutoTrader subclass info pointers into a vector
 * @param targetVector The vector to insert into
 * @param insertPosition Iterator pointing to the insertion position
 * @param rangeBegin Iterator pointing to the beginning of the range to insert
 * @param rangeEnd Iterator pointing to the end of the range to insert
 *
 * @details This function implements efficient vector insertion for AutoTrader subclass info objects.
 *          It handles memory reallocation, element copying, and maintains vector integrity.
 *          This is critical for managing AutoTrader item categorization hierarchies.
 *
 * @note Original address: 0x1403808B0
 *       This replaces the complex STL vector insertion implementation for subclass management.
 */
template<typename InputIterator>
void AutoTraderSubClassInfoVector::InsertRange(
    Vector<AutoTraderSubClassInfo*>& targetVector,
    typename Vector<AutoTraderSubClassInfo*>::iterator insertPosition,
    InputIterator rangeBegin,
    InputIterator rangeEnd) noexcept
{
    try {
        // Calculate the number of elements to insert
        const auto insertCount = std::distance(rangeBegin, rangeEnd);

        // Validate input parameters
        if (insertCount <= 0) {
            return; // Nothing to insert
        }

        // Validate that the insertion position is valid
        if (insertPosition < targetVector.begin() || insertPosition > targetVector.end()) {
            throw std::out_of_range("Invalid insertion position for AutoTrader subclass info vector");
        }

        // Calculate the insertion index for later use
        const auto insertIndex = std::distance(targetVector.begin(), insertPosition);

        // Reserve space if needed to avoid multiple reallocations
        const auto currentSize = targetVector.size();
        const auto newSize = currentSize + insertCount;

        if (newSize > targetVector.capacity()) {
            // Calculate new capacity with growth factor
            const auto newCapacity = std::max(newSize, targetVector.capacity() * 2);
            targetVector.reserve(newCapacity);
        }

        // Use the standard library's efficient insert method
        // This handles all the complex memory management, iterator invalidation,
        // and element copying that was done manually in the original code
        targetVector.insert(targetVector.begin() + insertIndex, rangeBegin, rangeEnd);

        LogManager::WriteDebug("AutoTrader",
            "Successfully inserted {} subclass info elements at position {}",
            insertCount, insertIndex);

    } catch (const std::exception& e) {
        // Log the error and handle gracefully
        LogManager::WriteError("AutoTrader",
            "Failed to insert AutoTrader subclass info range: {}", e.what());

        // In the original RF Online server, this would likely cause a crash
        // Our implementation handles it gracefully
    }
}

/**
 * @brief Specialized insertion function for AutoTrader subclass info vectors
 * @param targetVector The vector to insert into
 * @param insertPosition Iterator pointing to the insertion position
 * @param sourceVector Vector containing elements to insert
 *
 * @details This is a convenience wrapper for inserting entire vectors.
 *          Commonly used when merging AutoTrader subclass hierarchies.
 */
void AutoTraderSubClassInfoVector::InsertVector(
    Vector<AutoTraderSubClassInfo*>& targetVector,
    typename Vector<AutoTraderSubClassInfo*>::iterator insertPosition,
    const Vector<AutoTraderSubClassInfo*>& sourceVector) noexcept
{
    InsertRange(targetVector, insertPosition, sourceVector.begin(), sourceVector.end());
}

/**
 * @brief Adds a single AutoTrader subclass info to the vector
 * @param targetVector The vector to add to
 * @param subClassInfo Pointer to the subclass info to add
 * @return Iterator pointing to the inserted element
 */
typename Vector<AutoTraderSubClassInfo*>::iterator AutoTraderSubClassInfoVector::AddSubClassInfo(
    Vector<AutoTraderSubClassInfo*>& targetVector,
    AutoTraderSubClassInfo* subClassInfo) noexcept
{
    if (!subClassInfo) {
        LogManager::WriteWarning("AutoTrader", "Attempted to add null subclass info");
        return targetVector.end();
    }

    try {
        auto result = targetVector.insert(targetVector.end(), subClassInfo);

        LogManager::WriteDebug("AutoTrader",
            "Added subclass info with ID {} to vector. New size: {}",
            subClassInfo->GetGroupId(), targetVector.size());

        return result;
    } catch (const std::exception& e) {
        LogManager::WriteError("AutoTrader",
            "Failed to add AutoTrader subclass info: {}", e.what());
        return targetVector.end();
    }
}

/**
 * @brief Removes a subclass info from the vector by pointer
 * @param targetVector The vector to remove from
 * @param subClassInfo Pointer to the subclass info to remove
 * @return true if the element was found and removed, false otherwise
 */
bool AutoTraderSubClassInfoVector::RemoveSubClassInfo(
    Vector<AutoTraderSubClassInfo*>& targetVector,
    AutoTraderSubClassInfo* subClassInfo) noexcept
{
    if (!subClassInfo) {
        return false;
    }

    try {
        auto it = std::find(targetVector.begin(), targetVector.end(), subClassInfo);
        if (it != targetVector.end()) {
            targetVector.erase(it);

            LogManager::WriteDebug("AutoTrader",
                "Removed subclass info with ID {} from vector. New size: {}",
                subClassInfo->GetGroupId(), targetVector.size());

            return true;
        }
        return false;
    } catch (const std::exception& e) {
        LogManager::WriteError("AutoTrader",
            "Failed to remove AutoTrader subclass info: {}", e.what());
        return false;
    }
}

} // namespace NetWard::AutoTrader
