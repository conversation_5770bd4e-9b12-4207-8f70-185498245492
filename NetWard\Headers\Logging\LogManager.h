// NetWard RF Online Server - Logging System
// File: LogManager.h - Comprehensive logging functionality
// Compatible with Visual Studio 2022 (C++20)

#pragma once

#include "../Common/NetWardTypes.h"
#include <memory>
#include <string>
#include <fstream>
#include <mutex>
#include <chrono>
#include <format>

namespace NetWard::Logging {

// ============================================================================
// LOG LEVEL ENUMERATION
// ============================================================================

enum class LogLevel : uint8 {
    Debug = 0,
    Info = 1,
    Warning = 2,
    Error = 3,
    Critical = 4
};

// ============================================================================
// LOG ENTRY STRUCTURE
// ============================================================================

struct LogEntry {
    std::chrono::system_clock::time_point timestamp;
    LogLevel level;
    String category;
    String message;
    String fileName;
    uint32 lineNumber;
    String functionName;

    LogEntry() = default;
    LogEntry(LogLevel lvl, const String& cat, const String& msg)
        : timestamp(std::chrono::system_clock::now())
        , level(lvl)
        , category(cat)
        , message(msg)
        , lineNumber(0)
    {}
};

// ============================================================================
// LOG MANAGER CLASS
// ============================================================================

class LogManager {
public:
    static LogManager& GetInstance();

    // Initialization and cleanup
    bool Initialize(const String& logDirectory = "Logs");
    void Shutdown();

    // Core logging functions
    void WriteLog(LogLevel level, const String& category, const String& message);
    
    // Convenience functions with formatting support
    template<typename... Args>
    static void WriteDebug(const String& category, const String& format, Args&&... args) {
        if constexpr (sizeof...(args) > 0) {
            GetInstance().WriteLog(LogLevel::Debug, category, std::format(format, std::forward<Args>(args)...));
        } else {
            GetInstance().WriteLog(LogLevel::Debug, category, format);
        }
    }

    template<typename... Args>
    static void WriteInfo(const String& category, const String& format, Args&&... args) {
        if constexpr (sizeof...(args) > 0) {
            GetInstance().WriteLog(LogLevel::Info, category, std::format(format, std::forward<Args>(args)...));
        } else {
            GetInstance().WriteLog(LogLevel::Info, category, format);
        }
    }

    template<typename... Args>
    static void WriteWarning(const String& category, const String& format, Args&&... args) {
        if constexpr (sizeof...(args) > 0) {
            GetInstance().WriteLog(LogLevel::Warning, category, std::format(format, std::forward<Args>(args)...));
        } else {
            GetInstance().WriteLog(LogLevel::Warning, category, format);
        }
    }

    template<typename... Args>
    static void WriteError(const String& category, const String& format, Args&&... args) {
        if constexpr (sizeof...(args) > 0) {
            GetInstance().WriteLog(LogLevel::Error, category, std::format(format, std::forward<Args>(args)...));
        } else {
            GetInstance().WriteLog(LogLevel::Error, category, format);
        }
    }

    template<typename... Args>
    static void WriteCritical(const String& category, const String& format, Args&&... args) {
        if constexpr (sizeof...(args) > 0) {
            GetInstance().WriteLog(LogLevel::Critical, category, std::format(format, std::forward<Args>(args)...));
        } else {
            GetInstance().WriteLog(LogLevel::Critical, category, format);
        }
    }

    // Configuration
    void SetLogLevel(LogLevel minLevel) noexcept { m_minLogLevel = minLevel; }
    LogLevel GetLogLevel() const noexcept { return m_minLogLevel; }

    void SetConsoleOutput(bool enabled) noexcept { m_consoleOutput = enabled; }
    bool GetConsoleOutput() const noexcept { return m_consoleOutput; }

    void SetFileOutput(bool enabled) noexcept { m_fileOutput = enabled; }
    bool GetFileOutput() const noexcept { return m_fileOutput; }

    // File management
    bool OpenLogFile(const String& fileName);
    void CloseLogFile();
    void FlushLogs();

    // Statistics
    uint64 GetLogCount() const noexcept { return m_logCount; }
    uint64 GetErrorCount() const noexcept { return m_errorCount; }

private:
    LogManager() = default;
    ~LogManager() = default;

    NETWARD_DISABLE_COPY_AND_MOVE(LogManager);

    // Internal functions
    String FormatLogEntry(const LogEntry& entry) const;
    String GetLogLevelString(LogLevel level) const;
    String GetTimestampString(const std::chrono::system_clock::time_point& timestamp) const;

    // Member variables
    LogLevel m_minLogLevel = LogLevel::Info;
    bool m_consoleOutput = true;
    bool m_fileOutput = true;
    bool m_initialized = false;

    String m_logDirectory;
    String m_currentLogFile;
    std::ofstream m_logFileStream;

    uint64 m_logCount = 0;
    uint64 m_errorCount = 0;

    mutable std::mutex m_logMutex;
};

// ============================================================================
// LEGACY COMPATIBILITY (for original RF Online code patterns)
// ============================================================================

class CLogFile {
public:
    CLogFile() = default;
    explicit CLogFile(const String& fileName);
    ~CLogFile() = default;

    // Legacy Write function (compatible with original RF Online server)
    template<typename... Args>
    void Write(const String& format, Args&&... args) {
        if constexpr (sizeof...(args) > 0) {
            LogManager::WriteInfo("Legacy", std::format(format, std::forward<Args>(args)...));
        } else {
            LogManager::WriteInfo("Legacy", format);
        }
    }

    // File operations
    bool Open(const String& fileName);
    void Close();
    void Flush();

private:
    String m_fileName;
    bool m_isOpen = false;
};

// ============================================================================
// LOGGING MACROS (for convenience)
// ============================================================================

#ifdef NETWARD_DEBUG
    #define NETWARD_LOG_DEBUG(category, ...) NetWard::Logging::LogManager::WriteDebug(category, __VA_ARGS__)
#else
    #define NETWARD_LOG_DEBUG(category, ...) ((void)0)
#endif

#define NETWARD_LOG_INFO(category, ...) NetWard::Logging::LogManager::WriteInfo(category, __VA_ARGS__)
#define NETWARD_LOG_WARNING(category, ...) NetWard::Logging::LogManager::WriteWarning(category, __VA_ARGS__)
#define NETWARD_LOG_ERROR(category, ...) NetWard::Logging::LogManager::WriteError(category, __VA_ARGS__)
#define NETWARD_LOG_CRITICAL(category, ...) NetWard::Logging::LogManager::WriteCritical(category, __VA_ARGS__)

// ============================================================================
// PERFORMANCE LOGGING
// ============================================================================

class PerformanceTimer {
public:
    explicit PerformanceTimer(const String& operationName, const String& category = "Performance");
    ~PerformanceTimer();

    void Stop();
    uint64 GetElapsedMicroseconds() const;

private:
    String m_operationName;
    String m_category;
    std::chrono::high_resolution_clock::time_point m_startTime;
    bool m_stopped = false;
};

// ============================================================================
// TYPE ALIASES
// ============================================================================

using LogEntryPtr = SharedPtr<LogEntry>;
using LogEntryList = Vector<LogEntryPtr>;

} // namespace NetWard::Logging

// Global alias for convenience
namespace NetWard {
    using LogManager = Logging::LogManager;
}
