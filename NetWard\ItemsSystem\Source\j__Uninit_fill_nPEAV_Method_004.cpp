#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Uninit_fill_n@PEAVCUnmannedTraderUserInfo@@_KV1@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@YAXPEAVCUnmannedTraderUserInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderUserInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14001334A

void  std::_Uninit_fill_n<CUnmannedTraderUserInfo *,unsigned int64_t,CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(CUnmannedTraderUserInfo *_First, unsigned int64_t _Count, CUnmannedTraderUserInfo *_Val, std::allocator<CUnmannedTraderUserInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CUnmannedTraderUserInfo *,unsigned int64_t,CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
