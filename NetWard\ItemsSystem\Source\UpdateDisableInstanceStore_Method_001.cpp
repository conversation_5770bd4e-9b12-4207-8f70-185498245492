#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?UpdateDisableInstanceStore@CItemStoreManager@@QEAAEPEAD@Z
 * Address: 0x14034A660

char  CItemStoreManager::UpdateDisableInstanceStore(CItemStoreManager *this, char *pData)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-48h]@1
  char *v6; // [sp+20h] [bp-28h]@4
  int j; // [sp+28h] [bp-20h]@4
  int64_t v8; // [sp+30h] [bp-18h]@6

  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v6 = pData;
  for ( j = 0; j < *(uint32_t*)v6; ++j )
  {
    v8 = *((uint64_t*)v6 + 1) + 8i64 * j;
    if ( *(uint32_t*)(v8 + 4) )
      *(uint8_t*)v8 = CRFWorldDatabase::Update_DisableInstanceStore(pkDB, *(uint32_t*)(v8 + 4));
  }
  return 0;
}
