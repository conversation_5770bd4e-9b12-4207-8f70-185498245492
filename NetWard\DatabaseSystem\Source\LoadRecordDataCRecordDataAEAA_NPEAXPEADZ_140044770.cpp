#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?LoadRecordData@CRecordData@@AEAA_NPEAXPEAD@Z
 * Address: 0x140044770

char  CRecordData::LoadRecordData(CRecordData *this, void *hFile, char *pszErrMsg)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char **v5; // rcx@9
  char **v6; // rcx@10
  int64_t v8; // [sp+0h] [bp-E8h]@1
  LPOVERLAPPED lpOverlapped; // [sp+20h] [bp-C8h]@9
  int j; // [sp+30h] [bp-B8h]@6
  unsigned int NumberOfBytesRead; // [sp+44h] [bp-A4h]@9
  char Dest; // [sp+70h] [bp-78h]@10
  char **v13; // [sp+C0h] [bp-28h]@6
  char *v14; // [sp+C8h] [bp-20h]@8
  unsigned int64_t v15; // [sp+D0h] [bp-18h]@6
  unsigned int64_t v16; // [sp+D8h] [bp-10h]@4
  CRecordData *v17; // [sp+F0h] [bp+8h]@1
  HANDLE hFilea; // [sp+F8h] [bp+10h]@1
  char *v19; // [sp+100h] [bp+18h]@1

  v19 = pszErrMsg;
  hFilea = hFile;
  v17 = this;
  v3 = &v8;
  for ( i = 56i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v16 = (unsigned int64_t)&v8 ^ _security_cookie;
  if ( v17->m_Header.m_nRecordNum && v17->m_Header.m_nRecordSize )
  {
    v17->m_nLowNum = v17->m_Header.m_nRecordNum;
    v15 = v17->m_Header.m_nRecordNum;
    v13 = (char **)operator new[](saturated_mul(8ui64, v15));
    v17->m_ppsRecord = v13;
    for ( j = 0; j < v17->m_Header.m_nRecordNum; ++j )
    {
      v14 = (char *)operator new[](v17->m_Header.m_nRecordSize);
      v17->m_ppsRecord[j] = v14;
      if ( v17->m_bLoad )
      {
        strcpy_0(&Dest, (const char *)v17->m_ppsRecord[j] + 4);
        v6 = v17->m_ppsRecord;
        lpOverlapped = 0i64;
        ReadFile(hFilea, v6[j], v17->m_Header.m_nRecordSize, &NumberOfBytesRead, 0i64);
        if ( strcmp_0(&Dest, (const char *)v17->m_ppsRecord[j] + 4) )
        {
          if ( v19 )
          {
            lpOverlapped = (LPOVERLAPPED)(v17->m_ppsRecord[j] + 4);
            sprintf(
              v19,
              "%s ε ڵڵ(%s) εϷ ڵڵ(%s) ٸ",
              v17->m_szFileName,
              &Dest);
          }
          return 0;
        }
      }
      else
      {
        v5 = v17->m_ppsRecord;
        lpOverlapped = 0i64;
        ReadFile(hFilea, v5[j], v17->m_Header.m_nRecordSize, &NumberOfBytesRead, 0i64);
      }
    }
  }
  return 1;
}
