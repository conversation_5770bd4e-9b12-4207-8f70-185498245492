#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_ItemUpgrade@CUserDB@@QEAA_NEEK_N@Z
 * Address: 0x140114F10

char  CUserDB::Update_ItemUpgrade(CUserDB *this, char storage, char slot, unsigned int upg, bool bUpdate)
{
  int64_t *v5; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v8; // [sp+0h] [bp-68h]@1
  int v9; // [sp+20h] [bp-48h]@5
  _INVENKEY *v10; // [sp+30h] [bp-38h]@7
  _EQUIPKEY *v11; // [sp+38h] [bp-30h]@11
  _ANIMUSKEY *v12; // [sp+40h] [bp-28h]@15
  _INVENKEY *v13; // [sp+48h] [bp-20h]@19
  _INVENKEY *v14; // [sp+50h] [bp-18h]@23
  CUserDB *v15; // [sp+70h] [bp+8h]@1
  char v16; // [sp+78h] [bp+10h]@1
  char v17; // [sp+80h] [bp+18h]@1
  unsigned int v18; // [sp+88h] [bp+20h]@1

  v18 = upg;
  v17 = slot;
  v16 = storage;
  v15 = this;
  v5 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t*)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  if ( !IsStorageRange(storage, slot) )
  {
    v9 = (unsigned int8_t)v17;
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_Upgrade(CODE) : scode : %d, icode : %d  ",
      v15->m_aszAvatorName,
      (unsigned int8_t)v16);
    return 0;
  }
  if ( v16 )
  {
    switch ( v16 )
    {
      case 1:
        v11 = &v15->m_AvatorData.dbAvator.m_EquipKey[(unsigned int8_t)v17];
        if ( !_EQUIPKEY::IsFilled(v11) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_Upgrade(EQUIP, Idx:%d)",
            v15->m_aszAvatorName,
            (unsigned int8_t)v17);
          return 0;
        }
        v15->m_AvatorData.dbAvator.m_dwFixEquipLv[(unsigned int8_t)v17] = v18;
        break;
      case 4:
        v12 = (_ANIMUSKEY *)&v15->m_AvatorData.dbAnimus + 34 * (unsigned int8_t)v17;
        if ( !_ANIMUSKEY::IsFilled(v12) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_Upgrade(ANIMUS, Idx:%d)",
            v15->m_aszAvatorName,
            (unsigned int8_t)v17);
          return 0;
        }
        *(uint32_t*)&v12[9].byItemIndex = v18;
        break;
      case 5:
        v13 = &v15->m_AvatorData.dbTrunk.m_List[(unsigned int8_t)v17].Key;
        if ( !_INVENKEY::IsFilled(v13) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_Upgrade(TRUNK, Idx:%d)",
            v15->m_aszAvatorName,
            (unsigned int8_t)v17);
          return 0;
        }
        v13[3] = (_INVENKEY)v18;
        break;
      default:
        if ( v16 != 7 )
        {
          v9 = (unsigned int8_t)v17;
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_Upgrade(NOT EQUAL(%d), Idx:%d)",
            v15->m_aszAvatorName,
            (unsigned int8_t)v16);
          return 0;
        }
        v14 = &v15->m_AvatorData.dbTrunk.m_ExtList[(unsigned int8_t)v17].Key;
        if ( !_INVENKEY::IsFilled(v14) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_Upgrade(EXT_TRUNK, Idx:%d)",
            v15->m_aszAvatorName,
            (unsigned int8_t)v17);
          return 0;
        }
        v14[3] = (_INVENKEY)v18;
        break;
    }
  }
  else
  {
    v10 = (_INVENKEY *)((char *)&v15->m_AvatorData.dbInven + 37 * (unsigned int8_t)v17);
    if ( !_INVENKEY::IsFilled(v10) )
    {
      CLogFile::Write(&stru_1799C8E78, "%s:Update_Upgrade(INVEN, Idx:%d)", v15->m_aszAvatorName, (unsigned int8_t)v17);
      return 0;
    }
    v10[3] = (_INVENKEY)v18;
  }
  if ( bUpdate )
    v15->m_bDataUpdate = 1;
  return 1;
}
