#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?ProcUseMoveScroll@CMoveMapLimitInfoPortal@@AEAAEHPEADPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x14000E5D4

char  CMoveMapLimitInfoPortal::ProcUseMoveScroll(CMoveMapLimitInfoPortal *this, int iUserInx, char *pRequest, CMoveMapLimitRightInfo *pkRight)
{
  return CMoveMapLimitInfoPortal::ProcUseMoveScroll(this, iUserInx, pRequest, pkRight);
}
