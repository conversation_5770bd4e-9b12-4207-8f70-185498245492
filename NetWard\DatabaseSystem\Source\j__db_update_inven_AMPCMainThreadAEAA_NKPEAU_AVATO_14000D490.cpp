#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_db_update_inven_AMP@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD@Z
 * Address: 0x14000D490

bool  CMainThread::_db_update_inven_AMP(CMainThread *this, unsigned int dwAvatorSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pszQuery)
{
  return CMainThread::_db_update_inven_AMP(this, dwAvatorSerial, pNewData, pOldData, pszQuery);
}
