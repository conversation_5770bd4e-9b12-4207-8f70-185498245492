// NetWard RF Online Server - AutoTrader System
// File: AT_Add02.cpp - AutoTrader Class Info Vector Insertion
// Compatible with Visual Studio 2022 (C++20)
// Original Function: std::vector<CUnmannedTraderClassInfo*>::_Insert
// Original Address: 0x140373E70

#pragma once
#include "../../Headers/AutoTrader/AutoTraderCore.h"
#include "../../Headers/Common/NetWardTypes.h"
#include <vector>
#include <iterator>
#include <algorithm>
#include <memory>

namespace NetWard::AutoTrader {

/**
 * @brief Inserts a range of AutoTrader class info pointers into a vector
 * @param targetVector The vector to insert into
 * @param insertPosition Iterator pointing to the insertion position
 * @param rangeBegin Iterator pointing to the beginning of the range to insert
 * @param rangeEnd Iterator pointing to the end of the range to insert
 *
 * @details This function implements efficient vector insertion for AutoTrader class info objects.
 *          It handles memory reallocation, element copying, and maintains vector integrity.
 *          This is a critical function for managing AutoTrader class hierarchies.
 *
 * @note Original address: 0x140373E70
 *       This replaces the complex STL vector insertion implementation.
 */
template<typename InputIterator>
void AutoTraderClassInfoVector::InsertRange(
    Vector<AutoTraderClassInfo*>& targetVector,
    typename Vector<AutoTraderClassInfo*>::iterator insertPosition,
    InputIterator rangeBegin,
    InputIterator rangeEnd) noexcept
{
    try {
        // Calculate the number of elements to insert
        const auto insertCount = std::distance(rangeBegin, rangeEnd);

        // Validate input parameters
        if (insertCount <= 0) {
            return; // Nothing to insert
        }

        // Validate that the insertion position is valid
        if (insertPosition < targetVector.begin() || insertPosition > targetVector.end()) {
            throw std::out_of_range("Invalid insertion position");
        }

        // Calculate the insertion index for later use
        const auto insertIndex = std::distance(targetVector.begin(), insertPosition);

        // Reserve space if needed to avoid multiple reallocations
        const auto currentSize = targetVector.size();
        const auto newSize = currentSize + insertCount;

        if (newSize > targetVector.capacity()) {
            // Calculate new capacity (typically 1.5x or 2x growth)
            const auto newCapacity = std::max(newSize, targetVector.capacity() * 2);
            targetVector.reserve(newCapacity);
        }

        // Use the standard library's efficient insert method
        // This handles all the complex memory management, iterator invalidation,
        // and element copying that was done manually in the original code
        targetVector.insert(targetVector.begin() + insertIndex, rangeBegin, rangeEnd);

    } catch (const std::exception& e) {
        // Log the error and handle gracefully
        LogManager::WriteError("AutoTrader",
            "Failed to insert AutoTrader class info range: {}", e.what());

        // In the original RF Online server, this would likely cause a crash
        // Our implementation handles it gracefully
    }
}

/**
 * @brief Specialized insertion function for AutoTrader class info vectors
 * @param targetVector The vector to insert into
 * @param insertPosition Iterator pointing to the insertion position
 * @param sourceVector Vector containing elements to insert
 *
 * @details This is a convenience wrapper for inserting entire vectors.
 *          Commonly used when merging AutoTrader class hierarchies.
 */
void AutoTraderClassInfoVector::InsertVector(
    Vector<AutoTraderClassInfo*>& targetVector,
    typename Vector<AutoTraderClassInfo*>::iterator insertPosition,
    const Vector<AutoTraderClassInfo*>& sourceVector) noexcept
{
    InsertRange(targetVector, insertPosition, sourceVector.begin(), sourceVector.end());
}

/**
 * @brief Adds a single AutoTrader class info to the vector
 * @param targetVector The vector to add to
 * @param classInfo Pointer to the class info to add
 * @return Iterator pointing to the inserted element
 */
typename Vector<AutoTraderClassInfo*>::iterator AutoTraderClassInfoVector::AddClassInfo(
    Vector<AutoTraderClassInfo*>& targetVector,
    AutoTraderClassInfo* classInfo) noexcept
{
    if (!classInfo) {
        return targetVector.end();
    }

    try {
        return targetVector.insert(targetVector.end(), classInfo);
    } catch (const std::exception& e) {
        LogManager::WriteError("AutoTrader",
            "Failed to add AutoTrader class info: {}", e.what());
        return targetVector.end();
    }
}

} // namespace NetWard::AutoTrader
