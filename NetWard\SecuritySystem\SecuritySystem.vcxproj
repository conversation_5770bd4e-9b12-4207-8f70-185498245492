<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>

  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{I9J0K1L2-M3N4-5678-2345-************}</ProjectGuid>
    <RootNamespace>SecuritySystem</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemGroup>
    <ClCompile Include="Source\0AlgorithmImplVCBC_DecryptionCryptoPPVCipherModeFi_140454D00.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVCBC_EncryptionCryptoPPVCipherModeFi_1404567E0.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVCBC_EncryptionCryptoPPVCipherModeFi_14061B740.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVConcretePolicyHolderVEmptyCryptoPPV_14061B560.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVConcretePolicyHolderVEmptyCryptoPPV_14061B5C0.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVDL_DecryptorBaseUECPPointCryptoPPCr_140457ED0.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVDL_DecryptorBaseVIntegerCryptoPPCry_140637B20.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVDL_EncryptorBaseUECPPointCryptoPPCr_140457E80.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVDL_EncryptorBaseVIntegerCryptoPPCry_1406371B0.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVIteratedHashIUEnumToTypeW4ByteOrder_140456980.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVIteratedHashIUEnumToTypeW4ByteOrder_140457D70.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVIteratedHashIUEnumToTypeW4ByteOrder_140464370.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVIteratedHashIUEnumToTypeW4ByteOrder_140464510.cpp" />
    <ClCompile Include="Source\0CBC_CTS_DecryptionCryptoPPQEAAXZ_14055D2A0.cpp" />
    <ClCompile Include="Source\0CBC_CTS_EncryptionCryptoPPQEAAXZ_14055D220.cpp" />
    <ClCompile Include="Source\0CBC_DecryptionCryptoPPQEAAXZ_1404576B0.cpp" />
    <ClCompile Include="Source\0CBC_EncryptionCryptoPPQEAAXZ_1404578E0.cpp" />
    <ClCompile Include="Source\0CCheckSumCharacAccountTrunkDataQEAAKKEZ_1402C06A0.cpp" />
    <ClCompile Include="Source\0CCheckSumGuildDataQEAAKZ_1401BF340.cpp" />
    <ClCompile Include="Source\0CCheckSumQEAAXZ_1402C0560.cpp" />
    <ClCompile Include="Source\0CFB_DecryptionTemplateVAbstractPolicyHolderVCFB_C_14061B940.cpp" />
    <ClCompile Include="Source\0CFB_EncryptionTemplateVAbstractPolicyHolderVCFB_C_14061B8E0.cpp" />
    <ClCompile Include="Source\0CHashMapPtrPoolHVCNationCodeStrQEAAXZ_140208370.cpp" />
    <ClCompile Include="Source\0CHashMapPtrPoolHVCNationSettingFactoryQEAAXZ_1402299F0.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_CipherHolderVBlockCipherF_1404525C0.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_CipherHolderVBlockCipherF_140453380.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_CipherHolderVBlockCipherF_14061A500.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_CipherHolderVBlockCipherF_14061AAD0.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_CipherHolderVBlockCipherF_14061B400.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_ExternalCipherVCBC_CTS_De_14055B8A0.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_ExternalCipherVCBC_CTS_De_14055B900.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_ExternalCipherVCBC_CTS_De_14055B980.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_ExternalCipherVCBC_CTS_En_14055B6C0.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_ExternalCipherVCBC_CTS_En_14055B790.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_ExternalCipherVCBC_CTS_En_14055B810.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_ExternalCipherVCBC_Decryp_14055B570.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_ExternalCipherVCBC_Decryp_14055B5B0.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_ExternalCipherVCBC_Decryp_14055B630.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_ExternalCipherVCBC_Encryp_14055B420.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_ExternalCipherVCBC_Encryp_14055B460.cpp" />
    <ClCompile Include="Source\0CipherModeFinalTemplate_ExternalCipherVCBC_Encryp_14055B4E0.cpp" />
    <ClCompile Include="Source\0ClonableImplVSHA1CryptoPPVAlgorithmImplVIteratedH_140464310.cpp" />
    <ClCompile Include="Source\0ClonableImplVSHA1CryptoPPVAlgorithmImplVIteratedH_1404644C0.cpp" />
    <ClCompile Include="Source\0ClonableImplVSHA256CryptoPPVAlgorithmImplVIterate_1404546C0.cpp" />
    <ClCompile Include="Source\0ClonableImplVSHA256CryptoPPVAlgorithmImplVIterate_140457A40.cpp" />
    <ClCompile Include="Source\0ConcretePolicyHolderVEmptyCryptoPPVCFB_Decryption_14061B7B0.cpp" />
    <ClCompile Include="Source\0ConcretePolicyHolderVEmptyCryptoPPVCFB_Encryption_14061B760.cpp" />
    <ClCompile Include="Source\0DL_CryptoSystemBaseVPK_DecryptorCryptoPPVDL_Priva_140458280.cpp" />
    <ClCompile Include="Source\0DL_CryptoSystemBaseVPK_DecryptorCryptoPPVDL_Priva_140638D70.cpp" />
    <ClCompile Include="Source\0DL_CryptoSystemBaseVPK_EncryptorCryptoPPVDL_Publi_140458220.cpp" />
    <ClCompile Include="Source\0DL_CryptoSystemBaseVPK_EncryptorCryptoPPVDL_Publi_140638C40.cpp" />
    <ClCompile Include="Source\0DL_DecryptorBaseUECPPointCryptoPPCryptoPPQEAAXZ_1404580E0.cpp" />
    <ClCompile Include="Source\0DL_DecryptorBaseVIntegerCryptoPPCryptoPPQEAAXZ_1406388E0.cpp" />
    <ClCompile Include="Source\0DL_DecryptorImplUDL_CryptoSchemeOptionsUDLIESUEnu_1406351C0.cpp" />
    <ClCompile Include="Source\0DL_DecryptorImplUDL_CryptoSchemeOptionsUECIESVECP_140455A50.cpp" />
    <ClCompile Include="Source\0DL_EncryptionAlgorithm_XorVHMACVSHA1CryptoPPCrypt_1404645D0.cpp" />
    <ClCompile Include="Source\0DL_EncryptionAlgorithm_XorVHMACVSHA1CryptoPPCrypt_14063CCE0.cpp" />
    <ClCompile Include="Source\0DL_EncryptorBaseUECPPointCryptoPPCryptoPPQEAAXZ_140458090.cpp" />
    <ClCompile Include="Source\0DL_EncryptorBaseVIntegerCryptoPPCryptoPPQEAAXZ_1406387A0.cpp" />
    <ClCompile Include="Source\0DL_EncryptorImplUDL_CryptoSchemeOptionsUDLIESUEnu_140634A00.cpp" />
    <ClCompile Include="Source\0DL_EncryptorImplUDL_CryptoSchemeOptionsUECIESVECP_140454D50.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_DecryptorBaseUECPPointCrypto_140457CC0.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_DecryptorBaseVIntegerCryptoP_140635D70.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_EncryptorBaseUECPPointCrypto_140457C10.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_EncryptorBaseVIntegerCryptoP_140635D10.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_DecryptorBaseUECPPointCryptoPPCr_140457890.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_DecryptorBaseVIntegerCryptoPPCry_140635B70.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_EncryptorBaseUECPPointCryptoPPCr_1404577D0.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_EncryptorBaseVIntegerCryptoPPCry_140635B10.cpp" />
    <ClCompile Include="Source\0DL_SymmetricEncryptionAlgorithmCryptoPPQEAAXZ_140465F70.cpp" />
    <ClCompile Include="Source\0HashFilterCryptoPPQEAAAEAVHashTransformation1PEAV_140623D40.cpp" />
    <ClCompile Include="Source\0HashInputTooLongCryptoPPQEAAAEBV01Z_14058A4D0.cpp" />
    <ClCompile Include="Source\0HashInputTooLongCryptoPPQEAAAEBVbasic_stringDUcha_1405705C0.cpp" />
    <ClCompile Include="Source\0HashTransformationCryptoPPQEAAAEBV01Z_1404582E0.cpp" />
    <ClCompile Include="Source\0HashTransformationCryptoPPQEAAXZ_140458EC0.cpp" />
    <ClCompile Include="Source\0HashVerificationFailedHashVerificationFilterCrypt_1405FF760.cpp" />
    <ClCompile Include="Source\0HashVerificationFailedHashVerificationFilterCrypt_1405FF880.cpp" />
    <ClCompile Include="Source\0HashVerificationFilterCryptoPPQEAAAEAVHashTransfo_1405FCEC0.cpp" />
    <ClCompile Include="Source\0hash_compareHUlessHstdstdextQEAAXZ_1402085F0.cpp" />
    <ClCompile Include="Source\0hash_comparePEAUScheduleMSGUlessPEAUScheduleMSGst_140422FA0.cpp" />
    <ClCompile Include="Source\0hash_mapHPEAVCNationCodeStrVhash_compareHUlessHst_1402084C0.cpp" />
    <ClCompile Include="Source\0hash_mapHPEAVCNationSettingFactoryVhash_compareHU_140229BB0.cpp" />
    <ClCompile Include="Source\0hash_mapHPEBU_CashShop_fldVhash_compareHUlessHstd_140304DE0.cpp" />
    <ClCompile Include="Source\0hash_mapPEAUScheduleMSGKVhash_comparePEAUSchedule_1404205E0.cpp" />
    <ClCompile Include="Source\0IteratedHashBaseIVHashTransformationCryptoPPCrypt_140458130.cpp" />
    <ClCompile Include="Source\0IteratedHashBaseIVHashTransformationCryptoPPCrypt_140458E50.cpp" />
    <ClCompile Include="Source\0IteratedHashBaseIVSimpleKeyedTransformationVHashT_140551940.cpp" />
    <ClCompile Include="Source\0IteratedHashBase_KVHashTransformationCryptoPPCryp_140551690.cpp" />
    <ClCompile Include="Source\0IteratedHashBase_KVHashTransformationCryptoPPCryp_14055F780.cpp" />
    <ClCompile Include="Source\0IteratedHashBase_KVSimpleKeyedTransformationVHash_1405517A0.cpp" />
    <ClCompile Include="Source\0IteratedHashIUEnumToTypeW4ByteOrderCryptoPP00Cryp_140457990.cpp" />
    <ClCompile Include="Source\0IteratedHashIUEnumToTypeW4ByteOrderCryptoPP00Cryp_140457F20.cpp" />
    <ClCompile Include="Source\0IteratedHashWithStaticTransformIUEnumToTypeW4Byte_14044EDC0.cpp" />
    <ClCompile Include="Source\0IteratedHashWithStaticTransformIUEnumToTypeW4Byte_1404569D0.cpp" />
    <ClCompile Include="Source\0IteratedHashWithStaticTransformIUEnumToTypeW4Byte_140464250.cpp" />
    <ClCompile Include="Source\0IteratedHashWithStaticTransformIUEnumToTypeW4Byte_1404643D0.cpp" />
    <ClCompile Include="Source\0PK_DecryptorCryptoPPQEAAXZ_140458450.cpp" />
    <ClCompile Include="Source\0PK_DefaultDecryptionFilterCryptoPPQEAAAEAVRandomN_1405F6FE0.cpp" />
    <ClCompile Include="Source\0PK_DefaultEncryptionFilterCryptoPPQEAAAEAVRandomN_1405F6A50.cpp" />
    <ClCompile Include="Source\0PK_EncryptorCryptoPPQEAAXZ_140458390.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_DecryptorImplUDL_CryptoScheme_140453310.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_DecryptorImplUDL_CryptoScheme_140634030.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_EncryptorImplUDL_CryptoScheme_1404532A0.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_EncryptorImplUDL_CryptoScheme_140634010.cpp" />
    <ClCompile Include="Source\0SimpleKeyedTransformationVHashTransformationCrypt_140465AC0.cpp" />
    <ClCompile Include="Source\0simple_ptrVDL_EncryptionAlgorithm_XorVHMACVSHA1Cr_140460EA0.cpp" />
    <ClCompile Include="Source\0simple_ptrVDL_EncryptionAlgorithm_XorVHMACVSHA1Cr_14063C350.cpp" />
    <ClCompile Include="Source\0SingletonVDL_EncryptionAlgorithm_XorVHMACVSHA1Cry_14045B830.cpp" />
    <ClCompile Include="Source\0SingletonVDL_EncryptionAlgorithm_XorVHMACVSHA1Cry_1406398D0.cpp" />
    <ClCompile Include="Source\0_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_compar_140208600.cpp" />
    <ClCompile Include="Source\0_HashV_Hmap_traitsHPEAVCNationSettingFactoryVhash_140229CE0.cpp" />
    <ClCompile Include="Source\0_HashV_Hmap_traitsHPEBU_CashShop_fldVhash_compare_1403063F0.cpp" />
    <ClCompile Include="Source\0_HashV_Hmap_traitsPEAUScheduleMSGKVhash_comparePE_140421910.cpp" />
    <ClCompile Include="Source\0_Hmap_traitsHPEAVCNationCodeStrVhash_compareHUles_140208990.cpp" />
    <ClCompile Include="Source\0_Hmap_traitsHPEAVCNationSettingFactoryVhash_compa_140229F30.cpp" />
    <ClCompile Include="Source\0_Hmap_traitsHPEBU_CashShop_fldVhash_compareHUless_140307A10.cpp" />
    <ClCompile Include="Source\0_Hmap_traitsPEAUScheduleMSGKVhash_comparePEAUSche_1404240F0.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVCBC_DecryptionCryptoPPVCipherModeFi_140448C10.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVCBC_EncryptionCryptoPPVCipherModeFi_14044E620.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVCBC_EncryptionCryptoPPVCipherModeFi_140619D30.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVConcretePolicyHolderVEmptyCryptoPPV_140619C90.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVConcretePolicyHolderVEmptyCryptoPPV_140619CB0.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVDL_DecryptorBaseUECPPointCryptoPPCr_140449760.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVDL_DecryptorBaseVIntegerCryptoPPCry_140632C70.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVDL_EncryptorBaseUECPPointCryptoPPCr_140449720.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVDL_EncryptorBaseVIntegerCryptoPPCry_140632C20.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVIteratedHashIUEnumToTypeW4ByteOrder_14044E230.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVIteratedHashIUEnumToTypeW4ByteOrder_140463E40.cpp" />
    <ClCompile Include="Source\1CBC_CTS_DecryptionCryptoPPUEAAXZ_14055D2C0.cpp" />
    <ClCompile Include="Source\1CBC_CTS_EncryptionCryptoPPUEAAXZ_14055D240.cpp" />
    <ClCompile Include="Source\1CBC_DecryptionCryptoPPUEAAXZ_140448F90.cpp" />
    <ClCompile Include="Source\1CBC_EncryptionCryptoPPUEAAXZ_14044E6A0.cpp" />
    <ClCompile Include="Source\1CCheckSumCharacAccountTrunkDataQEAAXZ_1402C0740.cpp" />
    <ClCompile Include="Source\1CCheckSumGuildDataQEAAXZ_1401BF370.cpp" />
    <ClCompile Include="Source\1CCheckSumQEAAXZ_1402C0580.cpp" />
    <ClCompile Include="Source\1CFB_DecryptionTemplateVAbstractPolicyHolderVCFB_C_140619E30.cpp" />
    <ClCompile Include="Source\1CFB_EncryptionTemplateVAbstractPolicyHolderVCFB_C_140619E10.cpp" />
    <ClCompile Include="Source\1CHashMapPtrPoolHVCNationCodeStrQEAAXZ_1402083D0.cpp" />
    <ClCompile Include="Source\1CHashMapPtrPoolHVCNationSettingFactoryQEAAXZ_140229A50.cpp" />
    <ClCompile Include="Source\1CipherModeFinalTemplate_CipherHolderVBlockCipherF_1404489A0.cpp" />
    <ClCompile Include="Source\1CipherModeFinalTemplate_CipherHolderVBlockCipherF_14044E500.cpp" />
    <ClCompile Include="Source\1CipherModeFinalTemplate_CipherHolderVBlockCipherF_140619910.cpp" />
    <ClCompile Include="Source\1CipherModeFinalTemplate_CipherHolderVBlockCipherF_1406199A0.cpp" />
    <ClCompile Include="Source\1CipherModeFinalTemplate_CipherHolderVBlockCipherF_140619BE0.cpp" />
    <ClCompile Include="Source\1CipherModeFinalTemplate_ExternalCipherVCBC_CTS_De_14055E060.cpp" />
    <ClCompile Include="Source\1CipherModeFinalTemplate_ExternalCipherVCBC_CTS_En_14055E040.cpp" />
    <ClCompile Include="Source\1CipherModeFinalTemplate_ExternalCipherVCBC_Decryp_14055E020.cpp" />
    <ClCompile Include="Source\1CipherModeFinalTemplate_ExternalCipherVCBC_Encryp_14055E000.cpp" />
    <ClCompile Include="Source\1ClonableImplVSHA1CryptoPPVAlgorithmImplVIteratedH_140463E00.cpp" />
    <ClCompile Include="Source\1ClonableImplVSHA256CryptoPPVAlgorithmImplVIterate_14044E1B0.cpp" />
    <ClCompile Include="Source\1ConcretePolicyHolderVEmptyCryptoPPVCFB_Decryption_140619D90.cpp" />
    <ClCompile Include="Source\1ConcretePolicyHolderVEmptyCryptoPPVCFB_Encryption_140619D70.cpp" />
    <ClCompile Include="Source\1DL_CryptoSystemBaseVPK_DecryptorCryptoPPVDL_Priva_140449F40.cpp" />
    <ClCompile Include="Source\1DL_CryptoSystemBaseVPK_DecryptorCryptoPPVDL_Priva_1406332E0.cpp" />
    <ClCompile Include="Source\1DL_CryptoSystemBaseVPK_EncryptorCryptoPPVDL_Publi_140449F00.cpp" />
    <ClCompile Include="Source\1DL_CryptoSystemBaseVPK_EncryptorCryptoPPVDL_Publi_140633220.cpp" />
    <ClCompile Include="Source\1DL_DecryptorBaseUECPPointCryptoPPCryptoPPUEAAXZ_140449C40.cpp" />
    <ClCompile Include="Source\1DL_DecryptorBaseVIntegerCryptoPPCryptoPPUEAAXZ_140632EA0.cpp" />
    <ClCompile Include="Source\1DL_DecryptorImplUDL_CryptoSchemeOptionsUDLIESUEnu_140632610.cpp" />
    <ClCompile Include="Source\1DL_DecryptorImplUDL_CryptoSchemeOptionsUECIESVECP_140448C90.cpp" />
    <ClCompile Include="Source\1DL_EncryptorBaseUECPPointCryptoPPCryptoPPUEAAXZ_140449C00.cpp" />
    <ClCompile Include="Source\1DL_EncryptorBaseVIntegerCryptoPPCryptoPPUEAAXZ_140632E60.cpp" />
    <ClCompile Include="Source\1DL_EncryptorImplUDL_CryptoSchemeOptionsUDLIESUEnu_1406325F0.cpp" />
    <ClCompile Include="Source\1DL_EncryptorImplUDL_CryptoSchemeOptionsUECIESVECP_140448C50.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_DecryptorBaseUECPPointCrypto_140449480.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_DecryptorBaseVIntegerCryptoP_140632980.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_EncryptorBaseUECPPointCrypto_1404493F0.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_EncryptorBaseVIntegerCryptoP_140632920.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_DecryptorBaseUECPPointCryptoPPCr_140449060.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_DecryptorBaseVIntegerCryptoPPCry_140632700.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_EncryptorBaseUECPPointCryptoPPCr_140449020.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_EncryptorBaseVIntegerCryptoPPCry_1406326E0.cpp" />
    <ClCompile Include="Source\1HashFilterCryptoPPUEAAXZ_140623F20.cpp" />
    <ClCompile Include="Source\1HashInputTooLongCryptoPPUEAAXZ_1405706C0.cpp" />
    <ClCompile Include="Source\1HashTransformationCryptoPPUEAAXZ_14044E340.cpp" />
    <ClCompile Include="Source\1HashVerificationFailedHashVerificationFilterCrypt_1405FF860.cpp" />
    <ClCompile Include="Source\1HashVerificationFilterCryptoPPUEAAXZ_1405FF6E0.cpp" />
    <ClCompile Include="Source\1hash_mapHPEAVCNationCodeStrVhash_compareHUlessHst_140208480.cpp" />
    <ClCompile Include="Source\1hash_mapHPEAVCNationSettingFactoryVhash_compareHU_140229B70.cpp" />
    <ClCompile Include="Source\1hash_mapHPEBU_CashShop_fldVhash_compareHUlessHstd_140304940.cpp" />
    <ClCompile Include="Source\1hash_mapPEAUScheduleMSGKVhash_comparePEAUSchedule_14041F7D0.cpp" />
    <ClCompile Include="Source\1IteratedHashBaseIVHashTransformationCryptoPPCrypt_14044E300.cpp" />
    <ClCompile Include="Source\1IteratedHashBaseIVSimpleKeyedTransformationVHashT_14055D420.cpp" />
    <ClCompile Include="Source\1IteratedHashBase_KVHashTransformationCryptoPPCryp_14055D3E0.cpp" />
    <ClCompile Include="Source\1IteratedHashBase_KVSimpleKeyedTransformationVHash_14055D400.cpp" />
    <ClCompile Include="Source\1IteratedHashIUEnumToTypeW4ByteOrderCryptoPP00Cryp_14044E270.cpp" />
    <ClCompile Include="Source\1IteratedHashWithStaticTransformIUEnumToTypeW4Byte_14044E120.cpp" />
    <ClCompile Include="Source\1IteratedHashWithStaticTransformIUEnumToTypeW4Byte_140463D70.cpp" />
    <ClCompile Include="Source\1PK_DecryptorCryptoPPUEAAXZ_14044A270.cpp" />
    <ClCompile Include="Source\1PK_DefaultDecryptionFilterCryptoPPUEAAXZ_1405F7670.cpp" />
    <ClCompile Include="Source\1PK_DefaultEncryptionFilterCryptoPPUEAAXZ_1405F6F50.cpp" />
    <ClCompile Include="Source\1PK_EncryptorCryptoPPUEAAXZ_14044A1A0.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_DecryptorImplUDL_CryptoScheme_140448AC0.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_DecryptorImplUDL_CryptoScheme_1406324F0.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_EncryptorImplUDL_CryptoScheme_140448A80.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_EncryptorImplUDL_CryptoScheme_1406324D0.cpp" />
    <ClCompile Include="Source\1SimpleKeyedTransformationVHashTransformationCrypt_1404650A0.cpp" />
    <ClCompile Include="Source\1simple_ptrVDL_EncryptionAlgorithm_XorVHMACVSHA1Cr_14046AE80.cpp" />
    <ClCompile Include="Source\1simple_ptrVDL_EncryptionAlgorithm_XorVHMACVSHA1Cr_14063C370.cpp" />
    <ClCompile Include="Source\1_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_compar_140208540.cpp" />
    <ClCompile Include="Source\1_HashV_Hmap_traitsHPEAVCNationSettingFactoryVhash_140229C30.cpp" />
    <ClCompile Include="Source\1_HashV_Hmap_traitsHPEBU_CashShop_fldVhash_compare_140304E60.cpp" />
    <ClCompile Include="Source\1_HashV_Hmap_traitsPEAUScheduleMSGKVhash_comparePE_1404208C0.cpp" />
    <ClCompile Include="Source\AccessHashHMACVSHA1CryptoPPCryptoPPEEAAAEAVHashTra_1404656D0.cpp" />
    <ClCompile Include="Source\AccessInnerHashHMAC_BaseCryptoPPIEAAPEAEXZ_140624DD0.cpp" />
    <ClCompile Include="Source\AccessKeyDL_ObjectImplBaseVDL_DecryptorBaseUECPPoi_140453360.cpp" />
    <ClCompile Include="Source\AccessKeyDL_ObjectImplBaseVDL_EncryptorBaseUECPPoi_1404532F0.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_DecryptorBa_140455E00.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_DecryptorBa_140635310.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_EncryptorBa_140455120.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_EncryptorBa_140634B50.cpp" />
    <ClCompile Include="Source\AccessPolicyConcretePolicyHolderVEmptyCryptoPPVCFB_14061AA90.cpp" />
    <ClCompile Include="Source\AccessPolicyConcretePolicyHolderVEmptyCryptoPPVCFB_14061AC20.cpp" />
    <ClCompile Include="Source\AccessPrivateKeyDL_ObjectImplBaseVDL_DecryptorBase_140455D90.cpp" />
    <ClCompile Include="Source\AccessPrivateKeyDL_ObjectImplBaseVDL_DecryptorBase_1406352D0.cpp" />
    <ClCompile Include="Source\AccessPublicKeyDL_ObjectImplBaseVDL_EncryptorBaseU_1404550B0.cpp" />
    <ClCompile Include="Source\AccessPublicKeyDL_ObjectImplBaseVDL_EncryptorBaseV_140634B10.cpp" />
    <ClCompile Include="Source\Ahash_mapPEAUScheduleMSGKVhash_comparePEAUSchedule_140420660.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVCBC_DecryptionCryptoPPV_140453230.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVCBC_EncryptionCryptoPPV_1404534A0.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVCBC_EncryptionCryptoPPV_14061B4C0.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVConcretePolicyHolderVEm_14061AA10.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVConcretePolicyHolderVEm_14061ABA0.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVDL_DecryptorBaseUECPPoi_140455E40.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVDL_DecryptorBaseVIntege_140635330.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVDL_EncryptorBaseUECPPoi_140455160.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVDL_EncryptorBaseVIntege_140634B70.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVIteratedHashIUEnumToTyp_14044F090.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVIteratedHashIUEnumToTyp_1404640D0.cpp" />
    <ClCompile Include="Source\AlgorithmNameHashFilterCryptoPPUEBAAVbasic_stringD_140623E60.cpp" />
    <ClCompile Include="Source\AlgorithmNameHashVerificationFilterCryptoPPUEBAAVb_1405FF5E0.cpp" />
    <ClCompile Include="Source\begin_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_co_14020A3C0.cpp" />
    <ClCompile Include="Source\begin_HashV_Hmap_traitsHPEAVCNationSettingFactoryV_14022AD30.cpp" />
    <ClCompile Include="Source\BlockSizeHashTransformationCryptoPPUEBAIXZ_1404652D0.cpp" />
    <ClCompile Include="Source\BlockSizeIteratedHashIUEnumToTypeW4ByteOrderCrypto_14044ECB0.cpp" />
    <ClCompile Include="Source\CalculateDigestHashTransformationCryptoPPUEAAXPEAE_14044DE50.cpp" />
    <ClCompile Include="Source\CalculateTruncatedDigestHashTransformationCryptoPP_14044DF50.cpp" />
    <ClCompile Include="Source\CheckGuildCheckSumCGuildRankingAEAA_NKPEADAEAN1Z_14033A100.cpp" />
    <ClCompile Include="Source\CheckPublicKeyHashCCryptParamIEAAXAEAVByteQueueCry_140447FC0.cpp" />
    <ClCompile Include="Source\CiphertextLengthDL_CryptoSystemBaseVPK_DecryptorCr_140456570.cpp" />
    <ClCompile Include="Source\CiphertextLengthDL_CryptoSystemBaseVPK_DecryptorCr_140635830.cpp" />
    <ClCompile Include="Source\CiphertextLengthDL_CryptoSystemBaseVPK_EncryptorCr_1404558A0.cpp" />
    <ClCompile Include="Source\CiphertextLengthDL_CryptoSystemBaseVPK_EncryptorCr_1406350A0.cpp" />
    <ClCompile Include="Source\cleanupCHashMapPtrPoolHVCNationCodeStrQEAAXXZ_140209A80.cpp" />
    <ClCompile Include="Source\cleanupCHashMapPtrPoolHVCNationSettingFactoryQEAAX_14022AA20.cpp" />
    <ClCompile Include="Source\clear_HashV_Hmap_traitsPEAUScheduleMSGKVhash_compa_1404209F0.cpp" />
    <ClCompile Include="Source\CloneClonableImplVSHA1CryptoPPVAlgorithmImplVItera_140464000.cpp" />
    <ClCompile Include="Source\CloneClonableImplVSHA256CryptoPPVAlgorithmImplVIte_14044EFC0.cpp" />
    <ClCompile Include="Source\ConvertCCheckSumCharacTrunkConverterQEAAXPEAU_AVAT_1402C14D0.cpp" />
    <ClCompile Include="Source\ConvertCCheckSumGuildConverterQEAAXNNPEAVCCheckSum_1402C1760.cpp" />
    <ClCompile Include="Source\ConvertTrunkCCheckSumCharacTrunkConverterQEAAXKPEA_1402C16C0.cpp" />
    <ClCompile Include="Source\CreateDecryptionFilterPK_DecryptorCryptoPPUEBAPEAV_1405F5EB0.cpp" />
    <ClCompile Include="Source\CreateEncryptionFilterPK_EncryptorCryptoPPUEBAPEAV_1405F5E10.cpp" />
    <ClCompile Include="Source\CreatePutSpaceHashFilterCryptoPPUEAAPEAEAEA_KZ_140623EB0.cpp" />
    <ClCompile Include="Source\CreateUpdateSpaceHashTransformationCryptoPPUEAAPEA_1404652A0.cpp" />
    <ClCompile Include="Source\CreateUpdateSpaceIteratedHashBaseIVHashTransformat_140571830.cpp" />
    <ClCompile Include="Source\CreateUpdateSpaceIteratedHashBaseIVSimpleKeyedTran_1405720C0.cpp" />
    <ClCompile Include="Source\CreateUpdateSpaceIteratedHashBase_KVHashTransforma_1405706E0.cpp" />
    <ClCompile Include="Source\CreateUpdateSpaceIteratedHashBase_KVSimpleKeyedTra_140570F90.cpp" />
    <ClCompile Include="Source\DataBufIteratedHashIUEnumToTypeW4ByteOrderCryptoPP_14044ED00.cpp" />
    <ClCompile Include="Source\DecodeCCheckSumCharacAccountTrunkDataQEAAXPEAU_AVA_1402C0C60.cpp" />
    <ClCompile Include="Source\DecodeCCheckSumGuildDataQEAAXNNZ_1402C11D0.cpp" />
    <ClCompile Include="Source\DecodeValueCCheckSumQEAAKEKKZ_1402C0620.cpp" />
    <ClCompile Include="Source\DecryptCCryptorQEAA_NPEBE_KPEAE1Z_14046B4F0.cpp" />
    <ClCompile Include="Source\DecryptDL_DecryptorBaseUECPPointCryptoPPCryptoPPUE_140455EB0.cpp" />
    <ClCompile Include="Source\DecryptDL_DecryptorBaseVIntegerCryptoPPCryptoPPUEB_140635370.cpp" />
    <ClCompile Include="Source\DeCryptStringYAXPEADHEGZ_14043BCF0.cpp" />
    <ClCompile Include="Source\DecryptTF_DecryptorBaseCryptoPPUEBAAUDecodingResul_1406236E0.cpp" />
    <ClCompile Include="Source\DeCrypt_MoveYAXPEADHEGZ_14043BE30.cpp" />
    <ClCompile Include="Source\DigestSizeIteratedHashWithStaticTransformIUEnumToT_14044EDB0.cpp" />
    <ClCompile Include="Source\DigestSizeIteratedHashWithStaticTransformIUEnumToT_140463EE0.cpp" />
    <ClCompile Include="Source\EncodeCCheckSumCharacAccountTrunkDataQEAAXPEAU_AVA_1402C0C00.cpp" />
    <ClCompile Include="Source\EncodeCCheckSumGuildDataQEAAXNNZ_1402C1160.cpp" />
    <ClCompile Include="Source\EncodeValueCCheckSumQEAAKEKKZ_1402C05A0.cpp" />
    <ClCompile Include="Source\EncryptCCryptorQEAA_NPEBE_KPEAE1Z_14046B470.cpp" />
    <ClCompile Include="Source\EncryptCCryptParamQEAA_NPEBE_KPEAE1Z_1404479C0.cpp" />
    <ClCompile Include="Source\EncryptDL_EncryptorBaseUECPPointCryptoPPCryptoPPUE_1404551D0.cpp" />
    <ClCompile Include="Source\EncryptDL_EncryptorBaseVIntegerCryptoPPCryptoPPUEB_140634BB0.cpp" />
    <ClCompile Include="Source\EncryptionPairwiseConsistencyTest_FIPS_140_OnlyCry_14062CF30.cpp" />
    <ClCompile Include="Source\EnCryptStringYAXPEADHEGZ_14043BC50.cpp" />
    <ClCompile Include="Source\EncryptTF_EncryptorBaseCryptoPPUEBAXAEAVRandomNumb_140623980.cpp" />
    <ClCompile Include="Source\EnCrypt_MoveYAXPEADHEGZ_14043BD90.cpp" />
    <ClCompile Include="Source\end_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_comp_140208910.cpp" />
    <ClCompile Include="Source\end_HashV_Hmap_traitsHPEAVCNationSettingFactoryVha_14021B4D0.cpp" />
    <ClCompile Include="Source\end_HashV_Hmap_traitsHPEBU_CashShop_fldVhash_compa_140304F10.cpp" />
    <ClCompile Include="Source\end_HashV_Hmap_traitsPEAUScheduleMSGKVhash_compare_140420970.cpp" />
    <ClCompile Include="Source\FinalHashTransformationCryptoPPUEAAXPEAEZ_14044DDD0.cpp" />
    <ClCompile Include="Source\findkeyCHashMapPtrPoolHVCNationCodeStrQEAA_NAEBVCN_14020BE00.cpp" />
    <ClCompile Include="Source\find_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_com_14020D1A0.cpp" />
    <ClCompile Include="Source\find_HashV_Hmap_traitsHPEAVCNationSettingFactoryVh_14021C5B0.cpp" />
    <ClCompile Include="Source\find_HashV_Hmap_traitsHPEBU_CashShop_fldVhash_comp_140305FF0.cpp" />
    <ClCompile Include="Source\find_HashV_Hmap_traitsPEAUScheduleMSGKVhash_compar_140420B00.cpp" />
    <ClCompile Include="Source\FirstPutHashVerificationFilterCryptoPPMEAAXPEBEZ_1405FD100.cpp" />
    <ClCompile Include="Source\GenerateAndMaskP1363_MGF1CryptoPPUEBAXAEAVHashTran_14055D350.cpp" />
    <ClCompile Include="Source\GetAlgorithmSimpleKeyedTransformationVHashTransfor_1404653D0.cpp" />
    <ClCompile Include="Source\GetBitCountHiIteratedHashBaseIVHashTransformationC_1405518B0.cpp" />
    <ClCompile Include="Source\GetBitCountHiIteratedHashBaseIVSimpleKeyedTransfor_1405519C0.cpp" />
    <ClCompile Include="Source\GetBitCountHiIteratedHashBase_KVHashTransformation_140551710.cpp" />
    <ClCompile Include="Source\GetBitCountHiIteratedHashBase_KVSimpleKeyedTransfo_140551820.cpp" />
    <ClCompile Include="Source\GetBitCountLoIteratedHashBaseIVHashTransformationC_1405518D0.cpp" />
    <ClCompile Include="Source\GetBitCountLoIteratedHashBaseIVSimpleKeyedTransfor_1405519E0.cpp" />
    <ClCompile Include="Source\GetBitCountLoIteratedHashBase_KVHashTransformation_140551730.cpp" />
    <ClCompile Include="Source\GetBitCountLoIteratedHashBase_KVSimpleKeyedTransfo_140551840.cpp" />
    <ClCompile Include="Source\GetByteOrderIteratedHashIUEnumToTypeW4ByteOrderCry_14044ECC0.cpp" />
    <ClCompile Include="Source\getCHashMapPtrPoolHVCNationCodeStrQEAA_NHAEAPEAVCN_14020BC40.cpp" />
    <ClCompile Include="Source\getCHashMapPtrPoolHVCNationSettingFactoryQEAA_NHAE_14022A860.cpp" />
    <ClCompile Include="Source\GetDalantCCheckSumGuildDataQEAANXZ_1402C1240.cpp" />
    <ClCompile Include="Source\GetGoldCCheckSumGuildDataQEAANXZ_1402C12B0.cpp" />
    <ClCompile Include="Source\GetKeyAgreementAlgorithmDL_ObjectImplVDL_Decryptor_140455C40.cpp" />
    <ClCompile Include="Source\GetKeyAgreementAlgorithmDL_ObjectImplVDL_Decryptor_140635210.cpp" />
    <ClCompile Include="Source\GetKeyAgreementAlgorithmDL_ObjectImplVDL_Encryptor_140454F60.cpp" />
    <ClCompile Include="Source\GetKeyAgreementAlgorithmDL_ObjectImplVDL_Encryptor_140634A50.cpp" />
    <ClCompile Include="Source\GetKeyDerivationAlgorithmDL_ObjectImplVDL_Decrypto_140455CB0.cpp" />
    <ClCompile Include="Source\GetKeyDerivationAlgorithmDL_ObjectImplVDL_Decrypto_140635250.cpp" />
    <ClCompile Include="Source\GetKeyDerivationAlgorithmDL_ObjectImplVDL_Encrypto_140454FD0.cpp" />
    <ClCompile Include="Source\GetKeyDerivationAlgorithmDL_ObjectImplVDL_Encrypto_140634A90.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_DecryptorBaseU_140455E20.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_DecryptorBaseV_140635320.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_EncryptorBaseU_140455140.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_EncryptorBaseV_140634B60.cpp" />
    <ClCompile Include="Source\GetMaxSymmetricPlaintextLengthDL_EncryptionAlgorit_1404646F0.cpp" />
    <ClCompile Include="Source\GetMaxSymmetricPlaintextLengthDL_EncryptionAlgorit_14063CDB0.cpp" />
    <ClCompile Include="Source\GetPolicyConcretePolicyHolderVEmptyCryptoPPVCFB_De_14061ABE0.cpp" />
    <ClCompile Include="Source\GetPolicyConcretePolicyHolderVEmptyCryptoPPVCFB_En_14061AA50.cpp" />
    <ClCompile Include="Source\GetSymmetricCiphertextLengthDL_EncryptionAlgorithm_1404646D0.cpp" />
    <ClCompile Include="Source\GetSymmetricCiphertextLengthDL_EncryptionAlgorithm_14063CD90.cpp" />
    <ClCompile Include="Source\GetSymmetricEncryptionAlgorithmDL_ObjectImplVDL_De_140455D20.cpp" />
    <ClCompile Include="Source\GetSymmetricEncryptionAlgorithmDL_ObjectImplVDL_De_140635290.cpp" />
    <ClCompile Include="Source\GetSymmetricEncryptionAlgorithmDL_ObjectImplVDL_En_140455040.cpp" />
    <ClCompile Include="Source\GetSymmetricEncryptionAlgorithmDL_ObjectImplVDL_En_140634AD0.cpp" />
    <ClCompile Include="Source\GetSymmetricKeyLengthDL_EncryptionAlgorithm_XorVHM_1404646B0.cpp" />
    <ClCompile Include="Source\GetSymmetricKeyLengthDL_EncryptionAlgorithm_XorVHM_14063CD70.cpp" />
    <ClCompile Include="Source\HashBlockIteratedHashBaseIVHashTransformationCrypt_1405518F0.cpp" />
    <ClCompile Include="Source\HashBlockIteratedHashBaseIVSimpleKeyedTransformati_140551A00.cpp" />
    <ClCompile Include="Source\HashBlockIteratedHashBase_KVHashTransformationCryp_140551750.cpp" />
    <ClCompile Include="Source\HashBlockIteratedHashBase_KVSimpleKeyedTransformat_140551860.cpp" />
    <ClCompile Include="Source\HashMultipleBlocksIteratedHashBaseIVHashTransforma_140571BB0.cpp" />
    <ClCompile Include="Source\HashMultipleBlocksIteratedHashBaseIVSimpleKeyedTra_140572440.cpp" />
    <ClCompile Include="Source\HashMultipleBlocksIteratedHashBase_KVHashTransform_140570A70.cpp" />
    <ClCompile Include="Source\HashMultipleBlocksIteratedHashBase_KVSimpleKeyedTr_140571320.cpp" />
    <ClCompile Include="Source\HashVerificationFilterFlagsNameCryptoPPYAPEBDXZ_1405FF630.cpp" />
    <ClCompile Include="Source\hash_valueHstdextYA_KAEBHZ_140210930.cpp" />
    <ClCompile Include="Source\hash_valuePEAUScheduleMSGstdextYA_KAEBQEAUSchedule_140429060.cpp" />
    <ClCompile Include="Source\InitCCheckSumQEAA_NXZ_1402C0590.cpp" />
    <ClCompile Include="Source\InitializeDerivedAndReturnNewSizesHashVerification_1405FCFF0.cpp" />
    <ClCompile Include="Source\InitIteratedHashWithStaticTransformIUEnumToTypeW4B_14044EF10.cpp" />
    <ClCompile Include="Source\InitIteratedHashWithStaticTransformIUEnumToTypeW4B_140463F50.cpp" />
    <ClCompile Include="Source\insert_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_c_14020C140.cpp" />
    <ClCompile Include="Source\insert_HashV_Hmap_traitsHPEAVCNationSettingFactory_14021B550.cpp" />
    <ClCompile Include="Source\insert_HashV_Hmap_traitsHPEBU_CashShop_fldVhash_co_140304F90.cpp" />
    <ClCompile Include="Source\insert_HashV_Hmap_traitsPEAUScheduleMSGKVhash_comp_140421AD0.cpp" />
    <ClCompile Include="Source\IsForwardTransformationCFB_DecryptionTemplateVAbst_14055AD90.cpp" />
    <ClCompile Include="Source\IsForwardTransformationCFB_DecryptionTemplateVAbst_14055AE60.cpp" />
    <ClCompile Include="Source\IsForwardTransformationCFB_EncryptionTemplateVAbst_14055AD80.cpp" />
    <ClCompile Include="Source\IsForwardTransformationCFB_EncryptionTemplateVAbst_14055AE50.cpp" />
    <ClCompile Include="Source\IsolatedInitializeHashFilterCryptoPPUEAAXAEBVNameV_1405FCB10.cpp" />
    <ClCompile Include="Source\j_0AlgorithmImplVCBC_DecryptionCryptoPPVCipherMode_1400052AE.cpp" />
    <ClCompile Include="Source\j_0AlgorithmImplVCBC_EncryptionCryptoPPVCipherMode_14000B483.cpp" />
    <ClCompile Include="Source\j_0AlgorithmImplVDL_DecryptorBaseUECPPointCryptoPP_1400073BA.cpp" />
    <ClCompile Include="Source\j_0AlgorithmImplVDL_EncryptorBaseUECPPointCryptoPP_140008C97.cpp" />
    <ClCompile Include="Source\j_0AlgorithmImplVIteratedHashIUEnumToTypeW4ByteOrd_140004165.cpp" />
    <ClCompile Include="Source\j_0AlgorithmImplVIteratedHashIUEnumToTypeW4ByteOrd_140009B7E.cpp" />
    <ClCompile Include="Source\j_0AlgorithmImplVIteratedHashIUEnumToTypeW4ByteOrd_14000D22E.cpp" />
    <ClCompile Include="Source\j_0AlgorithmImplVIteratedHashIUEnumToTypeW4ByteOrd_14001377D.cpp" />
    <ClCompile Include="Source\j_0CBC_DecryptionCryptoPPQEAAXZ_140011EE6.cpp" />
    <ClCompile Include="Source\j_0CBC_EncryptionCryptoPPQEAAXZ_1400073EC.cpp" />
    <ClCompile Include="Source\j_0CCheckSumCharacAccountTrunkDataQEAAKKEZ_140013AE8.cpp" />
    <ClCompile Include="Source\j_0CCheckSumGuildDataQEAAKZ_1400073A1.cpp" />
    <ClCompile Include="Source\j_0CCheckSumQEAAXZ_14000A1E1.cpp" />
    <ClCompile Include="Source\j_0CHashMapPtrPoolHVCNationCodeStrQEAAXZ_140007EFF.cpp" />
    <ClCompile Include="Source\j_0CHashMapPtrPoolHVCNationSettingFactoryQEAAXZ_14000EC55.cpp" />
    <ClCompile Include="Source\j_0CipherModeFinalTemplate_CipherHolderVBlockCiphe_14000BD5C.cpp" />
    <ClCompile Include="Source\j_0CipherModeFinalTemplate_CipherHolderVBlockCiphe_14000F63C.cpp" />
    <ClCompile Include="Source\j_0ClonableImplVSHA1CryptoPPVAlgorithmImplVIterate_140008337.cpp" />
    <ClCompile Include="Source\j_0ClonableImplVSHA1CryptoPPVAlgorithmImplVIterate_14000EA11.cpp" />
    <ClCompile Include="Source\j_0ClonableImplVSHA256CryptoPPVAlgorithmImplVItera_14000985E.cpp" />
    <ClCompile Include="Source\j_0ClonableImplVSHA256CryptoPPVAlgorithmImplVItera_14001361A.cpp" />
    <ClCompile Include="Source\j_0DL_CryptoSystemBaseVPK_DecryptorCryptoPPVDL_Pri_140011261.cpp" />
    <ClCompile Include="Source\j_0DL_CryptoSystemBaseVPK_EncryptorCryptoPPVDL_Pub_140007D0B.cpp" />
    <ClCompile Include="Source\j_0DL_DecryptorBaseUECPPointCryptoPPCryptoPPQEAAXZ_140001154.cpp" />
    <ClCompile Include="Source\j_0DL_DecryptorImplUDL_CryptoSchemeOptionsUECIESVE_140010136.cpp" />
    <ClCompile Include="Source\j_0DL_EncryptionAlgorithm_XorVHMACVSHA1CryptoPPCry_140009D7C.cpp" />
    <ClCompile Include="Source\j_0DL_EncryptorBaseUECPPointCryptoPPCryptoPPQEAAXZ_140009E5D.cpp" />
    <ClCompile Include="Source\j_0DL_EncryptorImplUDL_CryptoSchemeOptionsUECIESVE_1400075B3.cpp" />
    <ClCompile Include="Source\j_0DL_ObjectImplBaseVDL_DecryptorBaseUECPPointCryp_140001A32.cpp" />
    <ClCompile Include="Source\j_0DL_ObjectImplBaseVDL_EncryptorBaseUECPPointCryp_14000C9EB.cpp" />
    <ClCompile Include="Source\j_0DL_ObjectImplVDL_DecryptorBaseUECPPointCryptoPP_1400017CB.cpp" />
    <ClCompile Include="Source\j_0DL_ObjectImplVDL_EncryptorBaseUECPPointCryptoPP_1400117F7.cpp" />
    <ClCompile Include="Source\j_0DL_SymmetricEncryptionAlgorithmCryptoPPQEAAXZ_14000FE07.cpp" />
    <ClCompile Include="Source\j_0HashTransformationCryptoPPQEAAAEBV01Z_14000ED95.cpp" />
    <ClCompile Include="Source\j_0HashTransformationCryptoPPQEAAXZ_1400101D6.cpp" />
    <ClCompile Include="Source\j_0hash_compareHUlessHstdstdextQEAAXZ_14000E8B8.cpp" />
    <ClCompile Include="Source\j_0hash_comparePEAUScheduleMSGUlessPEAUScheduleMSG_14000256D.cpp" />
    <ClCompile Include="Source\j_0hash_mapHPEAVCNationCodeStrVhash_compareHUlessH_14001177F.cpp" />
    <ClCompile Include="Source\j_0hash_mapHPEAVCNationSettingFactoryVhash_compare_140004DBD.cpp" />
    <ClCompile Include="Source\j_0hash_mapHPEBU_CashShop_fldVhash_compareHUlessHs_140010C08.cpp" />
    <ClCompile Include="Source\j_0hash_mapPEAUScheduleMSGKVhash_comparePEAUSchedu_14000F71D.cpp" />
    <ClCompile Include="Source\j_0IteratedHashBaseIVHashTransformationCryptoPPCry_140007E46.cpp" />
    <ClCompile Include="Source\j_0IteratedHashBaseIVHashTransformationCryptoPPCry_1400129D1.cpp" />
    <ClCompile Include="Source\j_0IteratedHashIUEnumToTypeW4ByteOrderCryptoPP00Cr_14000356C.cpp" />
    <ClCompile Include="Source\j_0IteratedHashIUEnumToTypeW4ByteOrderCryptoPP00Cr_14000CD33.cpp" />
    <ClCompile Include="Source\j_0IteratedHashWithStaticTransformIUEnumToTypeW4By_1400034BD.cpp" />
    <ClCompile Include="Source\j_0IteratedHashWithStaticTransformIUEnumToTypeW4By_140005114.cpp" />
    <ClCompile Include="Source\j_0IteratedHashWithStaticTransformIUEnumToTypeW4By_1400054E8.cpp" />
    <ClCompile Include="Source\j_0IteratedHashWithStaticTransformIUEnumToTypeW4By_140005B00.cpp" />
    <ClCompile Include="Source\j_0PK_DecryptorCryptoPPQEAAXZ_140006186.cpp" />
    <ClCompile Include="Source\j_0PK_EncryptorCryptoPPQEAAXZ_140011D65.cpp" />
    <ClCompile Include="Source\j_0PK_FinalTemplateVDL_DecryptorImplUDL_CryptoSche_14000E313.cpp" />
    <ClCompile Include="Source\j_0PK_FinalTemplateVDL_EncryptorImplUDL_CryptoSche_14000354E.cpp" />
    <ClCompile Include="Source\j_0SimpleKeyedTransformationVHashTransformationCry_14000551F.cpp" />
    <ClCompile Include="Source\j_0simple_ptrVDL_EncryptionAlgorithm_XorVHMACVSHA1_140006F2D.cpp" />
    <ClCompile Include="Source\j_0SingletonVDL_EncryptionAlgorithm_XorVHMACVSHA1C_14000B690.cpp" />
    <ClCompile Include="Source\j_0_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_comp_140009813.cpp" />
    <ClCompile Include="Source\j_0_HashV_Hmap_traitsHPEAVCNationSettingFactoryVha_140010BD1.cpp" />
    <ClCompile Include="Source\j_0_HashV_Hmap_traitsHPEBU_CashShop_fldVhash_compa_14000AAA1.cpp" />
    <ClCompile Include="Source\j_0_HashV_Hmap_traitsPEAUScheduleMSGKVhash_compare_14000D003.cpp" />
    <ClCompile Include="Source\j_0_Hmap_traitsHPEAVCNationCodeStrVhash_compareHUl_1400105EB.cpp" />
    <ClCompile Include="Source\j_0_Hmap_traitsHPEAVCNationSettingFactoryVhash_com_140009F7F.cpp" />
    <ClCompile Include="Source\j_0_Hmap_traitsHPEBU_CashShop_fldVhash_compareHUle_1400050DD.cpp" />
    <ClCompile Include="Source\j_0_Hmap_traitsPEAUScheduleMSGKVhash_comparePEAUSc_14000DBB6.cpp" />
    <ClCompile Include="Source\j_1AlgorithmImplVCBC_DecryptionCryptoPPVCipherMode_140007482.cpp" />
    <ClCompile Include="Source\j_1AlgorithmImplVCBC_EncryptionCryptoPPVCipherMode_140012F71.cpp" />
    <ClCompile Include="Source\j_1AlgorithmImplVDL_DecryptorBaseUECPPointCryptoPP_140002B71.cpp" />
    <ClCompile Include="Source\j_1AlgorithmImplVDL_EncryptorBaseUECPPointCryptoPP_14000C0B3.cpp" />
    <ClCompile Include="Source\j_1AlgorithmImplVIteratedHashIUEnumToTypeW4ByteOrd_14000283D.cpp" />
    <ClCompile Include="Source\j_1AlgorithmImplVIteratedHashIUEnumToTypeW4ByteOrd_140012689.cpp" />
    <ClCompile Include="Source\j_1CBC_DecryptionCryptoPPUEAAXZ_14000CD92.cpp" />
    <ClCompile Include="Source\j_1CBC_EncryptionCryptoPPUEAAXZ_14000C9C3.cpp" />
    <ClCompile Include="Source\j_1CCheckSumCharacAccountTrunkDataQEAAXZ_140012E31.cpp" />
    <ClCompile Include="Source\j_1CCheckSumGuildDataQEAAXZ_14000E1F1.cpp" />
    <ClCompile Include="Source\j_1CCheckSumQEAAXZ_140007CED.cpp" />
    <ClCompile Include="Source\j_1CHashMapPtrPoolHVCNationCodeStrQEAAXZ_1400106EF.cpp" />
    <ClCompile Include="Source\j_1CHashMapPtrPoolHVCNationSettingFactoryQEAAXZ_14000A2AE.cpp" />
    <ClCompile Include="Source\j_1CipherModeFinalTemplate_CipherHolderVBlockCiphe_140002077.cpp" />
    <ClCompile Include="Source\j_1CipherModeFinalTemplate_CipherHolderVBlockCiphe_14000B3F7.cpp" />
    <ClCompile Include="Source\j_1ClonableImplVSHA1CryptoPPVAlgorithmImplVIterate_14001091A.cpp" />
    <ClCompile Include="Source\j_1ClonableImplVSHA256CryptoPPVAlgorithmImplVItera_14000D576.cpp" />
    <ClCompile Include="Source\j_1DL_CryptoSystemBaseVPK_DecryptorCryptoPPVDL_Pri_14000740A.cpp" />
    <ClCompile Include="Source\j_1DL_CryptoSystemBaseVPK_EncryptorCryptoPPVDL_Pub_14000F894.cpp" />
    <ClCompile Include="Source\j_1DL_DecryptorBaseUECPPointCryptoPPCryptoPPUEAAXZ_140007946.cpp" />
    <ClCompile Include="Source\j_1DL_DecryptorImplUDL_CryptoSchemeOptionsUECIESVE_140004D77.cpp" />
    <ClCompile Include="Source\j_1DL_EncryptorBaseUECPPointCryptoPPCryptoPPUEAAXZ_14000FDEE.cpp" />
    <ClCompile Include="Source\j_1DL_EncryptorImplUDL_CryptoSchemeOptionsUECIESVE_140013AA7.cpp" />
    <ClCompile Include="Source\j_1DL_ObjectImplBaseVDL_DecryptorBaseUECPPointCryp_140009BBA.cpp" />
    <ClCompile Include="Source\j_1DL_ObjectImplBaseVDL_EncryptorBaseUECPPointCryp_140005B6E.cpp" />
    <ClCompile Include="Source\j_1DL_ObjectImplVDL_DecryptorBaseUECPPointCryptoPP_140012BAC.cpp" />
    <ClCompile Include="Source\j_1DL_ObjectImplVDL_EncryptorBaseUECPPointCryptoPP_140001357.cpp" />
    <ClCompile Include="Source\j_1HashTransformationCryptoPPUEAAXZ_140003701.cpp" />
    <ClCompile Include="Source\j_1hash_mapHPEAVCNationCodeStrVhash_compareHUlessH_14000AADD.cpp" />
    <ClCompile Include="Source\j_1hash_mapHPEAVCNationSettingFactoryVhash_compare_1400041D3.cpp" />
    <ClCompile Include="Source\j_1hash_mapHPEBU_CashShop_fldVhash_compareHUlessHs_140012774.cpp" />
    <ClCompile Include="Source\j_1hash_mapPEAUScheduleMSGKVhash_comparePEAUSchedu_14000D2A6.cpp" />
    <ClCompile Include="Source\j_1IteratedHashBaseIVHashTransformationCryptoPPCry_14000F687.cpp" />
    <ClCompile Include="Source\j_1IteratedHashIUEnumToTypeW4ByteOrderCryptoPP00Cr_14000E971.cpp" />
    <ClCompile Include="Source\j_1IteratedHashWithStaticTransformIUEnumToTypeW4By_140005FFB.cpp" />
    <ClCompile Include="Source\j_1IteratedHashWithStaticTransformIUEnumToTypeW4By_140009368.cpp" />
    <ClCompile Include="Source\j_1PK_DecryptorCryptoPPUEAAXZ_14000F416.cpp" />
    <ClCompile Include="Source\j_1PK_EncryptorCryptoPPUEAAXZ_14000BE65.cpp" />
    <ClCompile Include="Source\j_1PK_FinalTemplateVDL_DecryptorImplUDL_CryptoSche_140001F14.cpp" />
    <ClCompile Include="Source\j_1PK_FinalTemplateVDL_EncryptorImplUDL_CryptoSche_14001235A.cpp" />
    <ClCompile Include="Source\j_1SimpleKeyedTransformationVHashTransformationCry_140005BCD.cpp" />
    <ClCompile Include="Source\j_1simple_ptrVDL_EncryptionAlgorithm_XorVHMACVSHA1_140007D6A.cpp" />
    <ClCompile Include="Source\j_1_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_comp_14000B5EB.cpp" />
    <ClCompile Include="Source\j_1_HashV_Hmap_traitsHPEAVCNationSettingFactoryVha_14000B3CA.cpp" />
    <ClCompile Include="Source\j_1_HashV_Hmap_traitsHPEBU_CashShop_fldVhash_compa_14000AC95.cpp" />
    <ClCompile Include="Source\j_1_HashV_Hmap_traitsPEAUScheduleMSGKVhash_compare_1400033DC.cpp" />
    <ClCompile Include="Source\j_AccessHashHMACVSHA1CryptoPPCryptoPPEEAAAEAVHashT_14000D873.cpp" />
    <ClCompile Include="Source\j_AccessKeyDL_ObjectImplBaseVDL_DecryptorBaseUECPP_1400114D7.cpp" />
    <ClCompile Include="Source\j_AccessKeyDL_ObjectImplBaseVDL_EncryptorBaseUECPP_14001145A.cpp" />
    <ClCompile Include="Source\j_AccessKeyInterfaceDL_ObjectImplBaseVDL_Decryptor_14000F5E7.cpp" />
    <ClCompile Include="Source\j_AccessKeyInterfaceDL_ObjectImplBaseVDL_Encryptor_14000F97A.cpp" />
    <ClCompile Include="Source\j_AccessPrivateKeyDL_ObjectImplBaseVDL_DecryptorBa_1400030B7.cpp" />
    <ClCompile Include="Source\j_AccessPublicKeyDL_ObjectImplBaseVDL_EncryptorBas_14000BFF0.cpp" />
    <ClCompile Include="Source\j_Ahash_mapPEAUScheduleMSGKVhash_comparePEAUSchedu_14000B0A0.cpp" />
    <ClCompile Include="Source\j_AlgorithmNameAlgorithmImplVCBC_DecryptionCryptoP_1400081A7.cpp" />
    <ClCompile Include="Source\j_AlgorithmNameAlgorithmImplVCBC_EncryptionCryptoP_14000C25C.cpp" />
    <ClCompile Include="Source\j_AlgorithmNameAlgorithmImplVDL_DecryptorBaseUECPP_14000DCDD.cpp" />
    <ClCompile Include="Source\j_AlgorithmNameAlgorithmImplVDL_EncryptorBaseUECPP_140011CE8.cpp" />
    <ClCompile Include="Source\j_AlgorithmNameAlgorithmImplVIteratedHashIUEnumToT_140002400.cpp" />
    <ClCompile Include="Source\j_AlgorithmNameAlgorithmImplVIteratedHashIUEnumToT_140012CC9.cpp" />
    <ClCompile Include="Source\j_begin_HashV_Hmap_traitsHPEAVCNationCodeStrVhash__14000F96B.cpp" />
    <ClCompile Include="Source\j_begin_HashV_Hmap_traitsHPEAVCNationSettingFactor_14000FEBB.cpp" />
    <ClCompile Include="Source\j_BlockSizeHashTransformationCryptoPPUEBAIXZ_140012652.cpp" />
    <ClCompile Include="Source\j_BlockSizeIteratedHashIUEnumToTypeW4ByteOrderCryp_14000943F.cpp" />
    <ClCompile Include="Source\j_CalculateDigestHashTransformationCryptoPPUEAAXPE_140001631.cpp" />
    <ClCompile Include="Source\j_CalculateTruncatedDigestHashTransformationCrypto_140007F63.cpp" />
    <ClCompile Include="Source\j_CheckGuildCheckSumCGuildRankingAEAA_NKPEADAEAN1Z_140003E54.cpp" />
    <ClCompile Include="Source\j_CheckPublicKeyHashCCryptParamIEAAXAEAVByteQueueC_140004502.cpp" />
    <ClCompile Include="Source\j_CiphertextLengthDL_CryptoSystemBaseVPK_Decryptor_14000FDE9.cpp" />
    <ClCompile Include="Source\j_CiphertextLengthDL_CryptoSystemBaseVPK_Encryptor_140009769.cpp" />
    <ClCompile Include="Source\j_cleanupCHashMapPtrPoolHVCNationCodeStrQEAAXXZ_14000FB05.cpp" />
    <ClCompile Include="Source\j_cleanupCHashMapPtrPoolHVCNationSettingFactoryQEA_140007BBC.cpp" />
    <ClCompile Include="Source\j_clear_HashV_Hmap_traitsPEAUScheduleMSGKVhash_com_14000F47F.cpp" />
    <ClCompile Include="Source\j_CloneClonableImplVSHA1CryptoPPVAlgorithmImplVIte_1400044D0.cpp" />
    <ClCompile Include="Source\j_CloneClonableImplVSHA256CryptoPPVAlgorithmImplVI_140013A34.cpp" />
    <ClCompile Include="Source\j_ConvertCCheckSumCharacTrunkConverterQEAAXPEAU_AV_14000F4CF.cpp" />
    <ClCompile Include="Source\j_ConvertCCheckSumGuildConverterQEAAXNNPEAVCCheckS_1400052D6.cpp" />
    <ClCompile Include="Source\j_ConvertTrunkCCheckSumCharacTrunkConverterQEAAXKP_140010956.cpp" />
    <ClCompile Include="Source\j_CreateUpdateSpaceHashTransformationCryptoPPUEAAP_140002C8E.cpp" />
    <ClCompile Include="Source\j_DataBufIteratedHashIUEnumToTypeW4ByteOrderCrypto_14000EF66.cpp" />
    <ClCompile Include="Source\j_DecodeCCheckSumCharacAccountTrunkDataQEAAXPEAU_A_14000FD3A.cpp" />
    <ClCompile Include="Source\j_DecodeCCheckSumGuildDataQEAAXNNZ_140011E19.cpp" />
    <ClCompile Include="Source\j_DecodeValueCCheckSumQEAAKEKKZ_140009516.cpp" />
    <ClCompile Include="Source\j_DecryptCCryptorQEAA_NPEBE_KPEAE1Z_14000EA8E.cpp" />
    <ClCompile Include="Source\j_DecryptDL_DecryptorBaseUECPPointCryptoPPCryptoPP_14001291D.cpp" />
    <ClCompile Include="Source\j_DeCryptStringYAXPEADHEGZ_140005501.cpp" />
    <ClCompile Include="Source\j_DeCrypt_MoveYAXPEADHEGZ_14000A3E4.cpp" />
    <ClCompile Include="Source\j_DigestSizeIteratedHashWithStaticTransformIUEnumT_14000C059.cpp" />
    <ClCompile Include="Source\j_DigestSizeIteratedHashWithStaticTransformIUEnumT_14000CC93.cpp" />
    <ClCompile Include="Source\j_EncodeCCheckSumCharacAccountTrunkDataQEAAXPEAU_A_14000A759.cpp" />
    <ClCompile Include="Source\j_EncodeCCheckSumGuildDataQEAAXNNZ_1400137C8.cpp" />
    <ClCompile Include="Source\j_EncodeValueCCheckSumQEAAKEKKZ_14001221F.cpp" />
    <ClCompile Include="Source\j_EncryptCCryptorQEAA_NPEBE_KPEAE1Z_140012F76.cpp" />
    <ClCompile Include="Source\j_EncryptCCryptParamQEAA_NPEBE_KPEAE1Z_14000A542.cpp" />
    <ClCompile Include="Source\j_EncryptDL_EncryptorBaseUECPPointCryptoPPCryptoPP_140004412.cpp" />
    <ClCompile Include="Source\j_EnCryptStringYAXPEADHEGZ_140010C94.cpp" />
    <ClCompile Include="Source\j_EnCrypt_MoveYAXPEADHEGZ_140008189.cpp" />
    <ClCompile Include="Source\j_end_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_co_140006389.cpp" />
    <ClCompile Include="Source\j_end_HashV_Hmap_traitsHPEAVCNationSettingFactoryV_14000CF90.cpp" />
    <ClCompile Include="Source\j_end_HashV_Hmap_traitsHPEBU_CashShop_fldVhash_com_140013C3C.cpp" />
    <ClCompile Include="Source\j_end_HashV_Hmap_traitsPEAUScheduleMSGKVhash_compa_140006E4C.cpp" />
    <ClCompile Include="Source\j_FinalHashTransformationCryptoPPUEAAXPEAEZ_14000FC18.cpp" />
    <ClCompile Include="Source\j_findkeyCHashMapPtrPoolHVCNationCodeStrQEAA_NAEBV_140007E5F.cpp" />
    <ClCompile Include="Source\j_find_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_c_14000BE6F.cpp" />
    <ClCompile Include="Source\j_find_HashV_Hmap_traitsHPEAVCNationSettingFactory_140006F23.cpp" />
    <ClCompile Include="Source\j_find_HashV_Hmap_traitsHPEBU_CashShop_fldVhash_co_14000EAD9.cpp" />
    <ClCompile Include="Source\j_find_HashV_Hmap_traitsPEAUScheduleMSGKVhash_comp_1400026BC.cpp" />
    <ClCompile Include="Source\j_GetAlgorithmSimpleKeyedTransformationVHashTransf_14000489A.cpp" />
    <ClCompile Include="Source\j_GetByteOrderIteratedHashIUEnumToTypeW4ByteOrderC_14000EF1B.cpp" />
    <ClCompile Include="Source\j_getCHashMapPtrPoolHVCNationCodeStrQEAA_NHAEAPEAV_140013FE3.cpp" />
    <ClCompile Include="Source\j_getCHashMapPtrPoolHVCNationSettingFactoryQEAA_NH_14000397C.cpp" />
    <ClCompile Include="Source\j_GetDalantCCheckSumGuildDataQEAANXZ_14000EC0F.cpp" />
    <ClCompile Include="Source\j_GetGoldCCheckSumGuildDataQEAANXZ_14000227A.cpp" />
    <ClCompile Include="Source\j_GetKeyAgreementAlgorithmDL_ObjectImplVDL_Decrypt_140003F0D.cpp" />
    <ClCompile Include="Source\j_GetKeyAgreementAlgorithmDL_ObjectImplVDL_Encrypt_1400050A6.cpp" />
    <ClCompile Include="Source\j_GetKeyDerivationAlgorithmDL_ObjectImplVDL_Decryp_14000AB28.cpp" />
    <ClCompile Include="Source\j_GetKeyDerivationAlgorithmDL_ObjectImplVDL_Encryp_140013741.cpp" />
    <ClCompile Include="Source\j_GetKeyInterfaceDL_ObjectImplBaseVDL_DecryptorBas_14000366B.cpp" />
    <ClCompile Include="Source\j_GetKeyInterfaceDL_ObjectImplBaseVDL_EncryptorBas_140002F81.cpp" />
    <ClCompile Include="Source\j_GetMaxSymmetricPlaintextLengthDL_EncryptionAlgor_14000324C.cpp" />
    <ClCompile Include="Source\j_GetSymmetricCiphertextLengthDL_EncryptionAlgorit_140011455.cpp" />
    <ClCompile Include="Source\j_GetSymmetricEncryptionAlgorithmDL_ObjectImplVDL__14000E421.cpp" />
    <ClCompile Include="Source\j_GetSymmetricEncryptionAlgorithmDL_ObjectImplVDL__1400123FA.cpp" />
    <ClCompile Include="Source\j_GetSymmetricKeyLengthDL_EncryptionAlgorithm_XorV_140010852.cpp" />
    <ClCompile Include="Source\j_hash_valueHstdextYA_KAEBHZ_14000DC6A.cpp" />
    <ClCompile Include="Source\j_hash_valuePEAUScheduleMSGstdextYA_KAEBQEAUSchedu_14000CE87.cpp" />
    <ClCompile Include="Source\j_InitCCheckSumQEAA_NXZ_14000C522.cpp" />
    <ClCompile Include="Source\j_InitIteratedHashWithStaticTransformIUEnumToTypeW_14000394A.cpp" />
    <ClCompile Include="Source\j_InitIteratedHashWithStaticTransformIUEnumToTypeW_14000EBD8.cpp" />
    <ClCompile Include="Source\j_insert_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_140001F55.cpp" />
    <ClCompile Include="Source\j_insert_HashV_Hmap_traitsHPEAVCNationSettingFacto_14001005F.cpp" />
    <ClCompile Include="Source\j_insert_HashV_Hmap_traitsHPEBU_CashShop_fldVhash__1400020E5.cpp" />
    <ClCompile Include="Source\j_insert_HashV_Hmap_traitsPEAUScheduleMSGKVhash_co_140001195.cpp" />
    <ClCompile Include="Source\j_lower_bound_HashV_Hmap_traitsHPEAVCNationCodeStr_14000BD75.cpp" />
    <ClCompile Include="Source\j_lower_bound_HashV_Hmap_traitsHPEAVCNationSetting_14000BC99.cpp" />
    <ClCompile Include="Source\j_lower_bound_HashV_Hmap_traitsHPEBU_CashShop_fldV_140006415.cpp" />
    <ClCompile Include="Source\j_lower_bound_HashV_Hmap_traitsPEAUScheduleMSGKVha_140006979.cpp" />
    <ClCompile Include="Source\j_MakeHashCCryptorQEAA_NPEBE_KPEAE1Z_140010677.cpp" />
    <ClCompile Include="Source\j_MaxPlaintextLengthDL_CryptoSystemBaseVPK_Decrypt_1400125F8.cpp" />
    <ClCompile Include="Source\j_MaxPlaintextLengthDL_CryptoSystemBaseVPK_Encrypt_14000A876.cpp" />
    <ClCompile Include="Source\j_OptimalBlockSizeIteratedHashBaseIVHashTransforma_14000B82F.cpp" />
    <ClCompile Include="Source\j_OptimalDataAlignmentHashTransformationCryptoPPUE_140004FCF.cpp" />
    <ClCompile Include="Source\j_OptimalDataAlignmentIteratedHashBaseIVHashTransf_1400030CB.cpp" />
    <ClCompile Include="Source\j_ParameterSupportedDL_CryptoSystemBaseVPK_Decrypt_1400131DD.cpp" />
    <ClCompile Include="Source\j_ParameterSupportedDL_CryptoSystemBaseVPK_Encrypt_14000E16F.cpp" />
    <ClCompile Include="Source\j_ParameterSupportedDL_EncryptionAlgorithm_XorVHMA_14001055F.cpp" />
    <ClCompile Include="Source\j_ProcCodeCCheckSumBaseConverterQEAAKEKKZ_1400117A2.cpp" />
    <ClCompile Include="Source\j_ProcCodeCCheckSumBaseConverterQEAANEKNZ_1400013F7.cpp" />
    <ClCompile Include="Source\j_RefSingletonVDL_EncryptionAlgorithm_XorVHMACVSHA_140002054.cpp" />
    <ClCompile Include="Source\j_registCHashMapPtrPoolHVCNationCodeStrQEAAHPEAVCN_1400123DC.cpp" />
    <ClCompile Include="Source\j_registCHashMapPtrPoolHVCNationSettingFactoryQEAA_140007EA5.cpp" />
    <ClCompile Include="Source\j_ResizeBuffersCBC_DecryptionCryptoPPMEAAXXZ_14000D896.cpp" />
    <ClCompile Include="Source\j_Rhash_compareHUlessHstdstdextQEBA_KAEBHZ_140005C6D.cpp" />
    <ClCompile Include="Source\j_Rhash_compareHUlessHstdstdextQEBA_NAEBH0Z_1400021B7.cpp" />
    <ClCompile Include="Source\j_Rhash_comparePEAUScheduleMSGUlessPEAUScheduleMSG_140001758.cpp" />
    <ClCompile Include="Source\j_Rhash_comparePEAUScheduleMSGUlessPEAUScheduleMSG_14000FCAE.cpp" />
    <ClCompile Include="Source\j_RNewObjectVDL_EncryptionAlgorithm_XorVHMACVSHA1C_140005533.cpp" />
    <ClCompile Include="Source\j_SetAESDecryptorCCryptParamIEAAXXZ_1400044AD.cpp" />
    <ClCompile Include="Source\j_SetValueCCheckSumCharacAccountTrunkDataIEAAXW4CO_140003F12.cpp" />
    <ClCompile Include="Source\j_SetValueCCheckSumCharacAccountTrunkDataIEAAXW4CO_140012B2F.cpp" />
    <ClCompile Include="Source\j_SetValueCCheckSumGuildDataIEAAXW4COLUMN_D_TYPE1N_1400084C2.cpp" />
    <ClCompile Include="Source\j_size_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_c_14000A30D.cpp" />
    <ClCompile Include="Source\j_size_HashV_Hmap_traitsHPEAVCNationSettingFactory_140011A45.cpp" />
    <ClCompile Include="Source\j_size_HashV_Hmap_traitsHPEBU_CashShop_fldVhash_co_14000924B.cpp" />
    <ClCompile Include="Source\j_size_HashV_Hmap_traitsPEAUScheduleMSGKVhash_comp_140004BA1.cpp" />
    <ClCompile Include="Source\j_StateBufIteratedHashWithStaticTransformIUEnumToT_140006753.cpp" />
    <ClCompile Include="Source\j_StateBufIteratedHashWithStaticTransformIUEnumToT_14000CBE4.cpp" />
    <ClCompile Include="Source\j_StaticAlgorithmNameCipherModeFinalTemplate_Ciphe_1400029FF.cpp" />
    <ClCompile Include="Source\j_StaticAlgorithmNameCipherModeFinalTemplate_Ciphe_140008058.cpp" />
    <ClCompile Include="Source\j_SymmetricDecryptDL_EncryptionAlgorithm_XorVHMACV_1400072E3.cpp" />
    <ClCompile Include="Source\j_SymmetricEncryptDL_EncryptionAlgorithm_XorVHMACV_14000C081.cpp" />
    <ClCompile Include="Source\j_VerifyDigestHashTransformationCryptoPPUEAA_NPEBE_14000D47C.cpp" />
    <ClCompile Include="Source\j_VerifyHashCCryptorQEAA_NPEBE_KPEAE1Z_14000A150.cpp" />
    <ClCompile Include="Source\j_VerifyHashTransformationCryptoPPUEAA_NPEBEZ_140002B49.cpp" />
    <ClCompile Include="Source\j_VerifyTruncatedDigestHashTransformationCryptoPPU_1400120B7.cpp" />
    <ClCompile Include="Source\j__AtlVerifyStackAvailable_ATL_SAFE_ALLOCA_IMPLATL_14000CC70.cpp" />
    <ClCompile Include="Source\j__CheckGuildCheckSumCMainThreadAEAA_NKPEADAEAN1Z_1400133B8.cpp" />
    <ClCompile Include="Source\j__ECipherModeFinalTemplate_CipherHolderVBlockCiph_140005AD8.cpp" />
    <ClCompile Include="Source\j__ECipherModeFinalTemplate_CipherHolderVBlockCiph_140009593.cpp" />
    <ClCompile Include="Source\j__ECipherModeFinalTemplate_CipherHolderVBlockCiph_14000EEA8.cpp" />
    <ClCompile Include="Source\j__ECipherModeFinalTemplate_CipherHolderVBlockCiph_1400137E1.cpp" />
    <ClCompile Include="Source\j__EDL_DecryptorImplUDL_CryptoSchemeOptionsUECIESV_140006276.cpp" />
    <ClCompile Include="Source\j__EDL_EncryptorImplUDL_CryptoSchemeOptionsUECIESV_14000BCB2.cpp" />
    <ClCompile Include="Source\j__Ehash_mapHPEBU_CashShop_fldVhash_compareHUlessH_140013F9D.cpp" />
    <ClCompile Include="Source\j__GCipherModeFinalTemplate_CipherHolderVBlockCiph_14000830F.cpp" />
    <ClCompile Include="Source\j__GCipherModeFinalTemplate_CipherHolderVBlockCiph_14000D5B7.cpp" />
    <ClCompile Include="Source\j__GDL_DecryptorImplUDL_CryptoSchemeOptionsUECIESV_140002789.cpp" />
    <ClCompile Include="Source\j__GDL_DecryptorImplUDL_CryptoSchemeOptionsUECIESV_140010D93.cpp" />
    <ClCompile Include="Source\j__GDL_EncryptorImplUDL_CryptoSchemeOptionsUECIESV_140003F08.cpp" />
    <ClCompile Include="Source\j__GDL_EncryptorImplUDL_CryptoSchemeOptionsUECIESV_1400108F2.cpp" />
    <ClCompile Include="Source\j__Get_iter_from_vec_HashV_Hmap_traitsHPEAVCNation_140002B44.cpp" />
    <ClCompile Include="Source\j__Get_iter_from_vec_HashV_Hmap_traitsHPEAVCNation_140005862.cpp" />
    <ClCompile Include="Source\j__Get_iter_from_vec_HashV_Hmap_traitsHPEBU_CashSh_140011013.cpp" />
    <ClCompile Include="Source\j__Get_iter_from_vec_HashV_Hmap_traitsPEAUSchedule_140002635.cpp" />
    <ClCompile Include="Source\j__Hashval_HashV_Hmap_traitsHPEAVCNationCodeStrVha_1400129C7.cpp" />
    <ClCompile Include="Source\j__Hashval_HashV_Hmap_traitsHPEAVCNationSettingFac_14001253A.cpp" />
    <ClCompile Include="Source\j__Hashval_HashV_Hmap_traitsHPEBU_CashShop_fldVhas_140006DBB.cpp" />
    <ClCompile Include="Source\j__Hashval_HashV_Hmap_traitsPEAUScheduleMSGKVhash__140007F27.cpp" />
    <ClCompile Include="Source\j__Kfn_Hmap_traitsHPEAVCNationCodeStrVhash_compare_1400039B8.cpp" />
    <ClCompile Include="Source\j__Kfn_Hmap_traitsHPEAVCNationSettingFactoryVhash__140013B74.cpp" />
    <ClCompile Include="Source\j__Kfn_Hmap_traitsHPEBU_CashShop_fldVhash_compareH_140012BA7.cpp" />
    <ClCompile Include="Source\j__Kfn_Hmap_traitsPEAUScheduleMSGKVhash_comparePEA_14000875B.cpp" />
    <ClCompile Include="Source\KeyInnerHashHMAC_BaseCryptoPPAEAAXXZ_140624AA0.cpp" />
    <ClCompile Include="Source\LastPutHashVerificationFilterCryptoPPMEAAXPEBE_KZ_1405FD290.cpp" />
    <ClCompile Include="Source\lower_bound_HashV_Hmap_traitsHPEAVCNationCodeStrVh_14020D450.cpp" />
    <ClCompile Include="Source\lower_bound_HashV_Hmap_traitsHPEAVCNationSettingFa_14021CA30.cpp" />
    <ClCompile Include="Source\lower_bound_HashV_Hmap_traitsHPEBU_CashShop_fldVha_140306600.cpp" />
    <ClCompile Include="Source\lower_bound_HashV_Hmap_traitsPEAUScheduleMSGKVhash_140422B30.cpp" />
    <ClCompile Include="Source\MakeHashCCryptorQEAA_NPEBE_KPEAE1Z_14046B5C0.cpp" />
    <ClCompile Include="Source\MaxPlaintextLengthDL_CryptoSystemBaseVPK_Decryptor_140456480.cpp" />
    <ClCompile Include="Source\MaxPlaintextLengthDL_CryptoSystemBaseVPK_Decryptor_140635790.cpp" />
    <ClCompile Include="Source\MaxPlaintextLengthDL_CryptoSystemBaseVPK_Encryptor_1404557B0.cpp" />
    <ClCompile Include="Source\MaxPlaintextLengthDL_CryptoSystemBaseVPK_Encryptor_140635000.cpp" />
    <ClCompile Include="Source\MinLastBlockSizeCBC_CTS_DecryptionCryptoPPUEBAIXZ_14055B8E0.cpp" />
    <ClCompile Include="Source\MinLastBlockSizeCBC_CTS_EncryptionCryptoPPUEBAIXZ_14055B700.cpp" />
    <ClCompile Include="Source\NewHashOAEPVSHA1CryptoPPVP1363_MGF12CryptoPPMEBAPE_14055BE00.cpp" />
    <ClCompile Include="Source\NextPutMultipleHashVerificationFilterCryptoPPMEAAX_1405FD210.cpp" />
    <ClCompile Include="Source\OptimalBlockSizeHashTransformationCryptoPPUEBAIXZ_140562E00.cpp" />
    <ClCompile Include="Source\OptimalBlockSizeIteratedHashBaseIVHashTransformati_14044EC20.cpp" />
    <ClCompile Include="Source\OptimalBlockSizeIteratedHashBaseIVSimpleKeyedTrans_140551980.cpp" />
    <ClCompile Include="Source\OptimalBlockSizeIteratedHashBase_KVHashTransformat_1405516D0.cpp" />
    <ClCompile Include="Source\OptimalBlockSizeIteratedHashBase_KVSimpleKeyedTran_1405517E0.cpp" />
    <ClCompile Include="Source\OptimalDataAlignmentHashTransformationCryptoPPUEBA_1404652E0.cpp" />
    <ClCompile Include="Source\OptimalDataAlignmentIteratedHashBaseIVHashTransfor_14044EC70.cpp" />
    <ClCompile Include="Source\OptimalDataAlignmentIteratedHashBaseIVSimpleKeyedT_1405519A0.cpp" />
    <ClCompile Include="Source\OptimalDataAlignmentIteratedHashBase_KVHashTransfo_1405516F0.cpp" />
    <ClCompile Include="Source\OptimalDataAlignmentIteratedHashBase_KVSimpleKeyed_140551800.cpp" />
    <ClCompile Include="Source\P1363_MGF1KDF2_CommonCryptoPPYAXAEAVHashTransforma_140622290.cpp" />
    <ClCompile Include="Source\PadLastBlockIteratedHashBaseIVHashTransformationCr_140571A90.cpp" />
    <ClCompile Include="Source\PadLastBlockIteratedHashBaseIVSimpleKeyedTransform_140572320.cpp" />
    <ClCompile Include="Source\PadLastBlockIteratedHashBase_KVHashTransformationC_140570950.cpp" />
    <ClCompile Include="Source\PadLastBlockIteratedHashBase_KVSimpleKeyedTransfor_140571200.cpp" />
    <ClCompile Include="Source\ParameterSupportedDL_CryptoSystemBaseVPK_Decryptor_140456650.cpp" />
    <ClCompile Include="Source\ParameterSupportedDL_CryptoSystemBaseVPK_Decryptor_1406358C0.cpp" />
    <ClCompile Include="Source\ParameterSupportedDL_CryptoSystemBaseVPK_Encryptor_140455980.cpp" />
    <ClCompile Include="Source\ParameterSupportedDL_CryptoSystemBaseVPK_Encryptor_140635130.cpp" />
    <ClCompile Include="Source\ParameterSupportedDL_EncryptionAlgorithm_XorVHMACV_140464630.cpp" />
    <ClCompile Include="Source\ParameterSupportedDL_EncryptionAlgorithm_XorVHMACV_14063CD10.cpp" />
    <ClCompile Include="Source\ProcCodeCCheckSumBaseConverterQEAAKEKKZ_1402C13C0.cpp" />
    <ClCompile Include="Source\ProcCodeCCheckSumBaseConverterQEAANEKNZ_1402C1440.cpp" />
    <ClCompile Include="Source\ProcessBlocksCBC_DecryptionCryptoPPUEAAXPEAEPEBE_K_140619550.cpp" />
    <ClCompile Include="Source\ProcessBlocksCBC_EncryptionCryptoPPUEAAXPEAEPEBE_K_140619140.cpp" />
    <ClCompile Include="Source\ProcessLastBlockCBC_CTS_DecryptionCryptoPPUEAAXPEA_140619680.cpp" />
    <ClCompile Include="Source\ProcessLastBlockCBC_CTS_EncryptionCryptoPPUEAAXPEA_140619290.cpp" />
    <ClCompile Include="Source\Put2HashFilterCryptoPPUEAA_KPEBE_KH_NZ_1405FCB80.cpp" />
    <ClCompile Include="Source\Put2PK_DefaultDecryptionFilterCryptoPPUEAA_KPEBE_K_1405F7110.cpp" />
    <ClCompile Include="Source\Put2PK_DefaultEncryptionFilterCryptoPPUEAA_KPEBE_K_1405F6B70.cpp" />
    <ClCompile Include="Source\RefSingletonVDL_EncryptionAlgorithm_XorVHMACVSHA1C_14045FA00.cpp" />
    <ClCompile Include="Source\RefSingletonVDL_EncryptionAlgorithm_XorVHMACVSHA1C_14063AD80.cpp" />
    <ClCompile Include="Source\registCHashMapPtrPoolHVCNationCodeStrQEAAHPEAVCNat_14020B9A0.cpp" />
    <ClCompile Include="Source\registCHashMapPtrPoolHVCNationSettingFactoryQEAAHP_14021ACE0.cpp" />
    <ClCompile Include="Source\ResizeBuffersCBC_DecryptionCryptoPPMEAAXXZ_140452FF0.cpp" />
    <ClCompile Include="Source\RestartHashTransformationCryptoPPUEAAXXZ_140562DD0.cpp" />
    <ClCompile Include="Source\RestartIteratedHashBaseIVHashTransformationCryptoP_1405718B0.cpp" />
    <ClCompile Include="Source\RestartIteratedHashBaseIVSimpleKeyedTransformation_140572140.cpp" />
    <ClCompile Include="Source\RestartIteratedHashBase_KVHashTransformationCrypto_140570760.cpp" />
    <ClCompile Include="Source\RestartIteratedHashBase_KVSimpleKeyedTransformatio_140571010.cpp" />
    <ClCompile Include="Source\Rhash_compareHUlessHstdstdextQEBA_KAEBHZ_14020D970.cpp" />
    <ClCompile Include="Source\Rhash_compareHUlessHstdstdextQEBA_NAEBH0Z_14020DA40.cpp" />
    <ClCompile Include="Source\Rhash_comparePEAUScheduleMSGUlessPEAUScheduleMSGst_140424170.cpp" />
    <ClCompile Include="Source\Rhash_comparePEAUScheduleMSGUlessPEAUScheduleMSGst_140424240.cpp" />
    <ClCompile Include="Source\RNewObjectVDL_EncryptionAlgorithm_XorVHMACVSHA1Cry_140460DE0.cpp" />
    <ClCompile Include="Source\RNewObjectVDL_EncryptionAlgorithm_XorVHMACVSHA1Cry_14063BE90.cpp" />
    <ClCompile Include="Source\SetAESDecryptorCCryptParamIEAAXXZ_140447C10.cpp" />
    <ClCompile Include="Source\SetCipherCipherModeFinalTemplate_ExternalCipherVCB_140588770.cpp" />
    <ClCompile Include="Source\SetCipherCipherModeFinalTemplate_ExternalCipherVCB_140588870.cpp" />
    <ClCompile Include="Source\SetCipherCipherModeFinalTemplate_ExternalCipherVCB_140588970.cpp" />
    <ClCompile Include="Source\SetCipherCipherModeFinalTemplate_ExternalCipherVCB_140588A70.cpp" />
    <ClCompile Include="Source\SetCipherWithIVCipherModeFinalTemplate_ExternalCip_1405887C0.cpp" />
    <ClCompile Include="Source\SetCipherWithIVCipherModeFinalTemplate_ExternalCip_1405888C0.cpp" />
    <ClCompile Include="Source\SetCipherWithIVCipherModeFinalTemplate_ExternalCip_1405889C0.cpp" />
    <ClCompile Include="Source\SetCipherWithIVCipherModeFinalTemplate_ExternalCip_140588AC0.cpp" />
    <ClCompile Include="Source\SetValueCCheckSumCharacAccountTrunkDataIEAAXW4COLU_1402C0E20.cpp" />
    <ClCompile Include="Source\SetValueCCheckSumCharacAccountTrunkDataIEAAXW4COLU_1402C0E60.cpp" />
    <ClCompile Include="Source\SetValueCCheckSumGuildDataIEAAXW4COLUMN_D_TYPE1NZ_1402C1380.cpp" />
    <ClCompile Include="Source\size_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_com_14020D400.cpp" />
    <ClCompile Include="Source\size_HashV_Hmap_traitsHPEAVCNationSettingFactoryVh_14021C9E0.cpp" />
    <ClCompile Include="Source\size_HashV_Hmap_traitsHPEBU_CashShop_fldVhash_comp_1403065B0.cpp" />
    <ClCompile Include="Source\size_HashV_Hmap_traitsPEAUScheduleMSGKVhash_compar_140423FF0.cpp" />
    <ClCompile Include="Source\StateBufIteratedHashWithStaticTransformIUEnumToTyp_14044EF70.cpp" />
    <ClCompile Include="Source\StateBufIteratedHashWithStaticTransformIUEnumToTyp_140463FB0.cpp" />
    <ClCompile Include="Source\StaticAlgorithmNameCipherModeFinalTemplate_CipherH_140459640.cpp" />
    <ClCompile Include="Source\StaticAlgorithmNameCipherModeFinalTemplate_CipherH_14045A460.cpp" />
    <ClCompile Include="Source\StaticAlgorithmNameCipherModeFinalTemplate_CipherH_14061BD50.cpp" />
    <ClCompile Include="Source\StaticAlgorithmNameCipherModeFinalTemplate_CipherH_14061C3D0.cpp" />
    <ClCompile Include="Source\StaticAlgorithmNameCipherModeFinalTemplate_CipherH_14061C940.cpp" />
    <ClCompile Include="Source\SymmetricDecryptDL_EncryptionAlgorithm_XorVHMACVSH_140464AA0.cpp" />
    <ClCompile Include="Source\SymmetricDecryptDL_EncryptionAlgorithm_XorVHMACVSH_14063D040.cpp" />
    <ClCompile Include="Source\SymmetricEncryptDL_EncryptionAlgorithm_XorVHMACVSH_140464750.cpp" />
    <ClCompile Include="Source\SymmetricEncryptDL_EncryptionAlgorithm_XorVHMACVSH_14063CDE0.cpp" />
    <ClCompile Include="Source\ThrowIfInvalidTruncatedSizeHashTransformationCrypt_1405F4420.cpp" />
    <ClCompile Include="Source\TruncatedFinalIteratedHashBaseIVHashTransformation_1405718F0.cpp" />
    <ClCompile Include="Source\TruncatedFinalIteratedHashBaseIVSimpleKeyedTransfo_140572180.cpp" />
    <ClCompile Include="Source\TruncatedFinalIteratedHashBase_KVHashTransformatio_1405707A0.cpp" />
    <ClCompile Include="Source\TruncatedFinalIteratedHashBase_KVSimpleKeyedTransf_140571050.cpp" />
    <ClCompile Include="Source\TruncatedVerifyHashTransformationCryptoPPUEAA_NPEB_1405F4330.cpp" />
    <ClCompile Include="Source\UncheckedSetKeyCBC_CTS_EncryptionCryptoPPMEAAXPEBE_14055B720.cpp" />
    <ClCompile Include="Source\UpdateIteratedHashBaseIVHashTransformationCryptoPP_140571430.cpp" />
    <ClCompile Include="Source\UpdateIteratedHashBaseIVSimpleKeyedTransformationV_140571CC0.cpp" />
    <ClCompile Include="Source\UpdateIteratedHashBase_KVHashTransformationCryptoP_1405701B0.cpp" />
    <ClCompile Include="Source\UpdateIteratedHashBase_KVSimpleKeyedTransformation_140570B80.cpp" />
    <ClCompile Include="Source\VerifyDigestHashTransformationCryptoPPUEAA_NPEBE0__14044DCF0.cpp" />
    <ClCompile Include="Source\VerifyDL_Algorithm_GDSAUEC2NPointCryptoPPCryptoPPU_14055A360.cpp" />
    <ClCompile Include="Source\VerifyDL_Algorithm_GDSAUECPPointCryptoPPCryptoPPUE_140559C00.cpp" />
    <ClCompile Include="Source\VerifyDL_Algorithm_GDSAVIntegerCryptoPPCryptoPPUEB_140552B10.cpp" />
    <ClCompile Include="Source\VerifyDL_Algorithm_NRVIntegerCryptoPPCryptoPPUEBA__14063C650.cpp" />
    <ClCompile Include="Source\VerifyHashCCryptorQEAA_NPEBE_KPEAE1Z_14046B780.cpp" />
    <ClCompile Include="Source\VerifyHashTransformationCryptoPPUEAA_NPEBEZ_14044DED0.cpp" />
    <ClCompile Include="Source\VerifyPointEC2NCryptoPPQEBA_NAEBUEC2NPoint2Z_14062E370.cpp" />
    <ClCompile Include="Source\VerifyPointECPCryptoPPQEBA_NAEBUECPPoint2Z_14060EA20.cpp" />
    <ClCompile Include="Source\VerifyPrimeCryptoPPYA_NAEAVRandomNumberGenerator1A_140642FA0.cpp" />
    <ClCompile Include="Source\VerifyTruncatedDigestHashTransformationCryptoPPUEA_14044DFE0.cpp" />
    <ClCompile Include="Source\_AtlVerifyStackAvailable_ATL_SAFE_ALLOCA_IMPLATLYA_140024B80.cpp" />
    <ClCompile Include="Source\_ATL_ATL_SAFE_ALLOCA_IMPL_AtlVerifyStackAvailable__140024CE0.cpp" />
    <ClCompile Include="Source\_CCryptorMakeHash__1_catch0_14046B6C0.cpp" />
    <ClCompile Include="Source\_CCryptorMakeHash__1_catch1_14046B700.cpp" />
    <ClCompile Include="Source\_CCryptorMakeHash__1_catch2_14046B740.cpp" />
    <ClCompile Include="Source\_CCryptParamCheckPublicKeyHash__1_dtor0_140448110.cpp" />
    <ClCompile Include="Source\_CCryptParamCheckPublicKeyHash__1_dtor1_140448140.cpp" />
    <ClCompile Include="Source\_CCryptParamEncrypt__1_catch0_140447B30.cpp" />
    <ClCompile Include="Source\_CCryptParamEncrypt__1_catch1_140447B70.cpp" />
    <ClCompile Include="Source\_CCryptParamEncrypt__1_catch2_140447BB0.cpp" />
    <ClCompile Include="Source\_CGuildRankingCheckGuildCheckSum__1_dtor0_14033A400.cpp" />
    <ClCompile Include="Source\_CGuildRankingCheckGuildCheckSum__1_dtor1_14033A430.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr_cleanup__1_dto_140209C20.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr_cleanup__1_dto_140209C50.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr_cleanup__1_dto_140209C80.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr_cleanup__1_dto_140209CB0.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr_findkey__1_dto_14020BFB0.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr_findkey__1_dto_14020BFE0.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr_findkey__1_dto_14020C010.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr_findkey__1_dto_14020C040.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr_get__1_dtor0_14020BD50.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr_get__1_dtor1_14020BD80.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr_regist__1_dtor_14020BB40.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr_regist__1_dtor_14020BB70.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationCodeStr__CHashMapPtrPo_140208430.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationSettingFactory_cleanup_14022AB90.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationSettingFactory_cleanup_14022ABC0.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationSettingFactory_cleanup_14022ABF0.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationSettingFactory_cleanup_14022AC20.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationSettingFactory_get__1__14022A970.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationSettingFactory_get__1__14022A9A0.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationSettingFactory_regist__14021AE80.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationSettingFactory_regist__14021AEB0.cpp" />
    <ClCompile Include="Source\_CHashMapPtrPool_int_CNationSettingFactory__CHashM_140229AB0.cpp" />
    <ClCompile Include="Source\_CheckGuildCheckSumCMainThreadAEAA_NKPEADAEAN1Z_1401EDCA0.cpp" />
    <ClCompile Include="Source\_CMainThread_CheckGuildCheckSum__1_dtor0_1401EDFE0.cpp" />
    <ClCompile Include="Source\_CMainThread_CheckGuildCheckSum__1_dtor1_1401EE010.cpp" />
    <ClCompile Include="Source\_CryptoPPCBC_DecryptionCBC_Decryption__1_dtor0_140457710.cpp" />
    <ClCompile Include="Source\_CryptoPPCBC_Decryption_CBC_Decryption__1_dtor0_140448FE0.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeFinalTemplate_CipherHolder_Cryp_140448A10.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeFinalTemplate_CipherHolder_Cryp_14044E570.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeFinalTemplate_CipherHolder_Cryp_140452650.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeFinalTemplate_CipherHolder_Cryp_140452680.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeFinalTemplate_CipherHolder_Cryp_140453410.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeFinalTemplate_CipherHolder_Cryp_140453440.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeFinalTemplate_CipherHolder_Cryp_140459740.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeFinalTemplate_CipherHolder_Cryp_140459770.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeFinalTemplate_CipherHolder_Cryp_1404597A0.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeFinalTemplate_CipherHolder_Cryp_14045A560.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeFinalTemplate_CipherHolder_Cryp_14045A590.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeFinalTemplate_CipherHolder_Cryp_14045A5C0.cpp" />
    <ClCompile Include="Source\_CryptoPPClonableImpl_CryptoPPSHA1_CryptoPPAlgorit_140464080.cpp" />
    <ClCompile Include="Source\_CryptoPPClonableImpl_CryptoPPSHA256_CryptoPPAlgor_14044F040.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_DecryptorBase_CryptoPPECPPoint_Decrypt_140456260.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_DecryptorBase_CryptoPPECPPoint_Decrypt_140456290.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_DecryptorBase_CryptoPPECPPoint_Decrypt_1404562C0.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_DecryptorBase_CryptoPPECPPoint_Decrypt_1404562F0.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_EncryptionAlgorithm_Xor_CryptoPPHMAC_C_1404649A0.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_EncryptionAlgorithm_Xor_CryptoPPHMAC_C_1404649D0.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_EncryptionAlgorithm_Xor_CryptoPPHMAC_C_140464D60.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_EncryptionAlgorithm_Xor_CryptoPPHMAC_C_140464D90.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_EncryptorBase_CryptoPPECPPoint_Encrypt_140455580.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_EncryptorBase_CryptoPPECPPoint_Encrypt_1404555B0.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_EncryptorBase_CryptoPPECPPoint_Encrypt_1404555E0.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_EncryptorBase_CryptoPPECPPoint_Encrypt_140455610.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_EncryptorBase_CryptoPPECPPoint_Encrypt_140455640.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_ObjectImplBase_CryptoPPDL_DecryptorBas_1404494D0.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_ObjectImplBase_CryptoPPDL_DecryptorBas_140457D20.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_ObjectImplBase_CryptoPPDL_EncryptorBas_140449440.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_ObjectImplBase_CryptoPPDL_EncryptorBas_140457C70.cpp" />
    <ClCompile Include="Source\_CryptoPPIteratedHashWithStaticTransform_unsigned__14044E170.cpp" />
    <ClCompile Include="Source\_CryptoPPIteratedHashWithStaticTransform_unsigned__14044EE20.cpp" />
    <ClCompile Include="Source\_CryptoPPIteratedHashWithStaticTransform_unsigned__14044EE50.cpp" />
    <ClCompile Include="Source\_CryptoPPIteratedHashWithStaticTransform_unsigned__140456A40.cpp" />
    <ClCompile Include="Source\_CryptoPPIteratedHashWithStaticTransform_unsigned__140463DC0.cpp" />
    <ClCompile Include="Source\_CryptoPPIteratedHashWithStaticTransform_unsigned__1404642C0.cpp" />
    <ClCompile Include="Source\_CryptoPPIteratedHashWithStaticTransform_unsigned__140464430.cpp" />
    <ClCompile Include="Source\_CryptoPPIteratedHashWithStaticTransform_unsigned__140464460.cpp" />
    <ClCompile Include="Source\_CryptoPPIteratedHash_unsigned_int_CryptoPPEnumToT_14044E2C0.cpp" />
    <ClCompile Include="Source\_CryptoPPIteratedHash_unsigned_int_CryptoPPEnumToT_1404579F0.cpp" />
    <ClCompile Include="Source\_CryptoPPIteratedHash_unsigned_int_CryptoPPEnumToT_140457F90.cpp" />
    <ClCompile Include="Source\_CryptoPPPK_DecryptorPK_Decryptor__1_dtor0_1404584B0.cpp" />
    <ClCompile Include="Source\_CryptoPPPK_Decryptor_PK_Decryptor__1_dtor0_14044A2E0.cpp" />
    <ClCompile Include="Source\_CryptoPPPK_EncryptorPK_Encryptor__1_dtor0_1404583F0.cpp" />
    <ClCompile Include="Source\_CryptoPPPK_Encryptor_PK_Encryptor__1_dtor0_14044A210.cpp" />
    <ClCompile Include="Source\_CryptoPPSimpleKeyedTransformation_CryptoPPHashTra_140465110.cpp" />
    <ClCompile Include="Source\_CryptoPPSingleton_CryptoPPDL_EncryptionAlgorithm__14045FAC0.cpp" />
    <ClCompile Include="Source\_CryptoPPSingleton_CryptoPPDL_EncryptionAlgorithm__14045FAF0.cpp" />
    <ClCompile Include="Source\_CryptoPPSingleton_CryptoPPDL_EncryptionAlgorithm__1406E9780.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_CipherHolderVBlockCipher_140454C40.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_CipherHolderVBlockCipher_14046A390.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_CipherHolderVBlockCipher_14046A480.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_CipherHolderVBlockCipher_14061B500.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_CipherHolderVBlockCipher_14061CF20.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_CipherHolderVBlockCipher_14061CF30.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_CipherHolderVBlockCipher_14061CF40.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_CipherHolderVBlockCipher_14061CFB0.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_CipherHolderVBlockCipher_14061D040.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_ExternalCipherVCBC_CTS_D_14055D260.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_ExternalCipherVCBC_CTS_D_1405AE240.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_ExternalCipherVCBC_CTS_E_1405ADA00.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_ExternalCipherVCBC_Decry_1405ADD20.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_ExternalCipherVCBC_Encry_14055D160.cpp" />
    <ClCompile Include="Source\_ECipherModeFinalTemplate_ExternalCipherVCBC_Encry_1405ADF00.cpp" />
    <ClCompile Include="Source\_EConcretePolicyHolderVEmptyCryptoPPVCFB_Decryptio_14061CFD0.cpp" />
    <ClCompile Include="Source\_EConcretePolicyHolderVEmptyCryptoPPVCFB_Decryptio_14061D030.cpp" />
    <ClCompile Include="Source\_EConcretePolicyHolderVEmptyCryptoPPVCFB_Encryptio_14061CF80.cpp" />
    <ClCompile Include="Source\_EConcretePolicyHolderVEmptyCryptoPPVCFB_Encryptio_14061CFE0.cpp" />
    <ClCompile Include="Source\_EDL_DecryptorImplUDL_CryptoSchemeOptionsUDLIESUEn_140635B30.cpp" />
    <ClCompile Include="Source\_EDL_DecryptorImplUDL_CryptoSchemeOptionsUDLIESUEn_14063DF40.cpp" />
    <ClCompile Include="Source\_EDL_DecryptorImplUDL_CryptoSchemeOptionsUECIESVEC_14046A5B0.cpp" />
    <ClCompile Include="Source\_EDL_EncryptorImplUDL_CryptoSchemeOptionsUDLIESUEn_140635AD0.cpp" />
    <ClCompile Include="Source\_EDL_EncryptorImplUDL_CryptoSchemeOptionsUDLIESUEn_14063DFB0.cpp" />
    <ClCompile Include="Source\_EDL_EncryptorImplUDL_CryptoSchemeOptionsUECIESVEC_14046AC50.cpp" />
    <ClCompile Include="Source\_EHashFilterCryptoPPW7EAAPEAXIZ_140624650.cpp" />
    <ClCompile Include="Source\_EHashVerificationFilterCryptoPPW7EAAPEAXIZ_1406060F0.cpp" />
    <ClCompile Include="Source\_Ehash_mapHPEBU_CashShop_fldVhash_compareHUlessHst_140304870.cpp" />
    <ClCompile Include="Source\_EPK_DefaultDecryptionFilterCryptoPPUEAAPEAXIZ_1405F75F0.cpp" />
    <ClCompile Include="Source\_EPK_DefaultDecryptionFilterCryptoPPW7EAAPEAXIZ_1405F8590.cpp" />
    <ClCompile Include="Source\_EPK_DefaultEncryptionFilterCryptoPPW7EAAPEAXIZ_1405F8580.cpp" />
    <ClCompile Include="Source\_GCipherModeFinalTemplate_CipherHolderVBlockCipher_140456720.cpp" />
    <ClCompile Include="Source\_GCipherModeFinalTemplate_CipherHolderVBlockCipher_14061B580.cpp" />
    <ClCompile Include="Source\_GCipherModeFinalTemplate_CipherHolderVBlockCipher_14061B700.cpp" />
    <ClCompile Include="Source\_GCipherModeFinalTemplate_ExternalCipherVCBC_CTS_E_14055D1E0.cpp" />
    <ClCompile Include="Source\_GCipherModeFinalTemplate_ExternalCipherVCBC_Decry_14055D1A0.cpp" />
    <ClCompile Include="Source\_GConcretePolicyHolderVEmptyCryptoPPVCFB_Decryptio_14061B900.cpp" />
    <ClCompile Include="Source\_GConcretePolicyHolderVEmptyCryptoPPVCFB_Encryptio_14061B8A0.cpp" />
    <ClCompile Include="Source\_GDL_DecryptorImplUDL_CryptoSchemeOptionsUECIESVEC_140457820.cpp" />
    <ClCompile Include="Source\_GDL_EncryptorImplUDL_CryptoSchemeOptionsUECIESVEC_140457760.cpp" />
    <ClCompile Include="Source\_Get_iter_from_vec_HashV_Hmap_traitsHPEAVCNationCo_14020D390.cpp" />
    <ClCompile Include="Source\_Get_iter_from_vec_HashV_Hmap_traitsHPEAVCNationSe_14021C970.cpp" />
    <ClCompile Include="Source\_Get_iter_from_vec_HashV_Hmap_traitsHPEBU_CashShop_140306380.cpp" />
    <ClCompile Include="Source\_Get_iter_from_vec_HashV_Hmap_traitsPEAUScheduleMS_140423F80.cpp" />
    <ClCompile Include="Source\_GHashFilterCryptoPPUEAAPEAXIZ_140623EE0.cpp" />
    <ClCompile Include="Source\_GHashInputTooLongCryptoPPUEAAPEAXIZ_140570680.cpp" />
    <ClCompile Include="Source\_GHashVerificationFailedHashVerificationFilterCryp_1405FF820.cpp" />
    <ClCompile Include="Source\_GHashVerificationFilterCryptoPPUEAAPEAXIZ_1405FF640.cpp" />
    <ClCompile Include="Source\_GIteratedHashBaseIVHashTransformationCryptoPPCryp_14055C010.cpp" />
    <ClCompile Include="Source\_GIteratedHashBaseIVSimpleKeyedTransformationVHash_14055C050.cpp" />
    <ClCompile Include="Source\_GIteratedHashBase_KVHashTransformationCryptoPPCry_14055BF90.cpp" />
    <ClCompile Include="Source\_GIteratedHashBase_KVSimpleKeyedTransformationVHas_14055BFD0.cpp" />
    <ClCompile Include="Source\_GPK_DefaultEncryptionFilterCryptoPPUEAAPEAXIZ_1405F6F10.cpp" />
    <ClCompile Include="Source\_GSimpleKeyedTransformationVHashTransformationCryp_140550BA0.cpp" />
    <ClCompile Include="Source\_Hashval_HashV_Hmap_traitsHPEAVCNationCodeStrVhash_14020D8B0.cpp" />
    <ClCompile Include="Source\_Hashval_HashV_Hmap_traitsHPEAVCNationSettingFacto_14021CE90.cpp" />
    <ClCompile Include="Source\_Hashval_HashV_Hmap_traitsHPEBU_CashShop_fldVhash__140306A60.cpp" />
    <ClCompile Include="Source\_Hashval_HashV_Hmap_traitsPEAUScheduleMSGKVhash_co_140424040.cpp" />
    <ClCompile Include="Source\_Kfn_Hmap_traitsHPEAVCNationCodeStrVhash_compareHU_14020D960.cpp" />
    <ClCompile Include="Source\_Kfn_Hmap_traitsHPEAVCNationSettingFactoryVhash_co_14021CF40.cpp" />
    <ClCompile Include="Source\_Kfn_Hmap_traitsHPEBU_CashShop_fldVhash_compareHUl_140306B10.cpp" />
    <ClCompile Include="Source\_Kfn_Hmap_traitsPEAUScheduleMSGKVhash_comparePEAUS_140424160.cpp" />
    <ClCompile Include="Source\_stdexthash_map_ScheduleMSG_____ptr64_unsigned_lon_1404207C0.cpp" />
    <ClCompile Include="Source\_stdexthash_map_ScheduleMSG_____ptr64_unsigned_lon_1404207F0.cpp" />
    <ClCompile Include="Source\_stdexthash_map_ScheduleMSG_____ptr64_unsigned_lon_140420820.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_1402085A0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_1402086E0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_140208710.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_140208740.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CBB0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CBE0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CC10.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CC40.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CC70.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CCA0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CCD0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CD00.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CD30.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CD60.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CD90.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CDC0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CDF0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020CE30.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020D6F0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020D720.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020D750.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationCodeStr_14020D790.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021BFC0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021BFF0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021C020.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021C050.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021C080.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021C0B0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021C0E0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021C110.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021C140.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021C170.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021C1A0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021C1D0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021C200.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021C240.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021CCD0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021CD00.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021CD30.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_14021CD70.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_140229C90.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_140229DC0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_140229DF0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int_CNationSetting_140229E20.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140304EC0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305A00.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305A30.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305A60.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305A90.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305AC0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305AF0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305B20.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305B50.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305B80.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305BB0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305BE0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305C10.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305C40.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140305C80.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__1403064D0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140306500.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140306530.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__1403068A0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__1403068D0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140306900.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_int__CashShop_fld__140306940.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140420920.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140420AA0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_1404219F0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140421A20.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140421A50.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422540.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422570.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_1404225A0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_1404225D0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422600.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422630.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422660.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422690.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_1404226C0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_1404226F0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422720.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422750.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422780.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_1404227C0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422DD0.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422E00.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422E30.cpp" />
    <ClCompile Include="Source\_stdext_Hash_stdext_Hmap_traits_ScheduleMSG_____pt_140422E70.cpp" />
  </ItemGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>