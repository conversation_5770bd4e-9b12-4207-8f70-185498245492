#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?StartRespawnEvent@CMonsterEventRespawn@@QEAA_NPEAD0@Z
 * Address: 0x1402A6B90

char  CMonsterEventRespawn::StartRespawnEvent(CMonsterEventRespawn *this, char *pszEventCode, char *pwszErrCode)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@12
  char *v6; // rax@17
  int64_t v7; // [sp+0h] [bp-88h]@1
  CMonster *pParent; // [sp+20h] [bp-68h]@17
  bool bRobExp; // [sp+28h] [bp-60h]@17
  bool bRewardExp; // [sp+30h] [bp-58h]@17
  bool bDungeon; // [sp+38h] [bp-50h]@17
  bool bWithoutFail; // [sp+40h] [bp-48h]@17
  bool bApplyRopExpField; // [sp+48h] [bp-40h]@17
  int j; // [sp+50h] [bp-38h]@4
  _event_respawn *pEventRespawn; // [sp+58h] [bp-30h]@7
  int v16; // [sp+60h] [bp-28h]@13
  int k; // [sp+64h] [bp-24h]@13
  _event_respawn::_mon *v18; // [sp+68h] [bp-20h]@15
  int l; // [sp+70h] [bp-18h]@15
  CMonster *v20; // [sp+78h] [bp-10h]@17
  CMonsterEventRespawn *v21; // [sp+90h] [bp+8h]@1
  const char *Str2; // [sp+98h] [bp+10h]@1
  char *Dest; // [sp+A0h] [bp+18h]@1

  Dest = pwszErrCode;
  Str2 = pszEventCode;
  v21 = this;
  v3 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  for ( j = 0; ; ++j )
  {
    if ( j >= v21->m_nLoadEventRespawn )
      return 0;
    pEventRespawn = &v21->m_EventRespawn[j];
    if ( pEventRespawn->bLoad )
    {
      if ( !strcmp_0(pEventRespawn->szScriptName, Str2) )
        break;
    }
  }
  if ( pEventRespawn->bActive )
  {
    if ( Dest )
      sprintf(Dest, "now actived");
    result = 0;
  }
  else
  {
    _event_respawn::_state::init(&pEventRespawn->State);
    v16 = 0;
    for ( k = 0; k < pEventRespawn->wMonSetNum; ++k )
    {
      v18 = &pEventRespawn->MonSet[k];
      for ( l = 0; l < v18->wNum; ++l )
      {
        v6 = v18->pMonsterFld->m_strCode;
        bApplyRopExpField = 0;
        bWithoutFail = 0;
        bDungeon = 0;
        bRewardExp = pEventRespawn->Option.bExpReward;
        bRobExp = pEventRespawn->Option.bExpPenalty;
        pParent = 0i64;
        v20 = CreateRepMonster(pEventRespawn->pMap, 0, pEventRespawn->fPos, v6, 0i64, bRobExp, bRewardExp, 0, 0, 0);
        if ( v20 )
        {
          pEventRespawn->State.MonInfo[v16].pMon = v20;
          pEventRespawn->State.MonInfo[v16].dwSerial = v20->m_dwObjSerial;
          pEventRespawn->State.MonInfo[v16++].pMonFld = v18->pMonsterFld;
          if ( !pEventRespawn->Option.bItemLoot )
            CMonster::DisableStdItemLoot(v20);
          CMonster::LinkEventRespawn(v20, pEventRespawn);
        }
      }
    }
    pEventRespawn->State.nRespawnNum = v16;
    pEventRespawn->State.dwLastUpdateTime = timeGetTime();
    pEventRespawn->bActive = 1;
    result = 1;
  }
  return result;
}
