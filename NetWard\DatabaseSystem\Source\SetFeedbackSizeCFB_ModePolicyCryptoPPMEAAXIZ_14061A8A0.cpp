#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetFeedbackSize@CFB_ModePolicy@CryptoPP@@MEAAXI@Z
 * Address: 0x14061A8A0

void  CryptoPP::CFB_ModePolicy::SetFeedbackSize(CryptoPP::CFB_ModePolicy *this, unsigned int a2)
{
  CryptoPP::InvalidArgument v2; // [sp+20h] [bp-A8h]@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+70h] [bp-58h]@2
  unsigned int8_t v4; // [sp+A0h] [bp-28h]@2
  int64_t v5; // [sp+A8h] [bp-20h]@1
  unsigned int v6; // [sp+B0h] [bp-18h]@5
  CryptoPP::CFB_ModePolicy *v7; // [sp+D0h] [bp+8h]@1
  unsigned int v8; // [sp+D8h] [bp+10h]@1

  v8 = a2;
  v7 = this;
  v5 = -2i64;
  if ( a2 > CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&this->vfptr) )
  {
    memset(&v4, 0, sizeof(v4));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &s,
      "CFB_Mode: invalid feedback size",
      v4);
    CryptoPP::InvalidArgument::InvalidArgument(&v2, &s);
    CxxThrowException_0((int64_t)&v2, (int64_t)&TI3_AVInvalidArgument_CryptoPP__);
  }
  if ( v8 )
    v6 = v8;
  else
    v6 = CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&v7->vfptr);
  v7->m_feedbackSize = v6;
}
