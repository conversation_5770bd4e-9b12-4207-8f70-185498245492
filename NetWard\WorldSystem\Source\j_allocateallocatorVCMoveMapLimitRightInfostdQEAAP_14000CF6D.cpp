#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?allocate@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAPEAVCMoveMapLimitRightInfo@@_K@Z
 * Address: 0x14000CF6D

CMoveMapLimitRightInfo * std::allocator<CMoveMapLimitRightInfo>::allocate(std::allocator<CMoveMapLimitRightInfo> *this, unsigned int64_t _Count)
{
  return std::allocator<CMoveMapLimitRightInfo>::allocate(this, _Count);
}
