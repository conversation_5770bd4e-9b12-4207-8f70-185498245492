#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ReadRecord@CRecordData@@QEAA_NPEADK0@Z
 * Address: 0x140043D60

char  CRecordData::ReadRecord(CRecordData *this, char *szFile, unsigned int dwStructSize, char *pszErrMsg)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v7; // [sp+0h] [bp-68h]@1
  DWORD dwCreationDisposition; // [sp+20h] [bp-48h]@25
  char v9; // [sp+40h] [bp-28h]@11
  void *hFile; // [sp+48h] [bp-20h]@11
  unsigned int v11; // [sp+50h] [bp-18h]@23
  CRecordData *v12; // [sp+70h] [bp+8h]@1
  const char *Str; // [sp+78h] [bp+10h]@1
  unsigned int v14; // [sp+80h] [bp+18h]@1
  char *pszErrMsga; // [sp+88h] [bp+20h]@1

  pszErrMsga = pszErrMsg;
  v14 = dwStructSize;
  Str = szFile;
  v12 = this;
  v4 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( strlen_0(szFile) > 0x80 )
    return 0;
  if ( v12->m_bLoad )
  {
    if ( strcmp_0(v12->m_szFileName, Str) )
      return 0;
  }
  else
  {
    strcpy_0(v12->m_szFileName, Str);
  }
  v9 = 1;
  hFile = CreateFileA(Str, 0x80000000, 1u, 0i64, 3u, 0x80u, 0i64);
  if ( hFile == (void *)-1 )
  {
    v9 = 0;
  }
  else if ( CRecordData::LoadRecordHeader(v12, hFile, pszErrMsga) )
  {
    if ( !CRecordData::LoadRecordData(v12, hFile, pszErrMsga) )
      v9 = 0;
  }
  else
  {
    v9 = 0;
  }
  if ( hFile != (void *)-1 )
    CloseHandle(hFile);
  if ( v9 )
  {
    v11 = CRecordData::FileSize(v12, Str);
    if ( v12->m_dwTotalSize == v11 )
    {
      if ( v14 == v12->m_Header.m_nRecordSize )
      {
        v12->m_bLoad = 1;
        result = 1;
      }
      else
      {
        if ( pszErrMsga )
        {
          dwCreationDisposition = v14;
          sprintf(pszErrMsga, "%s (%d) ü (%d) ٸ", Str, v12->m_Header.m_nRecordSize);
        }
        result = 0;
      }
    }
    else
    {
      if ( pszErrMsga )
      {
        dwCreationDisposition = v11;
        sprintf(pszErrMsga, "%s (%d)  (%d) ٸ", Str, v12->m_dwTotalSize);
      }
      result = 0;
    }
  }
  else
  {
    if ( pszErrMsga )
      sprintf(pszErrMsga, "%s б ", Str);
    result = 0;
  }
  return result;
}
