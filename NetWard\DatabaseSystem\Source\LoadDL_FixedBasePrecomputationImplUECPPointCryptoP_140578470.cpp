#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Load@?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@UEAAXAEBV?$DL_GroupPrecomputation@UECPPoint@CryptoPP@@@2@AEAVBufferedTransformation@2@@Z
 * Address: 0x140578470

void  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::Load(int64_t a1, int64_t a2, struct CryptoPP::BufferedTransformation *a3)
{
  int64_t v3; // rax@3
  int64_t v4; // rax@6
  CryptoPP::ECPPoint *v5; // rax@6
  char v6; // [sp+30h] [bp-148h]@1
  CryptoPP::BERGeneralDecoder v7; // [sp+40h] [bp-138h]@1
  CryptoPP::ECPPoint v8; // [sp+80h] [bp-F8h]@3
  CryptoPP::ECPPoint v9; // [sp+D8h] [bp-A0h]@6
  int64_t v10; // [sp+130h] [bp-48h]@1
  int64_t v11; // [sp+138h] [bp-40h]@1
  int64_t v12; // [sp+140h] [bp-38h]@3
  int64_t v13; // [sp+148h] [bp-30h]@3
  int64_t v14; // [sp+150h] [bp-28h]@6
  CryptoPP::ECPPoint *v15; // [sp+158h] [bp-20h]@6
  CryptoPP::ECPPoint *__that; // [sp+160h] [bp-18h]@6
  int64_t v17; // [sp+180h] [bp+8h]@1
  int64_t v18; // [sp+188h] [bp+10h]@1

  v18 = a2;
  v17 = a1;
  v10 = -2i64;
  CryptoPP::BERSequenceDecoder::BERSequenceDecoder((CryptoPP::BERSequenceDecoder *)&v7, a3, 0x30u);
  CryptoPP::BERDecodeUnsigned<unsigned int>((CryptoPP *)&v7, (int *)&v6, 2u, 1u, 1u);
  v11 = *(uint64_t*)(v17 + 104);
  (*(void ( **)(signed int64_t, CryptoPP::BERGeneralDecoder *))(v11 + 8))(v17 + 104, &v7);
  *(uint32_t*)(v17 + 96) = CryptoPP::Integer::BitCount((CryptoPP::Integer *)(v17 + 104)) - 1;
  std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::clear((std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(v17 + 144));
  while ( !CryptoPP::BERGeneralDecoder::EndReached(&v7) )
  {
    LODWORD(v3) = (*(int ( **)(int64_t, CryptoPP::ECPPoint *, CryptoPP::BERGeneralDecoder *))(*(uint64_t*)v18 + 32i64))(
                    v18,
                    &v8,
                    &v7);
    v12 = v3;
    v13 = v3;
    std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::push_back(v17 + 144, v3);
    CryptoPP::ECPPoint::~ECPPoint(&v8);
  }
  if ( !std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::empty((std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(v17 + 144))
    && (unsigned int8_t)(**(int ( ***)(_QWORD))v18)(v18) )
  {
    LODWORD(v4) = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator[](v17 + 144, 0i64);
    v14 = *(uint64_t*)v18;
    LODWORD(v5) = (*(int ( **)(int64_t, CryptoPP::ECPPoint *, int64_t))(v14 + 16))(v18, &v9, v4);
    v15 = v5;
    __that = v5;
    CryptoPP::ECPPoint::operator=((CryptoPP::ECPPoint *)(v17 + 8), v5);
    CryptoPP::ECPPoint::~ECPPoint(&v9);
  }
  CryptoPP::BERGeneralDecoder::MessageEnd(&v7);
  CryptoPP::BERSequenceDecoder::~BERSequenceDecoder((CryptoPP::BERSequenceDecoder *)&v7);
}
