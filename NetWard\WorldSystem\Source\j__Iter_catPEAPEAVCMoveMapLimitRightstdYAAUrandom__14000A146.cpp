#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Iter_cat@PEAPEAVCMoveMapLimitRight@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCMoveMapLimitRight@@@Z
 * Address: 0x14000A146

std::random_access_iterator_tag  std::_Iter_cat<CMoveMapLimitRight * *>(CMoveMapLimitRight **const *__formal)
{
  return std::_Iter_cat<CMoveMapLimitRight * *>(__formal);
}
