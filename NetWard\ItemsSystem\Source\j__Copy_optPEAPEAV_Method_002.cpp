#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Copy_opt@PEAPEAVCUnmannedTraderSortType@@PEAPEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAPEAVCUnmannedTraderSortType@@PEAPEAV1@00Urandom_access_iterator_tag@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140003B16

CUnmannedTraderSortType ** std::_Copy_opt<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *,std::random_access_iterator_tag>(CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last, CUnmannedTraderSortType **_Dest, std::random_access_iterator_tag __formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Copy_opt<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *,std::random_access_iterator_tag>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
