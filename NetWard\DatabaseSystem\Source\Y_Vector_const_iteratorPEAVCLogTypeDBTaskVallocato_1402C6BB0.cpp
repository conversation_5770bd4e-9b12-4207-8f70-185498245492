#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??Y?$_Vector_const_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAAAEAV01@_J@Z
 * Address: 0x1402C6BB0

std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > * std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator+=(std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, int64_t _Off)
{
  this->_Myptr += _Off;
  return this;
}
