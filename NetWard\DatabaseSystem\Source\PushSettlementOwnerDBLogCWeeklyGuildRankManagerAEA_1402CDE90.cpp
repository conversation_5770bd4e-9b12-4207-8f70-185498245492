#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?PushSettlementOwnerDBLog@CWeeklyGuildRankManager@@AEAAXPEAD@Z
 * Address: 0x1402CDE90

void  CWeeklyGuildRankManager::PushSettlementOwnerDBLog(CWeeklyGuildRankManager *this, char *pInfo)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  CLogTypeDBTaskManager *v4; // rax@9
  CPvpUserAndGuildRankingSystem *v5; // rax@9
  int64_t v6; // [sp+0h] [bp-1B8h]@1
  char *v7; // [sp+20h] [bp-198h]@4
  int Dst; // [sp+40h] [bp-178h]@4
  char v9[17]; // [sp+44h] [bp-174h]@8
  char v10; // [sp+55h] [bp-163h]@8
  int16_t v11; // [sp+56h] [bp-162h]@8
  char v12[8]; // [sp+58h] [bp-160h]@8
  int64_t v13; // [sp+60h] [bp-158h]@8
  int64_t v14; // [sp+68h] [bp-150h]@8
  int v15[73]; // [sp+70h] [bp-148h]@8
  int j; // [sp+194h] [bp-24h]@4
  unsigned int64_t v17; // [sp+1A0h] [bp-18h]@4

  v2 = &v6;
  for ( i = 108i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v17 = (unsigned int64_t)&v6 ^ _security_cookie;
  v7 = pInfo;
  memset_0(&Dst, 0, 0x150ui64);
  for ( j = 0; j < 6; ++j )
  {
    if ( *(uint32_t*)&v7[56 * j + 16] )
    {
      *(&Dst + 14 * j) = *(uint32_t*)&v7[56 * j + 16];
      strcpy_s(&v9[56 * j], 0x11ui64, &v7[56 * j + 20]);
      *(&v10 + 56 * j) = v7[56 * j + 37];
      *(&v11 + 28 * j) = *(uint16_t*)&v7[56 * j + 38];
      v12[56 * j] = v7[56 * j + 40];
      *(&v13 + 7 * j) = *(uint64_t*)&v7[56 * j + 48];
      *(&v14 + 7 * j) = *(uint64_t*)&v7[56 * j + 56];
      v15[14 * j] = *(uint32_t*)&v7[56 * j + 64];
    }
  }
  v4 = CLogTypeDBTaskManager::Instance();
  CLogTypeDBTaskManager::Push(v4, 0, (char *)&Dst, 0x150u);
  v5 = CPvpUserAndGuildRankingSystem::Instance();
  CPvpUserAndGuildRankingSystem::Log(v5, "CWeeklyGuildRankManager::PushSettlementOwnerDBLog()!");
}
