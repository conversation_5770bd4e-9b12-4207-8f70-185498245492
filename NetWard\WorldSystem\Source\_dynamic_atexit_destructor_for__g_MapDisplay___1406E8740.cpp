#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _dynamic_atexit_destructor_for__g_MapDisplay__
 * Address: 0x1406E8740

void  dynamic_atexit_destructor_for__g_MapDisplay__()
{
  int64_t *v0; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v2; // [sp+0h] [bp-28h]@1

  v0 = &v2;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v0 = -858993460;
    v0 = (int64_t *)((char *)v0 + 4);
  }
  CMapDisplay::~CMapDisplay(&g_MapDisplay);
}
