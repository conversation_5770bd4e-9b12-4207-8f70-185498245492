#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint_::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__::_1_::dtor$0
 * Address: 0x14044BEF0

void  CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint_::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__::_1_::dtor_0(int64_t a1, int64_t a2)
{
  CryptoPP::ECPPoint::~ECPPoint((CryptoPP::ECPPoint *)(*(uint64_t*)(a2 + 64) + 8i64));
}
