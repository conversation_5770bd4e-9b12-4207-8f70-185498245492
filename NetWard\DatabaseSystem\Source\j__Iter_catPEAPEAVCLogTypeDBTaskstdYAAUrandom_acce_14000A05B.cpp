#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Iter_cat@PEAPEAVCLogTypeDBTask@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCLogTypeDBTask@@@Z
 * Address: 0x14000A05B

std::random_access_iterator_tag  std::_Iter_cat<CLogTypeDBTask * *>(CLogTypeDBTask **const *__formal)
{
  return std::_Iter_cat<CLogTypeDBTask * *>(__formal);
}
