#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Umove@PEAPEAVCMoveMapLimitInfo@@@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitInfo@@PEAPEAV2@00@Z
 * Address: 0x1403AAF70

CMoveMapLimitInfo ** std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Umove<CMoveMapLimitInfo * *>(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo **_Ptr)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  return stdext::_Unchecked_uninitialized_move<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *,std::allocator<CMoveMapLimitInfo *>>(
           _First,
           _Last,
           _Ptr,
           &v8->_Alval);
}
