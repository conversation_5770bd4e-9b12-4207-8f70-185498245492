#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::find_std::_Vector_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____CMoveMapLimitRight_____ptr64__::_1_::dtor$2
 * Address: 0x1403B1AD0

void  std::find_std::_Vector_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____CMoveMapLimitRight_____ptr64__::_1_::dtor_2(int64_t a1, int64_t a2)
{
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(*(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 56));
}
