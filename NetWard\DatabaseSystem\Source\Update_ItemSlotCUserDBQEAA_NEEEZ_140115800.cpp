#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_ItemSlot@CUserDB@@QEAA_NEEE@Z
 * Address: 0x140115800

char  CUserDB::Update_ItemSlot(CUserDB *this, char storage, char slot, char clientpos)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-68h]@1
  int v8; // [sp+20h] [bp-48h]@20
  char v9; // [sp+30h] [bp-38h]@4
  _INVENKEY *v10; // [sp+38h] [bp-30h]@22
  _EMBELLKEY *v11; // [sp+40h] [bp-28h]@26
  _INVENKEY *v12; // [sp+48h] [bp-20h]@30
  _INVENKEY *v13; // [sp+50h] [bp-18h]@34
  _INVENKEY *v14; // [sp+58h] [bp-10h]@38
  CUserDB *v15; // [sp+70h] [bp+8h]@1
  char v16; // [sp+80h] [bp+18h]@1
  char v17; // [sp+88h] [bp+20h]@1

  v17 = clientpos;
  v16 = slot;
  v15 = this;
  v4 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v9 = 0;
  if ( storage || (signed int)(unsigned int8_t)slot < 100 )
  {
    if ( storage != 2 || (signed int)(unsigned int8_t)slot < 7 )
    {
      if ( storage != 5 || (signed int)(unsigned int8_t)slot < 100 )
      {
        if ( storage != 6 || (signed int)(unsigned int8_t)slot < 40 )
        {
          if ( storage == 7 && (signed int)(unsigned int8_t)slot >= 40 )
            v9 = 1;
        }
        else
        {
          v9 = 1;
        }
      }
      else
      {
        v9 = 1;
      }
    }
    else
    {
      v9 = 1;
    }
  }
  else
  {
    v9 = 1;
  }
  if ( v9 )
  {
    v8 = (unsigned int8_t)slot;
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_ItemSlot(CODE) : scode : %d, icode : %d  ",
      v15->m_aszAvatorName,
      (unsigned int8_t)storage);
    return 0;
  }
  if ( storage )
  {
    switch ( storage )
    {
      case 2:
        v11 = (_EMBELLKEY *)((char *)&v15->m_AvatorData.dbEquip + 27 * (unsigned int8_t)slot);
        if ( !_EMBELLKEY::IsFilled(v11) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_ItemSlot(EMBELL, Idx:%d)",
            v15->m_aszAvatorName,
            (unsigned int8_t)v16);
          return 0;
        }
        v11->bySlotIndex = v17;
        break;
      case 5:
        v12 = &v15->m_AvatorData.dbTrunk.m_List[(unsigned int8_t)slot].Key;
        if ( !_INVENKEY::IsFilled(v12) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_ItemSlot(TRUNK, Idx:%d)",
            v15->m_aszAvatorName,
            (unsigned int8_t)v16);
          return 0;
        }
        v12->bySlotIndex = v17;
        break;
      case 6:
        v13 = (_INVENKEY *)&v15->m_AvatorData.dbPersonalAmineInven.m_List[(unsigned int8_t)slot];
        if ( !_INVENKEY::IsFilled(v13) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_ItemSlot(TRUNK, Idx:%d)",
            v15->m_aszAvatorName,
            (unsigned int8_t)v16);
          return 0;
        }
        v13->bySlotIndex = v17;
        break;
      default:
        if ( storage != 7 )
          return 0;
        v14 = &v15->m_AvatorData.dbTrunk.m_ExtList[(unsigned int8_t)slot].Key;
        if ( !_INVENKEY::IsFilled(v14) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_ItemSlot(EXT_TRUNK, Idx:%d)",
            v15->m_aszAvatorName,
            (unsigned int8_t)v16);
          return 0;
        }
        v14->bySlotIndex = v17;
        break;
    }
  }
  else
  {
    v10 = (_INVENKEY *)((char *)&v15->m_AvatorData.dbInven + 37 * (unsigned int8_t)slot);
    if ( !_INVENKEY::IsFilled(v10) )
    {
      CLogFile::Write(&stru_1799C8E78, "%s:Update_ItemSlot(INVEN, Idx:%d)", v15->m_aszAvatorName, (unsigned int8_t)v16);
      return 0;
    }
    v10->bySlotIndex = v17;
  }
  return 1;
}
