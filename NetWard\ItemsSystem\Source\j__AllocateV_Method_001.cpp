#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Allocate@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@YAPEAVCUnmannedTraderGroupDivisionVersionInfo@@_KPEAV1@@Z
 * Address: 0x140011E46

CUnmannedTraderGroupDivisionVersionInfo * std::_Allocate<CUnmannedTraderGroupDivisionVersionInfo>(unsigned int64_t _Count, CUnmannedTraderGroupDivisionVersionInfo *__formal)
{
  return std::_Allocate<CUnmannedTraderGroupDivisionVersionInfo>(_Count, __formal);
}
