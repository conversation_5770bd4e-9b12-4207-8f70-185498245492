#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Destroy_range@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@YAXPEAVCMoveMapLimitRightInfo@@0AEAV?$allocator@VCMoveMapLimitRightInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z
 * Address: 0x1403A3780

void  std::_Destroy_range<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, std::allocator<CMoveMapLimitRightInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfo *_Ptr; // [sp+30h] [bp+8h]@1
  CMoveMapLimitRightInfo *v8; // [sp+38h] [bp+10h]@1
  std::allocator<CMoveMapLimitRightInfo> *v9; // [sp+40h] [bp+18h]@1

  v9 = _Al;
  v8 = _Last;
  _Ptr = _First;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  while ( _Ptr != v8 )
  {
    std::allocator<CMoveMapLimitRightInfo>::destroy(v9, _Ptr);
    ++_Ptr;
  }
}
