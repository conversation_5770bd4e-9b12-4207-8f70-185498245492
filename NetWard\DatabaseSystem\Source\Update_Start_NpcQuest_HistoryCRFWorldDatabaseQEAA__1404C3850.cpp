#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_Start_NpcQuest_History@CRFWorldDatabase@@QEAA_NKPEADE0_J@Z
 * Address: 0x1404C3850

bool  CRFWorldDatabase::Update_Start_NpcQuest_History(CRFWorldDatabase *this, unsigned int dwSerial, char *szQuestCode, char byLevel, char *szTime, int64_t nEndTime)
{
  int64_t *v6; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v9; // [sp+0h] [bp-178h]@1
  char *v10; // [sp+20h] [bp-158h]@4
  int v11; // [sp+28h] [bp-150h]@4
  char *v12; // [sp+30h] [bp-148h]@4
  int64_t v13; // [sp+38h] [bp-140h]@4
  char DstBuf; // [sp+50h] [bp-128h]@4
  unsigned int64_t v15; // [sp+160h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+180h] [bp+8h]@1

  v16 = this;
  v6 = &v9;
  for ( i = 92i64; i; --i )
  {
    *(uint32_t*)v6 = -858993460;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  v15 = (unsigned int64_t)&v9 ^ _security_cookie;
  v13 = nEndTime;
  v12 = szTime;
  v11 = (unsigned int8_t)byLevel;
  v10 = szQuestCode;
  sprintf_s(&DstBuf, 0x100ui64, "{ CALL pUpdate_Start_Npc_Quest_History_Type1( %d, '%s', %d, '%s', %I64d) }", dwSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v16->vfptr, &DstBuf, 1);
}
