#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ReadRecord@COreCuttingTable@@QEAA_NPEADPEAVCRecordData@@10@Z
 * Address: 0x140204360

bool  COreCuttingTable::ReadRecord(COreCuttingTable *this, char *szFile, CRecordData *pOreRec, CRecordData *pResRec, char *pszErrMsg)
{
  int64_t *v5; // rdi@1
  signed int64_t i; // rcx@1
  bool result; // al@5
  int64_t v8; // [sp+0h] [bp-58h]@1
  int __n[2]; // [sp+20h] [bp-38h]@10
  void *v10; // [sp+28h] [bp-30h]@13
  void *__t; // [sp+30h] [bp-28h]@10
  int64_t v12; // [sp+38h] [bp-20h]@4
  void *v13; // [sp+40h] [bp-18h]@11
  COreCuttingTable *v14; // [sp+60h] [bp+8h]@1
  CRecordData *pOreReca; // [sp+70h] [bp+18h]@1
  CRecordData *pResReca; // [sp+78h] [bp+20h]@1

  pResReca = pResRec;
  pOreReca = pOreRec;
  v14 = this;
  v5 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(uint32_t*)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  v12 = -2i64;
  if ( CRecordData::ReadRecord(&v14->m_tblOreCutting, szFile, 0x88u, pszErrMsg) )
  {
    v14->m_nOreNum = CRecordData::GetRecordNum(pOreReca);
    if ( v14->m_nOreNum > 0 )
    {
      *(uint64_t*)__n = v14->m_nOreNum;
      __t = operator new[](saturated_mul(0x4B8ui64, *(unsigned int64_t *)__n));
      if ( __t )
      {
        `vector constructor iterator'(
          __t,
          0x4B8ui64,
          __n[0],
          (void *( *)(void *))COreCuttingTable::_ore_cut_list::_ore_cut_list);
        v13 = __t;
      }
      else
      {
        v13 = 0i64;
      }
      v10 = v13;
      v14->pOreList = (COreCuttingTable::_ore_cut_list *)v13;
      result = COreCuttingTable::Indexing(v14, pOreReca, pResReca, pszErrMsg);
    }
    else
    {
      if ( pszErrMsg )
        sprintf(pszErrMsg, "COreCuttingTable..  record num  <= 0");
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
