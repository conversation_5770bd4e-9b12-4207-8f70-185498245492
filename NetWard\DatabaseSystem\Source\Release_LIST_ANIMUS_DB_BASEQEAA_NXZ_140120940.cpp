#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Release@_LIST@_ANIMUS_DB_BASE@@QEAA_NXZ
 * Address: 0x140120940

char  _ANIMUS_DB_BASE::_LIST::Release(_ANIMUS_DB_BASE::_LIST *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v4; // [sp+0h] [bp-28h]@1
  _ANIMUS_DB_BASE::_LIST *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( _ANIMUSKEY::IsFilled(&v5->Key) )
  {
    _ANIMUSKEY::SetRelease(&v5->Key);
    v5->lnUID = 0i64;
    v5->dwT = -1;
    v5->byCsMethod = 0;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
