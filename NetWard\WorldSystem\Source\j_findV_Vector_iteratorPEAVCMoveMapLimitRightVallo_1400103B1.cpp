#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$find@V?$_Vector_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@PEAVCMoveMapLimitRight@@@std@@YA?AV?$_Vector_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@0@V10@0AEBQEAVCMoveMapLimitRight@@@Z
 * Address: 0x1400103B1

std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > * std::find<std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>,CMoveMapLimitRight *>(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *result, std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_First, std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Last, CMoveMapLimitRight *const *_Val)
{
  return std::find<std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>,CMoveMapLimitRight *>(
           result,
           _First,
           _Last,
           _Val);
}
