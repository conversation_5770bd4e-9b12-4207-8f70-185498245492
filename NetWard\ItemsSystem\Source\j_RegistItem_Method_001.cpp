#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?RegistItem@CUnmannedTraderRegistItemInfo@@QEAAXKGKKEEGE_KK_N@Z
 * Address: 0x14000E1A6

void  CUnmannedTraderRegistItemInfo::RegistItem(CUnmannedTraderRegistItemInfo *this, unsigned int dwRegistSerial, unsigned int16_t wItemSerial, unsigned int dwETSerialNumber, unsigned int dwPrice, char bySellTurm, char byTableCode, unsigned int16_t wItemIndex, char byStorageIndex, unsigned int64_t dwD, unsigned int dwU, bool bInserted)
{
  CUnmannedTraderRegistItemInfo::RegistItem(
    this,
    dwRegistSerial,
    wItemSerial,
    dwETSerialNumber,
    dwPrice,
    bySellTurm,
    byTableCode,
    wItemIndex,
    byStorageIndex,
    dwD,
    dwU,
    bInserted);
}
