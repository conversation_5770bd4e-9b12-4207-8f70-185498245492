#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_PostRegistryDisable@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404B1F20

bool  CRFWorldDatabase::Update_PostRegistryDisable(CRFWorldDatabase *this, unsigned int dwIndex)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-98h]@1
  char DstBuf; // [sp+30h] [bp-68h]@4
  char v7; // [sp+31h] [bp-67h]@4
  unsigned int64_t v8; // [sp+80h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+A0h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v8 = (unsigned int64_t)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v7, 0, 0x3Fui64);
  sprintf_s(&DstBuf, 0x40ui64, "update tbl_PostRegistry set dck=1 where serial=%d", dwIndex);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 1);
}
