#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Fill_n@PEAPEAVCUnmannedTraderSortType@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVCUnmannedTraderSortType@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400056D7

void  std::_Fill_n<CUnmannedTraderSortType * *,unsigned int64_t,CUnmannedTraderSortType *,std::random_access_iterator_tag>(CUnmannedTraderSortType **_First, unsigned int64_t _Count, CUnmannedTraderSortType *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  std::_Fill_n<CUnmannedTraderSortType * *,unsigned int64_t,CUnmannedTraderSortType *,std::random_access_iterator_tag>(
    _First,
    _Count,
    _Val,
    __formal,
    a5);
}
