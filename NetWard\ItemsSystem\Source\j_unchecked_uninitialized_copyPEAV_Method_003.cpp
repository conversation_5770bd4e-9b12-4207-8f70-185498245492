#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_uninitialized_copy@PEAVCUnmannedTraderRegistItemInfo@@PEAV1@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@stdext@@YAPEAVCUnmannedTraderRegistItemInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@Z
 * Address: 0x140009C5F

CUnmannedTraderRegistItemInfo * stdext::unchecked_uninitialized_copy<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::allocator<CUnmannedTraderRegistItemInfo>>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Dest, std::allocator<CUnmannedTraderRegistItemInfo> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::allocator<CUnmannedTraderRegistItemInfo>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
