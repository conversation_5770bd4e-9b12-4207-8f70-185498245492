#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Insert@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@2@@std@@@?$list@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@2@@std@@QEAAXV?$_Iterator@$0A@@01@00Uforward_iterator_tag@1@@Z
 * Address: 0x1400110CC

void  std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Insert<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>>(std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *this, std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *_Where, std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *_First, std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *_Last, std::forward_iterator_tag __formal)
{
  std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Insert<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>>(
    this,
    _Where,
    _First,
    _Last,
    __formal);
}
