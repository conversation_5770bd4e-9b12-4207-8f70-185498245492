#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$upvalue_@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@YAP6APEAVCMonster@@PEAD0MMM@ZPEAUlua_State@@@Z
 * Address: 0x14040A3A0

CMonster *( * lua_tinker::upvalue_<CMonster * (*)(char *,char *,float,float,float)>(struct lua_State *L, int64_t a2, int a3))(char *, char *, float, float, float)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-28h]@1
  lua_tinker::user2type<CMonster * (*)(char *,char *,float,float,float)> *v7; // [sp+30h] [bp+8h]@1

  v7 = (lua_tinker::user2type<CMonster * (*)(char *,char *,float,float,float)> *)L;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  return lua_tinker::user2type<CMonster * (*)(char *,char *,float,float,float)>::invoke(
           v7,
           (struct lua_State *)0xFFFFD8ED,
           a3);
}
