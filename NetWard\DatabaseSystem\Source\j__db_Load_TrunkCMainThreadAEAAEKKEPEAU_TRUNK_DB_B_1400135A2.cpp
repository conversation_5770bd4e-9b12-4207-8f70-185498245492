#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_db_Load_Trunk@CMainThread@@AEAAEKKEPEAU_TRUNK_DB_BASE@@@Z
 * Address: 0x1400135A2

char  CMainThread::_db_Load_Trunk(CMainThread *this, unsigned int dwSerial, unsigned int dwAccountSerial, char byRace, _TRUNK_DB_BASE *pTrunk)
{
  return CMainThread::_db_Load_Trunk(this, dwSerial, dwAccountSerial, byRace, pTrunk);
}
