#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?check_dummy@AutominePersonalMgr@@QEAA_NPEAVCMapData@@EPEAM@Z
 * Address: 0x140012DF0

bool  AutominePersonalMgr::check_dummy(AutominePersonalMgr *this, CMapData *pMap, char byCurDummyIndex, float *pfCurPos)
{
  return AutominePersonalMgr::check_dummy(this, pMap, byCurDummyIndex, pfCurPos);
}
