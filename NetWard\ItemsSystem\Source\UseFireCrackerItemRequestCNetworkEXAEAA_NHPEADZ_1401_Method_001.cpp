#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?UseFireCrackerItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB4A0

char  CNetworkEX::UseFireCrackerItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-D8h]@1
  unsigned int16_t *v7; // [sp+30h] [bp-A8h]@4
  CPlayer *v8; // [sp+38h] [bp-A0h]@4
  int v9; // [sp+40h] [bp-98h]@6
  char szMsg; // [sp+54h] [bp-84h]@9
  unsigned int16_t v11; // [sp+55h] [bp-83h]@9
  char pbyType; // [sp+74h] [bp-64h]@9
  char v13; // [sp+75h] [bp-63h]@9
  char v14[4]; // [sp+94h] [bp-44h]@10
  int16_t v15; // [sp+98h] [bp-40h]@10
  char v16; // [sp+B4h] [bp-24h]@10
  char v17; // [sp+B5h] [bp-23h]@10
  int v18; // [sp+C4h] [bp-14h]@7

  v3 = &v6;
  for ( i = 52i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v7 = (unsigned int16_t *)pBuf;
  v8 = &g_Player + n;
  if ( v8->m_bOper )
  {
    v9 = CPlayer::pc_UseFireCracker(v8, *v7);
    if ( v9 < 0 )
      v18 = v9;
    else
      v18 = 0;
    szMsg = v18;
    v11 = *v7;
    pbyType = 7;
    v13 = 38;
    CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, &szMsg, 3u);
    if ( v9 >= 0 )
    {
      *(uint32_t*)v14 = v8->m_dwObjSerial;
      v15 = v9;
      v16 = 7;
      v17 = 39;
      CGameObject::CircleReport((CGameObject *)&v8->vfptr, &v16, v14, 6, 0);
    }
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
