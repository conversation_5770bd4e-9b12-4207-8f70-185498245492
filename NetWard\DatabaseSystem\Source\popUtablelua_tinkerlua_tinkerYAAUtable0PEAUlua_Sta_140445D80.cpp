#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$pop@Utable@lua_tinker@@@lua_tinker@@YA?AUtable@0@PEAUlua_State@@@Z
 * Address: 0x140445D80

lua_tinker::table * lua_tinker::pop<lua_tinker::table>(lua_tinker::table *result, struct lua_State *L)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int v4; // eax@4
  int64_t v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  lua_tinker::table *v8; // [sp+40h] [bp+8h]@1
  struct lua_State *La; // [sp+48h] [bp+10h]@1

  La = L;
  v8 = result;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v7 = 0;
  v4 = lua_gettop(L);
  lua_tinker::table::table(v8, La, v4);
  return v8;
}
