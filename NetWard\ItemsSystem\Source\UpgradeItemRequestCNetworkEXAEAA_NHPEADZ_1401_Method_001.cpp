#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?UpgradeItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CAA20

char  CNetworkEX::UpgradeItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  char *v7; // rax@11
  char *v8; // rax@15
  char *v9; // rax@18
  char *v10; // rax@21
  int64_t v11; // [sp+0h] [bp-58h]@1
  char byJewelNum; // [sp+20h] [bp-38h]@22
  _STORAGE_POS_INDIV *pposUpgJewel; // [sp+28h] [bp-30h]@22
  _STORAGE_POS_INDIV *pposUpgItem; // [sp+30h] [bp-28h]@4
  CPlayer *v15; // [sp+38h] [bp-20h]@4
  int j; // [sp+40h] [bp-18h]@12
  CNetworkEX *v17; // [sp+60h] [bp+8h]@1

  v17 = this;
  v3 = &v11;
  for ( i = 20i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  pposUpgItem = (_STORAGE_POS_INDIV *)pBuf;
  v15 = &g_Player + n;
  if ( !v15->m_bOper || v15->m_pmTrd.bDTradeMode || v15->m_bCorpse )
  {
    result = 1;
  }
  else if ( pposUpgItem[3].byStorageCode <= 4 )
  {
    if ( pposUpgItem[1].byStorageCode )
    {
      v7 = CPlayerDB::GetCharNameA(&v15->m_Param);
      CLogFile::Write(
        &v17->m_LogFile,
        "odd.. %s: UpgradeItemRequest()..  if(pRecv->m_posTalik.byStorageCode != _STORAGE_POS::INVEN)",
        v7);
      result = 0;
    }
    else
    {
      for ( j = 0; j < pposUpgItem[3].byStorageCode; ++j )
      {
        if ( LOBYTE(pposUpgItem[j + 3].wItemSerial) )
        {
          v8 = CPlayerDB::GetCharNameA(&v15->m_Param);
          CLogFile::Write(
            &v17->m_LogFile,
            "odd.. %s: UpgradeItemRequest()..  if(pRecv->m_posUpgJewel[i].byStorageCode != _STORAGE_POS::INVEN)",
            v8);
          return 0;
        }
      }
      if ( pposUpgItem[2].byStorageCode )
      {
        v9 = CPlayerDB::GetCharNameA(&v15->m_Param);
        CLogFile::Write(
          &v17->m_LogFile,
          "odd.. %s: UpgradeItemRequest()..  if(pRecv->m_posToolItem.byStorageCode != _STORAGE_POS::INVEN)",
          v9);
        result = 0;
      }
      else if ( pposUpgItem->byStorageCode && pposUpgItem->byStorageCode != 1 )
      {
        v10 = CPlayerDB::GetCharNameA(&v15->m_Param);
        CLogFile::Write(
          &v17->m_LogFile,
          "odd.. %s: UpgradeItemRequest()..  if(pRecv->m_posToolItem.byStorageCode != _STORAGE_POS::INVEN)",
          v10);
        result = 0;
      }
      else
      {
        pposUpgJewel = (_STORAGE_POS_INDIV *)((char *)pposUpgItem + 13);
        byJewelNum = pposUpgItem[3].byStorageCode;
        CPlayer::pc_UpgradeItem(
          v15,
          pposUpgItem + 1,
          pposUpgItem + 2,
          pposUpgItem,
          byJewelNum,
          (_STORAGE_POS_INDIV *)((char *)pposUpgItem + 13));
        result = 1;
      }
    }
  }
  else
  {
    v6 = CPlayerDB::GetCharNameA(&v15->m_Param);
    CLogFile::Write(&v17->m_LogFile, "odd.. %s: UpgradeItemRequest()..  if(pRecv->byJewelNum > upgrade_jewel_num)", v6);
    result = 0;
  }
  return result;
}
