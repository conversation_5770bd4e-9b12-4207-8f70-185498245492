#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CMoveMapLimitInfoPortal::_CMoveMapLimitInfoPortal_::_1_::dtor$1
 * Address: 0x1403A40F0

void  CMoveMapLimitInfoPortal::_CMoveMapLimitInfoPortal_::_1_::dtor_1(int64_t a1, int64_t a2)
{
  std::vector<char *,std::allocator<char *>>::~vector<char *,std::allocator<char *>>((std::vector<char *,std::allocator<char *> > *)(*(uint64_t*)(a2 + 80) + 56i64));
}
