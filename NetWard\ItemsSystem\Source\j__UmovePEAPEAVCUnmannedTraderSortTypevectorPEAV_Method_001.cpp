#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Umove@PEAPEAVCUnmannedTraderSortType@@@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderSortType@@PEAPEAV2@00@Z
 * Address: 0x14000EF8E

CUnmannedTraderSortType ** std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Umove<CUnmannedTraderSortType * *>(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last, CUnmannedTraderSortType **_Ptr)
{
  return std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Umove<CUnmannedTraderSortType * *>(
           this,
           _First,
           _Last,
           _Ptr);
}
