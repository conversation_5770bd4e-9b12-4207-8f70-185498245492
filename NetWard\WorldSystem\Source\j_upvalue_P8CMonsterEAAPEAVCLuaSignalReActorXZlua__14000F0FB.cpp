#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$upvalue_@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@YAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAUlua_State@@@Z
 * Address: 0x14000F0FB

CLuaSignalReActor *( * lua_tinker::upvalue_<CLuaSignalReActor * (CMonster::*)(void)>(struct lua_State *L))(CMonster *this)
{
  return lua_tinker::upvalue_<CLuaSignalReActor * (CMonster::*)(void)>(L);
}
