#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAVCLogTypeDBTask@@PEAPEAV1@V?$allocator@PEAVCLogTypeDBTask@@@std@@@stdext@@YAPEAPEAVCLogTypeDBTask@@PEAPEAV1@00AEAV?$allocator@PEAVCLogTypeDBTask@@@std@@@Z
 * Address: 0x140005AA1

CLogTypeDBTask ** stdext::_Unchecked_uninitialized_move<CLogTypeDBTask * *,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(CLogTypeDBTask **_First, CLogTypeDBTask **_Last, CLogTypeDBTask **_Dest, std::allocator<CLogTypeDBTask *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CLogTypeDBTask * *,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
