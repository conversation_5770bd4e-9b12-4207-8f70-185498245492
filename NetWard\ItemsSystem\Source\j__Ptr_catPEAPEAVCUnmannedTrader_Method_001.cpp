#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Ptr_cat@PEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@@std@@YA?AU_Scalar_ptr_iterator_tag@0@AEAPEAPEAVCUnmannedTraderClassInfo@@0@Z
 * Address: 0x14001374B

std::_Scalar_ptr_iterator_tag  std::_Ptr_cat<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *>(CUnmannedTraderClassInfo ***__formal, CUnmannedTraderClassInfo ***a2)
{
  return std::_Ptr_cat<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *>(__formal, a2);
}
