#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Destroy_range@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@YAXPEAVCUnmannedTraderSchedule@@0AEAV?$allocator@VCUnmannedTraderSchedule@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z
 * Address: 0x140013598

void  std::_Destroy_range<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, std::allocator<CUnmannedTraderSchedule> *_Al, std::_Nonscalar_ptr_iterator_tag __formal)
{
  std::_Destroy_range<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(_First, _Last, _Al, __formal);
}
