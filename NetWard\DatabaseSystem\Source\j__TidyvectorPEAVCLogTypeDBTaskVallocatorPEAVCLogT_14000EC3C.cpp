#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Tidy@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAAXXZ
 * Address: 0x14000EC3C

void  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Tidy(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Tidy(this);
}
