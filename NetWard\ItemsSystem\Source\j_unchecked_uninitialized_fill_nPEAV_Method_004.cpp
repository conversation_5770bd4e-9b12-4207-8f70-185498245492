#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCUnmannedTraderItemCodeInfo@@_KV1@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@stdext@@YAXPEAVCUnmannedTraderItemCodeInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@Z
 * Address: 0x14000F169

void  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderItemCodeInfo *,unsigned int64_t,CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(CUnmannedTraderItemCodeInfo *_First, unsigned int64_t _Count, CUnmannedTraderItemCodeInfo *_Val, std::allocator<CUnmannedTraderItemCodeInfo> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderItemCodeInfo *,unsigned int64_t,CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    _First,
    _Count,
    _Val,
    _Al);
}
