#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Ptr_cat@PEAPEAVCMoveMapLimitInfo@@PEAPEAV1@@std@@YA?AU_Scalar_ptr_iterator_tag@0@AEAPEAPEAVCMoveMapLimitInfo@@0@Z
 * Address: 0x1403A36A0

char  std::_Ptr_cat<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *>(CMoveMapLimitInfo ***__formal, CMoveMapLimitInfo ***a2)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+24h] [bp-24h]@4

  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  return v6;
}
