#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Unchecked_move_backward@PEAPEAVCUnmannedTraderSortType@@PEAPEAV1@@stdext@@YAPEAPEAVCUnmannedTraderSortType@@PEAPEAV1@00@Z
 * Address: 0x140009FE8

CUnmannedTraderSortType ** stdext::_Unchecked_move_backward<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *>(CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last, CUnmannedTraderSortType **_Dest)
{
  return stdext::_Unchecked_move_backward<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *>(_First, _Last, _Dest);
}
