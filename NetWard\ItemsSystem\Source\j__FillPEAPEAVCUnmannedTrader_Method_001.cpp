#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Fill@PEAPEAVCUnmannedTraderClassInfo@@PEAV1@@std@@YAXPEAPEAVCUnmannedTraderClassInfo@@0AEBQEAV1@@Z
 * Address: 0x140004831

void  std::_Fill<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo *>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo *const *_Val)
{
  std::_Fill<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo *>(_First, _Last, _Val);
}
