#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_RankInGuild_Step8@CRFWorldDatabase@@QEAA_NXZ
 * Address: 0x1404B9660

char  CRFWorldDatabase::Update_RankInGuild_Step8(CRFWorldDatabase *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v4; // [sp+0h] [bp-28h]@1
  CRFWorldDatabase *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v5->vfptr,
    "CRFWorldDatabase::Update_RankInGuild_Step8() : Start drop #tbl_RankInGuildAll Table");
  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v5->vfptr, "drop table #tbl_RankInGuildAll", 0) )
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v5->vfptr,
      "CRFWorldDatabase::Update_RankInGuild_Step8() : End drop #tbl_RankInGuildAll Table");
    result = 1;
  }
  else
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v5->vfptr,
      "CRFWorldDatabase::Update_RankInGuild_Step8() : drop table #tbl_RankInGuildAll Fail!");
    result = 1;
  }
  return result;
}
