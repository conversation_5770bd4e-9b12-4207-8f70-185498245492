#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_RaceVoteInfoInit@CUserDB@@QEAA_NXZ
 * Address: 0x14011BB90

char  CUserDB::Update_RaceVoteInfoInit(CUserDB *this)
{
  this->m_AvatorData.dbSupplement.VoteEnable = 1;
  this->m_AvatorData.dbSupplement.dwAccumPlayTime = 0;
  this->m_AvatorData.dbSupplement.byVoted = 0;
  this->m_AvatorData.dbSupplement.wScanerCnt = 0;
  this->m_AvatorData.dbSupplement.dwScanerGetDate = 0;
  this->m_AvatorData.dbSupplement.dwLastResetDate = unk_1799CA308;
  this->m_bDataUpdate = 1;
  return 1;
}
