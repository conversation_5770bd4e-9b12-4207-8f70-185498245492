#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_<PERSON>len@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@KAXXZ
 * Address: 0x14000D48B

void __noreturn std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Xlen()
{
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_<PERSON><PERSON>();
}
