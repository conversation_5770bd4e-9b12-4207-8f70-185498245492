// NetWard RF Online Server - AutoTrader System
// File: AT_Add04.cpp - AutoTrader Item Code Info Vector Insertion
// Compatible with Visual Studio 2022 (C++20)
// Original Function: std::vector<CUnmannedTraderItemCodeInfo>::_Insert
// Original Address: 0x14037A610

#pragma once
#include "../../Headers/AutoTrader/AutoTraderCore.h"
#include "../../Headers/Common/NetWardTypes.h"
#include "../../Headers/Logging/LogManager.h"
#include <vector>
#include <iterator>
#include <algorithm>
#include <memory>
#include <stdexcept>

namespace NetWard::AutoTrader {

/**
 * @brief Inserts a range of AutoTrader item code info objects into a vector
 * @param targetVector The vector to insert into
 * @param insertPosition Iterator pointing to the insertion position
 * @param rangeBegin Iterator pointing to the beginning of the range to insert
 * @param rangeEnd Iterator pointing to the end of the range to insert
 * 
 * @details This function implements efficient vector insertion for AutoTrader item code info objects.
 *          It handles memory reallocation, element copying, and maintains vector integrity.
 *          This is critical for managing AutoTrader item identification and categorization.
 * 
 * @note Original address: 0x14037A610
 *       This replaces the complex STL vector insertion implementation for item codes.
 */
template<typename InputIterator>
void AutoTraderItemCodeInfoVector::InsertRange(
    Vector<AutoTraderItemCodeInfo>& targetVector,
    typename Vector<AutoTraderItemCodeInfo>::iterator insertPosition,
    InputIterator rangeBegin,
    InputIterator rangeEnd) noexcept
{
    try {
        // Calculate the number of elements to insert
        const auto insertCount = std::distance(rangeBegin, rangeEnd);
        
        // Validate input parameters
        if (insertCount <= 0) {
            return; // Nothing to insert
        }
        
        // Validate that the insertion position is valid
        if (insertPosition < targetVector.begin() || insertPosition > targetVector.end()) {
            throw std::out_of_range("Invalid insertion position for AutoTrader item code info vector");
        }
        
        // Calculate the insertion index for later use
        const auto insertIndex = std::distance(targetVector.begin(), insertPosition);
        
        // Reserve space if needed to avoid multiple reallocations
        const auto currentSize = targetVector.size();
        const auto newSize = currentSize + insertCount;
        
        if (newSize > targetVector.capacity()) {
            // Calculate new capacity with growth factor
            const auto newCapacity = std::max(newSize, targetVector.capacity() * 2);
            targetVector.reserve(newCapacity);
        }
        
        // Use the standard library's efficient insert method
        targetVector.insert(targetVector.begin() + insertIndex, rangeBegin, rangeEnd);
        
        LogManager::WriteDebug("AutoTrader", 
            "Successfully inserted {} item code info elements at position {}", 
            insertCount, insertIndex);
        
    } catch (const std::exception& e) {
        LogManager::WriteError("AutoTrader", 
            "Failed to insert AutoTrader item code info range: {}", e.what());
    }
}

/**
 * @brief Adds a single AutoTrader item code info to the vector
 * @param targetVector The vector to add to
 * @param itemCodeInfo The item code info to add
 * @return Iterator pointing to the inserted element
 */
typename Vector<AutoTraderItemCodeInfo>::iterator AutoTraderItemCodeInfoVector::AddItemCodeInfo(
    Vector<AutoTraderItemCodeInfo>& targetVector,
    const AutoTraderItemCodeInfo& itemCodeInfo) noexcept
{
    try {
        auto result = targetVector.insert(targetVector.end(), itemCodeInfo);
        
        LogManager::WriteDebug("AutoTrader", 
            "Added item code info with ID {} to vector. New size: {}", 
            itemCodeInfo.GetItemCode(), targetVector.size());
            
        return result;
    } catch (const std::exception& e) {
        LogManager::WriteError("AutoTrader", 
            "Failed to add AutoTrader item code info: {}", e.what());
        return targetVector.end();
    }
}

} // namespace NetWard::AutoTrader
