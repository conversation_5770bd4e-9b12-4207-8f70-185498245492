#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?UpdateRePrice@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034D230

char  CUnmannedTraderController::UpdateRePrice(CUnmannedTraderController *this, char *pData)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v5; // [sp+0h] [bp-68h]@1
  char *byProcRet; // [sp+20h] [bp-48h]@4
  char *v7; // [sp+30h] [bp-38h]@4
  char byState; // [sp+44h] [bp-24h]@4
  char v9; // [sp+54h] [bp-14h]@4
  CUnmannedTraderController *v10; // [sp+70h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v7 = pData;
  pData[20] = 0;
  byState = -1;
  byProcRet = v7 + 20;
  v9 = CUnmannedTraderController::CheckDBItemState(v10, v7[21], *((uint32_t*)v7 + 6), &byState, v7 + 20);
  if ( v7[20] )
  {
    result = v9;
  }
  else
  {
    LODWORD(byProcRet) = *((uint32_t*)v7 + 7);
    if ( CRFWorldDatabase::Update_UnmannedTraderSellInfoPrice(
           pkDB,
           v7[21],
           *((uint32_t*)v7 + 6),
           *((uint32_t*)v7 + 2),
           (unsigned int)byProcRet) )
    {
      result = 0;
    }
    else
    {
      v7[20] = 27;
      result = 24;
    }
  }
  return result;
}
