#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Ptr_cat@PEAPEAVCLogTypeDBTask@@PEAPEAV1@@std@@YA?AU_Scalar_ptr_iterator_tag@0@AEAPEAPEAVCLogTypeDBTask@@0@Z
 * Address: 0x14000C81A

std::_Scalar_ptr_iterator_tag  std::_Ptr_cat<CLogTypeDBTask * *,CLogTypeDBTask * *>(CLogTypeDBTask ***__formal, CLogTypeDBTask ***a2)
{
  return std::_Ptr_cat<CLogTypeDBTask * *,CLogTypeDBTask * *>(__formal, a2);
}
