#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_TrunkSlotNum@CUserDB@@QEAA_NE@Z
 * Address: 0x140116F10

char  CUserDB::Update_TrunkSlotNum(CUserDB *this, char bySlotNum)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-18h]@1
  CUserDB *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v6->m_AvatorData.dbTrunk.bySlotNum = bySlotNum;
  v6->m_bDataUpdate = 1;
  return 1;
}
