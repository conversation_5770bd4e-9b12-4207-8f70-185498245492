#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Fill_n@PEAPEAVCMoveMapLimitInfo@@_KPEAV1@@std@@YAXPEAPEAVCMoveMapLimitInfo@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403AC570

void  std::_Fill_n<CMoveMapLimitInfo * *,unsigned int64_t,CMoveMapLimitInfo *>(CMoveMapLimitInfo **_First, unsigned int64_t _Count, CMoveMapLimitInfo *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  memset64(_First, (unsigned int64_t)*_Val, _Count);
}
