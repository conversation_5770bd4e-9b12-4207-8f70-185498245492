#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetMyData@CMonsterAI@@UEAAHPEAVUsStateTBL@@PEAX@Z
 * Address: 0x14014FB70

int64_t  CMonsterAI::SetMyData(CMonsterAI *this, UsStateTBL *pStateTBL, void *pObject)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-28h]@1
  CMonsterAI *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  return Us_HFSM::SetMyData((Us_HFSM *)&v7->vfptr, pStateTBL, pObject) != 0;
}
