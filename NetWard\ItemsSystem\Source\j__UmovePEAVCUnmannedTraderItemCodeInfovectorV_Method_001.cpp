#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Umove@PEAVCUnmannedTraderItemCodeInfo@@@?$vector@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderItemCodeInfo@@PEAV2@00@Z
 * Address: 0x140003FD0

CUnmannedTraderItemCodeInfo * std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Umove<CUnmannedTraderItemCodeInfo *>(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this, CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, CUnmannedTraderItemCodeInfo *_Ptr)
{
  return std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Umove<CUnmannedTraderItemCodeInfo *>(
           this,
           _First,
           _Last,
           _Ptr);
}
