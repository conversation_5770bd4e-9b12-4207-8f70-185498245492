#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?PushDQSData@CMainThread@@QEAAPEAU_DB_QRY_SYN_DATA@@KPEAU_CLID@@EPEADH@Z
 * Address: 0x1401F4C50

_DB_QRY_SYN_DATA * CMainThread::PushDQSData(CMainThread *this, unsigned int dwAccountSerial, _CLID *pidWorld, char byQryCase, char *pQryData, int nSize)
{
  int64_t *v6; // rdi@1
  signed int64_t i; // rcx@1
  _DB_QRY_SYN_DATA *result; // rax@11
  int64_t v9; // [sp+0h] [bp-48h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-24h]@4
  _DB_QRY_SYN_DATA *v11; // [sp+38h] [bp-10h]@6
  CMainThread *v12; // [sp+50h] [bp+8h]@1
  unsigned int v13; // [sp+58h] [bp+10h]@1
  _CLID *Src; // [sp+60h] [bp+18h]@1
  char v15; // [sp+68h] [bp+20h]@1

  v15 = byQryCase;
  Src = pidWorld;
  v13 = dwAccountSerial;
  v12 = this;
  v6 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v6 = -*********;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  if ( !CNetIndexList::PopNode_Front(&v12->m_listDQSDataEmpty, &pdwOutIndex) )
    ServerProgramExit("m_listDQSDataEmpty.PopNode_Front() => failed", 1);
  v11 = &v12->m_DBQrySynData[pdwOutIndex];
  v12->m_DBQrySynData[pdwOutIndex].m_dwAccountSerial = v13;
  if ( Src )
    memcpy_0(&v11->m_idWorld, Src, 6ui64);
  v11->m_byQryCase = v15;
  if ( pQryData )
    memcpy_0(v11->m_sData, pQryData, nSize);
  if ( CNetIndexList::PushNode_Back(&v12->m_listDQSData, pdwOutIndex) )
  {
    v11->m_bUse = 1;
    v11->m_bLoad = 0;
    result = v11;
  }
  else
  {
    CLogFile::Write(&v12->m_logSystemError, "%d : m_listDQSData.PushNode_Back() => failed ", (unsigned int8_t)v15);
    result = 0i64;
  }
  return result;
}
