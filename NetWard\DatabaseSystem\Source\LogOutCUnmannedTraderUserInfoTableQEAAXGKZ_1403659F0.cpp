#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?LogOut@CUnmannedTraderUserInfoTable@@QEAAXGK@Z
 * Address: 0x1403659F0

void  CUnmannedTraderUserInfoTable::LogOut(CUnmannedTraderUserInfoTable *this, unsigned int16_t wInx, unsigned int dwSerial)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  CUnmannedTraderUserInfo *v5; // rax@7
  int64_t v6; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfoTable *v7; // [sp+30h] [bp+8h]@1
  unsigned int16_t v8; // [sp+38h] [bp+10h]@1
  unsigned int dwSeriala; // [sp+40h] [bp+18h]@1

  dwSeriala = dwSerial;
  v8 = wInx;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( !std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::empty(&v7->m_veckInfo)
    && std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(&v7->m_veckInfo) > v8 )
  {
    v5 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](&v7->m_veckInfo, v8);
    CUnmannedTraderUserInfo::LogOut(v5, dwSeriala, v7->m_pkLogger);
  }
}
