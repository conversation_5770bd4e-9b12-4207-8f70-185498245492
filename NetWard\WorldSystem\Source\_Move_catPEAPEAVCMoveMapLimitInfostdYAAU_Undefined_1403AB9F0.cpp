#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Move_cat@PEAPEAVCMoveMapLimitInfo@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAPEAVCMoveMapLimitInfo@@@Z
 * Address: 0x1403AB9F0

char  std::_Move_cat<CMoveMapLimitInfo * *>(CMoveMapLimitInfo **const *__formal)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-48h]@1
  char v5; // [sp+24h] [bp-24h]@4

  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  return v5;
}
