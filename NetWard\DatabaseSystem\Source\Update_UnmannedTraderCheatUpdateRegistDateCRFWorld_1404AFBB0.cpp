#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_UnmannedTraderCheatUpdateRegistDate@CRFWorldDatabase@@QEAA_NEK@Z
 * Address: 0x1404AFBB0

bool  CRFWorldDatabase::Update_UnmannedTraderCheatUpdateRegistDate(CRFWorldDatabase *this, char byType, unsigned int dwRegistSerial)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-458h]@1
  char Dest; // [sp+30h] [bp-428h]@4
  unsigned int64_t v8; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+460h] [bp+8h]@1

  v9 = this;
  v3 = &v6;
  for ( i = 276i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v8 = (unsigned int64_t)&v6 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pUpdate_utcheatregdate( %u, %u ) }", (unsigned int8_t)byType, dwRegistSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &Dest, 1);
}
