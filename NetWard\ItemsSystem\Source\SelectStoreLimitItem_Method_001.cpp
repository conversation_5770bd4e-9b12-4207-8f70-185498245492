#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SelectStoreLimitItem@CItemStoreManager@@QEAA_NXZ
 * Address: 0x14034A0C0

char  CItemStoreManager::SelectStoreLimitItem(CItemStoreManager *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v4; // [sp+0h] [bp-28h]@1
  CItemStoreManager *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( CRFWorldDatabase::Select_StoreLimitItem(pkDB, &v5->m_Sheet) == 1 )
  {
    CItemStoreManager::Log(
      v5,
      "CItemStoreManager::SelectStoreLimitItem\r\n\t\tg_Main.m_pWorldDB->Select_StoreLimitItem() Fail!\r\n");
    result = 0;
  }
  else
  {
    result = 1;
  }
  return result;
}
