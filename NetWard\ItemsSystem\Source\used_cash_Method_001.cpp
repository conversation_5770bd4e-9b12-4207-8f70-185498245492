#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?used_cash@CMgrAvatorItemHistory@@QEAAXHHPEAD@Z
 * Address: 0x14023E0D0

void  CMgrAvatorItemHistory::used_cash(CMgrAvatorItemHistory *this, int nCurCash, int nUseCash, char *pFileName)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  CMgrAvatorItemHistory *v8; // [sp+40h] [bp+8h]@1
  char *pszFileName; // [sp+58h] [bp+20h]@1

  pszFileName = pFileName;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  sData[0] = 0;
  v7 = nCurCash - nUseCash;
  sprintf(
    sData,
    "[CASH_AMOUNT] : [cash:%d] - [used:%d] = [remain:%d]\r\n",
    (unsigned int)nCurCash,
    (unsigned int)nUseCash);
  CMgrAvatorItemHistory::WriteFile(v8, pszFileName, sData);
}
