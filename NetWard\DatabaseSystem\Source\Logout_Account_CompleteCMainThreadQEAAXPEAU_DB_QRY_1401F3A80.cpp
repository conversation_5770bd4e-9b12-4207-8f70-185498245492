#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Logout_Account_Complete@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F3A80

void  CMainThread::Logout_Account_Complete(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  CUserDB *v6; // [sp+28h] [bp-10h]@4

  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v2 = -*********;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v5 = 0;
  v6 = &g_UserDB[pData->m_idWorld.wIndex];
  if ( v6->m_bActive )
  {
    if ( v6->m_idWorld.dwSerial == pData->m_idWorld.dwSerial )
      CUserDB::Exit_Account_Complete(v6, pData->m_byResult);
  }
}
