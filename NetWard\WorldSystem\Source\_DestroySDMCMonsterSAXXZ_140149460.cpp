#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_DestroySDM@CMonster@@SAXXZ
 * Address: 0x140149460

void CMonster::_DestroySDM(void)
{
  int64_t *v0; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v2; // [sp+0h] [bp-38h]@1
  void *v3; // [sp+20h] [bp-18h]@6

  v0 = &v2;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v0 = -858993460;
    v0 = (int64_t *)((char *)v0 + 4);
  }
  if ( CMonster::s_nAllocNum <= 0 )
  {
    if ( CMonster::s_idxMonsterLoot )
    {
      v3 = CMonster::s_idxMonsterLoot;
      operator delete[](CMonster::s_idxMonsterLoot);
      CMonster::s_idxMonsterLoot = 0i64;
    }
  }
}
