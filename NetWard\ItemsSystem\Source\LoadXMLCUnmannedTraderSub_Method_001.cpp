#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?LoadXML@CUnmannedTraderSubClassInfoDefault@@UEAA_NPEAVTiXmlElement@@AEAVCLogFile@@KK@Z
 * Address: 0x1403839A0

bool  CUnmannedTraderSubClassInfoDefault::LoadXML(CUnmannedTraderSubClassInfoDefault *this, TiXmlElement *elemSubClass, CLogFile *kLogger, unsigned int dwDivisionID, unsigned int dwClassID)
{
  int64_t *v5; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v8; // [sp+0h] [bp-38h]@1
  CUnmannedTraderSubClassInfoDefault *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  return CUnmannedTraderSubClassInfo::LoadXML(
           (CUnmannedTraderSubClassInfo *)&v9->vfptr,
           elemSubClass,
           kLogger,
           dwDivisionID,
           dwClassID);
}
