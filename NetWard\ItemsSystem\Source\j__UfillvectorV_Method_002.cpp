#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Ufill@?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderRegistItemInfo@@PEAV3@_KAEBV3@@Z
 * Address: 0x1400026D0

CUnmannedTraderRegistItemInfo * std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Ufill(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, CUnmannedTraderRegistItemInfo *_Ptr, unsigned int64_t _Count, CUnmannedTraderRegistItemInfo *_Val)
{
  return std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Ufill(
           this,
           _Ptr,
           _Count,
           _Val);
}
