#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Copy_backward_opt@PEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@00Urandom_access_iterator_tag@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140005074

CUnmannedTraderClassInfo ** std::_Copy_backward_opt<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::random_access_iterator_tag>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo **_Dest, std::random_access_iterator_tag __formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Copy_backward_opt<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::random_access_iterator_tag>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
