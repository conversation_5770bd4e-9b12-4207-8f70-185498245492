#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Fill_n@PEAPEAVCUnmannedTraderClassInfo@@_KPEAV1@@std@@YAXPEAPEAVCUnmannedTraderClassInfo@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140006E88

void  std::_Fill_n<CUnmannedTraderClassInfo * *,unsigned int64_t,CUnmannedTraderClassInfo *>(CUnmannedTraderClassInfo **_First, unsigned int64_t _Count, CUnmannedTraderClassInfo *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  std::_Fill_n<CUnmannedTraderClassInfo * *,unsigned int64_t,CUnmannedTraderClassInfo *>(_First, _Count, _Val, __formal);
}
