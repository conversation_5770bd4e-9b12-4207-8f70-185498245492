#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_UnmannedTraderResutlInfo@CRFWorldDatabase@@QEAA_NEKEKKAEBU_SYSTEMTIME@@@Z
 * Address: 0x1404AE410

bool  CRFWorldDatabase::Update_UnmannedTraderResutlInfo(CRFWorldDatabase *this, char byType, unsigned int dwRegistSerial, char byState, unsigned int dwBuyer, unsigned int dwTax, _SYSTEMTIME *kCurTime)
{
  int64_t *v7; // rdi@1
  signed int64_t i; // rcx@1
  int v9; // ecx@4
  int v10; // edx@4
  int v11; // er8@4
  int v12; // er9@4
  int v13; // er10@4
  int v14; // er11@4
  int64_t v16; // [sp+0h] [bp-4B8h]@1
  int v17; // [sp+20h] [bp-498h]@4
  unsigned int v18; // [sp+28h] [bp-490h]@4
  unsigned int v19; // [sp+30h] [bp-488h]@4
  int v20; // [sp+38h] [bp-480h]@4
  int v21; // [sp+40h] [bp-478h]@4
  int v22; // [sp+48h] [bp-470h]@4
  int v23; // [sp+50h] [bp-468h]@4
  int v24; // [sp+58h] [bp-460h]@4
  int v25; // [sp+60h] [bp-458h]@4
  int v26; // [sp+68h] [bp-450h]@4
  char Dest; // [sp+80h] [bp-438h]@4
  unsigned int64_t v28; // [sp+490h] [bp-28h]@4
  CRFWorldDatabase *v29; // [sp+4C0h] [bp+8h]@1
  char v30; // [sp+4C8h] [bp+10h]@1
  unsigned int v31; // [sp+4D0h] [bp+18h]@1
  char v32; // [sp+4D8h] [bp+20h]@1

  v32 = byState;
  v31 = dwRegistSerial;
  v30 = byType;
  v29 = this;
  v7 = &v16;
  for ( i = 298i64; i; --i )
  {
    *(uint32_t*)v7 = -*********;
    v7 = (int64_t *)((char *)v7 + 4);
  }
  v28 = (unsigned int64_t)&v16 ^ _security_cookie;
  v9 = kCurTime->wSecond;
  v10 = kCurTime->wMinute;
  v11 = kCurTime->wHour;
  v12 = kCurTime->wDay;
  v13 = kCurTime->wMonth;
  v14 = kCurTime->wYear;
  v26 = kCurTime->wMilliseconds;
  v25 = v9;
  v24 = v10;
  v23 = v11;
  v22 = v12;
  v21 = v13;
  v20 = v14;
  v19 = dwTax;
  v18 = dwBuyer;
  v17 = (unsigned int8_t)v32;
  sprintf(
    &Dest,
    "{ CALL pUpdate_utresutlinfobuy( %u, %u, %u, %u, %u,'%04d-%02d-%02d %02d:%02d:%02d.%03d' ) }",
    (unsigned int8_t)v30,
    v31);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v29->vfptr, &Dest, 1);
}
