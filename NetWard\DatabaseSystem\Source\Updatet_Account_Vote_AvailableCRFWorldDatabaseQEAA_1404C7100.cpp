#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Updatet_Account_Vote_Available@CRFWorldDatabase@@QEAAHKPEAE@Z
 * Address: 0x1404C7100

signed int64_t  CRFWorldDatabase::Updatet_Account_Vote_Available(CRFWorldDatabase *this, unsigned int dwSerial, char *byVoteEnable)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  signed int64_t result; // rax@8
  int64_t v6; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  char DstBuf; // [sp+40h] [bp-148h]@4
  char v10; // [sp+41h] [bp-147h]@4
  SQLLEN v11; // [sp+158h] [bp-30h]@22
  int16_t v12; // [sp+164h] [bp-24h]@9
  unsigned int8_t v13; // [sp+168h] [bp-20h]@16
  unsigned int8_t v14; // [sp+169h] [bp-1Fh]@24
  unsigned int64_t v15; // [sp+178h] [bp-10h]@4
  CRFWorldDatabase *v16; // [sp+190h] [bp+8h]@1
  char *TargetValue; // [sp+1A0h] [bp+18h]@1

  TargetValue = byVoteEnable;
  v16 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(uint32_t*)v3 = -*********;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v15 = (unsigned int64_t)&v6 ^ _security_cookie;
  DstBuf = 0;
  memset(&v10, 0, 0xFFui64);
  sprintf_s(
    &DstBuf,
    0x100ui64,
    "declare @out_amount int exec pUpdatet_Account_Vote_Available  %d, @vote = @out_amount output select @out_amount",
    dwSerial);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &DstBuf);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v12 = SQLExecDirect_0(v16->m_hStmtSelect, &DstBuf, -3);
    if ( v12 && v12 != 1 )
    {
      if ( v12 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v12 = SQLFetch_0(v16->m_hStmtSelect);
      if ( v12 && v12 != 1 )
      {
        v13 = 0;
        if ( v12 == 100 )
        {
          v13 = 2;
        }
        else
        {
          SQLStmt = v16->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
          v13 = 1;
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        result = v13;
      }
      else
      {
        *TargetValue = 0;
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v16->m_hStmtSelect, 1u, 4, TargetValue, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v14 = 0;
          if ( v12 == 100 )
          {
            v14 = 2;
          }
          else
          {
            SQLStmt = v16->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &DstBuf, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
            v14 = 1;
          }
          if ( v16->m_hStmtSelect )
            SQLCloseCursor_0(v16->m_hStmtSelect);
          result = v14;
        }
        else
        {
          if ( v16->m_hStmtSelect )
            SQLCloseCursor_0(v16->m_hStmtSelect);
          if ( v16->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &DstBuf);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1i64;
  }
  return result;
}
