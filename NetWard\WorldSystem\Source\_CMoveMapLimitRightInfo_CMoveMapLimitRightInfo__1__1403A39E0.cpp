#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CMoveMapLimitRightInfo::_CMoveMapLimitRightInfo_::_1_::dtor$0
 * Address: 0x1403A39E0

void  CMoveMapLimitRightInfo::_CMoveMapLimitRightInfo_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(*(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 64));
}
