#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Uninit_fill_n@PEAVCUnmannedTraderSchedule@@_KV1@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@YAXPEAVCUnmannedTraderSchedule@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderSchedule@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140008F8A

void  std::_Uninit_fill_n<CUnmannedTraderSchedule *,unsigned int64_t,CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(CUnmannedTraderSchedule *_First, unsigned int64_t _Count, CUnmannedTraderSchedule *_Val, std::allocator<CUnmannedTraderSchedule> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CUnmannedTraderSchedule *,unsigned int64_t,CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
