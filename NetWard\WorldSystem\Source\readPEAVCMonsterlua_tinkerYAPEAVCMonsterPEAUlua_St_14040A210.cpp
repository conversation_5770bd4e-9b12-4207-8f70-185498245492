#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$read@PEAVCMonster@@@lua_tinker@@YAPEAVCMonster@@PEAUlua_State@@H@Z
 * Address: 0x14040A210

CMonster * lua_tinker::read<CMonster *>(struct lua_State *L, int index)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-28h]@1
  struct lua_State *La; // [sp+30h] [bp+8h]@1

  La = L;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  return lua_tinker::lua2type<CMonster *>(La, index);
}
