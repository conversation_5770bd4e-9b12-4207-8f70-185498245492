#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Tidy@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAAXXZ
 * Address: 0x140005ACE

void  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Tidy(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Tidy(this);
}
