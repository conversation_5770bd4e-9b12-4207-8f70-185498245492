#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetOverRegistTime@CUnmannedTraderRegistItemInfo@@QEAAXXZ
 * Address: 0x14035FA80

void  CUnmannedTraderRegistItemInfo::SetOverRegistTime(CUnmannedTraderRegistItemInfo *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-48h]@1
  int64_t _Time; // [sp+28h] [bp-20h]@4
  CUnmannedTraderRegistItemInfo *v5; // [sp+50h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  _Time = 0i64;
  time_18(&_Time);
  v5->m_tStartTime = _Time - 3600 * v5->m_bySellTurm;
}
