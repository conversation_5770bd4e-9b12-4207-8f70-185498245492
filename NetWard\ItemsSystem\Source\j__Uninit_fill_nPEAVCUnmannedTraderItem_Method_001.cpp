#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Uninit_fill_n@PEAVCUnmannedTraderItemCodeInfo@@_KV1@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@YAXPEAVCUnmannedTraderItemCodeInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140013A7F

void  std::_Uninit_fill_n<CUnmannedTraderItemCodeInfo *,unsigned int64_t,CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(CUnmannedTraderItemCodeInfo *_First, unsigned int64_t _Count, CUnmannedTraderItemCodeInfo *_Val, std::allocator<CUnmannedTraderItemCodeInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CUnmannedTraderItemCodeInfo *,unsigned int64_t,CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
