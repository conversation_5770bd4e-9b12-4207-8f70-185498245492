#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?pc_OpenWorldFailureResult@CMainThread@@QEAAXPEAD@Z
 * Address: 0x1401F56F0

void  CMainThread::pc_OpenWorldFailureResult(CMainThread *this, char *szMsg)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-28h]@1

  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  MyMessageBox("pc_OpenWorldFailureResult", "request world-open fail");
  CWnd::SendMessageA(g_pFrame, 0x10u, 0i64, 0i64);
}
