#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Move_cat@PEAPEAVCMoveMapLimitInfo@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAPEAVCMoveMapLimitInfo@@@Z
 * Address: 0x14000BC71

std::_Undefined_move_tag  std::_Move_cat<CMoveMapLimitInfo * *>(CMoveMapLimitInfo **const *__formal)
{
  return std::_Move_cat<CMoveMapLimitInfo * *>(__formal);
}
