#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Reged_Char_Request@CUserDB@@QEAA_NXZ
 * Address: 0x140111580

char  CUserDB::Reged_Char_Request@<al>(CUserDB *this@<rcx>, signed int64_t a2@<rax>)
{
  void *v2; // rsp@1
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  char *v6; // rax@13
  int64_t v7; // [sp-20h] [bp-1138h]@1
  char *pQryData; // [sp+0h] [bp-1118h]@13
  int nSize; // [sp+8h] [bp-1110h]@13
  int j; // [sp+10h] [bp-1108h]@10
  _qry_sheet_reged v11; // [sp+30h] [bp-10E8h]@13
  unsigned int64_t v12; // [sp+1100h] [bp-18h]@4
  CUserDB *v13; // [sp+1120h] [bp+8h]@1

  v13 = this;
  v2 = alloca(a2);
  v3 = &v7;
  for ( i = 1100i64; i; --i )
  {
    *(uint32_t*)v3 = -*********;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v12 = (unsigned int64_t)&v7 ^ _security_cookie;
  if ( v13->m_bActive )
  {
    if ( _SYNC_STATE::chk_reged(&v13->m_ss) )
    {
      if ( v13->m_bDBWaitState )
      {
        result = 0;
      }
      else
      {
        for ( j = 0; j < 50; ++j )
        {
          v13->m_NotArrangedChar[j].dwSerial = -1;
          v13->m_dwArrangePassCase0[j] = -1;
        }
        CMgrAccountLobbyHistory::reged_char_request(&CUserDB::s_MgrLobbyHistory, v13->m_szLobbyHistoryFileName);
        _qry_sheet_reged::_qry_sheet_reged(&v11);
        v11.dwAccountSerial = v13->m_dwAccountSerial;
        v6 = inet_ntoa((struct in_addr)v13->m_ipAddress);
        strcpy_0(v11.in_szIP, v6);
        nSize = _qry_sheet_reged::size(&v11);
        pQryData = (char *)&v11;
        if ( CMainThread::PushDQSData(&g_Main, v13->m_dwAccountSerial, &v13->m_idWorld, 0, (char *)&v11, nSize) )
        {
          v13->m_bDBWaitState = 1;
          result = 1;
        }
        else
        {
          result = 0;
        }
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
