#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?max_size@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x140367CB0

unsigned int64_t  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::max_size(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  return std::allocator<CUnmannedTraderUserInfo>::max_size(&v5->_Alval);
}
