#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLSetConfigMode
 * Address: 0x1404DADD0

int  SQLSetConfigMode(unsigned int16_t wConfigMode)
{
  unsigned int16_t v1; // bx@1
  int64_t ( *v2)(); // rax@1
  int result; // eax@2

  v1 = wConfigMode;
  v2 = ODBC___GetSetupProc("SQLSetConfigMode");
  if ( v2 )
    result = ((int ( *)(_QWORD))v2)(v1);
  else
    result = 0;
  return result;
}
