#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetLogger@CUnmannedTraderUserInfoTable@@QEAAXPEAVCLogFile@@0@Z
 * Address: 0x140351910

void  CUnmannedTraderUserInfoTable::SetLogger(CUnmannedTraderUserInfoTable *this, CLogFile *pkLogger, CLogFile *pkSeviceLogger)
{
  this->m_pkLogger = pkLogger;
  this->m_pkServiceLogger = pkSeviceLogger;
}
