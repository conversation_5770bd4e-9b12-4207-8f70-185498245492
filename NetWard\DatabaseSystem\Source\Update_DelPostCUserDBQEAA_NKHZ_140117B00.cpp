#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_DelPost@CUserDB@@QEAA_NKH@Z
 * Address: 0x140117B00

char  CUserDB::Update_DelPost(CUserDB *this, unsigned int dwSerial, int nIndex)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@6
  bool *v6; // [sp+0h] [bp-18h]@1
  CUserDB *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = (int64_t *)&v6;
  for ( i = 4i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( nIndex >= 0 && nIndex < 50 )
  {
    if ( v7->m_AvatorData.dbPostData.dbPost.m_PostList[nIndex].bNew )
    {
      result = 1;
    }
    else
    {
      v6 = &v7->m_AvatorData.dbPostData.dbDelPost.m_bUpdate;
      if ( v7->m_AvatorData.dbPostData.dbDelPost.m_nCum < v7->m_AvatorData.dbPostData.dbDelPost.m_nMax )
      {
        *(uint32_t*)&v6[8 * *(uint32_t*)(v6 + 5) + 9] = dwSerial;
        *(uint32_t*)&v6[8 * (*(uint32_t*)(v6 + 5))++ + 13] = nIndex;
        *v6 = 1;
        v7->m_bDataUpdate = 1;
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
