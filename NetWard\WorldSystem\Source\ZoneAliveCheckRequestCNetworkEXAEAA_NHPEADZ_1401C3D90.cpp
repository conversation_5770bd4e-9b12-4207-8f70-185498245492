#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ZoneAliveCheckRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C3D90

char  CNetworkEX::ZoneAliveCheckRequest(CNetworkEX *this, int n, char *pBuf)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  CBillingManager *v5; // rax@4
  int64_t v7; // [sp+0h] [bp-38h]@1
  unsigned int *v8; // [sp+20h] [bp-18h]@4

  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v8 = (unsigned int *)pBuf;
  v5 = CTSingleton<CBillingManager>::Instance();
  CBillingManager::SendMsg_ZoneAliveCheck(v5, *v8);
  return 1;
}
