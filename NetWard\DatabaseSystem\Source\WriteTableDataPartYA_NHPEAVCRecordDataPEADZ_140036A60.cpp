#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?WriteTableDataPart@@YA_NHPEAVCRecordData@@PEAD@Z
 * Address: 0x140036A60

bool  WriteTableDataPart(int nTableCode, CRecordData *pItemData, char *szErrCode)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  bool result; // al@7
  int64_t v6; // [sp+0h] [bp-38h]@1
  __list *v7; // [sp+20h] [bp-18h]@12
  int v8; // [sp+40h] [bp+8h]@1

  v8 = nTableCode;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( s_ptblItemData )
  {
    if ( v8 < 37 )
    {
      v7 = ItemDataFile;
      result = CRecordData::ReadRecord(pItemData, ItemDataFile[v8].pfilename, ItemDataFile[v8].nstructsize, szErrCode) != 0;
    }
    else
    {
      if ( szErrCode )
        sprintf(szErrCode, "nTableCode<%d> >= item_tbl_num<%d>", (unsigned int)v8, 37i64);
      result = 0;
    }
  }
  else
  {
    if ( szErrCode )
      sprintf(szErrCode, "ù° ȣ");
    result = 0;
  }
  return result;
}
