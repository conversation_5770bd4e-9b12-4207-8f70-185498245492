#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SelectUsedRecordNum@CItemStoreManager@@QEAA_NPEAK@Z
 * Address: 0x140349F40

char  CItemStoreManager::SelectUsedRecordNum(CItemStoreManager *this, unsigned int *pdwUsedNum)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v5; // [sp+0h] [bp-28h]@1
  CItemStoreManager *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( CRFWorldDatabase::Select_UsedLimitItemRecordNum(pkDB, pdwUsedNum) == 1 )
  {
    CItemStoreManager::Log(
      v6,
      "CItemStoreManager::SelectUsedRecordNum\r\n\t\tg_Main.m_pWorldDB->Select_UsedLimitItemRecordNum() Fail!\r\n");
    result = 0;
  }
  else
  {
    result = 1;
  }
  return result;
}
