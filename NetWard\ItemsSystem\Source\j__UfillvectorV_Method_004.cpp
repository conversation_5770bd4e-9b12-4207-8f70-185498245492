#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Ufill@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderUserInfo@@PEAV3@_KAEBV3@@Z
 * Address: 0x14000E322

CUnmannedTraderUserInfo * std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Ufill(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, CUnmannedTraderUserInfo *_Ptr, unsigned int64_t _Count, CUnmannedTraderUserInfo *_Val)
{
  return std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Ufill(this, _Ptr, _Count, _Val);
}
