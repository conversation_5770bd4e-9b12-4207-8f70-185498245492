#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_MacroData@CRFWorldDatabase@@QEAA_NKPEAU_AIOC_A_MACRODATA@@@Z
 * Address: 0x1404A5CE0

char  CRFWorldDatabase::Update_MacroData(CRFWorldDatabase *this, unsigned int dwSerial, _AIOC_A_MACRODATA *pMacro)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@73
  int64_t v6; // [sp+0h] [bp-B18h]@1
  void *SQLStmt; // [sp+20h] [bp-AF8h]@15
  char *v8; // [sp+28h] [bp-AF0h]@27
  char *v9; // [sp+30h] [bp-AE8h]@60
  int16_t v10; // [sp+40h] [bp-AD8h]@74
  char Dst; // [sp+60h] [bp-AB8h]@6
  char Dest; // [sp+880h] [bp-298h]@15
  unsigned int j; // [sp+A84h] [bp-94h]@4
  unsigned int v14; // [sp+A90h] [bp-88h]@7
  unsigned int v15; // [sp+A94h] [bp-84h]@10
  unsigned int v16; // [sp+A98h] [bp-80h]@13
  unsigned int v17; // [sp+A9Ch] [bp-7Ch]@16
  unsigned int v18; // [sp+AA0h] [bp-78h]@19
  unsigned int v19; // [sp+AA4h] [bp-74h]@22
  unsigned int v20; // [sp+AA8h] [bp-70h]@25
  unsigned int v21; // [sp+AACh] [bp-6Ch]@28
  unsigned int v22; // [sp+AB0h] [bp-68h]@31
  unsigned int v23; // [sp+AB4h] [bp-64h]@34
  unsigned int v24; // [sp+AB8h] [bp-60h]@37
  unsigned int v25; // [sp+ABCh] [bp-5Ch]@40
  unsigned int v26; // [sp+AC0h] [bp-58h]@43
  char *v27; // [sp+AC8h] [bp-50h]@46
  char *v28; // [sp+AD0h] [bp-48h]@49
  char *v29; // [sp+AD8h] [bp-40h]@52
  char *v30; // [sp+AE0h] [bp-38h]@55
  _AIOC_A_MACRODATA::MACRO_CHATDATA *v31; // [sp+AE8h] [bp-30h]@58
  unsigned int v32; // [sp+AF0h] [bp-28h]@61
  unsigned int v33; // [sp+AF4h] [bp-24h]@64
  unsigned int v34; // [sp+AF8h] [bp-20h]@67
  unsigned int64_t v35; // [sp+B00h] [bp-18h]@4
  CRFWorldDatabase *v36; // [sp+B20h] [bp+8h]@1
  unsigned int v37; // [sp+B28h] [bp+10h]@1
  _AIOC_A_MACRODATA *v38; // [sp+B30h] [bp+18h]@1

  v38 = pMacro;
  v37 = dwSerial;
  v36 = this;
  v3 = &v6;
  for ( i = 708i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v35 = (unsigned int64_t)&v6 ^ _security_cookie;
  for ( j = 0; (signed int)j < 3; ++j )
  {
    memset_0(&Dst, 0, 0x800ui64);
    if ( (signed int)j >= 1 )
      v14 = 0;
    else
      v14 = v38->mcr_Potion[j].Potion[2];
    if ( (signed int)j >= 1 )
      v15 = 0;
    else
      v15 = v38->mcr_Potion[j].Potion[1];
    if ( (signed int)j >= 1 )
      v16 = 0;
    else
      v16 = v38->mcr_Potion[j].Potion[0];
    LODWORD(SQLStmt) = v14;
    sprintf(&Dst, " Update tbl_Macro Set hp=%d, fp=%d, sp=%d,", v16, v15);
    memset_0(&Dest, 0, 0x200ui64);
    if ( (signed int)j >= 3 )
      v17 = 0;
    else
      v17 = v38->mcr_Action[j].Action[3];
    if ( (signed int)j >= 3 )
      v18 = 0;
    else
      v18 = v38->mcr_Action[j].Action[2];
    if ( (signed int)j >= 3 )
      v19 = 0;
    else
      v19 = v38->mcr_Action[j].Action[1];
    if ( (signed int)j >= 3 )
      v20 = 0;
    else
      v20 = v38->mcr_Action[j].Action[0];
    LODWORD(v8) = v17;
    LODWORD(SQLStmt) = v18;
    sprintf(&Dest, " action0=%d, action1=%d, action2=%d, action3=%d,", v20, v19);
    strcat_0(&Dst, &Dest);
    memset_0(&Dest, 0, 0x200ui64);
    if ( (signed int)j >= 3 )
      v21 = 0;
    else
      v21 = v38->mcr_Action[j].Action[7];
    if ( (signed int)j >= 3 )
      v22 = 0;
    else
      v22 = v38->mcr_Action[j].Action[6];
    if ( (signed int)j >= 3 )
      v23 = 0;
    else
      v23 = v38->mcr_Action[j].Action[5];
    if ( (signed int)j >= 3 )
      v24 = 0;
    else
      v24 = v38->mcr_Action[j].Action[4];
    LODWORD(v8) = v21;
    LODWORD(SQLStmt) = v22;
    sprintf(&Dest, " action4=%d, action5=%d, action6=%d, action7=%d,", v24, v23);
    strcat_0(&Dst, &Dest);
    memset_0(&Dest, 0, 0x200ui64);
    if ( (signed int)j >= 3 )
      v25 = 0;
    else
      v25 = v38->mcr_Action[j].Action[9];
    if ( (signed int)j >= 3 )
      v26 = 0;
    else
      v26 = v38->mcr_Action[j].Action[8];
    sprintf(&Dest, " action8=%d, action9=%d,", v26, v25);
    strcat_0(&Dst, &Dest);
    memset_0(&Dest, 0, 0x200ui64);
    if ( (signed int)j >= 2 )
      v27 = (char *)&unk_1408551FF;
    else
      v27 = v38->mcr_Chat[j].Chat[4];
    if ( (signed int)j >= 2 )
      v28 = (char *)&unk_140855211;
    else
      v28 = v38->mcr_Chat[j].Chat[3];
    if ( (signed int)j >= 2 )
      v29 = (char *)&unk_140855212;
    else
      v29 = v38->mcr_Chat[j].Chat[2];
    if ( (signed int)j >= 2 )
      v30 = (char *)&unk_140855213;
    else
      v30 = v38->mcr_Chat[j].Chat[1];
    if ( (signed int)j >= 2 )
      v31 = (_AIOC_A_MACRODATA::MACRO_CHATDATA *)&unk_140855214;
    else
      v31 = &v38->mcr_Chat[j];
    v9 = v27;
    v8 = v28;
    SQLStmt = v29;
    sprintf(&Dest, " chat0='%s', chat1='%s', chat2='%s', chat3='%s', chat4='%s',", v31, v30);
    strcat_0(&Dst, &Dest);
    memset_0(&Dest, 0, 0x200ui64);
    if ( (signed int)j >= 1 )
      v32 = 0;
    else
      v32 = v38->mcr_Potion[j].PotionValue[2];
    if ( (signed int)j >= 1 )
      v33 = 0;
    else
      v33 = v38->mcr_Potion[j].PotionValue[1];
    if ( (signed int)j >= 1 )
      v34 = 0;
    else
      v34 = v38->mcr_Potion[j].PotionValue[0];
    LODWORD(SQLStmt) = v32;
    sprintf(&Dest, " hpvalue=%d, fpvalue=%d, spvalue=%d", v34, v33);
    strcat_0(&Dst, &Dest);
    memset_0(&Dest, 0, 0x200ui64);
    sprintf(&Dest, " where serial=%d and belt=%d", v37, j);
    strcat_0(&Dst, &Dest);
    if ( v36->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v36->vfptr, &Dst);
    if ( !v36->m_hStmtSelect && !CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v36->vfptr) )
    {
      CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v36->vfptr, "ReConnectDataBase Fail. Query : %s", &Dst);
      return 0;
    }
    v10 = SQLExecDirectA_0(v36->m_hStmtSelect, &Dst, -3);
    if ( v10 )
    {
      if ( v10 != 1 )
        break;
    }
    if ( v36->m_bSaveDBLog )
      CRFNewDatabase::FmtLog((CRFNewDatabase *)&v36->vfptr, L"%s Success", &Dst);
  }
  if ( v10 && v10 != 1 )
  {
    if ( v10 == 100 )
    {
      result = 0;
    }
    else
    {
      SQLStmt = v36->m_hStmtSelect;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v36->vfptr, v10, &Dst, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v36->vfptr, v10, v36->m_hStmtSelect);
      result = 0;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
