#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_SFContInsert@CUserDB@@QEAA_NEEEGEG@Z
 * Address: 0x140116AD0

char  CUserDB::Update_SFContInsert(CUserDB *this, char byContCode, char bySlotIndex, char byEffectCode, unsigned int16_t wEffectIndex, char byLv, unsigned int16_t wDurSec)
{
  int64_t *v7; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v10; // [sp+0h] [bp-38h]@1
  char pl_byLv[4]; // [sp+20h] [bp-18h]@9
  CUserDB *v12; // [sp+40h] [bp+8h]@1
  char v13; // [sp+48h] [bp+10h]@1
  char v14; // [sp+50h] [bp+18h]@1
  char v15; // [sp+58h] [bp+20h]@1

  v15 = byEffectCode;
  v14 = bySlotIndex;
  v13 = byContCode;
  v12 = this;
  v7 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v7 = -858993460;
    v7 = (int64_t *)((char *)v7 + 4);
  }
  if ( (signed int)(unsigned int8_t)byContCode < 2 )
  {
    if ( (signed int)(unsigned int8_t)bySlotIndex < 8 )
    {
      if ( _SFCONT_DB_BASE::_LIST::IsFilled((_SFCONT_DB_BASE::_LIST *)&v12->m_AvatorData.dbSfcont + 8 * (unsigned int8_t)byContCode + (unsigned int8_t)bySlotIndex) )
      {
        *(uint32_t*)pl_byLv = (unsigned int8_t)v14;
        CLogFile::Write(
          &stru_1799C8E78,
          "%s : Update_SFContInsert(EXIST) : code : %d, slot : %d",
          v12->m_aszAvatorName,
          (unsigned int8_t)v13);
        result = 0;
      }
      else
      {
        _SFCONT_DB_BASE::_LIST::SetKey(
          (_SFCONT_DB_BASE::_LIST *)&v12->m_AvatorData.dbSfcont + 8 * (unsigned int8_t)v13 + (unsigned int8_t)v14,
          0,
          v15,
          wEffectIndex,
          byLv,
          wDurSec);
        result = 1;
      }
    }
    else
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_SFContInsert(SlotIndex OVER) : slot : %d",
        v12->m_aszAvatorName,
        (unsigned int8_t)bySlotIndex);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_SFContInsert(byContCode OVER) : code : %d",
      v12->m_aszAvatorName,
      (unsigned int8_t)byContCode);
    result = 0;
  }
  return result;
}
