#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ScrollMapRight@CMapExtend@@QEAAXHH@Z
 * Address: 0x1401A2220

void  CMapExtend::ScrollMapRight(CMapExtend *this, int nMapX, int nInterval)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-38h]@1
  tagPOINT bottomRight; // [sp+20h] [bp-18h]@8
  tagPOINT topLeft; // [sp+28h] [bp-10h]@8
  CMapExtend *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( v8->m_bExtendMode )
  {
    if ( nInterval + v8->m_ptEndMap.x <= nMapX )
    {
      v8->m_ptStartMap.x += nInterval;
      v8->m_ptEndMap.x += nInterval;
      v8->m_ptCenter.x += nInterval;
    }
    else
    {
      v8->m_ptEndMap.x = nMapX;
      v8->m_ptStartMap.x = v8->m_ptEndMap.x - v8->m_sizeExtend.cx;
      v8->m_ptCenter.x = v8->m_ptEndMap.x - v8->m_sizeExtend.cx / 2;
    }
    bottomRight = (tagPOINT)v8->m_ptEndMap;
    topLeft = (tagPOINT)v8->m_ptStartMap;
    CRect::SetRect(&v8->m_rcExtend, topLeft, bottomRight);
  }
}
