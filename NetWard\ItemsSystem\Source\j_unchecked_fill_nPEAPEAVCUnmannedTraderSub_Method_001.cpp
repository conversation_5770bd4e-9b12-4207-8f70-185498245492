#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_fill_n@PEAPEAVCUnmannedTraderSubClassInfo@@_KPEAV1@@stdext@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@_KAEBQEAV1@@Z
 * Address: 0x140010E15

void  stdext::unchecked_fill_n<CUnmannedTraderSubClassInfo * *,unsigned int64_t,CUnmannedTraderSubClassInfo *>(CUnmannedTraderSubClassInfo **_First, unsigned int64_t _Count, CUnmannedTraderSubClassInfo *const *_Val)
{
  stdext::unchecked_fill_n<CUnmannedTraderSubClassInfo * *,unsigned int64_t,CUnmannedTraderSubClassInfo *>(
    _First,
    _Count,
    _<PERSON>);
}
