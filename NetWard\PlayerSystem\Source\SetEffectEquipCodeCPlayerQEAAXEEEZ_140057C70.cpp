#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetEffectEquipCode@CPlayer@@QEAAXEEE@Z
 * Address: 0x140057C70

void  CPlayer::SetEffectEquipCode(CPlayer *this, char byStorageCode, char bySlotIndex, char byCode)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char *v6; // [sp+0h] [bp-18h]@1
  CPlayer *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v4 = (int64_t *)&v6;
  for ( i = 4i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v6 = 0i64;
  if ( byStorageCode == 1 )
    v6 = &v7->m_byEffectEquipCode[(unsigned int8_t)bySlotIndex];
  else
    v6 = (char *)&(&v7->m_pRecordSet)[726] + (unsigned int8_t)bySlotIndex + 18;
  *v6 = byCode;
}
