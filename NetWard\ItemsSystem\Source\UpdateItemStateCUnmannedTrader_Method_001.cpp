#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?UpdateItemState@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034E100

char  CUnmannedTraderController::UpdateItemState(CUnmannedTraderController *this, char *pData)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v5; // [sp+0h] [bp-78h]@1
  _SYSTEMTIME *kCurTime; // [sp+20h] [bp-58h]@4
  char *v7; // [sp+30h] [bp-48h]@4
  char Dst; // [sp+48h] [bp-30h]@4

  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v7 = pData;
  memset_0(&Dst, 0, 0x10ui64);
  GetLocalTime((LPSYSTEMTIME)&Dst);
  kCurTime = (_SYSTEMTIME *)&Dst;
  if ( CRFWorldDatabase::Update_UnmannedTraderItemState(pkDB, *v7, *((uint32_t*)v7 + 1), v7[8], (_SYSTEMTIME *)&Dst) )
    result = 0;
  else
    result = 24;
  return result;
}
