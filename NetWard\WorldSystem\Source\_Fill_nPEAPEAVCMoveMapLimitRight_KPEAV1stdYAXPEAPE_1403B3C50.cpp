#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Fill_n@PEAPEAVCMoveMapLimitRight@@_KPEAV1@@std@@YAXPEAPEAVCMoveMapLimitRight@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403B3C50

void  std::_Fill_n<CMoveMapLimitRight * *,unsigned int64_t,CMoveMapLimitRight *>(CMoveMapLimitRight **_First, unsigned int64_t _Count, CMoveMapLimitRight *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  memset64(_First, (unsigned int64_t)*_Val, _Count);
}
