#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Loop@CUnmannedTraderTradeInfo@@QEAAXXZ
 * Address: 0x140392080

void  CUnmannedTraderTradeInfo::Loop(CUnmannedTraderTradeInfo *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-28h]@1
  CUnmannedTraderTradeInfo *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v4->m_pkTimer && CMyTimer::CountingTimer(v4->m_pkTimer) )
  {
    if ( IsDayChanged(&v4->m_iOldDay) )
    {
      CUnmannedTraderTradeInfo::UpdateIncome(v4);
      CUnmannedTraderTradeInfo::NotifyIncome(v4);
    }
    CUnmannedTraderTradeInfo::SaveINI(v4);
  }
}
