#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVCUnmannedTraderSortType@@_KPEAV1@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@stdext@@YAXPEAPEAVCUnmannedTraderSortType@@_KAEBQEAV1@AEAV?$allocator@PEAVCUnmannedTraderSortType@@@std@@@Z
 * Address: 0x14001145F

void  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderSortType * *,unsigned int64_t,CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(CUnmannedTraderSortType **_First, unsigned int64_t _Count, CUnmannedTraderSortType *const *_Val, std::allocator<CUnmannedTraderSortType *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderSortType * *,unsigned int64_t,CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
