#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CMapOperation::CMapOperation_::_1_::dtor$1
 * Address: 0x140196000

void  CMapOperation::CMapOperation_::_1_::dtor_1(int64_t a1, int64_t a2)
{
  std::vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>::~vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>((std::vector<std::pair<int,int>,std::allocator<std::pair<int,int> > > *)(*(uint64_t*)(a2 + 64) + 48i64));
}
