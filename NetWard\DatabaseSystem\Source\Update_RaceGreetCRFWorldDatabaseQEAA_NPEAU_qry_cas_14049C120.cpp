#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_RaceGreet@CRFWorldDatabase@@QEAA_NPEAU_qry_case_race_greetingmsg@@@Z
 * Address: 0x14049C120

bool  CRFWorldDatabase::Update_RaceGreet(CRFWorldDatabase *this, _qry_case_race_greetingmsg *pSheet)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-468h]@1
  int v6; // [sp+20h] [bp-448h]@4
  char Dest; // [sp+40h] [bp-428h]@4
  char v8; // [sp+41h] [bp-427h]@4
  unsigned int64_t v9; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+470h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 280i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v9 = (unsigned int64_t)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v8, 0, 0x3FFui64);
  v6 = pSheet->type;
  sprintf(
    &Dest,
    "update tbl_GreetMsg set Name='%s', GMsg='%s' where useType=%d",
    pSheet->in_bossname,
    pSheet->in_racegreetingmsg);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &Dest, 1);
}
