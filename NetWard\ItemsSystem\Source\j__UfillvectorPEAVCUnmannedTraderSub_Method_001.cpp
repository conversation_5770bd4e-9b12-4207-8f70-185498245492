#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Ufill@?$vector@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x14000D13E

CUnmannedTraderSubClassInfo ** std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Ufill(std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this, CUnmannedTraderSubClassInfo **_Ptr, unsigned int64_t _Count, CUnmannedTraderSubClassInfo *const *_Val)
{
  return std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Ufill(
           this,
           _Ptr,
           _Count,
           _Val);
}
