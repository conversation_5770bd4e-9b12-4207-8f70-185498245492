#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SearchPatrollPath@DfAIMgr@@SAXPEAVCMonsterAI@@PEAVCMonster@@@Z
 * Address: 0x1401532F0

void  DfAIMgr::SearchPatrollPath(CMonsterAI *pAI, CMonster *pMon)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-28h]@1
  CMonster *mon; // [sp+38h] [bp+10h]@1

  mon = pMon;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( CMonsterHelper::SearchPatrolMovePos(pMon, (float (*)[3])vTargetPos_0) )
    DfAIMgr::ChangeTargetPos(mon, vTargetPos_0);
}
