#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?size@_target_monster_contsf_allinform_zocl@@QEAAHXZ
 * Address: 0x1400F0140

signed int64_t  _target_monster_contsf_allinform_zocl::size(_target_monster_contsf_allinform_zocl *this)
{
  if ( this->byContCount > 8 )
    this->byContCount = 0;
  return 21 - 2i64 * (8 - this->byContCount);
}
