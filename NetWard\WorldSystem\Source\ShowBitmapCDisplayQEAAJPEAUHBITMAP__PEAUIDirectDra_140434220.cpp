#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ShowBitmap@CDisplay@@QEAAJPEAUHBITMAP__@@PEAUIDirectDrawPalette@@@Z
 * Address: 0x140434220

signed int64_t  CDisplay::ShowBitmap(CDisplay *this, HBITMAP__ *hbm, IDirectDrawPalette *pPalette)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  signed int64_t result; // rax@6
  int64_t v6; // [sp+0h] [bp-108h]@1
  CSurface v7; // [sp+40h] [bp-C8h]@9
  unsigned int v8; // [sp+E4h] [bp-24h]@10
  int v9; // [sp+E8h] [bp-20h]@11
  int64_t v10; // [sp+F0h] [bp-18h]@4
  CDisplay *v11; // [sp+110h] [bp+8h]@1
  HBITMAP__ *hBMP; // [sp+118h] [bp+10h]@1

  hBMP = hbm;
  v11 = this;
  v3 = &v6;
  for ( i = 64i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v10 = -2i64;
  if ( v11->m_pddsFrontBuffer && v11->m_pddsBackBuffer )
  {
    if ( pPalette )
      ((void ( *)(IDirectDrawSurface7 *, IDirectDrawPalette *))v11->m_pddsFrontBuffer->vfptr[10].AddRef)(
        v11->m_pddsFrontBuffer,
        pPalette);
    CSurface::CSurface(&v7);
    CSurface::Create(&v7, v11->m_pddsBackBuffer);
    if ( CSurface::DrawBitmap(&v7, hBMP, 0, 0, 0, 0) >= 0 )
    {
      v9 = CDisplay::Present(v11);
      CSurface::~CSurface(&v7);
      result = (unsigned int)v9;
    }
    else
    {
      v8 = -2147467259;
      CSurface::~CSurface(&v7);
      result = v8;
    }
  }
  else
  {
    result = 2147500035i64;
  }
  return result;
}
