#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?RegistCheatTableUnion@CNationSettingFactory@@IEAA_NPEAVCNationSettingData@@@Z
 * Address: 0x1402135B0

bool  CNationSettingFactory::RegistCheatTableUnion(CNationSettingFactory *this, CNationSettingData *pkData)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  bool result; // al@5
  int64_t v5; // [sp+0h] [bp-38h]@1
  CNationSettingFactory *v6; // [sp+40h] [bp+8h]@1
  CNationSettingData *pkDataa; // [sp+48h] [bp+10h]@1

  pkDataa = pkData;
  v6 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkData,
          "change degree",
          (bool ( *)(CPlayer *))ct_change_degree,
          31,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "respawn start",
          (bool ( *)(CPlayer *))ct_respawn_start,
          52,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "respawn stop",
          (bool ( *)(CPlayer *))ct_respawn_stop,
          52,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "¥",
          (bool ( *)(CPlayer *))ct_circle_mon_kill,
          20,
          6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "屺",
          (bool ( *)(CPlayer *))ct_look_like_boss,
          4,
          6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_change_mastery, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "",
          (bool ( *)(CPlayer *))ct_all_item_muzi,
          28,
          6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "allmuzi", (bool ( *)(CPlayer *))ct_all_item_muzi, 28, 6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_report_cri_hp, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "ǥ", (bool ( *)(CPlayer *))ct_report_position, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "xyz", (bool ( *)(CPlayer *))ct_report_position, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "ġ", (bool ( *)(CPlayer *))ct_alter_exp, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "altexp", (bool ( *)(CPlayer *))ct_alter_exp, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_all_map, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "jump", (bool ( *)(CPlayer *))ct_jump_to_pos, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_pass_dungeon, 20, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "pass dungeon",
          (bool ( *)(CPlayer *))ct_pass_dungeon,
          20,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "Ⱥ", (bool ( *)(CPlayer *))ct_tracing_hide, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "transparent",
          (bool ( *)(CPlayer *))ct_tracing_hide,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "ߺ", (bool ( *)(CPlayer *))ct_tracing_show, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "no transparent",
          (bool ( *)(CPlayer *))ct_tracing_show,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "max attack point",
          (bool ( *)(CPlayer *))ct_max_attack,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "min attack point",
          (bool ( *)(CPlayer *))ct_min_attack,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "normal attack point",
          (bool ( *)(CPlayer *))ct_mormal_attack,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_set_matchless, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "matchless",
          (bool ( *)(CPlayer *))ct_set_matchless,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "",
          (bool ( *)(CPlayer *))ct_release_matchless,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "no matchless",
          (bool ( *)(CPlayer *))ct_release_matchless,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "",
          (bool ( *)(CPlayer *))ct_goto_shipport_town,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "port",
          (bool ( *)(CPlayer *))ct_goto_shipport_town,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "Ҽ",
          (bool ( *)(CPlayer *))ct_goto_shipport_eder,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "eder",
          (bool ( *)(CPlayer *))ct_goto_shipport_eder,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "Ÿ", (bool ( *)(CPlayer *))ct_free_ride_ship, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "goto ship",
          (bool ( *)(CPlayer *))ct_free_ride_ship,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "ָ", (bool ( *)(CPlayer *))ct_pass_sch, 52, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_start_cri, 52, 6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "start holy", (bool ( *)(CPlayer *))ct_start_cri, 52, 6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "stone", (bool ( *)(CPlayer *))ct_start_cri, 52, 6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, " ȳ", (bool ( *)(CPlayer *))ct_exip_keeper, 52, 6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "stone bye", (bool ( *)(CPlayer *))ct_exip_keeper, 52, 6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "keeper bye",
          (bool ( *)(CPlayer *))ct_exip_keeper,
          52,
          6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_start_keeper, 52, 6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "servant", (bool ( *)(CPlayer *))ct_start_keeper, 52, 6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "start keeper",
          (bool ( *)(CPlayer *))ct_start_keeper,
          52,
          6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_goto_stone, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "goto stone", (bool ( *)(CPlayer *))ct_goto_stone, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "ư", (bool ( *)(CPlayer *))ct_exit_stone, 52, 6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_set_loot_free, 20, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "beggar", (bool ( *)(CPlayer *))ct_set_loot_free, 20, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "",
          (bool ( *)(CPlayer *))ct_release_loot_free,
          20,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "no beggar",
          (bool ( *)(CPlayer *))ct_release_loot_free,
          20,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "#", (bool ( *)(CPlayer *))ct_change_class, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "kick", (bool ( *)(CPlayer *))ct_kick_player, 52, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_complete_quest, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "pass quest",
          (bool ( *)(CPlayer *))ct_complete_quest,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "charcall",
          (bool ( *)(CPlayer *))ct_recall_player,
          22,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "moncall",
          (bool ( *)(CPlayer *))ct_recall_monster,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "ź", (bool ( *)(CPlayer *))ct_goto_mine, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "goto mine", (bool ( *)(CPlayer *))ct_goto_mine, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "Ǯ ", (bool ( *)(CPlayer *))ct_copy_avator, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "copy", (bool ( *)(CPlayer *))ct_copy_avator, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "? ", (bool ( *)(CPlayer *))ct_user_num, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "total", (bool ( *)(CPlayer *))ct_user_num, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "die", (bool ( *)(CPlayer *))ct_die, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_half_gauge, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "miracle", (bool ( *)(CPlayer *))ct_half_gauge, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "Ҳ", (bool ( *)(CPlayer *))ct_full_gauge, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "fullmiracle",
          (bool ( *)(CPlayer *))ct_full_gauge,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "κ",
          (bool ( *)(CPlayer *))ct_alter_inven_dur,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "׹",
          (bool ( *)(CPlayer *))ct_set_never_die,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "neverdie",
          (bool ( *)(CPlayer *))ct_set_never_die,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "׹",
          (bool ( *)(CPlayer *))ct_release_never_die,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "no neverdie",
          (bool ( *)(CPlayer *))ct_release_never_die,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "Ŭ",
          (bool ( *)(CPlayer *))ct_free_sf_by_class,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "lv", (bool ( *)(CPlayer *))ct_alter_lv, 30, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "upfcitem", (bool ( *)(CPlayer *))ct_up_forceitem, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "upskill", (bool ( *)(CPlayer *))ct_up_skill, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "upforce",
          (bool ( *)(CPlayer *))ct_up_forcemastery,
          28,
          4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "allskill", (bool ( *)(CPlayer *))ct_up_allskill, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "⿩", (bool ( *)(CPlayer *))ct_alter_pvp, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "contribute_p",
          (bool ( *)(CPlayer *))ct_alter_pvp,
          28,
          4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "⿩", (bool ( *)(CPlayer *))ct_alter_cashbag, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "contribute_m",
          (bool ( *)(CPlayer *))ct_alter_cashbag,
          28,
          4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "recalltime",
          (bool ( *)(CPlayer *))ct_animus_recall_term,
          28,
          4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "recallexp",
          (bool ( *)(CPlayer *))ct_set_animus_exp,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "monset", (bool ( *)(CPlayer *))ct_init_monster, 52, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "λ", (bool ( *)(CPlayer *))ct_full_force, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "full force", (bool ( *)(CPlayer *))ct_full_force, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "߸տ",
          (bool ( *)(CPlayer *))ct_loot_material,
          28,
          4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "Ÿ", (bool ( *)(CPlayer *))ct_loot_tower, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_loot_bag, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "threeseven", (bool ( *)(CPlayer *))ct_loot_bag, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "δ", (bool ( *)(CPlayer *))ct_loot_mine, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "α׸", (bool ( *)(CPlayer *))ct_inven_empty, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "clear inven",
          (bool ( *)(CPlayer *))ct_inven_empty,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "Ǽ", (bool ( *)(CPlayer *))ct_set_make_succ, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "god hand",
          (bool ( *)(CPlayer *))ct_set_make_succ,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "Ǽ",
          (bool ( *)(CPlayer *))ct_release_make_succ,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "chicken hand",
          (bool ( *)(CPlayer *))ct_release_make_succ,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "ȿð",
          (bool ( *)(CPlayer *))ct_cont_effet_time,
          28,
          4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "effect time",
          (bool ( *)(CPlayer *))ct_cont_effet_time,
          28,
          4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "ȿα׸",
          (bool ( *)(CPlayer *))ct_cont_effet_clear,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "effect clear",
          (bool ( *)(CPlayer *))ct_cont_effet_clear,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "Ĭ", (bool ( *)(CPlayer *))ct_view_method, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "Ŵ",
          (bool ( *)(CPlayer *))ct_make_system_tower,
          16,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "׸Ŵ",
          (bool ( *)(CPlayer *))ct_destroy_system_tower,
          16,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "ֺӸ",
          (bool ( *)(CPlayer *))ct_circle_user_num,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "circle",
          (bool ( *)(CPlayer *))ct_circle_user_num,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "",
          (bool ( *)(CPlayer *))ct_boss_sms_cancel,
          36,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "âα׸", (bool ( *)(CPlayer *))ct_trunk_init, 36, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "fullset", (bool ( *)(CPlayer *))ct_fullset, 30, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "ۿ", (bool ( *)(CPlayer *))ct_loot_upgrade, 28, 4) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_loot_dungeon, 28, 6) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "ѹ޾",
          (bool ( *)(CPlayer *))ct_defense_item_grace,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "dfgrace",
          (bool ( *)(CPlayer *))ct_defense_item_grace_Jp,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "recallattack",
          (bool ( *)(CPlayer *))ct_animus_attack_grade,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_telekinesis, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "allskillpt",
          (bool ( *)(CPlayer *))ct_up_allskill_pt,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "ûҳ",
          (bool ( *)(CPlayer *))ct_complete_quest_other,
          30,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "ҵ",
          (bool ( *)(CPlayer *))ct_loot_upgrade_item,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "Ȱ", (bool ( *)(CPlayer *))ct_resurrect_player, 28, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "resurrect",
          (bool ( *)(CPlayer *))ct_resurrect_player,
          28,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "",
          (bool ( *)(CPlayer *))ct_add_guild_schedule,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "Į",
          (bool ( *)(CPlayer *))ct_set_guildbattle_color,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "Į",
          (bool ( *)(CPlayer *))ct_cur_guildbattle_color,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "createfield",
          (bool ( *)(CPlayer *))ct_create_guildbattle_field_object,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "destroyfield",
          (bool ( *)(CPlayer *))ct_destroy_guildbattle_field_object,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "regenstone",
          (bool ( *)(CPlayer *))ct_regen_gravitystone,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "destroystone",
          (bool ( *)(CPlayer *))ct_destroy_gravitystone,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "takestone",
          (bool ( *)(CPlayer *))ct_take_gravitystone,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "getstone",
          (bool ( *)(CPlayer *))ct_get_gravitystone,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "dropstone",
          (bool ( *)(CPlayer *))ct_drop_gravitystone,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "forcestone",
          (bool ( *)(CPlayer *))ct_guild_battle_force_stone,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "checkgoal",
          (bool ( *)(CPlayer *))ct_check_guild_batlle_goal,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "recvschedule",
          (bool ( *)(CPlayer *))ct_recv_reserved_schedulelist,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "recvbattleinfo",
          (bool ( *)(CPlayer *))ct_recv_current_battle_info,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "",
          (bool ( *)(CPlayer *))ct_add_one_day_guild_schedule,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "Ȱ", (bool ( *)(CPlayer *))ct_guild_suggest, 8, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_guild_info, 8, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "recvtotalguildrank",
          (bool ( *)(CPlayer *))ct_recv_total_guild_rank,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "recvpvpguildrank",
          (bool ( *)(CPlayer *))ct_recv_pvp_guild_rank,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "changetaxrate",
          (bool ( *)(CPlayer *))ct_recv_change_atrad_taxrate,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "հ",
          (bool ( *)(CPlayer *))ct_combine_ex_result,
          8,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "amp-set", (bool ( *)(CPlayer *))ct_amp_set, 52, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "amp-full", (bool ( *)(CPlayer *))ct_amp_full, 52, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "servertime",
          (bool ( *)(CPlayer *))ct_server_time,
          52,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "partycall", (bool ( *)(CPlayer *))ct_party_call, 52, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "guildcall", (bool ( *)(CPlayer *))ct_guild_call, 52, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "cashamount",
          (bool ( *)(CPlayer *))ct_loadcashamount,
          20,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "csbuy", (bool ( *)(CPlayer *))ct_cashitembuy, 20, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "primium", (bool ( *)(CPlayer *))ct_PcBandPrimium, 20, 7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "classevent",
          (bool ( *)(CPlayer *))ct_ClassRefineEvent,
          20,
          7) )
    return 0;
  if ( !CNationSettingFactory::RegistCheat(
          v6,
          pkDataa,
          "takeholymental",
          (bool ( *)(CPlayer *))ct_takeholymental,
          52,
          7) )
    return 0;
  if ( CMainThread::IsReleaseServiceMode(&g_Main) )
  {
    if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_HolySystem, 20, 7) )
      return 0;
    if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "war", (bool ( *)(CPlayer *))ct_HolySystem_Jp, 52, 7) )
      return 0;
    if ( !CNationSettingFactory::RegistCheat(
            v6,
            pkDataa,
            "Ű۰",
            (bool ( *)(CPlayer *))ct_HolyKeeperAttack,
            20,
            7) )
      return 0;
  }
  else
  {
    if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "", (bool ( *)(CPlayer *))ct_HolySystem, 52, 7) )
      return 0;
    if ( !CNationSettingFactory::RegistCheat(v6, pkDataa, "war", (bool ( *)(CPlayer *))ct_HolySystem_Jp, 52, 7) )
      return 0;
    if ( !CNationSettingFactory::RegistCheat(
            v6,
            pkDataa,
            "Ű۰",
            (bool ( *)(CPlayer *))ct_HolyKeeperAttack,
            52,
            7) )
      return 0;
  }
  if ( CNationSettingFactory::RegistCheat(
         v6,
         pkDataa,
         "弱ڵ",
         (bool ( *)(CPlayer *))ct_SetPatriarchAuto,
         4,
         7) )
  {
    if ( CNationSettingFactory::RegistCheat(
           v6,
           pkDataa,
           "弱",
           (bool ( *)(CPlayer *))ct_SetPatriarchProcessor,
           4,
           7) )
    {
      if ( CNationSettingFactory::RegistCheat(
             v6,
             pkDataa,
             "弱",
             (bool ( *)(CPlayer *))ct_InformPatriarchProcessor,
             4,
             7) )
      {
        if ( CNationSettingFactory::RegistCheat(
               v6,
               pkDataa,
               "ʱȭ",
               (bool ( *)(CPlayer *))ct_PvpLimitInit,
               52,
               7) )
        {
          if ( CNationSettingFactory::RegistCheat(
                 v6,
                 pkDataa,
                 "recalllv",
                 (bool ( *)(CPlayer *))ct_set_animus_lv,
                 28,
                 7) )
          {
            if ( CNationSettingFactory::RegistCheat(
                   v6,
                   pkDataa,
                   "ִϸӽҲ",
                   (bool ( *)(CPlayer *))ct_full_animus_gauge,
                   28,
                   7) )
            {
              if ( CNationSettingFactory::RegistCheat(
                     v6,
                     pkDataa,
                     "AniPow",
                     (bool ( *)(CPlayer *))ct_full_animus_gauge,
                     28,
                     7) )
              {
                if ( CNationSettingFactory::RegistCheat(
                       v6,
                       pkDataa,
                       "",
                       (bool ( *)(CPlayer *))ct_NuAfterEffect,
                       52,
                       7) )
                {
                  if ( CNationSettingFactory::RegistCheat(
                         v6,
                         pkDataa,
                         "AfterEff",
                         (bool ( *)(CPlayer *))ct_NuAfterEffect,
                         52,
                         7) )
                  {
                    if ( CNationSettingFactory::RegistCheat(
                           v6,
                           pkDataa,
                           "ĳ",
                           (bool ( *)(CPlayer *))ct_CdeEndup,
                           28,
                           7) )
                    {
                      if ( CNationSettingFactory::RegistCheat(
                             v6,
                             pkDataa,
                             "removesfdelay",
                             (bool ( *)(CPlayer *))ct_remove_sf_delay,
                             28,
                             7) )
                      {
                        if ( CNationSettingFactory::RegistCheat(
                               v6,
                               pkDataa,
                               "utcancellogout",
                               (bool ( *)(CPlayer *))ct_ut_cancel_registlogout,
                               28,
                               7) )
                        {
                          if ( CNationSettingFactory::RegistCheat(
                                 v6,
                                 pkDataa,
                                 "utcancel",
                                 (bool ( *)(CPlayer *))ct_ut_cancel_regist,
                                 28,
                                 7) )
                          {
                            if ( CNationSettingFactory::RegistCheat(
                                   v6,
                                   pkDataa,
                                   "ĳ̺Ʈ",
                                   (bool ( *)(CPlayer *))ct_CashEventStart,
                                   28,
                                   7) )
                            {
                              if ( CNationSettingFactory::RegistCheat(
                                     v6,
                                     pkDataa,
                                     "chatsave",
                                     (bool ( *)(CPlayer *))ct_chatsave,
                                     28,
                                     7) )
                              {
                                if ( CNationSettingFactory::RegistCheat(
                                       v6,
                                       pkDataa,
                                       "drop",
                                       (bool ( *)(CPlayer *))ct_drop_jade,
                                       28,
                                       7) )
                                {
                                  if ( CNationSettingFactory::RegistCheat(
                                         v6,
                                         pkDataa,
                                         "oreamount",
                                         (bool ( *)(CPlayer *))ct_set_ore_amount,
                                         28,
                                         4) )
                                  {
                                    if ( CNationSettingFactory::RegistCheat(
                                           v6,
                                           pkDataa,
                                           "remainore",
                                           (bool ( *)(CPlayer *))ct_query_remain_ore,
                                           28,
                                           7) )
                                    {
                                      if ( CNationSettingFactory::RegistCheat(
                                             v6,
                                             pkDataa,
                                             "׵",
                                             (bool ( *)(CPlayer *))ct_continue_palytime_inc,
                                             28,
                                             7) )
                                      {
                                        if ( CNationSettingFactory::RegistCheat(
                                               v6,
                                               pkDataa,
                                               "serverrate",
                                               (bool ( *)(CPlayer *))ct_server_rate,
                                               28,
                                               7) )
                                        {
                                          if ( CNationSettingFactory::RegistCheat(
                                                 v6,
                                                 pkDataa,
                                                 "premiumrate",
                                                 (bool ( *)(CPlayer *))ct_premium_rate,
                                                 28,
                                                 7) )
                                          {
                                            if ( CNationSettingFactory::RegistCheat(
                                                   v6,
                                                   pkDataa,
                                                   "trapattack",
                                                   (bool ( *)(CPlayer *))ct_trap_attack_grade,
                                                   28,
                                                   7) )
                                            {
                                              if ( CNationSettingFactory::RegistCheat(
                                                     v6,
                                                     pkDataa,
                                                     "hitme",
                                                     (bool ( *)(CPlayer *))ct_set_damage_part,
                                                     28,
                                                     7) )
                                              {
                                                if ( CNationSettingFactory::RegistCheat(
                                                       v6,
                                                       pkDataa,
                                                       "eventset start",
                                                       (bool ( *)(CPlayer *))ct_eventset_start,
                                                       52,
                                                       7) )
                                                {
                                                  if ( CNationSettingFactory::RegistCheat(
                                                         v6,
                                                         pkDataa,
                                                         "eventset stop",
                                                         (bool ( *)(CPlayer *))ct_eventset_stop,
                                                         52,
                                                         7) )
                                                  {
                                                    if ( CNationSettingFactory::RegistCheat(
                                                           v6,
                                                           pkDataa,
                                                           "temp point",
                                                           (bool ( *)(CPlayer *))ct_set_temp_cash_point,
                                                           28,
                                                           7) )
                                                    {
                                                      if ( CNationSettingFactory::RegistCheat(
                                                             v6,
                                                             pkDataa,
                                                             "new killerlist",
                                                             (bool ( *)(CPlayer *))ct_set_kill_list_init,
                                                             28,
                                                             7) )
                                                      {
                                                        if ( CNationSettingFactory::RegistCheat(
                                                               v6,
                                                               pkDataa,
                                                               "buf",
                                                               (bool ( *)(CPlayer *))ct_buf_potion_use,
                                                               28,
                                                               7) )
                                                        {
                                                          if ( CNationSettingFactory::RegistCheat(
                                                                 v6,
                                                                 pkDataa,
                                                                 "lua",
                                                                 (bool ( *)(CPlayer *))ct_lua_command,
                                                                 20,
                                                                 7) )
                                                          {
                                                            if ( CNationSettingFactory::RegistCheat(
                                                                   v6,
                                                                   pkDataa,
                                                                   "userchatban",
                                                                   (bool ( *)(CPlayer *))ct_userchatban,
                                                                   20,
                                                                   7) )
                                                            {
                                                              if ( CNationSettingFactory::RegistCheat(
                                                                     v6,
                                                                     pkDataa,
                                                                     "itemloot",
                                                                     (bool ( *)(CPlayer *))ct_itemloot,
                                                                     16,
                                                                     7) )
                                                              {
                                                                if ( CNationSettingFactory::RegistCheat(
                                                                       v6,
                                                                       pkDataa,
                                                                       "minespeed",
                                                                       (bool ( *)(CPlayer *))ct_minespeed,
                                                                       16,
                                                                       7) )
                                                                {
                                                                  if ( CNationSettingFactory::RegistCheat(
                                                                         v6,
                                                                         pkDataa,
                                                                         "sfmastery",
                                                                         (bool ( *)(CPlayer *))ct_sfmastery,
                                                                         16,
                                                                         7) )
                                                                  {
                                                                    if ( CNationSettingFactory::RegistCheat(
                                                                           v6,
                                                                           pkDataa,
                                                                           "basemastery",
                                                                           (bool ( *)(CPlayer *))ct_basemastery,
                                                                           16,
                                                                           7) )
                                                                    {
                                                                      if ( CNationSettingFactory::RegistCheat(
                                                                             v6,
                                                                             pkDataa,
                                                                             "animuexp",
                                                                             (bool ( *)(CPlayer *))ct_animusexp,
                                                                             16,
                                                                             7) )
                                                                      {
                                                                        if ( CNationSettingFactory::RegistCheat(
                                                                               v6,
                                                                               pkDataa,
                                                                               "playerexp",
                                                                               (bool ( *)(CPlayer *))ct_playerexp,
                                                                               16,
                                                                               7) )
                                                                        {
                                                                          if ( CNationSettingFactory::RegistCheat(
                                                                                 v6,
                                                                                 pkDataa,
                                                                                 "darkholereward",
                                                                                 (bool ( *)(CPlayer *))ct_darkholereward,
                                                                                 16,
                                                                                 7) )
                                                                          {
                                                                            if ( CNationSettingFactory::RegistCheat(
                                                                                   v6,
                                                                                   pkDataa,
                                                                                   "pcitemloot",
                                                                                   (bool ( *)(CPlayer *))ct_pcitemloot,
                                                                                   16,
                                                                                   7) )
                                                                            {
                                                                              if ( CNationSettingFactory::RegistCheat(
                                                                                     v6,
                                                                                     pkDataa,
                                                                                     "pcminespeed",
                                                                                     (bool ( *)(CPlayer *))ct_pcminespeed,
                                                                                     16,
                                                                                     7) )
                                                                              {
                                                                                if ( CNationSettingFactory::RegistCheat(
                                                                                       v6,
                                                                                       pkDataa,
                                                                                       "pcsfmastery",
                                                                                       (bool ( *)(CPlayer *))ct_pcsfmastery,
                                                                                       16,
                                                                                       7) )
                                                                                {
                                                                                  if ( CNationSettingFactory::RegistCheat(
                                                                                         v6,
                                                                                         pkDataa,
                                                                                         "pcbasemastery",
                                                                                         (bool ( *)(CPlayer *))ct_pcbasemastery,
                                                                                         16,
                                                                                         7) )
                                                                                  {
                                                                                    if ( CNationSettingFactory::RegistCheat(
                                                                                           v6,
                                                                                           pkDataa,
                                                                                           "pcanimuexp",
                                                                                           (bool ( *)(CPlayer *))ct_pcanimusexp,
                                                                                           16,
                                                                                           7) )
                                                                                    {
                                                                                      if ( CNationSettingFactory::RegistCheat(
                                                                                             v6,
                                                                                             pkDataa,
                                                                                             "pcplayerexp",
                                                                                             (bool ( *)(CPlayer *))ct_pcplayerexp,
                                                                                             16,
                                                                                             7) )
                                                                                      {
                                                                                        if ( CNationSettingFactory::RegistCheat(
                                                                                               v6,
                                                                                               pkDataa,
                                                                                               "mepcbang",
                                                                                               (bool ( *)(CPlayer *))ct_mepcbang,
                                                                                               28,
                                                                                               7) )
                                                                                        {
                                                                                          if ( CNationSettingFactory::RegistCheat(
                                                                                                 v6,
                                                                                                 pkDataa,
                                                                                                 "pcbangitemget",
                                                                                                 (bool ( *)(CPlayer *))ct_pcbangitemget,
                                                                                                 28,
                                                                                                 7) )
                                                                                          {
                                                                                            if ( CNationSettingFactory::RegistCheat(
                                                                                                   v6,
                                                                                                   pkDataa,
                                                                                                   "expirepcbang",
                                                                                                   (bool ( *)(CPlayer *))ct_expire_pcbang,
                                                                                                   28,
                                                                                                   7) )
                                                                                            {
                                                                                              if ( CNationSettingFactory::RegistCheat(
                                                                                                     v6,
                                                                                                     pkDataa,
                                                                                                     "voteenable",
                                                                                                     (bool ( *)(CPlayer *))ct_vote_enable,
                                                                                                     28,
                                                                                                     7) )
                                                                                              {
                                                                                                if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "voteinfo", (bool ( *)(CPlayer *))ct_elect_info_player, 28, 7) )
                                                                                                {
                                                                                                  if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "playerset", (bool ( *)(CPlayer *))ct_elect_set_player, 28, 7) )
                                                                                                  {
                                                                                                    if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "envset", (bool ( *)(CPlayer *))ct_elect_set_env, 28, 7) )
                                                                                                    {
                                                                                                      if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "timeset", (bool ( *)(CPlayer *))ct_period_time_set, 28, 7) )
                                                                                                      {
                                                                                                        if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "tlinfoset", (bool ( *)(CPlayer *))ct_tl_info_set, 28, 7) )
                                                                                                        {
                                                                                                          if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "tlinfo", (bool ( *)(CPlayer *))ct_tl_info_view, 28, 7) )
                                                                                                          {
                                                                                                            if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "tlsysset", (bool ( *)(CPlayer *))ct_tl_system_setting, 28, 7) )
                                                                                                            {
                                                                                                              if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "actpset", (bool ( *)(CPlayer *))ct_action_point_set, 28, 7) )
                                                                                                              {
                                                                                                                if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "¸", (bool ( *)(CPlayer *))ct_Win_RaceWar, 28, 7) )
                                                                                                                {
                                                                                                                  if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "eventst", (bool ( *)(CPlayer *))ct_Gold_Age_Event_Status, 28, 7) )
                                                                                                                  {
                                                                                                                    if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "setevent", (bool ( *)(CPlayer *))ct_Gold_Age_Set_Event_Status, 28, 7) )
                                                                                                                    {
                                                                                                                      if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "getboxcnt", (bool ( *)(CPlayer *))ct_Gold_Age_Get_Box_Cnt, 28, 7) )
                                                                                                                      {
                                                                                                                        if ( CNationSettingFactory::RegistCheat(v6, pkDataa, "sethp", (bool ( *)(CPlayer *))ct_set_hp, 28, 7) )
                                                                                                                          result = CNationSettingFactory::RegistCheat(v6, pkDataa, "hfs full", (bool ( *)(CPlayer *))ct_set_hfs_full, 28, 7) != 0;
                                                                                                                        else
                                                                                                                          result = 0;
                                                                                                                      }
                                                                                                                      else
                                                                                                                      {
                                                                                                                        result = 0;
                                                                                                                      }
                                                                                                                    }
                                                                                                                    else
                                                                                                                    {
                                                                                                                      result = 0;
                                                                                                                    }
                                                                                                                  }
                                                                                                                  else
                                                                                                                  {
                                                                                                                    result = 0;
                                                                                                                  }
                                                                                                                }
                                                                                                                else
                                                                                                                {
                                                                                                                  result = 0;
                                                                                                                }
                                                                                                              }
                                                                                                              else
                                                                                                              {
                                                                                                                result = 0;
                                                                                                              }
                                                                                                            }
                                                                                                            else
                                                                                                            {
                                                                                                              result = 0;
                                                                                                            }
                                                                                                          }
                                                                                                          else
                                                                                                          {
                                                                                                            result = 0;
                                                                                                          }
                                                                                                        }
                                                                                                        else
                                                                                                        {
                                                                                                          result = 0;
                                                                                                        }
                                                                                                      }
                                                                                                      else
                                                                                                      {
                                                                                                        result = 0;
                                                                                                      }
                                                                                                    }
                                                                                                    else
                                                                                                    {
                                                                                                      result = 0;
                                                                                                    }
                                                                                                  }
                                                                                                  else
                                                                                                  {
                                                                                                    result = 0;
                                                                                                  }
                                                                                                }
                                                                                                else
                                                                                                {
                                                                                                  result = 0;
                                                                                                }
                                                                                              }
                                                                                              else
                                                                                              {
                                                                                                result = 0;
                                                                                              }
                                                                                            }
                                                                                            else
                                                                                            {
                                                                                              result = 0;
                                                                                            }
                                                                                          }
                                                                                          else
                                                                                          {
                                                                                            result = 0;
                                                                                          }
                                                                                        }
                                                                                        else
                                                                                        {
                                                                                          result = 0;
                                                                                        }
                                                                                      }
                                                                                      else
                                                                                      {
                                                                                        result = 0;
                                                                                      }
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                      result = 0;
                                                                                    }
                                                                                  }
                                                                                  else
                                                                                  {
                                                                                    result = 0;
                                                                                  }
                                                                                }
                                                                                else
                                                                                {
                                                                                  result = 0;
                                                                                }
                                                                              }
                                                                              else
                                                                              {
                                                                                result = 0;
                                                                              }
                                                                            }
                                                                            else
                                                                            {
                                                                              result = 0;
                                                                            }
                                                                          }
                                                                          else
                                                                          {
                                                                            result = 0;
                                                                          }
                                                                        }
                                                                        else
                                                                        {
                                                                          result = 0;
                                                                        }
                                                                      }
                                                                      else
                                                                      {
                                                                        result = 0;
                                                                      }
                                                                    }
                                                                    else
                                                                    {
                                                                      result = 0;
                                                                    }
                                                                  }
                                                                  else
                                                                  {
                                                                    result = 0;
                                                                  }
                                                                }
                                                                else
                                                                {
                                                                  result = 0;
                                                                }
                                                              }
                                                              else
                                                              {
                                                                result = 0;
                                                              }
                                                            }
                                                            else
                                                            {
                                                              result = 0;
                                                            }
                                                          }
                                                          else
                                                          {
                                                            result = 0;
                                                          }
                                                        }
                                                        else
                                                        {
                                                          result = 0;
                                                        }
                                                      }
                                                      else
                                                      {
                                                        result = 0;
                                                      }
                                                    }
                                                    else
                                                    {
                                                      result = 0;
                                                    }
                                                  }
                                                  else
                                                  {
                                                    result = 0;
                                                  }
                                                }
                                                else
                                                {
                                                  result = 0;
                                                }
                                              }
                                              else
                                              {
                                                result = 0;
                                              }
                                            }
                                            else
                                            {
                                              result = 0;
                                            }
                                          }
                                          else
                                          {
                                            result = 0;
                                          }
                                        }
                                        else
                                        {
                                          result = 0;
                                        }
                                      }
                                      else
                                      {
                                        result = 0;
                                      }
                                    }
                                    else
                                    {
                                      result = 0;
                                    }
                                  }
                                  else
                                  {
                                    result = 0;
                                  }
                                }
                                else
                                {
                                  result = 0;
                                }
                              }
                              else
                              {
                                result = 0;
                              }
                            }
                            else
                            {
                              result = 0;
                            }
                          }
                          else
                          {
                            result = 0;
                          }
                        }
                        else
                        {
                          result = 0;
                        }
                      }
                      else
                      {
                        result = 0;
                      }
                    }
                    else
                    {
                      result = 0;
                    }
                  }
                  else
                  {
                    result = 0;
                  }
                }
                else
                {
                  result = 0;
                }
              }
              else
              {
                result = 0;
              }
            }
            else
            {
              result = 0;
            }
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
