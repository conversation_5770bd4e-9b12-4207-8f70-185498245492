#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?Regist@CUnmannedTraderClassInfoFactory@@QEAA_NPEAVCUnmannedTraderClassInfo@@@Z
 * Address: 0x140004BA6

bool  CUnmannedTraderClassInfoFactory::Regist(CUnmannedTraderClassInfoFactory *this, CUnmannedTraderClassInfo *pkType)
{
  return CUnmannedTraderClassInfoFactory::Regist(this, pkType);
}
