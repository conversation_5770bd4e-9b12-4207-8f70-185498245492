#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetAutoCommitMode@CRFNewDatabase@@QEAAX_N@Z
 * Address: 0x140487440

void  CRFNewDatabase::SetAutoCommitMode(CRFNewDatabase *this, bool bAutoCommit)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-28h]@1
  CRFNewDatabase *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( bAutoCommit )
    SQLSetConnectAttr_0(v5->m_hDbc, 102, (SQLPOINTER)1, 0);
  else
    SQLSetConnectAttr_0(v5->m_hDbc, 102, 0i64, 0);
}
