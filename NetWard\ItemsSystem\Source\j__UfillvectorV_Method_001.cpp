#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Ufill@?$vector@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV3@_KAEBV3@@Z
 * Address: 0x14000C743

CUnmannedTraderGroupDivisionVersionInfo * std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Ufill(std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, CUnmannedTraderGroupDivisionVersionInfo *_Ptr, unsigned int64_t _Count, CUnmannedTraderGroupDivisionVersionInfo *_Val)
{
  return std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Ufill(
           this,
           _Ptr,
           _Count,
           _Val);
}
