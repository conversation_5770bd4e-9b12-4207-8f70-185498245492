#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Insert_n_::_1_::catch$1
 * Address: 0x1403B1340

void  __noreturn std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Insert_n_::_1_::catch_1(int64_t a1, int64_t a2)
{
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Destroy(
    *(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > **)(a2 + 192),
    (CMoveMapLimitRightInfo *)(*(uint64_t*)(*(uint64_t*)(a2 + 200) + 16i64) + 40i64 * *(uint64_t*)(a2 + 208)),
    (CMoveMapLimitRightInfo *)(*(uint64_t*)(*(uint64_t*)(a2 + 192) + 24i64) + 40i64 * *(uint64_t*)(a2 + 208)));
  CxxThrowException_0(0i64, 0i64);
}
