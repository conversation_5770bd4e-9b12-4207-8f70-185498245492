#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_TrunkMoney@CUserDB@@QEAA_NNN@Z
 * Address: 0x140117040

char  CUserDB::Update_TrunkMoney(CUserDB *this, long double dGold, long double dDalant)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  _TRUNK_DB_BASE *v6; // [sp+0h] [bp-18h]@1
  CUserDB *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = (int64_t *)&v6;
  for ( i = 4i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v6 = &v7->m_AvatorData.dbTrunk;
  v7->m_AvatorData.dbTrunk.dGold = dGold;
  v6->dDalant = dDalant;
  v7->m_bDataUpdate = 1;
  return 1;
}
