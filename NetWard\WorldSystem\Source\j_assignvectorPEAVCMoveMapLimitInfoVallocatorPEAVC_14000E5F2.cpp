#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?assign@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEAAX_KAEBQEAVCMoveMapLimitInfo@@@Z
 * Address: 0x14000E5F2

void  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::assign(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, unsigned int64_t _Count, CMoveMapLimitInfo *const *_Val)
{
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::assign(this, _Count, _<PERSON>);
}
