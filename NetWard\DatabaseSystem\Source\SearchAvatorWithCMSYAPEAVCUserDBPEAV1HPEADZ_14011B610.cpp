#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SearchAvatorWithCMS@@YAPEAVCUserDB@@PEAV1@HPEAD@Z
 * Address: 0x14011B610

CUserDB * SearchAvatorWithCMS(CUserDB *pList, int nMax, char *pCMS)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CUserDB *v8; // [sp+40h] [bp+8h]@1
  int v9; // [sp+48h] [bp+10h]@1
  const char *Str2; // [sp+50h] [bp+18h]@1

  Str2 = pCMS;
  v9 = nMax;
  v8 = pList;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  for ( j = 0; j < v9; ++j )
  {
    if ( v8[j].m_bActive && v8[j].m_bField && !strcmp_0(v8[j].m_BillingInfo.szCMS, Str2) )
      return &v8[j];
  }
  return 0i64;
}
