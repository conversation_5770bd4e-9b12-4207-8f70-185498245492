#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Unchecked_move_backward@PEAPEAVCLogTypeDBTask@@PEAPEAV1@@stdext@@YAPEAPEAVCLogTypeDBTask@@PEAPEAV1@00@Z
 * Address: 0x1400127BA

CLogTypeDBTask ** stdext::_Unchecked_move_backward<CLogTypeDBTask * *,CLogTypeDBTask * *>(CLogTypeDBTask **_First, CLogTypeDBTask **_Last, CLogTypeDBTask **_Dest)
{
  return stdext::_Unchecked_move_backward<CLogTypeDBTask * *,CLogTypeDBTask * *>(_First, _Last, _Dest);
}
