#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Construct@PEAVCLogTypeDBTask@@PEAV1@@std@@YAXPEAPEAVCLogTypeDBTask@@AEBQEAV1@@Z
 * Address: 0x1402C83C0

void  std::_Construct<CLogTypeDBTask *,CLogTypeDBTask *>(CLogTypeDBTask **_Ptr, CLogTypeDBTask *const *_Val)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-48h]@1
  void *_Where; // [sp+20h] [bp-28h]@4
  uint64_t*v6; // [sp+28h] [bp-20h]@4
  CLogTypeDBTask **v7; // [sp+50h] [bp+8h]@1
  CLogTypeDBTask *const *v8; // [sp+58h] [bp+10h]@1

  v8 = _Val;
  v7 = _Ptr;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  _Where = v7;
  v6 = operator new(8ui64, v7);
  if ( v6 )
    *v6 = *v8;
}
