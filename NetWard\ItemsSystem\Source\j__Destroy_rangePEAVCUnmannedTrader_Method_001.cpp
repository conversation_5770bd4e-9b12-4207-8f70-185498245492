#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Destroy_range@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@YAXPEAPEAVCUnmannedTraderClassInfo@@0AEAV?$allocator@PEAVCUnmannedTraderClassInfo@@@0@U_Scalar_ptr_iterator_tag@0@@Z
 * Address: 0x14000A777

void  std::_Destroy_range<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, std::allocator<CUnmannedTraderClassInfo *> *_Al, std::_Scalar_ptr_iterator_tag __formal)
{
  std::_Destroy_range<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
    _First,
    _Last,
    _Al,
    __formal);
}
