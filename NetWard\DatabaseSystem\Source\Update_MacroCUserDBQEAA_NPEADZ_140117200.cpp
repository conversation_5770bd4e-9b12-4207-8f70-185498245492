#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_Macro@CUserDB@@QEAA_NPEAD@Z
 * Address: 0x140117200

char  CUserDB::Update_Macro(CUserDB *this, char *pBuf)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-58h]@1
  char *v6; // [sp+20h] [bp-38h]@21
  char *v7; // [sp+30h] [bp-28h]@4
  _AIOC_A_MACRODATA *v8; // [sp+38h] [bp-20h]@4
  int k; // [sp+40h] [bp-18h]@6
  int j; // [sp+44h] [bp-14h]@4
  CUserDB *v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v7 = pBuf;
  v8 = &v11->m_AvatorData.dbMacro;
  for ( j = 0; j < 1; ++j )
  {
    for ( k = 0; k < 3; ++k )
    {
      v8->mcr_Potion[j].Potion[k] = *(uint32_t*)&v7[4 * k];
      v8->mcr_Potion[j].PotionValue[k] = *(uint32_t*)&v7[4 * k + 12];
    }
  }
  for ( j = 0; j < 3; ++j )
  {
    for ( k = 0; k < 10; ++k )
      v8->mcr_Action[j].Action[k] = *(uint32_t*)&v7[4 * (k + 10 * j) + 24];
  }
  for ( j = 0; j < 2; ++j )
  {
    for ( k = 0; k < 5; ++k )
    {
      if ( !IsSQLValidString(&v7[405 * j + 144] + 81 * k) )
      {
        v6 = &v7[405 * j + 144] + 81 * k;
        CLogFile::Write(
          &stru_1799C8E78,
          "%u(%s) CUserDB::Update_Macro() : ::IsSQLValidString( pMacro->chatting[iBelt][iSock](%s) ) Invalid!",
          v11->m_dwSerial,
          v11->m_aszAvatorName);
        return 1;
      }
      memset_0((char *)&v8->mcr_Chat[j] + 256 * (signed int64_t)k, 0, 0x51ui64);
      strncpy((char *)&v8->mcr_Chat[j] + 256 * (signed int64_t)k, &v7[405 * j + 144] + 81 * k, 0x51ui64);
    }
  }
  v11->m_bDataUpdate = 1;
  return 1;
}
