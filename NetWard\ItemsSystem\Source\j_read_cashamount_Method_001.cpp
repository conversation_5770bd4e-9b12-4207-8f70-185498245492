#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?read_cashamount@CMgrAvatorItemHistory@@QEAAXKKHPEAD@Z
 * Address: 0x14001002D

void  CMgrAvatorItemHistory::read_cashamount(CMgrAvatorItemHistory *this, unsigned int dwAC, unsigned int dwAV, int nCash, char *pFileName)
{
  CMgrAvatorItemHistory::read_cashamount(this, dwAC, dwAV, nCash, pFileName);
}
