#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?ReRegist@CUnmannedTraderUserInfo@@QEAAXEPEAU_unmannedtrader_re_regist_request_clzo@@PEAVCLogFile@@@Z
 * Address: 0x140007E6E

void  CUnmannedTraderUserInfo::ReRegist(CUnmannedTraderUserInfo *this, char byType, _unmannedtrader_re_regist_request_clzo *pRequest, CLogFile *pkLogger)
{
  CUnmannedTraderUserInfo::ReRegist(this, byType, pRequest, pkLogger);
}
