#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?trunk_io_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@_NKKPEAD@Z
 * Address: 0x14000E59D

void  CMgrAvatorItemHistory::trunk_io_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pIOItem, bool bInput, unsigned int dwFeeDalant, unsigned int dwNewDalant, char *pszFileName)
{
  CMgrAvatorItemHistory::trunk_io_item(this, n, pIOItem, bInput, dwFeeDalant, dwNewDalant, pszFileName);
}
