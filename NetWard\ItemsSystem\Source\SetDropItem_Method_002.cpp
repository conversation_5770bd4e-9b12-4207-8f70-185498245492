#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetDropItem@CHolyStone@@QEAAXXZ
 * Address: 0x140137D80

void  CHolyStone::SetDropItem(CHolyStone *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-38h]@1
  CEventLootTable::_event_drop *v4; // [sp+20h] [bp-18h]@4
  CHolyStone *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v5->m_nCurrLootIndex = CMonster::s_idxMonsterLoot[v5->m_pRecordSet->m_dwIndex].nStartRecIndex;
  v5->m_nEndLootIndex = CMonster::s_idxMonsterLoot[v5->m_pRecordSet->m_dwIndex].nEndRecIndex;
  v5->m_nCurrDropIndex = 0;
  v4 = CEventLootTable::GetRecord(qword_1799C66E8, v5->m_pRecordSet->m_strCode);
  if ( v4 )
  {
    v5->m_wMagnifications = v4->wMagnifications;
    v5->m_wRange = v4->wRange;
    v5->m_wDropCntOnce = v4->wDropCntOnce;
    CMyTimer::BeginTimer(&v5->m_tmrDropTime, v4->wDropDelay);
  }
  else
  {
    v5->m_wMagnifications = 1;
    v5->m_wRange = 400;
    v5->m_wDropCntOnce = -1;
    CMyTimer::BeginTimer(&v5->m_tmrDropTime, 0);
  }
  v5->m_wAddCountWithPlayer = CHolyStone::GetAddCountWithPlayer(v5);
}
