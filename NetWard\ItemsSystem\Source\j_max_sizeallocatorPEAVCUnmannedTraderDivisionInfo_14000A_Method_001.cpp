#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?max_size@?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@QEBA_KXZ
 * Address: 0x14000ACA9

unsigned int64_t  std::allocator<CUnmannedTraderDivisionInfo *>::max_size(std::allocator<CUnmannedTraderDivisionInfo *> *this)
{
  return std::allocator<CUnmannedTraderDivisionInfo *>::max_size(this);
}
