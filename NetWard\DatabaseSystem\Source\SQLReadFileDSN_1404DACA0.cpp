#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLReadFileDSN
 * Address: 0x1404DACA0

int  SQLReadFileDSN(const char *lpszFileName, const char *lpszAppName, const char *lpszKeyName, char *lpszString, unsigned int16_t cbString, unsigned int16_t *pcbString)
{
  const char *v6; // rbp@1
  char *v7; // rbx@1
  const char *v8; // rdi@1
  const char *v9; // rsi@1
  int64_t ( *v10)(); // rax@1
  int result; // eax@2

  v6 = lpszFileName;
  v7 = lpszString;
  v8 = lpszKeyName;
  v9 = lpszAppName;
  v10 = ODBC___GetSetupProc("SQLReadFileDSN");
  if ( v10 )
    result = ((int ( *)(const char *, const char *, const char *, char *))v10)(v6, v9, v8, v7);
  else
    result = 0;
  return result;
}
