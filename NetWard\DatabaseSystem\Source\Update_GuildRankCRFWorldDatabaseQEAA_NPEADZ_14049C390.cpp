#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_GuildRank@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x14049C390

char  CRFWorldDatabase::Update_GuildRank(CRFWorldDatabase *this, char *szDate)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-278h]@1
  void *SQLStmt; // [sp+20h] [bp-258h]@11
  int16_t v7; // [sp+30h] [bp-248h]@7
  char Dest; // [sp+50h] [bp-228h]@4
  char v9; // [sp+51h] [bp-227h]@4
  unsigned int64_t v10; // [sp+260h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+280h] [bp+8h]@1
  char *v12; // [sp+288h] [bp+10h]@1

  v12 = szDate;
  v11 = this;
  v2 = &v5;
  for ( i = 156i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v10 = (unsigned int64_t)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v9, 0, 0x1FFui64);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : Update_GuildRank Start!",
    szDate);
  if ( !v11->m_hStmtUpdate && !CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v11->vfptr) )
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v11->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    return 0;
  }
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 0);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : Start Create #tbl_GuildRankTemp Table",
    v12);
  sprintf(&Dest, "select top %d g.GuildSerial as serial, sum(b.lv) + sum(g.pvppoint)/10000 as GuildPower ", 500i64);
  strcat_0(&Dest, "into #tbl_GuildRankTemp ");
  strcat_0(&Dest, "from tbl_general as g join tbl_base as b on g.serial = b.serial ");
  strcat_0(&Dest, "group by g.GuildSerial ");
  strcat_0(&Dest, "having g.guildserial >= 0");
  v7 = SQLExecDirect_0(v11->m_hStmtUpdate, &Dest, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : Create #tbl_GuildRankTemp Table Fail SQL_ERROR!",
        v12);
      SQLStmt = v11->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &Dest, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : Create #tbl_GuildRankTemp Table Fail NO_DATA!",
      v12);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : End Create #tbl_GuildRankTemp Table",
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : Start Create tbl_GuildRank%s Table",
    v12,
    v12);
  sprintf(
    &Dest,
    "select IDENTITY(int, 1, 1) AS Rank, serial, GuildPower, -1 as Rate, 1 as Grade into [dbo].[tbl_GuildRank%s] ",
    v12);
  strcat_0(&Dest, "from #tbl_GuildRankTemp order by GuildPower desc");
  v7 = SQLExecDirect_0(v11->m_hStmtUpdate, &Dest, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : Create tbl_GuildRank%s Table Fail SQL_ERROR!",
        v12,
        v12);
      SQLStmt = v11->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &Dest, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : Create tbl_GuildRank%s Table Fail NO_DATA!",
      v12,
      v12);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : End Create tbl_GuildRank%s Table",
    v12,
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : Start drop table #tbl_GuildRankTemp",
    v12);
  sprintf(&Dest, "drop table #tbl_GuildRankTemp");
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 1) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : drop table #tbl_GuildRankTemp Fail!",
      v12);
  CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v11->vfptr);
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : End drop table #tbl_GuildRankTemp",
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : Start Update Rate",
    v12);
  sprintf(&Dest, "update tbl_GuildRank%s set Rate = ( (Rank*100)/(select count(*) from tbl_GuildRank%s) )", v12, v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_GuildRank%s set Grade = 2 where rate <= 95 and GuildPower >= 300", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_GuildRank%s set Grade = 3 where rate <= 85 and GuildPower >= 1500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_GuildRank%s set Grade = 4 where rate <= 65 and GuildPower >= 3000", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_GuildRank%s set Grade = 5 where rate <= 35 and GuildPower >= 10000", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_GuildRank%s set Grade = 6 where rate <= 15 and GuildPower >= 25000", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_GuildRank%s set Grade = 7 where rate <= 5 and GuildPower >= 30000", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : End Update Rate",
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : Start Drop tbl_GuildRankToday Table",
    v12);
  if ( CRFNewDatabase::TableExist((CRFNewDatabase *)&v11->vfptr, "tbl_GuildRankToday") )
  {
    sprintf(&Dest, "Drop Table tbl_GuildRankToday");
    if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 1) )
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : %s Fail!",
        v12,
        &Dest);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : End Drop tbl_GuildRankToday Table",
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : Start Create tbl_GuildRankToday Table",
    v12);
  sprintf(
    &Dest,
    "select Rank, Rate, Serial, GuildPower, Grade into [dbo].[tbl_GuildRankToday] from tbl_GuildRank%s",
    v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : End Create tbl_GuildRankToday Table",
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank(char* szDate(%s)) : Update_GuildRank Success",
    v12);
  return 1;
}
