// NetWard RF Online Server - Guild Management System
// File: GuildManager.h - Core guild management functionality
// Compatible with Visual Studio 2022 (C++20)

#pragma once

#include "../Common/NetWardTypes.h"
#include <memory>
#include <string>
#include <chrono>

namespace NetWard::Guild {

// ============================================================================
// FORWARD DECLARATIONS
// ============================================================================

class Guild;
class GuildMember;
class GuildHistoryManager;

// ============================================================================
// GUILD ENUMS
// ============================================================================

enum class GuildMoneyOutputType : uint8 {
    None = 0,
    Trade = 1,
    Tax = 2,
    AutoTradeTax = 6,
    Penalty = 7
};

enum class GuildRank : uint8 {
    Member = 0,
    Officer = 1,
    Leader = 2
};

// ============================================================================
// GUILD CLASS
// ============================================================================

class Guild {
public:
    Guild() = default;
    explicit Guild(uint32 guildSerial);
    ~Guild() = default;

    NETWARD_DISABLE_COPY_AND_MOVE(Guild);

    // Core guild information
    uint32 GetGuildSerial() const noexcept { return m_guildSerial; }
    void SetGuildSerial(uint32 serial) noexcept { m_guildSerial = serial; }

    const String& GetGuildName() const noexcept { return m_guildName; }
    void SetGuildName(const String& name) { m_guildName = name; }

    // Dalant (guild currency) management
    double GetTotalDalant() const noexcept { return m_totalDalant; }
    void SetTotalDalant(double amount) noexcept { m_totalDalant = amount; }

    // Money output type for transactions
    GuildMoneyOutputType GetMoneyOutputType() const noexcept { return m_moneyOutputType; }
    void SetMoneyOutputType(GuildMoneyOutputType type) noexcept { m_moneyOutputType = type; }

    // History file management
    const String& GetHistoryFileName() const noexcept { return m_historyFileName; }
    void SetHistoryFileName(const String& fileName) { m_historyFileName = fileName; }

    // Member management
    void BroadcastMemberUpdate() const;
    void SendMoneyIOMessage(
        uint32 guildSerial,
        double amount,
        double additionalFees,
        bool isInput,
        const char* dateString) const;

    // Static guild operations
    static void MakeDownMemberPacket(Guild* guild);
    static void SendMsg_IOMoney(
        Guild* guild,
        uint32 guildSerial,
        double amount,
        double additionalFees,
        int isInput,
        const char* dateString);

private:
    uint32 m_guildSerial = 0;
    String m_guildName;
    double m_totalDalant = 0.0;
    GuildMoneyOutputType m_moneyOutputType = GuildMoneyOutputType::None;
    String m_historyFileName;
};

// ============================================================================
// GUILD HISTORY MANAGER
// ============================================================================

class GuildHistoryManager {
public:
    static GuildHistoryManager& GetInstance();

    // Money transaction logging
    static void AddMoneyTransaction(
        const String& description,
        uint32 guildSerial,
        int32 amount,
        uint32 additionalData,
        double totalAmount,
        double transactionAmount,
        const String& historyFileName);

    // History management
    bool LoadHistory(const String& fileName);
    bool SaveHistory(const String& fileName) const;
    void ClearHistory();

private:
    GuildHistoryManager() = default;
    ~GuildHistoryManager() = default;

    NETWARD_DISABLE_COPY_AND_MOVE(GuildHistoryManager);

    // Static instance for global access (similar to original RF Online design)
    static GuildHistoryManager s_instance;
};

// ============================================================================
// GUILD MEMBER CLASS
// ============================================================================

class GuildMember {
public:
    GuildMember() = default;
    explicit GuildMember(CharacterID characterId);
    ~GuildMember() = default;

    // Member information
    CharacterID GetCharacterId() const noexcept { return m_characterId; }
    void SetCharacterId(CharacterID id) noexcept { m_characterId = id; }

    const String& GetCharacterName() const noexcept { return m_characterName; }
    void SetCharacterName(const String& name) { m_characterName = name; }

    GuildRank GetRank() const noexcept { return m_rank; }
    void SetRank(GuildRank rank) noexcept { m_rank = rank; }

    // Activity tracking
    std::chrono::system_clock::time_point GetLastLogin() const noexcept { return m_lastLogin; }
    void SetLastLogin(std::chrono::system_clock::time_point time) noexcept { m_lastLogin = time; }

    std::chrono::system_clock::time_point GetJoinDate() const noexcept { return m_joinDate; }
    void SetJoinDate(std::chrono::system_clock::time_point date) noexcept { m_joinDate = date; }

    // Status
    bool IsOnline() const noexcept { return m_isOnline; }
    void SetOnline(bool online) noexcept { m_isOnline = online; }

private:
    CharacterID m_characterId = 0;
    String m_characterName;
    GuildRank m_rank = GuildRank::Member;
    std::chrono::system_clock::time_point m_lastLogin;
    std::chrono::system_clock::time_point m_joinDate;
    bool m_isOnline = false;
};

// ============================================================================
// GUILD MANAGER CLASS
// ============================================================================

class GuildManager {
public:
    static GuildManager& GetInstance();

    // Guild operations
    SharedPtr<Guild> CreateGuild(const String& guildName, CharacterID leaderId);
    SharedPtr<Guild> GetGuild(uint32 guildSerial) const;
    bool RemoveGuild(uint32 guildSerial);

    // Member operations
    bool AddMember(uint32 guildSerial, CharacterID characterId);
    bool RemoveMember(uint32 guildSerial, CharacterID characterId);
    SharedPtr<GuildMember> GetMember(uint32 guildSerial, CharacterID characterId) const;

    // Utility functions
    bool IsValidGuild(uint32 guildSerial) const;
    size_t GetGuildCount() const;
    size_t GetMemberCount(uint32 guildSerial) const;

private:
    GuildManager() = default;
    ~GuildManager() = default;

    NETWARD_DISABLE_COPY_AND_MOVE(GuildManager);

    HashMap<uint32, SharedPtr<Guild>> m_guilds;
    HashMap<uint32, Vector<SharedPtr<GuildMember>>> m_guildMembers;
};

// ============================================================================
// TYPE ALIASES
// ============================================================================

using GuildPtr = SharedPtr<Guild>;
using GuildMemberPtr = SharedPtr<GuildMember>;
using GuildList = Vector<GuildPtr>;
using GuildMemberList = Vector<GuildMemberPtr>;

} // namespace NetWard::Guild
