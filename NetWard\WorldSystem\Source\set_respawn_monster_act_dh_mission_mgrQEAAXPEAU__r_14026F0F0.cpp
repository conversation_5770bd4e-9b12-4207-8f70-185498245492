#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?set@_respawn_monster_act@_dh_mission_mgr@@QEAAXPEAU__respawn_monster@@@Z
 * Address: 0x14026F0F0

void  _dh_mission_mgr::_respawn_monster_act::set(_dh_mission_mgr::_respawn_monster_act *this, __respawn_monster *data)
{
  this->pData = data;
  this->nCum = 0;
  this->dwLastRespawnTime = 0;
  if ( !data->bCallEvent )
    this->bStart = 1;
}
