#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_db_Update_GoldBoxItem@CMainThread@@AEAAEHPEAU_db_golden_box_item@@0@Z
 * Address: 0x14000A65A

char  CMainThread::_db_Update_GoldBoxItem(CMainThread *this, int nDBSerial, _db_golden_box_item *pNewData, _db_golden_box_item *pOldData)
{
  return CMainThread::_db_Update_GoldBoxItem(this, nDBSerial, pNewData, pOldData);
}
