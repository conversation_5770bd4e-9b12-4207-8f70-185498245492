#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Fill@PEAVCUnmannedTraderItemCodeInfo@@V1@@std@@YAXPEAVCUnmannedTraderItemCodeInfo@@0AEBV1@@Z
 * Address: 0x1400091B5

void  std::_Fill<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, CUnmannedTraderItemCodeInfo *_Val)
{
  std::_Fill<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo>(_First, _Last, _Val);
}
