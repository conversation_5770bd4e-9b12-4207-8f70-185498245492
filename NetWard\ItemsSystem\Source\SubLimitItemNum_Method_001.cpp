#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SubLimitItemNum@CItemStore@@QEAAXHH@Z
 * Address: 0x1402628B0

void  CItemStore::SubLimitItemNum(CItemStore *this, int nLimitItemIndex, int nSubNum)
{
  if ( nLimitItemIndex < this->m_nLimitStorageItemNum )
  {
    this->m_pLimitStorageItem[nLimitItemIndex].nLimitNum -= nSubNum;
    if ( this->m_pLimitStorageItem[nLimitItemIndex].nLimitNum < 0 )
      this->m_pLimitStorageItem[nLimitItemIndex].nLimitNum = 0;
  }
}
