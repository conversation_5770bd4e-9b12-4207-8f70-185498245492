#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CUnmannedTraderClassInfoTableCodeType::GetGroupID_::_1_::dtor$0
 * Address: 0x140377580

void  CUnmannedTraderClassInfoTableCodeType::GetGroupID_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>((std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)(a2 + 56));
}
