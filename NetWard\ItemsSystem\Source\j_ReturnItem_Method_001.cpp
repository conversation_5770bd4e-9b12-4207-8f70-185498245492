#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?ReturnItem@CQuestMgr@@QEAA_NPEADHE_N@Z
 * Address: 0x14001402E

bool  CQuestMgr::ReturnItem(CQuestMgr *this, char *pszItemCode, int nEndReturnItemCnt, char byQuestDBSlot, bool bCheckOnly)
{
  return CQuestMgr::ReturnItem(this, pszItemCode, nEndReturnItemCnt, byQuestDBSlot, bCheckOnly);
}
