#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Release@CCashDbWorkerGB@@UEAAXXZ
 * Address: 0x140318DE0

void  CCashDbWorkerGB::Release(CCashDbWorkerGB *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-28h]@1
  CCashDbWorkerGB *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v4->_pkNet )
  {
    CEnglandBillingMgr::Free(v4->_pkNet);
    v4->_pkNet = 0i64;
  }
}
