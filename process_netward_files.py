#!/usr/bin/env python3
"""
NetWard Server - Complete Decompiled File Processor
Converts ALL IDA Pro decompiled C files to Visual Studio 2022 compatible C++ project
Based on RF Online ZoneServerUD_x64 decompiled source
"""

import os
import re
import shutil
import uuid
from pathlib import Path
from typing import Dict, List, Tuple

class NetWardProjectProcessor:
    def __init__(self, source_dir: str, output_dir: str = "NetWard"):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)

        # All the folders from your decompiled source
        self.system_folders = [
            "authentication",
            "combat",
            "database",
            "economy",
            "items",
            "network",
            "player",
            "security",
            "system",
            "world"
        ]

        # Project GUIDs for Visual Studio
        self.project_guids = {
            "NetWardMain": "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}",
            "Authentication": "{B2C3D4E5-F6G7-8901-BCDE-F23456789012}",
            "Combat": "{C3D4E5F6-G7H8-9012-CDEF-************}",
            "Database": "{D4E5F6G7-H8I9-0123-DEF0-************}",
            "Economy": "{E5F6G7H8-I9J0-1234-EF01-************}",
            "Items": "{F6G7H8I9-J0K1-2345-F012-************}",
            "Network": "{G7H8I9J0-K1L2-3456-0123-************}",
            "Player": "{H8I9J0K1-L2M3-4567-1234-************}",
            "Security": "{I9J0K1L2-M3N4-5678-2345-************}",
            "System": "{J0K1L2M3-N4O5-6789-3456-012345678901}",
            "World": "{K1L2M3N4-O5P6-7890-4567-123456789012}"
        }

    def process_all_files(self):
        """Process all decompiled files and create complete NetWard project"""
        print("="*60)
        print("NETWARD SERVER PROJECT GENERATOR")
        print("="*60)
        print("Processing RF Online ZoneServerUD_x64 decompiled source...")
        print(f"Source: {self.source_dir}")
        print(f"Output: {self.output_dir}")
        print("="*60)

        # Create output directory structure
        self.create_directory_structure()

        # Copy and process the main header
        self.process_main_header()

        # Process all system folders
        all_projects = {}
        for folder in self.system_folders:
            print(f"\nProcessing {folder.upper()} system...")
            processed_files = self.process_folder(folder)
            if processed_files:
                all_projects[folder] = processed_files
                print(f"  ✓ Processed {len(processed_files)} files")
            else:
                print(f"  ⚠ No files found in {folder}")

        # Generate all project files
        self.generate_all_projects(all_projects)

        # Generate solution file
        self.generate_solution_file()

        print("\n" + "="*60)
        print("NETWARD PROJECT GENERATION COMPLETE!")
        print("="*60)
        print(f"✓ Visual Studio 2022 solution created: {self.output_dir}/NetWard.sln")
        print(f"✓ {len(all_projects)} subsystem projects created")
        print(f"✓ Main executable project: NetWardMain")
        print("\nNext steps:")
        print("1. Open NetWard.sln in Visual Studio 2022")
        print("2. Set NetWardMain as startup project")
        print("3. Build solution (some compilation errors expected initially)")
        print("4. Fix function signatures and missing dependencies")
        print("5. Test and iterate")
        print("="*60)

    def create_directory_structure(self):
        """Create the complete Visual Studio project directory structure"""
        print("Creating project directory structure...")

        # Base directories
        base_dirs = ["Common", "NetWardMain"]

        # System directories
        system_dirs = []
        for folder in self.system_folders:
            system_dirs.extend([
                f"{folder.title()}System/Source",
                f"{folder.title()}System/Include"
            ])

        all_dirs = base_dirs + system_dirs

        for dir_path in all_dirs:
            (self.output_dir / dir_path).mkdir(parents=True, exist_ok=True)

        print(f"  ✓ Created {len(all_dirs)} directories")

    def process_main_header(self):
        """Process the main ZoneServerUD_x64.h header file using HeaderExtractor"""
        header_source = self.source_dir.parent / "Decompiled Header - IDA Pro" / "ZoneServerUD_x64.h"
        header_output_dir = self.output_dir / "Common"

        if header_source.exists():
            print("Processing main header file with HeaderExtractor...")
            try:
                # Import and use the HeaderExtractor
                from header_extractor import HeaderExtractor

                extractor = HeaderExtractor(str(header_source), str(header_output_dir))
                extractor.extract_all_headers()

                print(f"  ✓ Headers extracted and organized into modular files")
            except Exception as e:
                print(f"  ⚠ Error processing header: {e}")
                # Fallback to simple processing
                self.process_header_fallback(header_source, header_output_dir)
        else:
            print(f"  ⚠ Header file not found: {header_source}")

    def process_header_fallback(self, header_source, header_output_dir):
        """Fallback header processing if HeaderExtractor fails"""
        print("  Using fallback header processing...")
        header_dest = header_output_dir / "NetWardCore.h"

        try:
            with open(header_source, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Clean up the header content
            cleaned_content = self.clean_header_content(content)

            with open(header_dest, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)

            print(f"  ✓ Fallback header created: NetWardCore.h")
        except Exception as e:
            print(f"  ⚠ Fallback header processing failed: {e}")

    def clean_header_content(self, content: str) -> str:
        """Clean up header file content for C++ compatibility"""
        lines = content.split('\n')
        cleaned_lines = []

        # Add header guard and C++ compatibility
        cleaned_lines.extend([
            "#pragma once",
            "",
            "// NetWard Server Core Definitions",
            "// Generated from RF Online ZoneServerUD_x64 decompiled headers",
            "",
            "#ifndef NETWARD_CORE_H",
            "#define NETWARD_CORE_H",
            "",
            "#ifdef __cplusplus",
            "extern \"C\" {",
            "#endif",
            "",
            "// Standard includes for compatibility",
            "#include <windows.h>",
            "#include <stdint.h>",
            "#include <memory>",
            ""
        ])

        # Process original content (first 100 lines to avoid too much processing)
        for i, line in enumerate(lines[:100]):
            if i > 50:  # Skip too much content for now
                break

            # Clean up the line
            cleaned_line = line

            # Replace IDA-specific types with standard types
            replacements = {
                '__int64': 'int64_t',
                '__int32': 'int32_t',
                '__int16': 'int16_t',
                '__int8': 'int8_t',
                '__fastcall': '',
                '__usercall': '',
                '__cdecl': '',
                '__stdcall': ''
            }

            for old, new in replacements.items():
                cleaned_line = cleaned_line.replace(old, new)

            cleaned_lines.append(cleaned_line)

        # Add closing guards
        cleaned_lines.extend([
            "",
            "#ifdef __cplusplus",
            "}",
            "#endif",
            "",
            "#endif // NETWARD_CORE_H"
        ])

        return '\n'.join(cleaned_lines)

    def process_folder(self, folder_name: str) -> List[str]:
        """Process all C files in a specific folder"""
        source_folder = self.source_dir / folder_name
        project_name = f"{folder_name.title()}System"
        output_folder = self.output_dir / project_name / "Source"

        processed_files = []

        if not source_folder.exists():
            return processed_files

        for c_file in source_folder.glob("*.c"):
            try:
                cpp_file = self.convert_c_to_cpp(c_file, output_folder)
                if cpp_file:
                    processed_files.append(cpp_file)
            except Exception as e:
                print(f"    ⚠ Error processing {c_file.name}: {e}")

        return processed_files

    def convert_c_to_cpp(self, c_file: Path, output_folder: Path) -> str:
        """Convert a single C file to C++ compatible format with class grouping"""
        try:
            # Read the original file
            with open(c_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Extract function information from IDA comments
            function_info = self.extract_function_info(content)

            # Determine class name and method name
            class_name, method_name = self.parse_function_signature(function_info)

            # Generate class-based filename
            if class_name:
                cpp_filename = f"{class_name}.cpp"
            else:
                # Fallback to original filename if no class detected
                cpp_filename = c_file.stem + ".cpp"

            output_file = output_folder / cpp_filename

            # Clean up the content
            cleaned_content = self.clean_function_content(content, function_info, class_name)

            # Append to existing class file or create new one
            self.append_to_class_file(output_file, cleaned_content, function_info)

            return cpp_filename
        except Exception as e:
            print(f"      Error converting {c_file.name}: {e}")
            return None

    def parse_function_signature(self, function_info: dict) -> tuple:
        """Parse function signature to extract class and method names"""
        if 'mangled_name' not in function_info:
            return None, None

        mangled_name = function_info['mangled_name']

        # Common patterns for C++ mangled names
        # ??0CClassName@@QEAA@XZ = Constructor
        # ??1CClassName@@QEAA@XZ = Destructor
        # ?MethodName@CClassName@@QEAA... = Method

        # Extract class name patterns
        class_patterns = [
            r'\?\?[01]([A-Za-z_][A-Za-z0-9_]*)@@',  # Constructor/Destructor
            r'\?[A-Za-z_][A-Za-z0-9_]*@([A-Za-z_][A-Za-z0-9_]*)@@',  # Method
            r'([A-Za-z_][A-Za-z0-9_]*)::[A-Za-z_][A-Za-z0-9_]*',  # Direct class::method
        ]

        for pattern in class_patterns:
            match = re.search(pattern, mangled_name)
            if match:
                class_name = match.group(1)

                # Extract method name
                if '??0' in mangled_name:
                    method_name = class_name  # Constructor
                elif '??1' in mangled_name:
                    method_name = f"~{class_name}"  # Destructor
                else:
                    # Try to extract method name
                    method_match = re.search(r'\?([A-Za-z_][A-Za-z0-9_]*)@', mangled_name)
                    method_name = method_match.group(1) if method_match else "Unknown"

                return class_name, method_name

        return None, None

    def append_to_class_file(self, output_file: Path, content: str, function_info: dict):
        """Append function to existing class file or create new one"""
        if output_file.exists():
            # Append to existing file
            with open(output_file, 'a', encoding='utf-8') as f:
                f.write(f"\n// Function: {function_info.get('mangled_name', 'Unknown')}\n")
                f.write(f"// Address: {function_info.get('address', 'Unknown')}\n")
                f.write(content)
                f.write("\n")
        else:
            # Create new file with header
            class_name = output_file.stem
            header_content = self.generate_class_file_header(class_name)

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(header_content)
                f.write(f"\n// Function: {function_info.get('mangled_name', 'Unknown')}\n")
                f.write(f"// Address: {function_info.get('address', 'Unknown')}\n")
                f.write(content)
                f.write("\n")

    def generate_class_file_header(self, class_name: str) -> str:
        """Generate header for a class implementation file"""
        return f"""// {class_name} Implementation
// NetWard Server - RF Online Zone Server
// Generated from decompiled IDA Pro source

#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

// {class_name} class implementation

"""

    def extract_function_info(self, content: str) -> dict:
        """Extract function information from IDA Pro comments"""
        info = {}

        # Extract function name from comment
        func_match = re.search(r'/\*\s*Function:\s*(.+?)\s*\*/', content)
        if func_match:
            info['mangled_name'] = func_match.group(1).strip()
            info['demangled_name'] = self.demangle_function_name(info['mangled_name'])

        # Extract address
        addr_match = re.search(r'/\*\s*Address:\s*(.+?)\s*\*/', content)
        if addr_match:
            info['address'] = addr_match.group(1).strip()

        return info

    def demangle_function_name(self, mangled_name: str) -> str:
        """Basic demangling of function names"""
        # This is a simplified demangling - you might want to use a proper demangler

        # Remove common IDA artifacts
        name = mangled_name.replace('?', '').replace('@', '::')

        # Basic pattern matching for common cases
        if 'QEAA' in name or 'UEAA' in name:
            # This is likely a member function
            parts = name.split('::')
            if len(parts) >= 2:
                class_name = parts[0]
                method_name = parts[1].split('Q')[0].split('U')[0]
                return f"{class_name}::{method_name}"

        return name

    def clean_function_content(self, content: str, function_info: dict, class_name: str = None) -> str:
        """Clean up function content for C++ compilation"""
        lines = content.split('\n')
        cleaned_lines = []

        # Skip includes since they're added in the class file header
        in_comment_block = False

        # Process original content with cleanup
        for line in lines:
            stripped = line.strip()

            # Skip IDA-specific comments at the top
            if stripped.startswith('/*') and ('Function:' in stripped or 'Address:' in stripped):
                in_comment_block = True
                continue
            if in_comment_block and stripped == '*/':
                in_comment_block = False
                continue
            if in_comment_block:
                continue

            # Replace IDA-specific types and calling conventions
            line = line.replace('__int64', 'int64_t')
            line = line.replace('__int32', 'int32_t')
            line = line.replace('__int16', 'int16_t')
            line = line.replace('__int8', 'int8_t')
            line = line.replace('__fastcall', '')
            line = line.replace('__usercall', '')
            line = line.replace('__cdecl', '')
            line = line.replace('__stdcall', '')

            # Fix common IDA artifacts
            line = re.sub(r'_QWORD\s*\*', 'uint64_t*', line)
            line = re.sub(r'_DWORD\s*\*', 'uint32_t*', line)
            line = re.sub(r'_WORD\s*\*', 'uint16_t*', line)
            line = re.sub(r'_BYTE\s*\*', 'uint8_t*', line)

            # Fix integer literals
            line = re.sub(r'(\d+)i64', r'\1LL', line)  # 12i64 -> 12LL
            line = re.sub(r'0i64', '0', line)  # 0i64 -> 0

            # Clean up extra spaces
            line = re.sub(r'\s+', ' ', line)

            cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def generate_all_projects(self, all_projects: Dict[str, List[str]]):
        """Generate all Visual Studio project files"""
        print("\nGenerating Visual Studio project files...")

        # Generate individual system projects
        for folder, files in all_projects.items():
            project_name = f"{folder.title()}System"
            self.generate_system_project(project_name, folder, files)
            print(f"  ✓ Generated {project_name}.vcxproj")

        # Generate main executable project
        self.generate_main_project(list(all_projects.keys()))
        print(f"  ✓ Generated NetWardMain.vcxproj")

    def generate_system_project(self, project_name: str, folder_name: str, source_files: List[str]):
        """Generate a system project file (static library)"""
        project_dir = self.output_dir / project_name
        project_file = project_dir / f"{project_name}.vcxproj"

        # Generate file list
        file_list = ""
        for file in source_files:
            file_list += f'    <ClCompile Include="Source\\{file}" />\n'

        project_content = f'''<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>

  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{self.project_guids.get(folder_name.title(), "{" + str(uuid.uuid4()).upper() + "}")}</ProjectGuid>
    <RootNamespace>{project_name}</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\\Microsoft.Cpp.Default.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\\Microsoft.Cpp.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemGroup>
{file_list}  </ItemGroup>

  <Import Project="$(VCTargetsPath)\\Microsoft.Cpp.targets" />
</Project>'''

        with open(project_file, 'w', encoding='utf-8') as f:
            f.write(project_content)

    def generate_main_project(self, system_folders: List[str]):
        """Generate main executable project"""
        # Create main.cpp
        main_cpp = '''#include "../Common/NetWardCore.h"
#include <iostream>
#include <windows.h>
#include <thread>
#include <chrono>

// NetWard Server - RF Online Zone Server Implementation
// Generated from decompiled ZoneServerUD_x64 source

class NetWardServer {
public:
    NetWardServer() {
        std::cout << "NetWard Server Initializing..." << std::endl;
    }

    bool Initialize() {
        std::cout << "Initializing server systems..." << std::endl;

        // TODO: Initialize all subsystems
        // - Authentication System
        // - Database System
        // - World System
        // - Combat System
        // - Economy System
        // - Player System
        // - Network System
        // - Security System
        // - Items System

        std::cout << "All systems initialized successfully!" << std::endl;
        return true;
    }

    void Run() {
        std::cout << "NetWard Server is now running..." << std::endl;
        std::cout << "Press Ctrl+C to stop the server." << std::endl;

        // Main server loop
        while (m_running) {
            // TODO: Process server logic
            // - Handle network packets
            // - Update game world
            // - Process player actions
            // - Handle database operations

            std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
        }
    }

    void Shutdown() {
        std::cout << "Shutting down NetWard Server..." << std::endl;
        m_running = false;

        // TODO: Cleanup all systems

        std::cout << "Server shutdown complete." << std::endl;
    }

private:
    bool m_running = true;
};

int main()
{
    std::cout << "============================================" << std::endl;
    std::cout << "    NetWard Server v1.0" << std::endl;
    std::cout << "    RF Online Zone Server Implementation" << std::endl;
    std::cout << "============================================" << std::endl;

    NetWardServer server;

    if (!server.Initialize()) {
        std::cerr << "Failed to initialize server!" << std::endl;
        return -1;
    }

    try {
        server.Run();
    }
    catch (const std::exception& e) {
        std::cerr << "Server error: " << e.what() << std::endl;
        server.Shutdown();
        return -1;
    }

    server.Shutdown();
    return 0;
}'''

        # Create main.cpp
        main_dir = self.output_dir / "NetWardMain"
        with open(main_dir / "main.cpp", 'w', encoding='utf-8') as f:
            f.write(main_cpp)

        # Generate project references
        project_refs = ""
        for folder in system_folders:
            project_name = f"{folder.title()}System"
            guid = self.project_guids.get(folder.title(), "{" + str(uuid.uuid4()).upper() + "}")
            project_refs += f'''    <ProjectReference Include="..\\{project_name}\\{project_name}.vcxproj">
      <Project>{guid}</Project>
    </ProjectReference>
'''

        # Create project file for main executable
        main_project = f'''<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>

  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{self.project_guids["NetWardMain"]}</ProjectGuid>
    <RootNamespace>NetWardMain</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\\Microsoft.Cpp.Default.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\\Microsoft.Cpp.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
  </PropertyGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>ws2_32.lib;winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>ws2_32.lib;winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>

  <ItemGroup>
    <ClCompile Include="main.cpp" />
  </ItemGroup>

  <ItemGroup>
{project_refs}  </ItemGroup>

  <Import Project="$(VCTargetsPath)\\Microsoft.Cpp.targets" />
</Project>'''

        with open(main_dir / "NetWardMain.vcxproj", 'w', encoding='utf-8') as f:
            f.write(main_project)

    def generate_solution_file(self):
        """Generate Visual Studio solution file"""
        print("Generating solution file...")

        # Generate project entries for solution
        project_entries = ""
        config_entries = ""

        # Main project
        main_guid = self.project_guids["NetWardMain"]
        project_entries += f'Project("{{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}}") = "NetWardMain", "NetWardMain\\NetWardMain.vcxproj", "{main_guid}"\n'

        # Add dependencies
        deps = []
        for folder in self.system_folders:
            project_name = f"{folder.title()}System"
            guid = self.project_guids.get(folder.title(), "{" + str(uuid.uuid4()).upper() + "}")
            deps.append(guid)

        if deps:
            project_entries += "\tProjectSection(ProjectDependencies) = postProject\n"
            for dep_guid in deps:
                project_entries += f"\t\t{dep_guid} = {dep_guid}\n"
            project_entries += "\tEndProjectSection\n"
        project_entries += "EndProject\n\n"

        # System projects
        for folder in self.system_folders:
            project_name = f"{folder.title()}System"
            guid = self.project_guids.get(folder.title(), "{" + str(uuid.uuid4()).upper() + "}")
            project_entries += f'Project("{{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}}") = "{project_name}", "{project_name}\\{project_name}.vcxproj", "{guid}"\n'
            project_entries += "EndProject\n\n"

        # Generate configuration entries
        all_guids = [main_guid] + [self.project_guids.get(folder.title(), "{" + str(uuid.uuid4()).upper() + "}") for folder in self.system_folders]

        for guid in all_guids:
            config_entries += f"\t\t{guid}.Debug|x64.ActiveCfg = Debug|x64\n"
            config_entries += f"\t\t{guid}.Debug|x64.Build.0 = Debug|x64\n"
            config_entries += f"\t\t{guid}.Release|x64.ActiveCfg = Release|x64\n"
            config_entries += f"\t\t{guid}.Release|x64.Build.0 = Release|x64\n"
            config_entries += "\n"

        solution_content = f'''Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1

{project_entries}Global
\tGlobalSection(SolutionConfigurationPlatforms) = preSolution
\t\tDebug|x64 = Debug|x64
\t\tRelease|x64 = Release|x64
\tEndGlobalSection
\t
\tGlobalSection(ProjectConfigurationPlatforms) = postSolution
{config_entries}\tEndGlobalSection
\t
\tGlobalSection(SolutionProperties) = preSolution
\t\tHideSolutionNode = FALSE
\tEndGlobalSection
EndGlobal'''

        with open(self.output_dir / "NetWard.sln", 'w', encoding='utf-8') as f:
            f.write(solution_content)

# Usage and main execution
if __name__ == "__main__":
    import sys

    # Set your paths here
    source_directory = "Decompiled Source Code - IDA Pro"  # Your current folder
    output_directory = "NetWard"  # Where to create the VS2022 project

    print("NetWard Server Project Generator")
    print("================================")

    if len(sys.argv) > 1:
        source_directory = sys.argv[1]
    if len(sys.argv) > 2:
        output_directory = sys.argv[2]

    processor = NetWardProjectProcessor(source_directory, output_directory)
    processor.process_all_files()

    print("\n" + "="*60)
    print("SUCCESS! NetWard project has been generated!")
    print("="*60)
    print(f"📁 Project location: {output_directory}")
    print(f"🎯 Solution file: {output_directory}/NetWard.sln")
    print("\n🚀 Quick Start:")
    print("1. Open NetWard.sln in Visual Studio 2022")
    print("2. Right-click 'NetWardMain' → Set as Startup Project")
    print("3. Press F5 to build and run")
    print("\n💡 Note: Some compilation errors are expected initially.")
    print("   These can be fixed by adjusting function signatures and types.")
    print("="*60)