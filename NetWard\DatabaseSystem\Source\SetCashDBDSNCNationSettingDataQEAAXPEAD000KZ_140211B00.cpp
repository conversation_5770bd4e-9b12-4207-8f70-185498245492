#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetCashDBDSN@CNationSettingData@@QEAAXPEAD000K@Z
 * Address: 0x140211B00

void  CNationSettingData::SetCashDBDSN(CNationSettingData *this, char *szIP, char *szDBName, char *szAccount, char *szPassword, unsigned int dwPort)
{
  int64_t *v6; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v8; // [sp+0h] [bp-28h]@1
  CNationSettingData *v9; // [sp+30h] [bp+8h]@1
  const char *v10; // [sp+38h] [bp+10h]@1
  const char *v11; // [sp+48h] [bp+20h]@1

  v11 = szAccount;
  v10 = szIP;
  v9 = this;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v6 = -*********;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  strcpy_s<64>((char (*)[64])v9->m_szCashDBName, szDBName);
  strcpy_s<16>((char (*)[16])v9->m_szCashDBIP, v10);
  strcpy_s<64>((char (*)[64])v9->m_szCashDBID, v11);
  strcpy_s<64>((char (*)[64])v9->m_szCashDBPW, szPassword);
  v9->m_wCashDBPort = dwPort;
  CNationSettingData::SetCashDBDSNSetFlag(v9);
}
