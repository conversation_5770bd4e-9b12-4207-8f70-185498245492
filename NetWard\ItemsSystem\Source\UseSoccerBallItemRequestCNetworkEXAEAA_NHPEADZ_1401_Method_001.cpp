#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?UseSoccerBallItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB6D0

char  CNetworkEX::UseSoccerBallItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-E8h]@1
  unsigned int16_t *v7; // [sp+30h] [bp-B8h]@4
  CPlayer *v8; // [sp+38h] [bp-B0h]@4
  unsigned int16_t wItemIndex; // [sp+44h] [bp-A4h]@6
  char szMsg; // [sp+64h] [bp-84h]@6
  unsigned int16_t v11; // [sp+65h] [bp-83h]@6
  char pbyType; // [sp+84h] [bp-64h]@6
  char v13; // [sp+85h] [bp-63h]@6
  char v14[4]; // [sp+A4h] [bp-44h]@7
  unsigned int16_t v15; // [sp+A8h] [bp-40h]@7
  bool v16; // [sp+AAh] [bp-3Eh]@7
  char v17; // [sp+C4h] [bp-24h]@7
  char v18; // [sp+C5h] [bp-23h]@7

  v3 = &v6;
  for ( i = 56i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v7 = (unsigned int16_t *)pBuf;
  v8 = &g_Player + n;
  if ( v8->m_bOper )
  {
    wItemIndex = -1;
    v11 = *v7;
    szMsg = CPlayer::pc_UserSoccerBall(v8, *v7, &wItemIndex);
    pbyType = 7;
    v13 = 47;
    CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, &szMsg, 3u);
    if ( !szMsg )
    {
      *(uint32_t*)v14 = v8->m_dwObjSerial;
      v15 = wItemIndex;
      v16 = v8->m_bTakeSoccerBall;
      v17 = 7;
      v18 = 48;
      CGameObject::CircleReport((CGameObject *)&v8->vfptr, &v17, v14, 7, 0);
      CPlayer::SenseState(v8);
    }
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
