#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?PushTask@CCashDBWorkManager@@QEAA_NHPEAE_K@Z
 * Address: 0x1402F3340

bool  CCashDBWorkManager::PushTask(CCashDBWorkManager *this, int nTaskCode, char *p, unsigned int64_t size)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-28h]@1
  CCashDBWorkManager *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  return CashDbWorker::PushTask(v8->m_pWorker, nTaskCode, p, size);
}
