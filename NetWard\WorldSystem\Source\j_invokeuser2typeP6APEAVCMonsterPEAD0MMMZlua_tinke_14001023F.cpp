#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?invoke@?$user2type@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@SAP6APEAVCMonster@@PEAD0MMM@ZPEAUlua_State@@H@Z
 * Address: 0x14001023F

CMonster *( * lua_tinker::user2type<CMonster * (*)(char *,char *,float,float,float)>::invoke(lua_tinker::user2type<CMonster * (*)(char *,char *,float,float,float)> *this, struct lua_State *L, int index))(char *, char *, float, float, float)
{
  return lua_tinker::user2type<CMonster * (*)(char *,char *,float,float,float)>::invoke(this, L, index);
}
