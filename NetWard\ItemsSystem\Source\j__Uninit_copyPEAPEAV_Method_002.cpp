#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Uninit_copy@PEAPEAVCUnmannedTraderSortType@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@YAPEAPEAVCUnmannedTraderSortType@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderSortType@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400125A8

CUnmannedTraderSortType ** std::_Uninit_copy<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *,std::allocator<CUnmannedTraderSortType *>>(CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last, CUnmannedTraderSortType **_Dest, std::allocator<CUnmannedTraderSortType *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *,std::allocator<CUnmannedTraderSortType *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
