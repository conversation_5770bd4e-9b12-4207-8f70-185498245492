#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetDamage@CTrap@@UEAAHHPEAVCCharacter@@H_NHK1@Z
 * Address: 0x14013F1C0

int64_t  CTrap::SetDamage(CTrap *this, int nDam, CCharacter *pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn)
{
  int64_t *v8; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v11; // [sp+0h] [bp-38h]@1
  int v12; // [sp+20h] [bp-18h]@6
  CTrap *v13; // [sp+40h] [bp+8h]@1

  v13 = this;
  v8 = &v11;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v8 = -858993460;
    v8 = (int64_t *)((char *)v8 + 4);
  }
  if ( nDam > 1 )
  {
    if ( v13->m_nHP - nDam <= 0 )
      v12 = 0;
    else
      v12 = v13->m_nHP - nDam;
    v13->m_nHP = v12;
  }
  if ( v13->m_nHP )
  {
    CGameObject::SetBreakTranspar((CGameObject *)&v13->vfptr, 1);
  }
  else
  {
    CTrap::Attack(v13, (CCharacter *)&v13->vfptr);
    CTrap::Destroy(v13, 1);
  }
  return v13->m_nHP;
}
