#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?push_back@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEAAXAEBQEAVCUnmannedTraderSortType@@@Z
 * Address: 0x140013057

void  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::push_back(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, CUnmannedTraderSortType *const *_Val)
{
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::push_back(this, _Val);
}
