#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Umove@PEAVCMoveMapLimitRightInfo@@@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAPEAVCMoveMapLimitRightInfo@@PEAV2@00@Z
 * Address: 0x1400126D4

CMoveMapLimitRightInfo * std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Umove<CMoveMapLimitRightInfo *>(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Ptr)
{
  return std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Umove<CMoveMapLimitRightInfo *>(
           this,
           _First,
           _Last,
           _Ptr);
}
