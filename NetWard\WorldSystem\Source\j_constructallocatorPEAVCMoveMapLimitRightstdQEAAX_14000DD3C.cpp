#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?construct@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEAAXPEAPEAVCMoveMapLimitRight@@AEBQEAV3@@Z
 * Address: 0x14000DD3C

void  std::allocator<CMoveMapLimitRight *>::construct(std::allocator<CMoveMapLimitRight *> *this, CMoveMapLimitRight **_Ptr, CMoveMapLimitRight *const *_Val)
{
  std::allocator<CMoveMapLimitRight *>::construct(this, _Ptr, _Val);
}
