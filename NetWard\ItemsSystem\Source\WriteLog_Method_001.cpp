#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?WriteLog@CMgrAvatorItemHistory@@QEAAXPEAD@Z
 * Address: 0x14023FA80

void  CMgrAvatorItemHistory::WriteLog(CMgrAvatorItemHistory *this, char *pszFileName)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-28h]@1
  CMgrAvatorItemHistory *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( sData[0] )
    CMgrAvatorItemHistory::WriteFile(v5, pszFileName, sData);
}
