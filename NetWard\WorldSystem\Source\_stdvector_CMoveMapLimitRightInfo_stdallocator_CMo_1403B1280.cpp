#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Insert_n_::_1_::dtor$0
 * Address: 0x1403B1280

void  std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Insert_n_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::~_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(*(std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > **)(a2 + 200));
}
