#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??_E?$CArray@U_State@CLuaLooting_Novus_Item@@@US@@UEAAPEAXI@Z_0
 * Address: 0x140011865

void * US::CArray<CLuaLooting_Novus_Item::_State>::`vector deleting destructor'(US::CArray<CLuaLooting_Novus_Item::_State> *this, unsigned int a2)
{
  return US::CArray<CLuaLooting_Novus_Item::_State>::`vector deleting destructor'(this, a2);
}
