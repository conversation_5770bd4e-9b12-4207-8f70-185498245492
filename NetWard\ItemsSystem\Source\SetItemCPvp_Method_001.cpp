#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetItem@CPvpCashMng@@QEAA_NPEADH@Z
 * Address: 0x1403F5B50

char  CPvpCashMng::SetItem(CPvpCashMng *this, char *szItemCode, int nInx)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@8
  int64_t v6; // [sp+0h] [bp-38h]@1
  _base_fld *v7; // [sp+20h] [bp-18h]@4
  unsigned int8_t j; // [sp+28h] [bp-10h]@4
  CPvpCashMng *v9; // [sp+40h] [bp+8h]@1
  const char *szRecordCode; // [sp+48h] [bp+10h]@1
  int v11; // [sp+50h] [bp+18h]@1

  v11 = nInx;
  szRecordCode = szItemCode;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v7 = 0i64;
  for ( j = 0; ; ++j )
  {
    if ( (signed int)j >= 37 )
      return 0;
    v7 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, szRecordCode);
    if ( v7 )
      break;
  }
  if ( GetItemKindCode(j) )
  {
    result = 0;
  }
  else
  {
    v9->m_TalikList.TalikInfo[v11].byTableCode = j;
    v9->m_TalikList.TalikInfo[v11].m_pFld = v7;
    result = 1;
  }
  return result;
}
