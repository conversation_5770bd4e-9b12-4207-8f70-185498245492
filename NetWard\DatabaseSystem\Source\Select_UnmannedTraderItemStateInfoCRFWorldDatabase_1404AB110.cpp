#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Select_UnmannedTraderItemStateInfo@CRFWorldDatabase@@QEAAHPEAU_unmannedtrader_stade_id_info@@K@Z
 * Address: 0x1404AB110

signed int64_t  CRFWorldDatabase::Select_UnmannedTraderItemStateInfo(CRFWorldDatabase *this, _unmannedtrader_stade_id_info *pkInfo, unsigned int dwMaxCnt)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  signed int64_t result; // rax@8
  int64_t v6; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@17
  SQLLEN v9; // [sp+38h] [bp-150h]@17
  int16_t v10; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int v12; // [sp+164h] [bp-24h]@4
  unsigned int j; // [sp+168h] [bp-20h]@14
  unsigned int64_t v14; // [sp+178h] [bp-10h]@4
  CRFWorldDatabase *v15; // [sp+190h] [bp+8h]@1
  _unmannedtrader_stade_id_info *v16; // [sp+198h] [bp+10h]@1

  v16 = pkInfo;
  v15 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v14 = (unsigned int64_t)&v6 ^ _security_cookie;
  v12 = 0;
  sprintf(&Dest, "select top %u [id], [desc] from [dbo].[tbl_utresultstateid]", dwMaxCnt);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &Dest);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v10 = SQLExecDirectA_0(v15->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v10, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v10, v15->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      for ( j = 0; ; ++j )
      {
        v10 = SQLFetch_0(v15->m_hStmtSelect);
        if ( v10 )
        {
          if ( v10 != 1 )
            break;
        }
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 1u, -18, &v16[j], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)128;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 2u, -8, v16[j].wszDesc, 128i64, &v9);
      }
      if ( v15->m_hStmtSelect )
        SQLCloseCursor_0(v15->m_hStmtSelect);
      if ( v15->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &Dest);
      result = 0i64;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
