#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Loop@CUnmannedTraderLazyCleaner@@QEAAXXZ
 * Address: 0x140392B10

void  CUnmannedTraderLazyCleaner::Loop(CUnmannedTraderLazyCleaner *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-58h]@1
  char v4; // [sp+34h] [bp-24h]@8
  char v5; // [sp+35h] [bp-23h]@8
  char v6; // [sp+36h] [bp-22h]@8
  CUnmannedTraderLazyCleaner *v7; // [sp+60h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( !v7->m_bClearProcess && v7->m_pkTimer && CMyTimer::CountingTimer(v7->m_pkTimer) )
  {
    v4 = 0;
    v5 = 0;
    memset(&v6, 0, 2ui64);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 67, &v4, 4);
    v7->m_bClearProcess = 1;
  }
}
