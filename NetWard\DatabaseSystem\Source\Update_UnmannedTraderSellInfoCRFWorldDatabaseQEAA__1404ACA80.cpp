#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_UnmannedTraderSellInfo@CRFWorldDatabase@@QEAA_NKAEBU_unmannedtrader_registsingleitem@@AEBU_SYSTEMTIME@@@Z
 * Address: 0x1404ACA80

bool  CRFWorldDatabase::Update_UnmannedTraderSellInfo(CRFWorldDatabase *this, unsigned int dwRegSerial, _unmannedtrader_registsingleitem *kInfo, _SYSTEMTIME *kCurTime)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int v6; // ecx@4
  int v7; // edx@4
  int v8; // er8@4
  int v9; // er9@4
  int v10; // er10@4
  int v11; // er11@4
  int v12; // ebx@4
  int v13; // edi@4
  unsigned int v14; // esi@4
  int64_t v16; // [sp+0h] [bp-4C8h]@1
  int v17; // [sp+20h] [bp-4A8h]@4
  int v18; // [sp+28h] [bp-4A0h]@4
  int v19; // [sp+30h] [bp-498h]@4
  int v20; // [sp+38h] [bp-490h]@4
  int v21; // [sp+40h] [bp-488h]@4
  int v22; // [sp+48h] [bp-480h]@4
  int v23; // [sp+50h] [bp-478h]@4
  int v24; // [sp+58h] [bp-470h]@4
  unsigned int v25; // [sp+60h] [bp-468h]@4
  unsigned int v26; // [sp+68h] [bp-460h]@4
  int v27; // [sp+70h] [bp-458h]@4
  char Dest; // [sp+90h] [bp-438h]@4
  unsigned int64_t v29; // [sp+4A0h] [bp-28h]@4
  CRFWorldDatabase *v30; // [sp+4D0h] [bp+8h]@1
  unsigned int v31; // [sp+4D8h] [bp+10h]@1
  _unmannedtrader_registsingleitem *v32; // [sp+4E0h] [bp+18h]@1
  _SYSTEMTIME *v33; // [sp+4E8h] [bp+20h]@1

  v33 = kCurTime;
  v32 = kInfo;
  v31 = dwRegSerial;
  v30 = this;
  v4 = &v16;
  for ( i = 300i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v29 = (unsigned int64_t)&v16 ^ _security_cookie;
  v6 = kCurTime->wMilliseconds;
  v7 = kCurTime->wSecond;
  v8 = kCurTime->wMinute;
  v9 = kCurTime->wHour;
  v10 = v33->wDay;
  v11 = v33->wMonth;
  v12 = v33->wYear;
  v13 = v32->byRace;
  v14 = v32->byType;
  v27 = v32->bySellTurm;
  v26 = v32->dwPrice;
  v25 = v32->dwOwnerSerial;
  v24 = v6;
  v23 = v7;
  v22 = v8;
  v21 = v9;
  v20 = v10;
  v19 = v11;
  v18 = v12;
  v17 = v13;
  sprintf(&Dest, "{ CALL pUpdate_sellInfo( %u, %d, %u,'%04d-%02d-%02d %02d:%02d:%02d.%03d', %d, %u, %u ) }", v14, v31);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v30->vfptr, &Dest, 1);
}
