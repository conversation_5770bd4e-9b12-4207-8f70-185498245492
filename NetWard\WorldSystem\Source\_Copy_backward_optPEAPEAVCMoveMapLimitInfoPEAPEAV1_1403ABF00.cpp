#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Copy_backward_opt@PEAPEAVCMoveMapLimitInfo@@PEAPEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAPEAVCMoveMapLimitInfo@@PEAPEAV1@00Urandom_access_iterator_tag@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403ABF00

CMoveMapLimitInfo ** std::_Copy_backward_opt<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *,std::random_access_iterator_tag>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo **_Dest, std::random_access_iterator_tag __formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  int64_t *v6; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v9; // [sp+0h] [bp-38h]@1
  int64_t v10; // [sp+20h] [bp-18h]@4
  void *Dst; // [sp+28h] [bp-10h]@4
  CMoveMapLimitInfo **Src; // [sp+40h] [bp+8h]@1

  Src = _First;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v6 = -858993460;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  v10 = _Last - Src;
  Dst = &_Dest[-v10];
  if ( v10 > 0 )
    memmove_s(Dst, 8 * v10, Src, 8 * v10);
  return (CMoveMapLimitInfo **)Dst;
}
