#pragma once

#ifndef NETWARD_AUTHENTICATION_H
#define NETWARD_AUTHENTICATION_H

// NetWard Authentication System Header
// Generated from RF Online ZoneServerUD_x64 decompiled source

#include <windows.h>
#include <stdint.h>
#include <memory>
#include <vector>
#include <string>

// Forward declarations
struct CBillingVtbl;
struct CAsyncLogInfo;

// Class definitions

// CBillingVtbl
struct CBillingVtbl
{
 void *( *__vecDelDtor)(CBilling *this, unsigned int);
 void ( *Login)(CBilling *this, CUserDB *);
 void ( *Alive)(CBilling *this, CUserDB *);
 void ( *Logout)(CBilling *this, CUserDB *);
 void ( *BillingClose)(CBilling *this, char *);
 bool ( *SendMsg_Login)(CBilling *this, char *, char *, char *, int16_t, _SYSTEMTIME *, int);
};

// CAsyncLogInfo
struct CAsyncLogInfo
{
 ASYNC_LOG_TYPE m_eType;
 unsigned int m_dwLogCount;
 char *m_szLogDirPath;
 char *m_szLogFileName;
 char *m_szTypeName;
 CMyTimer *m_pkTimer;
 CNetCriticalSection m_csLock;
};


#endif // NETWARD_AUTHENTICATION_H
