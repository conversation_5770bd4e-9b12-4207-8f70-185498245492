#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_BossDieWriteLog_End@CMonster@@QEAAXXZ
 * Address: 0x1401440C0

void  CMonster::_BossDieWriteLog_End(CMonster *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-28h]@1
  CMonster *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v4->m_pMonRec->m_bMonsterCondition == 1 )
    CLogFile::Write(&CMonster::s_logTrace_Boss_Looting, "\r\n");
}
