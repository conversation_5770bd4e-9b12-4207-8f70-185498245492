#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?destroy@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEAAXPEAPEAVCMoveMapLimitRight@@@Z
 * Address: 0x140010226

void  std::allocator<CMoveMapLimitRight *>::destroy(std::allocator<CMoveMapLimitRight *> *this, CMoveMapLimitRight **_Ptr)
{
  std::allocator<CMoveMapLimitRight *>::destroy(this, _Ptr);
}
