#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetCashDBDSN@CNationSettingManager@@QEAAXPEAD000K@Z
 * Address: 0x140205750

void  CNationSettingManager::SetCashDBDSN(CNationSettingManager *this, char *szIP, char *szDBName, char *szAccount, char *szPassword, unsigned int dwPort)
{
  int64_t *v6; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v8; // [sp+0h] [bp-38h]@1
  char *v9; // [sp+20h] [bp-18h]@4
  unsigned int v10; // [sp+28h] [bp-10h]@4
  CNationSettingManager *v11; // [sp+40h] [bp+8h]@1

  v11 = this;
  v6 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v6 = -*********;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  v10 = dwPort;
  v9 = szPassword;
  CNationSettingData::SetCashDBDSN(v11->m_pData, szIP, szDBName, szAccount, szPassword, dwPort);
}
