#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_QuestDelete@CUserDB@@QEAA_NE@Z
 * Address: 0x140115620

char  CUserDB::Update_QuestDelete(CUserDB *this, char bySlotIndex)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v5; // [sp+0h] [bp-28h]@1
  CUserDB *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( (signed int)(unsigned int8_t)bySlotIndex < 30 )
  {
    if ( v6->m_AvatorData.dbQuest.m_List[(unsigned int8_t)bySlotIndex].byQuestType == 255 )
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_QuestDelete(EXIST) : slot : %d",
        v6->m_aszAvatorName,
        (unsigned int8_t)bySlotIndex);
      result = 0;
    }
    else
    {
      _QUEST_DB_BASE::_LIST::Init((_QUEST_DB_BASE::_LIST *)&v6->m_AvatorData.dbQuest + (unsigned int8_t)bySlotIndex);
      v6->m_bDataUpdate = 1;
      result = 1;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_QuestDelete(SlotIndex OVER) : slot : %d",
      v6->m_aszAvatorName,
      (unsigned int8_t)bySlotIndex);
    result = 0;
  }
  return result;
}
