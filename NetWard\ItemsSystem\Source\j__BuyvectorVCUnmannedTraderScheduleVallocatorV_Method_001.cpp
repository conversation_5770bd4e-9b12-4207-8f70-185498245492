#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Buy@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@IEAA_N_K@Z
 * Address: 0x140012C60

bool  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Buy(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, unsigned int64_t _Capacity)
{
  return std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Buy(this, _Capacity);
}
