#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Ufill@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderSortType@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x1400114BE

CUnmannedTraderSortType ** std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Ufill(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, CUnmannedTraderSortType **_Ptr, unsigned int64_t _Count, CUnmannedTraderSortType *const *_Val)
{
  return std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Ufill(
           this,
           _Ptr,
           _Count,
           _Val);
}
