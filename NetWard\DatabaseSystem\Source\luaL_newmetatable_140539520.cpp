#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: luaL_newmetatable
 * Address: 0x140539520

signed int64_t  luaL_newmetatable(int64_t a1)
{
  int64_t v1; // rbx@1
  signed int64_t result; // rax@2

  v1 = a1;
  lua_getfield(a1, -10000);
  if ( (unsigned int)lua_type(v1, -1) )
  {
    result = 0i64;
  }
  else
  {
    lua_settop(v1, -2);
    lua_createtable(v1, 0, 0);
    lua_pushvalue(v1, -1);
    lua_setfield(v1, -10000);
    result = 1i64;
  }
  return result;
}
