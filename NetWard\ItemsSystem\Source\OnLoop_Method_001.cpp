#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?OnLoop@CMgrAvatorItemHistory@@QEAAXXZ
 * Address: 0x140236220

void  CMgrAvatorItemHistory::OnLoop(CMgrAvatorItemHistory *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-28h]@1
  CMgrAvatorItemHistory *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( CMyTimer::CountingTimer(&v4->m_tmrUpdateTime) )
  {
    _strdate(v4->m_szCurDate);
    v4->m_szCurDate[5] = 0;
    _strtime(v4->m_szCurTime);
    v4->m_szCurTime[5] = 0;
  }
}
