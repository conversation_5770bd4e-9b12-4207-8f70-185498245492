#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?max_size@?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x140361790

unsigned int64_t  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::max_size(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  return std::allocator<CUnmannedTraderRegistItemInfo>::max_size(&v5->_Alval);
}
