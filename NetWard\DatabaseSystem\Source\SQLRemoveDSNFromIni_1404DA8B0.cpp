#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLRemoveDSNFromIni
 * Address: 0x1404DA8B0

int  SQLRemoveDSNFromIni(const char *lpszDSN)
{
  const char *v1; // rbx@1
  int64_t ( *v2)(); // rax@1
  int result; // eax@2

  v1 = lpszDSN;
  v2 = ODBC___GetSetupProc("SQLRemoveDSNFromIni");
  if ( v2 )
    result = ((int ( *)(const char *))v2)(v1);
  else
    result = 0;
  return result;
}
