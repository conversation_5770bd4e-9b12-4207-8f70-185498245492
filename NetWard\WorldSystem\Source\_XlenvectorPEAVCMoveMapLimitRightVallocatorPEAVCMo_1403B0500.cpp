#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_Xlen@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@KAXXZ
 * Address: 0x1403B0500

void  __noreturn std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Xlen(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-B8h]@1
  std::length_error v4; // [sp+20h] [bp-98h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > _Message; // [sp+68h] [bp-50h]@4
  unsigned int8_t v6; // [sp+98h] [bp-20h]@4
  int64_t v7; // [sp+A0h] [bp-18h]@4

  v1 = &v3;
  for ( i = 44i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v7 = -2i64;
  memset(&v6, 0, sizeof(v6));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &_Message,
    "vector<T> too long",
    v6);
  std::length_error::length_error(&v4, &_Message);
  CxxThrowException_0(&v4, &TI3_AVlength_error_std__);
}
