#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLRemoveDriverManager
 * Address: 0x1404DAAAC

int  SQLRemoveDriverManager(unsigned int *lpdwUsageCount)
{
  unsigned int *v1; // rbx@1
  int64_t ( *v2)(); // rax@1
  int result; // eax@2

  v1 = lpdwUsageCount;
  v2 = ODBC___GetSetupProc("SQLRemoveDriverManager");
  if ( v2 )
    result = ((int ( *)(unsigned int *))v2)(v1);
  else
    result = 0;
  return result;
}
