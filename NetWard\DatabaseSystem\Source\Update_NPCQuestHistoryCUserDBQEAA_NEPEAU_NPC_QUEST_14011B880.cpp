#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_NPCQuestHistory@CUserDB@@QEAA_NEPEAU_NPC_QUEST_HISTORY@_QUEST_DB_BASE@@@Z
 * Address: 0x14011B880

char  CUserDB::Update_NPCQuestHistory(CUserDB *this, char byIndex, _QUEST_DB_BASE::_NPC_QUEST_HISTORY *pHisData)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-28h]@1
  CUserDB *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned int8_t)byIndex < 70 )
  {
    memcpy_0(&v7->m_AvatorData.dbQuest.m_History[(unsigned int8_t)byIndex], pHisData, 0x11ui64);
    result = 1;
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_NPCQuestHistory(Index OVER) : %d",
      v7->m_aszAvatorName,
      (unsigned int8_t)byIndex);
    result = 0;
  }
  return result;
}
