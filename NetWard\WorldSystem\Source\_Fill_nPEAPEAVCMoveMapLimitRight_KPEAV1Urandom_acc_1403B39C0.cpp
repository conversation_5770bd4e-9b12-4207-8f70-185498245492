#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Fill_n@PEAPEAVCMoveMapLimitRight@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVCMoveMapLimitRight@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403B39C0

void  std::_Fill_n<CMoveMapLimitRight * *,unsigned int64_t,CMoveMapLimitRight *,std::random_access_iterator_tag>(CMoveMapLimitRight **_First, unsigned int64_t _Count, CMoveMapLimitRight *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  int64_t *v5; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-38h]@1
  std::_Range_checked_iterator_tag v8; // [sp+20h] [bp-18h]@4
  CMoveMapLimitRight **_Firsta; // [sp+40h] [bp+8h]@1

  _Firsta = _First;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  std::_Fill_n<CMoveMapLimitRight * *,unsigned int64_t,CMoveMapLimitRight *>(_Firsta, _Count, _Val, v8);
}
