#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?ProcSellWaitItem@CUnmannedTraderUserInfo@@AEAAXPEAU_qry_case_unmandtrader_log_in_proc_update_complete@@EPEAVCLogFile@@@Z
 * Address: 0x140008B3E

void  CUnmannedTraderUserInfo::ProcSellWaitItem(CUnmannedTraderUserInfo *this, _qry_case_unmandtrader_log_in_proc_update_complete *pkResult, char byGroupType, CLogFile *pkLogger)
{
  CUnmannedTraderUserInfo::ProcSellWaitItem(this, pkResult, byGroupType, pkLogger);
}
