#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$unchecked_uninitialized_copy@PEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV1@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@stdext@@YAPEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@Z
 * Address: 0x14039C250

CUnmannedTraderGroupDivisionVersionInfo * stdext::unchecked_uninitialized_copy<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(CUnmannedTraderGroupDivisionVersionInfo *_First, CUnmannedTraderGroupDivisionVersionInfo *_Last, CUnmannedTraderGroupDivisionVersionInfo *_Dest, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Al)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v9; // [sp+31h] [bp-17h]@4
  CUnmannedTraderGroupDivisionVersionInfo *__formal; // [sp+50h] [bp+8h]@1
  CUnmannedTraderGroupDivisionVersionInfo *_Lasta; // [sp+58h] [bp+10h]@1
  CUnmannedTraderGroupDivisionVersionInfo *_Desta; // [sp+60h] [bp+18h]@1
  std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  _Desta = _Dest;
  _Lasta = _Last;
  __formal = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Ptr_cat<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *>(
         &__formal,
         &_Desta);
  return std::_Uninit_copy<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(
           __formal,
           _Lasta,
           _Desta,
           _Ala,
           v9,
           v8);
}
