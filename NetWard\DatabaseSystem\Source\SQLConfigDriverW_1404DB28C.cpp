#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLConfigDriverW
 * Address: 0x1404DB28C

int  SQLConfigDriverW(HWND__ *hwndParent, unsigned int16_t fRequest, const unsigned int16_t *lpszDriver, const unsigned int16_t *lpszArgs, unsigned int16_t *lpszMsg, unsigned int16_t cbMsgMax, unsigned int16_t *pcbMsgOut)
{
  HWND__ *v7; // rbp@1
  const unsigned int16_t *v8; // rbx@1
  const unsigned int16_t *v9; // rdi@1
  unsigned int16_t v10; // si@1
  int64_t ( *v11)(); // rax@1
  int result; // eax@2

  v7 = hwndParent;
  v8 = lpszArgs;
  v9 = lpszDriver;
  v10 = fRequest;
  v11 = ODBC___GetSetupProc("SQLConfigDriverW");
  if ( v11 )
    result = ((int ( *)(HWND__ *, _QWORD, const unsigned int16_t *, const unsigned int16_t *))v11)(
               v7,
               v10,
               v9,
               v8);
  else
    result = 0;
  return result;
}
