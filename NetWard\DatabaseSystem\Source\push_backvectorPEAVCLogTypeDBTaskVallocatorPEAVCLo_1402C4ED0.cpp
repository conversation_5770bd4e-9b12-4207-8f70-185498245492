#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?push_back@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAAXAEBQEAVCLogTypeDBTask@@@Z
 * Address: 0x1402C4ED0

void  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::push_back(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, CLogTypeDBTask *const *_Val)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  unsigned int64_t v4; // rax@4
  int64_t v5; // [sp+0h] [bp-78h]@1
  char v6; // [sp+20h] [bp-58h]@6
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *result; // [sp+38h] [bp-40h]@6
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > v8; // [sp+40h] [bp-38h]@6
  unsigned int64_t v9; // [sp+58h] [bp-20h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v10; // [sp+60h] [bp-18h]@6
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v11; // [sp+80h] [bp+8h]@1
  CLogTypeDBTask **_Vala; // [sp+88h] [bp+10h]@1

  _Vala = (CLogTypeDBTask **)_Val;
  v11 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v9 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::size(v11);
  v4 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::capacity(v11);
  if ( v9 >= v4 )
  {
    result = (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v6;
    v10 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::end(
            v11,
            (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v6);
    std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::insert(v11, &v8, v10, _Vala);
    std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(&v8);
  }
  else
  {
    v11->_Mylast = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Ufill(
                     v11,
                     v11->_Mylast,
                     1ui64,
                     _Vala);
  }
}
