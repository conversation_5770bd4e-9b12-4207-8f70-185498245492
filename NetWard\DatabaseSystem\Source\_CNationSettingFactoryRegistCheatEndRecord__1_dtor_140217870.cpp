#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CNationSettingFactory::RegistCheatEndRecord_::_1_::dtor$0
 * Address: 0x140217870

void  CNationSettingFactory::RegistCheatEndRecord_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::~_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(*(std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > **)(a2 + 144));
}
