#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_fill_n@PEAPEAVCUnmannedTraderDivisionInfo@@_KPEAV1@@stdext@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@_KAEBQEAV1@@Z
 * Address: 0x14000C35B

void  stdext::unchecked_fill_n<CUnmannedTraderDivisionInfo * *,unsigned int64_t,CUnmannedTraderDivisionInfo *>(CUnmannedTraderDivisionInfo **_First, unsigned int64_t _Count, CUnmannedTraderDivisionInfo *const *_Val)
{
  stdext::unchecked_fill_n<CUnmannedTraderDivisionInfo * *,unsigned int64_t,CUnmannedTraderDivisionInfo *>(
    _First,
    _Count,
    _Val);
}
