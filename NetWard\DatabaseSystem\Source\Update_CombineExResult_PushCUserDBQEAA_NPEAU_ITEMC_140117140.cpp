#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_CombineExResult_Push@CUserDB@@QEAA_NPEAU_ITEMCOMBINE_DB_BASE@@@Z
 * Address: 0x140117140

char  CUserDB::Update_CombineExResult_Push(CUserDB *this, _ITEMCOMBINE_DB_BASE *pItemCombineDB_IN)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-38h]@1
  void *Dst; // [sp+20h] [bp-18h]@4
  CUserDB *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  Dst = &v7->m_AvatorData.dbItemCombineEx;
  memcpy_0(&v7->m_AvatorData.dbItemCombineEx, pItemCombineDB_IN, 0x134ui64);
  v7->m_bDataUpdate = 1;
  return 1;
}
