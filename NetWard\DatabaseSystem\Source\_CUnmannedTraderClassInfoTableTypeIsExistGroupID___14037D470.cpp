#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CUnmannedTraderClassInfoTableType::IsExistGroupID_::_1_::dtor$0
 * Address: 0x14037D470

void  CUnmannedTraderClassInfoTableType::IsExistGroupID_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>((std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)(a2 + 40));
}
