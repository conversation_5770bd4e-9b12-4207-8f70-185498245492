#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?RegistCheatTableOnlyInternal@CNationSettingFactory@@IEAA_NPEAVCNationSettingData@@@Z
 * Address: 0x1402168B0

bool  CNationSettingFactory::RegistCheatTableOnlyInternal(CNationSettingFactory *this, CNationSettingData *pkData)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  bool result; // al@5
  int64_t v5; // [sp+0h] [bp-38h]@1
  CNationSettingFactory *v6; // [sp+40h] [bp+8h]@1
  CNationSettingData *pkDataa; // [sp+48h] [bp+10h]@1

  pkDataa = pkData;
  v6 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( CMainThread::IsReleaseServiceMode(&g_Main) )
  {
    result = 1;
  }
  else if ( CNationSettingFactory::RegistCheat(
              v6,
              pkDataa,
              "",
              (bool ( *)(CPlayer *))ct_change_degree,
              31,
              7) )
  {
    if ( CNationSettingFactory::RegistCheat(
           v6,
           pkDataa,
           "show me the dalant",
           (bool ( *)(CPlayer *))ct_alter_dalant,
           30,
           7) )
    {
      if ( CNationSettingFactory::RegistCheat(
             v6,
             pkDataa,
             "show me the gold",
             (bool ( *)(CPlayer *))ct_alter_gold,
             30,
             7) )
      {
        if ( CNationSettingFactory::RegistCheat(
               v6,
               pkDataa,
               "Ӹʱȭ",
               (bool ( *)(CPlayer *))ct_SetPatriarchClear,
               52,
               7) )
        {
          if ( CNationSettingFactory::RegistCheat(
                 v6,
                 pkDataa,
                 "",
                 (bool ( *)(CPlayer *))ct_SetPatriarchGroup,
                 52,
                 7) )
          {
            if ( CNationSettingFactory::RegistCheat(
                   v6,
                   pkDataa,
                   "BossMe",
                   (bool ( *)(CPlayer *))ct_SetPatriarchGroup,
                   52,
                   7) )
            {
              if ( CNationSettingFactory::RegistCheat(
                     v6,
                     pkDataa,
                     "ѽð",
                     (bool ( *)(CPlayer *))ct_InformCristalBattleBeforeAnHour,
                     12,
                     7) )
              {
                if ( CNationSettingFactory::RegistCheat(
                       v6,
                       pkDataa,
                       "OneHourAfter",
                       (bool ( *)(CPlayer *))ct_InformCristalBattleBeforeAnHour,
                       12,
                       7) )
                {
                  if ( CNationSettingFactory::RegistCheat(
                         v6,
                         pkDataa,
                         "",
                         (bool ( *)(CPlayer *))ct_SetSettleOwnerGuild,
                         52,
                         7) )
                  {
                    if ( CNationSettingFactory::RegistCheat(
                           v6,
                           pkDataa,
                           "Set Settle Owner Guild",
                           (bool ( *)(CPlayer *))ct_SetSettleOwnerGuild,
                           52,
                           7) )
                    {
                      if ( CNationSettingFactory::RegistCheat(
                             v6,
                             pkDataa,
                             "ʱȭ",
                             (bool ( *)(CPlayer *))ct_ClearSettleOwnerGuild,
                             52,
                             7) )
                      {
                        if ( CNationSettingFactory::RegistCheat(
                               v6,
                               pkDataa,
                               "Clear Settle Owner Guild",
                               (bool ( *)(CPlayer *))ct_ClearSettleOwnerGuild,
                               52,
                               7) )
                        {
                          if ( CNationSettingFactory::RegistCheat(
                                 v6,
                                 pkDataa,
                                 "",
                                 (bool ( *)(CPlayer *))ct_ReqPunishment,
                                 52,
                                 7) )
                          {
                            if ( CNationSettingFactory::RegistCheat(
                                   v6,
                                   pkDataa,
                                   "溯",
                                   (bool ( *)(CPlayer *))ct_ReqChangeHonorGuild,
                                   52,
                                   7) )
                            {
                              if ( CNationSettingFactory::RegistCheat(
                                     v6,
                                     pkDataa,
                                     "gotochar",
                                     (bool ( *)(CPlayer *))ct_goto_char,
                                     52,
                                     7) )
                              {
                                if ( CNationSettingFactory::RegistCheat(
                                       v6,
                                       pkDataa,
                                       "gotomon",
                                       (bool ( *)(CPlayer *))ct_goto_monster,
                                       28,
                                       7) )
                                {
                                  if ( CNationSettingFactory::RegistCheat(
                                         v6,
                                         pkDataa,
                                         "ĳν",
                                         (bool ( *)(CPlayer *))ct_CdeStart,
                                         28,
                                         7) )
                                  {
                                    if ( CNationSettingFactory::RegistCheat(
                                           v6,
                                           pkDataa,
                                           "exception",
                                           (bool ( *)(CPlayer *))ct_exception,
                                           16,
                                           0) )
                                    {
                                      if ( CNationSettingFactory::RegistCheat(
                                             v6,
                                             pkDataa,
                                             "premium",
                                             (bool ( *)(CPlayer *))ct_pcroom_premium,
                                             28,
                                             7) )
                                      {
                                        if ( CNationSettingFactory::RegistCheat(
                                               v6,
                                               pkDataa,
                                               "",
                                               (bool ( *)(CPlayer *))ct_SetGuildGrade,
                                               12,
                                               7) )
                                        {
                                          if ( CNationSettingFactory::RegistCheat(
                                                 v6,
                                                 pkDataa,
                                                 "Set Guild Grade",
                                                 (bool ( *)(CPlayer *))ct_SetGuildGrade,
                                                 12,
                                                 7) )
                                          {
                                            if ( CNationSettingFactory::RegistCheat(
                                                   v6,
                                                   pkDataa,
                                                   "",
                                                   (bool ( *)(CPlayer *))ct_SetGuildGradeByName,
                                                   12,
                                                   7) )
                                            {
                                              if ( CNationSettingFactory::RegistCheat(
                                                     v6,
                                                     pkDataa,
                                                     "Set Guild Grade By Name",
                                                     (bool ( *)(CPlayer *))ct_SetGuildGradeByName,
                                                     12,
                                                     7) )
                                              {
                                                if ( CNationSettingFactory::RegistCheat(
                                                       v6,
                                                       pkDataa,
                                                       "",
                                                       (bool ( *)(CPlayer *))ct_SetGuildGradeByGuildSerial,
                                                       12,
                                                       7) )
                                                {
                                                  if ( CNationSettingFactory::RegistCheat(
                                                         v6,
                                                         pkDataa,
                                                         "Set Guild Grade By GuildSerial",
                                                         (bool ( *)(CPlayer *))ct_SetGuildGradeByGuildSerial,
                                                         12,
                                                         7) )
                                                  {
                                                    if ( CNationSettingFactory::RegistCheat(
                                                           v6,
                                                           pkDataa,
                                                           "渶",
                                                           (bool ( *)(CPlayer *))ct_SetGuildMaster,
                                                           12,
                                                           7) )
                                                    {
                                                      if ( CNationSettingFactory::RegistCheat(
                                                             v6,
                                                             pkDataa,
                                                             "MasterMe",
                                                             (bool ( *)(CPlayer *))ct_SetGuildMaster,
                                                             12,
                                                             7) )
                                                      {
                                                        if ( CNationSettingFactory::RegistCheat(
                                                               v6,
                                                               pkDataa,
                                                               "",
                                                               (bool ( *)(CPlayer *))ct_SetMaxLevelLimit,
                                                               12,
                                                               7) )
                                                        {
                                                          if ( CNationSettingFactory::RegistCheat(
                                                                 v6,
                                                                 pkDataa,
                                                                 "altlv",
                                                                 (bool ( *)(CPlayer *))ct_SetMaxLevelLimit,
                                                                 12,
                                                                 7) )
                                                          {
                                                            if ( CNationSettingFactory::RegistCheat(
                                                                   v6,
                                                                   pkDataa,
                                                                   "setexprate",
                                                                   (bool ( *)(CPlayer *))ct_set_exp_rate,
                                                                   28,
                                                                   7) )
                                                            {
                                                              if ( CNationSettingFactory::RegistCheat(
                                                                     v6,
                                                                     pkDataa,
                                                                     "gotonpc",
                                                                     (bool ( *)(CPlayer *))ct_goto_npc,
                                                                     28,
                                                                     7) )
                                                              {
                                                                if ( CNationSettingFactory::RegistCheat(
                                                                       v6,
                                                                       pkDataa,
                                                                       "npcquest",
                                                                       (bool ( *)(CPlayer *))ct_request_npc_quest,
                                                                       28,
                                                                       7) )
                                                                {
                                                                  if ( CNationSettingFactory::RegistCheat(
                                                                         v6,
                                                                         pkDataa,
                                                                         "delquest",
                                                                         (bool ( *)(CPlayer *))ct_request_delete_quest,
                                                                         28,
                                                                         7) )
                                                                  {
                                                                    if ( CNationSettingFactory::RegistCheat(
                                                                           v6,
                                                                           pkDataa,
                                                                           "manageguild",
                                                                           (bool ( *)(CPlayer *))ct_manage_guild,
                                                                           28,
                                                                           7) )
                                                                    {
                                                                      if ( CNationSettingFactory::RegistCheat(
                                                                             v6,
                                                                             pkDataa,
                                                                             "masterelect",
                                                                             (bool ( *)(CPlayer *))ct_change_master_elect,
                                                                             28,
                                                                             7) )
                                                                      {
                                                                        if ( CNationSettingFactory::RegistCheat(
                                                                               v6,
                                                                               pkDataa,
                                                                               "Ǯ",
                                                                               (bool ( *)(CPlayer *))ct_release_punishment,
                                                                               28,
                                                                               7) )
                                                                        {
                                                                          if ( CNationSettingFactory::RegistCheat(
                                                                                 v6,
                                                                                 pkDataa,
                                                                                 "conevent",
                                                                                 (bool ( *)(CPlayer *))ct_ConEventStart,
                                                                                 28,
                                                                                 7) )
                                                                          {
                                                                            result = CNationSettingFactory::RegistCheat(
                                                                                       v6,
                                                                                       pkDataa,
                                                                                       "*",
                                                                                       (bool ( *)(CPlayer *))ct_loot_item,
                                                                                       28,
                                                                                       7) != 0;
                                                                          }
                                                                          else
                                                                          {
                                                                            result = 0;
                                                                          }
                                                                        }
                                                                        else
                                                                        {
                                                                          result = 0;
                                                                        }
                                                                      }
                                                                      else
                                                                      {
                                                                        result = 0;
                                                                      }
                                                                    }
                                                                    else
                                                                    {
                                                                      result = 0;
                                                                    }
                                                                  }
                                                                  else
                                                                  {
                                                                    result = 0;
                                                                  }
                                                                }
                                                                else
                                                                {
                                                                  result = 0;
                                                                }
                                                              }
                                                              else
                                                              {
                                                                result = 0;
                                                              }
                                                            }
                                                            else
                                                            {
                                                              result = 0;
                                                            }
                                                          }
                                                          else
                                                          {
                                                            result = 0;
                                                          }
                                                        }
                                                        else
                                                        {
                                                          result = 0;
                                                        }
                                                      }
                                                      else
                                                      {
                                                        result = 0;
                                                      }
                                                    }
                                                    else
                                                    {
                                                      result = 0;
                                                    }
                                                  }
                                                  else
                                                  {
                                                    result = 0;
                                                  }
                                                }
                                                else
                                                {
                                                  result = 0;
                                                }
                                              }
                                              else
                                              {
                                                result = 0;
                                              }
                                            }
                                            else
                                            {
                                              result = 0;
                                            }
                                          }
                                          else
                                          {
                                            result = 0;
                                          }
                                        }
                                        else
                                        {
                                          result = 0;
                                        }
                                      }
                                      else
                                      {
                                        result = 0;
                                      }
                                    }
                                    else
                                    {
                                      result = 0;
                                    }
                                  }
                                  else
                                  {
                                    result = 0;
                                  }
                                }
                                else
                                {
                                  result = 0;
                                }
                              }
                              else
                              {
                                result = 0;
                              }
                            }
                            else
                            {
                              result = 0;
                            }
                          }
                          else
                          {
                            result = 0;
                          }
                        }
                        else
                        {
                          result = 0;
                        }
                      }
                      else
                      {
                        result = 0;
                      }
                    }
                    else
                    {
                      result = 0;
                    }
                  }
                  else
                  {
                    result = 0;
                  }
                }
                else
                {
                  result = 0;
                }
              }
              else
              {
                result = 0;
              }
            }
            else
            {
              result = 0;
            }
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
