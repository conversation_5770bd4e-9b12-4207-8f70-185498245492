#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CUnmannedTraderClassInfoTableCodeType::GetGroupID_::_1_::dtor$2
 * Address: 0x1403775B0

void  CUnmannedTraderClassInfoTableCodeType::GetGroupID_::_1_::dtor_2(int64_t a1, int64_t a2)
{
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>((std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)(a2 + 120));
}
