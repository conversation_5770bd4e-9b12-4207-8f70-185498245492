#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Copy_backward_opt@PEAVCUnmannedTraderRegistItemInfo@@PEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAVCUnmannedTraderRegistItemInfo@@PEAV1@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400049BC

CUnmannedTraderRegistItemInfo * std::_Copy_backward_opt<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::random_access_iterator_tag>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Dest, std::random_access_iterator_tag __formal, std::_Nonscalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Copy_backward_opt<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::random_access_iterator_tag>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
