#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?ProcUpdate@CUnmannedTraderLazyCleaner@@AEAAEEPEBU_SYSTEMTIME@@PEA_N@Z
 * Address: 0x140013AC5

char  CUnmannedTraderLazyCleaner::ProcUpdate(CUnmannedTraderLazyCleaner *this, char byState, _SYSTEMTIME *pCurTime, bool *pbRemain)
{
  return CUnmannedTraderLazyCleaner::ProcUpdate(this, byState, pCurTime, pbRemain);
}
