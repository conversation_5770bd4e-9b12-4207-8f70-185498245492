#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetCurBspMap@CGameObject@@QEAA_NPEAVCMapData@@@Z
 * Address: 0x14017AF30

char  CGameObject::SetCurBspMap(CGameObject *this, CMapData *pMap)
{
  char result; // al@2

  if ( this->m_pCurMap == pMap )
  {
    result = 0;
  }
  else
  {
    this->m_pCurMap = pMap;
    result = 1;
  }
  return result;
}
