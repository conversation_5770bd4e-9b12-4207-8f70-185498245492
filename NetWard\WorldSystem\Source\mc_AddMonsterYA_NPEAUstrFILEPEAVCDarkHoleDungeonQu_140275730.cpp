#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?mc_AddMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x140275730

bool  mc_AddMonster(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  bool result; // al@5
  int64_t v6; // rax@11
  int64_t v7; // [sp+0h] [bp-D8h]@1
  _react_obj poutReactObject; // [sp+48h] [bp-90h]@4
  _react_area poutReactArea; // [sp+88h] [bp-50h]@6
  int v10; // [sp+A4h] [bp-34h]@8
  __add_monster *v11; // [sp+A8h] [bp-30h]@13
  __add_monster *v12; // [sp+B0h] [bp-28h]@10
  int64_t v13; // [sp+B8h] [bp-20h]@4
  __add_monster *v14; // [sp+C0h] [bp-18h]@11
  strFILE *fstra; // [sp+E0h] [bp+8h]@1
  CDarkHoleDungeonQuestSetup *pSetupa; // [sp+E8h] [bp+10h]@1
  char *v17; // [sp+F0h] [bp+18h]@1

  v17 = pszoutErrMsg;
  pSetupa = pSetup;
  fstra = fstr;
  v3 = &v7;
  for ( i = 52i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v13 = -2i64;
  _react_obj::_react_obj(&poutReactObject);
  if ( GetReactObject(fstra, pSetupa, &poutReactObject, 1, 6u, v17, 0i64) )
  {
    _react_area::_react_area(&poutReactArea);
    if ( GetReactArea(fstra, pSetupa, &poutReactArea, 3u, v17, 0i64) )
    {
      v10 = pSetupa->m_pCurLoadMission->nAddMonsterNum;
      if ( v10 < 32 )
      {
        v12 = (__add_monster *)operator new(0x30ui64);
        if ( v12 )
        {
          __add_monster::__add_monster(v12);
          v14 = (__add_monster *)v6;
        }
        else
        {
          v14 = 0i64;
        }
        v11 = v14;
        pSetupa->m_pCurLoadMission->pAddMonster[v10] = v14;
        _react_obj::copy(&pSetupa->m_pCurLoadMission->pAddMonster[v10]->ReactObj, &poutReactObject);
        _react_area::copy(&pSetupa->m_pCurLoadMission->pAddMonster[v10]->ReactArea, &poutReactArea);
        ++pSetupa->m_pCurLoadMission->nAddMonsterNum;
        result = 1;
      }
      else
      {
        result = _false(fstra, pSetupa);
      }
    }
    else
    {
      result = _false(fstra, pSetupa);
    }
  }
  else
  {
    result = _false(fstra, pSetupa);
  }
  return result;
}
