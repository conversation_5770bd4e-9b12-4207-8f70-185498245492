#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CUnmannedTraderClassInfoTableCodeType::operator__::_1_::dtor$1
 * Address: 0x140377780

void  CUnmannedTraderClassInfoTableCodeType::operator__::_1_::dtor_1(int64_t a1, int64_t a2)
{
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(*(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > **)(a2 + 88));
}
