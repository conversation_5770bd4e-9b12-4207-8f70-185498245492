#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?LoadRecordHeader@CRecordData@@AEAA_NPEAXPEAD@Z
 * Address: 0x140044450

char  CRecordData::LoadRecordHeader(CRecordData *this, void *hFile, char *pszErrMsg)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-A8h]@1
  LPOVERLAPPED lpOverlapped; // [sp+20h] [bp-88h]@7
  int v8; // [sp+28h] [bp-80h]@7
  int v9; // [sp+30h] [bp-78h]@7
  int v10; // [sp+38h] [bp-70h]@7
  int v11; // [sp+40h] [bp-68h]@7
  _record_bin_header Buffer; // [sp+58h] [bp-50h]@4
  unsigned int v13; // [sp+74h] [bp-34h]@4
  unsigned int NumberOfBytesRead; // [sp+84h] [bp-24h]@4
  CRecordData *v15; // [sp+B0h] [bp+8h]@1
  HANDLE hFilea; // [sp+B8h] [bp+10h]@1
  char *Dest; // [sp+C0h] [bp+18h]@1

  Dest = pszErrMsg;
  hFilea = hFile;
  v15 = this;
  v3 = &v6;
  for ( i = 40i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  _record_bin_header::_record_bin_header(&Buffer);
  ReadFile(hFilea, &Buffer, 4u, &NumberOfBytesRead, 0i64);
  ReadFile(hFilea, &Buffer.m_nFieldNum, 4u, &NumberOfBytesRead, 0i64);
  ReadFile(hFilea, &Buffer.m_nRecordSize, 4u, &NumberOfBytesRead, 0i64);
  v13 = Buffer.m_nRecordSize * Buffer.m_nRecordNum + 12;
  if ( !v15->m_bLoad )
    goto LABEL_16;
  if ( memcmp_0(&Buffer, &v15->m_Header, 0xCui64) )
  {
    if ( Dest )
    {
      v11 = Buffer.m_nRecordSize;
      v10 = Buffer.m_nFieldNum;
      v9 = Buffer.m_nRecordNum;
      v8 = v15->m_Header.m_nRecordSize;
      LODWORD(lpOverlapped) = v15->m_Header.m_nFieldNum;
      sprintf(
        Dest,
        "%s ε (%d-%d-%d) εϷ (%d-%d-%d) ٸ",
        v15->m_szFileName,
        v15->m_Header.m_nRecordNum);
    }
    return 0;
  }
  if ( v15->m_dwTotalSize == v13 )
  {
LABEL_16:
    memcpy_0(&v15->m_Header, &Buffer, 0xCui64);
    v15->m_dwTotalSize = v13;
    return 1;
  }
  if ( Dest )
  {
    LODWORD(lpOverlapped) = v13;
    sprintf(
      Dest,
      "%s ε (%d) εϷ (%d) ٸ",
      v15->m_szFileName,
      v15->m_dwTotalSize);
  }
  return 0;
}
