#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLRemoveTranslatorW
 * Address: 0x1404DB224

int  SQLRemoveTranslatorW(const unsigned int16_t *lpszTranslator, unsigned int *lpdwUsageCount)
{
  const unsigned int16_t *v2; // rdi@1
  unsigned int *v3; // rbx@1
  int64_t ( *v4)(); // rax@1
  int result; // eax@2

  v2 = lpszTranslator;
  v3 = lpdwUsageCount;
  v4 = ODBC___GetSetupProc("SQLRemoveTranslatorW");
  if ( v4 )
    result = ((int ( *)(const unsigned int16_t *, unsigned int *))v4)(v2, v3);
  else
    result = 0;
  return result;
}
