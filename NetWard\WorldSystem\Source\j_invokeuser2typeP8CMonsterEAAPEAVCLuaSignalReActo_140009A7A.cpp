#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?invoke@?$user2type@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@SAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAUlua_State@@H@Z
 * Address: 0x140009A7A

CLuaSignalReActor *( * lua_tinker::user2type<CLuaSignalReActor * (CMonster::*)(void)>::invoke(lua_tinker::user2type<CLuaSignalReActor * ( CMonster::*)(void)> *this, struct lua_State *L, int index))(CMonster *this)
{
  return lua_tinker::user2type<CLuaSignalReActor * (CMonster::*)(void)>::invoke(this, L, index);
}
