#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?Search@CUnmannedTraderController@@QEAAXGPEAU_unmannedtrader_search_list_request_cl<PERSON>@@@Z
 * Address: 0x1400126A2

void  CUnmannedTraderController::Search(CUnmannedTraderController *this, unsigned int16_t wInx, _unmannedtrader_search_list_request_clzo *pRequest)
{
  CUnmannedTraderController::Search(this, wInx, pRequest);
}
