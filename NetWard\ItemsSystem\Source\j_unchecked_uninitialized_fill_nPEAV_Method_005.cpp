#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCUnmannedTraderUserInfo@@_KV1@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@stdext@@YAXPEAVCUnmannedTraderUserInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderUserInfo@@@std@@@Z
 * Address: 0x140012968

void  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderUserInfo *,unsigned int64_t,CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(CUnmannedTraderUserInfo *_First, unsigned int64_t _Count, CUnmannedTraderUserInfo *_Val, std::allocator<CUnmannedTraderUserInfo> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderUserInfo *,unsigned int64_t,CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(
    _First,
    _Count,
    _Val,
    _Al);
}
