#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Select_utSellWaitItems_SalesTotals@CRFWorldDatabase@@QEAAEEKPEAK@Z
 * Address: 0x1404C8C60

char  CRFWorldDatabase::Select_utSellWaitItems_SalesTotals(CRFWorldDatabase *this, char byType, unsigned int dwOwnor, unsigned int *pSalesTotals)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v7; // [sp+0h] [bp-488h]@1
  char _Dest[1024]; // [sp+40h] [bp-448h]@4
  char v9; // [sp+444h] [bp-44h]@4
  int v10; // [sp+448h] [bp-40h]@8
  unsigned int16_t ColumnNumber; // [sp+454h] [bp-34h]@8
  unsigned int64_t v12; // [sp+470h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+490h] [bp+8h]@1
  unsigned int *v14; // [sp+4A8h] [bp+20h]@1

  v14 = pSalesTotals;
  v13 = this;
  v4 = &v7;
  for ( i = 288i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v12 = (unsigned int64_t)&v7 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0x3FFui64);
  sprintf_s<1024>(
    (char (*)[1024])_Dest,
    "{ CALL pSelect_utsellwaititems_salestotals( %u, %d ) }",
    (unsigned int8_t)byType,
    dwOwnor);
  v9 = CRFNewDatabase::SQLExecDirect_RetErrCode((CRFNewDatabase *)&v13->vfptr, _Dest);
  if ( v9 )
  {
    result = v9;
  }
  else
  {
    v9 = CRFNewDatabase::SQLFetch_RetErrCode((CRFNewDatabase *)&v13->vfptr, _Dest);
    if ( v9 )
    {
      result = v9;
    }
    else
    {
      v10 = 0;
      ColumnNumber = 1;
      v9 = CRFNewDatabase::SQLGetData_RetErrCode((CRFNewDatabase *)&v13->vfptr, _Dest, &ColumnNumber, 4, v14);
      if ( v9 )
      {
        result = v9;
      }
      else
      {
        CRFNewDatabase::SelectCleanUp((CRFNewDatabase *)&v13->vfptr, _Dest);
        result = 0;
      }
    }
  }
  return result;
}
