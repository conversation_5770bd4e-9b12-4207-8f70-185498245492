#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Ptr_cat@PEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@@std@@YA?AU_Scalar_ptr_iterator_tag@0@AEAPEAPEAVCUnmannedTraderDivisionInfo@@0@Z
 * Address: 0x140007347

std::_Scalar_ptr_iterator_tag  std::_Ptr_cat<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *>(CUnmannedTraderDivisionInfo ***__formal, CUnmannedTraderDivisionInfo ***a2)
{
  return std::_Ptr_cat<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *>(__formal, a2);
}
