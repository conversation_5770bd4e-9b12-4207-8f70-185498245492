#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SubProcGotoLimitZone@CMoveMapLimitInfoPortal@@AEAAEHPEADPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403A4D10

char  CMoveMapLimitInfoPortal::SubProcGotoLimitZone@<al>(CMoveMapLimitInfoPortal *this@<rcx>, int iUserInx@<edx>, char *pRequest@<r8>, CMoveMapLimitRightInfo *pkRight@<r9>, float a5@<xmm0>)
{
  int64_t *v5; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@6
  int v8; // eax@33
  int64_t v9; // [sp+0h] [bp-78h]@1
  float *pfStartPos; // [sp+20h] [bp-58h]@37
  char *v11; // [sp+30h] [bp-48h]@4
  CGameObject *v12; // [sp+38h] [bp-40h]@7
  float pNewPos; // [sp+48h] [bp-30h]@35
  char v14; // [sp+4Ch] [bp-2Ch]@35
  CMoveMapLimitInfoPortal *v15; // [sp+80h] [bp+8h]@1
  CMoveMapLimitRightInfo *v16; // [sp+98h] [bp+20h]@1

  v16 = pkRight;
  v15 = this;
  v5 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(uint32_t*)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  v11 = pRequest;
  if ( iUserInx >= 0 && iUserInx < 2532 )
  {
    v12 = (CGameObject *)(&g_Player.vfptr + 6357 * iUserInx);
    if ( v15->m_pStoreNPC && v15->m_pkDestDummy )
    {
      if ( v15->m_iMapInx == v12->m_pCurMap->m_nMapCode )
      {
        if ( v15->m_pStoreNPC->m_pItemStore->m_pRec->m_dwIndex == *(uint32_t*)v11 )
        {
          if ( CGameObject::GetCurSecNum(v12) == -1 || v12->m_bMapLoading )
          {
            result = 23;
          }
          else if ( v12->m_bCorpse )
          {
            result = 24;
          }
          else if ( v12->m_pCurMap->m_pMapSet->m_nMapType )
          {
            result = 25;
          }
          else if ( LOBYTE(v12[226].m_wMapLayerIndex) == 1 )
          {
            result = 26;
          }
          else if ( CPlayer::IsSiegeMode((CPlayer *)v12) )
          {
            result = 27;
          }
          else if ( CPlayer::IsRidingUnit((CPlayer *)v12) )
          {
            result = 28;
          }
          else if ( _effect_parameter::GetEff_State((_effect_parameter *)v12[9].m_fOldPos, 20) )
          {
            result = 29;
          }
          else if ( _effect_parameter::GetEff_State((_effect_parameter *)v12[9].m_fOldPos, 28) )
          {
            result = 29;
          }
          else
          {
            GetSqrt(v15->m_pStoreNPC->m_pItemStore->m_pDum->m_pDumPos->m_fCenterPos, v12->m_fCurPos);
            if ( a5 <= 100.0 )
            {
              v8 = CMoveMapLimitInfo::GetType((CMoveMapLimitInfo *)&v15->vfptr);
              if ( CMoveMapLimitRightInfo::IsHaveRight(v16, v8) )
              {
                pNewPos = 0.0;
                memset(&v14, 0, 8ui64);
                if ( CMapData::GetRandPosInDummy(v12->m_pCurMap, v15->m_pkDestDummy, &pNewPos, 1) )
                {
                  pfStartPos = &pNewPos;
                  CPlayer::OutOfMap((CPlayer *)v12, v12->m_pCurMap, 0, 3, &pNewPos);
                  CPlayer::SendMsg_GotoBasePortalResult((CPlayer *)v12, 0);
                  result = 0;
                }
                else
                {
                  result = 32;
                }
              }
              else
              {
                result = 31;
              }
            }
            else
            {
              result = 30;
            }
          }
        }
        else
        {
          result = 22;
        }
      }
      else
      {
        result = 21;
      }
    }
    else
    {
      result = 5;
    }
  }
  else
  {
    result = 20;
  }
  return result;
}
