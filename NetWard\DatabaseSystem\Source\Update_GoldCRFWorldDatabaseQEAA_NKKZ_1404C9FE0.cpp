#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_Gold@CRFWorldDatabase@@QEAA_NKK@Z
 * Address: 0x1404C9FE0

bool  CRFWorldDatabase::Update_Gold(CRFWorldDatabase *this, unsigned int dwSerial, unsigned int dwGold)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-D8h]@1
  char Dest; // [sp+30h] [bp-A8h]@4
  char v8; // [sp+31h] [bp-A7h]@4
  unsigned int64_t v9; // [sp+C0h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+E0h] [bp+8h]@1

  v10 = this;
  v3 = &v6;
  for ( i = 52i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v9 = (unsigned int64_t)&v6 ^ _security_cookie;
  Dest = 0;
  memset(&v8, 0, 0x7Fui64);
  sprintf(&Dest, "UPDATE tbl_base SET Gold = %d WHERE Serial = %d", dwGold, dwSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &Dest, 1);
}
