#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_Destroy@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAXPEAVCMoveMapLimitRightInfo@@0@Z
 * Address: 0x1403A2D30

void  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Destroy(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  std::_Destroy_range<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(_First, _Last, &v6->_Alval);
}
