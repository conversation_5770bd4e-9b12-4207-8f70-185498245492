<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>

  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{C3D4E5F6-G7H8-9012-CDEF-************}</ProjectGuid>
    <RootNamespace>CombatSystem</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemGroup>
    <ClCompile Include="Source\0allocatorVCGuildBattleRewardItemGUILD_BATTLEstdQE_1403D1530.cpp" />
    <ClCompile Include="Source\0allocatorVCGuildBattleRewardItemGUILD_BATTLEstdQE_1403D1900.cpp" />
    <ClCompile Include="Source\0CashItemRemoteStoreQEAAXZ_1402F3800.cpp" />
    <ClCompile Include="Source\0CBattleTournamentInfoQEAAXZ_1403FEA40.cpp" />
    <ClCompile Include="Source\0CCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAXZ_1403CDE40.cpp" />
    <ClCompile Include="Source\0CGuildBattleControllerIEAAXZ_1403D5680.cpp" />
    <ClCompile Include="Source\0CGuildBattleGUILD_BATTLEQEAAXZ_1403EB010.cpp" />
    <ClCompile Include="Source\0CGuildBattleLoggerGUILD_BATTLEIEAAXZ_1403CE6F0.cpp" />
    <ClCompile Include="Source\0CGuildBattleRankManagerGUILD_BATTLEIEAAXZ_1403CA2F0.cpp" />
    <ClCompile Include="Source\0CGuildBattleReservedScheduleGUILD_BATTLEQEAAIZ_1403DAB60.cpp" />
    <ClCompile Include="Source\0CGuildBattleReservedScheduleListManagerGUILD_BATT_1403CD260.cpp" />
    <ClCompile Include="Source\0CGuildBattleReservedScheduleMapGroupGUILD_BATTLEQ_1403DBA50.cpp" />
    <ClCompile Include="Source\0CGuildBattleRewardItemGUILD_BATTLEQEAAXZ_1403C8EF0.cpp" />
    <ClCompile Include="Source\0CGuildBattleRewardItemManagerGUILD_BATTLEIEAAXZ_1403C93A0.cpp" />
    <ClCompile Include="Source\0CGuildBattleScheduleGUILD_BATTLEQEAAKZ_1403D9B00.cpp" />
    <ClCompile Include="Source\0CGuildBattleScheduleManagerGUILD_BATTLEIEAAXZ_1403DC830.cpp" />
    <ClCompile Include="Source\0CGuildBattleSchedulePoolGUILD_BATTLEIEAAXZ_1403DA430.cpp" />
    <ClCompile Include="Source\0CGuildBattleSchedulerGUILD_BATTLEIEAAXZ_1403DEDA0.cpp" />
    <ClCompile Include="Source\0CGuildBattleStateGUILD_BATTLEQEAAXZ_1403DEE30.cpp" />
    <ClCompile Include="Source\0CGuildBattleStateListGUILD_BATTLEQEAAHHIZ_1403DEF90.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleFieldGUILD_BATTLEQEAAXZ_1403EB7B0.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleFieldListGUILD_BATTLEIEAAXZ_1403EE250.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleGuildGUILD_BATTLEQEAAEZ_1403E04C0.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleGuildMemberGUILD_BATTLEQEAAXZ_1403DF960.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleGUILD_BATTLEQEAAKZ_1403E2E40.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleLoggerGUILD_BATTLEQEAAXZ_1403EB070.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleManagerGUILD_BATTLEIEAAXZ_1403D33C0.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateCountDownGUILD_BATTLEQEAAX_1403F0820.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateDivideGUILD_BATTLEQEAAXZ_1403F0D20.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateFinGUILD_BATTLEQEAAXZ_1403F0F20.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateGUILD_BATTLEQEAAXZ_1403F3120.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateInBattleGUILD_BATTLEQEAAXZ_1403F0950.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateListGUILD_BATTLEQEAAXZ_1403F1E80.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateListPoolGUILD_BATTLEIEAAXZ_1403F22B0.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateNotifyGUILD_BATTLEQEAAXZ_1403F06B0.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateReadyGUILD_BATTLEQEAAXZ_1403F0770.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateReturnGUILD_BATTLEQEAAXZ_1403F0E10.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateRoundGUILD_BATTLEQEAAXZ_1403F0FD0.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateRoundListGUILD_BATTLEQEAAX_1403F2150.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateRoundProcessGUILD_BATTLEQE_1403F1770.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateRoundReturnStartPosGUILD_B_1403F1B10.cpp" />
    <ClCompile Include="Source\0CNormalGuildBattleStateRoundStartGUILD_BATTLEQEAA_1403F1350.cpp" />
    <ClCompile Include="Source\0CPossibleBattleGuildListManagerGUILD_BATTLEIEAAXZ_1403C9530.cpp" />
    <ClCompile Include="Source\0CReservedGuildScheduleDayGroupGUILD_BATTLEQEAAXZ_1403CCBC0.cpp" />
    <ClCompile Include="Source\0CReservedGuildScheduleMapGroupGUILD_BATTLEQEAAXZ_1403CC340.cpp" />
    <ClCompile Include="Source\0CReservedGuildSchedulePageGUILD_BATTLEQEAAXZ_1403CBC30.cpp" />
    <ClCompile Include="Source\0CRFCashItemDatabaseQEAAXZ_1402F2AE0.cpp" />
    <ClCompile Include="Source\0MonsterSFContDamageToleracneQEAAXZ_140157E80.cpp" />
    <ClCompile Include="Source\0vectorVCGuildBattleRewardItemGUILD_BATTLEVallocat_1403D0C20.cpp" />
    <ClCompile Include="Source\0_attack_count_result_zoclQEAAXZ_1400EEED0.cpp" />
    <ClCompile Include="Source\0_ATTACK_DELAY_CHECKERQEAAXZ_140072D80.cpp" />
    <ClCompile Include="Source\0_attack_force_result_zoclQEAAXZ_1400EEDD0.cpp" />
    <ClCompile Include="Source\0_attack_gen_result_zoclQEAAXZ_1400EECD0.cpp" />
    <ClCompile Include="Source\0_attack_keeper_inform_zoclQEAAXZ_140136C00.cpp" />
    <ClCompile Include="Source\0_attack_paramQEAAXZ_14008E4A0.cpp" />
    <ClCompile Include="Source\0_attack_selfdestruction_result_zoclQEAAXZ_1400EEF50.cpp" />
    <ClCompile Include="Source\0_attack_siege_result_zoclQEAAXZ_1400EEFD0.cpp" />
    <ClCompile Include="Source\0_attack_trap_inform_zoclQEAAXZ_140141400.cpp" />
    <ClCompile Include="Source\0_attack_unit_result_zoclQEAAXZ_1400EEE50.cpp" />
    <ClCompile Include="Source\0_be_damaged_charQEAAXZ_14013E400.cpp" />
    <ClCompile Include="Source\0_eff_list_ATTACK_DELAY_CHECKERQEAAXZ_140072E20.cpp" />
    <ClCompile Include="Source\0_guild_battle_suggest_matterQEAAXZ_14025CF40.cpp" />
    <ClCompile Include="Source\0_mas_list_ATTACK_DELAY_CHECKERQEAAXZ_140072E40.cpp" />
    <ClCompile Include="Source\0_param_cashitem_dblogQEAAKZ_140304CC0.cpp" />
    <ClCompile Include="Source\0_personal_automine_attacked_zoclQEAAXZ_1402DE2F0.cpp" />
    <ClCompile Include="Source\0_possible_battle_guild_list_result_zoclQEAAXZ_1403D07E0.cpp" />
    <ClCompile Include="Source\0_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV12_1403D1610.cpp" />
    <ClCompile Include="Source\0_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV12_1403D2430.cpp" />
    <ClCompile Include="Source\0_remain_num_of_goodCashItemRemoteStoreQEAAXZ_1403047C0.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D15A0.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D2360.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorVCGuildBattleRewardItemGUILD_BATT_1403D1540.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorVCGuildBattleRewardItemGUILD_BATT_1403D19C0.cpp" />
    <ClCompile Include="Source\0_Vector_valVCGuildBattleRewardItemGUILD_BATTLEVal_1403D14C0.cpp" />
    <ClCompile Include="Source\1CashItemRemoteStoreQEAAXZ_1402F3A90.cpp" />
    <ClCompile Include="Source\1CBattleTournamentInfoQEAAXZ_1403FEAB0.cpp" />
    <ClCompile Include="Source\1CCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAXZ_1403CDE80.cpp" />
    <ClCompile Include="Source\1CGuildBattleControllerIEAAXZ_1403D56A0.cpp" />
    <ClCompile Include="Source\1CGuildBattleGUILD_BATTLEQEAAXZ_1403EB040.cpp" />
    <ClCompile Include="Source\1CGuildBattleLoggerGUILD_BATTLEIEAAXZ_1403CE710.cpp" />
    <ClCompile Include="Source\1CGuildBattleRankManagerGUILD_BATTLEIEAAXZ_1403CA330.cpp" />
    <ClCompile Include="Source\1CGuildBattleReservedScheduleGUILD_BATTLEQEAAXZ_1403DABB0.cpp" />
    <ClCompile Include="Source\1CGuildBattleReservedScheduleListManagerGUILD_BATT_1403CD300.cpp" />
    <ClCompile Include="Source\1CGuildBattleReservedScheduleMapGroupGUILD_BATTLEQ_1403DBA90.cpp" />
    <ClCompile Include="Source\1CGuildBattleRewardItemManagerGUILD_BATTLEIEAAXZ_1403C93E0.cpp" />
    <ClCompile Include="Source\1CGuildBattleScheduleGUILD_BATTLEQEAAXZ_1403D9B80.cpp" />
    <ClCompile Include="Source\1CGuildBattleScheduleManagerGUILD_BATTLEIEAAXZ_1403DC8E0.cpp" />
    <ClCompile Include="Source\1CGuildBattleSchedulePoolGUILD_BATTLEIEAAXZ_1403DA470.cpp" />
    <ClCompile Include="Source\1CGuildBattleSchedulerGUILD_BATTLEIEAAXZ_1403DEE20.cpp" />
    <ClCompile Include="Source\1CGuildBattleStateGUILD_BATTLEQEAAXZ_14007F740.cpp" />
    <ClCompile Include="Source\1CGuildBattleStateListGUILD_BATTLEQEAAXZ_14007F810.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleFieldGUILD_BATTLEQEAAXZ_1403EB830.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleFieldListGUILD_BATTLEIEAAXZ_1403EE2C0.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleGuildGUILD_BATTLEQEAAXZ_1403E05A0.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleGuildMemberGUILD_BATTLEQEAAXZ_1403DF9A0.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleGUILD_BATTLEQEAAXZ_1403E2FB0.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleLoggerGUILD_BATTLEQEAAXZ_1403CEBE0.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleManagerGUILD_BATTLEIEAAXZ_1403D3430.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateCountDownGUILD_BATTLEQEAAX_14007FD60.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateDivideGUILD_BATTLEQEAAXZ_140080100.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateFinGUILD_BATTLEQEAAXZ_140080280.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateGUILD_BATTLEQEAAXZ_14007FB50.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateInBattleGUILD_BATTLEQEAAXZ_14007FE30.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateListGUILD_BATTLEQEAAXZ_14007F850.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateListPoolGUILD_BATTLEIEAAXZ_1403F22E0.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateNotifyGUILD_BATTLEQEAAXZ_14007FAF0.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateReadyGUILD_BATTLEQEAAXZ_14007FC90.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateReturnGUILD_BATTLEQEAAXZ_1400801C0.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateRoundGUILD_BATTLEQEAAXZ_1403F31E0.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateRoundListGUILD_BATTLEQEAAX_14007FEE0.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateRoundProcessGUILD_BATTLEQE_1403F1890.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateRoundReturnStartPosGUILD_B_1403F1C30.cpp" />
    <ClCompile Include="Source\1CNormalGuildBattleStateRoundStartGUILD_BATTLEQEAA_1403F1470.cpp" />
    <ClCompile Include="Source\1CPossibleBattleGuildListManagerGUILD_BATTLEIEAAXZ_1403C9580.cpp" />
    <ClCompile Include="Source\1CReservedGuildScheduleDayGroupGUILD_BATTLEQEAAXZ_1403CCBF0.cpp" />
    <ClCompile Include="Source\1CReservedGuildScheduleMapGroupGUILD_BATTLEQEAAXZ_1403CC3C0.cpp" />
    <ClCompile Include="Source\1CReservedGuildSchedulePageGUILD_BATTLEQEAAXZ_1403CBCE0.cpp" />
    <ClCompile Include="Source\1CRFCashItemDatabaseUEAAXZ_1402F2BB0.cpp" />
    <ClCompile Include="Source\1vectorVCGuildBattleRewardItemGUILD_BATTLEVallocat_1403D0CA0.cpp" />
    <ClCompile Include="Source\1_param_cashitem_dblogQEAAXZ_140304D50.cpp" />
    <ClCompile Include="Source\1_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV12_1403D0FF0.cpp" />
    <ClCompile Include="Source\1_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D0FB0.cpp" />
    <ClCompile Include="Source\1_Vector_iteratorVCGuildBattleRewardItemGUILD_BATT_1403D0F70.cpp" />
    <ClCompile Include="Source\4CGuildBattleReservedScheduleGUILD_BATTLEQEAAAEBV0_1403DB6E0.cpp" />
    <ClCompile Include="Source\4CGuildBattleReservedScheduleMapGroupGUILD_BATTLEQ_1403DC790.cpp" />
    <ClCompile Include="Source\4CGuildBattleScheduleGUILD_BATTLEQEAAAEBV01AEBV01Z_1403DA350.cpp" />
    <ClCompile Include="Source\8_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D23C0.cpp" />
    <ClCompile Include="Source\9_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D1A20.cpp" />
    <ClCompile Include="Source\accHitTestCFormViewUEAAJJJPEAUtagVARIANTZ_14002BB60.cpp" />
    <ClCompile Include="Source\accHitTestCWndUEAAJJJPEAUtagVARIANTZ_0_1404DBCBE.cpp" />
    <ClCompile Include="Source\Action_Attack_OnLoopDfAIMgrSAXPEAVUs_HFSMKPEAXZ_1401518E0.cpp" />
    <ClCompile Include="Source\AddCGuildBattleControllerQEAAEPEAVCGuild0KEKZ_14025D2A0.cpp" />
    <ClCompile Include="Source\AddCGuildBattleControllerQEAAEPEAVCGuild0KKEKZ_1403D5DB0.cpp" />
    <ClCompile Include="Source\AddCGuildBattleReservedScheduleGUILD_BATTLEQEAAEKK_1403DAC40.cpp" />
    <ClCompile Include="Source\AddCGuildBattleReservedScheduleMapGroupGUILD_BATTL_1403DC440.cpp" />
    <ClCompile Include="Source\AddCGuildBattleScheduleManagerGUILD_BATTLEQEAAEIKK_1403D9180.cpp" />
    <ClCompile Include="Source\AddCNormalGuildBattleManagerGUILD_BATTLEQEAAEPEAVC_1403D3DC0.cpp" />
    <ClCompile Include="Source\AddCompleteCGuildBattleControllerQEAAXEIKKZ_1403D6E20.cpp" />
    <ClCompile Include="Source\AddCompleteCNormalGuildBattleGUILD_BATTLEQEAAXEZ_1403E3910.cpp" />
    <ClCompile Include="Source\AddCompleteCNormalGuildBattleManagerGUILD_BATTLEQE_1403D4060.cpp" />
    <ClCompile Include="Source\AddDefaultDBRecordCNormalGuildBattleManagerGUILD_B_1403D4FD0.cpp" />
    <ClCompile Include="Source\AddDefaultDBTableCGuildBattleScheduleManagerGUILD__1403DD320.cpp" />
    <ClCompile Include="Source\AddGoldCntCNormalGuildBattleGuildMemberGUILD_BATTL_1403EAE50.cpp" />
    <ClCompile Include="Source\AddGuildBattleSchduleCMainThreadQEAAXPEAU_DB_QRY_S_1401F4170.cpp" />
    <ClCompile Include="Source\AddKillCntCNormalGuildBattleGuildMemberGUILD_BATTL_1403EADF0.cpp" />
    <ClCompile Include="Source\AddScheduleCGuildBattleControllerQEAAEPEADZ_1403D6AD0.cpp" />
    <ClCompile Include="Source\AdvanceCGuildBattleStateListGUILD_BATTLEIEAAXHZ_1403DF610.cpp" />
    <ClCompile Include="Source\AdvanceRegenStateCNormalGuildBattleStateInBattleGU_1403EB290.cpp" />
    <ClCompile Include="Source\AdvanceRegenStateCNormalGuildBattleStateListGUILD__1403EB220.cpp" />
    <ClCompile Include="Source\allocateallocatorVCGuildBattleRewardItemGUILD_BATT_1403D1970.cpp" />
    <ClCompile Include="Source\AreaDamageProcCAttackQEAAXHHPEAMH_NZ_14016C320.cpp" />
    <ClCompile Include="Source\AskJoinCNormalGuildBattleGuildGUILD_BATTLEIEAAXHPE_1403E2C80.cpp" />
    <ClCompile Include="Source\AskJoinCNormalGuildBattleGuildGUILD_BATTLEQEAAXHKE_1403E0D20.cpp" />
    <ClCompile Include="Source\AskJoinCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEA_1403E1ED0.cpp" />
    <ClCompile Include="Source\AskJoinCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_1403E3F40.cpp" />
    <ClCompile Include="Source\AskJoinCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E49E0.cpp" />
    <ClCompile Include="Source\assignvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1403D0D10.cpp" />
    <ClCompile Include="Source\AttackableHeightCAnimusUEAAHXZ_140129880.cpp" />
    <ClCompile Include="Source\AttackableHeightCGameObjectUEAAHXZ_14012E340.cpp" />
    <ClCompile Include="Source\AttackableHeightCGuardTowerUEAAHXZ_1401328E0.cpp" />
    <ClCompile Include="Source\AttackableHeightCMonsterUEAAHXZ_1401468D0.cpp" />
    <ClCompile Include="Source\AttackableHeightCTrapUEAAHXZ_1401412D0.cpp" />
    <ClCompile Include="Source\AttackCAnimusQEAA_NKZ_140126AC0.cpp" />
    <ClCompile Include="Source\AttackCNuclearBombQEAAXHHZ_14013C4F0.cpp" />
    <ClCompile Include="Source\AttackForceCAttackQEAAXPEAU_attack_param_NZ_14016A210.cpp" />
    <ClCompile Include="Source\AttackForceRequestCNetworkEXAEAA_NHPEADZ_1401C1A50.cpp" />
    <ClCompile Include="Source\AttackGenCAttackQEAAXPEAU_attack_param_N1Z_140169520.cpp" />
    <ClCompile Include="Source\AttackMonsterForceCMonsterAttackQEAAXPEAU_attack_p_14015BA60.cpp" />
    <ClCompile Include="Source\AttackMonsterGenCMonsterAttackQEAAXPEAU_attack_par_14015B300.cpp" />
    <ClCompile Include="Source\AttackObjectCMonsterQEAAHHPEAVCGameObjectZ_140142A60.cpp" />
    <ClCompile Include="Source\AttackPersonalRequestCNetworkEXAEAA_NHPEADZ_1401C1680.cpp" />
    <ClCompile Include="Source\AttackSiegeRequestCNetworkEXAEAA_NHPEADZ_1401C1F20.cpp" />
    <ClCompile Include="Source\AttackTestRequestCNetworkEXAEAA_NHPEADZ_1401C1D60.cpp" />
    <ClCompile Include="Source\AttackUnitRequestCNetworkEXAEAA_NHPEADZ_1401C1C00.cpp" />
    <ClCompile Include="Source\AvectorVCGuildBattleRewardItemGUILD_BATTLEVallocat_1403D0CE0.cpp" />
    <ClCompile Include="Source\beginvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D1030.cpp" />
    <ClCompile Include="Source\BuyByCashCashItemRemoteStoreAEAA_NGPEADZ_1402FE0D0.cpp" />
    <ClCompile Include="Source\BuyByGoldCashItemRemoteStoreAEAA_NGPEADZ_1402FECC0.cpp" />
    <ClCompile Include="Source\BuyCashItemRemoteStoreQEAA_NGPEADZ_1402FE050.cpp" />
    <ClCompile Include="Source\BuyLimSaleCashItemRemoteStoreQEAAGEKZ_1402FD950.cpp" />
    <ClCompile Include="Source\buy_to_inven_cashitemCMgrAvatorItemHistoryQEAAXEGH_14023DF10.cpp" />
    <ClCompile Include="Source\CalcAvgDamageCAttackQEAAXXZ_14016DBB0.cpp" />
    <ClCompile Include="Source\CallProc_InsertCashItemLogCRFCashItemDatabaseQEAA__140483530.cpp" />
    <ClCompile Include="Source\CallProc_RFOnlineAvg_EventCRFCashItemDatabaseQEAAH_1404832B0.cpp" />
    <ClCompile Include="Source\CallProc_RFOnlineUseCRFCashItemDatabaseQEAAHAEAU_p_140482840.cpp" />
    <ClCompile Include="Source\CallProc_RFOnlineUse_JapCRFCashItemDatabaseQEAAHAE_140483A70.cpp" />
    <ClCompile Include="Source\CallProc_RFONLINE_CancelCRFCashItemDatabaseQEAAHAE_140482D90.cpp" />
    <ClCompile Include="Source\CallProc_RFONLINE_Cancel_JapCRFCashItemDatabaseQEA_140484090.cpp" />
    <ClCompile Include="Source\CancelSuggestedMatter_guild_battle_suggest_matterQ_14025D350.cpp" />
    <ClCompile Include="Source\capacityvectorVCGuildBattleRewardItemGUILD_BATTLEV_1403D2480.cpp" />
    <ClCompile Include="Source\cashitem_del_from_invenCMgrAvatorItemHistoryQEAAXE_14023DD70.cpp" />
    <ClCompile Include="Source\ChangeDiscountEventTimeCashItemRemoteStoreQEAA_NXZ_1402F7B10.cpp" />
    <ClCompile Include="Source\ChangeEventTimeCashItemRemoteStoreQEAA_NEZ_1402FD050.cpp" />
    <ClCompile Include="Source\Change_Conditional_Event_StatusCashItemRemoteStore_1402FBF00.cpp" />
    <ClCompile Include="Source\CheatBuyCashItemRemoteStoreQEAA_NGPEBDHZ_1402F5B50.cpp" />
    <ClCompile Include="Source\CheatDestroyStoneCNormalGuildBattleFieldGUILD_BATT_1403ED6C0.cpp" />
    <ClCompile Include="Source\CheatLoadCashAmountCashItemRemoteStoreQEAA_NGHZ_1402F5990.cpp" />
    <ClCompile Include="Source\CheatRegenStoneCNormalGuildBattleFieldGUILD_BATTLE_1403ED490.cpp" />
    <ClCompile Include="Source\CheckAttackCHolyKeeperQEAA_NXZ_1401338D0.cpp" />
    <ClCompile Include="Source\CheckBallTakeLimitTimeCNormalGuildBattleFieldGUILD_1403ED0F0.cpp" />
    <ClCompile Include="Source\CheckCGuildBattleScheduleGUILD_BATTLEQEAAHXZ_1403D9DE0.cpp" />
    <ClCompile Include="Source\CheckGetGravityStoneCNormalGuildBattleManagerGUILD_1403D4800.cpp" />
    <ClCompile Include="Source\CheckGoalCNormalGuildBattleManagerGUILD_BATTLEQEAA_1403D4910.cpp" />
    <ClCompile Include="Source\CheckGuildBattleLimitCAttackIEAA_NPEAVCGameObjectP_14016B570.cpp" />
    <ClCompile Include="Source\CheckGuildBattleSuggestRequestToDestGuildCGuildQEA_1402579F0.cpp" />
    <ClCompile Include="Source\CheckIsInTownCNormalGuildBattleFieldGUILD_BATTLEQE_1403ED070.cpp" />
    <ClCompile Include="Source\CheckLoopCGuildBattleStateListGUILD_BATTLEIEAAHXZ_1403DF4D0.cpp" />
    <ClCompile Include="Source\CheckNextEventCGuildBattleReservedScheduleGUILD_BA_1403DB750.cpp" />
    <ClCompile Include="Source\CheckRecordCGuildBattleRankManagerGUILD_BATTLEIEAA_1403CB7C0.cpp" />
    <ClCompile Include="Source\CheckTakeGravityStoneCNormalGuildBattleManagerGUIL_1403D4700.cpp" />
    <ClCompile Include="Source\Check_CashEvent_INICashItemRemoteStoreQEAA_NEZ_1402F8770.cpp" />
    <ClCompile Include="Source\Check_CashEvent_StatusCashItemRemoteStoreQEAAXEZ_1402F93C0.cpp" />
    <ClCompile Include="Source\check_cash_discount_iniCashItemRemoteStoreQEAAXXZ_1402F6970.cpp" />
    <ClCompile Include="Source\check_cash_discount_statusCashItemRemoteStoreQEAAX_1402F6C30.cpp" />
    <ClCompile Include="Source\Check_Conditional_Event_INICashItemRemoteStoreQEAA_1402FC480.cpp" />
    <ClCompile Include="Source\Check_Conditional_Event_StatusCashItemRemoteStoreQ_1402FC060.cpp" />
    <ClCompile Include="Source\Check_GrosssalesCashItemRemoteStoreQEAAXKZ_1402FB6D0.cpp" />
    <ClCompile Include="Source\check_loaded_cde_statusCashItemRemoteStoreQEAAXXZ_1402F5F30.cpp" />
    <ClCompile Include="Source\Check_Loaded_Event_StatusCashItemRemoteStoreQEAAXE_1402F8140.cpp" />
    <ClCompile Include="Source\Check_Total_SellingCashItemRemoteStoreQEAAXXZ_1402FB640.cpp" />
    <ClCompile Include="Source\CleanUpBattleCNormalGuildBattleGuildGUILD_BATTLEQE_1403E1E50.cpp" />
    <ClCompile Include="Source\CleanUpBattleCNormalGuildBattleGuildMemberGUILD_BA_1403E00D0.cpp" />
    <ClCompile Include="Source\CleanUpBattleCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E6FE0.cpp" />
    <ClCompile Include="Source\CleanUpCCurrentGuildBattleInfoManagerGUILD_BATTLEI_1403CE510.cpp" />
    <ClCompile Include="Source\CleanUpCGuildBattleControllerIEAAXXZ_1403D7900.cpp" />
    <ClCompile Include="Source\CleanUpCGuildBattleRankManagerGUILD_BATTLEIEAAXXZ_1403CB3F0.cpp" />
    <ClCompile Include="Source\CleanUpDanglingReservedScheduleCGuildBattleReserve_1403DB570.cpp" />
    <ClCompile Include="Source\CleanUpDanglingReservedScheduleCGuildBattleReserve_1403DC5F0.cpp" />
    <ClCompile Include="Source\CleanUpDanglingReservedScheduleCGuildBattleSchedul_1403DD1C0.cpp" />
    <ClCompile Include="Source\ClearAllCGuildBattleSchedulePoolGUILD_BATTLEQEAAXX_1403DA890.cpp" />
    <ClCompile Include="Source\ClearBallCNormalGuildBattleFieldGUILD_BATTLEQEAA_N_1403ED140.cpp" />
    <ClCompile Include="Source\ClearByDayIDCGuildBattleSchedulePoolGUILD_BATTLEQE_1403DA930.cpp" />
    <ClCompile Include="Source\ClearCCurrentGuildBattleInfoManagerGUILD_BATTLEQEA_1403CE220.cpp" />
    <ClCompile Include="Source\ClearCGuildBattleControllerQEAAXXZ_1403D62F0.cpp" />
    <ClCompile Include="Source\ClearCGuildBattleRankManagerGUILD_BATTLEIEAAXEZ_1403CB530.cpp" />
    <ClCompile Include="Source\ClearCGuildBattleRankManagerGUILD_BATTLEQEAAXXZ_1403CB4B0.cpp" />
    <ClCompile Include="Source\ClearCGuildBattleReservedScheduleGUILD_BATTLEQEAAX_1403DAE20.cpp" />
    <ClCompile Include="Source\ClearCGuildBattleReservedScheduleGUILD_BATTLEQEAA__1403DB420.cpp" />
    <ClCompile Include="Source\ClearCGuildBattleReservedScheduleListManagerGUILD__1403CD970.cpp" />
    <ClCompile Include="Source\ClearCGuildBattleReservedScheduleMapGroupGUILD_BAT_1403DC020.cpp" />
    <ClCompile Include="Source\ClearCGuildBattleReservedScheduleMapGroupGUILD_BAT_1403DC230.cpp" />
    <ClCompile Include="Source\ClearCGuildBattleScheduleGUILD_BATTLEQEAAXXZ_1403D9E90.cpp" />
    <ClCompile Include="Source\ClearCGuildBattleScheduleManagerGUILD_BATTLEAEAAXX_1403DD480.cpp" />
    <ClCompile Include="Source\ClearCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1403DF030.cpp" />
    <ClCompile Include="Source\ClearCNormalGuildBattleGuildGUILD_BATTLEQEAAXXZ_1403E0600.cpp" />
    <ClCompile Include="Source\ClearCNormalGuildBattleGuildMemberGUILD_BATTLEQEAA_1403DF9B0.cpp" />
    <ClCompile Include="Source\ClearCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E3660.cpp" />
    <ClCompile Include="Source\ClearCNormalGuildBattleManagerGUILD_BATTLEIEAAXPEA_1403D5400.cpp" />
    <ClCompile Include="Source\ClearCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_1403D4280.cpp" />
    <ClCompile Include="Source\ClearCNormalGuildBattleStateListPoolGUILD_BATTLEQE_1403F2640.cpp" />
    <ClCompile Include="Source\ClearCPossibleBattleGuildListManagerGUILD_BATTLEQE_1403D98E0.cpp" />
    <ClCompile Include="Source\ClearCReservedGuildScheduleDayGroupGUILD_BATTLEQEA_1403CCE80.cpp" />
    <ClCompile Include="Source\ClearCReservedGuildScheduleMapGroupGUILD_BATTLEQEA_1403CC4C0.cpp" />
    <ClCompile Include="Source\ClearCReservedGuildSchedulePageGUILD_BATTLEQEAA_NX_1403CC220.cpp" />
    <ClCompile Include="Source\ClearDBCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403D9F10.cpp" />
    <ClCompile Include="Source\ClearDBRecordCNormalGuildBattleGUILD_BATTLEQEAA_NX_1403E3740.cpp" />
    <ClCompile Include="Source\ClearElapsedScheduleCGuildBattleReservedScheduleGU_1403DB950.cpp" />
    <ClCompile Include="Source\ClearGuildBattleCGuildQEAAXXZ_140258290.cpp" />
    <ClCompile Include="Source\ClearInBattleStateCNormalGuildBattleGuildGUILD_BAT_1403E1680.cpp" />
    <ClCompile Include="Source\ClearRegenCNormalGuildBattleFieldGUILD_BATTLEQEAA__1403ED190.cpp" />
    <ClCompile Include="Source\ClearTommorowScheduleByIDCGuildBattleScheduleManag_1403DD0D0.cpp" />
    <ClCompile Include="Source\Clear_guild_battle_suggest_matterQEAAXXZ_1402075A0.cpp" />
    <ClCompile Include="Source\CompleteClearGuildBattleRankCGuildBattleController_1403D7030.cpp" />
    <ClCompile Include="Source\CompleteCreateGuildBattleRankTableCGuildBattleCont_1403D6FB0.cpp" />
    <ClCompile Include="Source\CompleteLoadGuildBattleTotalRecordCMainThreadAEAAX_1401EDBD0.cpp" />
    <ClCompile Include="Source\CompleteOutGuildbattleCostCGuildQEAAXKKKKZ_140257B00.cpp" />
    <ClCompile Include="Source\CompleteUpdateRankCGuildBattleControllerQEAAXEEPEA_1403D6EC0.cpp" />
    <ClCompile Include="Source\CompleteUpdateReservedScheduleCGuildBattleControll_1403D6F60.cpp" />
    <ClCompile Include="Source\constructallocatorVCGuildBattleRewardItemGUILD_BAT_1403D2F40.cpp" />
    <ClCompile Include="Source\CopyUseTimeFieldCGuildBattleReservedScheduleGUILD__1403DAD20.cpp" />
    <ClCompile Include="Source\CopyUseTimeFieldCGuildBattleReservedScheduleMapGro_1403DC4F0.cpp" />
    <ClCompile Include="Source\CreateFieldObjectCNormalGuildBattleFieldGUILD_BATT_1403EC630.cpp" />
    <ClCompile Include="Source\CreateGuildBattleRankTableCRFWorldDatabaseQEAA_NPE_1404A3810.cpp" />
    <ClCompile Include="Source\CreateLogFileCGuildBattleLoggerGUILD_BATTLEQEAAXPE_1403CE9E0.cpp" />
    <ClCompile Include="Source\CreateLogFileCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E7040.cpp" />
    <ClCompile Include="Source\CreateLogFileCNormalGuildBattleLoggerGUILD_BATTLEQ_1403CED50.cpp" />
    <ClCompile Include="Source\CreateLoggerCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_1403D9020.cpp" />
    <ClCompile Include="Source\ct_StopBattleCHolyStoneSystemQEAA_NXZ_1402815B0.cpp" />
    <ClCompile Include="Source\deallocateallocatorVCGuildBattleRewardItemGUILD_BA_1403D1920.cpp" />
    <ClCompile Include="Source\DecideColorInxCNormalGuildBattleGUILD_BATTLEQEAAXX_1403E3E20.cpp" />
    <ClCompile Include="Source\DecideWinCNormalGuildBattleGUILD_BATTLEIEAAEXZ_1403E76F0.cpp" />
    <ClCompile Include="Source\DecPvpPointCNormalGuildBattleGuildGUILD_BATTLEQEAA_1403E1860.cpp" />
    <ClCompile Include="Source\DecPvpPointCNormalGuildBattleGuildMemberGUILD_BATT_1403DFE10.cpp" />
    <ClCompile Include="Source\DeleteGuildBattleInfoCRFWorldDatabaseQEAA_NXZ_1404A2410.cpp" />
    <ClCompile Include="Source\DeleteGuildBattleScheduleInfoCRFWorldDatabaseQEAA__1404A0FD0.cpp" />
    <ClCompile Include="Source\DestGuildIsAvailableBattleRequestStateCGuildQEAAEX_140257960.cpp" />
    <ClCompile Include="Source\destroyallocatorVCGuildBattleRewardItemGUILD_BATTL_1403D2FA0.cpp" />
    <ClCompile Include="Source\DestroyCCurrentGuildBattleInfoManagerGUILD_BATTLES_1403CDF80.cpp" />
    <ClCompile Include="Source\DestroyCGuildBattleControllerSAXXZ_1403D57A0.cpp" />
    <ClCompile Include="Source\DestroyCGuildBattleLoggerGUILD_BATTLESAXXZ_1403CE850.cpp" />
    <ClCompile Include="Source\DestroyCGuildBattleRankManagerGUILD_BATTLESAXXZ_1403CA430.cpp" />
    <ClCompile Include="Source\DestroyCGuildBattleReservedScheduleListManagerGUIL_1403CD420.cpp" />
    <ClCompile Include="Source\DestroyCGuildBattleScheduleManagerGUILD_BATTLESAXX_1403DCAD0.cpp" />
    <ClCompile Include="Source\DestroyCGuildBattleSchedulePoolGUILD_BATTLESAXXZ_1403DA640.cpp" />
    <ClCompile Include="Source\DestroyCGuildBattleSchedulerGUILD_BATTLESAXXZ_1403DD7C0.cpp" />
    <ClCompile Include="Source\DestroyCNormalGuildBattleFieldGUILD_BATTLEAEAAXXZ_1403EDED0.cpp" />
    <ClCompile Include="Source\DestroyCNormalGuildBattleFieldListGUILD_BATTLESAXX_1403EE420.cpp" />
    <ClCompile Include="Source\DestroyCNormalGuildBattleManagerGUILD_BATTLESAXXZ_1403D35F0.cpp" />
    <ClCompile Include="Source\DestroyCNormalGuildBattleStateListPoolGUILD_BATTLE_1403F2430.cpp" />
    <ClCompile Include="Source\DestroyCPossibleBattleGuildListManagerGUILD_BATTLE_1403C9710.cpp" />
    <ClCompile Include="Source\DestroyFieldObjectCNormalGuildBattleFieldGUILD_BAT_1403EC810.cpp" />
    <ClCompile Include="Source\dhRExtractSubStringCRFCashItemDatabaseQEAAXPEAD0HZ_140483420.cpp" />
    <ClCompile Include="Source\DividePvpPointCNormalGuildBattleGUILD_BATTLEQEAAXX_1403E6490.cpp" />
    <ClCompile Include="Source\DoDayChangedWorkCNormalGuildBattleManagerGUILD_BAT_1403D4220.cpp" />
    <ClCompile Include="Source\DoDayChangedWorkCPossibleBattleGuildListManagerGUI_1403C9AE0.cpp" />
    <ClCompile Include="Source\DropGravityStoneCNormalGuildBattleGUILD_BATTLEQEAA_1403E5830.cpp" />
    <ClCompile Include="Source\DropGravityStoneCNormalGuildBattleManagerGUILD_BAT_1403D4A10.cpp" />
    <ClCompile Include="Source\dtor00OnToolHitTestCMFCToolBarMEBA_JVCPointPEAUtag_140646C00.cpp" />
    <ClCompile Include="Source\dtor10OnToolHitTestCMFCToolBarMEBA_JVCPointPEAUtag_140646C20.cpp" />
    <ClCompile Include="Source\dtor20OnToolHitTestCMFCToolBarMEBA_JVCPointPEAUtag_140646C40.cpp" />
    <ClCompile Include="Source\endvectorVCGuildBattleRewardItemGUILD_BATTLEValloc_1403D10A0.cpp" />
    <ClCompile Include="Source\EnterCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuildB_14007F760.cpp" />
    <ClCompile Include="Source\EnterCNormalGuildBattleStateCountDownGUILD_BATTLEM_1403F0870.cpp" />
    <ClCompile Include="Source\EnterCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAVC_14007FBC0.cpp" />
    <ClCompile Include="Source\EnterCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAVC_1403F0380.cpp" />
    <ClCompile Include="Source\EnterCNormalGuildBattleStateInBattleGUILD_BATTLEME_1403F0A00.cpp" />
    <ClCompile Include="Source\EnterCNormalGuildBattleStateNotifyGUILD_BATTLEMEAA_1403F0700.cpp" />
    <ClCompile Include="Source\EnterCNormalGuildBattleStateReadyGUILD_BATTLEMEAAH_1403F07C0.cpp" />
    <ClCompile Include="Source\EnterCNormalGuildBattleStateRoundGUILD_BATTLEMEAAH_1403F3180.cpp" />
    <ClCompile Include="Source\EnterCNormalGuildBattleStateRoundGUILD_BATTLEUEAAH_1403F1020.cpp" />
    <ClCompile Include="Source\EnterCNormalGuildBattleStateRoundProcessGUILD_BATT_1403F1970.cpp" />
    <ClCompile Include="Source\EnterCNormalGuildBattleStateRoundReturnStartPosGUI_1403F1D10.cpp" />
    <ClCompile Include="Source\EnterCNormalGuildBattleStateRoundStartGUILD_BATTLE_1403F1550.cpp" />
    <ClCompile Include="Source\erasevectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D1110.cpp" />
    <ClCompile Include="Source\fillPEAVCGuildBattleRewardItemGUILD_BATTLEV12stdYA_1403D27F0.cpp" />
    <ClCompile Include="Source\fill_eff_list_ATTACK_DELAY_CHECKERQEAA_NXZ_14008EB90.cpp" />
    <ClCompile Include="Source\fill_mas_list_ATTACK_DELAY_CHECKERQEAA_NXZ_14008EBC0.cpp" />
    <ClCompile Include="Source\FinCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuildBat_14007F7A0.cpp" />
    <ClCompile Include="Source\FinCNormalGuildBattleStateDivideGUILD_BATTLEMEAAHP_1403F0D70.cpp" />
    <ClCompile Include="Source\FinCNormalGuildBattleStateFinGUILD_BATTLEMEAAHPEAV_1403F0F70.cpp" />
    <ClCompile Include="Source\FinCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAVCNo_14007FC00.cpp" />
    <ClCompile Include="Source\FinCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAVCGu_1403F0460.cpp" />
    <ClCompile Include="Source\FinCNormalGuildBattleStateInBattleGUILD_BATTLEMEAA_1403F0B50.cpp" />
    <ClCompile Include="Source\FinCNormalGuildBattleStateReturnGUILD_BATTLEMEAAHP_1403F0E60.cpp" />
    <ClCompile Include="Source\FinCNormalGuildBattleStateRoundGUILD_BATTLEMEAAHPE_1403F31C0.cpp" />
    <ClCompile Include="Source\FinCNormalGuildBattleStateRoundGUILD_BATTLEUEAAHPE_1403F1100.cpp" />
    <ClCompile Include="Source\FinCNormalGuildBattleStateRoundStartGUILD_BATTLEME_1403F1660.cpp" />
    <ClCompile Include="Source\FindCashRecCashItemRemoteStoreSAPEBU_CashShop_fldH_1402F48F0.cpp" />
    <ClCompile Include="Source\FindCGuildBattleRankManagerGUILD_BATTLEIEAA_NEKAEA_1403CB6F0.cpp" />
    <ClCompile Include="Source\FindCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CCFA0.cpp" />
    <ClCompile Include="Source\FindCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CCA30.cpp" />
    <ClCompile Include="Source\FindCReservedGuildSchedulePageGUILD_BATTLEQEAA_NKZ_1403CBD80.cpp" />
    <ClCompile Include="Source\FlashDamageProcCAttackIEAAXHHHH_NZ_14016B6F0.cpp" />
    <ClCompile Include="Source\FlipCGuildBattleControllerQEAAXXZ_1403D6260.cpp" />
    <ClCompile Include="Source\FlipCGuildBattleReservedScheduleGUILD_BATTLEQEAAXX_1403DB4B0.cpp" />
    <ClCompile Include="Source\FlipCGuildBattleReservedScheduleListManagerGUILD_B_1403CD8F0.cpp" />
    <ClCompile Include="Source\FlipCGuildBattleReservedScheduleMapGroupGUILD_BATT_1403DC1A0.cpp" />
    <ClCompile Include="Source\FlipCGuildBattleScheduleManagerGUILD_BATTLEAEAAXXZ_1403DD3B0.cpp" />
    <ClCompile Include="Source\FlipCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_1403D41C0.cpp" />
    <ClCompile Include="Source\FlipCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CD1C0.cpp" />
    <ClCompile Include="Source\FlipCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CCB50.cpp" />
    <ClCompile Include="Source\FlipCReservedGuildSchedulePageGUILD_BATTLEQEAAXXZ_1403CC090.cpp" />
    <ClCompile Include="Source\ForceNextCGuildBattleStateListGUILD_BATTLEIEAAXXZ_1403DF6A0.cpp" />
    <ClCompile Include="Source\force_endup_cash_discount_eventCashItemRemoteStore_1402F7850.cpp" />
    <ClCompile Include="Source\Get1PCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNormal_1403D9360.cpp" />
    <ClCompile Include="Source\Get2PCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNormal_1403D9380.cpp" />
    <ClCompile Include="Source\GetAccessibilityHitTestCWndQEAAJJJPEAUtagVARIANTZ__1404DC1DA.cpp" />
    <ClCompile Include="Source\GetAmountCGuildBattleRewardItemGUILD_BATTLEQEBAEXZ_1403EAE80.cpp" />
    <ClCompile Include="Source\GetANSIGuildNameCNormalGuildBattleGuildGUILD_BATTL_1403E0770.cpp" />
    <ClCompile Include="Source\GetAttackDelay_WEAPON_PARAMQEAAKHHZ_14008E730.cpp" />
    <ClCompile Include="Source\GetAttackDPCAnimusUEAAHXZ_14012CDF0.cpp" />
    <ClCompile Include="Source\GetAttackDPCGameObjectUEAAHXZ_14012E360.cpp" />
    <ClCompile Include="Source\GetAttackDPCGuardTowerUEAAHXZ_1401328C0.cpp" />
    <ClCompile Include="Source\GetAttackDPCHolyKeeperUEAAHXZ_140136B30.cpp" />
    <ClCompile Include="Source\GetAttackDPCHolyStoneUEAAHXZ_140138EB0.cpp" />
    <ClCompile Include="Source\GetAttackDPCMonsterUEAAHXZ_14014BB10.cpp" />
    <ClCompile Include="Source\GetAttackDPCTrapUEAAHXZ_1401412B0.cpp" />
    <ClCompile Include="Source\GetAttackPartCAnimusQEAAHXZ_1401264E0.cpp" />
    <ClCompile Include="Source\GetAttackPartCMonsterQEAAHXZ_14014DDC0.cpp" />
    <ClCompile Include="Source\GetAttackPivotCHolyKeeperQEAAPEAMXZ_140133E40.cpp" />
    <ClCompile Include="Source\GetAttackRangeCAnimusUEAAMXZ_1401295C0.cpp" />
    <ClCompile Include="Source\GetAttackRangeCGameObjectUEAAMXZ_14012E300.cpp" />
    <ClCompile Include="Source\GetAttackRangeCGuardTowerUEAAMXZ_140132670.cpp" />
    <ClCompile Include="Source\GetAttackRangeCHolyKeeperUEAAMXZ_140136880.cpp" />
    <ClCompile Include="Source\GetAttackRangeCMonsterUEAAMXZ_140146660.cpp" />
    <ClCompile Include="Source\GetAttackRangeCTrapUEAAMXZ_140141070.cpp" />
    <ClCompile Include="Source\GetAttackToolType_WEAPON_PARAMQEAAHXZ_1400349B0.cpp" />
    <ClCompile Include="Source\GetBattleByGuildSerialCNormalGuildBattleManagerGUI_1403D5580.cpp" />
    <ClCompile Include="Source\GetBattleCNormalGuildBattleManagerGUILD_BATTLEIEAA_1403D5520.cpp" />
    <ClCompile Include="Source\GetBattleModeTimeCMonsterAIQEAAKXZ_140155890.cpp" />
    <ClCompile Include="Source\GetBattleTimeCGuildBattleScheduleGUILD_BATTLEQEAAA_1403D9120.cpp" />
    <ClCompile Include="Source\GetBattleTurmCGuildBattleScheduleGUILD_BATTLEQEAAJ_1403D9440.cpp" />
    <ClCompile Include="Source\GetBlueCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNorm_1403F30E0.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataBRUEAAHPEAU_Cash_14022F020.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataCNUEAAHPEAU_Cash_140230920.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataESUEAAHPEAU_Cash_1402318F0.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataGBUEAAHPEAU_Cash_14022C0D0.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataIDUEAAHPEAU_Cash_14022CA60.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataJPUEAAHPEAU_Cash_14022D3E0.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataKRUEAAHPEAU_Cash_14022B4A0.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataNULLUEAAHPEAU_Ca_1402130B0.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataPHUEAAHPEAU_Cash_14022DE90.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataRUUEAAHPEAU_Cash_14022E820.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataTHUEAAHPEAU_Cash_140232270.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataTWUEAAHPEAU_Cash_1402300F0.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataUEAAHPEAU_CashSh_140212910.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingDataUSUEAAHPEAU_Cash_1402313F0.cpp" />
    <ClCompile Include="Source\GetCashItemPriceCNationSettingManagerQEAAHPEAU_Cas_140304810.cpp" />
    <ClCompile Include="Source\GetCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAVCGu_1403DAA30.cpp" />
    <ClCompile Include="Source\GetCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAVCGu_1403DEA50.cpp" />
    <ClCompile Include="Source\GetCircleZoneCGuildBattleControllerQEAAPEAVCGameOb_1403D6860.cpp" />
    <ClCompile Include="Source\GetCircleZoneCNormalGuildBattleFieldGUILD_BATTLEQE_1403ECA10.cpp" />
    <ClCompile Include="Source\GetCircleZoneCNormalGuildBattleFieldListGUILD_BATT_1403EED90.cpp" />
    <ClCompile Include="Source\GetCNormalGuildBattleStateListPoolGUILD_BATTLEQEAA_1403F26D0.cpp" />
    <ClCompile Include="Source\GetColorInxCNormalGuildBattleGuildGUILD_BATTLEQEAA_1403EAFC0.cpp" />
    <ClCompile Include="Source\GetColorNameCNormalGuildBattleGuildGUILD_BATTLEQEA_1403EB320.cpp" />
    <ClCompile Include="Source\GetCombatStateCMonsterQEAAEXZ_140143870.cpp" />
    <ClCompile Include="Source\GetCurScheduleIDCGuildBattleReservedScheduleGUILD__1403DB640.cpp" />
    <ClCompile Include="Source\GetCurScheduleIDCGuildBattleReservedScheduleMapGro_1403DC710.cpp" />
    <ClCompile Include="Source\GetCurScheduleIDCGuildBattleScheduleManagerGUILD_B_1403DD2D0.cpp" />
    <ClCompile Include="Source\GetDamagedObjNumCNuclearBombQEAAHXZ_14013E3A0.cpp" />
    <ClCompile Include="Source\GetDayIDCGuildBattleReservedScheduleMapGroupGUILD__1403D9A90.cpp" />
    <ClCompile Include="Source\GetEmptyMemberCNormalGuildBattleGuildGUILD_BATTLEI_1403E29E0.cpp" />
    <ClCompile Include="Source\GetEvnetTimeCashItemRemoteStoreQEAAXPEAU_cash_even_1402FBA00.cpp" />
    <ClCompile Include="Source\GetFieldCNormalGuildBattleFieldListGUILD_BATTLEQEA_1403EE870.cpp" />
    <ClCompile Include="Source\GetFieldCNormalGuildBattleFieldListGUILD_BATTLEQEA_1403EE950.cpp" />
    <ClCompile Include="Source\GetFieldCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNor_1400A6A80.cpp" />
    <ClCompile Include="Source\GetFirstMapFieldByRaceCNormalGuildBattleFieldListG_1403EEB10.cpp" />
    <ClCompile Include="Source\GetFirstMapInxByRaceCNormalGuildBattleFieldListGUI_1403EEA80.cpp" />
    <ClCompile Include="Source\GetGoalCntCNormalGuildBattleGuildGUILD_BATTLEQEAAK_1403EB150.cpp" />
    <ClCompile Include="Source\GetGoalCountCNormalGuildBattleGuildMemberGUILD_BAT_1403EB380.cpp" />
    <ClCompile Include="Source\GetGravityStoneCNormalGuildBattleGUILD_BATTLEQEAAE_1403E4B80.cpp" />
    <ClCompile Include="Source\GetGuildBattleNumberCNormalGuildBattleGUILD_BATTLE_1403D93A0.cpp" />
    <ClCompile Include="Source\GetGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAPEA_1403EB130.cpp" />
    <ClCompile Include="Source\GetGuildCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNor_1403E3AB0.cpp" />
    <ClCompile Include="Source\GetGuildNameCNormalGuildBattleGuildGUILD_BATTLEQEA_1403E0710.cpp" />
    <ClCompile Include="Source\GetGuildRaceCNormalGuildBattleGuildGUILD_BATTLEQEA_1403E0830.cpp" />
    <ClCompile Include="Source\GetGuildSerialCNormalGuildBattleGuildGUILD_BATTLEQ_1403E07D0.cpp" />
    <ClCompile Include="Source\GetIDCGuildBattleReservedScheduleGUILD_BATTLEQEAAI_1403DEC40.cpp" />
    <ClCompile Include="Source\GetIDCNormalGuildBattleGUILD_BATTLEQEAAKXZ_1403D9340.cpp" />
    <ClCompile Include="Source\GetIndexCNormalGuildBattleGuildMemberGUILD_BATTLEQ_1403E0300.cpp" />
    <ClCompile Include="Source\GetInfoCNormalGuildBattleGUILD_BATTLEQEAA_NAEAU_gu_1403E39A0.cpp" />
    <ClCompile Include="Source\GetItemCodeCGuildBattleRewardItemGUILD_BATTLEQEBAP_1403C9270.cpp" />
    <ClCompile Include="Source\GetJoinMemberCntCNormalGuildBattleGuildGUILD_BATTL_1403E2B20.cpp" />
    <ClCompile Include="Source\GetKillCountCNormalGuildBattleGuildMemberGUILD_BAT_1403EB360.cpp" />
    <ClCompile Include="Source\GetKillCountSumCNormalGuildBattleGuildGUILD_BATTLE_1403EB3F0.cpp" />
    <ClCompile Include="Source\GetLeftTimeCCurrentGuildBattleInfoManagerGUILD_BAT_1403CE5C0.cpp" />
    <ClCompile Include="Source\GetLeftTimeCGuildBattleScheduleGUILD_BATTLEQEAA_NA_1403DA220.cpp" />
    <ClCompile Include="Source\GetLimDiscoutCashItemRemoteStoreQEAAEXZ_1402FD930.cpp" />
    <ClCompile Include="Source\GetLoggerCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNo_1403F3100.cpp" />
    <ClCompile Include="Source\GetMapCNormalGuildBattleFieldGUILD_BATTLEQEAAPEAVC_1400A6A60.cpp" />
    <ClCompile Include="Source\GetMapCntCNormalGuildBattleFieldListGUILD_BATTLEQE_1403D0B10.cpp" />
    <ClCompile Include="Source\GetMapCodeCNormalGuildBattleFieldGUILD_BATTLEQEAAH_1403EC950.cpp" />
    <ClCompile Include="Source\GetMapIDCNormalGuildBattleFieldGUILD_BATTLEQEAAKXZ_1403EB0F0.cpp" />
    <ClCompile Include="Source\GetMapInxCNormalGuildBattleFieldListGUILD_BATTLEQE_1403EE990.cpp" />
    <ClCompile Include="Source\GetMapInxListCNormalGuildBattleFieldListGUILD_BATT_1403EEB70.cpp" />
    <ClCompile Include="Source\GetMapStrCodeCNormalGuildBattleFieldGUILD_BATTLEQE_1403EC910.cpp" />
    <ClCompile Include="Source\GetMaxJoinMemberCountCNormalGuildBattleGuildGUILD__1403EB410.cpp" />
    <ClCompile Include="Source\GetMaxPageCReservedGuildScheduleMapGroupGUILD_BATT_1403D0A80.cpp" />
    <ClCompile Include="Source\GetMemberCNormalGuildBattleGuildGUILD_BATTLEIEAAHK_1403E2A60.cpp" />
    <ClCompile Include="Source\GetMemberPtrCNormalGuildBattleGuildGUILD_BATTLEQEA_1403E0A00.cpp" />
    <ClCompile Include="Source\GetObjTypeCGuildBattleGUILD_BATTLEUEAAHXZ_1403EB060.cpp" />
    <ClCompile Include="Source\GetObjTypeCNormalGuildBattleGUILD_BATTLEUEAAHXZ_1403EB090.cpp" />
    <ClCompile Include="Source\GetPortalIndexInfoCNormalGuildBattleFieldGUILD_BAT_1403ECAF0.cpp" />
    <ClCompile Include="Source\GetRealStartTimeCGuildBattleScheduleGUILD_BATTLEQE_1403D93F0.cpp" />
    <ClCompile Include="Source\GetRedCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNorma_1403F30C0.cpp" />
    <ClCompile Include="Source\GetRefCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAV_1403DAB00.cpp" />
    <ClCompile Include="Source\GetRegenerCGuildBattleControllerQEAAPEAVCGameObjec_1403D6810.cpp" />
    <ClCompile Include="Source\GetRegenerCNormalGuildBattleFieldGUILD_BATTLEQEAAP_1403EC980.cpp" />
    <ClCompile Include="Source\GetRegenerCNormalGuildBattleFieldListGUILD_BATTLEQ_1403EECF0.cpp" />
    <ClCompile Include="Source\GetRemainNumOfGoodCashItemRemoteStoreQEAAHGZ_1402F5CD0.cpp" />
    <ClCompile Include="Source\GetRemainNumOfGoodCashItemRemoteStoreQEAAHQEADZ_1402F5D60.cpp" />
    <ClCompile Include="Source\GetScoreCNormalGuildBattleGuildGUILD_BATTLEQEAAKXZ_1403EB170.cpp" />
    <ClCompile Include="Source\GetSerialCNormalGuildBattleGuildMemberGUILD_BATTLE_1403EAFA0.cpp" />
    <ClCompile Include="Source\GetSetDiscoutCashItemRemoteStoreQEAAEEZ_1402FB0A0.cpp" />
    <ClCompile Include="Source\GetSIDCGuildBattleScheduleGUILD_BATTLEQEAAKXZ_1403D9100.cpp" />
    <ClCompile Include="Source\GetSIDCGuildBattleSchedulePoolGUILD_BATTLEQEAAKIKZ_1403DEC80.cpp" />
    <ClCompile Include="Source\GetSLIDCGuildBattleReservedScheduleMapGroupGUILD_B_1403DC680.cpp" />
    <ClCompile Include="Source\GetStartBattleTickTimeCHolyStoneSystemQEAAKXZ_14027B5C0.cpp" />
    <ClCompile Include="Source\GetStateCGuildBattleScheduleGUILD_BATTLEQEAAHXZ_1403D93D0.cpp" />
    <ClCompile Include="Source\GetStoneCGuildBattleControllerQEAAPEAVCGameObjectH_1403D67C0.cpp" />
    <ClCompile Include="Source\GetStoneCNormalGuildBattleFieldGUILD_BATTLEQEAAPEA_1403F0360.cpp" />
    <ClCompile Include="Source\GetStoneCNormalGuildBattleFieldListGUILD_BATTLEQEA_1403EEC50.cpp" />
    <ClCompile Include="Source\GetTermCGuildBattleStateGUILD_BATTLEUEAAAVCTimeSpa_14007F7C0.cpp" />
    <ClCompile Include="Source\GetTermCGuildBattleStateListGUILD_BATTLEQEAAAVCTim_1403DF470.cpp" />
    <ClCompile Include="Source\GetTermCNormalGuildBattleStateCountDownGUILD_BATTL_14007FDC0.cpp" />
    <ClCompile Include="Source\GetTermCNormalGuildBattleStateDivideGUILD_BATTLEUE_140080160.cpp" />
    <ClCompile Include="Source\GetTermCNormalGuildBattleStateFinGUILD_BATTLEUEAAA_1400802E0.cpp" />
    <ClCompile Include="Source\GetTermCNormalGuildBattleStateInBattleGUILD_BATTLE_1400800D0.cpp" />
    <ClCompile Include="Source\GetTermCNormalGuildBattleStateNotifyGUILD_BATTLEUE_14007FC20.cpp" />
    <ClCompile Include="Source\GetTermCNormalGuildBattleStateReadyGUILD_BATTLEUEA_14007FCF0.cpp" />
    <ClCompile Include="Source\GetTermCNormalGuildBattleStateReturnGUILD_BATTLEUE_140080220.cpp" />
    <ClCompile Include="Source\GetTimeCGuildBattleScheduleGUILD_BATTLEQEAAAVCTime_1403DEAC0.cpp" />
    <ClCompile Include="Source\GetTodayDayIDCGuildBattleScheduleManagerGUILD_BATT_1403D9A40.cpp" />
    <ClCompile Include="Source\GetTodaySLIDByMapCGuildBattleScheduleManagerGUILD__1403DD230.cpp" />
    <ClCompile Include="Source\GetToleranceProbMonsterSFContDamageToleracneQEAAMX_14014CAF0.cpp" />
    <ClCompile Include="Source\GetTomorrowDayIDCGuildBattleScheduleManagerGUILD_B_1403D9AB0.cpp" />
    <ClCompile Include="Source\GetTomorrowSLIDByMapCGuildBattleScheduleManagerGUI_1403DD280.cpp" />
    <ClCompile Include="Source\GetTopGoalMemberCNormalGuildBattleGuildGUILD_BATTL_1403E0B00.cpp" />
    <ClCompile Include="Source\GetTopKillMemberCNormalGuildBattleGuildGUILD_BATTL_1403E0A70.cpp" />
    <ClCompile Include="Source\GetWinnerGradeCBattleTournamentInfoQEAAEKPEADZ_1403FEC30.cpp" />
    <ClCompile Include="Source\Get_CashEvent_StatusCashItemRemoteStoreQEAAEEZ_1402FAC70.cpp" />
    <ClCompile Include="Source\get_cde_statusCashItemRemoteStoreQEAAEXZ_1402F7380.cpp" />
    <ClCompile Include="Source\Get_Conditional_Event_NameCashItemRemoteStoreQEAAX_1402FC8A0.cpp" />
    <ClCompile Include="Source\Get_Conditional_Event_StatusCashItemRemoteStoreQEA_1402FC460.cpp" />
    <ClCompile Include="Source\GoalCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEAVCN_1403E1600.cpp" />
    <ClCompile Include="Source\GoalCNormalGuildBattleGUILD_BATTLEQEAAEKHZ_1403E4CA0.cpp" />
    <ClCompile Include="Source\GoalCNormalGuildBattleStateGUILD_BATTLEUEAAXXZ_14007FBB0.cpp" />
    <ClCompile Include="Source\GoodsListBuyByCashCashItemRemoteStoreAEAA_NGPEADZ_1403009C0.cpp" />
    <ClCompile Include="Source\GoodsListBuyByGoldCashItemRemoteStoreAEAA_NGPEADZ_140300C60.cpp" />
    <ClCompile Include="Source\GoodsListCashItemRemoteStoreQEAA_NGPEADZ_1402F5220.cpp" />
    <ClCompile Include="Source\GotoCGuildBattleStateListGUILD_BATTLEQEAAHXZ_1403DF2C0.cpp" />
    <ClCompile Include="Source\GotoStateCGuildBattleStateListGUILD_BATTLEQEAA_NHZ_1403F3370.cpp" />
    <ClCompile Include="Source\GuildBattleBlockReportCNetworkEXAEAA_NHPEADZ_1401D7F40.cpp" />
    <ClCompile Include="Source\GuildBattleCurrentBattleInfoRequestCNetworkEXAEAA__1401C8770.cpp" />
    <ClCompile Include="Source\GuildBattleGetGravityStoneRequestCNetworkEXAEAA_NH_1401C8940.cpp" />
    <ClCompile Include="Source\GuildBattleGoalRequestCNetworkEXAEAA_NHPEADZ_1401C89E0.cpp" />
    <ClCompile Include="Source\GuildBattleJoinGuildBattleRequestCNetworkEXAEAA_NH_1401C8820.cpp" />
    <ClCompile Include="Source\GuildBattlePossibleGuildBattleListCNetworkEXAEAA_N_1401C84A0.cpp" />
    <ClCompile Include="Source\GuildBattleRankListRequestCNetworkEXAEAA_NHPEADZ_1401C8560.cpp" />
    <ClCompile Include="Source\GuildBattleReservedScheduleRequestCNetworkEXAEAA_N_1401C8670.cpp" />
    <ClCompile Include="Source\GuildBattleResultLogCNormalGuildBattleGUILD_BATTLE_1403E6E10.cpp" />
    <ClCompile Include="Source\GuildBattleResultLogNotifyWebCNormalGuildBattleGUI_1403E83E0.cpp" />
    <ClCompile Include="Source\GuildBattleResultLogPushDBLogCNormalGuildBattleGUI_1403E7D30.cpp" />
    <ClCompile Include="Source\GuildBattleSuggestRequestToDestGuildCGuildQEAAEKKK_140257C30.cpp" />
    <ClCompile Include="Source\GuildBattleTakeGravityStoneRequestCNetworkEXAEAA_N_1401C88B0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E7FF0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E82F0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E83B0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8780.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8880.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8A50.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8B90.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8DE0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9040.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9240.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9300.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E93C0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9480.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9500.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E95C0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8070.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8370.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8430.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8800.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8900.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8AD0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8C10.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8E60.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E90C0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E92C0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E9380.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E9440.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E9580.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E9640.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8030.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8330.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E83F0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E87C0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E88C0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8A90.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8BD0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8E20.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9080.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9280.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9340.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9400.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E94C0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9540.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9600.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DA120.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DA8F0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DAA70.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DD460.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DDFB0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DF520.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DF7C0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E0540.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E1270.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E3F80.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E4140.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E42C0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E4440.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E4570.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E4770.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DA1D0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DA9A0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DAB20.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DD510.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DE060.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DF5D0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DF870.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E05F0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E1320.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E4030.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E41F0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E4370.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E4620.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E4820.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DA170.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DA940.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DAAC0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DD4B0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DE000.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DF570.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DF810.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E0590.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E12C0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E3FD0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E4190.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E4310.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E4490.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E45C0.cpp" />
    <ClCompile Include="Source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E47C0.cpp" />
    <ClCompile Include="Source\IncPvpPointCNormalGuildBattleGuildGUILD_BATTLEQEAA_1403E1730.cpp" />
    <ClCompile Include="Source\IncPvpPointCNormalGuildBattleGuildMemberGUILD_BATT_1403DFCE0.cpp" />
    <ClCompile Include="Source\IncVerCReservedGuildSchedulePageGUILD_BATTLEQEAAXX_1403CBD40.cpp" />
    <ClCompile Include="Source\inform_cashdiscount_eventCashItemRemoteStoreQEAAXG_1402F6F60.cpp" />
    <ClCompile Include="Source\inform_cashdiscount_status_allCashItemRemoteStoreQ_1402F6FC0.cpp" />
    <ClCompile Include="Source\Inform_CashEventCashItemRemoteStoreQEAAXGZ_1402FAFC0.cpp" />
    <ClCompile Include="Source\Inform_CashEvent_Status_AllCashItemRemoteStoreQEAA_1402FADC0.cpp" />
    <ClCompile Include="Source\Inform_ConditionalEventCashItemRemoteStoreQEAAXGZ_1402FC970.cpp" />
    <ClCompile Include="Source\Inform_ConditionalEvent_Status_AllCashItemRemoteSt_1402FC640.cpp" />
    <ClCompile Include="Source\InGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY_SYN_D_1401F46D0.cpp" />
    <ClCompile Include="Source\InGuildbattleRewardMoneyCMainThreadQEAAXPEAU_DB_QR_1401F4AD0.cpp" />
    <ClCompile Include="Source\InitCBattleTournamentInfoQEAAXXZ_1403FEAC0.cpp" />
    <ClCompile Include="Source\InitCCurrentGuildBattleInfoManagerGUILD_BATTLEQEAA_1403CE000.cpp" />
    <ClCompile Include="Source\InitCGuildBattleControllerQEAA_NXZ_1403D5820.cpp" />
    <ClCompile Include="Source\InitCGuildBattleLoggerGUILD_BATTLEQEAA_NXZ_1403CE8D0.cpp" />
    <ClCompile Include="Source\InitCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_1403CA4B0.cpp" />
    <ClCompile Include="Source\InitCGuildBattleReservedScheduleListManagerGUILD_B_1403CD4A0.cpp" />
    <ClCompile Include="Source\InitCGuildBattleReservedScheduleMapGroupGUILD_BATT_1403DBB90.cpp" />
    <ClCompile Include="Source\InitCGuildBattleRewardItemGUILD_BATTLEQEAA_NGZ_1403C8F30.cpp" />
    <ClCompile Include="Source\InitCGuildBattleRewardItemManagerGUILD_BATTLEQEAA__1403C9420.cpp" />
    <ClCompile Include="Source\InitCGuildBattleScheduleManagerGUILD_BATTLEQEAA_NI_1403DCB50.cpp" />
    <ClCompile Include="Source\InitCGuildBattleSchedulePoolGUILD_BATTLEQEAA_NIZ_1403DA6C0.cpp" />
    <ClCompile Include="Source\InitCGuildBattleSchedulerGUILD_BATTLEQEAA_NXZ_1403DD840.cpp" />
    <ClCompile Include="Source\InitCNormalGuildBattleFieldGUILD_BATTLEQEAA_NIZ_1403EB870.cpp" />
    <ClCompile Include="Source\InitCNormalGuildBattleFieldListGUILD_BATTLEQEAA_NX_1403EE4A0.cpp" />
    <ClCompile Include="Source\InitCNormalGuildBattleGUILD_BATTLEQEAAXPEAVCGuild0_1403E30D0.cpp" />
    <ClCompile Include="Source\InitCNormalGuildBattleGUILD_BATTLEQEAA_N_NIKKKKEZ_1403E3180.cpp" />
    <ClCompile Include="Source\InitCNormalGuildBattleLoggerGUILD_BATTLEQEAA_NXZ_1403CEC70.cpp" />
    <ClCompile Include="Source\InitCNormalGuildBattleManagerGUILD_BATTLEQEAA_NXZ_1403D3670.cpp" />
    <ClCompile Include="Source\InitCNormalGuildBattleStateListPoolGUILD_BATTLEQEA_1403F24B0.cpp" />
    <ClCompile Include="Source\InitCPossibleBattleGuildListManagerGUILD_BATTLEQEA_1403C9790.cpp" />
    <ClCompile Include="Source\InitCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CCC90.cpp" />
    <ClCompile Include="Source\InitCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CC420.cpp" />
    <ClCompile Include="Source\InitCReservedGuildSchedulePageGUILD_BATTLEQEAA_NEZ_1403CC170.cpp" />
    <ClCompile Include="Source\InitializeCashItemRemoteStoreQEAA_NXZ_1402F4EF0.cpp" />
    <ClCompile Include="Source\InitMonsterSFContDamageToleracneQEAAXMZ_140157EF0.cpp" />
    <ClCompile Include="Source\InitUseFieldCNormalGuildBattleFieldListGUILD_BATTL_1403EEE30.cpp" />
    <ClCompile Include="Source\Init_ATTACK_DELAY_CHECKERQEAAXXZ_140072E60.cpp" />
    <ClCompile Include="Source\init_eff_list_ATTACK_DELAY_CHECKERQEAAXXZ_140072F70.cpp" />
    <ClCompile Include="Source\init_mas_list_ATTACK_DELAY_CHECKERQEAAXXZ_140072F90.cpp" />
    <ClCompile Include="Source\InsertGuildBattleDefaultRecordCRFWorldDatabaseQEAA_1404A24B0.cpp" />
    <ClCompile Include="Source\InsertGuildBattleRankRecordCRFWorldDatabaseQEAA_NK_1404A3760.cpp" />
    <ClCompile Include="Source\InsertGuildBattleScheduleDefaultRecordCRFWorldData_1404A1070.cpp" />
    <ClCompile Include="Source\insertvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1403D16C0.cpp" />
    <ClCompile Include="Source\Insert_PatrirchItemChargeRefundCRFWorldDatabaseQEA_1404BE8B0.cpp" />
    <ClCompile Include="Source\Insert_PatrirchItemChargeRefundPatriarchElectProce_1402BC040.cpp" />
    <ClCompile Include="Source\Insert_RaceBattleLogCRFWorldDatabaseQEAA_NPEAU_rac_1404C1670.cpp" />
    <ClCompile Include="Source\InstanceCashItemRemoteStoreSAPEAV1XZ_140079810.cpp" />
    <ClCompile Include="Source\InstanceCCurrentGuildBattleInfoManagerGUILD_BATTLE_1403CDEC0.cpp" />
    <ClCompile Include="Source\InstanceCGuildBattleControllerSAPEAV1XZ_1403D56E0.cpp" />
    <ClCompile Include="Source\InstanceCGuildBattleLoggerGUILD_BATTLESAPEAV12XZ_1403CE790.cpp" />
    <ClCompile Include="Source\InstanceCGuildBattleRankManagerGUILD_BATTLESAPEAV1_1403CA370.cpp" />
    <ClCompile Include="Source\InstanceCGuildBattleReservedScheduleListManagerGUI_1403CD360.cpp" />
    <ClCompile Include="Source\InstanceCGuildBattleRewardItemManagerGUILD_BATTLES_1403D9790.cpp" />
    <ClCompile Include="Source\InstanceCGuildBattleScheduleManagerGUILD_BATTLESAP_1403DCA10.cpp" />
    <ClCompile Include="Source\InstanceCGuildBattleSchedulePoolGUILD_BATTLESAPEAV_1403DA580.cpp" />
    <ClCompile Include="Source\InstanceCGuildBattleSchedulerGUILD_BATTLESAPEAV12X_1403DD700.cpp" />
    <ClCompile Include="Source\InstanceCNormalGuildBattleFieldListGUILD_BATTLESAP_1403EE360.cpp" />
    <ClCompile Include="Source\InstanceCNormalGuildBattleManagerGUILD_BATTLESAPEA_1403D3530.cpp" />
    <ClCompile Include="Source\InstanceCNormalGuildBattleStateListPoolGUILD_BATTL_1403F2370.cpp" />
    <ClCompile Include="Source\InstanceCPossibleBattleGuildListManagerGUILD_BATTL_1403C9650.cpp" />
    <ClCompile Include="Source\IsAttackableInTownCGameObjectUEAA_NXZ_140072B70.cpp" />
    <ClCompile Include="Source\IsAttackableInTownCMonsterUEAA_NXZ_14014BB30.cpp" />
    <ClCompile Include="Source\IsAvailableSuggestCGuildBattleControllerQEAAEPEAVC_1403D5BD0.cpp" />
    <ClCompile Include="Source\IsBattleModeUseCRecallRequestQEAA_NXZ_14024FD00.cpp" />
    <ClCompile Include="Source\IsBeAttackedAbleAutominePersonalUEAA_N_NZ_1402DDD00.cpp" />
    <ClCompile Include="Source\IsBeAttackedAbleCAnimusUEAA_N_NZ_14012CE10.cpp" />
    <ClCompile Include="Source\IsBeAttackedAbleCGameObjectUEAA_N_NZ_14012E390.cpp" />
    <ClCompile Include="Source\IsBeAttackedAbleCGuardTowerUEAA_N_NZ_1401328F0.cpp" />
    <ClCompile Include="Source\IsBeAttackedAbleCHolyKeeperUEAA_N_NZ_140136B00.cpp" />
    <ClCompile Include="Source\IsBeAttackedAbleCHolyStoneUEAA_N_NZ_140138ED0.cpp" />
    <ClCompile Include="Source\IsBeAttackedAbleCMonsterUEAA_N_NZ_1401468F0.cpp" />
    <ClCompile Include="Source\IsBeAttackedAbleCTrapUEAA_N_NZ_1401412E0.cpp" />
    <ClCompile Include="Source\IsBuyCashItemByGoldCashItemRemoteStoreQEAA_NXZ_1400F0860.cpp" />
    <ClCompile Include="Source\IsCashItemYAHEKZ_14003F600.cpp" />
    <ClCompile Include="Source\IsCharInSectorCAttackSAHQEAM00MMZ_14016D920.cpp" />
    <ClCompile Include="Source\IsCommitteeMemberCNormalGuildBattleGuildMemberGUIL_1403E0220.cpp" />
    <ClCompile Include="Source\IsCompleteBattle_guild_battle_suggest_matterQEAA_N_1403D0800.cpp" />
    <ClCompile Include="Source\isConEventTimeCashItemRemoteStoreQEAA_NXZ_1402FBB80.cpp" />
    <ClCompile Include="Source\IsDayChangedCGuildBattleScheduleManagerGUILD_BATTL_1403DD530.cpp" />
    <ClCompile Include="Source\IsDelay_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008EC60.cpp" />
    <ClCompile Include="Source\IsDoneCGuildBattleReservedScheduleGUILD_BATTLEQEAA_1403DEC60.cpp" />
    <ClCompile Include="Source\IsDoneCGuildBattleReservedScheduleMapGroupGUILD_BA_1403DED60.cpp" />
    <ClCompile Include="Source\IsDoneCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403DE9F0.cpp" />
    <ClCompile Include="Source\IsEmptyCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403DE990.cpp" />
    <ClCompile Include="Source\IsEmptyCGuildBattleStateListGUILD_BATTLEQEAA_NXZ_1403DF8E0.cpp" />
    <ClCompile Include="Source\IsEmptyCNormalGuildBattleGuildMemberGUILD_BATTLEQE_1403EAD50.cpp" />
    <ClCompile Include="Source\IsEmptyCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_1403D9630.cpp" />
    <ClCompile Include="Source\IsEmptyTimeCGuildBattleReservedScheduleGUILD_BATTL_1403DABC0.cpp" />
    <ClCompile Include="Source\IsEmptyTimeCGuildBattleReservedScheduleMapGroupGUI_1403DC3C0.cpp" />
    <ClCompile Include="Source\IsEmptyTimeCGuildBattleScheduleManagerGUILD_BATTLE_1403D9870.cpp" />
    <ClCompile Include="Source\IsEnableStartCNormalGuildBattleGuildMemberGUILD_BA_1403E01B0.cpp" />
    <ClCompile Include="Source\IsEventTimeCashItemRemoteStoreQEAA_NEZ_1402FACA0.cpp" />
    <ClCompile Include="Source\IsExistCNormalGuildBattleGuildMemberGUILD_BATTLEQE_1403E0130.cpp" />
    <ClCompile Include="Source\IsInBattleCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_14007C0A0.cpp" />
    <ClCompile Include="Source\IsInBattleCNormalGuildBattleStateListGUILD_BATTLEQ_14007C0F0.cpp" />
    <ClCompile Include="Source\IsInBattleRegenStateCNormalGuildBattleGUILD_BATTLE_1403D94B0.cpp" />
    <ClCompile Include="Source\IsInBattleRegenStateCNormalGuildBattleStateInBattl_1403D9570.cpp" />
    <ClCompile Include="Source\IsInBattleRegenStateCNormalGuildBattleStateListGUI_1403D9500.cpp" />
    <ClCompile Include="Source\IsInBattleRegenStateCNormalGuildBattleStateRoundLi_1403D95C0.cpp" />
    <ClCompile Include="Source\IsJoinMemberCNormalGuildBattleGuildGUILD_BATTLEIEA_1403E2BC0.cpp" />
    <ClCompile Include="Source\IsMemberCNormalGuildBattleGuildGUILD_BATTLEQEAA_NK_1403EB1B0.cpp" />
    <ClCompile Include="Source\IsMemberGuildCNormalGuildBattleGUILD_BATTLEQEAA_NK_1403D9690.cpp" />
    <ClCompile Include="Source\IsNullCGuildBattleRewardItemGUILD_BATTLEQEBA_NXZ_1403EAEA0.cpp" />
    <ClCompile Include="Source\IsPreAttackAbleMonCMonsterQEAAHXZ_140155460.cpp" />
    <ClCompile Include="Source\IsProcCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403DEAF0.cpp" />
    <ClCompile Include="Source\IsProcCGuildBattleStateListGUILD_BATTLEQEAA_NXZ_1403D9250.cpp" />
    <ClCompile Include="Source\IsProcCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_1403D9200.cpp" />
    <ClCompile Include="Source\IsReadyOrCountStateCNormalGuildBattleGUILD_BATTLEQ_14007BFD0.cpp" />
    <ClCompile Include="Source\IsReadyOrCountStateCNormalGuildBattleStateListGUIL_14007C020.cpp" />
    <ClCompile Include="Source\IsRegistedMapInxCNormalGuildBattleFieldListGUILD_B_1403EF0C0.cpp" />
    <ClCompile Include="Source\IsReStartCNormalGuildBattleGuildGUILD_BATTLEQEAAHK_1403E0970.cpp" />
    <ClCompile Include="Source\IsReStartCNormalGuildBattleGuildMemberGUILD_BATTLE_1403EADD0.cpp" />
    <ClCompile Include="Source\IsReStartCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_1403E41A0.cpp" />
    <ClCompile Include="Source\IsSFContDamageMonsterSFContDamageToleracneQEAA_NXZ_140157F90.cpp" />
    <ClCompile Include="Source\IsStorageCodeWithItemKindYAHHHZ_14003BD10.cpp" />
    <ClCompile Include="Source\IsWaitCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403DEB50.cpp" />
    <ClCompile Include="Source\IsWhiteSpaceCondensedTiXmlBaseSA_NXZ_140530810.cpp" />
    <ClCompile Include="Source\IsWhiteSpaceTiXmlBaseKA_NDZ_140530820.cpp" />
    <ClCompile Include="Source\Is_Battle_ModeCGameObjectUEAA_NXZ_14012CBC0.cpp" />
    <ClCompile Include="Source\is_cde_timeCashItemRemoteStoreQEAA_NXZ_1402F7070.cpp" />
    <ClCompile Include="Source\JoinCNormalGuildBattleGuildGUILD_BATTLEQEAAEKEAEAH_1403E0B90.cpp" />
    <ClCompile Include="Source\JoinCNormalGuildBattleGuildMemberGUILD_BATTLEQEAAX_1403DFA30.cpp" />
    <ClCompile Include="Source\JoinCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_1403E3B90.cpp" />
    <ClCompile Include="Source\JoinCNormalGuildBattleManagerGUILD_BATTLEQEAAXHKKZ_1403D4420.cpp" />
    <ClCompile Include="Source\JoinGuildCGuildBattleControllerQEAAXHKKZ_1400AD450.cpp" />
    <ClCompile Include="Source\JoinGuildCNormalGuildBattleManagerGUILD_BATTLEQEAA_1403D4BB0.cpp" />
    <ClCompile Include="Source\JudgeBattleCNormalGuildBattleGUILD_BATTLEQEAAEXZ_1403E6400.cpp" />
    <ClCompile Include="Source\j_0allocatorVCGuildBattleRewardItemGUILD_BATTLEstd_1400029C8.cpp" />
    <ClCompile Include="Source\j_0allocatorVCGuildBattleRewardItemGUILD_BATTLEstd_14000CC5C.cpp" />
    <ClCompile Include="Source\j_0CashItemRemoteStoreQEAAXZ_140013930.cpp" />
    <ClCompile Include="Source\j_0CBattleTournamentInfoQEAAXZ_140008C01.cpp" />
    <ClCompile Include="Source\j_0CCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAX_1400017BC.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleControllerIEAAXZ_140002748.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleGUILD_BATTLEQEAAXZ_140011EE1.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleLoggerGUILD_BATTLEIEAAXZ_140001870.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleRankManagerGUILD_BATTLEIEAAXZ_1400064FB.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleReservedScheduleGUILD_BATTLEQEAAIZ_14000ED36.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleReservedScheduleListManagerGUILD_BA_14000559C.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleReservedScheduleMapGroupGUILD_BATTL_14000AD85.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleRewardItemGUILD_BATTLEQEAAXZ_14000764E.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleRewardItemManagerGUILD_BATTLEIEAAXZ_140010DCA.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleScheduleGUILD_BATTLEQEAAKZ_140013C32.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleScheduleManagerGUILD_BATTLEIEAAXZ_140004E4E.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleSchedulePoolGUILD_BATTLEIEAAXZ_14000697E.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleSchedulerGUILD_BATTLEIEAAXZ_140005D30.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleStateGUILD_BATTLEQEAAXZ_140011E50.cpp" />
    <ClCompile Include="Source\j_0CGuildBattleStateListGUILD_BATTLEQEAAHHIZ_140008C9C.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleFieldGUILD_BATTLEQEAAXZ_1400114D2.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleFieldListGUILD_BATTLEIEAAXZ_1400080D5.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleGuildGUILD_BATTLEQEAAEZ_14000B596.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleGuildMemberGUILD_BATTLEQEAAXZ_1400058E4.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleGUILD_BATTLEQEAAKZ_140011E4B.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleLoggerGUILD_BATTLEQEAAXZ_140011388.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleManagerGUILD_BATTLEIEAAXZ_140005673.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateCountDownGUILD_BATTLEQEA_14000BC80.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateDivideGUILD_BATTLEQEAAXZ_14000C1C6.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateFinGUILD_BATTLEQEAAXZ_140010FDC.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateGUILD_BATTLEQEAAXZ_14000CC11.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateInBattleGUILD_BATTLEQEAA_14000253B.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateListGUILD_BATTLEQEAAXZ_1400127B0.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateListPoolGUILD_BATTLEIEAA_14000323D.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateNotifyGUILD_BATTLEQEAAXZ_140005BB4.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateReadyGUILD_BATTLEQEAAXZ_14000A943.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateReturnGUILD_BATTLEQEAAXZ_14000FCB3.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateRoundGUILD_BATTLEQEAAXZ_1400020C7.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateRoundListGUILD_BATTLEQEA_140002F04.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateRoundProcessGUILD_BATTLE_140003ABC.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateRoundReturnStartPosGUILD_1400118DD.cpp" />
    <ClCompile Include="Source\j_0CNormalGuildBattleStateRoundStartGUILD_BATTLEQE_1400085DF.cpp" />
    <ClCompile Include="Source\j_0CPossibleBattleGuildListManagerGUILD_BATTLEIEAA_14000CCF7.cpp" />
    <ClCompile Include="Source\j_0CReservedGuildScheduleDayGroupGUILD_BATTLEQEAAX_14000DBC5.cpp" />
    <ClCompile Include="Source\j_0CReservedGuildScheduleMapGroupGUILD_BATTLEQEAAX_140006799.cpp" />
    <ClCompile Include="Source\j_0CReservedGuildSchedulePageGUILD_BATTLEQEAAXZ_140012198.cpp" />
    <ClCompile Include="Source\j_0CRFCashItemDatabaseQEAAXZ_140013EA8.cpp" />
    <ClCompile Include="Source\j_0MonsterSFContDamageToleracneQEAAXZ_140011072.cpp" />
    <ClCompile Include="Source\j_0vectorVCGuildBattleRewardItemGUILD_BATTLEValloc_140011A18.cpp" />
    <ClCompile Include="Source\j_0_attack_count_result_zoclQEAAXZ_14000E692.cpp" />
    <ClCompile Include="Source\j_0_ATTACK_DELAY_CHECKERQEAAXZ_14000C0E0.cpp" />
    <ClCompile Include="Source\j_0_attack_force_result_zoclQEAAXZ_1400039FE.cpp" />
    <ClCompile Include="Source\j_0_attack_gen_result_zoclQEAAXZ_14000C086.cpp" />
    <ClCompile Include="Source\j_0_attack_keeper_inform_zoclQEAAXZ_1400013E8.cpp" />
    <ClCompile Include="Source\j_0_attack_paramQEAAXZ_14000AE4D.cpp" />
    <ClCompile Include="Source\j_0_attack_selfdestruction_result_zoclQEAAXZ_140012C56.cpp" />
    <ClCompile Include="Source\j_0_attack_siege_result_zoclQEAAXZ_140010F8C.cpp" />
    <ClCompile Include="Source\j_0_attack_trap_inform_zoclQEAAXZ_1400033F5.cpp" />
    <ClCompile Include="Source\j_0_attack_unit_result_zoclQEAAXZ_14000B6FE.cpp" />
    <ClCompile Include="Source\j_0_be_damaged_charQEAAXZ_14000A335.cpp" />
    <ClCompile Include="Source\j_0_eff_list_ATTACK_DELAY_CHECKERQEAAXZ_14000A560.cpp" />
    <ClCompile Include="Source\j_0_guild_battle_suggest_matterQEAAXZ_140004B56.cpp" />
    <ClCompile Include="Source\j_0_mas_list_ATTACK_DELAY_CHECKERQEAAXZ_14000A82B.cpp" />
    <ClCompile Include="Source\j_0_param_cashitem_dblogQEAAKZ_14000CE0F.cpp" />
    <ClCompile Include="Source\j_0_personal_automine_attacked_zoclQEAAXZ_14000471E.cpp" />
    <ClCompile Include="Source\j_0_possible_battle_guild_list_result_zoclQEAAXZ_14000DC92.cpp" />
    <ClCompile Include="Source\j_0_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV_140001DF2.cpp" />
    <ClCompile Include="Source\j_0_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV_1400085AD.cpp" />
    <ClCompile Include="Source\j_0_remain_num_of_goodCashItemRemoteStoreQEAAXZ_14000560F.cpp" />
    <ClCompile Include="Source\j_0_Vector_const_iteratorVCGuildBattleRewardItemGU_140001393.cpp" />
    <ClCompile Include="Source\j_0_Vector_const_iteratorVCGuildBattleRewardItemGU_140006127.cpp" />
    <ClCompile Include="Source\j_0_Vector_iteratorVCGuildBattleRewardItemGUILD_BA_140002E2D.cpp" />
    <ClCompile Include="Source\j_0_Vector_iteratorVCGuildBattleRewardItemGUILD_BA_14000F79F.cpp" />
    <ClCompile Include="Source\j_0_Vector_valVCGuildBattleRewardItemGUILD_BATTLEV_140007DC4.cpp" />
    <ClCompile Include="Source\j_1CashItemRemoteStoreQEAAXZ_140011815.cpp" />
    <ClCompile Include="Source\j_1CBattleTournamentInfoQEAAXZ_14000B604.cpp" />
    <ClCompile Include="Source\j_1CCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAX_14000AD17.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleControllerIEAAXZ_14000C4FA.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleGUILD_BATTLEQEAAXZ_14000B41A.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleLoggerGUILD_BATTLEIEAAXZ_14000FE0C.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleRankManagerGUILD_BATTLEIEAAXZ_140007243.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleReservedScheduleGUILD_BATTLEQEAAXZ_14000C540.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleReservedScheduleListManagerGUILD_BA_14000C4B4.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleReservedScheduleMapGroupGUILD_BATTL_140009075.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleRewardItemManagerGUILD_BATTLEIEAAXZ_140007DE2.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleScheduleGUILD_BATTLEQEAAXZ_1400037A1.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleScheduleManagerGUILD_BATTLEIEAAXZ_140013327.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleSchedulePoolGUILD_BATTLEIEAAXZ_1400074C3.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleSchedulerGUILD_BATTLEIEAAXZ_14000BF82.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleStateGUILD_BATTLEQEAAXZ_140010217.cpp" />
    <ClCompile Include="Source\j_1CGuildBattleStateListGUILD_BATTLEQEAAXZ_14000CEA5.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleFieldGUILD_BATTLEQEAAXZ_140010145.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleFieldListGUILD_BATTLEIEAAXZ_14000E2FF.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleGuildGUILD_BATTLEQEAAXZ_140013A1B.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleGuildMemberGUILD_BATTLEQEAAXZ_14001133D.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleGUILD_BATTLEQEAAXZ_14000FF38.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleLoggerGUILD_BATTLEQEAAXZ_14000A533.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleManagerGUILD_BATTLEIEAAXZ_1400066B3.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateCountDownGUILD_BATTLEQEA_140009CF0.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateDivideGUILD_BATTLEQEAAXZ_140002185.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateFinGUILD_BATTLEQEAAXZ_140005C1D.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateGUILD_BATTLEQEAAXZ_14000D648.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateInBattleGUILD_BATTLEQEAA_140009831.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateListGUILD_BATTLEQEAAXZ_140004D45.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateListPoolGUILD_BATTLEIEAA_14000D463.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateNotifyGUILD_BATTLEQEAAXZ_140007A90.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateReadyGUILD_BATTLEQEAAXZ_14000448F.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateReturnGUILD_BATTLEQEAAXZ_14000312F.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateRoundGUILD_BATTLEQEAAXZ_14000358F.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateRoundListGUILD_BATTLEQEA_14000696F.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateRoundProcessGUILD_BATTLE_14000EF7F.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateRoundReturnStartPosGUILD_14000A349.cpp" />
    <ClCompile Include="Source\j_1CNormalGuildBattleStateRoundStartGUILD_BATTLEQE_1400012BC.cpp" />
    <ClCompile Include="Source\j_1CPossibleBattleGuildListManagerGUILD_BATTLEIEAA_1400069E7.cpp" />
    <ClCompile Include="Source\j_1CReservedGuildScheduleDayGroupGUILD_BATTLEQEAAX_140006E6A.cpp" />
    <ClCompile Include="Source\j_1CReservedGuildScheduleMapGroupGUILD_BATTLEQEAAX_14000ABE1.cpp" />
    <ClCompile Include="Source\j_1CReservedGuildSchedulePageGUILD_BATTLEQEAAXZ_14000DD32.cpp" />
    <ClCompile Include="Source\j_1CRFCashItemDatabaseUEAAXZ_14000F1AF.cpp" />
    <ClCompile Include="Source\j_1vectorVCGuildBattleRewardItemGUILD_BATTLEValloc_14000CA36.cpp" />
    <ClCompile Include="Source\j_1_param_cashitem_dblogQEAAXZ_14000ACE0.cpp" />
    <ClCompile Include="Source\j_1_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV_140013AE3.cpp" />
    <ClCompile Include="Source\j_1_Vector_const_iteratorVCGuildBattleRewardItemGU_1400069C9.cpp" />
    <ClCompile Include="Source\j_1_Vector_iteratorVCGuildBattleRewardItemGUILD_BA_14000C54A.cpp" />
    <ClCompile Include="Source\j_4CGuildBattleReservedScheduleGUILD_BATTLEQEAAAEB_14000ECE6.cpp" />
    <ClCompile Include="Source\j_4CGuildBattleReservedScheduleMapGroupGUILD_BATTL_14000983B.cpp" />
    <ClCompile Include="Source\j_4CGuildBattleScheduleGUILD_BATTLEQEAAAEBV01AEBV0_14000E138.cpp" />
    <ClCompile Include="Source\j_8_Vector_const_iteratorVCGuildBattleRewardItemGU_1400104F1.cpp" />
    <ClCompile Include="Source\j_9_Vector_const_iteratorVCGuildBattleRewardItemGU_14000C7DE.cpp" />
    <ClCompile Include="Source\j_accHitTestCFormViewUEAAJJJPEAUtagVARIANTZ_140006DC5.cpp" />
    <ClCompile Include="Source\j_Action_Attack_OnLoopDfAIMgrSAXPEAVUs_HFSMKPEAXZ_140008D23.cpp" />
    <ClCompile Include="Source\j_AddCGuildBattleControllerQEAAEPEAVCGuild0KEKZ_14000ECFF.cpp" />
    <ClCompile Include="Source\j_AddCGuildBattleControllerQEAAEPEAVCGuild0KKEKZ_1400092CD.cpp" />
    <ClCompile Include="Source\j_AddCGuildBattleReservedScheduleGUILD_BATTLEQEAAE_140012021.cpp" />
    <ClCompile Include="Source\j_AddCGuildBattleReservedScheduleMapGroupGUILD_BAT_14000BC5D.cpp" />
    <ClCompile Include="Source\j_AddCGuildBattleScheduleManagerGUILD_BATTLEQEAAEI_14000774D.cpp" />
    <ClCompile Include="Source\j_AddCNormalGuildBattleManagerGUILD_BATTLEQEAAEPEA_1400042EB.cpp" />
    <ClCompile Include="Source\j_AddCompleteCGuildBattleControllerQEAAXEIKKZ_14000FB91.cpp" />
    <ClCompile Include="Source\j_AddCompleteCNormalGuildBattleGUILD_BATTLEQEAAXEZ_14001316F.cpp" />
    <ClCompile Include="Source\j_AddCompleteCNormalGuildBattleManagerGUILD_BATTLE_140003477.cpp" />
    <ClCompile Include="Source\j_AddDefaultDBRecordCNormalGuildBattleManagerGUILD_140013B5B.cpp" />
    <ClCompile Include="Source\j_AddDefaultDBTableCGuildBattleScheduleManagerGUIL_1400086F2.cpp" />
    <ClCompile Include="Source\j_AddGoldCntCNormalGuildBattleGuildMemberGUILD_BAT_140001898.cpp" />
    <ClCompile Include="Source\j_AddGuildBattleSchduleCMainThreadQEAAXPEAU_DB_QRY_140009BF6.cpp" />
    <ClCompile Include="Source\j_AddKillCntCNormalGuildBattleGuildMemberGUILD_BAT_14000B249.cpp" />
    <ClCompile Include="Source\j_AddScheduleCGuildBattleControllerQEAAEPEADZ_140007D47.cpp" />
    <ClCompile Include="Source\j_AdvanceCGuildBattleStateListGUILD_BATTLEIEAAXHZ_140009665.cpp" />
    <ClCompile Include="Source\j_AdvanceRegenStateCNormalGuildBattleStateInBattle_1400026E4.cpp" />
    <ClCompile Include="Source\j_AdvanceRegenStateCNormalGuildBattleStateListGUIL_14000F628.cpp" />
    <ClCompile Include="Source\j_allocateallocatorVCGuildBattleRewardItemGUILD_BA_140009ECB.cpp" />
    <ClCompile Include="Source\j_AreaDamageProcCAttackQEAAXHHPEAMH_NZ_140006B09.cpp" />
    <ClCompile Include="Source\j_AskJoinCNormalGuildBattleGuildGUILD_BATTLEIEAAXH_14000ED54.cpp" />
    <ClCompile Include="Source\j_AskJoinCNormalGuildBattleGuildGUILD_BATTLEQEAAXH_14001347B.cpp" />
    <ClCompile Include="Source\j_AskJoinCNormalGuildBattleGuildGUILD_BATTLEQEAAXP_1400119C3.cpp" />
    <ClCompile Include="Source\j_AskJoinCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_14000EA02.cpp" />
    <ClCompile Include="Source\j_AskJoinCNormalGuildBattleGUILD_BATTLEQEAAXXZ_14000FB6E.cpp" />
    <ClCompile Include="Source\j_assignvectorVCGuildBattleRewardItemGUILD_BATTLEV_14000E3DB.cpp" />
    <ClCompile Include="Source\j_AttackableHeightCAnimusUEAAHXZ_140010000.cpp" />
    <ClCompile Include="Source\j_AttackableHeightCGameObjectUEAAHXZ_14000B3E3.cpp" />
    <ClCompile Include="Source\j_AttackableHeightCGuardTowerUEAAHXZ_14000AF4C.cpp" />
    <ClCompile Include="Source\j_AttackableHeightCMonsterUEAAHXZ_1400048E0.cpp" />
    <ClCompile Include="Source\j_AttackableHeightCTrapUEAAHXZ_14000C5B3.cpp" />
    <ClCompile Include="Source\j_AttackCAnimusQEAA_NKZ_140001C94.cpp" />
    <ClCompile Include="Source\j_AttackCNuclearBombQEAAXHHZ_14000F88F.cpp" />
    <ClCompile Include="Source\j_AttackForceCAttackQEAAXPEAU_attack_param_NZ_14001098D.cpp" />
    <ClCompile Include="Source\j_AttackForceRequestCNetworkEXAEAA_NHPEADZ_1400067B7.cpp" />
    <ClCompile Include="Source\j_AttackGenCAttackQEAAXPEAU_attack_param_N1Z_140013714.cpp" />
    <ClCompile Include="Source\j_AttackMonsterForceCMonsterAttackQEAAXPEAU_attack_14000AF65.cpp" />
    <ClCompile Include="Source\j_AttackMonsterGenCMonsterAttackQEAAXPEAU_attack_p_14000999E.cpp" />
    <ClCompile Include="Source\j_AttackObjectCMonsterQEAAHHPEAVCGameObjectZ_140008F58.cpp" />
    <ClCompile Include="Source\j_AttackPersonalRequestCNetworkEXAEAA_NHPEADZ_14000EEA3.cpp" />
    <ClCompile Include="Source\j_AttackSiegeRequestCNetworkEXAEAA_NHPEADZ_14000510F.cpp" />
    <ClCompile Include="Source\j_AttackTestRequestCNetworkEXAEAA_NHPEADZ_1400111E4.cpp" />
    <ClCompile Include="Source\j_AttackUnitRequestCNetworkEXAEAA_NHPEADZ_14000B609.cpp" />
    <ClCompile Include="Source\j_AvectorVCGuildBattleRewardItemGUILD_BATTLEValloc_14000E36D.cpp" />
    <ClCompile Include="Source\j_beginvectorVCGuildBattleRewardItemGUILD_BATTLEVa_140013AC0.cpp" />
    <ClCompile Include="Source\j_BuyByCashCashItemRemoteStoreAEAA_NGPEADZ_140013D81.cpp" />
    <ClCompile Include="Source\j_BuyByGoldCashItemRemoteStoreAEAA_NGPEADZ_140001078.cpp" />
    <ClCompile Include="Source\j_BuyCashItemRemoteStoreQEAA_NGPEADZ_140006EE2.cpp" />
    <ClCompile Include="Source\j_BuyLimSaleCashItemRemoteStoreQEAAGEKZ_140013737.cpp" />
    <ClCompile Include="Source\j_buy_to_inven_cashitemCMgrAvatorItemHistoryQEAAXE_140011126.cpp" />
    <ClCompile Include="Source\j_CalcAvgDamageCAttackQEAAXXZ_1400123A0.cpp" />
    <ClCompile Include="Source\j_CallProc_InsertCashItemLogCRFCashItemDatabaseQEA_140007E0A.cpp" />
    <ClCompile Include="Source\j_CallProc_RFOnlineAvg_EventCRFCashItemDatabaseQEA_140013FD9.cpp" />
    <ClCompile Include="Source\j_CallProc_RFOnlineUseCRFCashItemDatabaseQEAAHAEAU_140008DAA.cpp" />
    <ClCompile Include="Source\j_CallProc_RFOnlineUse_JapCRFCashItemDatabaseQEAAH_140013FB1.cpp" />
    <ClCompile Include="Source\j_CallProc_RFONLINE_CancelCRFCashItemDatabaseQEAAH_140004A61.cpp" />
    <ClCompile Include="Source\j_CallProc_RFONLINE_Cancel_JapCRFCashItemDatabaseQ_140008067.cpp" />
    <ClCompile Include="Source\j_CancelSuggestedMatter_guild_battle_suggest_matte_14001192D.cpp" />
    <ClCompile Include="Source\j_capacityvectorVCGuildBattleRewardItemGUILD_BATTL_140010884.cpp" />
    <ClCompile Include="Source\j_cashitem_del_from_invenCMgrAvatorItemHistoryQEAA_14000DF35.cpp" />
    <ClCompile Include="Source\j_ChangeDiscountEventTimeCashItemRemoteStoreQEAA_N_1400104CE.cpp" />
    <ClCompile Include="Source\j_ChangeEventTimeCashItemRemoteStoreQEAA_NEZ_1400049DA.cpp" />
    <ClCompile Include="Source\j_Change_Conditional_Event_StatusCashItemRemoteSto_14000F8E9.cpp" />
    <ClCompile Include="Source\j_CheatBuyCashItemRemoteStoreQEAA_NGPEBDHZ_140008FD5.cpp" />
    <ClCompile Include="Source\j_CheatDestroyStoneCNormalGuildBattleFieldGUILD_BA_14000BFB4.cpp" />
    <ClCompile Include="Source\j_CheatLoadCashAmountCashItemRemoteStoreQEAA_NGHZ_1400047AA.cpp" />
    <ClCompile Include="Source\j_CheatRegenStoneCNormalGuildBattleFieldGUILD_BATT_140002FE0.cpp" />
    <ClCompile Include="Source\j_CheckAttackCHolyKeeperQEAA_NXZ_140004FAC.cpp" />
    <ClCompile Include="Source\j_CheckBallTakeLimitTimeCNormalGuildBattleFieldGUI_14000B672.cpp" />
    <ClCompile Include="Source\j_CheckCGuildBattleScheduleGUILD_BATTLEQEAAHXZ_14000A1E6.cpp" />
    <ClCompile Include="Source\j_CheckGetGravityStoneCNormalGuildBattleManagerGUI_14000BF50.cpp" />
    <ClCompile Include="Source\j_CheckGoalCNormalGuildBattleManagerGUILD_BATTLEQE_14000DBC0.cpp" />
    <ClCompile Include="Source\j_CheckGuildBattleLimitCAttackIEAA_NPEAVCGameObjec_140005E20.cpp" />
    <ClCompile Include="Source\j_CheckGuildBattleSuggestRequestToDestGuildCGuildQ_14000927D.cpp" />
    <ClCompile Include="Source\j_CheckIsInTownCNormalGuildBattleFieldGUILD_BATTLE_1400060A0.cpp" />
    <ClCompile Include="Source\j_CheckLoopCGuildBattleStateListGUILD_BATTLEIEAAHX_140008DB9.cpp" />
    <ClCompile Include="Source\j_CheckNextEventCGuildBattleReservedScheduleGUILD__14000AA0B.cpp" />
    <ClCompile Include="Source\j_CheckRecordCGuildBattleRankManagerGUILD_BATTLEIE_140001712.cpp" />
    <ClCompile Include="Source\j_CheckTakeGravityStoneCNormalGuildBattleManagerGU_14000B1A4.cpp" />
    <ClCompile Include="Source\j_Check_CashEvent_INICashItemRemoteStoreQEAA_NEZ_1400016FE.cpp" />
    <ClCompile Include="Source\j_Check_CashEvent_StatusCashItemRemoteStoreQEAAXEZ_140005F9C.cpp" />
    <ClCompile Include="Source\j_check_cash_discount_iniCashItemRemoteStoreQEAAXX_14000C234.cpp" />
    <ClCompile Include="Source\j_check_cash_discount_statusCashItemRemoteStoreQEA_1400110B3.cpp" />
    <ClCompile Include="Source\j_Check_Conditional_Event_INICashItemRemoteStoreQE_1400088DC.cpp" />
    <ClCompile Include="Source\j_Check_Conditional_Event_StatusCashItemRemoteStor_14000B4FB.cpp" />
    <ClCompile Include="Source\j_Check_GrosssalesCashItemRemoteStoreQEAAXKZ_140002B7B.cpp" />
    <ClCompile Include="Source\j_check_loaded_cde_statusCashItemRemoteStoreQEAAXX_14000F6AF.cpp" />
    <ClCompile Include="Source\j_Check_Loaded_Event_StatusCashItemRemoteStoreQEAA_14000F1D2.cpp" />
    <ClCompile Include="Source\j_Check_Total_SellingCashItemRemoteStoreQEAAXXZ_14000AB50.cpp" />
    <ClCompile Include="Source\j_CleanUpBattleCNormalGuildBattleGuildGUILD_BATTLE_140011671.cpp" />
    <ClCompile Include="Source\j_CleanUpBattleCNormalGuildBattleGuildMemberGUILD__140013CA0.cpp" />
    <ClCompile Include="Source\j_CleanUpBattleCNormalGuildBattleGUILD_BATTLEQEAAX_1400080BC.cpp" />
    <ClCompile Include="Source\j_CleanUpCCurrentGuildBattleInfoManagerGUILD_BATTL_140003571.cpp" />
    <ClCompile Include="Source\j_CleanUpCGuildBattleControllerIEAAXXZ_14000FDF3.cpp" />
    <ClCompile Include="Source\j_CleanUpCGuildBattleRankManagerGUILD_BATTLEIEAAXX_14000A835.cpp" />
    <ClCompile Include="Source\j_CleanUpDanglingReservedScheduleCGuildBattleReser_140002F86.cpp" />
    <ClCompile Include="Source\j_CleanUpDanglingReservedScheduleCGuildBattleReser_1400039AE.cpp" />
    <ClCompile Include="Source\j_CleanUpDanglingReservedScheduleCGuildBattleSched_140013D68.cpp" />
    <ClCompile Include="Source\j_ClearAllCGuildBattleSchedulePoolGUILD_BATTLEQEAA_14000B343.cpp" />
    <ClCompile Include="Source\j_ClearBallCNormalGuildBattleFieldGUILD_BATTLEQEAA_14000F6AA.cpp" />
    <ClCompile Include="Source\j_ClearByDayIDCGuildBattleSchedulePoolGUILD_BATTLE_14000DA9E.cpp" />
    <ClCompile Include="Source\j_ClearCCurrentGuildBattleInfoManagerGUILD_BATTLEQ_140004C23.cpp" />
    <ClCompile Include="Source\j_ClearCGuildBattleControllerQEAAXXZ_14000ACE5.cpp" />
    <ClCompile Include="Source\j_ClearCGuildBattleRankManagerGUILD_BATTLEIEAAXEZ_140012440.cpp" />
    <ClCompile Include="Source\j_ClearCGuildBattleRankManagerGUILD_BATTLEQEAAXXZ_14000C67B.cpp" />
    <ClCompile Include="Source\j_ClearCGuildBattleReservedScheduleGUILD_BATTLEQEA_140008A35.cpp" />
    <ClCompile Include="Source\j_ClearCGuildBattleReservedScheduleGUILD_BATTLEQEA_140012742.cpp" />
    <ClCompile Include="Source\j_ClearCGuildBattleReservedScheduleListManagerGUIL_140010CE9.cpp" />
    <ClCompile Include="Source\j_ClearCGuildBattleReservedScheduleMapGroupGUILD_B_140007036.cpp" />
    <ClCompile Include="Source\j_ClearCGuildBattleReservedScheduleMapGroupGUILD_B_1400128A5.cpp" />
    <ClCompile Include="Source\j_ClearCGuildBattleScheduleGUILD_BATTLEQEAAXXZ_140006573.cpp" />
    <ClCompile Include="Source\j_ClearCGuildBattleScheduleManagerGUILD_BATTLEAEAA_140012B02.cpp" />
    <ClCompile Include="Source\j_ClearCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1400016F9.cpp" />
    <ClCompile Include="Source\j_ClearCNormalGuildBattleGuildGUILD_BATTLEQEAAXXZ_14000289C.cpp" />
    <ClCompile Include="Source\j_ClearCNormalGuildBattleGuildMemberGUILD_BATTLEQE_14000AA4C.cpp" />
    <ClCompile Include="Source\j_ClearCNormalGuildBattleGUILD_BATTLEQEAAXXZ_14000DC1F.cpp" />
    <ClCompile Include="Source\j_ClearCNormalGuildBattleManagerGUILD_BATTLEIEAAXP_14000BB1D.cpp" />
    <ClCompile Include="Source\j_ClearCNormalGuildBattleManagerGUILD_BATTLEQEAAXX_14000D76F.cpp" />
    <ClCompile Include="Source\j_ClearCNormalGuildBattleStateListPoolGUILD_BATTLE_140011216.cpp" />
    <ClCompile Include="Source\j_ClearCPossibleBattleGuildListManagerGUILD_BATTLE_140007464.cpp" />
    <ClCompile Include="Source\j_ClearCReservedGuildScheduleDayGroupGUILD_BATTLEQ_1400090A2.cpp" />
    <ClCompile Include="Source\j_ClearCReservedGuildScheduleMapGroupGUILD_BATTLEQ_14000D062.cpp" />
    <ClCompile Include="Source\j_ClearCReservedGuildSchedulePageGUILD_BATTLEQEAA__140003C92.cpp" />
    <ClCompile Include="Source\j_ClearDBCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_140003BC5.cpp" />
    <ClCompile Include="Source\j_ClearDBRecordCNormalGuildBattleGUILD_BATTLEQEAA__140008ABC.cpp" />
    <ClCompile Include="Source\j_ClearElapsedScheduleCGuildBattleReservedSchedule_140009E71.cpp" />
    <ClCompile Include="Source\j_ClearGuildBattleCGuildQEAAXXZ_140008D87.cpp" />
    <ClCompile Include="Source\j_ClearInBattleStateCNormalGuildBattleGuildGUILD_B_14000693D.cpp" />
    <ClCompile Include="Source\j_ClearRegenCNormalGuildBattleFieldGUILD_BATTLEQEA_140002E96.cpp" />
    <ClCompile Include="Source\j_ClearTommorowScheduleByIDCGuildBattleScheduleMan_14000427D.cpp" />
    <ClCompile Include="Source\j_Clear_guild_battle_suggest_matterQEAAXXZ_14000AFDD.cpp" />
    <ClCompile Include="Source\j_CompleteClearGuildBattleRankCGuildBattleControll_1400139D0.cpp" />
    <ClCompile Include="Source\j_CompleteCreateGuildBattleRankTableCGuildBattleCo_140012355.cpp" />
    <ClCompile Include="Source\j_CompleteLoadGuildBattleTotalRecordCMainThreadAEA_140011158.cpp" />
    <ClCompile Include="Source\j_CompleteOutGuildbattleCostCGuildQEAAXKKKKZ_14000A59C.cpp" />
    <ClCompile Include="Source\j_CompleteUpdateRankCGuildBattleControllerQEAAXEEP_140006A0A.cpp" />
    <ClCompile Include="Source\j_CompleteUpdateReservedScheduleCGuildBattleContro_14001106D.cpp" />
    <ClCompile Include="Source\j_constructallocatorVCGuildBattleRewardItemGUILD_B_140011EA0.cpp" />
    <ClCompile Include="Source\j_CopyUseTimeFieldCGuildBattleReservedScheduleGUIL_140001140.cpp" />
    <ClCompile Include="Source\j_CopyUseTimeFieldCGuildBattleReservedScheduleMapG_1400047E6.cpp" />
    <ClCompile Include="Source\j_CreateFieldObjectCNormalGuildBattleFieldGUILD_BA_140004EEE.cpp" />
    <ClCompile Include="Source\j_CreateGuildBattleRankTableCRFWorldDatabaseQEAA_N_140012512.cpp" />
    <ClCompile Include="Source\j_CreateLogFileCGuildBattleLoggerGUILD_BATTLEQEAAX_1400118D3.cpp" />
    <ClCompile Include="Source\j_CreateLogFileCNormalGuildBattleGUILD_BATTLEQEAAX_140011C7F.cpp" />
    <ClCompile Include="Source\j_CreateLogFileCNormalGuildBattleLoggerGUILD_BATTL_14000E237.cpp" />
    <ClCompile Include="Source\j_CreateLoggerCNormalGuildBattleGUILD_BATTLEQEAA_N_14000D29C.cpp" />
    <ClCompile Include="Source\j_ct_StopBattleCHolyStoneSystemQEAA_NXZ_14000812F.cpp" />
    <ClCompile Include="Source\j_deallocateallocatorVCGuildBattleRewardItemGUILD__14000BC21.cpp" />
    <ClCompile Include="Source\j_DecideColorInxCNormalGuildBattleGUILD_BATTLEQEAA_14000DACB.cpp" />
    <ClCompile Include="Source\j_DecideWinCNormalGuildBattleGUILD_BATTLEIEAAEXZ_140003148.cpp" />
    <ClCompile Include="Source\j_DecPvpPointCNormalGuildBattleGuildGUILD_BATTLEQE_140009D3B.cpp" />
    <ClCompile Include="Source\j_DecPvpPointCNormalGuildBattleGuildMemberGUILD_BA_140010375.cpp" />
    <ClCompile Include="Source\j_DeleteGuildBattleInfoCRFWorldDatabaseQEAA_NXZ_14000EBCE.cpp" />
    <ClCompile Include="Source\j_DeleteGuildBattleScheduleInfoCRFWorldDatabaseQEA_140009B74.cpp" />
    <ClCompile Include="Source\j_DestGuildIsAvailableBattleRequestStateCGuildQEAA_140012472.cpp" />
    <ClCompile Include="Source\j_destroyallocatorVCGuildBattleRewardItemGUILD_BAT_140013174.cpp" />
    <ClCompile Include="Source\j_DestroyCCurrentGuildBattleInfoManagerGUILD_BATTL_140008F35.cpp" />
    <ClCompile Include="Source\j_DestroyCGuildBattleControllerSAXXZ_140003EEF.cpp" />
    <ClCompile Include="Source\j_DestroyCGuildBattleLoggerGUILD_BATTLESAXXZ_140005EC0.cpp" />
    <ClCompile Include="Source\j_DestroyCGuildBattleRankManagerGUILD_BATTLESAXXZ_14000B668.cpp" />
    <ClCompile Include="Source\j_DestroyCGuildBattleReservedScheduleListManagerGU_14000DC65.cpp" />
    <ClCompile Include="Source\j_DestroyCGuildBattleScheduleManagerGUILD_BATTLESA_14000BBA4.cpp" />
    <ClCompile Include="Source\j_DestroyCGuildBattleSchedulePoolGUILD_BATTLESAXXZ_140010CB7.cpp" />
    <ClCompile Include="Source\j_DestroyCGuildBattleSchedulerGUILD_BATTLESAXXZ_140011905.cpp" />
    <ClCompile Include="Source\j_DestroyCNormalGuildBattleFieldGUILD_BATTLEAEAAXX_140008F62.cpp" />
    <ClCompile Include="Source\j_DestroyCNormalGuildBattleFieldListGUILD_BATTLESA_1400083F5.cpp" />
    <ClCompile Include="Source\j_DestroyCNormalGuildBattleManagerGUILD_BATTLESAXX_14000361B.cpp" />
    <ClCompile Include="Source\j_DestroyCNormalGuildBattleStateListPoolGUILD_BATT_1400010FA.cpp" />
    <ClCompile Include="Source\j_DestroyCPossibleBattleGuildListManagerGUILD_BATT_14000D41D.cpp" />
    <ClCompile Include="Source\j_DestroyFieldObjectCNormalGuildBattleFieldGUILD_B_140011BBC.cpp" />
    <ClCompile Include="Source\j_dhRExtractSubStringCRFCashItemDatabaseQEAAXPEAD0_1400107C1.cpp" />
    <ClCompile Include="Source\j_DividePvpPointCNormalGuildBattleGUILD_BATTLEQEAA_14000305D.cpp" />
    <ClCompile Include="Source\j_DoDayChangedWorkCNormalGuildBattleManagerGUILD_B_140008CBA.cpp" />
    <ClCompile Include="Source\j_DoDayChangedWorkCPossibleBattleGuildListManagerG_140006302.cpp" />
    <ClCompile Include="Source\j_DropGravityStoneCNormalGuildBattleGUILD_BATTLEQE_140006BF9.cpp" />
    <ClCompile Include="Source\j_DropGravityStoneCNormalGuildBattleManagerGUILD_B_140011090.cpp" />
    <ClCompile Include="Source\j_endvectorVCGuildBattleRewardItemGUILD_BATTLEVall_140004642.cpp" />
    <ClCompile Include="Source\j_EnterCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuil_1400117C5.cpp" />
    <ClCompile Include="Source\j_EnterCNormalGuildBattleStateCountDownGUILD_BATTL_140010BF4.cpp" />
    <ClCompile Include="Source\j_EnterCNormalGuildBattleStateGUILD_BATTLEMEAAHPEA_140005FC9.cpp" />
    <ClCompile Include="Source\j_EnterCNormalGuildBattleStateGUILD_BATTLEUEAAHPEA_14000DC1A.cpp" />
    <ClCompile Include="Source\j_EnterCNormalGuildBattleStateInBattleGUILD_BATTLE_140008869.cpp" />
    <ClCompile Include="Source\j_EnterCNormalGuildBattleStateNotifyGUILD_BATTLEME_140012B34.cpp" />
    <ClCompile Include="Source\j_EnterCNormalGuildBattleStateReadyGUILD_BATTLEMEA_140005A10.cpp" />
    <ClCompile Include="Source\j_EnterCNormalGuildBattleStateRoundGUILD_BATTLEMEA_14000F25E.cpp" />
    <ClCompile Include="Source\j_EnterCNormalGuildBattleStateRoundGUILD_BATTLEUEA_140009043.cpp" />
    <ClCompile Include="Source\j_EnterCNormalGuildBattleStateRoundProcessGUILD_BA_14000E7FF.cpp" />
    <ClCompile Include="Source\j_EnterCNormalGuildBattleStateRoundReturnStartPosG_14000FD0D.cpp" />
    <ClCompile Include="Source\j_EnterCNormalGuildBattleStateRoundStartGUILD_BATT_14001307F.cpp" />
    <ClCompile Include="Source\j_erasevectorVCGuildBattleRewardItemGUILD_BATTLEVa_14000BD70.cpp" />
    <ClCompile Include="Source\j_fillPEAVCGuildBattleRewardItemGUILD_BATTLEV12std_140009642.cpp" />
    <ClCompile Include="Source\j_fill_eff_list_ATTACK_DELAY_CHECKERQEAA_NXZ_140013B60.cpp" />
    <ClCompile Include="Source\j_fill_mas_list_ATTACK_DELAY_CHECKERQEAA_NXZ_1400088D7.cpp" />
    <ClCompile Include="Source\j_FinCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuildB_1400062A8.cpp" />
    <ClCompile Include="Source\j_FinCNormalGuildBattleStateDivideGUILD_BATTLEMEAA_140009E26.cpp" />
    <ClCompile Include="Source\j_FinCNormalGuildBattleStateFinGUILD_BATTLEMEAAHPE_140010CF3.cpp" />
    <ClCompile Include="Source\j_FinCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAVC_14000B32F.cpp" />
    <ClCompile Include="Source\j_FinCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAVC_1400084E0.cpp" />
    <ClCompile Include="Source\j_FinCNormalGuildBattleStateInBattleGUILD_BATTLEME_14000F4E3.cpp" />
    <ClCompile Include="Source\j_FinCNormalGuildBattleStateReturnGUILD_BATTLEMEAA_1400056CD.cpp" />
    <ClCompile Include="Source\j_FinCNormalGuildBattleStateRoundGUILD_BATTLEMEAAH_1400031D9.cpp" />
    <ClCompile Include="Source\j_FinCNormalGuildBattleStateRoundGUILD_BATTLEUEAAH_140011CA2.cpp" />
    <ClCompile Include="Source\j_FinCNormalGuildBattleStateRoundStartGUILD_BATTLE_1400139E9.cpp" />
    <ClCompile Include="Source\j_FindCashRecCashItemRemoteStoreSAPEBU_CashShop_fl_1400070D1.cpp" />
    <ClCompile Include="Source\j_FindCGuildBattleRankManagerGUILD_BATTLEIEAA_NEKA_140005326.cpp" />
    <ClCompile Include="Source\j_FindCReservedGuildScheduleDayGroupGUILD_BATTLEQE_14000CBB2.cpp" />
    <ClCompile Include="Source\j_FindCReservedGuildScheduleMapGroupGUILD_BATTLEQE_14000E4DF.cpp" />
    <ClCompile Include="Source\j_FindCReservedGuildSchedulePageGUILD_BATTLEQEAA_N_14000C662.cpp" />
    <ClCompile Include="Source\j_FlashDamageProcCAttackIEAAXHHHH_NZ_14000B186.cpp" />
    <ClCompile Include="Source\j_FlipCGuildBattleControllerQEAAXXZ_140007798.cpp" />
    <ClCompile Include="Source\j_FlipCGuildBattleReservedScheduleGUILD_BATTLEQEAA_14000249B.cpp" />
    <ClCompile Include="Source\j_FlipCGuildBattleReservedScheduleListManagerGUILD_140002432.cpp" />
    <ClCompile Include="Source\j_FlipCGuildBattleReservedScheduleMapGroupGUILD_BA_14000B069.cpp" />
    <ClCompile Include="Source\j_FlipCGuildBattleScheduleManagerGUILD_BATTLEAEAAX_140010546.cpp" />
    <ClCompile Include="Source\j_FlipCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_140003094.cpp" />
    <ClCompile Include="Source\j_FlipCReservedGuildScheduleDayGroupGUILD_BATTLEQE_140008AA8.cpp" />
    <ClCompile Include="Source\j_FlipCReservedGuildScheduleMapGroupGUILD_BATTLEQE_14000FFDD.cpp" />
    <ClCompile Include="Source\j_FlipCReservedGuildSchedulePageGUILD_BATTLEQEAAXX_140005DE9.cpp" />
    <ClCompile Include="Source\j_ForceNextCGuildBattleStateListGUILD_BATTLEIEAAXX_14001065E.cpp" />
    <ClCompile Include="Source\j_force_endup_cash_discount_eventCashItemRemoteSto_1400070D6.cpp" />
    <ClCompile Include="Source\j_Get1PCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNorm_140006037.cpp" />
    <ClCompile Include="Source\j_Get2PCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNorm_1400134B2.cpp" />
    <ClCompile Include="Source\j_GetAmountCGuildBattleRewardItemGUILD_BATTLEQEBAE_140007B8F.cpp" />
    <ClCompile Include="Source\j_GetANSIGuildNameCNormalGuildBattleGuildGUILD_BAT_14000A303.cpp" />
    <ClCompile Include="Source\j_GetAttackDelay_WEAPON_PARAMQEAAKHHZ_140008F3A.cpp" />
    <ClCompile Include="Source\j_GetAttackDPCAnimusUEAAHXZ_14000E417.cpp" />
    <ClCompile Include="Source\j_GetAttackDPCGameObjectUEAAHXZ_1400026C6.cpp" />
    <ClCompile Include="Source\j_GetAttackDPCGuardTowerUEAAHXZ_14000A8F3.cpp" />
    <ClCompile Include="Source\j_GetAttackDPCHolyKeeperUEAAHXZ_140009F20.cpp" />
    <ClCompile Include="Source\j_GetAttackDPCHolyStoneUEAAHXZ_140007C11.cpp" />
    <ClCompile Include="Source\j_GetAttackDPCMonsterUEAAHXZ_140011725.cpp" />
    <ClCompile Include="Source\j_GetAttackDPCTrapUEAAHXZ_1400110F9.cpp" />
    <ClCompile Include="Source\j_GetAttackPartCAnimusQEAAHXZ_140006D57.cpp" />
    <ClCompile Include="Source\j_GetAttackPartCMonsterQEAAHXZ_140005BD2.cpp" />
    <ClCompile Include="Source\j_GetAttackPivotCHolyKeeperQEAAPEAMXZ_14000B63B.cpp" />
    <ClCompile Include="Source\j_GetAttackRangeCAnimusUEAAMXZ_1400091E2.cpp" />
    <ClCompile Include="Source\j_GetAttackRangeCGameObjectUEAAMXZ_140010334.cpp" />
    <ClCompile Include="Source\j_GetAttackRangeCGuardTowerUEAAMXZ_140013642.cpp" />
    <ClCompile Include="Source\j_GetAttackRangeCHolyKeeperUEAAMXZ_14000E34F.cpp" />
    <ClCompile Include="Source\j_GetAttackRangeCMonsterUEAAMXZ_140008BF7.cpp" />
    <ClCompile Include="Source\j_GetAttackRangeCTrapUEAAMXZ_140004462.cpp" />
    <ClCompile Include="Source\j_GetAttackToolType_WEAPON_PARAMQEAAHXZ_14000F047.cpp" />
    <ClCompile Include="Source\j_GetBattleByGuildSerialCNormalGuildBattleManagerG_140013101.cpp" />
    <ClCompile Include="Source\j_GetBattleCNormalGuildBattleManagerGUILD_BATTLEIE_14000A4E8.cpp" />
    <ClCompile Include="Source\j_GetBattleModeTimeCMonsterAIQEAAKXZ_140002801.cpp" />
    <ClCompile Include="Source\j_GetBattleTimeCGuildBattleScheduleGUILD_BATTLEQEA_14000DB7F.cpp" />
    <ClCompile Include="Source\j_GetBattleTurmCGuildBattleScheduleGUILD_BATTLEQEA_140010A3C.cpp" />
    <ClCompile Include="Source\j_GetBlueCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNo_1400129EF.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataBRUEAAHPEAU_Ca_14000F547.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataCNUEAAHPEAU_Ca_1400135E8.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataESUEAAHPEAU_Ca_1400070A4.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataGBUEAAHPEAU_Ca_140009B51.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataIDUEAAHPEAU_Ca_1400099DA.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataJPUEAAHPEAU_Ca_14000A358.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataKRUEAAHPEAU_Ca_140009F8E.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataNULLUEAAHPEAU__140005FB0.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataPHUEAAHPEAU_Ca_1400076AD.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataRUUEAAHPEAU_Ca_140009E44.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataTHUEAAHPEAU_Ca_140010A05.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataTWUEAAHPEAU_Ca_1400062CB.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataUEAAHPEAU_Cash_14000D92C.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingDataUSUEAAHPEAU_Ca_140001352.cpp" />
    <ClCompile Include="Source\j_GetCashItemPriceCNationSettingManagerQEAAHPEAU_C_1400088B9.cpp" />
    <ClCompile Include="Source\j_GetCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAVC_1400077B6.cpp" />
    <ClCompile Include="Source\j_GetCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAVC_140012AD0.cpp" />
    <ClCompile Include="Source\j_GetCircleZoneCGuildBattleControllerQEAAPEAVCGame_1400020B3.cpp" />
    <ClCompile Include="Source\j_GetCircleZoneCNormalGuildBattleFieldGUILD_BATTLE_140009AE3.cpp" />
    <ClCompile Include="Source\j_GetCircleZoneCNormalGuildBattleFieldListGUILD_BA_140010B72.cpp" />
    <ClCompile Include="Source\j_GetCNormalGuildBattleStateListPoolGUILD_BATTLEQE_14001139C.cpp" />
    <ClCompile Include="Source\j_GetColorInxCNormalGuildBattleGuildGUILD_BATTLEQE_140006EAB.cpp" />
    <ClCompile Include="Source\j_GetColorNameCNormalGuildBattleGuildGUILD_BATTLEQ_1400011A4.cpp" />
    <ClCompile Include="Source\j_GetCombatStateCMonsterQEAAEXZ_140008C4C.cpp" />
    <ClCompile Include="Source\j_GetCurScheduleIDCGuildBattleReservedScheduleGUIL_14000675D.cpp" />
    <ClCompile Include="Source\j_GetCurScheduleIDCGuildBattleReservedScheduleMapG_140006EB0.cpp" />
    <ClCompile Include="Source\j_GetCurScheduleIDCGuildBattleScheduleManagerGUILD_14000C6BC.cpp" />
    <ClCompile Include="Source\j_GetDamagedObjNumCNuclearBombQEAAHXZ_14000ABB9.cpp" />
    <ClCompile Include="Source\j_GetDayIDCGuildBattleReservedScheduleMapGroupGUIL_140001B6D.cpp" />
    <ClCompile Include="Source\j_GetEmptyMemberCNormalGuildBattleGuildGUILD_BATTL_140009D90.cpp" />
    <ClCompile Include="Source\j_GetEvnetTimeCashItemRemoteStoreQEAAXPEAU_cash_ev_14000C423.cpp" />
    <ClCompile Include="Source\j_GetFieldCNormalGuildBattleFieldListGUILD_BATTLEQ_1400038F5.cpp" />
    <ClCompile Include="Source\j_GetFieldCNormalGuildBattleFieldListGUILD_BATTLEQ_14000A42A.cpp" />
    <ClCompile Include="Source\j_GetFieldCNormalGuildBattleGUILD_BATTLEQEAAPEAVCN_14000F5DD.cpp" />
    <ClCompile Include="Source\j_GetFirstMapFieldByRaceCNormalGuildBattleFieldLis_14000D2BF.cpp" />
    <ClCompile Include="Source\j_GetFirstMapInxByRaceCNormalGuildBattleFieldListG_14000B2E9.cpp" />
    <ClCompile Include="Source\j_GetGoalCntCNormalGuildBattleGuildGUILD_BATTLEQEA_140011CED.cpp" />
    <ClCompile Include="Source\j_GetGoalCountCNormalGuildBattleGuildMemberGUILD_B_140008D8C.cpp" />
    <ClCompile Include="Source\j_GetGravityStoneCNormalGuildBattleGUILD_BATTLEQEA_140013C0F.cpp" />
    <ClCompile Include="Source\j_GetGuildBattleNumberCNormalGuildBattleGUILD_BATT_14000BAC3.cpp" />
    <ClCompile Include="Source\j_GetGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAP_1400048A4.cpp" />
    <ClCompile Include="Source\j_GetGuildCNormalGuildBattleGUILD_BATTLEQEAAPEAVCN_140003120.cpp" />
    <ClCompile Include="Source\j_GetGuildNameCNormalGuildBattleGuildGUILD_BATTLEQ_14000BD1B.cpp" />
    <ClCompile Include="Source\j_GetGuildRaceCNormalGuildBattleGuildGUILD_BATTLEQ_14000C937.cpp" />
    <ClCompile Include="Source\j_GetGuildSerialCNormalGuildBattleGuildGUILD_BATTL_140005560.cpp" />
    <ClCompile Include="Source\j_GetIDCGuildBattleReservedScheduleGUILD_BATTLEQEA_1400069F6.cpp" />
    <ClCompile Include="Source\j_GetIDCNormalGuildBattleGUILD_BATTLEQEAAKXZ_140008175.cpp" />
    <ClCompile Include="Source\j_GetIndexCNormalGuildBattleGuildMemberGUILD_BATTL_14000DCE2.cpp" />
    <ClCompile Include="Source\j_GetInfoCNormalGuildBattleGUILD_BATTLEQEAA_NAEAU__14000ECC3.cpp" />
    <ClCompile Include="Source\j_GetItemCodeCGuildBattleRewardItemGUILD_BATTLEQEB_1400038AF.cpp" />
    <ClCompile Include="Source\j_GetJoinMemberCntCNormalGuildBattleGuildGUILD_BAT_1400037BA.cpp" />
    <ClCompile Include="Source\j_GetKillCountCNormalGuildBattleGuildMemberGUILD_B_14000F0D3.cpp" />
    <ClCompile Include="Source\j_GetKillCountSumCNormalGuildBattleGuildGUILD_BATT_14000D260.cpp" />
    <ClCompile Include="Source\j_GetLeftTimeCCurrentGuildBattleInfoManagerGUILD_B_140009E8F.cpp" />
    <ClCompile Include="Source\j_GetLeftTimeCGuildBattleScheduleGUILD_BATTLEQEAA__14000B843.cpp" />
    <ClCompile Include="Source\j_GetLimDiscoutCashItemRemoteStoreQEAAEXZ_140005D8F.cpp" />
    <ClCompile Include="Source\j_GetLoggerCNormalGuildBattleGUILD_BATTLEQEAAPEAVC_140004ACF.cpp" />
    <ClCompile Include="Source\j_GetMapCNormalGuildBattleFieldGUILD_BATTLEQEAAPEA_14000D972.cpp" />
    <ClCompile Include="Source\j_GetMapCntCNormalGuildBattleFieldListGUILD_BATTLE_140011027.cpp" />
    <ClCompile Include="Source\j_GetMapCodeCNormalGuildBattleFieldGUILD_BATTLEQEA_140007135.cpp" />
    <ClCompile Include="Source\j_GetMapIDCNormalGuildBattleFieldGUILD_BATTLEQEAAK_14000877E.cpp" />
    <ClCompile Include="Source\j_GetMapInxCNormalGuildBattleFieldListGUILD_BATTLE_14000FA2E.cpp" />
    <ClCompile Include="Source\j_GetMapInxListCNormalGuildBattleFieldListGUILD_BA_14001089D.cpp" />
    <ClCompile Include="Source\j_GetMapStrCodeCNormalGuildBattleFieldGUILD_BATTLE_140006005.cpp" />
    <ClCompile Include="Source\j_GetMaxJoinMemberCountCNormalGuildBattleGuildGUIL_140006136.cpp" />
    <ClCompile Include="Source\j_GetMaxPageCReservedGuildScheduleMapGroupGUILD_BA_14000E3F9.cpp" />
    <ClCompile Include="Source\j_GetMemberCNormalGuildBattleGuildGUILD_BATTLEIEAA_1400057D1.cpp" />
    <ClCompile Include="Source\j_GetMemberPtrCNormalGuildBattleGuildGUILD_BATTLEQ_14000B681.cpp" />
    <ClCompile Include="Source\j_GetObjTypeCGuildBattleGUILD_BATTLEUEAAHXZ_1400086CF.cpp" />
    <ClCompile Include="Source\j_GetObjTypeCNormalGuildBattleGUILD_BATTLEUEAAHXZ_140002608.cpp" />
    <ClCompile Include="Source\j_GetPortalIndexInfoCNormalGuildBattleFieldGUILD_B_140009061.cpp" />
    <ClCompile Include="Source\j_GetRealStartTimeCGuildBattleScheduleGUILD_BATTLE_1400070F4.cpp" />
    <ClCompile Include="Source\j_GetRedCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNor_14000BADC.cpp" />
    <ClCompile Include="Source\j_GetRefCGuildBattleSchedulePoolGUILD_BATTLEQEAAPE_14000E89F.cpp" />
    <ClCompile Include="Source\j_GetRegenerCGuildBattleControllerQEAAPEAVCGameObj_14000D6F7.cpp" />
    <ClCompile Include="Source\j_GetRegenerCNormalGuildBattleFieldGUILD_BATTLEQEA_14000EDE0.cpp" />
    <ClCompile Include="Source\j_GetRegenerCNormalGuildBattleFieldListGUILD_BATTL_14000787E.cpp" />
    <ClCompile Include="Source\j_GetRemainNumOfGoodCashItemRemoteStoreQEAAHGZ_140009228.cpp" />
    <ClCompile Include="Source\j_GetRemainNumOfGoodCashItemRemoteStoreQEAAHQEADZ_1400100AF.cpp" />
    <ClCompile Include="Source\j_GetScoreCNormalGuildBattleGuildGUILD_BATTLEQEAAK_140002883.cpp" />
    <ClCompile Include="Source\j_GetSerialCNormalGuildBattleGuildMemberGUILD_BATT_14000FE98.cpp" />
    <ClCompile Include="Source\j_GetSetDiscoutCashItemRemoteStoreQEAAEEZ_14000B54B.cpp" />
    <ClCompile Include="Source\j_GetSIDCGuildBattleScheduleGUILD_BATTLEQEAAKXZ_14000522C.cpp" />
    <ClCompile Include="Source\j_GetSIDCGuildBattleSchedulePoolGUILD_BATTLEQEAAKI_14000FAB0.cpp" />
    <ClCompile Include="Source\j_GetSLIDCGuildBattleReservedScheduleMapGroupGUILD_1400064A6.cpp" />
    <ClCompile Include="Source\j_GetStartBattleTickTimeCHolyStoneSystemQEAAKXZ_14000DC5B.cpp" />
    <ClCompile Include="Source\j_GetStateCGuildBattleScheduleGUILD_BATTLEQEAAHXZ_1400057B3.cpp" />
    <ClCompile Include="Source\j_GetStoneCGuildBattleControllerQEAAPEAVCGameObjec_140004DEF.cpp" />
    <ClCompile Include="Source\j_GetStoneCNormalGuildBattleFieldGUILD_BATTLEQEAAP_140009D04.cpp" />
    <ClCompile Include="Source\j_GetStoneCNormalGuildBattleFieldListGUILD_BATTLEQ_140006F1E.cpp" />
    <ClCompile Include="Source\j_GetTermCGuildBattleStateGUILD_BATTLEUEAAAVCTimeS_140007C0C.cpp" />
    <ClCompile Include="Source\j_GetTermCGuildBattleStateListGUILD_BATTLEQEAAAVCT_14000B0A5.cpp" />
    <ClCompile Include="Source\j_GetTermCNormalGuildBattleStateCountDownGUILD_BAT_140003404.cpp" />
    <ClCompile Include="Source\j_GetTermCNormalGuildBattleStateDivideGUILD_BATTLE_140012DFA.cpp" />
    <ClCompile Include="Source\j_GetTermCNormalGuildBattleStateFinGUILD_BATTLEUEA_1400089AE.cpp" />
    <ClCompile Include="Source\j_GetTermCNormalGuildBattleStateInBattleGUILD_BATT_14000EA2F.cpp" />
    <ClCompile Include="Source\j_GetTermCNormalGuildBattleStateNotifyGUILD_BATTLE_14000EC8C.cpp" />
    <ClCompile Include="Source\j_GetTermCNormalGuildBattleStateReadyGUILD_BATTLEU_140008BB1.cpp" />
    <ClCompile Include="Source\j_GetTermCNormalGuildBattleStateReturnGUILD_BATTLE_1400025B8.cpp" />
    <ClCompile Include="Source\j_GetTimeCGuildBattleScheduleGUILD_BATTLEQEAAAVCTi_140001FF5.cpp" />
    <ClCompile Include="Source\j_GetTodayDayIDCGuildBattleScheduleManagerGUILD_BA_140011748.cpp" />
    <ClCompile Include="Source\j_GetTodaySLIDByMapCGuildBattleScheduleManagerGUIL_14000294B.cpp" />
    <ClCompile Include="Source\j_GetToleranceProbMonsterSFContDamageToleracneQEAA_140012E3B.cpp" />
    <ClCompile Include="Source\j_GetTomorrowDayIDCGuildBattleScheduleManagerGUILD_14000FBF5.cpp" />
    <ClCompile Include="Source\j_GetTomorrowSLIDByMapCGuildBattleScheduleManagerG_140006E15.cpp" />
    <ClCompile Include="Source\j_GetTopGoalMemberCNormalGuildBattleGuildGUILD_BAT_1400118C9.cpp" />
    <ClCompile Include="Source\j_GetTopKillMemberCNormalGuildBattleGuildGUILD_BAT_1400132B9.cpp" />
    <ClCompile Include="Source\j_GetWinnerGradeCBattleTournamentInfoQEAAEKPEADZ_140012A49.cpp" />
    <ClCompile Include="Source\j_Get_CashEvent_StatusCashItemRemoteStoreQEAAEEZ_14000346D.cpp" />
    <ClCompile Include="Source\j_get_cde_statusCashItemRemoteStoreQEAAEXZ_14001028F.cpp" />
    <ClCompile Include="Source\j_Get_Conditional_Event_NameCashItemRemoteStoreQEA_140008C7E.cpp" />
    <ClCompile Include="Source\j_Get_Conditional_Event_StatusCashItemRemoteStoreQ_140004DC2.cpp" />
    <ClCompile Include="Source\j_GoalCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEAV_14000BAA5.cpp" />
    <ClCompile Include="Source\j_GoalCNormalGuildBattleGUILD_BATTLEQEAAEKHZ_14000214E.cpp" />
    <ClCompile Include="Source\j_GoalCNormalGuildBattleStateGUILD_BATTLEUEAAXXZ_14000C117.cpp" />
    <ClCompile Include="Source\j_GoodsListBuyByCashCashItemRemoteStoreAEAA_NGPEAD_140011252.cpp" />
    <ClCompile Include="Source\j_GoodsListBuyByGoldCashItemRemoteStoreAEAA_NGPEAD_140010DE3.cpp" />
    <ClCompile Include="Source\j_GoodsListCashItemRemoteStoreQEAA_NGPEADZ_140001A82.cpp" />
    <ClCompile Include="Source\j_GotoCGuildBattleStateListGUILD_BATTLEQEAAHXZ_14000C63F.cpp" />
    <ClCompile Include="Source\j_GotoStateCGuildBattleStateListGUILD_BATTLEQEAA_N_140013246.cpp" />
    <ClCompile Include="Source\j_GuildBattleBlockReportCNetworkEXAEAA_NHPEADZ_140011856.cpp" />
    <ClCompile Include="Source\j_GuildBattleCurrentBattleInfoRequestCNetworkEXAEA_14000C46E.cpp" />
    <ClCompile Include="Source\j_GuildBattleGetGravityStoneRequestCNetworkEXAEAA__14000F989.cpp" />
    <ClCompile Include="Source\j_GuildBattleGoalRequestCNetworkEXAEAA_NHPEADZ_140012FA8.cpp" />
    <ClCompile Include="Source\j_GuildBattleJoinGuildBattleRequestCNetworkEXAEAA__140005867.cpp" />
    <ClCompile Include="Source\j_GuildBattlePossibleGuildBattleListCNetworkEXAEAA_140005A06.cpp" />
    <ClCompile Include="Source\j_GuildBattleRankListRequestCNetworkEXAEAA_NHPEADZ_140013BAB.cpp" />
    <ClCompile Include="Source\j_GuildBattleReservedScheduleRequestCNetworkEXAEAA_14000E377.cpp" />
    <ClCompile Include="Source\j_GuildBattleResultLogCNormalGuildBattleGUILD_BATT_14000E804.cpp" />
    <ClCompile Include="Source\j_GuildBattleResultLogNotifyWebCNormalGuildBattleG_14000B83E.cpp" />
    <ClCompile Include="Source\j_GuildBattleResultLogPushDBLogCNormalGuildBattleG_14000C80B.cpp" />
    <ClCompile Include="Source\j_GuildBattleSuggestRequestToDestGuildCGuildQEAAEK_140011F59.cpp" />
    <ClCompile Include="Source\j_GuildBattleTakeGravityStoneRequestCNetworkEXAEAA_140007F18.cpp" />
    <ClCompile Include="Source\j_IncPvpPointCNormalGuildBattleGuildGUILD_BATTLEQE_14000D206.cpp" />
    <ClCompile Include="Source\j_IncPvpPointCNormalGuildBattleGuildMemberGUILD_BA_14000E020.cpp" />
    <ClCompile Include="Source\j_IncVerCReservedGuildSchedulePageGUILD_BATTLEQEAA_14000770C.cpp" />
    <ClCompile Include="Source\j_inform_cashdiscount_eventCashItemRemoteStoreQEAA_140012B3E.cpp" />
    <ClCompile Include="Source\j_inform_cashdiscount_status_allCashItemRemoteStor_14000B59B.cpp" />
    <ClCompile Include="Source\j_Inform_CashEventCashItemRemoteStoreQEAAXGZ_140012A30.cpp" />
    <ClCompile Include="Source\j_Inform_CashEvent_Status_AllCashItemRemoteStoreQE_14001369C.cpp" />
    <ClCompile Include="Source\j_Inform_ConditionalEventCashItemRemoteStoreQEAAXG_14000FDA3.cpp" />
    <ClCompile Include="Source\j_Inform_ConditionalEvent_Status_AllCashItemRemote_14000EC19.cpp" />
    <ClCompile Include="Source\j_InGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY_SYN_14000EE3A.cpp" />
    <ClCompile Include="Source\j_InGuildbattleRewardMoneyCMainThreadQEAAXPEAU_DB__1400060CD.cpp" />
    <ClCompile Include="Source\j_InitCBattleTournamentInfoQEAAXXZ_140005EE3.cpp" />
    <ClCompile Include="Source\j_InitCCurrentGuildBattleInfoManagerGUILD_BATTLEQE_14000C4A0.cpp" />
    <ClCompile Include="Source\j_InitCGuildBattleControllerQEAA_NXZ_14000FD4E.cpp" />
    <ClCompile Include="Source\j_InitCGuildBattleLoggerGUILD_BATTLEQEAA_NXZ_140008341.cpp" />
    <ClCompile Include="Source\j_InitCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_14000A466.cpp" />
    <ClCompile Include="Source\j_InitCGuildBattleReservedScheduleListManagerGUILD_14000FEAC.cpp" />
    <ClCompile Include="Source\j_InitCGuildBattleReservedScheduleMapGroupGUILD_BA_140001186.cpp" />
    <ClCompile Include="Source\j_InitCGuildBattleRewardItemGUILD_BATTLEQEAA_NGZ_140010AE1.cpp" />
    <ClCompile Include="Source\j_InitCGuildBattleRewardItemManagerGUILD_BATTLEQEA_140012FBC.cpp" />
    <ClCompile Include="Source\j_InitCGuildBattleScheduleManagerGUILD_BATTLEQEAA__140003D73.cpp" />
    <ClCompile Include="Source\j_InitCGuildBattleSchedulePoolGUILD_BATTLEQEAA_NIZ_140012ACB.cpp" />
    <ClCompile Include="Source\j_InitCGuildBattleSchedulerGUILD_BATTLEQEAA_NXZ_1400125EE.cpp" />
    <ClCompile Include="Source\j_InitCNormalGuildBattleFieldGUILD_BATTLEQEAA_NIZ_140003D46.cpp" />
    <ClCompile Include="Source\j_InitCNormalGuildBattleFieldListGUILD_BATTLEQEAA__140001A14.cpp" />
    <ClCompile Include="Source\j_InitCNormalGuildBattleGUILD_BATTLEQEAAXPEAVCGuil_140010A37.cpp" />
    <ClCompile Include="Source\j_InitCNormalGuildBattleGUILD_BATTLEQEAA_N_NIKKKKE_14000F100.cpp" />
    <ClCompile Include="Source\j_InitCNormalGuildBattleLoggerGUILD_BATTLEQEAA_NXZ_140004C55.cpp" />
    <ClCompile Include="Source\j_InitCNormalGuildBattleManagerGUILD_BATTLEQEAA_NX_14000351C.cpp" />
    <ClCompile Include="Source\j_InitCNormalGuildBattleStateListPoolGUILD_BATTLEQ_14000CB71.cpp" />
    <ClCompile Include="Source\j_InitCPossibleBattleGuildListManagerGUILD_BATTLEQ_140008355.cpp" />
    <ClCompile Include="Source\j_InitCReservedGuildScheduleDayGroupGUILD_BATTLEQE_14000B97E.cpp" />
    <ClCompile Include="Source\j_InitCReservedGuildScheduleMapGroupGUILD_BATTLEQE_140003599.cpp" />
    <ClCompile Include="Source\j_InitCReservedGuildSchedulePageGUILD_BATTLEQEAA_N_140008125.cpp" />
    <ClCompile Include="Source\j_InitializeCashItemRemoteStoreQEAA_NXZ_14000D742.cpp" />
    <ClCompile Include="Source\j_InitMonsterSFContDamageToleracneQEAAXMZ_1400035C6.cpp" />
    <ClCompile Include="Source\j_InitUseFieldCNormalGuildBattleFieldListGUILD_BAT_14000AD80.cpp" />
    <ClCompile Include="Source\j_Init_ATTACK_DELAY_CHECKERQEAAXXZ_140013DB3.cpp" />
    <ClCompile Include="Source\j_init_eff_list_ATTACK_DELAY_CHECKERQEAAXXZ_14000FF29.cpp" />
    <ClCompile Include="Source\j_init_mas_list_ATTACK_DELAY_CHECKERQEAAXXZ_140001E60.cpp" />
    <ClCompile Include="Source\j_InsertGuildBattleDefaultRecordCRFWorldDatabaseQE_1400118B5.cpp" />
    <ClCompile Include="Source\j_InsertGuildBattleRankRecordCRFWorldDatabaseQEAA__140010B9F.cpp" />
    <ClCompile Include="Source\j_InsertGuildBattleScheduleDefaultRecordCRFWorldDa_140003012.cpp" />
    <ClCompile Include="Source\j_insertvectorVCGuildBattleRewardItemGUILD_BATTLEV_1400126E3.cpp" />
    <ClCompile Include="Source\j_Insert_PatrirchItemChargeRefundCRFWorldDatabaseQ_1400099F8.cpp" />
    <ClCompile Include="Source\j_Insert_PatrirchItemChargeRefundPatriarchElectPro_14000DDD2.cpp" />
    <ClCompile Include="Source\j_Insert_RaceBattleLogCRFWorldDatabaseQEAA_NPEAU_r_14001004B.cpp" />
    <ClCompile Include="Source\j_InstanceCashItemRemoteStoreSAPEAV1XZ_140010320.cpp" />
    <ClCompile Include="Source\j_InstanceCCurrentGuildBattleInfoManagerGUILD_BATT_1400028B5.cpp" />
    <ClCompile Include="Source\j_InstanceCGuildBattleControllerSAPEAV1XZ_140013458.cpp" />
    <ClCompile Include="Source\j_InstanceCGuildBattleLoggerGUILD_BATTLESAPEAV12XZ_14000E5C0.cpp" />
    <ClCompile Include="Source\j_InstanceCGuildBattleRankManagerGUILD_BATTLESAPEA_140001E5B.cpp" />
    <ClCompile Include="Source\j_InstanceCGuildBattleReservedScheduleListManagerG_140004660.cpp" />
    <ClCompile Include="Source\j_InstanceCGuildBattleRewardItemManagerGUILD_BATTL_140008747.cpp" />
    <ClCompile Include="Source\j_InstanceCGuildBattleScheduleManagerGUILD_BATTLES_140002DE2.cpp" />
    <ClCompile Include="Source\j_InstanceCGuildBattleSchedulePoolGUILD_BATTLESAPE_1400075F4.cpp" />
    <ClCompile Include="Source\j_InstanceCGuildBattleSchedulerGUILD_BATTLESAPEAV1_14000C2BB.cpp" />
    <ClCompile Include="Source\j_InstanceCNormalGuildBattleFieldListGUILD_BATTLES_14000D4D6.cpp" />
    <ClCompile Include="Source\j_InstanceCNormalGuildBattleManagerGUILD_BATTLESAP_140003A76.cpp" />
    <ClCompile Include="Source\j_InstanceCNormalGuildBattleStateListPoolGUILD_BAT_14000C5C2.cpp" />
    <ClCompile Include="Source\j_InstanceCPossibleBattleGuildListManagerGUILD_BAT_140007E96.cpp" />
    <ClCompile Include="Source\j_IsAttackableInTownCGameObjectUEAA_NXZ_14000BC03.cpp" />
    <ClCompile Include="Source\j_IsAttackableInTownCMonsterUEAA_NXZ_140013151.cpp" />
    <ClCompile Include="Source\j_IsAvailableSuggestCGuildBattleControllerQEAAEPEA_14000898B.cpp" />
    <ClCompile Include="Source\j_IsBattleModeUseCRecallRequestQEAA_NXZ_14000D02B.cpp" />
    <ClCompile Include="Source\j_IsBeAttackedAbleAutominePersonalUEAA_N_NZ_1400021B2.cpp" />
    <ClCompile Include="Source\j_IsBeAttackedAbleCAnimusUEAA_N_NZ_140002C98.cpp" />
    <ClCompile Include="Source\j_IsBeAttackedAbleCGameObjectUEAA_N_NZ_14000CDA6.cpp" />
    <ClCompile Include="Source\j_IsBeAttackedAbleCGuardTowerUEAA_N_NZ_14000250E.cpp" />
    <ClCompile Include="Source\j_IsBeAttackedAbleCHolyKeeperUEAA_N_NZ_1400075AE.cpp" />
    <ClCompile Include="Source\j_IsBeAttackedAbleCHolyStoneUEAA_N_NZ_14001366F.cpp" />
    <ClCompile Include="Source\j_IsBeAttackedAbleCMonsterUEAA_N_NZ_140006C8F.cpp" />
    <ClCompile Include="Source\j_IsBeAttackedAbleCTrapUEAA_N_NZ_14000CDCE.cpp" />
    <ClCompile Include="Source\j_IsBuyCashItemByGoldCashItemRemoteStoreQEAA_NXZ_14000CFB3.cpp" />
    <ClCompile Include="Source\j_IsCashItemYAHEKZ_1400022A2.cpp" />
    <ClCompile Include="Source\j_IsCharInSectorCAttackSAHQEAM00MMZ_140006EBF.cpp" />
    <ClCompile Include="Source\j_IsCommitteeMemberCNormalGuildBattleGuildMemberGU_1400103FC.cpp" />
    <ClCompile Include="Source\j_IsCompleteBattle_guild_battle_suggest_matterQEAA_140012E4A.cpp" />
    <ClCompile Include="Source\j_isConEventTimeCashItemRemoteStoreQEAA_NXZ_140009BA6.cpp" />
    <ClCompile Include="Source\j_IsDayChangedCGuildBattleScheduleManagerGUILD_BAT_140010267.cpp" />
    <ClCompile Include="Source\j_IsDelay_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14000FD26.cpp" />
    <ClCompile Include="Source\j_IsDoneCGuildBattleReservedScheduleGUILD_BATTLEQE_14000280B.cpp" />
    <ClCompile Include="Source\j_IsDoneCGuildBattleReservedScheduleMapGroupGUILD__1400087FB.cpp" />
    <ClCompile Include="Source\j_IsDoneCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_14000818E.cpp" />
    <ClCompile Include="Source\j_IsEmptyCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_14000F2CC.cpp" />
    <ClCompile Include="Source\j_IsEmptyCGuildBattleStateListGUILD_BATTLEQEAA_NXZ_1400111CB.cpp" />
    <ClCompile Include="Source\j_IsEmptyCNormalGuildBattleGuildMemberGUILD_BATTLE_140004B06.cpp" />
    <ClCompile Include="Source\j_IsEmptyCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_14000763A.cpp" />
    <ClCompile Include="Source\j_IsEmptyTimeCGuildBattleReservedScheduleGUILD_BAT_140009787.cpp" />
    <ClCompile Include="Source\j_IsEmptyTimeCGuildBattleReservedScheduleMapGroupG_14000A0A6.cpp" />
    <ClCompile Include="Source\j_IsEmptyTimeCGuildBattleScheduleManagerGUILD_BATT_140010933.cpp" />
    <ClCompile Include="Source\j_IsEnableStartCNormalGuildBattleGuildMemberGUILD__1400067DA.cpp" />
    <ClCompile Include="Source\j_IsEventTimeCashItemRemoteStoreQEAA_NEZ_140005D85.cpp" />
    <ClCompile Include="Source\j_IsExistCNormalGuildBattleGuildMemberGUILD_BATTLE_140006159.cpp" />
    <ClCompile Include="Source\j_IsInBattleCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_140011FCC.cpp" />
    <ClCompile Include="Source\j_IsInBattleCNormalGuildBattleStateListGUILD_BATTL_1400103A7.cpp" />
    <ClCompile Include="Source\j_IsInBattleRegenStateCNormalGuildBattleGUILD_BATT_14000AC5E.cpp" />
    <ClCompile Include="Source\j_IsInBattleRegenStateCNormalGuildBattleStateInBat_14000BEE7.cpp" />
    <ClCompile Include="Source\j_IsInBattleRegenStateCNormalGuildBattleStateListG_140001861.cpp" />
    <ClCompile Include="Source\j_IsInBattleRegenStateCNormalGuildBattleStateRound_1400119F0.cpp" />
    <ClCompile Include="Source\j_IsJoinMemberCNormalGuildBattleGuildGUILD_BATTLEI_1400096CE.cpp" />
    <ClCompile Include="Source\j_IsMemberCNormalGuildBattleGuildGUILD_BATTLEQEAA__14000D995.cpp" />
    <ClCompile Include="Source\j_IsMemberGuildCNormalGuildBattleGUILD_BATTLEQEAA__14000C92D.cpp" />
    <ClCompile Include="Source\j_IsNullCGuildBattleRewardItemGUILD_BATTLEQEBA_NXZ_14000923C.cpp" />
    <ClCompile Include="Source\j_IsPreAttackAbleMonCMonsterQEAAHXZ_140002982.cpp" />
    <ClCompile Include="Source\j_IsProcCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_14000ACB8.cpp" />
    <ClCompile Include="Source\j_IsProcCGuildBattleStateListGUILD_BATTLEQEAA_NXZ_14000E223.cpp" />
    <ClCompile Include="Source\j_IsProcCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_14000E03E.cpp" />
    <ClCompile Include="Source\j_IsReadyOrCountStateCNormalGuildBattleGUILD_BATTL_140001A7D.cpp" />
    <ClCompile Include="Source\j_IsReadyOrCountStateCNormalGuildBattleStateListGU_1400075EF.cpp" />
    <ClCompile Include="Source\j_IsRegistedMapInxCNormalGuildBattleFieldListGUILD_14000AAB5.cpp" />
    <ClCompile Include="Source\j_IsReStartCNormalGuildBattleGuildGUILD_BATTLEQEAA_140010FB9.cpp" />
    <ClCompile Include="Source\j_IsReStartCNormalGuildBattleGuildMemberGUILD_BATT_14000533A.cpp" />
    <ClCompile Include="Source\j_IsReStartCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_14000CEDC.cpp" />
    <ClCompile Include="Source\j_IsSFContDamageMonsterSFContDamageToleracneQEAA_N_1400018C0.cpp" />
    <ClCompile Include="Source\j_IsStorageCodeWithItemKindYAHHHZ_140003724.cpp" />
    <ClCompile Include="Source\j_IsWaitCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_14000BA96.cpp" />
    <ClCompile Include="Source\j_Is_Battle_ModeCGameObjectUEAA_NXZ_14000DE27.cpp" />
    <ClCompile Include="Source\j_is_cde_timeCashItemRemoteStoreQEAA_NXZ_140010866.cpp" />
    <ClCompile Include="Source\j_JoinCNormalGuildBattleGuildGUILD_BATTLEQEAAEKEAE_14000534E.cpp" />
    <ClCompile Include="Source\j_JoinCNormalGuildBattleGuildMemberGUILD_BATTLEQEA_14000FA83.cpp" />
    <ClCompile Include="Source\j_JoinCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_140003D87.cpp" />
    <ClCompile Include="Source\j_JoinCNormalGuildBattleManagerGUILD_BATTLEQEAAXHK_140012DB9.cpp" />
    <ClCompile Include="Source\j_JoinGuildCGuildBattleControllerQEAAXHKKZ_1400082CE.cpp" />
    <ClCompile Include="Source\j_JoinGuildCNormalGuildBattleManagerGUILD_BATTLEQE_14000F3EE.cpp" />
    <ClCompile Include="Source\j_JudgeBattleCNormalGuildBattleGUILD_BATTLEQEAAEXZ_1400108E8.cpp" />
    <ClCompile Include="Source\j_KillCNormalGuildBattleGuildGUILD_BATTLEQEAAHPEAV_14000D148.cpp" />
    <ClCompile Include="Source\j_KillCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_140010BB3.cpp" />
    <ClCompile Include="Source\j_KillCNormalGuildBattleManagerGUILD_BATTLEQEAAHKK_140003FCB.cpp" />
    <ClCompile Include="Source\j_LeaveGuildCNormalGuildBattleGuildGUILD_BATTLEQEA_1400035E4.cpp" />
    <ClCompile Include="Source\j_LimitedSale_check_countCashItemRemoteStoreQEAA_N_140005C27.cpp" />
    <ClCompile Include="Source\j_LoadBuyCashModeCashItemRemoteStoreAEAA_NXZ_140002ADB.cpp" />
    <ClCompile Include="Source\j_LoadCGuildBattleControllerQEAA_NXZ_140010C53.cpp" />
    <ClCompile Include="Source\j_LoadCGuildBattleRankManagerGUILD_BATTLEIEAA_NEZ_140012044.cpp" />
    <ClCompile Include="Source\j_LoadCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_14000521D.cpp" />
    <ClCompile Include="Source\j_LoadCGuildBattleReservedScheduleGUILD_BATTLEQEAA_14000F08D.cpp" />
    <ClCompile Include="Source\j_LoadCGuildBattleReservedScheduleListManagerGUILD_140002B2B.cpp" />
    <ClCompile Include="Source\j_LoadCGuildBattleReservedScheduleMapGroupGUILD_BA_140007739.cpp" />
    <ClCompile Include="Source\j_LoadCGuildBattleScheduleGUILD_BATTLEQEAA_N_NKE_J_14000A713.cpp" />
    <ClCompile Include="Source\j_LoadCGuildBattleScheduleManagerGUILD_BATTLEQEAA__1400014A6.cpp" />
    <ClCompile Include="Source\j_LoadCNormalGuildBattleManagerGUILD_BATTLEIEAA_N__140005FCE.cpp" />
    <ClCompile Include="Source\j_LoadCNormalGuildBattleManagerGUILD_BATTLEQEAA_NH_1400044A3.cpp" />
    <ClCompile Include="Source\j_LoadCPossibleBattleGuildListManagerGUILD_BATTLEQ_14000A286.cpp" />
    <ClCompile Include="Source\j_LoadCReservedGuildScheduleDayGroupGUILD_BATTLEQE_140005CA4.cpp" />
    <ClCompile Include="Source\j_LoadCReservedGuildScheduleMapGroupGUILD_BATTLEQE_14000A678.cpp" />
    <ClCompile Include="Source\j_LoadDBGuildBattleInfoCNormalGuildBattleManagerGU_14000889B.cpp" />
    <ClCompile Include="Source\j_LoadDummysCNormalGuildBattleFieldGUILD_BATTLEAEA_140005D17.cpp" />
    <ClCompile Include="Source\j_LoadGuildBattleInfoCRFWorldDatabaseQEAA_NKKPEAU__140010037.cpp" />
    <ClCompile Include="Source\j_LoadGuildBattleScheduleInfoCRFWorldDatabaseQEAAE_1400080F3.cpp" />
    <ClCompile Include="Source\j_LoadINICGuildBattleControllerAEAA_NAEAIAEAH111Z_140008AB2.cpp" />
    <ClCompile Include="Source\j_LoadNationalPriceCashItemRemoteStoreAEAA_NAEAVCR_14000C0C2.cpp" />
    <ClCompile Include="Source\j_LoadTodayScheduleCGuildBattleReservedScheduleLis_1400056EB.cpp" />
    <ClCompile Include="Source\j_LoadTomorrowScheduleCGuildBattleReservedSchedule_14000F0B0.cpp" />
    <ClCompile Include="Source\j_load_cash_discount_eventCashItemRemoteStoreQEAAX_140010F4B.cpp" />
    <ClCompile Include="Source\j_Load_Cash_EventCashItemRemoteStoreQEAAXXZ_14001208A.cpp" />
    <ClCompile Include="Source\j_load_cde_iniCashItemRemoteStoreQEAAXPEAU_cash_di_140006A1E.cpp" />
    <ClCompile Include="Source\j_Load_Conditional_EventCashItemRemoteStoreQEAAXXZ_140001505.cpp" />
    <ClCompile Include="Source\j_load_con_event_iniCashItemRemoteStoreQEAAXPEAU_c_1400125C6.cpp" />
    <ClCompile Include="Source\j_Load_Event_INICashItemRemoteStoreQEAAXPEAU_cash__140001C30.cpp" />
    <ClCompile Include="Source\j_Load_LimitedSale_Event_INICashItemRemoteStoreQEA_140004ED5.cpp" />
    <ClCompile Include="Source\j_LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEADZZ_140010802.cpp" />
    <ClCompile Include="Source\j_LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZZ_1400030E4.cpp" />
    <ClCompile Include="Source\j_LogCGuildBattleStateGUILD_BATTLEIEAAXPEADZ_140001D2A.cpp" />
    <ClCompile Include="Source\j_LogCGuildBattleStateListGUILD_BATTLEIEAAXPEADZ_140006523.cpp" />
    <ClCompile Include="Source\j_LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEAD_14000726B.cpp" />
    <ClCompile Include="Source\j_LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEA__140004AF7.cpp" />
    <ClCompile Include="Source\j_LogCNormalGuildBattleStateGUILD_BATTLEIEAAXPEAVC_140005B87.cpp" />
    <ClCompile Include="Source\j_LogCNormalGuildBattleStateRoundGUILD_BATTLEIEAAX_14000F4B1.cpp" />
    <ClCompile Include="Source\j_log_about_cash_eventCashItemRemoteStoreQEAAXPEAD_1400060FA.cpp" />
    <ClCompile Include="Source\j_LoopCGuildBattleControllerQEAAXXZ_140005DDA.cpp" />
    <ClCompile Include="Source\j_LoopCGuildBattleReservedScheduleGUILD_BATTLEQEAA_1400074FA.cpp" />
    <ClCompile Include="Source\j_LoopCGuildBattleReservedScheduleMapGroupGUILD_BA_14000175D.cpp" />
    <ClCompile Include="Source\j_LoopCGuildBattleScheduleManagerGUILD_BATTLEQEAAX_14000ABAA.cpp" />
    <ClCompile Include="Source\j_LoopCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuild_1400111FD.cpp" />
    <ClCompile Include="Source\j_LoopCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_140002059.cpp" />
    <ClCompile Include="Source\j_LoopCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAV_140010FFA.cpp" />
    <ClCompile Include="Source\j_LoopCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAV_1400065BE.cpp" />
    <ClCompile Include="Source\j_LoopCNormalGuildBattleStateInBattleGUILD_BATTLEU_14000FD08.cpp" />
    <ClCompile Include="Source\j_LoopCNormalGuildBattleStateRoundGUILD_BATTLEMEAA_14001372D.cpp" />
    <ClCompile Include="Source\j_LoopCNormalGuildBattleStateRoundGUILD_BATTLEUEAA_140009755.cpp" />
    <ClCompile Include="Source\j_LoopCNormalGuildBattleStateRoundProcessGUILD_BAT_140009291.cpp" />
    <ClCompile Include="Source\j_LoopCNormalGuildBattleStateRoundReturnStartPosGU_1400124D1.cpp" />
    <ClCompile Include="Source\j_LoopCNormalGuildBattleStateRoundStartGUILD_BATTL_140002F9A.cpp" />
    <ClCompile Include="Source\j_loop_cash_discount_eventCashItemRemoteStoreQEAAX_1400092FA.cpp" />
    <ClCompile Include="Source\j_Loop_Cash_EventCashItemRemoteStoreQEAAXXZ_140012E81.cpp" />
    <ClCompile Include="Source\j_Loop_Check_Total_SellingCashItemRemoteStoreQEAAX_140009D27.cpp" />
    <ClCompile Include="Source\j_Loop_ContEventCashItemRemoteStoreQEAAXXZ_14000B398.cpp" />
    <ClCompile Include="Source\j_Loop_TatalCashEventCashItemRemoteStoreQEAAXXZ_14000BA4B.cpp" />
    <ClCompile Include="Source\j_MakePageCPossibleBattleGuildListManagerGUILD_BAT_14000F9CF.cpp" />
    <ClCompile Include="Source\j_ManageAcceptORRefuseGuildBattleCGuildQEAAE_NZ_14001272E.cpp" />
    <ClCompile Include="Source\j_ManageProposeGuildBattleCGuildQEAAEKKKKZ_140010DD4.cpp" />
    <ClCompile Include="Source\j_max_elementPEAVCNormalGuildBattleGuildMemberGUIL_14000382D.cpp" />
    <ClCompile Include="Source\j_max_elementPEAVCNormalGuildBattleGuildMemberGUIL_140011DE7.cpp" />
    <ClCompile Include="Source\j_max_sizeallocatorVCGuildBattleRewardItemGUILD_BA_14000D544.cpp" />
    <ClCompile Include="Source\j_max_sizevectorVCGuildBattleRewardItemGUILD_BATTL_140007F1D.cpp" />
    <ClCompile Include="Source\j_ModifyMonsterAttFcCMonsterAttackIEAAMMZ_140003FDF.cpp" />
    <ClCompile Include="Source\j_MoveMapCNormalGuildBattleGuildGUILD_BATTLEQEAAXI_140004FC0.cpp" />
    <ClCompile Include="Source\j_MoveMemberCNormalGuildBattleGuildGUILD_BATTLEQEA_14000B717.cpp" />
    <ClCompile Include="Source\j_NetCloseCNormalGuildBattleGuildGUILD_BATTLEQEAA__140002FE5.cpp" />
    <ClCompile Include="Source\j_NetCloseCNormalGuildBattleGuildMemberGUILD_BATTL_1400138BD.cpp" />
    <ClCompile Include="Source\j_NextCGuildBattleReservedScheduleGUILD_BATTLEAEAA_14000CA63.cpp" />
    <ClCompile Include="Source\j_NextCGuildBattleStateListGUILD_BATTLEQEAAH_NZ_140011B3F.cpp" />
    <ClCompile Include="Source\j_NotifyBallPositionCNormalGuildBattleGUILD_BATTLE_14000C56D.cpp" />
    <ClCompile Include="Source\j_NotifyBattleResultCNormalGuildBattleGUILD_BATTLE_140003C6A.cpp" />
    <ClCompile Include="Source\j_NotifyBeforeStartCNormalGuildBattleGUILD_BATTLEQ_140005E8E.cpp" />
    <ClCompile Include="Source\j_NotifyCommitteeMemberPositionCNormalGuildBattleG_14000C2FC.cpp" />
    <ClCompile Include="Source\j_NotifyDestoryBallCNormalGuildBattleGUILD_BATTLEQ_140004827.cpp" />
    <ClCompile Include="Source\j_NotifyLeftMinuteBeforeStartCNormalGuildBattleGui_14000A9BB.cpp" />
    <ClCompile Include="Source\j_NotifyPassGravityStoneLimitTimeCNormalGuildBattl_14000E2CD.cpp" />
    <ClCompile Include="Source\j_OnlyOnceInitMonsterSFContDamageToleracneQEAAXPEA_140009A84.cpp" />
    <ClCompile Include="Source\j_On_HS_SCENE_BATTLE_END_WAIT_TIMECHolyStoneSystem_140006497.cpp" />
    <ClCompile Include="Source\j_On_HS_SCENE_BATTLE_TIMECHolyStoneSystemIEAAXXZ_14000E1DD.cpp" />
    <ClCompile Include="Source\j_On_HS_SCENE_KEEPER_ATTACKABLE_TIMECHolyStoneSyst_14000B523.cpp" />
    <ClCompile Include="Source\j_On_HS_SCENE_KEEPER_DEATTACKABLE_TIMECHolyStoneSy_14000A2D6.cpp" />
    <ClCompile Include="Source\j_OutDestGuildbattleCostCMainThreadQEAAXPEAU_DB_QR_140007B17.cpp" />
    <ClCompile Include="Source\j_OutSrcGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY_14000565F.cpp" />
    <ClCompile Include="Source\j_ProcCheckGetGravityStoneCNormalGuildBattleManage_14000B7CB.cpp" />
    <ClCompile Include="Source\j_ProcCheckGoalCNormalGuildBattleManagerGUILD_BATT_140013908.cpp" />
    <ClCompile Include="Source\j_ProcCheckTakeGravityStoneCNormalGuildBattleManag_14000C1E4.cpp" />
    <ClCompile Include="Source\j_ProcessCGuildBattleScheduleGUILD_BATTLEIEAAHXZ_140008F3F.cpp" />
    <ClCompile Include="Source\j_ProcessCGuildBattleStateListGUILD_BATTLEQEAAXPEA_140009B9C.cpp" />
    <ClCompile Include="Source\j_ProcessCNormalGuildBattleGUILD_BATTLEQEAAXXZ_140008422.cpp" />
    <ClCompile Include="Source\j_ProcJoinCNormalGuildBattleManagerGUILD_BATTLEIEA_140001AA0.cpp" />
    <ClCompile Include="Source\j_PushClearGuildBattleRankCGuildBattleControllerQE_140005637.cpp" />
    <ClCompile Include="Source\j_PushClearGuildBattleRankCGuildBattleRankManagerG_1400108FC.cpp" />
    <ClCompile Include="Source\j_PushCreateGuildBattleRankTableCGuildBattleContro_140002978.cpp" />
    <ClCompile Include="Source\j_PushCreateGuildBattleRankTableCGuildBattleRankMa_1400065EB.cpp" />
    <ClCompile Include="Source\j_PushDQSCGuildBattleReservedScheduleListManagerGU_14000740F.cpp" />
    <ClCompile Include="Source\j_PushDQSClearCGuildBattleReservedScheduleMapGroup_140009D4F.cpp" />
    <ClCompile Include="Source\j_PushDQSDataCNormalGuildBattleManagerGUILD_BATTLE_14000C5F9.cpp" />
    <ClCompile Include="Source\j_PushDQSDestGuildOutputGuildBattleCostCGuildQEAAX_14000141A.cpp" />
    <ClCompile Include="Source\j_PushDQSDrawRankCNormalGuildBattleGUILD_BATTLEIEA_140005470.cpp" />
    <ClCompile Include="Source\j_PushDQSInGuildBattleCostCGuildQEAAXXZ_140011D06.cpp" />
    <ClCompile Include="Source\j_PushDQSInGuildBattleRewardMoneyCGuildQEAAXXZ_1400124BD.cpp" />
    <ClCompile Include="Source\j_PushDQSPvpPointCNormalGuildBattleGuildMemberGUIL_140008995.cpp" />
    <ClCompile Include="Source\j_PushDQSSourceGuildOutputGuildBattleCostCGuildQEA_14000527C.cpp" />
    <ClCompile Include="Source\j_PushDQSWinLoseRankCNormalGuildBattleGUILD_BATTLE_140003017.cpp" />
    <ClCompile Include="Source\j_PushItemCObjectListQEAA_NPEAU_object_list_pointZ_140001C49.cpp" />
    <ClCompile Include="Source\j_PushItem_TRAP_PARAMQEAA_NPEAVCTrapKZ_1400036CF.cpp" />
    <ClCompile Include="Source\j_RCTopGoalPrediCateCNormalGuildBattleGuildGUILD_B_140001104.cpp" />
    <ClCompile Include="Source\j_RCTopKillPrediCateCNormalGuildBattleGuildGUILD_B_14000658C.cpp" />
    <ClCompile Include="Source\j_RegenBallCNormalGuildBattleFieldGUILD_BATTLEQEAA_14000F317.cpp" />
    <ClCompile Include="Source\j_RequestSubProcSetRaceBattleResultCRaceBuffByHoly_140012F80.cpp" />
    <ClCompile Include="Source\j_ReturnBindPosAllCNormalGuildBattleGuildGUILD_BAT_14000C75C.cpp" />
    <ClCompile Include="Source\j_ReturnBindPosCNormalGuildBattleGuildMemberGUILD__14000580D.cpp" />
    <ClCompile Include="Source\j_ReturnHQPosAllCNormalGuildBattleGuildGUILD_BATTL_140006FA5.cpp" />
    <ClCompile Include="Source\j_ReturnStartPosAllCNormalGuildBattleGuildGUILD_BA_140005EAC.cpp" />
    <ClCompile Include="Source\j_ReturnStartPosCNormalGuildBattleGuildMemberGUILD_140004511.cpp" />
    <ClCompile Include="Source\j_RewardGuildBattleMoneyCNormalGuildBattleGUILD_BA_14000FEB6.cpp" />
    <ClCompile Include="Source\j_RewardItemCNormalGuildBattleGuildGUILD_BATTLEQEA_14000E1F6.cpp" />
    <ClCompile Include="Source\j_RewardItemCNormalGuildBattleGUILD_BATTLEQEAAXXZ_140010FA5.cpp" />
    <ClCompile Include="Source\j_rollback_cashitemCMgrAvatorItemHistoryQEAAXPEAD__140013034.cpp" />
    <ClCompile Include="Source\j_SaveCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_14000F817.cpp" />
    <ClCompile Include="Source\j_SaveCNormalGuildBattleManagerGUILD_BATTLEQEAA_NK_14000BE38.cpp" />
    <ClCompile Include="Source\j_SaveINICGuildBattleControllerAEAA_NXZ_140009430.cpp" />
    <ClCompile Include="Source\j_SearchItemAddSpeedCEquipItemSFAgentIEAAMPEAU_db__14000D45E.cpp" />
    <ClCompile Include="Source\j_SectorDamageProcCAttackIEAAXHHHHHH_NZ_1400057C7.cpp" />
    <ClCompile Include="Source\j_SelectGuildBattleRankListCGuildBattleRankManager_140012314.cpp" />
    <ClCompile Include="Source\j_SelectGuildBattleRankListCRFWorldDatabaseQEAA_NE_1400037E2.cpp" />
    <ClCompile Include="Source\j_SelectGuildBattleRankRecordCRFWorldDatabaseQEAA__140012C15.cpp" />
    <ClCompile Include="Source\j_SelectGuildBattleRerservedListCRFWorldDatabaseQE_140003EEA.cpp" />
    <ClCompile Include="Source\j_SelectGuildBattleScheduleInfoIDCRFWorldDatabaseQ_14001019F.cpp" />
    <ClCompile Include="Source\j_SelectRowCountGuildBattleInfoCRFWorldDatabaseQEA_140004DF9.cpp" />
    <ClCompile Include="Source\j_SelectRowCountGuildBattleScheduleInfoCRFWorldDat_140009048.cpp" />
    <ClCompile Include="Source\j_Select_BattleResultLogLatestCRFWorldDatabaseQEAA_14000CD56.cpp" />
    <ClCompile Include="Source\j_Select_BattleTournamentInfoCRFWorldDatabaseQEAA__140001910.cpp" />
    <ClCompile Include="Source\j_Select_FailBattleCountCRFWorldDatabaseQEAAHEKAEA_1400040DE.cpp" />
    <ClCompile Include="Source\j_Select_GuildBattleRecordCRFWorldDatabaseQEAA_NKP_140012C51.cpp" />
    <ClCompile Include="Source\j_Select_LoseBattleCountCRFWorldDatabaseQEAAHEKAEA_1400103CF.cpp" />
    <ClCompile Include="Source\j_Select_WinBattleCountCRFWorldDatabaseQEAAHEKAEAK_140013601.cpp" />
    <ClCompile Include="Source\j_SellCashItemRemoteStoreQEAA_NGPEADZ_14000EDCC.cpp" />
    <ClCompile Include="Source\j_SetActiveSuccCAttackQEAAX_NZ_14000AC2C.cpp" />
    <ClCompile Include="Source\j_SetAttackPartCGameObjectUEAAXHZ_140012738.cpp" />
    <ClCompile Include="Source\j_SetBattleModeTimeCMonsterAIQEAAXKZ_140008012.cpp" />
    <ClCompile Include="Source\j_SetBattleStateCNormalGuildBattleGuildMemberGUILD_140005D49.cpp" />
    <ClCompile Include="Source\j_SetBattleTimeCNormalGuildBattleStateInBattleGUIL_1400122CE.cpp" />
    <ClCompile Include="Source\j_SetBattleTimeCNormalGuildBattleStateListGUILD_BA_1400066D6.cpp" />
    <ClCompile Include="Source\j_SetCCurrentGuildBattleInfoManagerGUILD_BATTLEQEA_1400029B9.cpp" />
    <ClCompile Include="Source\j_SetCGuildBattleScheduleGUILD_BATTLEQEAAEKKZ_1400038EB.cpp" />
    <ClCompile Include="Source\j_SetColorInxCNormalGuildBattleGuildGUILD_BATTLEQE_14000574F.cpp" />
    <ClCompile Include="Source\j_SetCombatStateCMonsterQEAAXEZ_14000C9BE.cpp" />
    <ClCompile Include="Source\j_SetCopmlteGuildBattleSuggestCGuildQEAAXXZ_140013D5E.cpp" />
    <ClCompile Include="Source\j_SetDamageAbleStateCHolyKeeperQEAAX_NZ_14000D9EA.cpp" />
    <ClCompile Include="Source\j_SetDelay_ATTACK_DELAY_CHECKERQEAAXKZ_14000E7B4.cpp" />
    <ClCompile Include="Source\j_SetGotoRegenStartCNormalGuildBattleGUILD_BATTLEQ_14000CB8F.cpp" />
    <ClCompile Include="Source\j_SetGotoRegenStateCNormalGuildBattleStateInBattle_140008C15.cpp" />
    <ClCompile Include="Source\j_SetGotoRegenStateCNormalGuildBattleStateListGUIL_14000E8C2.cpp" />
    <ClCompile Include="Source\j_SetGuildBattleMatterCGuildQEAAXKKKKZ_14000FC86.cpp" />
    <ClCompile Include="Source\j_SetGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAX_140009223.cpp" />
    <ClCompile Include="Source\j_SetItemCGuildBattleRewardItemGUILD_BATTLEIEAA_NP_140009C37.cpp" />
    <ClCompile Include="Source\j_SetLoadCBattleTournamentInfoQEAAX_NZ_14000187A.cpp" />
    <ClCompile Include="Source\j_SetNextDiscountEventTimeCashItemRemoteStoreQEAA__140006BA4.cpp" />
    <ClCompile Include="Source\j_SetNextEventCNormalGuildBattleManagerGUILD_BATTL_1400057D6.cpp" />
    <ClCompile Include="Source\j_SetNextEventTimeCashItemRemoteStoreQEAA_NEZ_140002D92.cpp" />
    <ClCompile Include="Source\j_SetNextEvnetCGuildBattleScheduleManagerGUILD_BAT_140005DE4.cpp" />
    <ClCompile Include="Source\j_SetNextStateCGuildBattleStateListGUILD_BATTLEMEA_140008B2A.cpp" />
    <ClCompile Include="Source\j_SetNextStateCNormalGuildBattleStateListGUILD_BAT_14000E9FD.cpp" />
    <ClCompile Include="Source\j_SetNextStateCNormalGuildBattleStateRoundListGUIL_1400041F1.cpp" />
    <ClCompile Include="Source\j_SetOrderViewAttackStateCPvpOrderViewQEAAXXZ_140002CC5.cpp" />
    <ClCompile Include="Source\j_SetOrderViewDamagedStateCPvpOrderViewQEAAXXZ_14000ECE1.cpp" />
    <ClCompile Include="Source\j_SetPortalInxCNormalGuildBattleFieldGUILD_BATTLEA_14000E87C.cpp" />
    <ClCompile Include="Source\j_SetProcStateCGuildBattleScheduleGUILD_BATTLEQEAA_14000A308.cpp" />
    <ClCompile Include="Source\j_SetReadyCGuildBattleStateListGUILD_BATTLEQEAAXXZ_140001780.cpp" />
    <ClCompile Include="Source\j_SetReadyStateCNormalGuildBattleGUILD_BATTLEQEAAX_140001118.cpp" />
    <ClCompile Include="Source\j_SetReadyStateCNormalGuildBattleManagerGUILD_BATT_14000CBC6.cpp" />
    <ClCompile Include="Source\j_SetReStartFlagCNormalGuildBattleGuildGUILD_BATTL_140001893.cpp" />
    <ClCompile Include="Source\j_SetReStartFlagCNormalGuildBattleGuildMemberGUILD_140001AEB.cpp" />
    <ClCompile Include="Source\j_SetSFDamageToleracne_VariationMonsterSFContDamag_140003620.cpp" />
    <ClCompile Include="Source\j_SetStateListCGuildBattleScheduleGUILD_BATTLEQEAA_14000B6BD.cpp" />
    <ClCompile Include="Source\j_SetStaticMemberCAttackSAXPEAVCRecordDataZ_1400035B2.cpp" />
    <ClCompile Include="Source\j_SetWaitCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1400049B7.cpp" />
    <ClCompile Include="Source\j_SetWinnerInfoCBattleTournamentInfoQEAA_NKPEADEZ_140011AE0.cpp" />
    <ClCompile Include="Source\j_Set_CashEvent_StatusCashItemRemoteStoreQEAAXEEZ_14000DE31.cpp" />
    <ClCompile Include="Source\j_set_cde_statusCashItemRemoteStoreQEAAXEZ_14000B767.cpp" />
    <ClCompile Include="Source\j_Set_Conditional_Evnet_StatusCashItemRemoteStoreQ_14000241E.cpp" />
    <ClCompile Include="Source\j_Set_DB_LimitedSale_EventCashItemRemoteStoreQEAAX_140011469.cpp" />
    <ClCompile Include="Source\j_Set_FROMDB_LimitedSale_EventCashItemRemoteStoreQ_14000E30E.cpp" />
    <ClCompile Include="Source\j_Set_LimitedSale_countCashItemRemoteStoreQEAAXEKZ_140002F9F.cpp" />
    <ClCompile Include="Source\j_Set_LimitedSale_DCKCashItemRemoteStoreQEAAXEEZ_140004C19.cpp" />
    <ClCompile Include="Source\j_Set_LimitedSale_EventCashItemRemoteStoreQEAAXXZ_140013B7E.cpp" />
    <ClCompile Include="Source\j_Set_LimitedSale_Event_IniCashItemRemoteStoreQEAA_140012DCD.cpp" />
    <ClCompile Include="Source\j_sizevectorVCGuildBattleRewardItemGUILD_BATTLEVal_14000AAD8.cpp" />
    <ClCompile Include="Source\j_size_attack_count_result_zoclQEAAHXZ_140010F19.cpp" />
    <ClCompile Include="Source\j_size_attack_force_result_zoclQEAAHXZ_140013D63.cpp" />
    <ClCompile Include="Source\j_size_attack_gen_result_zoclQEAAHXZ_140011FB3.cpp" />
    <ClCompile Include="Source\j_size_attack_keeper_inform_zoclQEAAHXZ_14001157C.cpp" />
    <ClCompile Include="Source\j_size_attack_selfdestruction_result_zoclQEAAHXZ_14000FA24.cpp" />
    <ClCompile Include="Source\j_size_attack_siege_result_zoclQEAAHXZ_140006875.cpp" />
    <ClCompile Include="Source\j_size_attack_trap_inform_zoclQEAAHXZ_14000F13C.cpp" />
    <ClCompile Include="Source\j_size_attack_unit_result_zoclQEAAHXZ_1400026B7.cpp" />
    <ClCompile Include="Source\j_size_guild_battle_get_gravity_stone_result_zoclQ_140006E10.cpp" />
    <ClCompile Include="Source\j_size_guild_battle_goal_result_zoclQEAAHXZ_14000AF2E.cpp" />
    <ClCompile Include="Source\j_size_guild_battle_rank_list_result_zoclQEAAHXZ_14000A0D3.cpp" />
    <ClCompile Include="Source\j_size_guild_battle_reserved_schedule_result_zoclQ_140001055.cpp" />
    <ClCompile Include="Source\j_size_guild_battle_suggest_request_result_zoclQEA_140010EE7.cpp" />
    <ClCompile Include="Source\j_size_notify_not_use_premium_cashitem_zoclQEAAHXZ_14001203F.cpp" />
    <ClCompile Include="Source\j_size_param_cashitem_dblogQEAAHXZ_140001AB9.cpp" />
    <ClCompile Include="Source\j_size_personal_automine_attacked_zoclQEAAHXZ_14000EAFC.cpp" />
    <ClCompile Include="Source\j_size_possible_battle_guild_list_result_zoclQEAAH_1400045A2.cpp" />
    <ClCompile Include="Source\j_size_qry_case_addguildbattlescheduleQEAAHXZ_1400017B7.cpp" />
    <ClCompile Include="Source\j_size_qry_case_dest_guild_out_guildbattlecostQEAA_14000E700.cpp" />
    <ClCompile Include="Source\j_size_qry_case_in_guildbattlecostQEAAHXZ_140012B16.cpp" />
    <ClCompile Include="Source\j_size_qry_case_in_guildbattlerewardmoneyQEAAHXZ_140013E49.cpp" />
    <ClCompile Include="Source\j_size_qry_case_loadguildbattlerankQEAAHXZ_140002B30.cpp" />
    <ClCompile Include="Source\j_size_qry_case_load_guildbattle_totalrecordQEAAHX_140007FEA.cpp" />
    <ClCompile Include="Source\j_size_qry_case_src_guild_out_guildbattlecostQEAAH_14000D837.cpp" />
    <ClCompile Include="Source\j_size_qry_case_updateclearguildbattleDayInfoQEAAH_1400117ED.cpp" />
    <ClCompile Include="Source\j_size_qry_case_updatedrawguildbattlerankQEAAHXZ_1400060A5.cpp" />
    <ClCompile Include="Source\j_size_qry_case_updatewinloseguildbattlerankQEAAHX_14000D5E9.cpp" />
    <ClCompile Include="Source\j_SrcGuildIsAvailableBattleRequestStateCGuildQEAAE_1400053B2.cpp" />
    <ClCompile Include="Source\j_start_casheventCashItemRemoteStoreQEAA_NHHHHEZ_1400016F4.cpp" />
    <ClCompile Include="Source\j_start_cdeCashItemRemoteStoreQEAA_NHHHHZ_140004F7A.cpp" />
    <ClCompile Include="Source\j_start_coneventCashItemRemoteStoreQEAA_NHHEZ_140004F61.cpp" />
    <ClCompile Include="Source\j_StockOldInfoCNormalGuildBattleGuildMemberGUILD_B_14000909D.cpp" />
    <ClCompile Include="Source\j_TakeGravityStoneCNormalGuildBattleGUILD_BATTLEQE_1400067DF.cpp" />
    <ClCompile Include="Source\j_unchecked_copyPEAVCGuildBattleRewardItemGUILD_BA_14000BF28.cpp" />
    <ClCompile Include="Source\j_unchecked_uninitialized_copyPEAVCGuildBattleRewa_140010965.cpp" />
    <ClCompile Include="Source\j_unchecked_uninitialized_fill_nPEAVCGuildBattleRe_14001087F.cpp" />
    <ClCompile Include="Source\j_UpdateCGuildBattleRankManagerGUILD_BATTLEQEAA_NE_14000F8BC.cpp" />
    <ClCompile Include="Source\j_UpdateClearGuildBattleDayInfoCNormalGuildBattleM_1400047B9.cpp" />
    <ClCompile Include="Source\j_UpdateClearGuildBattleInfoCRFWorldDatabaseQEAA_N_140004192.cpp" />
    <ClCompile Include="Source\j_UpdateClearGuildBattleRankCRFWorldDatabaseQEAA_N_1400108C0.cpp" />
    <ClCompile Include="Source\j_UpdateClearGuildBattleScheduleDayInfoCGuildBattl_1400013ED.cpp" />
    <ClCompile Include="Source\j_UpdateClearGuildBattleScheduleInfoCRFWorldDataba_1400014C4.cpp" />
    <ClCompile Include="Source\j_UpdateClearGuildBattleScheduleInfoCRFWorldDataba_14001141E.cpp" />
    <ClCompile Include="Source\j_UpdateClearRerservedDayInfoCGuildBattleControlle_1400042A5.cpp" />
    <ClCompile Include="Source\j_UpdateDayChangedWorkCGuildBattleScheduleManagerG_14000998F.cpp" />
    <ClCompile Include="Source\j_UpdateDrawCGuildBattleControllerQEAA_NEKEKZ_140008F67.cpp" />
    <ClCompile Include="Source\j_UpdateDrawCGuildBattleRankManagerGUILD_BATTLEQEA_14000CABD.cpp" />
    <ClCompile Include="Source\j_UpdateDrawGuildBattleResultCRFWorldDatabaseQEAA__140003594.cpp" />
    <ClCompile Include="Source\j_UpdateGoalCntCCurrentGuildBattleInfoManagerGUILD_140010BA9.cpp" />
    <ClCompile Include="Source\j_UpdateGuildBattleDrawRankInfoCMainThreadQEAAXPEA_14000FA88.cpp" />
    <ClCompile Include="Source\j_UpdateGuildBattleInfoCRFWorldDatabaseQEAA_NKKKKE_140001F1E.cpp" />
    <ClCompile Include="Source\j_UpdateGuildBattleScheduleInfoCRFWorldDatabaseQEA_14000E46C.cpp" />
    <ClCompile Include="Source\j_UpdateGuildBattleWinCntCGuildQEAAXKKKZ_14000ED40.cpp" />
    <ClCompile Include="Source\j_UpdateGuildBattleWinLoseRankInfoCMainThreadQEAAX_14000B578.cpp" />
    <ClCompile Include="Source\j_UpdateGuildListCPossibleBattleGuildListManagerGU_1400016EF.cpp" />
    <ClCompile Include="Source\j_UpdateLoadGuildBattleRankCMainThreadQEAAXPEAU_DB_14000EFE8.cpp" />
    <ClCompile Include="Source\j_UpdateLoseGuildBattleResultCRFWorldDatabaseQEAA__140008AF8.cpp" />
    <ClCompile Include="Source\j_UpdateMonsterSFContDamageToleracneQEAAXXZ_140009827.cpp" />
    <ClCompile Include="Source\j_UpdatePossibleBattleGuildListCGuildBattleControl_14000D8B4.cpp" />
    <ClCompile Include="Source\j_UpdateRankCGuildBattleControllerQEAA_NEPEAEZ_140006FC3.cpp" />
    <ClCompile Include="Source\j_UpdateReservedGuildBattleScheduleCGuildBattleCon_14000D4EF.cpp" />
    <ClCompile Include="Source\j_UpdateReservedGuildBattleScheduleCMainThreadQEAA_14000994E.cpp" />
    <ClCompile Include="Source\j_UpdateReservedSheduleCGuildBattleReservedSchedul_140005182.cpp" />
    <ClCompile Include="Source\j_UpdateScoreCCurrentGuildBattleInfoManagerGUILD_B_140002022.cpp" />
    <ClCompile Include="Source\j_UpdateScoreCNormalGuildBattleGuildGUILD_BATTLEIE_14000E610.cpp" />
    <ClCompile Include="Source\j_UpdateTodayScheduleCGuildBattleReservedScheduleL_14000D274.cpp" />
    <ClCompile Include="Source\j_UpdateTomorrowCompleteCGuildBattleReservedSchedu_1400074E6.cpp" />
    <ClCompile Include="Source\j_UpdateTomorrowScheduleCGuildBattleReservedSchedu_140001E06.cpp" />
    <ClCompile Include="Source\j_UpdateUseFieldCGuildBattleReservedScheduleGUILD__140004020.cpp" />
    <ClCompile Include="Source\j_UpdateUseFlagCGuildBattleReservedScheduleGUILD_B_140011C84.cpp" />
    <ClCompile Include="Source\j_UpdateUseFlagCGuildBattleReservedScheduleMapGrou_14000585D.cpp" />
    <ClCompile Include="Source\j_UpdateUseFlagCGuildBattleScheduleManagerGUILD_BA_140010A2D.cpp" />
    <ClCompile Include="Source\j_UpdateWinGuildBattleResultCRFWorldDatabaseQEAA_N_14000321F.cpp" />
    <ClCompile Include="Source\j_UpdateWinLoseCGuildBattleControllerQEAA_NEKEKZ_140003C0B.cpp" />
    <ClCompile Include="Source\j_UpdateWinLoseCGuildBattleRankManagerGUILD_BATTLE_14000142E.cpp" />
    <ClCompile Include="Source\j_Update_BattleResultLogBattleResultAndPvpPointCRF_140011DEC.cpp" />
    <ClCompile Include="Source\j_Update_CristalBattleCharInfoCRFWorldDatabaseQEAA_140007D06.cpp" />
    <ClCompile Include="Source\j_update_cristalbattle_dateCRFWorldDatabaseQEAA_NK_140001046.cpp" />
    <ClCompile Include="Source\j_Update_IncreaseWeeklyGuildGuildBattlePvpPointSum_140004868.cpp" />
    <ClCompile Include="Source\j_update_iniCashItemRemoteStoreQEAAXPEAU_cash_disc_140008184.cpp" />
    <ClCompile Include="Source\j_Update_INICashItemRemoteStoreQEAAXPEAU_cash_even_1400026DA.cpp" />
    <ClCompile Include="Source\j_WriteLogPer10Min_CombatCHolyStoneSystemIEAAXXZ_14000515F.cpp" />
    <ClCompile Include="Source\j__AllocateVCGuildBattleRewardItemGUILD_BATTLEstdY_14000FF9C.cpp" />
    <ClCompile Include="Source\j__Assign_nvectorVCGuildBattleRewardItemGUILD_BATT_14000C199.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_item_calc_price_discountCa_140002518.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_item_calc_price_limitsaleC_14000AE48.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_item_calc_price_one_n_oneC_14000FA74.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_item_setbuydblogCashItemRe_14000C64E.cpp" />
    <ClCompile Include="Source\j__BuyvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1400111EE.cpp" />
    <ClCompile Include="Source\j__CalcForceAttPntCAttackIEAAH_NZ_1400030C1.cpp" />
    <ClCompile Include="Source\j__CalcGenAttPntCAttackIEAAH_NZ_1400132FA.cpp" />
    <ClCompile Include="Source\j__check_buyitemCashItemRemoteStoreAEAAAW4CS_RCODE_1400056FA.cpp" />
    <ClCompile Include="Source\j__complete_tsk_cashitem_buy_dblogCashDbWorkerIEAA_1400022DE.cpp" />
    <ClCompile Include="Source\j__ConstructVCGuildBattleRewardItemGUILD_BATTLEV12_14000670D.cpp" />
    <ClCompile Include="Source\j__Copy_backward_optPEAVCGuildBattleRewardItemGUIL_14000188E.cpp" />
    <ClCompile Include="Source\j__Copy_optPEAVCGuildBattleRewardItemGUILD_BATTLEP_14000C6AD.cpp" />
    <ClCompile Include="Source\j__db_Load_BattleTournamentInfoCMainThreadAEAAXXZ_140003EAE.cpp" />
    <ClCompile Include="Source\j__db_load_losebattlecountCMainThreadAEAAEKPEAU_AV_14000ECFA.cpp" />
    <ClCompile Include="Source\j__delay_check_ATTACK_DELAY_CHECKERQEAA_NEGEZ_1400055CE.cpp" />
    <ClCompile Include="Source\j__DestroyVCGuildBattleRewardItemGUILD_BATTLEstdYA_140008FA3.cpp" />
    <ClCompile Include="Source\j__DestroyvectorVCGuildBattleRewardItemGUILD_BATTL_14000AF6F.cpp" />
    <ClCompile Include="Source\j__Destroy_rangeVCGuildBattleRewardItemGUILD_BATTL_140004D27.cpp" />
    <ClCompile Include="Source\j__Destroy_rangeVCGuildBattleRewardItemGUILD_BATTL_1400116D0.cpp" />
    <ClCompile Include="Source\j__ECNormalGuildBattleFieldGUILD_BATTLEQEAAPEAXIZ_140007608.cpp" />
    <ClCompile Include="Source\j__ECNormalGuildBattleStateListGUILD_BATTLEQEAAPEA_140003846.cpp" />
    <ClCompile Include="Source\j__ECReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_14000C0E5.cpp" />
    <ClCompile Include="Source\j__FillPEAVCGuildBattleRewardItemGUILD_BATTLEV12st_14000904D.cpp" />
    <ClCompile Include="Source\j__GCCurrentGuildBattleInfoManagerGUILD_BATTLEIEAA_140011207.cpp" />
    <ClCompile Include="Source\j__GCGuildBattleControllerIEAAPEAXIZ_140010406.cpp" />
    <ClCompile Include="Source\j__GCGuildBattleLoggerGUILD_BATTLEIEAAPEAXIZ_14000BFF5.cpp" />
    <ClCompile Include="Source\j__GCGuildBattleRankManagerGUILD_BATTLEIEAAPEAXIZ_140002BC6.cpp" />
    <ClCompile Include="Source\j__GCGuildBattleReservedScheduleGUILD_BATTLEQEAAPE_140012DC8.cpp" />
    <ClCompile Include="Source\j__GCGuildBattleReservedScheduleListManagerGUILD_B_140009D5E.cpp" />
    <ClCompile Include="Source\j__GCGuildBattleScheduleGUILD_BATTLEQEAAPEAXIZ_140009FD4.cpp" />
    <ClCompile Include="Source\j__GCGuildBattleScheduleManagerGUILD_BATTLEIEAAPEA_140007F8B.cpp" />
    <ClCompile Include="Source\j__GCGuildBattleSchedulePoolGUILD_BATTLEIEAAPEAXIZ_14000EBF1.cpp" />
    <ClCompile Include="Source\j__GCGuildBattleSchedulerGUILD_BATTLEIEAAPEAXIZ_14000BA7D.cpp" />
    <ClCompile Include="Source\j__GCNormalGuildBattleFieldListGUILD_BATTLEIEAAPEA_1400116AD.cpp" />
    <ClCompile Include="Source\j__GCNormalGuildBattleGUILD_BATTLEQEAAPEAXIZ_140009CDC.cpp" />
    <ClCompile Include="Source\j__GCNormalGuildBattleManagerGUILD_BATTLEIEAAPEAXI_140009764.cpp" />
    <ClCompile Include="Source\j__GCNormalGuildBattleStateListPoolGUILD_BATTLEIEA_1400123F0.cpp" />
    <ClCompile Include="Source\j__GCPossibleBattleGuildListManagerGUILD_BATTLEIEA_14001128E.cpp" />
    <ClCompile Include="Source\j__GCRFCashItemDatabaseUEAAPEAXIZ_0_140010613.cpp" />
    <ClCompile Include="Source\j__GCRFCashItemDatabaseUEAAPEAXIZ_14000A637.cpp" />
    <ClCompile Include="Source\j__InitLoggersCashItemRemoteStoreAEAA_NXZ_140013FFC.cpp" />
    <ClCompile Include="Source\j__Insert_nvectorVCGuildBattleRewardItemGUILD_BATT_14000CD4C.cpp" />
    <ClCompile Include="Source\j__Iter_randomPEAVCGuildBattleRewardItemGUILD_BATT_140013921.cpp" />
    <ClCompile Include="Source\j__MakeLinkTableCashItemRemoteStoreAEAA_NPEADHZ_14001249F.cpp" />
    <ClCompile Include="Source\j__Max_elementPEAVCNormalGuildBattleGuildMemberGUI_140005B32.cpp" />
    <ClCompile Include="Source\j__Max_elementPEAVCNormalGuildBattleGuildMemberGUI_1400114E1.cpp" />
    <ClCompile Include="Source\j__Move_backward_optPEAVCGuildBattleRewardItemGUIL_140012BB1.cpp" />
    <ClCompile Include="Source\j__Move_catPEAVCGuildBattleRewardItemGUILD_BATTLEs_14001233C.cpp" />
    <ClCompile Include="Source\j__Ptr_catPEAVCGuildBattleRewardItemGUILD_BATTLEPE_140009435.cpp" />
    <ClCompile Include="Source\j__PushItemCutLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LT_14000C135.cpp" />
    <ClCompile Include="Source\j__PushItemMoveLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_L_14000870B.cpp" />
    <ClCompile Include="Source\j__ReadGoodsCashItemRemoteStoreAEAA_NXZ_140004606.cpp" />
    <ClCompile Include="Source\j__TidyvectorVCGuildBattleRewardItemGUILD_BATTLEVa_1400041AB.cpp" />
    <ClCompile Include="Source\j__UfillvectorVCGuildBattleRewardItemGUILD_BATTLEV_140006C3A.cpp" />
    <ClCompile Include="Source\j__UmovePEAVCGuildBattleRewardItemGUILD_BATTLEvect_14000B7D0.cpp" />
    <ClCompile Include="Source\j__Unchecked_move_backwardPEAVCGuildBattleRewardIt_14000239C.cpp" />
    <ClCompile Include="Source\j__Unchecked_uninitialized_movePEAVCGuildBattleRew_14000F358.cpp" />
    <ClCompile Include="Source\j__Uninit_copyPEAVCGuildBattleRewardItemGUILD_BATT_140011E82.cpp" />
    <ClCompile Include="Source\j__Uninit_fill_nPEAVCGuildBattleRewardItemGUILD_BA_14000B6C2.cpp" />
    <ClCompile Include="Source\j__Uninit_movePEAVCGuildBattleRewardItemGUILD_BATT_140012D1E.cpp" />
    <ClCompile Include="Source\j__XlenvectorVCGuildBattleRewardItemGUILD_BATTLEVa_1400131A1.cpp" />
    <ClCompile Include="Source\j___CheckGoodsCashItemRemoteStoreAEAA_NAEAVCRecord_14000F484.cpp" />
    <ClCompile Include="Source\KillCNormalGuildBattleGuildGUILD_BATTLEQEAAHPEAVCN_1403E1560.cpp" />
    <ClCompile Include="Source\KillCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_1403E5C30.cpp" />
    <ClCompile Include="Source\KillCNormalGuildBattleManagerGUILD_BATTLEQEAAHKKKZ_1403D4B20.cpp" />
    <ClCompile Include="Source\LeaveGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAX_1403E1460.cpp" />
    <ClCompile Include="Source\LimitedSale_check_countCashItemRemoteStoreQEAA_NEK_1402FDD90.cpp" />
    <ClCompile Include="Source\LoadBuyCashModeCashItemRemoteStoreAEAA_NXZ_1402F4D90.cpp" />
    <ClCompile Include="Source\LoadCGuildBattleControllerQEAA_NXZ_1403D5950.cpp" />
    <ClCompile Include="Source\LoadCGuildBattleRankManagerGUILD_BATTLEIEAA_NEZ_1403CB820.cpp" />
    <ClCompile Include="Source\LoadCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_1403CA610.cpp" />
    <ClCompile Include="Source\LoadCGuildBattleReservedScheduleGUILD_BATTLEQEAA_N_1403DAEA0.cpp" />
    <ClCompile Include="Source\LoadCGuildBattleReservedScheduleListManagerGUILD_B_1403CD540.cpp" />
    <ClCompile Include="Source\LoadCGuildBattleReservedScheduleMapGroupGUILD_BATT_1403DBDC0.cpp" />
    <ClCompile Include="Source\LoadCGuildBattleScheduleGUILD_BATTLEQEAA_N_NKE_JGZ_1403D9F60.cpp" />
    <ClCompile Include="Source\LoadCGuildBattleScheduleManagerGUILD_BATTLEQEAA_NH_1403DCDD0.cpp" />
    <ClCompile Include="Source\LoadCNormalGuildBattleManagerGUILD_BATTLEIEAA_N_NI_1403D4D90.cpp" />
    <ClCompile Include="Source\LoadCNormalGuildBattleManagerGUILD_BATTLEQEAA_NHIH_1403D38F0.cpp" />
    <ClCompile Include="Source\LoadCPossibleBattleGuildListManagerGUILD_BATTLEQEA_1403C99F0.cpp" />
    <ClCompile Include="Source\LoadCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CCF20.cpp" />
    <ClCompile Include="Source\LoadCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CC540.cpp" />
    <ClCompile Include="Source\LoadDBGuildBattleInfoCNormalGuildBattleManagerGUIL_1403D4F40.cpp" />
    <ClCompile Include="Source\LoadDummysCNormalGuildBattleFieldGUILD_BATTLEAEAA__1403ED930.cpp" />
    <ClCompile Include="Source\LoadGuildBattleInfoCRFWorldDatabaseQEAA_NKKPEAU_wo_1404A25D0.cpp" />
    <ClCompile Include="Source\LoadGuildBattleScheduleInfoCRFWorldDatabaseQEAAEII_1404A15E0.cpp" />
    <ClCompile Include="Source\LoadINICGuildBattleControllerAEAA_NAEAIAEAH111Z_1403D7BB0.cpp" />
    <ClCompile Include="Source\LoadNationalPriceCashItemRemoteStoreAEAA_NAEAVCRec_1402F4C90.cpp" />
    <ClCompile Include="Source\LoadTodayScheduleCGuildBattleReservedScheduleListM_1403CDD00.cpp" />
    <ClCompile Include="Source\LoadTomorrowScheduleCGuildBattleReservedScheduleLi_1403CDDA0.cpp" />
    <ClCompile Include="Source\load_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F5E30.cpp" />
    <ClCompile Include="Source\Load_Cash_EventCashItemRemoteStoreQEAAXXZ_1402F7F10.cpp" />
    <ClCompile Include="Source\load_cde_iniCashItemRemoteStoreQEAAXPEAU_cash_disc_1402F60C0.cpp" />
    <ClCompile Include="Source\Load_Conditional_EventCashItemRemoteStoreQEAAXXZ_1402FC330.cpp" />
    <ClCompile Include="Source\load_con_event_iniCashItemRemoteStoreQEAAXPEAU_con_1402FBC10.cpp" />
    <ClCompile Include="Source\Load_Event_INICashItemRemoteStoreQEAAXPEAU_cash_ev_1402F99B0.cpp" />
    <ClCompile Include="Source\Load_LimitedSale_Event_INICashItemRemoteStoreQEAAX_1402FD4A0.cpp" />
    <ClCompile Include="Source\LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEADZZ_1403CEAC0.cpp" />
    <ClCompile Include="Source\LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZZ_1403CEB50.cpp" />
    <ClCompile Include="Source\LogCGuildBattleStateGUILD_BATTLEIEAAXPEADZ_1403DEE60.cpp" />
    <ClCompile Include="Source\LogCGuildBattleStateListGUILD_BATTLEIEAAXPEADZ_1403DF340.cpp" />
    <ClCompile Include="Source\LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEADZZ_1403CEE40.cpp" />
    <ClCompile Include="Source\LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZ_1403CEED0.cpp" />
    <ClCompile Include="Source\LogCNormalGuildBattleStateGUILD_BATTLEIEAAXPEAVCNo_1403F04D0.cpp" />
    <ClCompile Include="Source\LogCNormalGuildBattleStateRoundGUILD_BATTLEIEAAXPE_1403F1170.cpp" />
    <ClCompile Include="Source\log_about_cash_eventCashItemRemoteStoreQEAAXPEADPE_1402F73A0.cpp" />
    <ClCompile Include="Source\LoopCGuildBattleControllerQEAAXXZ_1403D6760.cpp" />
    <ClCompile Include="Source\LoopCGuildBattleReservedScheduleGUILD_BATTLEQEAA_N_1403DAD80.cpp" />
    <ClCompile Include="Source\LoopCGuildBattleReservedScheduleMapGroupGUILD_BATT_1403DC0D0.cpp" />
    <ClCompile Include="Source\LoopCGuildBattleScheduleManagerGUILD_BATTLEQEAAXXZ_1403DCD30.cpp" />
    <ClCompile Include="Source\LoopCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuildBa_14007F780.cpp" />
    <ClCompile Include="Source\LoopCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_1403D4110.cpp" />
    <ClCompile Include="Source\LoopCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAVCN_14007FBE0.cpp" />
    <ClCompile Include="Source\LoopCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAVCG_1403F03F0.cpp" />
    <ClCompile Include="Source\LoopCNormalGuildBattleStateInBattleGUILD_BATTLEUEA_140080070.cpp" />
    <ClCompile Include="Source\LoopCNormalGuildBattleStateRoundGUILD_BATTLEMEAAHP_1403F31A0.cpp" />
    <ClCompile Include="Source\LoopCNormalGuildBattleStateRoundGUILD_BATTLEUEAAHP_1403F1090.cpp" />
    <ClCompile Include="Source\LoopCNormalGuildBattleStateRoundProcessGUILD_BATTL_1403F1A00.cpp" />
    <ClCompile Include="Source\LoopCNormalGuildBattleStateRoundReturnStartPosGUIL_1403F1DA0.cpp" />
    <ClCompile Include="Source\LoopCNormalGuildBattleStateRoundStartGUILD_BATTLEM_1403F15E0.cpp" />
    <ClCompile Include="Source\loop_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F6920.cpp" />
    <ClCompile Include="Source\Loop_Cash_EventCashItemRemoteStoreQEAAXXZ_1402F7E80.cpp" />
    <ClCompile Include="Source\Loop_Check_Total_SellingCashItemRemoteStoreQEAAXXZ_1402FB620.cpp" />
    <ClCompile Include="Source\Loop_ContEventCashItemRemoteStoreQEAAXXZ_1402FB5D0.cpp" />
    <ClCompile Include="Source\Loop_TatalCashEventCashItemRemoteStoreQEAAXXZ_1402FB560.cpp" />
    <ClCompile Include="Source\MakePageCPossibleBattleGuildListManagerGUILD_BATTL_1403C9D70.cpp" />
    <ClCompile Include="Source\ManageAcceptORRefuseGuildBattleCGuildQEAAE_NZ_140259EF0.cpp" />
    <ClCompile Include="Source\ManageProposeGuildBattleCGuildQEAAEKKKKZ_1402593D0.cpp" />
    <ClCompile Include="Source\max_elementPEAVCNormalGuildBattleGuildMemberGUILD__1403EB430.cpp" />
    <ClCompile Include="Source\max_elementPEAVCNormalGuildBattleGuildMemberGUILD__1403EB4A0.cpp" />
    <ClCompile Include="Source\max_sizeallocatorVCGuildBattleRewardItemGUILD_BATT_1403D22F0.cpp" />
    <ClCompile Include="Source\max_sizevectorVCGuildBattleRewardItemGUILD_BATTLEV_1403D1670.cpp" />
    <ClCompile Include="Source\ModifyMonsterAttFcCMonsterAttackIEAAMMZ_1401615B0.cpp" />
    <ClCompile Include="Source\MoveMapCNormalGuildBattleGuildGUILD_BATTLEQEAAXIPE_1403E0F60.cpp" />
    <ClCompile Include="Source\MoveMemberCNormalGuildBattleGuildGUILD_BATTLEQEAA__1403E1140.cpp" />
    <ClCompile Include="Source\NetCloseCNormalGuildBattleGuildGUILD_BATTLEQEAA_N__1403E13B0.cpp" />
    <ClCompile Include="Source\NetCloseCNormalGuildBattleGuildMemberGUILD_BATTLEQ_1403DFAE0.cpp" />
    <ClCompile Include="Source\NextCGuildBattleReservedScheduleGUILD_BATTLEAEAA_N_1403DB7F0.cpp" />
    <ClCompile Include="Source\NextCGuildBattleStateListGUILD_BATTLEQEAAH_NZ_1403DF220.cpp" />
    <ClCompile Include="Source\NotifyBallPositionCNormalGuildBattleGUILD_BATTLEQE_1403E53E0.cpp" />
    <ClCompile Include="Source\NotifyBattleResultCNormalGuildBattleGUILD_BATTLEQE_1403E6A90.cpp" />
    <ClCompile Include="Source\NotifyBeforeStartCNormalGuildBattleGUILD_BATTLEQEA_1403E50E0.cpp" />
    <ClCompile Include="Source\NotifyCommitteeMemberPositionCNormalGuildBattleGUI_1403E55B0.cpp" />
    <ClCompile Include="Source\NotifyDestoryBallCNormalGuildBattleGUILD_BATTLEQEA_1403E5510.cpp" />
    <ClCompile Include="Source\NotifyLeftMinuteBeforeStartCNormalGuildBattleGuild_1403E2050.cpp" />
    <ClCompile Include="Source\NotifyPassGravityStoneLimitTimeCNormalGuildBattleG_1403E5700.cpp" />
    <ClCompile Include="Source\OnlyOnceInitMonsterSFContDamageToleracneQEAAXPEAVC_140157ED0.cpp" />
    <ClCompile Include="Source\OnToolHitTestCWndUEBA_JVCPointPEAUtagTOOLINFOAZ_0_1404DBC28.cpp" />
    <ClCompile Include="Source\On_HS_SCENE_BATTLE_END_WAIT_TIMECHolyStoneSystemIE_14027C3E0.cpp" />
    <ClCompile Include="Source\On_HS_SCENE_BATTLE_TIMECHolyStoneSystemIEAAXXZ_14027C120.cpp" />
    <ClCompile Include="Source\On_HS_SCENE_KEEPER_ATTACKABLE_TIMECHolyStoneSystem_14027C540.cpp" />
    <ClCompile Include="Source\On_HS_SCENE_KEEPER_DEATTACKABLE_TIMECHolyStoneSyst_14027C610.cpp" />
    <ClCompile Include="Source\OutDestGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY__1401F4970.cpp" />
    <ClCompile Include="Source\OutSrcGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY_S_1401F47E0.cpp" />
    <ClCompile Include="Source\ProcCheckGetGravityStoneCNormalGuildBattleManagerG_1403D52C0.cpp" />
    <ClCompile Include="Source\ProcCheckGoalCNormalGuildBattleManagerGUILD_BATTLE_1403D5360.cpp" />
    <ClCompile Include="Source\ProcCheckTakeGravityStoneCNormalGuildBattleManager_1403D5220.cpp" />
    <ClCompile Include="Source\ProcessCGuildBattleScheduleGUILD_BATTLEIEAAHXZ_1403DA3B0.cpp" />
    <ClCompile Include="Source\ProcessCGuildBattleStateListGUILD_BATTLEQEAAXPEAVC_1403DF090.cpp" />
    <ClCompile Include="Source\ProcessCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403D92F0.cpp" />
    <ClCompile Include="Source\ProcJoinCNormalGuildBattleManagerGUILD_BATTLEIEAAH_1403D5190.cpp" />
    <ClCompile Include="Source\PushClearGuildBattleRankCGuildBattleControllerQEAA_1402CFCA0.cpp" />
    <ClCompile Include="Source\PushClearGuildBattleRankCGuildBattleRankManagerGUI_1403CB380.cpp" />
    <ClCompile Include="Source\PushCreateGuildBattleRankTableCGuildBattleControll_1402CFC50.cpp" />
    <ClCompile Include="Source\PushCreateGuildBattleRankTableCGuildBattleRankMana_1403CB250.cpp" />
    <ClCompile Include="Source\PushDQSCGuildBattleReservedScheduleListManagerGUIL_1403CD690.cpp" />
    <ClCompile Include="Source\PushDQSClearCGuildBattleReservedScheduleMapGroupGU_1403DC2E0.cpp" />
    <ClCompile Include="Source\PushDQSDataCNormalGuildBattleManagerGUILD_BATTLEIE_1403D5050.cpp" />
    <ClCompile Include="Source\PushDQSDestGuildOutputGuildBattleCostCGuildQEAAXXZ_140258020.cpp" />
    <ClCompile Include="Source\PushDQSDrawRankCNormalGuildBattleGUILD_BATTLEIEAAX_1403E7B70.cpp" />
    <ClCompile Include="Source\PushDQSInGuildBattleCostCGuildQEAAXXZ_140257DB0.cpp" />
    <ClCompile Include="Source\PushDQSInGuildBattleRewardMoneyCGuildQEAAXXZ_140258180.cpp" />
    <ClCompile Include="Source\PushDQSPvpPointCNormalGuildBattleGuildMemberGUILD__1403E03F0.cpp" />
    <ClCompile Include="Source\PushDQSSourceGuildOutputGuildBattleCostCGuildQEAAX_140257EC0.cpp" />
    <ClCompile Include="Source\PushDQSWinLoseRankCNormalGuildBattleGUILD_BATTLEIE_1403E7C40.cpp" />
    <ClCompile Include="Source\PushItemCObjectListQEAA_NPEAU_object_list_pointZ_140189C30.cpp" />
    <ClCompile Include="Source\PushItem_TRAP_PARAMQEAA_NPEAVCTrapKZ_1400791F0.cpp" />
    <ClCompile Include="Source\RCTopGoalPrediCateCNormalGuildBattleGuildGUILD_BAT_1403EB720.cpp" />
    <ClCompile Include="Source\RCTopKillPrediCateCNormalGuildBattleGuildGUILD_BAT_1403EB5D0.cpp" />
    <ClCompile Include="Source\RegenBallCNormalGuildBattleFieldGUILD_BATTLEQEAAHX_1403ECBE0.cpp" />
    <ClCompile Include="Source\RequestSubProcSetRaceBattleResultCRaceBuffByHolyQu_1403B66E0.cpp" />
    <ClCompile Include="Source\ReturnBindPosAllCNormalGuildBattleGuildGUILD_BATTL_1403E1CC0.cpp" />
    <ClCompile Include="Source\ReturnBindPosCNormalGuildBattleGuildMemberGUILD_BA_1403E0020.cpp" />
    <ClCompile Include="Source\ReturnHQPosAllCNormalGuildBattleGuildGUILD_BATTLEQ_1403E1B50.cpp" />
    <ClCompile Include="Source\ReturnStartPosAllCNormalGuildBattleGuildGUILD_BATT_1403E1D50.cpp" />
    <ClCompile Include="Source\ReturnStartPosCNormalGuildBattleGuildMemberGUILD_B_1403DFFC0.cpp" />
    <ClCompile Include="Source\RewardGuildBattleMoneyCNormalGuildBattleGUILD_BATT_1403E6840.cpp" />
    <ClCompile Include="Source\RewardItemCNormalGuildBattleGuildGUILD_BATTLEQEAAX_1403E19B0.cpp" />
    <ClCompile Include="Source\RewardItemCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E6960.cpp" />
    <ClCompile Include="Source\rollback_cashitemCMgrAvatorItemHistoryQEAAXPEAD_K0_14023E160.cpp" />
    <ClCompile Include="Source\SaveCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_1403E37C0.cpp" />
    <ClCompile Include="Source\SaveCNormalGuildBattleManagerGUILD_BATTLEQEAA_NKZ_1403D3D10.cpp" />
    <ClCompile Include="Source\SaveINICGuildBattleControllerAEAA_NXZ_1403D7960.cpp" />
    <ClCompile Include="Source\SearchItemAddSpeedCEquipItemSFAgentIEAAMPEAU_db_co_1401217E0.cpp" />
    <ClCompile Include="Source\SectorDamageProcCAttackIEAAXHHHHHH_NZ_14016CDD0.cpp" />
    <ClCompile Include="Source\SelectGuildBattleRankListCGuildBattleRankManagerGU_1403CAF70.cpp" />
    <ClCompile Include="Source\SelectGuildBattleRankListCRFWorldDatabaseQEAA_NEPE_1404A2B70.cpp" />
    <ClCompile Include="Source\SelectGuildBattleRankRecordCRFWorldDatabaseQEAA_NK_1404A3460.cpp" />
    <ClCompile Include="Source\SelectGuildBattleRerservedListCRFWorldDatabaseQEAA_1404A38C0.cpp" />
    <ClCompile Include="Source\SelectGuildBattleScheduleInfoIDCRFWorldDatabaseQEA_1404A1CF0.cpp" />
    <ClCompile Include="Source\SelectRowCountGuildBattleInfoCRFWorldDatabaseQEAAH_1404A20D0.cpp" />
    <ClCompile Include="Source\SelectRowCountGuildBattleScheduleInfoCRFWorldDatab_1404A12B0.cpp" />
    <ClCompile Include="Source\Select_BattleResultLogLatestCRFWorldDatabaseQEAAEP_1404B0DE0.cpp" />
    <ClCompile Include="Source\Select_BattleTournamentInfoCRFWorldDatabaseQEAA_NP_1404C5EA0.cpp" />
    <ClCompile Include="Source\Select_FailBattleCountCRFWorldDatabaseQEAAHEKAEAKZ_1404C12B0.cpp" />
    <ClCompile Include="Source\Select_GuildBattleRecordCRFWorldDatabaseQEAA_NKPEA_1404AA8F0.cpp" />
    <ClCompile Include="Source\Select_LoseBattleCountCRFWorldDatabaseQEAAHEKAEAKZ_1404C0B30.cpp" />
    <ClCompile Include="Source\Select_WinBattleCountCRFWorldDatabaseQEAAHEKAEAKZ_1404C0EF0.cpp" />
    <ClCompile Include="Source\SellCashItemRemoteStoreQEAA_NGPEADZ_1402F5CB0.cpp" />
    <ClCompile Include="Source\SetActiveSuccCAttackQEAAX_NZ_14008E710.cpp" />
    <ClCompile Include="Source\SetAttackPartCGameObjectUEAAXHZ_14012C700.cpp" />
    <ClCompile Include="Source\SetBattleModeTimeCMonsterAIQEAAXKZ_1401555F0.cpp" />
    <ClCompile Include="Source\SetBattleStateCNormalGuildBattleGuildMemberGUILD_B_1403DFC80.cpp" />
    <ClCompile Include="Source\SetBattleTimeCNormalGuildBattleStateInBattleGUILD__1403D90D0.cpp" />
    <ClCompile Include="Source\SetBattleTimeCNormalGuildBattleStateListGUILD_BATT_1403D9070.cpp" />
    <ClCompile Include="Source\SetCCurrentGuildBattleInfoManagerGUILD_BATTLEQEAA__1403CE160.cpp" />
    <ClCompile Include="Source\SetCGuildBattleScheduleGUILD_BATTLEQEAAEKKZ_1403D9B90.cpp" />
    <ClCompile Include="Source\SetColorInxCNormalGuildBattleGuildGUILD_BATTLEQEAA_1403EB190.cpp" />
    <ClCompile Include="Source\SetCombatStateCMonsterQEAAXEZ_140143830.cpp" />
    <ClCompile Include="Source\SetCopmlteGuildBattleSuggestCGuildQEAAXXZ_1402582E0.cpp" />
    <ClCompile Include="Source\SetDamageAbleStateCHolyKeeperQEAAX_NZ_140284770.cpp" />
    <ClCompile Include="Source\SetDelay_ATTACK_DELAY_CHECKERQEAAXKZ_14008E760.cpp" />
    <ClCompile Include="Source\SetGotoRegenStartCNormalGuildBattleGUILD_BATTLEQEA_1403F3240.cpp" />
    <ClCompile Include="Source\SetGotoRegenStateCNormalGuildBattleStateInBattleGU_1403F3300.cpp" />
    <ClCompile Include="Source\SetGotoRegenStateCNormalGuildBattleStateListGUILD__1403F3290.cpp" />
    <ClCompile Include="Source\SetGuildBattleMatterCGuildQEAAXKKKKZ_140259E40.cpp" />
    <ClCompile Include="Source\SetGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAXPE_1403EB0A0.cpp" />
    <ClCompile Include="Source\SetItemCGuildBattleRewardItemGUILD_BATTLEIEAA_NPEA_1403C92D0.cpp" />
    <ClCompile Include="Source\SetLoadCBattleTournamentInfoQEAAX_NZ_1403FEB50.cpp" />
    <ClCompile Include="Source\SetNextDiscountEventTimeCashItemRemoteStoreQEAA_NX_1402F7900.cpp" />
    <ClCompile Include="Source\SetNextEventCNormalGuildBattleManagerGUILD_BATTLEQ_1403DED80.cpp" />
    <ClCompile Include="Source\SetNextEventTimeCashItemRemoteStoreQEAA_NEZ_1402FCD30.cpp" />
    <ClCompile Include="Source\SetNextEvnetCGuildBattleScheduleManagerGUILD_BATTL_1403DD6C0.cpp" />
    <ClCompile Include="Source\SetNextStateCGuildBattleStateListGUILD_BATTLEMEAAX_14007F830.cpp" />
    <ClCompile Include="Source\SetNextStateCNormalGuildBattleStateListGUILD_BATTL_140080340.cpp" />
    <ClCompile Include="Source\SetNextStateCNormalGuildBattleStateRoundListGUILD__140080040.cpp" />
    <ClCompile Include="Source\SetOrderViewAttackStateCPvpOrderViewQEAAXXZ_1403F7AA0.cpp" />
    <ClCompile Include="Source\SetOrderViewDamagedStateCPvpOrderViewQEAAXXZ_1403F7AF0.cpp" />
    <ClCompile Include="Source\SetPortalInxCNormalGuildBattleFieldGUILD_BATTLEAEA_1403EDD60.cpp" />
    <ClCompile Include="Source\SetProcStateCGuildBattleScheduleGUILD_BATTLEQEAAXX_1403DEBB0.cpp" />
    <ClCompile Include="Source\SetReadyCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1403EB0D0.cpp" />
    <ClCompile Include="Source\SetReadyStateCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E3B30.cpp" />
    <ClCompile Include="Source\SetReadyStateCNormalGuildBattleManagerGUILD_BATTLE_1403D5480.cpp" />
    <ClCompile Include="Source\SetReStartFlagCNormalGuildBattleGuildGUILD_BATTLEQ_1403E0900.cpp" />
    <ClCompile Include="Source\SetReStartFlagCNormalGuildBattleGuildMemberGUILD_B_1403EADB0.cpp" />
    <ClCompile Include="Source\SetSFDamageToleracne_VariationMonsterSFContDamageT_140158000.cpp" />
    <ClCompile Include="Source\SetStateListCGuildBattleScheduleGUILD_BATTLEQEAAXP_1403D9150.cpp" />
    <ClCompile Include="Source\SetStaticMemberCAttackSAXPEAVCRecordDataZ_14016D860.cpp" />
    <ClCompile Include="Source\SetWaitCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1403EB110.cpp" />
    <ClCompile Include="Source\SetWinnerInfoCBattleTournamentInfoQEAA_NKPEADEZ_1403FEB70.cpp" />
    <ClCompile Include="Source\Set_CashEvent_StatusCashItemRemoteStoreQEAAXEEZ_1402FAB90.cpp" />
    <ClCompile Include="Source\set_cde_statusCashItemRemoteStoreQEAAXEZ_1402F72D0.cpp" />
    <ClCompile Include="Source\Set_Conditional_Evnet_StatusCashItemRemoteStoreQEA_1402FC3E0.cpp" />
    <ClCompile Include="Source\Set_DB_LimitedSale_EventCashItemRemoteStoreQEAAXXZ_1402FDAD0.cpp" />
    <ClCompile Include="Source\Set_FROMDB_LimitedSale_EventCashItemRemoteStoreQEA_1402FDE70.cpp" />
    <ClCompile Include="Source\Set_LimitedSale_countCashItemRemoteStoreQEAAXEKZ_1402FDA00.cpp" />
    <ClCompile Include="Source\Set_LimitedSale_DCKCashItemRemoteStoreQEAAXEEZ_1402FD750.cpp" />
    <ClCompile Include="Source\Set_LimitedSale_EventCashItemRemoteStoreQEAAXXZ_1402FDFD0.cpp" />
    <ClCompile Include="Source\Set_LimitedSale_Event_IniCashItemRemoteStoreQEAAXP_1402FD780.cpp" />
    <ClCompile Include="Source\sizevectorVCGuildBattleRewardItemGUILD_BATTLEVallo_1403D2270.cpp" />
    <ClCompile Include="Source\size_attack_count_result_zoclQEAAHXZ_1400EEEF0.cpp" />
    <ClCompile Include="Source\size_attack_force_result_zoclQEAAHXZ_1400EEDF0.cpp" />
    <ClCompile Include="Source\size_attack_gen_result_zoclQEAAHXZ_1400EECF0.cpp" />
    <ClCompile Include="Source\size_attack_keeper_inform_zoclQEAAHXZ_140136C20.cpp" />
    <ClCompile Include="Source\size_attack_selfdestruction_result_zoclQEAAHXZ_1400EEF70.cpp" />
    <ClCompile Include="Source\size_attack_siege_result_zoclQEAAHXZ_1400EEFF0.cpp" />
    <ClCompile Include="Source\size_attack_trap_inform_zoclQEAAHXZ_140141420.cpp" />
    <ClCompile Include="Source\size_attack_unit_result_zoclQEAAHXZ_1400EEE70.cpp" />
    <ClCompile Include="Source\size_guild_battle_get_gravity_stone_result_zoclQEA_1403EAFE0.cpp" />
    <ClCompile Include="Source\size_guild_battle_goal_result_zoclQEAAHXZ_1403EB3A0.cpp" />
    <ClCompile Include="Source\size_guild_battle_rank_list_result_zoclQEAAHXZ_1403D0930.cpp" />
    <ClCompile Include="Source\size_guild_battle_reserved_schedule_result_zoclQEA_1403D0970.cpp" />
    <ClCompile Include="Source\size_guild_battle_suggest_request_result_zoclQEAAH_14025D5F0.cpp" />
    <ClCompile Include="Source\size_notify_not_use_premium_cashitem_zoclQEAAHXZ_1400F0850.cpp" />
    <ClCompile Include="Source\size_param_cashitem_dblogQEAAHXZ_140304D90.cpp" />
    <ClCompile Include="Source\size_personal_automine_attacked_zoclQEAAHXZ_1402DE340.cpp" />
    <ClCompile Include="Source\size_possible_battle_guild_list_result_zoclQEAAHXZ_1403D0860.cpp" />
    <ClCompile Include="Source\size_qry_case_addguildbattlescheduleQEAAHXZ_1403D93C0.cpp" />
    <ClCompile Include="Source\size_qry_case_dest_guild_out_guildbattlecostQEAAHX_14025D5D0.cpp" />
    <ClCompile Include="Source\size_qry_case_in_guildbattlecostQEAAHXZ_14025D5B0.cpp" />
    <ClCompile Include="Source\size_qry_case_in_guildbattlerewardmoneyQEAAHXZ_14025D5E0.cpp" />
    <ClCompile Include="Source\size_qry_case_loadguildbattlerankQEAAHXZ_140207580.cpp" />
    <ClCompile Include="Source\size_qry_case_load_guildbattle_totalrecordQEAAHXZ_140207590.cpp" />
    <ClCompile Include="Source\size_qry_case_src_guild_out_guildbattlecostQEAAHXZ_14025D5C0.cpp" />
    <ClCompile Include="Source\size_qry_case_updateclearguildbattleDayInfoQEAAHXZ_1403DECE0.cpp" />
    <ClCompile Include="Source\size_qry_case_updatedrawguildbattlerankQEAAHXZ_1403EB3D0.cpp" />
    <ClCompile Include="Source\size_qry_case_updatewinloseguildbattlerankQEAAHXZ_1403EB3E0.cpp" />
    <ClCompile Include="Source\SkipWhiteSpaceTiXmlBaseKAPEBDPEBDW4TiXmlEncodingZ_140530B60.cpp" />
    <ClCompile Include="Source\SrcGuildIsAvailableBattleRequestStateCGuildQEAAEXZ_1402578D0.cpp" />
    <ClCompile Include="Source\start_casheventCashItemRemoteStoreQEAA_NHHHHEZ_1402FB0D0.cpp" />
    <ClCompile Include="Source\start_cdeCashItemRemoteStoreQEAA_NHHHHZ_1402F7590.cpp" />
    <ClCompile Include="Source\start_coneventCashItemRemoteStoreQEAA_NHHEZ_1402FCAE0.cpp" />
    <ClCompile Include="Source\StockOldInfoCNormalGuildBattleGuildMemberGUILD_BAT_1403DFB40.cpp" />
    <ClCompile Include="Source\TakeGravityStoneCNormalGuildBattleGUILD_BATTLEQEAA_1403E4A60.cpp" />
    <ClCompile Include="Source\unchecked_copyPEAVCGuildBattleRewardItemGUILD_BATT_1403D2590.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_copyPEAVCGuildBattleReward_1403D31F0.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_fill_nPEAVCGuildBattleRewa_1403D2910.cpp" />
    <ClCompile Include="Source\UpdateCGuildBattleRankManagerGUILD_BATTLEQEAA_NEPE_1403CA680.cpp" />
    <ClCompile Include="Source\UpdateClearGuildBattleDayInfoCNormalGuildBattleMan_1403D42E0.cpp" />
    <ClCompile Include="Source\UpdateClearGuildBattleInfoCRFWorldDatabaseQEAA_NKK_1404A2AC0.cpp" />
    <ClCompile Include="Source\UpdateClearGuildBattleRankCRFWorldDatabaseQEAA_NXZ_1404A31B0.cpp" />
    <ClCompile Include="Source\UpdateClearGuildBattleScheduleDayInfoCGuildBattleS_1403DD8B0.cpp" />
    <ClCompile Include="Source\UpdateClearGuildBattleScheduleInfoCRFWorldDatabase_1404A1C40.cpp" />
    <ClCompile Include="Source\UpdateClearGuildBattleScheduleInfoCRFWorldDatabase_1404A2020.cpp" />
    <ClCompile Include="Source\UpdateClearRerservedDayInfoCGuildBattleControllerQ_1403D6CF0.cpp" />
    <ClCompile Include="Source\UpdateDayChangedWorkCGuildBattleScheduleManagerGUI_1403DD650.cpp" />
    <ClCompile Include="Source\UpdateDrawCGuildBattleControllerQEAA_NEKEKZ_1403D6C80.cpp" />
    <ClCompile Include="Source\UpdateDrawCGuildBattleRankManagerGUILD_BATTLEQEAA__1403CB120.cpp" />
    <ClCompile Include="Source\UpdateDrawGuildBattleResultCRFWorldDatabaseQEAA_NK_1404A33B0.cpp" />
    <ClCompile Include="Source\UpdateGoalCntCCurrentGuildBattleInfoManagerGUILD_B_1403CE340.cpp" />
    <ClCompile Include="Source\UpdateGuildBattleDrawRankInfoCMainThreadQEAAXPEAU__1401F4470.cpp" />
    <ClCompile Include="Source\UpdateGuildBattleInfoCRFWorldDatabaseQEAA_NKKKKEZ_1404A29E0.cpp" />
    <ClCompile Include="Source\UpdateGuildBattleScheduleInfoCRFWorldDatabaseQEAA__1404A1AA0.cpp" />
    <ClCompile Include="Source\UpdateGuildBattleWinCntCGuildQEAAXKKKZ_140253190.cpp" />
    <ClCompile Include="Source\UpdateGuildBattleWinLoseRankInfoCMainThreadQEAAXPE_1401F4290.cpp" />
    <ClCompile Include="Source\UpdateGuildListCPossibleBattleGuildListManagerGUIL_1403C9B90.cpp" />
    <ClCompile Include="Source\UpdateLoadGuildBattleRankCMainThreadQEAAXPEAU_DB_Q_1401F4650.cpp" />
    <ClCompile Include="Source\UpdateLoseGuildBattleResultCRFWorldDatabaseQEAA_NK_1404A3300.cpp" />
    <ClCompile Include="Source\UpdateMonsterSFContDamageToleracneQEAAXXZ_140158080.cpp" />
    <ClCompile Include="Source\UpdatePossibleBattleGuildListCGuildBattleControlle_1403D6370.cpp" />
    <ClCompile Include="Source\UpdateRankCGuildBattleControllerQEAA_NEPEAEZ_1403D6D80.cpp" />
    <ClCompile Include="Source\UpdateReservedGuildBattleScheduleCGuildBattleContr_1403D6DD0.cpp" />
    <ClCompile Include="Source\UpdateReservedGuildBattleScheduleCMainThreadQEAAXP_1401F4BE0.cpp" />
    <ClCompile Include="Source\UpdateReservedSheduleCGuildBattleReservedScheduleL_1403CD720.cpp" />
    <ClCompile Include="Source\UpdateScoreCCurrentGuildBattleInfoManagerGUILD_BAT_1403CE2B0.cpp" />
    <ClCompile Include="Source\UpdateScoreCNormalGuildBattleGuildGUILD_BATTLEIEAA_1403EAE20.cpp" />
    <ClCompile Include="Source\UpdateTodayScheduleCGuildBattleReservedScheduleLis_1403CD9C0.cpp" />
    <ClCompile Include="Source\UpdateTomorrowCompleteCGuildBattleReservedSchedule_1403CD7A0.cpp" />
    <ClCompile Include="Source\UpdateTomorrowScheduleCGuildBattleReservedSchedule_1403CDB60.cpp" />
    <ClCompile Include="Source\UpdateUseFieldCGuildBattleReservedScheduleGUILD_BA_1403DB8D0.cpp" />
    <ClCompile Include="Source\UpdateUseFlagCGuildBattleReservedScheduleGUILD_BAT_1403DB4F0.cpp" />
    <ClCompile Include="Source\UpdateUseFlagCGuildBattleReservedScheduleMapGroupG_1403DC560.cpp" />
    <ClCompile Include="Source\UpdateUseFlagCGuildBattleScheduleManagerGUILD_BATT_1403DD120.cpp" />
    <ClCompile Include="Source\UpdateWinGuildBattleResultCRFWorldDatabaseQEAA_NKK_1404A3250.cpp" />
    <ClCompile Include="Source\UpdateWinLoseCGuildBattleControllerQEAA_NEKEKZ_1403D6C10.cpp" />
    <ClCompile Include="Source\UpdateWinLoseCGuildBattleRankManagerGUILD_BATTLEQE_1403CAFF0.cpp" />
    <ClCompile Include="Source\Update_BattleResultLogBattleResultAndPvpPointCRFWo_1404B1100.cpp" />
    <ClCompile Include="Source\Update_CristalBattleCharInfoCRFWorldDatabaseQEAA_N_1404A9A90.cpp" />
    <ClCompile Include="Source\update_cristalbattle_dateCRFWorldDatabaseQEAA_NKEZ_1404C5160.cpp" />
    <ClCompile Include="Source\Update_IncreaseWeeklyGuildGuildBattlePvpPointSumCR_1404A70F0.cpp" />
    <ClCompile Include="Source\update_iniCashItemRemoteStoreQEAAXPEAU_cash_discou_1402F7160.cpp" />
    <ClCompile Include="Source\Update_INICashItemRemoteStoreQEAAXPEAU_cash_event__1402F8C40.cpp" />
    <ClCompile Include="Source\WriteLogPer10Min_CombatCHolyStoneSystemIEAAXXZ_1402804E0.cpp" />
    <ClCompile Include="Source\_AllocateVCGuildBattleRewardItemGUILD_BATTLEstdYAP_1403D26D0.cpp" />
    <ClCompile Include="Source\_Assign_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D0D70.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_item_calc_price_discountCash_1402FFCE0.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_item_calc_price_limitsaleCas_1402FFE60.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_item_calc_price_one_n_oneCas_1402FFDB0.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_item_setbuydblogCashItemRemo_140300760.cpp" />
    <ClCompile Include="Source\_BuyvectorVCGuildBattleRewardItemGUILD_BATTLEVallo_1403D12E0.cpp" />
    <ClCompile Include="Source\_CalcForceAttPntCAttackIEAAH_NZ_14016AF70.cpp" />
    <ClCompile Include="Source\_CalcGenAttPntCAttackIEAAH_NZ_14016AA00.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreBuyByCash__1_dtor0_1402FEC90.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreBuyByGold__1_dtor0_1402FEF70.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor0_1402F3920.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor1_1402F3960.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor2_1402F3990.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor3_1402F39C0.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor4_1402F39F0.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor5_1402F3A30.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor6_1402F3A60.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreCheatLoadCashAmount__1_dtor0_1402F5B20.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreFindCashRec__1_dtor0_1402F4A30.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreFindCashRec__1_dtor1_1402F4A60.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreGoodsListBuyByCash__1_dtor0_140300C30.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStoreInstance__1_dtor0_1400798A0.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor0_1402F3B80.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor1_1402F3BC0.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor2_1402F3BF0.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor3_1402F3C20.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor4_1402F3C50.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor5_1402F3C90.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor6_1402F3CC0.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStore_MakeLinkTable__1_dtor0_1402F48C0.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStore_ReadGoods__1_dtor0_1402F4C60.cpp" />
    <ClCompile Include="Source\_CashItemRemoteStore__CheckGoods__1_dtor0_1402F4620.cpp" />
    <ClCompile Include="Source\_CGuildBattleControllerInstance__1_dtor0_1403D5770.cpp" />
    <ClCompile Include="Source\_check_buyitemCashItemRemoteStoreAEAAAW4CS_RCODEEP_1402F4FA0.cpp" />
    <ClCompile Include="Source\_complete_tsk_cashitem_buy_dblogCashDbWorkerIEAAXP_1402F0210.cpp" />
    <ClCompile Include="Source\_ConstructVCGuildBattleRewardItemGUILD_BATTLEV12st_1403D3120.cpp" />
    <ClCompile Include="Source\_Copy_backward_optPEAVCGuildBattleRewardItemGUILD__1403D3060.cpp" />
    <ClCompile Include="Source\_Copy_optPEAVCGuildBattleRewardItemGUILD_BATTLEPEA_1403D2A80.cpp" />
    <ClCompile Include="Source\_db_Load_BattleTournamentInfoCMainThreadAEAAXXZ_1401B7210.cpp" />
    <ClCompile Include="Source\_db_load_losebattlecountCMainThreadAEAAEKPEAU_AVAT_1401A99F0.cpp" />
    <ClCompile Include="Source\_delay_check_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10.cpp" />
    <ClCompile Include="Source\_DestroyVCGuildBattleRewardItemGUILD_BATTLEstdYAXP_1403D31E0.cpp" />
    <ClCompile Include="Source\_DestroyvectorVCGuildBattleRewardItemGUILD_BATTLEV_1403D17A0.cpp" />
    <ClCompile Include="Source\_Destroy_rangeVCGuildBattleRewardItemGUILD_BATTLEV_1403D2650.cpp" />
    <ClCompile Include="Source\_Destroy_rangeVCGuildBattleRewardItemGUILD_BATTLEV_1403D2B40.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CAttacks_DefParam___1406DC660.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__GUILD_BATTLECGuildBattle_1406E4080.cpp" />
    <ClCompile Include="Source\_ECNormalGuildBattleFieldGUILD_BATTLEQEAAPEAXIZ_1403F0220.cpp" />
    <ClCompile Include="Source\_ECNormalGuildBattleStateListGUILD_BATTLEQEAAPEAXI_1403F33B0.cpp" />
    <ClCompile Include="Source\_ECReservedGuildScheduleMapGroupGUILD_BATTLEQEAAPE_1403D09B0.cpp" />
    <ClCompile Include="Source\_FillPEAVCGuildBattleRewardItemGUILD_BATTLEV12stdY_1403D2C70.cpp" />
    <ClCompile Include="Source\_GCCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAPE_1403D0B40.cpp" />
    <ClCompile Include="Source\_GCGuildBattleControllerIEAAPEAXIZ_1403D9720.cpp" />
    <ClCompile Include="Source\_GCGuildBattleLoggerGUILD_BATTLEIEAAPEAXIZ_1403D0BB0.cpp" />
    <ClCompile Include="Source\_GCGuildBattleRankManagerGUILD_BATTLEIEAAPEAXIZ_1403D08C0.cpp" />
    <ClCompile Include="Source\_GCGuildBattleReservedScheduleGUILD_BATTLEQEAAPEAX_1403DEBD0.cpp" />
    <ClCompile Include="Source\_GCGuildBattleReservedScheduleListManagerGUILD_BAT_1403D0AA0.cpp" />
    <ClCompile Include="Source\_GCGuildBattleScheduleGUILD_BATTLEQEAAPEAXIZ_1403DE8B0.cpp" />
    <ClCompile Include="Source\_GCGuildBattleScheduleManagerGUILD_BATTLEIEAAPEAXI_1403DECF0.cpp" />
    <ClCompile Include="Source\_GCGuildBattleSchedulePoolGUILD_BATTLEIEAAPEAXIZ_1403DE920.cpp" />
    <ClCompile Include="Source\_GCGuildBattleSchedulerGUILD_BATTLEIEAAPEAXIZ_1403DEDB0.cpp" />
    <ClCompile Include="Source\_GCNormalGuildBattleFieldListGUILD_BATTLEIEAAPEAXI_1403F02F0.cpp" />
    <ClCompile Include="Source\_GCNormalGuildBattleGUILD_BATTLEQEAAPEAXIZ_1403D8F40.cpp" />
    <ClCompile Include="Source\_GCNormalGuildBattleManagerGUILD_BATTLEIEAAPEAXIZ_1403D8FB0.cpp" />
    <ClCompile Include="Source\_GCNormalGuildBattleStateListPoolGUILD_BATTLEIEAAP_1403F3480.cpp" />
    <ClCompile Include="Source\_GCPossibleBattleGuildListManagerGUILD_BATTLEIEAAP_1403D0770.cpp" />
    <ClCompile Include="Source\_GCRFCashItemDatabaseUEAAPEAXIZ_1402F2B40.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECCurrentGuildBattleInfoManagerInstanc_1403CDF50.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleLoggerInit__1_dtor0_1403CE9B0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleLoggerInstance__1_dtor0_1403CE820.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleRankManagerInstance__1_dt_1403CA400.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleReservedScheduleListManag_1403CD3F0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleReservedScheduleMapGroupI_1403DBD90.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleRewardItemManagerInstance_1403D9820.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleScheduleManagerInit__1_dt_1403DCD00.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleScheduleManagerInstance___1403DCAA0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleScheduleManager_CGuildBat_1403DC9D0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleSchedulePoolInit__1_dtor0_1403DA860.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleSchedulePoolInstance__1_d_1403DA610.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleSchedulerInstance__1_dtor_1403DD790.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleStateListLog__1_dtor0_1403DF440.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECGuildBattleStateLog__1_dtor0_1403DEF60.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2EF0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2F20.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2F50.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2F80.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor0_1403EC510.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor1_1403EC540.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor2_1403EC570.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor3_1403EC5A0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor4_1403EC5D0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor5_1403EC600.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleFieldListInit__1_dt_1403EE840.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleFieldListInstance___1403EE3F0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleFieldLoadDummys__1__1403EDD30.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleGuildCNormalGuildBa_1403E0550.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleLoggerInit__1_dtor0_1403CED20.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleManagerInit__1_dtor_1403D38C0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleManagerInstance__1__1403D35C0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateInBattleCNorma_1403F09D0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateInBattle_CNorm_14007FE90.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2000.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2030.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2060.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2090.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F20C0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F20F0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2120.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateListPoolInit___1403F2610.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateListPoolInstan_1403F2400.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F920.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F950.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F980.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F9B0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F9E0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007FA10.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007FA40.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundListCNorm_1403F2220.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundListCNorm_1403F2250.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundListCNorm_1403F2280.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundList_CNor_14007FF70.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundList_CNor_14007FFA0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundList_CNor_14007FFD0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundProcessCN_1403F1830.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundProcessCN_1403F1860.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundProcess_C_1403F1940.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundReturnSta_1403F1BD0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundReturnSta_1403F1C00.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundReturnSta_1403F1CE0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundStartCNor_1403F1410.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundStartCNor_1403F1440.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattleStateRoundStart_CNo_1403F1520.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattle_CNormalGuildBattle_1403E3040.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattle_CNormalGuildBattle_1403E3070.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECNormalGuildBattle_CNormalGuildBattle_1403E30A0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECPossibleBattleGuildListManagerInit___1403C99C0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECPossibleBattleGuildListManagerInstan_1403C96E0.cpp" />
    <ClCompile Include="Source\_GUILD_BATTLECReservedGuildScheduleDayGroupInit__1_1403CCE50.cpp" />
    <ClCompile Include="Source\_InitLoggersCashItemRemoteStoreAEAA_NXZ_1402F3CF0.cpp" />
    <ClCompile Include="Source\_Insert_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D1A90.cpp" />
    <ClCompile Include="Source\_Iter_randomPEAVCGuildBattleRewardItemGUILD_BATTLE_1403D29C0.cpp" />
    <ClCompile Include="Source\_MakeLinkTableCashItemRemoteStoreAEAA_NPEADHZ_1402F4650.cpp" />
    <ClCompile Include="Source\_Max_elementPEAVCNormalGuildBattleGuildMemberGUILD_1403EB510.cpp" />
    <ClCompile Include="Source\_Max_elementPEAVCNormalGuildBattleGuildMemberGUILD_1403EB660.cpp" />
    <ClCompile Include="Source\_Move_backward_optPEAVCGuildBattleRewardItemGUILD__1403D2D70.cpp" />
    <ClCompile Include="Source\_Move_catPEAVCGuildBattleRewardItemGUILD_BATTLEstd_1403D2D10.cpp" />
    <ClCompile Include="Source\_Ptr_catPEAVCGuildBattleRewardItemGUILD_BATTLEPEAV_1403D2A20.cpp" />
    <ClCompile Include="Source\_PushItemCutLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LTDZ_14024AEB0.cpp" />
    <ClCompile Include="Source\_PushItemMoveLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LTD_14024B300.cpp" />
    <ClCompile Include="Source\_ReadGoodsCashItemRemoteStoreAEAA_NXZ_1402F4A90.cpp" />
    <ClCompile Include="Source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D0EE0.cpp" />
    <ClCompile Include="Source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D11F0.cpp" />
    <ClCompile Include="Source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1220.cpp" />
    <ClCompile Include="Source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1250.cpp" />
    <ClCompile Include="Source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1750.cpp" />
    <ClCompile Include="Source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D18A0.cpp" />
    <ClCompile Include="Source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1FE0.cpp" />
    <ClCompile Include="Source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D2010.cpp" />
    <ClCompile Include="Source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D2070.cpp" />
    <ClCompile Include="Source\_std_Uninit_copy_GUILD_BATTLECGuildBattleRewardIte_1403D3330.cpp" />
    <ClCompile Include="Source\_std_Uninit_fill_n_GUILD_BATTLECGuildBattleRewardI_1403D2EB0.cpp" />
    <ClCompile Include="Source\_TidyvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D13E0.cpp" />
    <ClCompile Include="Source\_UfillvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1403D2500.cpp" />
    <ClCompile Include="Source\_UmovePEAVCGuildBattleRewardItemGUILD_BATTLEvector_1403D2780.cpp" />
    <ClCompile Include="Source\_Unchecked_move_backwardPEAVCGuildBattleRewardItem_1403D2850.cpp" />
    <ClCompile Include="Source\_Unchecked_uninitialized_movePEAVCGuildBattleRewar_1403D2BC0.cpp" />
    <ClCompile Include="Source\_Uninit_copyPEAVCGuildBattleRewardItemGUILD_BATTLE_1403D32A0.cpp" />
    <ClCompile Include="Source\_Uninit_fill_nPEAVCGuildBattleRewardItemGUILD_BATT_1403D2E20.cpp" />
    <ClCompile Include="Source\_Uninit_movePEAVCGuildBattleRewardItemGUILD_BATTLE_1403D2FF0.cpp" />
    <ClCompile Include="Source\_XlenvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D1810.cpp" />
    <ClCompile Include="Source\__CheckGoodsCashItemRemoteStoreAEAA_NAEAVCRecordDa_1402F4150.cpp" />
  </ItemGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>