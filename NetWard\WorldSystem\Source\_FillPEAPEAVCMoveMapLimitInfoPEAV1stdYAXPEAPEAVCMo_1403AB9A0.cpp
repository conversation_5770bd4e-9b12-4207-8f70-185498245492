#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Fill@PEAPEAVCMoveMapLimitInfo@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitInfo@@0AEBQEAV1@@Z
 * Address: 0x1403AB9A0

void  std::_Fill<CMoveMapLimitInfo * *,CMoveMapLimitInfo *>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo *const *_Val)
{
  CMoveMapLimitInfo **i; // [sp+10h] [bp+8h]@1

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}
