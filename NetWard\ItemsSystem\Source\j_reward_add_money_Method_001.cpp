#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?reward_add_money@CMgrAvatorItemHistory@@QEAAXHPEADKKKK0@Z
 * Address: 0x14000C4CD

void  CMgrAvatorItemHistory::reward_add_money(CMgrAvatorItemHistory *this, int n, char *psz<PERSON><PERSON>e, unsigned int dwAddDalant, unsigned int dwAddGold, unsigned int dwSumDalant, unsigned int dwSumGold, char *pszFileName)
{
  CMgrAvatorItemHistory::reward_add_money(
    this,
    n,
    psz<PERSON>lause,
    dwAddDalant,
    dwAddGold,
    dwSumDalant,
    dwSumGold,
    pszFileName);
}
