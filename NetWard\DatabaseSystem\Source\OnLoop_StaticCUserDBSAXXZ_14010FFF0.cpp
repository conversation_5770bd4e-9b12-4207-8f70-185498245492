#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?OnLoop_Static@CUserDB@@SAXXZ
 * Address: 0x14010FFF0

void CUserDB::OnLoop_Static(void)
{
  int64_t *v0; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v2; // [sp+0h] [bp-28h]@1

  v0 = &v2;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v0 = -*********;
    v0 = (int64_t *)((char *)v0 + 4);
  }
  CMgrAccountLobbyHistory::OnLoop(&CUserDB::s_MgrLobbyHistory);
}
