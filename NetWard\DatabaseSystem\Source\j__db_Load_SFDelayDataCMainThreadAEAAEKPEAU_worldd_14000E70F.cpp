#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_db_Load_SFDelayData@CMainThread@@AEAAEKPEAU_worlddb_sf_delay_info@@@Z
 * Address: 0x14000E70F

char  CMainThread::_db_Load_SFDelayData(CMainThread *this, unsigned int dwSerial, _worlddb_sf_delay_info *pDbSFDelayInfo)
{
  return CMainThread::_db_Load_SFDelayData(this, dwSerial, pDbSFDelayInfo);
}
