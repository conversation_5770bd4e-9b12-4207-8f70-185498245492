#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Ufill@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAAPEAPEAVCLogTypeDBTask@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x14000EF0C

CLogTypeDBTask ** std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Ufill(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, CLogTypeDBTask **_Ptr, unsigned int64_t _Count, CLogTypeDBTask *const *_Val)
{
  return std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Ufill(this, _Ptr, _Count, _Val);
}
