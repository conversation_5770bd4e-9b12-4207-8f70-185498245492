#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?PopFront@CItemDropMgr@@IEAA_NXZ
 * Address: 0x1402D01D0

char  CItemDropMgr::PopFront(CItemDropMgr *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v4; // [sp+0h] [bp-48h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-24h]@6
  CItemDropMgr *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( CNetIndexList::size(&v6->m_listTask) > 0 )
  {
    pdwOutIndex = -1;
    CNetIndexList::PopNode_Front(&v6->m_listTask, &pdwOutIndex);
    CNetIndexList::PushNode_Back(&v6->m_listEmpty, pdwOutIndex);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
