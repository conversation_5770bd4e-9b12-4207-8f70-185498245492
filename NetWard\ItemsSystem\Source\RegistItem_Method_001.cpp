#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?RegistItem@CUnmannedTraderRegistItemInfo@@QEAAXKGKKEEGE_KK_N@Z
 * Address: 0x1403605B0

void  CUnmannedTraderRegistItemInfo::RegistItem(CUnmannedTraderRegistItemInfo *this, unsigned int dwRegistSerial, unsigned int16_t wItemSerial, unsigned int dwETSerialNumber, unsigned int dwPrice, char bySellTurm, char byTableCode, unsigned int16_t wItemIndex, char byStorageIndex, unsigned int64_t dwD, unsigned int dwU, bool bInserted)
{
  int64_t *v12; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v14; // [sp+0h] [bp-48h]@1
  int64_t _Time; // [sp+28h] [bp-20h]@4
  int v16; // [sp+34h] [bp-14h]@5
  CUnmannedTraderRegistItemInfo *v17; // [sp+50h] [bp+8h]@1
  unsigned int v18; // [sp+58h] [bp+10h]@1
  unsigned int16_t v19; // [sp+60h] [bp+18h]@1
  unsigned int v20; // [sp+68h] [bp+20h]@1

  v20 = dwETSerialNumber;
  v19 = wItemSerial;
  v18 = dwRegistSerial;
  v17 = this;
  v12 = &v14;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v12 = -*********;
    v12 = (int64_t *)((char *)v12 + 4);
  }
  _Time = 0i64;
  time_18(&_Time);
  v17->m_tStartTime = _Time;
  v17->m_dwRegistSerial = v18;
  v17->m_wItemSerial = v19;
  v17->m_dwETSerialNumber = v20;
  v17->m_dwPrice = dwPrice;
  v17->m_bySellTurm = bySellTurm;
  v17->m_dwBuyerSerial = -1;
  v17->m_dwTax = 0;
  v17->m_tResultTime = 0i64;
  v17->m_wszBuyerName[0] = 0;
  v17->m_szBuyerAccount[0] = 0;
  v17->m_byTableCode = byTableCode;
  v17->m_wItemIndex = wItemIndex;
  v17->m_byStorageIndex = byStorageIndex;
  v17->m_dwD = dwD;
  v17->m_dwU = dwU;
  if ( bInserted )
    v16 = 2;
  else
    v16 = 1;
  CUnmannedTraderItemState::Set(&v17->m_kState, v16);
}
