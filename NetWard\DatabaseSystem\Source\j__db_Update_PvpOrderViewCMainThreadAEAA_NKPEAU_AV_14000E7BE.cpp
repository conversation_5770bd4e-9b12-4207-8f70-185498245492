#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_db_Update_PvpOrderView@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD1@Z
 * Address: 0x14000E7BE

bool  CMainThread::_db_Update_PvpOrderView(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *szQuery, char *szError)
{
  return CMainThread::_db_Update_PvpOrderView(this, dwSerial, pNewData, pOldData, szQuery, szError);
}
