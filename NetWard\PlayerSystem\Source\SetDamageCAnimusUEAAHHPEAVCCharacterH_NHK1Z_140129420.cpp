#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetDamage@CAnimus@@UEAAHHPEAVCCharacter@@H_NHK1@Z
 * Address: 0x140129420

int64_t  CAnimus::SetDamage(CAnimus *this, int nDam, CCharacter *pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn)
{
  int64_t *v8; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v11; // [sp+0h] [bp-28h]@1
  CAnimus *v12; // [sp+30h] [bp+8h]@1
  signed int nDamage; // [sp+38h] [bp+10h]@1

  nDamage = nDam;
  v12 = this;
  v8 = &v11;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v8 = -858993460;
    v8 = (int64_t *)((char *)v8 + 4);
  }
  if ( v12->m_byRoleCode == 1 )
    CAnimus::CalcDefExp(v12, pDst, nDam);
  if ( nDamage > 1 )
  {
    v12->m_nHP -= nDamage;
    if ( v12->m_nHP <= 0 )
      v12->m_nHP = 0;
    CAnimus::AlterHP_MasterReport(v12);
    if ( bCrt )
    {
      v12->m_dwStunTime = GetLoopTime();
      if ( v12->m_bMove )
      {
        CCharacter::MoveBreak((CCharacter *)&v12->vfptr, v12->m_fMoveSpeed);
        CCharacter::Stop((CCharacter *)&v12->vfptr);
      }
    }
  }
  if ( !v12->m_nHP )
  {
    CAnimus::AlterExp(v12, (unsigned int)(signed int)ffloor(-0.0 - v12->m_pRecord->m_fPenalty));
    CAnimus::Return_MasterRequest(v12, 1);
  }
  return v12->m_nHP;
}
