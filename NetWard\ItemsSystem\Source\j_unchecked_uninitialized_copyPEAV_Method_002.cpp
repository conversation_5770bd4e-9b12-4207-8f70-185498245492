#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_uninitialized_copy@PEAVCUnmannedTraderItemCodeInfo@@PEAV1@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@stdext@@YAPEAVCUnmannedTraderItemCodeInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@Z
 * Address: 0x14000ED68

CUnmannedTraderItemCodeInfo * stdext::unchecked_uninitialized_copy<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *,std::allocator<CUnmannedTraderItemCodeInfo>>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, CUnmannedTraderItemCodeInfo *_Dest, std::allocator<CUnmannedTraderItemCodeInfo> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *,std::allocator<CUnmannedTraderItemCodeInfo>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
