#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Uninit_move@PEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderSubClassInfo@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140013BA1

CUnmannedTraderSubClassInfo ** std::_Uninit_move<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *,std::allocator<CUnmannedTraderSubClassInfo *>,std::_Undefined_move_tag>(CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last, CUnmannedTraderSubClassInfo **_Dest, std::allocator<CUnmannedTraderSubClassInfo *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *,std::allocator<CUnmannedTraderSubClassInfo *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
