#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?TransPort@CMonsterHelper@@SAXPEAVCMonster@@QEAM@Z
 * Address: 0x14015A310

void  CMonsterHelper::TransPort(CMonster *mon, float *tarPos)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // r8@4
  int64_t v5; // [sp+0h] [bp-88h]@1
  _monster_create_setdata Dst; // [sp+30h] [bp-58h]@4
  unsigned int v7; // [sp+74h] [bp-14h]@4
  CMonster *v8; // [sp+90h] [bp+8h]@1
  float *Src; // [sp+98h] [bp+10h]@1

  Src = tarPos;
  v8 = mon;
  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  _monster_create_setdata::_monster_create_setdata(&Dst);
  memcpy_0(Dst.m_fStartPos, Src, 0xCui64);
  Dst.m_nLayerIndex = v8->m_wMapLayerIndex;
  Dst.m_pMap = v8->m_pCurMap;
  Dst.m_pRecordSet = v8->m_pRecordSet;
  Dst.pActiveRec = v8->m_pActiveRec;
  Dst.bDungeon = v8->m_bDungeon;
  Dst.pDumPosition = v8->m_pDumPosition;
  Dst.pParent = CMonsterHierarchy::GetParent(&v8->m_MonHierarcy);
  v7 = ((int ( *)(CMonster *))v8->vfptr->GetHP)(v8);
  Dst.bRobExp = v8->m_bRobExp;
  CMonster::Destroy(v8, 1, 0i64);
  CMonster::Create(v8, &Dst);
  LOBYTE(v4) = 1;
  ((void ( *)(CMonster *, _QWORD, int64_t))v8->vfptr->SetHP)(v8, v7, v4);
}
