#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetDBPostData@CUserDB@@QEAAXHKHEH_KKK_N0@Z
 * Address: 0x1401174D0

void  CUserDB::SetDBPostData(CUserDB *this, int n, unsigned int dwSerial, int nNumber, char byState, int nKey, unsigned int64_t dwDur, unsigned int dwUpt, unsigned int dwGold, bool bUpdateIndex, unsigned int64_t lnUID)
{
  int64_t *v11; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v13; // [sp+0h] [bp-28h]@1
  CUserDB *v14; // [sp+30h] [bp+8h]@1

  v14 = this;
  v11 = &v13;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v11 = -858993460;
    v11 = (int64_t *)((char *)v11 + 4);
  }
  if ( n >= 0 && n < 50 )
  {
    v14->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwPSSerial = dwSerial;
    v14->m_AvatorData.dbPostData.dbPost.m_PostList[n].nNumber = nNumber;
    v14->m_AvatorData.dbPostData.dbPost.m_PostList[n].byState = byState;
    v14->m_AvatorData.dbPostData.dbPost.m_PostList[n].nKey = nKey;
    v14->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwDur = dwDur;
    v14->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwUpt = dwUpt;
    v14->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwGold = dwGold;
    v14->m_AvatorData.dbPostData.dbPost.m_PostList[n].bNew = 0;
    v14->m_AvatorData.dbPostData.dbPost.m_PostList[n].lnUID = lnUID;
    if ( bUpdateIndex )
    {
      v14->m_AvatorData.dbPostData.dbPost.m_PostList[n].bUpdate = 1;
      v14->m_AvatorData.dbPostData.dbPost.m_PostList[n].bUpdateIndex = 1;
      v14->m_AvatorData.dbPostData.dbPost.m_bUpdate = 1;
      v14->m_bDataUpdate = 1;
    }
    memcpy_s(
      (char *)&v14->m_AvatorData_bk.dbPostData + 297 * n,
      0x129ui64,
      (char *)&v14->m_AvatorData.dbPostData + 297 * n,
      0x129ui64);
  }
}
