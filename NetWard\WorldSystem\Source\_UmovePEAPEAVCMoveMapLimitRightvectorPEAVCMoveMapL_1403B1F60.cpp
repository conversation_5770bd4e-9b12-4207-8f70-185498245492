#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Umove@PEAPEAVCMoveMapLimitRight@@@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitRight@@PEAPEAV2@00@Z
 * Address: 0x1403B1F60

CMoveMapLimitRight ** std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Umove<CMoveMapLimitRight * *>(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight **_Ptr)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  return stdext::_Unchecked_uninitialized_move<CMoveMapLimitRight * *,CMoveMapLimitRight * *,std::allocator<CMoveMapLimitRight *>>(
           _First,
           _Last,
           _Ptr,
           &v8->_Alval);
}
