#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_Ufill@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitRight@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x1403AF4E0

CMoveMapLimitRight ** std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Ufill(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, CMoveMapLimitRight **_Ptr, unsigned int64_t _Count, CMoveMapLimitRight *const *_Val)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v8; // [sp+30h] [bp+8h]@1
  CMoveMapLimitRight **_First; // [sp+38h] [bp+10h]@1
  unsigned int64_t _Counta; // [sp+40h] [bp+18h]@1

  _Counta = _Count;
  _First = _Ptr;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  stdext::unchecked_uninitialized_fill_n<CMoveMapLimitRight * *,unsigned int64_t,CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    _Ptr,
    _Count,
    _Val,
    &v8->_Alval);
  return &_First[_Counta];
}
