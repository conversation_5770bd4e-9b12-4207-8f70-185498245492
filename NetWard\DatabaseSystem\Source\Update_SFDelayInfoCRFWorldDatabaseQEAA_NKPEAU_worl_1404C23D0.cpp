#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_SFDelayInfo@CRFWorldDatabase@@QEAA_NKPEAU_worlddb_sf_delay_info@@@Z
 * Address: 0x1404C23D0

bool  CRFWorldDatabase::Update_SFDelayInfo(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_sf_delay_info *pSFDelay)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-168h]@1
  char Dest; // [sp+40h] [bp-128h]@4
  unsigned int64_t v8; // [sp+150h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+170h] [bp+8h]@1
  _worlddb_sf_delay_info *buf; // [sp+180h] [bp+18h]@1

  buf = pSFDelay;
  v9 = this;
  v3 = &v6;
  for ( i = 88i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v8 = (unsigned int64_t)&v6 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pUpdate_SFDelay( %d, ? ) }", dwSerial);
  return CRFNewDatabase::ExecUpdateBinaryQuery((CRFNewDatabase *)&v9->vfptr, &Dest, buf, 130, 0);
}
