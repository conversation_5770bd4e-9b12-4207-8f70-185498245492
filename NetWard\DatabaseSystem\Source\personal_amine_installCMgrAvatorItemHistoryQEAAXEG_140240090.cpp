#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?personal_amine_install@CMgrAvatorItemHistory@@QEAAXEGPEAU_personal_amine_inven_db_load@@PEAD@Z
 * Address: 0x140240090

void  CMgrAvatorItemHistory::personal_amine_install(CMgrAvatorItemHistory *this, char byTblCode, unsigned int16_t wItemIndex, _personal_amine_inven_db_load *pCon, char *szFileName)
{
  int64_t *v5; // rdi@1
  signed int64_t i; // rcx@1
  char *v7; // rax@4
  int v8; // ecx@8
  char *v9; // rax@8
  unsigned int v10; // ecx@8
  size_t v11; // rax@8
  int64_t v12; // [sp+0h] [bp-58h]@1
  char *v13; // [sp+20h] [bp-38h]@8
  unsigned int64_t v14; // [sp+28h] [bp-30h]@8
  char *DstBuf; // [sp+30h] [bp-28h]@4
  int j; // [sp+38h] [bp-20h]@4
  int64_t v17; // [sp+40h] [bp-18h]@8
  int nTableCode; // [sp+48h] [bp-10h]@8
  CMgrAvatorItemHistory *v19; // [sp+60h] [bp+8h]@1
  char v20; // [sp+68h] [bp+10h]@1
  unsigned int16_t v21; // [sp+70h] [bp+18h]@1
  _personal_amine_inven_db_load *v22; // [sp+78h] [bp+20h]@1

  v22 = pCon;
  v21 = wItemIndex;
  v20 = byTblCode;
  v19 = this;
  v5 = &v12;
  for ( i = 20i64; i; --i )
  {
    *(uint32_t*)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  memset_0(sData, 0, 0x4E20ui64);
  v7 = GetItemKorName((unsigned int8_t)v20, v21);
  sprintf_s(sData, 0x4E20ui64, "[PERSONA_AMINE_INSTALL] - %s\r\n", v7);
  DstBuf = &sData[strlen_0(sData)];
  for ( j = 0; j < 40; ++j )
  {
    if ( v22->m_List[j].m_bLoad )
    {
      v17 = 50i64 * j;
      v8 = v22->m_List[j].m_wItemIndex;
      nTableCode = v22->m_List[j].m_byTableCode;
      v9 = GetItemKorName(nTableCode, v8);
      v10 = v22->m_List[j].m_byStorageIndex;
      v14 = v22->m_List[(unsigned int64_t)v17 / 0x32].m_dwDur;
      v13 = v9;
      sprintf_s(DstBuf, 20000 - (DstBuf - sData), "[%02d]%s >> num: %d\r\n", v10);
      v11 = strlen_0(DstBuf);
      DstBuf += v11;
    }
  }
  CMgrAvatorItemHistory::WriteFile(v19, szFileName, sData);
}
