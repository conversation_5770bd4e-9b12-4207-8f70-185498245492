#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Ufill@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitRight@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x14000CF5E

CMoveMapLimitRight ** std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Ufill(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, CMoveMapLimitRight **_Ptr, unsigned int64_t _Count, CMoveMapLimitRight *const *_Val)
{
  return std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Ufill(this, _Ptr, _Count, _Val);
}
