#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Allocate@VCUnmannedTraderRegistItemInfo@@@std@@YAPEAVCUnmannedTraderRegistItemInfo@@_KPEAV1@@Z
 * Address: 0x14000D7F6

CUnmannedTraderRegistItemInfo * std::_Allocate<CUnmannedTraderRegistItemInfo>(unsigned int64_t _Count, CUnmannedTraderRegistItemInfo *__formal)
{
  return std::_Allocate<CUnmannedTraderRegistItemInfo>(_Count, __formal);
}
