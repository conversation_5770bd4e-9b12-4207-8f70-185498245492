#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?LoadDB@CRaceBossWinRate@@QEAA_NXZ
 * Address: 0x14024CB70

bool  CRaceBossWinRate::LoadDB(CRaceBossWinRate *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  bool result; // al@5
  int64_t v4; // [sp+0h] [bp-28h]@1
  CRaceBossWinRate *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( CRaceBossWinRate::LoadBossCurrentWinRate(v5) )
    result = CRaceBossWinRate::LoadBossAccmulationWinRate(v5, 0i64) == 0;
  else
    result = 0;
  return result;
}
