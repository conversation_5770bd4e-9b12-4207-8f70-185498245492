#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CLogTypeDBTaskPool::_CLogTypeDBTaskPool_::_1_::dtor$3
 * Address: 0x1402C20E0

void  CLogTypeDBTaskPool::_CLogTypeDBTaskPool_::_1_::dtor_3(int64_t a1, int64_t a2)
{
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>((std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)(*(uint64_t*)(a2 + 64) + 488i64));
}
