#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_Post@CUserDB@@QEAAXHKHEH_KKK0@Z
 * Address: 0x140117900

void  CUserDB::Update_Post(CUserDB *this, int n, unsigned int dwSerial, int nNumber, char byState, int nKey, unsigned int64_t dwDur, unsigned int dwUpt, unsigned int dwGold, unsigned int64_t lnUID)
{
  if ( n >= 0 && n < 50 )
  {
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwPSSerial = dwSerial;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].nNumber = nNumber;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].byState = byState;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].nKey = nKey;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwDur = dwDur;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwUpt = dwUpt;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwGold = dwGold;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].lnUID = lnUID;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].bUpdate = 1;
    this->m_AvatorData.dbPostData.dbPost.m_bUpdate = 1;
    this->m_bDataUpdate = 1;
  }
}
