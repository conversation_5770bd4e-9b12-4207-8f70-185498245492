#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Destroy_range@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@YAXPEAPEAVCMoveMapLimitRight@@0AEAV?$allocator@PEAVCMoveMapLimitRight@@@0@@Z
 * Address: 0x1403A3C00

void  std::_Destroy_range<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, std::allocator<CMoveMapLimitRight *> *_Al)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-38h]@1
  std::_Scalar_ptr_iterator_tag v6; // [sp+20h] [bp-18h]@4
  CMoveMapLimitRight **__formal; // [sp+40h] [bp+8h]@1
  CMoveMapLimitRight **_Lasta; // [sp+48h] [bp+10h]@1
  std::allocator<CMoveMapLimitRight *> *_Ala; // [sp+50h] [bp+18h]@1

  _Ala = _Al;
  _Lasta = _Last;
  __formal = _First;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v6 = std::_Ptr_cat<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(&__formal, &_Lasta);
  std::_Destroy_range<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(__formal, _Lasta, _Ala, v6);
}
