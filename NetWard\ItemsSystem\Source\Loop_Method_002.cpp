#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Loop@CItemStoreManager@@QEAAXXZ
 * Address: 0x140349350

void  CItemStoreManager::Loop(CItemStoreManager *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-78h]@1
  int j; // [sp+30h] [bp-48h]@6
  int k; // [sp+34h] [bp-44h]@13
  bool *v6; // [sp+38h] [bp-40h]@5
  CItemStore *v7; // [sp+40h] [bp-38h]@5
  int64_t _Time; // [sp+58h] [bp-20h]@5
  int l; // [sp+64h] [bp-14h]@49
  unsigned int v10; // [sp+68h] [bp-10h]@41
  char v11; // [sp+6Ch] [bp-Ch]@41
  CItemStoreManager *v12; // [sp+80h] [bp+8h]@1

  v12 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( CMyTimer::CountingTimer(&v12->m_tmrCheckTime) )
  {
    v6 = 0i64;
    v7 = 0i64;
    time_16(&_Time);
    if ( _Time > v12->m_tNextInitTime )
    {
      for ( j = 0; j < 3; ++j )
      {
        v7 = v12->m_pLimitInitNormalStore[j];
        CItemStore::SetLimitItemInitTime(v7);
        if ( v7->m_nLimitStorageItemNum > 0 )
        {
          CItemStore::InitLimitItemInfo(v7);
          CItemStore::UpdateLimitItemNum(v7, 1);
        }
      }
      for ( j = 0; j < v12->m_nInstanceItemStoreListNum; ++j )
      {
        v6 = &v12->m_InstanceItemStoreList[j].m_bUse;
        for ( k = 0; k < *((uint32_t*)v6 + 2); ++k )
        {
          v7 = (CItemStore *)(*((uint64_t*)v6 + 2) + 120i64 * k);
          CItemStore::SetLimitItemInitTime(v7);
          if ( v7->m_nLimitStorageItemNum > 0 )
          {
            CItemStore::InitLimitItemInfo(v7);
            CItemStore::UpdateLimitItemNum(v7, 1);
          }
        }
      }
      CItemStoreManager::SetNextEnforceInitTime(v12);
    }
    for ( j = 0; j < v12->m_nMapItemStoreListNum; ++j )
    {
      v6 = &v12->m_MapItemStoreList[j].m_bUse;
      for ( k = 0; k < *((uint32_t*)v6 + 2); ++k )
      {
        v7 = (CItemStore *)(*((uint64_t*)v6 + 2) + 120i64 * k);
        if ( v7->m_nLimitStorageItemNum > 0 && v7->m_dwLimitInitTime <= _Time )
        {
          CItemStore::SetLimitItemInitTime(v7);
          CItemStore::InitLimitItemInfo(v7);
          CItemStore::UpdateLimitItemNum(v7, 1);
        }
      }
    }
    for ( j = 0; j < v12->m_nInstanceItemStoreListNum; ++j )
    {
      v6 = &v12->m_InstanceItemStoreList[j].m_bUse;
      if ( *v6 )
      {
        for ( k = 0; k < *((uint32_t*)v6 + 2); ++k )
        {
          v7 = (CItemStore *)(*((uint64_t*)v6 + 2) + 120i64 * k);
          if ( v7->m_nLimitStorageItemNum > 0 && v7->m_dwLimitInitTime <= _Time )
          {
            CItemStore::SetLimitItemInitTime(v7);
            CItemStore::InitLimitItemInfo(v7);
            CItemStore::UpdateLimitItemNum(v7, 1);
          }
        }
      }
    }
    if ( CMyTimer::CountingTimer(&v12->m_tmrSaveTime) )
    {
      v10 = 0;
      v11 = 0;
      for ( j = 0; j < v12->m_nMapItemStoreListNum; ++j )
      {
        v6 = &v12->m_MapItemStoreList[j].m_bUse;
        if ( *v6 )
        {
          for ( k = 0; k < *((uint32_t*)v6 + 2); ++k )
          {
            v7 = (CItemStore *)(*((uint64_t*)v6 + 2) + 120i64 * k);
            if ( v7->m_bUpdate && v7->m_nLimitStorageItemNum > 0 )
            {
              v12->m_Sheet.pStoreList[v10].dwDBSerial = v7->m_dwDBSerial;
              v12->m_Sheet.pStoreList[v10].bNewSerial = 0;
              v12->m_Sheet.pStoreList[v10].byType = v6[1];
              v12->m_Sheet.pStoreList[v10].nTypeSerial = *((uint32_t*)v6 + 1);
              v12->m_Sheet.pStoreList[v10].dwStoreIndex = v7->m_pRec->m_dwIndex;
              v12->m_Sheet.pStoreList[v10].dwLimitInitTime = v7->m_dwLimitInitTime;
              for ( l = 0; l < 16; ++l )
              {
                if ( _INVENKEY::CovDBKey(&v7->m_pLimitStorageItem[l].Key) != -1 )
                {
                  _INVENKEY::operator=(
                    (_INVENKEY *)&v12->m_Sheet.pStoreList[v10].ItemData[l],
                    &v7->m_pLimitStorageItem[l].Key);
                  v12->m_Sheet.pStoreList[v10].ItemData[l].nLimitNum = v7->m_pLimitStorageItem[l].nLimitNum;
                }
              }
              ++v10;
              v7->m_bUpdate = 0;
              v11 = 1;
            }
          }
        }
      }
      for ( j = 0; j < v12->m_nInstanceItemStoreListNum; ++j )
      {
        v6 = &v12->m_InstanceItemStoreList[j].m_bUse;
        if ( *v6 )
        {
          for ( k = 0; k < *((uint32_t*)v6 + 2); ++k )
          {
            v7 = (CItemStore *)(*((uint64_t*)v6 + 2) + 120i64 * k);
            if ( v7->m_bUpdate && v7->m_nLimitStorageItemNum > 0 )
            {
              v12->m_Sheet.pStoreList[v10].dwDBSerial = v7->m_dwDBSerial;
              v12->m_Sheet.pStoreList[v10].bNewSerial = 0;
              v12->m_Sheet.pStoreList[v10].byType = v6[1];
              v12->m_Sheet.pStoreList[v10].nTypeSerial = *((uint32_t*)v6 + 1);
              v12->m_Sheet.pStoreList[v10].dwStoreIndex = v7->m_pRec->m_dwIndex;
              v12->m_Sheet.pStoreList[v10].dwLimitInitTime = v7->m_dwLimitInitTime;
              for ( l = 0; l < 16; ++l )
              {
                if ( _INVENKEY::CovDBKey(&v7->m_pLimitStorageItem[l].Key) != -1 )
                {
                  _INVENKEY::operator=(
                    (_INVENKEY *)&v12->m_Sheet.pStoreList[v10].ItemData[l],
                    &v7->m_pLimitStorageItem[l].Key);
                  v12->m_Sheet.pStoreList[v10].ItemData[l].nLimitNum = v7->m_pLimitStorageItem[l].nLimitNum;
                }
              }
              ++v10;
              v7->m_bUpdate = 0;
              v11 = 1;
            }
          }
        }
      }
      if ( v11 && pkDB )
      {
        v12->m_Sheet.dwCount = v10;
        CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 112, 0i64, 0);
      }
    }
  }
}
