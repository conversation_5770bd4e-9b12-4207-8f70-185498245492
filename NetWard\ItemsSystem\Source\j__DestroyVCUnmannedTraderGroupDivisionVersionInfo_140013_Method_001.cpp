#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Destroy@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@YAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@@Z
 * Address: 0x140013CA5

void  std::_Destroy<CUnmannedTraderGroupDivisionVersionInfo>(CUnmannedTraderGroupDivisionVersionInfo *_Ptr)
{
  std::_Destroy<CUnmannedTraderGroupDivisionVersionInfo>(_Ptr);
}
