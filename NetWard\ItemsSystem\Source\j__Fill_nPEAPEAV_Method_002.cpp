#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Fill_n@PEAPEAVCUnmannedTraderDivisionInfo@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140013BBA

void  std::_Fill_n<CUnmannedTraderDivisionInfo * *,unsigned int64_t,CUnmannedTraderDivisionInfo *,std::random_access_iterator_tag>(CUnmannedTraderDivisionInfo **_First, unsigned int64_t _Count, CUnmannedTraderDivisionInfo *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  std::_Fill_n<CUnmannedTraderDivisionInfo * *,unsigned int64_t,CUnmannedTraderDivisionInfo *,std::random_access_iterator_tag>(
    _First,
    _Count,
    _Val,
    __formal,
    a5);
}
