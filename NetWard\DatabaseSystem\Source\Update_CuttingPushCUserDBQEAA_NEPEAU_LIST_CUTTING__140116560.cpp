#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_CuttingPush@CUserDB@@QEAA_NEPEAU_LIST@_CUTTING_DB_BASE@@@Z
 * Address: 0x140116560

char  CUserDB::Update_CuttingPush(CUserDB *this, char resnum, _CUTTING_DB_BASE::_LIST *plist)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-48h]@1
  int v7; // [sp+20h] [bp-28h]@9
  unsigned int j; // [sp+30h] [bp-18h]@6
  int v9; // [sp+34h] [bp-14h]@8
  CUserDB *v10; // [sp+50h] [bp+8h]@1
  char v11; // [sp+58h] [bp+10h]@1
  _CUTTING_DB_BASE::_LIST *Src; // [sp+60h] [bp+18h]@1

  Src = plist;
  v11 = resnum;
  v10 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned int8_t)resnum <= 20 )
  {
    for ( j = 0; (signed int)j < (unsigned int8_t)v11; ++j )
    {
      v9 = Src[j].Key.wItemIndex;
      if ( v9 >= GetMaxResKind() )
      {
        v7 = Src[j].Key.wItemIndex;
        CLogFile::Write(
          &stru_1799C8E78,
          "%s : Update_CuttingPush(CODE) List[%d].byResIndex (%d) => failed ",
          v10->m_aszAvatorName,
          j);
        return 0;
      }
    }
    _CUTTING_DB_BASE::Init(&v10->m_AvatorData.dbCutting);
    v10->m_AvatorData.dbCutting.m_byLeftNum = v11;
    memcpy_0(v10->m_AvatorData.dbCutting.m_List, Src, 8i64 * (unsigned int8_t)v11);
    v10->m_bDataUpdate = 1;
    result = 1;
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_CuttingPush(CODE) byResNum (%d) => failed ",
      v10->m_aszAvatorName,
      (unsigned int8_t)resnum);
    result = 0;
  }
  return result;
}
