#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV1@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@stdext@@YAPEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@Z
 * Address: 0x14000D242

CUnmannedTraderGroupDivisionVersionInfo * stdext::_Unchecked_uninitialized_move<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(CUnmannedTraderGroupDivisionVersionInfo *_First, CUnmannedTraderGroupDivisionVersionInfo *_Last, CUnmannedTraderGroupDivisionVersionInfo *_Dest, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
