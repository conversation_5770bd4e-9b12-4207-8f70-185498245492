#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_RankInGuild_Step5@CRFWorldDatabase@@QEAA_NKPEAU_worlddb_rankinguild_info@@@Z
 * Address: 0x1404B8D50

char  CRFWorldDatabase::Update_RankInGuild_Step5(CRFWorldDatabase *this, unsigned int dwGuildSerial, _worlddb_rankinguild_info *pGuildMemberRankData)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@6
  int64_t v6; // [sp+0h] [bp-768h]@1
  void *SQLStmt; // [sp+20h] [bp-748h]@11
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-740h]@15
  SQLLEN v9; // [sp+38h] [bp-730h]@15
  int16_t v10; // [sp+44h] [bp-724h]@7
  char DstBuf; // [sp+60h] [bp-708h]@4
  char v12; // [sp+61h] [bp-707h]@4
  int j; // [sp+464h] [bp-304h]@4
  int TargetValue; // [sp+474h] [bp-2F4h]@4
  int Dst; // [sp+4A0h] [bp-2C8h]@4
  char v16[624]; // [sp+4A4h] [bp-2C4h]@15
  int v17; // [sp+714h] [bp-54h]@26
  double v18; // [sp+738h] [bp-30h]@26
  int k; // [sp+744h] [bp-24h]@32
  unsigned int64_t v20; // [sp+750h] [bp-18h]@4
  CRFWorldDatabase *v21; // [sp+770h] [bp+8h]@1
  unsigned int v22; // [sp+778h] [bp+10h]@1
  _worlddb_rankinguild_info *v23; // [sp+780h] [bp+18h]@1

  v23 = pGuildMemberRankData;
  v22 = dwGuildSerial;
  v21 = this;
  v3 = &v6;
  for ( i = 472i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v20 = (unsigned int64_t)&v6 ^ _security_cookie;
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v21->vfptr,
    "CRFWorldDatabase::Update_RankInGuild_Step5( dwGuildSerial(%u), pGuildMemberRankData ) : Start Set Return Result!",
    dwGuildSerial);
  DstBuf = 0;
  memset(&v12, 0, 0x3FFui64);
  j = 0;
  TargetValue = 2;
  memset_0(&Dst, 0, 0x258ui64);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v21->vfptr,
    "CRFWorldDatabase::Update_RankInGuild_Step5( dwGuildSerial(%u), pGuildMemberRankData ) : Start Get Grade from #tbl_RankInGuildCom",
    v22);
  sprintf_s(&DstBuf, 0x400ui64, "select top %u serial, Grade from #tbl_RankInGuildCom order by Grade", 50i64);
  if ( v21->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v21->vfptr) )
  {
    v10 = SQLExecDirect_0(v21->m_hStmtSelect, &DstBuf, -3);
    if ( v10 && v10 != 1 )
    {
      CRFWorldDatabase::Update_RankInGuild_Step6(v21);
      if ( v10 == 100 )
      {
        CRFNewDatabase::FmtLog(
          (CRFNewDatabase *)&v21->vfptr,
          "CRFWorldDatabase::Update_RankInGuild_Step5( dwGuildSerial(%u), pGuildMemberRankData ) : %s : NO_DATA!",
          v22,
          &DstBuf);
        result = 0;
      }
      else
      {
        CRFNewDatabase::FmtLog(
          (CRFNewDatabase *)&v21->vfptr,
          "CRFWorldDatabase::Update_RankInGuild_Step5( dwGuildSerial(%u), pGuildMemberRankData ) : %s : Sql Error!",
          v22,
          &DstBuf);
        SQLStmt = v21->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v21->vfptr, v10, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v10, v21->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      for ( j = 0; ; ++j )
      {
        v10 = SQLFetch_0(v21->m_hStmtSelect);
        if ( v10 )
        {
          if ( v10 != 1 )
            break;
        }
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v21->m_hStmtSelect, 1u, 4, &Dst + 3 * j, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v21->m_hStmtSelect, 2u, 4, &TargetValue, 0i64, &v9);
        v16[12 * j] = TargetValue;
      }
      if ( v21->m_hStmtSelect )
        SQLCloseCursor_0(v21->m_hStmtSelect);
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v21->vfptr,
        "CRFWorldDatabase::Update_RankInGuild_Step5( dwGuildSerial(%u), pGuildMemberRankData ) : End Get Grade from #tbl_RankInGuildCom",
        v22);
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v21->vfptr,
        "CRFWorldDatabase::Update_RankInGuild_Step5( dwGuildSerial(%u), pGuildMemberRankData ) : Start Get Grade from #tb"
        "l_RankInGuildAll",
        v22);
      sprintf_s<1024>(
        (char (*)[1024])&DstBuf,
        "select serial, lv, Pvppoint, CurGrade from #tbl_RankInGuildAll order by NewRank");
      if ( v21->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v21->vfptr) )
      {
        v10 = SQLExecDirect_0(v21->m_hStmtSelect, &DstBuf, -3);
        if ( v10 && v10 != 1 )
        {
          CRFWorldDatabase::Update_RankInGuild_Step6(v21);
          if ( v10 == 100 )
          {
            CRFNewDatabase::FmtLog(
              (CRFNewDatabase *)&v21->vfptr,
              "CRFWorldDatabase::Update_RankInGuild_Step5( dwGuildSerial(%u), pGuildMemberRankData ) : %s : NO_DATA!",
              v22,
              &DstBuf);
            result = 0;
          }
          else
          {
            CRFNewDatabase::FmtLog(
              (CRFNewDatabase *)&v21->vfptr,
              "CRFWorldDatabase::Update_RankInGuild_Step5( dwGuildSerial(%u), pGuildMemberRankData ) : %s : Sql Error!",
              v22,
              &DstBuf);
            SQLStmt = v21->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v21->vfptr, v10, &DstBuf, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v10, v21->m_hStmtSelect);
            result = 0;
          }
        }
        else
        {
          j = 0;
          v17 = 0;
          v18 = 0.0;
          while ( 1 )
          {
            v10 = SQLFetch_0(v21->m_hStmtSelect);
            if ( v10 )
            {
              if ( v10 != 1 )
                break;
            }
            StrLen_or_IndPtr = &v9;
            SQLStmt = 0i64;
            v10 = SQLGetData_0(v21->m_hStmtSelect, 1u, 4, &v23->MemberData[j], 0i64, &v9);
            StrLen_or_IndPtr = &v9;
            SQLStmt = 0i64;
            v10 = SQLGetData_0(v21->m_hStmtSelect, 2u, -18, &v17, 0i64, &v9);
            StrLen_or_IndPtr = &v9;
            SQLStmt = 0i64;
            v10 = SQLGetData_0(v21->m_hStmtSelect, 3u, 8, &v18, 0i64, &v9);
            StrLen_or_IndPtr = &v9;
            SQLStmt = 0i64;
            v10 = SQLGetData_0(v21->m_hStmtSelect, 4u, 4, &v23->MemberData[j].byGrade, 0i64, &v9);
            v23->MemberData[j].byLv = v17;
            v23->MemberData[j].dwPvpPoint = (signed int)floor(v18);
            if ( v23->MemberData[j].byGrade == 2 )
              v23->MemberData[j].byGrade = 2;
            else
              v23->MemberData[j].byGrade = 0;
            for ( k = 0; k < 50; ++k )
            {
              if ( v23->MemberData[j].dwSerial == *(&Dst + 3 * k) )
              {
                v23->MemberData[j].byGrade = v16[12 * k];
                break;
              }
            }
            ++j;
          }
          v23->wRecordCount = j;
          if ( v21->m_hStmtSelect )
            SQLCloseCursor_0(v21->m_hStmtSelect);
          CRFNewDatabase::FmtLog(
            (CRFNewDatabase *)&v21->vfptr,
            "CRFWorldDatabase::Update_RankInGuild_Step5( dwGuildSerial(%u), pGuildMemberRankData ) : End Set Return Result!",
            v22);
          result = 1;
        }
      }
      else
      {
        CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v21->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
        result = 0;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v21->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 0;
  }
  return result;
}
