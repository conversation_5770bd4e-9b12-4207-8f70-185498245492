#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Fill_n@PEAPEAVCUnmannedTraderSortType@@_KPEAV1@@std@@YAXPEAPEAVCUnmannedTraderSortType@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140007E55

void  std::_Fill_n<CUnmannedTraderSortType * *,unsigned int64_t,CUnmannedTraderSortType *>(CUnmannedTraderSortType **_First, unsigned int64_t _Count, CUnmannedTraderSortType *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  std::_Fill_n<CUnmannedTraderSortType * *,unsigned int64_t,CUnmannedTraderSortType *>(_First, _Count, _Val, __formal);
}
