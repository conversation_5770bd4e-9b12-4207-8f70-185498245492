#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_LoadSafe@CMapData@@AEAA_NPEAD@Z
 * Address: 0x140183F80

char  CMapData::_LoadSafe(CMapData *this, char *pszMapCode)
{
  int64_t *v2; // rdi@1
  signed int64_t j; // rcx@1
  char result; // al@5
  int64_t v5; // [sp+0h] [bp-108h]@1
  char Dest; // [sp+30h] [bp-D8h]@4
  int i; // [sp+B4h] [bp-54h]@13
  _dummy_position *pDumPos; // [sp+B8h] [bp-50h]@15
  int __n[2]; // [sp+C8h] [bp-40h]@10
  void *v10; // [sp+D0h] [bp-38h]@13
  void *__t; // [sp+D8h] [bp-30h]@10
  int64_t v12; // [sp+E0h] [bp-28h]@4
  void *v13; // [sp+E8h] [bp-20h]@11
  unsigned int64_t v14; // [sp+F0h] [bp-18h]@4
  CMapData *v15; // [sp+110h] [bp+8h]@1

  v15 = this;
  v2 = &v5;
  for ( j = 64i64; j; --j )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v12 = -2i64;
  v14 = (unsigned int64_t)&v5 ^ _security_cookie;
  sprintf(&Dest, ".\\map\\%s\\%s.spt", pszMapCode, pszMapCode);
  if ( CDummyPosTable::LoadDummyPosition(&v15->m_tbSafeDumPos, &Dest, "*sa") )
  {
    if ( CMapData::ConvertLocalToWorldDummy(v15, &v15->m_tbSafeDumPos, 0) )
    {
      v15->m_nSafeDumNum = CDummyPosTable::GetRecordNum(&v15->m_tbSafeDumPos);
      if ( v15->m_nSafeDumNum )
      {
        *(uint64_t*)__n = v15->m_nSafeDumNum;
        __t = operator new[](saturated_mul(8ui64, *(unsigned int64_t *)__n));
        if ( __t )
        {
          `vector constructor iterator'(__t, 8ui64, __n[0], (void *( *)(void *))_safe_dummy::_safe_dummy);
          v13 = __t;
        }
        else
        {
          v13 = 0i64;
        }
        v10 = v13;
        v15->m_pSafeDummy = (_safe_dummy *)v13;
        for ( i = 0; i < v15->m_nSafeDumNum; ++i )
        {
          pDumPos = CDummyPosTable::GetRecord(&v15->m_tbSafeDumPos, i);
          _safe_dummy::SetDummy(&v15->m_pSafeDummy[i], pDumPos);
          CMapData::CheckCenterPosDummy(v15, pDumPos);
        }
        result = 1;
      }
      else
      {
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    MyMessageBox("CMapData Error", "m_tbSafeDumPos.LoadDummyPosition(%s) == false", &Dest);
    result = 0;
  }
  return result;
}
