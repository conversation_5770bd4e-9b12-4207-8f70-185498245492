#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?Search@CUnmannedTraderUserInfo@@QEAAXEPEAU_unmannedtrader_search_list_request_clzo@@PEAVCLogFile@@@Z
 * Address: 0x1400096C4

void  CUnmannedTraderUserInfo::Search(CUnmannedTraderUserInfo *this, char byType, _unmannedtrader_search_list_request_clzo *pRequest, CLogFile *pkLogger)
{
  CUnmannedTraderUserInfo::Search(this, byType, pRequest, pkLogger);
}
