#!/usr/bin/env python3
"""
NetWard RF Online Server - Manual Editing Helper Tool
Assists with safe manual editing of decompiled C++ files
"""

import os
import shutil
import datetime
from pathlib import Path
from typing import Dict, List, Optional

class ManualEditingHelper:
    """Helper tool for safe manual editing workflow"""
    
    def __init__(self, project_root: str = "NetWard"):
        self.project_root = Path(project_root)
        self.source_dir = self.project_root / "ItemsSystem" / "Source"
        self.backup_dir = self.project_root / "Backups"
        self.progress_file = self.project_root / "editing_progress.txt"
        
        # File categories for systematic editing
        self.file_categories = {
            "Tier1_Core": [
                "AT_Get.cpp", "AT_Set.cpp", "AT_Init.cpp",
                "IT_Get.cpp", "IT_Set.cpp", "IT_Init.cpp", 
                "GL_Get.cpp", "GL_Set.cpp", "GL_Init.cpp",
                "UT_Get.cpp", "UT_Set.cpp", "UT_Init.cpp"
            ],
            "Tier2_AutoTrader": ["AT_*.cpp"],
            "Tier2_Items": ["IT_*.cpp"],
            "Tier2_Global": ["GL_*.cpp"],
            "Tier3_Character": ["AN_*.cpp", "CH_*.cpp"],
            "Tier3_Network": ["NW_*.cpp"],
            "Tier4_Jump": ["j_*.cpp"]
        }
        
    def create_daily_backup(self, files_to_edit: List[str]) -> str:
        """Create backup for today's editing session"""
        today = datetime.date.today().strftime("%Y-%m-%d")
        daily_backup_dir = self.backup_dir / "Daily" / today
        daily_backup_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 Creating daily backup: {daily_backup_dir}")
        
        for file_name in files_to_edit:
            source_file = self.source_dir / file_name
            if source_file.exists():
                backup_file = daily_backup_dir / file_name
                shutil.copy2(source_file, backup_file)
                print(f"  ✅ Backed up: {file_name}")
            else:
                print(f"  ⚠️  File not found: {file_name}")
        
        return str(daily_backup_dir)
    
    def get_next_files_to_edit(self, count: int = 25) -> List[str]:
        """Get the next batch of files to edit based on progress"""
        completed_files = self.get_completed_files()
        all_files = list(self.source_dir.glob("*.cpp"))
        
        # Filter out completed files
        remaining_files = [
            f.name for f in all_files 
            if f.name not in completed_files
        ]
        
        # Sort by priority (Tier 1 first, then alphabetically)
        remaining_files.sort(key=self.get_file_priority)
        
        return remaining_files[:count]
    
    def get_file_priority(self, filename: str) -> int:
        """Get priority score for file (lower = higher priority)"""
        if any(filename.startswith(prefix) for prefix in ["AT_Get", "IT_Get", "GL_Get", "UT_Get"]):
            return 1  # Highest priority
        elif any(filename.startswith(prefix) for prefix in ["AT_Set", "IT_Set", "GL_Set", "UT_Set"]):
            return 2
        elif any(filename.startswith(prefix) for prefix in ["AT_Init", "IT_Init", "GL_Init", "UT_Init"]):
            return 3
        elif filename.startswith("AT_"):
            return 10
        elif filename.startswith("IT_"):
            return 11
        elif filename.startswith("GL_"):
            return 12
        elif filename.startswith("UT_"):
            return 13
        elif filename.startswith("AN_"):
            return 20
        elif filename.startswith("CH_"):
            return 21
        elif filename.startswith("NW_"):
            return 22
        elif filename.startswith("j_"):
            return 100  # Lowest priority (jump functions)
        else:
            return 50  # Medium priority
    
    def analyze_file(self, filename: str) -> Dict:
        """Analyze a decompiled file and extract key information"""
        file_path = self.source_dir / filename
        
        if not file_path.exists():
            return {"error": f"File {filename} not found"}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        analysis = {
            "filename": filename,
            "system_type": self.determine_system_type(filename),
            "original_function": self.extract_original_function(content),
            "original_address": self.extract_original_address(content),
            "suggested_namespace": self.suggest_namespace(filename),
            "suggested_class": self.suggest_class_name(filename),
            "suggested_method": self.suggest_method_name(filename),
            "required_headers": self.suggest_headers(filename)
        }
        
        return analysis
    
    def determine_system_type(self, filename: str) -> str:
        """Determine the system type from filename"""
        if filename.startswith("AT_"):
            return "AutoTrader"
        elif filename.startswith("IT_"):
            return "Items"
        elif filename.startswith("GL_"):
            return "Global"
        elif filename.startswith("UT_"):
            return "Utility"
        elif filename.startswith("AN_"):
            return "Character"
        elif filename.startswith("CH_"):
            return "Character"
        elif filename.startswith("NW_"):
            return "Network"
        elif filename.startswith("IS_"):
            return "ItemStore"
        elif filename.startswith("j_"):
            return "Jump"
        else:
            return "Unknown"
    
    def extract_original_function(self, content: str) -> str:
        """Extract original function signature from decompiled code"""
        import re
        pattern = r'Function:\s*([^\n]+)'
        match = re.search(pattern, content)
        return match.group(1).strip() if match else "Unknown"
    
    def extract_original_address(self, content: str) -> str:
        """Extract original memory address from decompiled code"""
        import re
        pattern = r'Address:\s*([^\n]+)'
        match = re.search(pattern, content)
        return match.group(1).strip() if match else "Unknown"
    
    def suggest_namespace(self, filename: str) -> str:
        """Suggest appropriate namespace"""
        system = self.determine_system_type(filename)
        return f"NetWard::{system}"
    
    def suggest_class_name(self, filename: str) -> str:
        """Suggest clean class name"""
        system = self.determine_system_type(filename)
        if system == "AutoTrader":
            return "AutoTraderManager"
        elif system == "Items":
            return "ItemManager"
        elif system == "Character":
            return "CharacterManager"
        elif system == "Network":
            return "NetworkManager"
        else:
            return f"{system}Manager"
    
    def suggest_method_name(self, filename: str) -> str:
        """Suggest clean method name"""
        base_name = filename.replace(".cpp", "")
        parts = base_name.split("_")
        
        if len(parts) >= 2:
            action = parts[1]  # Get, Set, Init, etc.
            if action == "Get":
                return "GetData"
            elif action == "Set":
                return "SetData"
            elif action == "Init":
                return "Initialize"
            elif action == "Upd":
                return "Update"
            else:
                return action
        
        return "ProcessData"
    
    def suggest_headers(self, filename: str) -> List[str]:
        """Suggest required header files"""
        system = self.determine_system_type(filename)
        
        headers = [
            f"../../Headers/{system}/{system}Core.h",
            "../../Headers/Common/NetWardTypes.h"
        ]
        
        if system in ["AutoTrader", "Items"]:
            headers.append("../../Headers/Data/RecordManager.h")
        
        headers.extend([
            "<memory>",
            "<algorithm>"
        ])
        
        return headers
    
    def generate_template(self, filename: str) -> str:
        """Generate editing template for a file"""
        analysis = self.analyze_file(filename)
        
        template = f"""// NetWard RF Online Server - {analysis['system_type']} System
// File: {filename} - {self.get_file_description(filename)}
// Compatible with Visual Studio 2022 (C++20)
// Original Function: {analysis['original_function']}
// Original Address: {analysis['original_address']}

#pragma once
{chr(10).join(f'#include "{header}"' if not header.startswith('<') else f'#include {header}' for header in analysis['required_headers'])}

namespace {analysis['suggested_namespace']} {{

/**
 * @brief [DESCRIBE WHAT THIS FUNCTION DOES]
 * @param [PARAM1] [PARAM1_DESCRIPTION]
 * @return [RETURN_DESCRIPTION]
 * 
 * @details [DETAILED_EXPLANATION_OF_FUNCTIONALITY]
 * @note Original address: {analysis['original_address']}
 */
[RETURN_TYPE] {analysis['suggested_class']}::{analysis['suggested_method']}([PARAMETERS]) noexcept
{{
    // TODO: Implement the logic from the original function
    // 
    // Original logic analysis:
    // 1. [STEP_1]
    // 2. [STEP_2] 
    // 3. [STEP_3]
    
    return [DEFAULT_RETURN_VALUE];
}}

}} // namespace {analysis['suggested_namespace']}
"""
        return template
    
    def get_file_description(self, filename: str) -> str:
        """Get description for the file"""
        if "Get" in filename:
            return "Data retrieval functions"
        elif "Set" in filename:
            return "Data modification functions"
        elif "Init" in filename:
            return "Initialization functions"
        elif "Upd" in filename:
            return "Update functions"
        else:
            return "System functions"
    
    def mark_file_completed(self, filename: str):
        """Mark a file as completed"""
        with open(self.progress_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"{timestamp} - COMPLETED: {filename}\n")
        
        print(f"✅ Marked as completed: {filename}")
    
    def get_completed_files(self) -> set:
        """Get list of completed files"""
        if not self.progress_file.exists():
            return set()
        
        completed = set()
        with open(self.progress_file, 'r', encoding='utf-8') as f:
            for line in f:
                if "COMPLETED:" in line:
                    filename = line.split("COMPLETED:")[-1].strip()
                    completed.add(filename)
        
        return completed
    
    def get_progress_stats(self) -> Dict:
        """Get current progress statistics"""
        all_files = list(self.source_dir.glob("*.cpp"))
        completed_files = self.get_completed_files()
        
        total_count = len(all_files)
        completed_count = len(completed_files)
        remaining_count = total_count - completed_count
        
        progress_percent = (completed_count / total_count * 100) if total_count > 0 else 0
        
        return {
            "total_files": total_count,
            "completed_files": completed_count,
            "remaining_files": remaining_count,
            "progress_percent": progress_percent
        }

def main():
    """Main helper function"""
    helper = ManualEditingHelper()
    
    print("🎮 NetWard Manual Editing Helper")
    print("=" * 50)
    
    # Show current progress
    stats = helper.get_progress_stats()
    print(f"📊 Progress: {stats['completed_files']}/{stats['total_files']} files ({stats['progress_percent']:.1f}%)")
    print(f"📋 Remaining: {stats['remaining_files']} files")
    print()
    
    # Get next files to edit
    next_files = helper.get_next_files_to_edit(25)
    print(f"📝 Next 25 files to edit:")
    for i, filename in enumerate(next_files, 1):
        print(f"  {i:2d}. {filename}")
    print()
    
    # Create backup for today's session
    if next_files:
        backup_dir = helper.create_daily_backup(next_files)
        print(f"💾 Backup created: {backup_dir}")
        print()
    
    # Show analysis for first file
    if next_files:
        first_file = next_files[0]
        print(f"🔍 Analysis for {first_file}:")
        analysis = helper.analyze_file(first_file)
        for key, value in analysis.items():
            if key != "filename":
                print(f"  {key}: {value}")
        print()
        
        # Generate template
        template = helper.generate_template(first_file)
        template_file = helper.project_root / "Templates" / f"{first_file}.template"
        template_file.parent.mkdir(exist_ok=True)
        
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template)
        
        print(f"📄 Template generated: {template_file}")

if __name__ == "__main__":
    main()
