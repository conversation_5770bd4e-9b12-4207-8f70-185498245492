#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Move_cat@PEAPEAVCUnmannedTraderSubClassInfo@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAPEAVCUnmannedTraderSubClassInfo@@@Z
 * Address: 0x14000C955

std::_Undefined_move_tag  std::_Move_cat<CUnmannedTraderSubClassInfo * *>(CUnmannedTraderSubClassInfo **const *__formal)
{
  return std::_Move_cat<CUnmannedTraderSubClassInfo * *>(__formal);
}
