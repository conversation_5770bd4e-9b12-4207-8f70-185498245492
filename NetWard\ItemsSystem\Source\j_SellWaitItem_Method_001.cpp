#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?SellWaitItem@CUnmannedTraderRegistItemInfo@@QEAAEGPEAVCLogFile@@_JAEAE@Z
 * Address: 0x1400035FD

char  CUnmannedTraderRegistItemInfo::SellWaitItem(CUnmannedTraderRegistItemInfo *this, unsigned int16_t wInx, CLogFile *pkLogger, int64_t tResultTime, char *byStorageInx)
{
  return CUnmannedTraderRegistItemInfo::SellWaitItem(this, wInx, pkLogger, tResultTime, byStorageInx);
}
