#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Allocate@VCUnmannedTraderUserInfo@@@std@@YAPEAVCUnmannedTraderUserInfo@@_KPEAV1@@Z
 * Address: 0x1400139CB

CUnmannedTraderUserInfo * std::_Allocate<CUnmannedTraderUserInfo>(unsigned int64_t _Count, CUnmannedTraderUserInfo *__formal)
{
  return std::_Allocate<CUnmannedTraderUserInfo>(_Count, __formal);
}
