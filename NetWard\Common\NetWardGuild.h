#pragma once

#ifndef NETWARD_GUILD_H
#define NETWARD_GUILD_H

// NetWard Guild System Header
// Generated from RF Online ZoneServerUD_x64 decompiled source

#include <windows.h>
#include <stdint.h>
#include <memory>
#include <vector>
#include <string>

// Forward declarations
struct CGuild;
struct CGuildListVtbl;
struct GUILD_BATTLE::CGuildBattleVtbl;
struct GUILD_BATTLE::CGuildBattleStateListVtbl;
struct CGuildVtbl;
struct CGuildRoomInfo;
struct CGuildList;
struct CGuildRoomSystem;
struct _qry_case_joinacguild;
struct CGuildMasterEffect;
struct GUILD_BATTLE::CGuildBattle;
struct GUILD_BATTLE::CGuildBattleState;
struct GUILD_BATTLE::CGuildBattleStateVtbl;
struct GUILD_BATTLE::CGuildBattleLogger;
struct GUILD_BATTLE::CGuildBattleRewardItem;
struct GUILD_BATTLE::CGuildBattleRewardItemManager;
struct GUILD_BATTLE::CGuildBattleSchedule;
struct GUILD_BATTLE::CGuildBattleSchedulePool;
struct GUILD_BATTLE::CGuildBattleReservedSchedule;
struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup;
struct GUILD_BATTLE::CGuildBattleScheduleManager;
struct GUILD_BATTLE::CGuildBattleRankManager;
struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager;

// Class definitions

// CGuild
struct CGuild;
struct CGuildVtbl;

// CGuildListVtbl
struct CGuildListVtbl
{
 void *( *__vecDelDtor)(CGuildList *this, unsigned int);
};

// GUILD_BATTLE::CGuildBattleVtbl
struct GUILD_BATTLE::CGuildBattleVtbl
{
 int ( *GetObjType)(GUILD_BATTLE::CGuildBattle *this);
};

// GUILD_BATTLE::CGuildBattleStateListVtbl
struct GUILD_BATTLE::CGuildBattleStateListVtbl
{
 void ( *SetNextState)(GUILD_BATTLE::CGuildBattleStateList *this);
};

// CGuildVtbl
struct CGuildVtbl
{
 void *( *__vecDelDtor)(CGuild *this, unsigned int);
};

// CGuildRoomInfo
struct CGuildRoomInfo
{
 bool m_bRent;
 char m_byRoomType;
 char m_byRace;
 int m_iGuildIdx;
 unsigned int m_dwGuildSerial;
 int m_timeBegin;
 int m_timer;
 std::vector<RoomCharInfo,std::allocator<RoomCharInfo> > m_vecRoomMember;
 CMapData *m_pRoomMap;
 unsigned int16_t m_wRoomMapLayer;
 _LAYER_SET *m_pLayerSet;
 _dummy_position *m_pRoomStartDummy;
};

// CGuildList
struct CGuildList
{
 CGuildListVtbl *vfptr;
 bool m_bInit;
 char m_byMaxPage[3];
 __guild_list_page *m_pGuildList[3];
};

// CGuildRoomSystem
struct CGuildRoomSystem
{
 std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo> > m_vecGuildRoom;
 CMapData *m_pRoomMap[3][2];
};

// _qry_case_joinacguild
struct _qry_case_joinacguild
{
 unsigned int in_guildindex;
 unsigned int in_guildserial;
 unsigned int in_applierindex;
 unsigned int in_applierserial;
 unsigned int in_accepterserial;
 char in_Grade;
 int in_MemberNum;
};

// CGuildMasterEffect
struct CGuildMasterEffect
{
 char m_byAdjustableGrade;
 _guild_master_advantage_info m_EffectData[8];
};

// GUILD_BATTLE::CGuildBattle
struct GUILD_BATTLE::CGuildBattle
{
 GUILD_BATTLE::CGuildBattleVtbl *vfptr;
};

// GUILD_BATTLE::CGuildBattleState
struct GUILD_BATTLE::CGuildBattleState
{
 GUILD_BATTLE::CGuildBattleStateVtbl *vfptr;
};

// GUILD_BATTLE::CGuildBattleStateVtbl
struct GUILD_BATTLE::CGuildBattleStateVtbl
{
 int ( *Enter)(GUILD_BATTLE::CGuildBattleState *this, GUILD_BATTLE::CGuildBattle *);
 int ( *Loop)(GUILD_BATTLE::CGuildBattleState *this, GUILD_BATTLE::CGuildBattle *);
 int ( *Fin)(GUILD_BATTLE::CGuildBattleState *this, GUILD_BATTLE::CGuildBattle *);
 ATL::CTimeSpan *( *GetTerm)(GUILD_BATTLE::CGuildBattleState *this, ATL::CTimeSpan *result);
};

// GUILD_BATTLE::CGuildBattleLogger
struct GUILD_BATTLE::CGuildBattleLogger
{
 CLogFile *m_pkLogger;
};

// GUILD_BATTLE::CGuildBattleRewardItem
struct GUILD_BATTLE::CGuildBattleRewardItem
{
 char m_ucD;
 char m_ucTableCode;
 _base_fld *m_pFld;
};

// GUILD_BATTLE::CGuildBattleRewardItemManager
struct GUILD_BATTLE::CGuildBattleRewardItemManager
{
 std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > m_kItem;
};

// GUILD_BATTLE::CGuildBattleSchedule
struct GUILD_BATTLE::CGuildBattleSchedule
{
 unsigned int m_dwScheduleID;
 GUILD_BATTLE::CGuildBattleSchedule::GS_STATE m_eState;
 ATL::CTime m_kNextStartTime;
 ATL::CTime m_kBattleStartTime;
 ATL::CTimeSpan m_kBattleTime;
 GUILD_BATTLE::CGuildBattleStateList *m_pkStateList;
};

// GUILD_BATTLE::CGuildBattleSchedulePool
struct GUILD_BATTLE::CGuildBattleSchedulePool
{
 unsigned int m_uiMapCnt;
 unsigned int m_dwMaxScheduleCnt;
 GUILD_BATTLE::CGuildBattleSchedule **m_ppkSchedule;
};

// GUILD_BATTLE::CGuildBattleReservedSchedule
struct GUILD_BATTLE::CGuildBattleReservedSchedule
{
 unsigned int m_uiScheduleListID;
 bool m_bDone;
 unsigned int m_uiCurScheduleInx;
 bool m_bUseField[23];
 GUILD_BATTLE::CGuildBattleSchedule *m_pkSchedule[23];
};

// GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup
struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup
{
 bool m_bDone;
 unsigned int m_uiDayInx;
 unsigned int m_uiMapCnt;
 GUILD_BATTLE::CGuildBattleReservedSchedule **m_ppkReservedSchedule;
};

// GUILD_BATTLE::CGuildBattleScheduleManager
struct GUILD_BATTLE::CGuildBattleScheduleManager
{
 bool m_bLoad;
 ATL::CTime *m_pkOldDayTime;
 CMyTimer *m_pkTimer;
 unsigned int m_uiMapCnt;
 GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup m_kSchdule[2];
 GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *m_pkTodaySchedule;
 GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *m_pkTomorrowSchedule;
};

// GUILD_BATTLE::CGuildBattleRankManager
struct GUILD_BATTLE::CGuildBattleRankManager
{
 unsigned int m_dwVer[3];
 unsigned int m_dwGuildSerial[3][30][10];
 _guild_battle_rank_list_result_zocl **m_ppkList;
};

// GUILD_BATTLE::CGuildBattleReservedScheduleListManager
struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager
{
 unsigned int m_uiMapCnt;
 GUILD_BATTLE::CReservedGuildScheduleDayGroup m_kList[2];
 GUILD_BATTLE::CReservedGuildScheduleDayGroup *m_pkToday;
 GUILD_BATTLE::CReservedGuildScheduleDayGroup *m_pkTomorrow;
};


#endif // NETWARD_GUILD_H
