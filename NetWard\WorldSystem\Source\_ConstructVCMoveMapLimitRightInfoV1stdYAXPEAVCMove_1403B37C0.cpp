#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Construct@VCMoveMapLimitRightInfo@@V1@@std@@YAXPEAVCMoveMapLimitRightInfo@@AEBV1@@Z
 * Address: 0x1403B37C0

void  std::_Construct<CMoveMapLimitRightInfo,CMoveMapLimitRightInfo>(CMoveMapLimitRightInfo *_Ptr, CMoveMapLimitRightInfo *_Val)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-58h]@1
  void *_Where; // [sp+20h] [bp-38h]@4
  CMoveMapLimitRightInfo *v6; // [sp+30h] [bp-28h]@4
  int64_t v7; // [sp+38h] [bp-20h]@4
  CMoveMapLimitRightInfo *v8; // [sp+60h] [bp+8h]@1
  CMoveMapLimitRightInfo *__that; // [sp+68h] [bp+10h]@1

  __that = _Val;
  v8 = _Ptr;
  v2 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v7 = -2i64;
  _Where = v8;
  v6 = (CMoveMapLimitRightInfo *)operator new(0x28ui64, v8);
  if ( v6 )
    CMoveMapLimitRightInfo::CMoveMapLimitRightInfo(v6, __that);
}
