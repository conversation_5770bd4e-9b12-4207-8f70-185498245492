#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Xlen_::_1_::dtor$0
 * Address: 0x1403A2E30

int  std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Xlen_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  return std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(a2 + 104);
}
