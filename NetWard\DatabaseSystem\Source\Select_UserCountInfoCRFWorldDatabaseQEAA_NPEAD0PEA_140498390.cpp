#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Select_UserCountInfo@CRFWorldDatabase@@QEAA_NPEAD0PEAU_worlddb_user_count_info@@@Z
 * Address: 0x140498390

char  CRFWorldDatabase::Select_UserCountInfo(CRFWorldDatabase *this, char *szStartDate, char *szEndDate, _worlddb_user_count_info *pUserCountData)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@8
  int64_t v7; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@16
  SQLLEN v10; // [sp+38h] [bp-150h]@16
  int16_t v11; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int v13; // [sp+164h] [bp-24h]@4
  unsigned int64_t v14; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+190h] [bp+8h]@1
  _worlddb_user_count_info *v16; // [sp+1A8h] [bp+20h]@1

  v16 = pUserCountData;
  v15 = this;
  v4 = &v7;
  for ( i = 96i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v14 = (unsigned int64_t)&v7 ^ _security_cookie;
  v13 = 0;
  sprintf(&Dest, "{ CALL pSelect_UserCountInfo('%s', '%s') }", szStartDate, szEndDate);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &Dest);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v11 = SQLExecDirectA_0(v15->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      while ( 1 )
      {
        v11 = SQLFetch_0(v15->m_hStmtSelect);
        if ( v11 && v11 != 1 )
          break;
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 1u, -18, &v16->UserCount[v13], 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 2u, -18, &v16->UserCount[v13].byMonth, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 3u, -18, &v16->UserCount[v13].byDay, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 4u, -18, &v16->UserCount[v13].byHour, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 5u, -18, &v16->UserCount[v13].dwAvgCount, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 6u, -18, &v16->UserCount[v13++].dwMaxCount, 0i64, &v10);
        if ( v11 )
        {
          if ( v11 != 1 )
            break;
        }
      }
      v16->wRowCount = v13;
      if ( v15->m_hStmtSelect )
        SQLCloseCursor_0(v15->m_hStmtSelect);
      if ( v15->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &Dest);
      result = 1;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "SQLAllocHandle Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
