#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCUnmannedTraderGroupDivisionVersionInfo@@_KV1@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@stdext@@YAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@Z
 * Address: 0x140001FA5

void  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderGroupDivisionVersionInfo *,unsigned int64_t,CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(CUnmannedTraderGroupDivisionVersionInfo *_First, unsigned int64_t _Count, CUnmannedTraderGroupDivisionVersionInfo *_Val, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderGroupDivisionVersionInfo *,unsigned int64_t,CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(
    _First,
    _Count,
    _Val,
    _Al);
}
