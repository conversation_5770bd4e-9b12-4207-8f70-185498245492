#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?Regist@CUnmannedTraderController@@QEAAXGPEAU_a_trade_reg_item_request_cl<PERSON>@@@Z
 * Address: 0x14000A209

void  CUnmannedTraderController::Regist(CUnmannedTraderController *this, unsigned int16_t wInx, _a_trade_reg_item_request_clzo *pRequest)
{
  CUnmannedTraderController::Regist(this, wInx, pRequest);
}
