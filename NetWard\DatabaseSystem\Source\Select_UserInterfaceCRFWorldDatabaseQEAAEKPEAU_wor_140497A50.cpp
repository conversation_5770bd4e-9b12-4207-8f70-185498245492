#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Select_UserInterface@CRFWorldDatabase@@QEAAEKPEAU_worlddb_userinterface_info@@@Z
 * Address: 0x140497A50

char  CRFWorldDatabase::Select_UserInterface(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_userinterface_info *pUserinterfaceInfo)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@8
  int64_t v6; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@24
  SQLLEN v9; // [sp+38h] [bp-150h]@24
  int16_t v10; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int j; // [sp+164h] [bp-24h]@4
  int v13; // [sp+168h] [bp-20h]@4
  char v14; // [sp+16Ch] [bp-1Ch]@16
  unsigned int64_t v15; // [sp+178h] [bp-10h]@4
  CRFWorldDatabase *v16; // [sp+190h] [bp+8h]@1
  _worlddb_userinterface_info *v17; // [sp+1A0h] [bp+18h]@1

  v17 = pUserinterfaceInfo;
  v16 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v15 = (unsigned int64_t)&v6 ^ _security_cookie;
  j = 0;
  v13 = 0;
  sprintf(&Dest, "{ CALL pSelect_UserInterface0513( %d ) }", dwSerial);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &Dest);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v10 = SQLExecDirectA_0(v16->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v10, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v10, v16->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v10 = SQLFetch_0(v16->m_hStmtSelect);
      if ( v10 && v10 != 1 )
      {
        v14 = 0;
        if ( v10 == 100 )
        {
          v14 = 2;
        }
        else
        {
          SQLStmt = v16->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v10, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v10, v16->m_hStmtSelect);
          v14 = 1;
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        result = v14;
      }
      else
      {
        for ( j = 0; j < 50; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v16->m_hStmtSelect, v13, 5, (char *)v17 + 2 * j, 0i64, &v9);
          if ( v10 )
          {
            if ( v10 != 1 )
              break;
          }
        }
        for ( j = 0; j < 8; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v16->m_hStmtSelect, v13, 4, &v17->dwDamageCForce[j], 0i64, &v9);
          if ( v10 )
          {
            if ( v10 != 1 )
              break;
          }
        }
        for ( j = 0; j < 8; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v16->m_hStmtSelect, v13, 4, &v17->dwHelpCForce[j], 0i64, &v9);
          if ( v10 )
          {
            if ( v10 != 1 )
              break;
          }
        }
        for ( j = 0; j < 2; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v16->m_hStmtSelect, v13, 4, &v17->dwSkill[j], 0i64, &v9);
          if ( v10 )
          {
            if ( v10 != 1 )
              break;
          }
        }
        for ( j = 0; j < 2; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v16->m_hStmtSelect, v13, 4, &v17->dwForce[j], 0i64, &v9);
          if ( v10 )
          {
            if ( v10 != 1 )
              break;
          }
        }
        for ( j = 0; j < 2; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v16->m_hStmtSelect, v13, 4, &v17->dwCharacter[j], 0i64, &v9);
          if ( v10 )
          {
            if ( v10 != 1 )
              break;
          }
        }
        for ( j = 0; j < 2; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v16->m_hStmtSelect, v13, 4, &v17->dwAnimus[j], 0i64, &v9);
          if ( v10 )
          {
            if ( v10 != 1 )
              break;
          }
        }
        ++v13;
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v16->m_hStmtSelect, v13, 4, &v17->dwInven, 0i64, &v9);
        for ( j = 0; j < 5; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v16->m_hStmtSelect, v13, 4, &v17->dwInvenBag[j], 0i64, &v9);
          if ( v10 )
          {
            if ( v10 != 1 )
              break;
          }
        }
        ++v13;
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v16->m_hStmtSelect, v13, -28, &v17->byLinkBoardLock, 0i64, &v9);
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        if ( v16->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
