#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Uninit_fill_n@PEAPEAVCUnmannedTraderSortType@@_KPEAV1@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@YAXPEAPEAVCUnmannedTraderSortType@@_KAEBQEAV1@AEAV?$allocator@PEAVCUnmannedTraderSortType@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140009200

void  std::_Uninit_fill_n<CUnmannedTraderSortType * *,unsigned int64_t,CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(CUnmannedTraderSortType **_First, unsigned int64_t _Count, CUnmannedTraderSortType *const *_Val, std::allocator<CUnmannedTraderSortType *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CUnmannedTraderSortType * *,unsigned int64_t,CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
