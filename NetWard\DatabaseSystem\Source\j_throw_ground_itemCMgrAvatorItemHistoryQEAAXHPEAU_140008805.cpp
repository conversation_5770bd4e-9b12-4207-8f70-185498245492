#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?throw_ground_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@PEADPEAM1@Z
 * Address: 0x140008805

void  CMgrAvatorItemHistory::throw_ground_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pItem, char *pMapCode, float *pfPos, char *pszFileName)
{
  CMgrAvatorItemHistory::throw_ground_item(this, n, pItem, pMapCode, pfPos, pszFileName);
}
