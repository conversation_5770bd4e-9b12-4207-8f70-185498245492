#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetResetInfo@CSetItemEffect@@AEAAX_NKEE@Z
 * Address: 0x1402E2FB0

void  CSetItemEffect::SetResetInfo(CSetItemEffect *this, bool bSet, unsigned int dwSetItem, char bySetItemNum, char bySetEffectNum)
{
  if ( bSet )
  {
    this->m_resetInfo.m_bCheckSetEffect = 1;
    this->m_resetInfo.m_dwSetItem = dwSetItem;
    this->m_resetInfo.m_bySetItemNum = bySetItemNum;
    this->m_resetInfo.m_bySetEffectNum = bySetEffectNum;
  }
  else
  {
    this->m_resetInfo.m_bCheckSetEffect = 0;
    this->m_resetInfo.m_dwSetItem = 0;
    this->m_resetInfo.m_bySetItemNum = 0;
    this->m_resetInfo.m_bySetEffectNum = 0;
  }
}
