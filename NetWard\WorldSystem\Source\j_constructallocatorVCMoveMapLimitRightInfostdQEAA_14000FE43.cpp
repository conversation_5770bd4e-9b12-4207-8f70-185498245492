#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?construct@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAXPEAVCMoveMapLimitRightInfo@@AEBV3@@Z
 * Address: 0x14000FE43

void  std::allocator<CMoveMapLimitRightInfo>::construct(std::allocator<CMoveMapLimitRightInfo> *this, CMoveMapLimitRightInfo *_Ptr, CMoveMapLimitRightInfo *_Val)
{
  std::allocator<CMoveMapLimitRightInfo>::construct(this, _Ptr, _Val);
}
