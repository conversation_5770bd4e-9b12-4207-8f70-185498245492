#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?WriteLog_CharSelect@CUserDB@@QEAAXXZ
 * Address: 0x140113C00

void  CUserDB::WriteLog_CharSelect(CUserDB *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-B8h]@1
  char *pQryData; // [sp+20h] [bp-98h]@4
  int nSize; // [sp+28h] [bp-90h]@4
  char Dest; // [sp+38h] [bp-80h]@4
  unsigned int v7; // [sp+48h] [bp-70h]@4
  char v8; // [sp+4Ch] [bp-6Ch]@4
  unsigned int v9; // [sp+60h] [bp-58h]@4
  int16_t v10; // [sp+64h] [bp-54h]@4
  char v11; // [sp+66h] [bp-52h]@4
  char v12; // [sp+67h] [bp-51h]@4
  char v13; // [sp+68h] [bp-50h]@4
  char v14; // [sp+69h] [bp-4Fh]@4
  char v15; // [sp+6Ah] [bp-4Eh]@4
  tm *v16; // [sp+78h] [bp-40h]@4
  int64_t _Time; // [sp+88h] [bp-30h]@4
  unsigned int64_t v18; // [sp+A0h] [bp-18h]@4
  CUserDB *v19; // [sp+C0h] [bp+8h]@1

  v19 = this;
  v1 = &v3;
  for ( i = 44i64; i; --i )
  {
    *(uint32_t*)v1 = -*********;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v18 = (unsigned int64_t)&v3 ^ _security_cookie;
  strcpy_0(&Dest, v19->m_szAccountID);
  v7 = v19->m_dwAccountSerial;
  strcpy_0(&v8, v19->m_wszAvatorName);
  v9 = v19->m_dwSerial;
  time_2(&_Time);
  v16 = localtime_0(&_Time);
  v10 = v16->tm_year + 1900;
  v11 = v16->tm_mon + 1;
  v12 = v16->tm_mday;
  v13 = v16->tm_hour;
  v14 = v16->tm_min;
  v15 = v16->tm_sec;
  nSize = _log_case_charselect::size((_log_case_charselect *)&Dest);
  pQryData = &Dest;
  CMainThread::PushDQSData(&g_Main, v19->m_dwAccountSerial, &v19->m_idWorld, 10, &Dest, nSize);
}
