#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??Y?$_Vector_iterator@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@QEAAAEAV01@_J@Z
 * Address: 0x140010EEC

std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > * std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator+=(std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this, int64_t _Off)
{
  return std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator+=(
           this,
           _Off);
}
