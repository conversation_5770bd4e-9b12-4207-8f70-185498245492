#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Umove@PEAPEAVCUnmannedTraderDivisionInfo@@@?$vector@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV2@00@Z
 * Address: 0x140006DE8

CUnmannedTraderDivisionInfo ** std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Umove<CUnmannedTraderDivisionInfo * *>(std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this, CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last, CUnmannedTraderDivisionInfo **_Ptr)
{
  return std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Umove<CUnmannedTraderDivisionInfo * *>(
           this,
           _First,
           _Last,
           _Ptr);
}
