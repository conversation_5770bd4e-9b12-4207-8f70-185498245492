#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?personal_amine_itemlog@CMgrAvatorItemHistory@@QEAAXPEADEEGK0@Z
 * Address: 0x14023FE40

void  CMgrAvatorItemHistory::personal_amine_itemlog(CMgrAvatorItemHistory *this, char *szLogDesc, char byPos, char byTblCode, unsigned int16_t wItemIndex, unsigned int dwDur, char *szFileName)
{
  int64_t *v7; // rdi@1
  signed int64_t i; // rcx@1
  char *v9; // rax@4
  int64_t v10; // [sp+0h] [bp-68h]@1
  char *v11; // [sp+20h] [bp-48h]@4
  int v12; // [sp+28h] [bp-40h]@4
  int v13; // [sp+30h] [bp-38h]@4
  int v14; // [sp+38h] [bp-30h]@4
  unsigned int v15; // [sp+40h] [bp-28h]@4
  int v16; // [sp+50h] [bp-18h]@4
  int v17; // [sp+54h] [bp-14h]@4
  int v18; // [sp+58h] [bp-10h]@4
  CMgrAvatorItemHistory *v19; // [sp+70h] [bp+8h]@1
  char *v20; // [sp+78h] [bp+10h]@1

  v20 = szLogDesc;
  v19 = this;
  v7 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t*)v7 = -858993460;
    v7 = (int64_t *)((char *)v7 + 4);
  }
  sData[0] = 0;
  v16 = wItemIndex;
  v17 = (unsigned int8_t)byTblCode;
  v18 = (unsigned int8_t)byPos;
  v9 = GetItemKorName((unsigned int8_t)byTblCode, wItemIndex);
  v15 = dwDur;
  v14 = v16;
  v13 = v17;
  v12 = v18;
  v11 = v9;
  sprintf_s(sData, 0x4E20ui64, "[%s] %s(pos:%d code:%d idx:%d) dur(%d)\r\n", v20);
  CMgrAvatorItemHistory::WriteFile(v19, szFileName, sData);
}
