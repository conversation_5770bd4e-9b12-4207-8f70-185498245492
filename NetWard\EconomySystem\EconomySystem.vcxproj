<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>

  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{E5F6G7H8-I9J0-1234-EF01-************}</ProjectGuid>
    <RootNamespace>EconomySystem</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemGroup>
    <ClCompile Include="Source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp" />
    <ClCompile Include="Source\0_economy_history_dataQEAAXZ_140205870.cpp" />
    <ClCompile Include="Source\0_guild_money_io_download_zoclQEAAXZ_14025CDF0.cpp" />
    <ClCompile Include="Source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp" />
    <ClCompile Include="Source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp" />
    <ClCompile Include="Source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp" />
    <ClCompile Include="Source\CanAddMoneyForMaxLimGoldYA_N_K0Z_14003F190.cpp" />
    <ClCompile Include="Source\CanAddMoneyForMaxLimMoneyYA_N_K0Z_14003F110.cpp" />
    <ClCompile Include="Source\CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_14033B300.cpp" />
    <ClCompile Include="Source\check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1401B1340.cpp" />
    <ClCompile Include="Source\ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.cpp" />
    <ClCompile Include="Source\ContinueModalCWndUEAAHXZ_0_1404DBC46.cpp" />
    <ClCompile Include="Source\DoModalCDialogUEAA_JXZ_0_1404DBD66.cpp" />
    <ClCompile Include="Source\DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.cpp" />
    <ClCompile Include="Source\DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14025F0C0.cpp" />
    <ClCompile Include="Source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14057A6B0.cpp" />
    <ClCompile Include="Source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14062D110.cpp" />
    <ClCompile Include="Source\dtor001SchedulingRingdetailsConcurrencyQEAAXZ4HA_140654370.cpp" />
    <ClCompile Include="Source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550C80.cpp" />
    <ClCompile Include="Source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550D10.cpp" />
    <ClCompile Include="Source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140555BF0.cpp" />
    <ClCompile Include="Source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_14055CD30.cpp" />
    <ClCompile Include="Source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.cpp" />
    <ClCompile Include="Source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.cpp" />
    <ClCompile Include="Source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A0E00.cpp" />
    <ClCompile Include="Source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1330.cpp" />
    <ClCompile Include="Source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1830.cpp" />
    <ClCompile Include="Source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.cpp" />
    <ClCompile Include="Source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406041F0.cpp" />
    <ClCompile Include="Source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406060C0.cpp" />
    <ClCompile Include="Source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140616110.cpp" />
    <ClCompile Include="Source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140658580.cpp" />
    <ClCompile Include="Source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.cpp" />
    <ClCompile Include="Source\dtor00AddToRunnablesInternalContextBasedetailsConc_140597C40.cpp" />
    <ClCompile Include="Source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A58D0.cpp" />
    <ClCompile Include="Source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6240.cpp" />
    <ClCompile Include="Source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6880.cpp" />
    <ClCompile Include="Source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A69C0.cpp" />
    <ClCompile Include="Source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.cpp" />
    <ClCompile Include="Source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.cpp" />
    <ClCompile Include="Source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406030B0.cpp" />
    <ClCompile Include="Source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406143E0.cpp" />
    <ClCompile Include="Source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.cpp" />
    <ClCompile Include="Source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065B9A0.cpp" />
    <ClCompile Include="Source\dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.cpp" />
    <ClCompile Include="Source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp" />
    <ClCompile Include="Source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp" />
    <ClCompile Include="Source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp" />
    <ClCompile Include="Source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.cpp" />
    <ClCompile Include="Source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0C90.cpp" />
    <ClCompile Include="Source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0FD0.cpp" />
    <ClCompile Include="Source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A11C0.cpp" />
    <ClCompile Include="Source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1500.cpp" />
    <ClCompile Include="Source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A16C0.cpp" />
    <ClCompile Include="Source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1A00.cpp" />
    <ClCompile Include="Source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A9800.cpp" />
    <ClCompile Include="Source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405AA220.cpp" />
    <ClCompile Include="Source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.cpp" />
    <ClCompile Include="Source\dtor00FoundAvailableVirtualProcessorSchedulerBased_140613960.cpp" />
    <ClCompile Include="Source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14064F020.cpp" />
    <ClCompile Include="Source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.cpp" />
    <ClCompile Include="Source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.cpp" />
    <ClCompile Include="Source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14064E810.cpp" />
    <ClCompile Include="Source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140658560.cpp" />
    <ClCompile Include="Source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.cpp" />
    <ClCompile Include="Source\dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.cpp" />
    <ClCompile Include="Source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F60.cpp" />
    <ClCompile Include="Source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.cpp" />
    <ClCompile Include="Source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406502F0.cpp" />
    <ClCompile Include="Source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A0CB0.cpp" />
    <ClCompile Include="Source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A0FF0.cpp" />
    <ClCompile Include="Source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A11E0.cpp" />
    <ClCompile Include="Source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A1520.cpp" />
    <ClCompile Include="Source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A16E0.cpp" />
    <ClCompile Include="Source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A1A20.cpp" />
    <ClCompile Include="Source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A5D40.cpp" />
    <ClCompile Include="Source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp" />
    <ClCompile Include="Source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp" />
    <ClCompile Include="Source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp" />
    <ClCompile Include="Source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp" />
    <ClCompile Include="Source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp" />
    <ClCompile Include="Source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp" />
    <ClCompile Include="Source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp" />
    <ClCompile Include="Source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp" />
    <ClCompile Include="Source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp" />
    <ClCompile Include="Source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp" />
    <ClCompile Include="Source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp" />
    <ClCompile Include="Source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp" />
    <ClCompile Include="Source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp" />
    <ClCompile Include="Source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp" />
    <ClCompile Include="Source\dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.cpp" />
    <ClCompile Include="Source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp" />
    <ClCompile Include="Source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp" />
    <ClCompile Include="Source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp" />
    <ClCompile Include="Source\dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.cpp" />
    <ClCompile Include="Source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F80.cpp" />
    <ClCompile Include="Source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.cpp" />
    <ClCompile Include="Source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140650310.cpp" />
    <ClCompile Include="Source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp" />
    <ClCompile Include="Source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp" />
    <ClCompile Include="Source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp" />
    <ClCompile Include="Source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp" />
    <ClCompile Include="Source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp" />
    <ClCompile Include="Source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp" />
    <ClCompile Include="Source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp" />
    <ClCompile Include="Source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp" />
    <ClCompile Include="Source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp" />
    <ClCompile Include="Source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp" />
    <ClCompile Include="Source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp" />
    <ClCompile Include="Source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp" />
    <ClCompile Include="Source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp" />
    <ClCompile Include="Source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp" />
    <ClCompile Include="Source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp" />
    <ClCompile Include="Source\eAddDalantYAXHHZ_1402A41B0.cpp" />
    <ClCompile Include="Source\eGetDalantYANHZ_1402A4390.cpp" />
    <ClCompile Include="Source\eGetGuideHistoryYAPEAU_economy_history_dataXZ_1402A48C0.cpp" />
    <ClCompile Include="Source\eGetOldDalantYANHZ_1402A47A0.cpp" />
    <ClCompile Include="Source\EndModalLoopCWndUEAAXHZ_0_1404DBC4C.cpp" />
    <ClCompile Include="Source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp" />
    <ClCompile Include="Source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp" />
    <ClCompile Include="Source\ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEADZ_1401D2D10.cpp" />
    <ClCompile Include="Source\ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEADZ_1401D2DC0.cpp" />
    <ClCompile Include="Source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp" />
    <ClCompile Include="Source\GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14025D640.cpp" />
    <ClCompile Include="Source\GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_1400AD130.cpp" />
    <ClCompile Include="Source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp" />
    <ClCompile Include="Source\GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_1401C8410.cpp" />
    <ClCompile Include="Source\InstanceCMoneySupplyMgrSAPEAV1XZ_140095070.cpp" />
    <ClCompile Include="Source\IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140253230.cpp" />
    <ClCompile Include="Source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp" />
    <ClCompile Include="Source\j_0_economy_history_dataQEAAXZ_140009719.cpp" />
    <ClCompile Include="Source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp" />
    <ClCompile Include="Source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp" />
    <ClCompile Include="Source\j_CanAddMoneyForMaxLimGoldYA_N_K0Z_140003C15.cpp" />
    <ClCompile Include="Source\j_CanAddMoneyForMaxLimMoneyYA_N_K0Z_140011FE5.cpp" />
    <ClCompile Include="Source\j_CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_140004BFB.cpp" />
    <ClCompile Include="Source\j_check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1400048B3.cpp" />
    <ClCompile Include="Source\j_DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14000EA52.cpp" />
    <ClCompile Include="Source\j_eAddDalantYAXHHZ_14000650A.cpp" />
    <ClCompile Include="Source\j_eGetDalantYANHZ_140012CB5.cpp" />
    <ClCompile Include="Source\j_eGetGuideHistoryYAPEAU_economy_history_dataXZ_14000F0C9.cpp" />
    <ClCompile Include="Source\j_eGetOldDalantYANHZ_14001212F.cpp" />
    <ClCompile Include="Source\j_ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEA_14000A1FF.cpp" />
    <ClCompile Include="Source\j_ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEA_14000D5F8.cpp" />
    <ClCompile Include="Source\j_FindAllFileYAHPEADPEAPEADHZ_14000E787.cpp" />
    <ClCompile Include="Source\j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.cpp" />
    <ClCompile Include="Source\j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.cpp" />
    <ClCompile Include="Source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp" />
    <ClCompile Include="Source\j_GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_140003805.cpp" />
    <ClCompile Include="Source\j_InstanceCMoneySupplyMgrSAPEAV1XZ_14000EF11.cpp" />
    <ClCompile Include="Source\j_IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140010483.cpp" />
    <ClCompile Include="Source\j_LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io__14000EC96.cpp" />
    <ClCompile Include="Source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp" />
    <ClCompile Include="Source\j_ManagePopGuildMoneyCGuildQEAAEKKKZ_14000C0FE.cpp" />
    <ClCompile Include="Source\j_pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1400027C0.cpp" />
    <ClCompile Include="Source\j_PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140013EEE.cpp" />
    <ClCompile Include="Source\j_push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_14000E1AB.cpp" />
    <ClCompile Include="Source\j_qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140005407.cpp" />
    <ClCompile Include="Source\j_SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_140002351.cpp" />
    <ClCompile Include="Source\j_SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member__14000C707.cpp" />
    <ClCompile Include="Source\j_SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_140001203.cpp" />
    <ClCompile Include="Source\j_size_guild_money_io_download_zoclQEAAHXZ_140006046.cpp" />
    <ClCompile Include="Source\j_size_log_sheet_economyQEAAHXZ_140011DBF.cpp" />
    <ClCompile Include="Source\j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.cpp" />
    <ClCompile Include="Source\j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.cpp" />
    <ClCompile Include="Source\j_size_qry_case_outputgmoneyQEAAHXZ_140011171.cpp" />
    <ClCompile Include="Source\j_SubChargeCostAutoMineMachineQEAAXEPEADZ_1400062C6.cpp" />
    <ClCompile Include="Source\j_TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEA_14000BDB6.cpp" />
    <ClCompile Include="Source\j_TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_140010BC2.cpp" />
    <ClCompile Include="Source\j_UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14000CBE9.cpp" />
    <ClCompile Include="Source\j_UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_140013665.cpp" />
    <ClCompile Include="Source\j_UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_140001CF3.cpp" />
    <ClCompile Include="Source\j_UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHP_140013DAE.cpp" />
    <ClCompile Include="Source\j_UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEK_1400076D0.cpp" />
    <ClCompile Include="Source\j_UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEH_1400032BF.cpp" />
    <ClCompile Include="Source\j_UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_1400030B2.cpp" />
    <ClCompile Include="Source\j_UpdateUnitRepairingChargesDataCMoneySupplyMgrQEA_140004903.cpp" />
    <ClCompile Include="Source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp" />
    <ClCompile Include="Source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp" />
    <ClCompile Include="Source\j__ReadEconomyIniFileYA_NXZ_140007F31.cpp" />
    <ClCompile Include="Source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp" />
    <ClCompile Include="Source\j___CheckCond_DalantCQuestMgrQEAA_NEHZ_14000F6D7.cpp" />
    <ClCompile Include="Source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp" />
    <ClCompile Include="Source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp" />
    <ClCompile Include="Source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp" />
    <ClCompile Include="Source\pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1402495E0.cpp" />
    <ClCompile Include="Source\PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140254330.cpp" />
    <ClCompile Include="Source\push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_140249510.cpp" />
    <ClCompile Include="Source\qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_140274740.cpp" />
    <ClCompile Include="Source\SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_14025A6E0.cpp" />
    <ClCompile Include="Source\SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.cpp" />
    <ClCompile Include="Source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp" />
    <ClCompile Include="Source\size_guild_money_io_download_zoclQEAAHXZ_14025D430.cpp" />
    <ClCompile Include="Source\size_log_sheet_economyQEAAHXZ_1402A5CE0.cpp" />
    <ClCompile Include="Source\size_MONEY_SUPPLY_DATAQEAAHXZ_140430790.cpp" />
    <ClCompile Include="Source\size_qry_case_inputgmoneyQEAAHXZ_1400AD290.cpp" />
    <ClCompile Include="Source\size_qry_case_outputgmoneyQEAAHXZ_1400AD4C0.cpp" />
    <ClCompile Include="Source\SubChargeCostAutoMineMachineQEAAXEPEADZ_1402D25E0.cpp" />
    <ClCompile Include="Source\TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEADZ_1401D63E0.cpp" />
    <ClCompile Include="Source\TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_1401D6140.cpp" />
    <ClCompile Include="Source\UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14042C4C0.cpp" />
    <ClCompile Include="Source\UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_14042F470.cpp" />
    <ClCompile Include="Source\UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_14042F1B0.cpp" />
    <ClCompile Include="Source\UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHPEA_14042E520.cpp" />
    <ClCompile Include="Source\UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEKZ_14042F2D0.cpp" />
    <ClCompile Include="Source\UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEHPE_14042D890.cpp" />
    <ClCompile Include="Source\UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_14042B7D0.cpp" />
    <ClCompile Include="Source\UpdateUnitRepairingChargesDataCMoneySupplyMgrQEAAX_14042F530.cpp" />
    <ClCompile Include="Source\_CMainThreadcheck_min_max_guild_money__1_dtor0_1401B16B0.cpp" />
    <ClCompile Include="Source\_CMoneySupplyMgrInstance__1_dtor0_140095100.cpp" />
    <ClCompile Include="Source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp" />
    <ClCompile Include="Source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp" />
    <ClCompile Include="Source\_ReadEconomyIniFileYA_NXZ_1402A5040.cpp" />
    <ClCompile Include="Source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp" />
    <ClCompile Include="Source\__CheckCond_DalantCQuestMgrQEAA_NEHZ_140288770.cpp" />
    <ClCompile Include="Source\__HrLoadAllImportsForDll_14067693C.cpp" />
  </ItemGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>