#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?StartScreenPoint@CMapExtend@@QEAAXPEAVCPoint@@PEAVCMapData@@PEAVCRect@@@Z
 * Address: 0x1401A1670

void  CMapExtend::StartScreenPoint(CMapExtend *this, CPoint *pt, CMapData *pMap, CRect *rcWnd)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-38h]@1
  _bsp_info *v7; // [sp+20h] [bp-18h]@5
  CMapExtend *v8; // [sp+40h] [bp+8h]@1
  CRect *v9; // [sp+58h] [bp+20h]@1

  v9 = rcWnd;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( !v8->m_bExtendMode )
  {
    v8->m_bSetArea = 1;
    v8->m_bMove = 0;
    v8->m_ptStartScreen = *pt;
    v8->m_ptMoveScreen = *pt;
    v7 = CMapData::GetBspInfo(pMap);
    _map_rate::Setting(&v8->m_Rate, v9->right, v9->bottom);
  }
}
