#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_QuestUpdate@CUserDB@@QEAA_NEPEAU_LIST@_QUEST_DB_BASE@@_N@Z
 * Address: 0x140115700

char  CUserDB::Update_QuestUpdate(CUserDB *this, char bySlotIndex, _QUEST_DB_BASE::_LIST *pSlotData, bool bUpdate)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v7; // [sp+0h] [bp-28h]@1
  CUserDB *v8; // [sp+30h] [bp+8h]@1
  bool v9; // [sp+48h] [bp+20h]@1

  v9 = bUpdate;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned int8_t)bySlotIndex < 30 )
  {
    if ( v8->m_AvatorData.dbQuest.m_List[(unsigned int8_t)bySlotIndex].byQuestType == 255 )
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_QuestUpdate(NOTHING) : slot : %d",
        v8->m_aszAvatorName,
        (unsigned int8_t)bySlotIndex);
      result = 0;
    }
    else
    {
      memcpy_0((char *)&v8->m_AvatorData.dbQuest + 13 * (unsigned int8_t)bySlotIndex, pSlotData, 0xDui64);
      if ( v9 )
        v8->m_bDataUpdate = 1;
      result = 1;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_QuestUpdate(SlotIndex OVER) : slot : %d",
      v8->m_aszAvatorName,
      (unsigned int8_t)bySlotIndex);
    result = 0;
  }
  return result;
}
