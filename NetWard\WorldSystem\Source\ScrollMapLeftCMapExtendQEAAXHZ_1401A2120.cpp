#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ScrollMapLeft@CMapExtend@@QEAAXH@Z
 * Address: 0x1401A2120

void  CMapExtend::ScrollMapLeft(CMapExtend *this, int nInterval)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-38h]@1
  tagPOINT bottomRight; // [sp+20h] [bp-18h]@8
  tagPOINT topLeft; // [sp+28h] [bp-10h]@8
  CMapExtend *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v7->m_bExtendMode )
  {
    if ( v7->m_ptStartMap.x - nInterval >= 0 )
    {
      v7->m_ptStartMap.x -= nInterval;
      v7->m_ptEndMap.x -= nInterval;
      v7->m_ptCenter.x -= nInterval;
    }
    else
    {
      v7->m_ptStartMap.x = 0;
      v7->m_ptEndMap.x = v7->m_sizeExtend.cx;
      v7->m_ptCenter.x = v7->m_sizeExtend.cx / 2;
    }
    bottomRight = (tagPOINT)v7->m_ptEndMap;
    topLeft = (tagPOINT)v7->m_ptStartMap;
    CRect::SetRect(&v7->m_rcExtend, topLeft, bottomRight);
  }
}
