#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetCashDBInitState@CNationSettingManager@@QEAAXXZ
 * Address: 0x1402F2D60

void  CNationSettingManager::SetCashDBInitState(CNationSettingManager *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-28h]@1
  CNationSettingManager *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  CNationSettingData::SetCashDBInitFlag(v4->m_pData);
}
