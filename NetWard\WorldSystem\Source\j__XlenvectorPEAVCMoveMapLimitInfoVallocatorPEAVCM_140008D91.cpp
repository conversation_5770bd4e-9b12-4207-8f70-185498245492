#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_<PERSON>len@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@KAXXZ
 * Address: 0x140008D91

void  __noreturn std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Xlen(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_<PERSON>len(this);
}
