#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::_Insert_n_::_1_::catch$1
 * Address: 0x1403B0310

void  __noreturn std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::_Insert_n_::_1_::catch_1(int64_t a1, int64_t a2)
{
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Destroy(
    *(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 160),
    (CMoveMapLimitRight **)(*(uint64_t*)(*(uint64_t*)(a2 + 168) + 16i64) + 8i64 * *(uint64_t*)(a2 + 176)),
    (CMoveMapLimitRight **)(*(uint64_t*)(*(uint64_t*)(a2 + 160) + 24i64) + 8i64 * *(uint64_t*)(a2 + 176)));
  CxxThrowException_0(0i64, 0i64);
}
