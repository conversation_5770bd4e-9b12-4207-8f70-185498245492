#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?PushUpdateState@CUnmannedTraderItemState@@SA_NEKEKGEG@Z
 * Address: 0x140351C70

char  CUnmannedTraderItemState::PushUpdateState(char byType, unsigned int dwRegistSerial, char byState, unsigned int dwOwnerSerial, unsigned int16_t wItemSerial, char byItemTableCode, unsigned int16_t wItemTableIndex)
{
  int64_t *v7; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int v10; // eax@6
  int64_t v11; // [sp+0h] [bp-68h]@1
  _qry_case_unmandtrader_updateitemstate v12; // [sp+38h] [bp-30h]@6
  char v13; // [sp+70h] [bp+8h]@1
  unsigned int v14; // [sp+78h] [bp+10h]@1
  char v15; // [sp+80h] [bp+18h]@1
  unsigned int v16; // [sp+88h] [bp+20h]@1

  v16 = dwOwnerSerial;
  v15 = byState;
  v14 = dwRegistSerial;
  v13 = byType;
  v7 = &v11;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t*)v7 = -858993460;
    v7 = (int64_t *)((char *)v7 + 4);
  }
  if ( CUnmannedTraderItemState::GetMaxStateCnt() > (unsigned int8_t)byState )
  {
    v12.byType = v13;
    v12.dwRegistSerial = v14;
    v12.byState = v15;
    v12.wItemSerial = wItemSerial;
    v12.dwOwnerSerial = v16;
    v12.byItemTableCode = byItemTableCode;
    v12.wItemTableIndex = wItemTableIndex;
    v10 = _qry_case_unmandtrader_updateitemstate::size(&v12);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 59, &v12.byType, v10);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
