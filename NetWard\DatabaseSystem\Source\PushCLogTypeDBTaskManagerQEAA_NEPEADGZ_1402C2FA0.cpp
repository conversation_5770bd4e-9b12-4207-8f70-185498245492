#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Push@CLogTypeDBTaskManager@@QEAA_NEPEADG@Z
 * Address: 0x1402C2FA0

bool  CLogTypeDBTaskManager::Push(CLogTypeDBTaskManager *this, char byQueryType, char *pcData, unsigned int16_t wSize)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  bool result; // al@8
  int64_t v7; // [sp+0h] [bp-478h]@1
  int v8; // [sp+20h] [bp-458h]@5
  CLogTypeDBTask *pTask; // [sp+30h] [bp-448h]@4
  char DstBuf; // [sp+50h] [bp-428h]@5
  unsigned int64_t v11; // [sp+460h] [bp-18h]@4
  CLogTypeDBTaskManager *v12; // [sp+480h] [bp+8h]@1
  char v13; // [sp+488h] [bp+10h]@1
  char *pcDataa; // [sp+490h] [bp+18h]@1
  unsigned int16_t v15; // [sp+498h] [bp+20h]@1

  v15 = wSize;
  pcDataa = pcData;
  v13 = byQueryType;
  v12 = this;
  v4 = &v7;
  for ( i = 284i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v11 = (unsigned int64_t)&v7 ^ _security_cookie;
  pTask = CLogTypeDBTaskPool::GetEmpty(&v12->m_kPool);
  if ( !pTask )
  {
    CLogTypeDBTaskManager::Log(
      v12,
      "CLogTypeDBTaskManager::Push( BYTE byQueryType(%u), BYTE * pcData, WORD wSize(%u) ) : m_kPool.GetEmpty() NULL!",
      (unsigned int8_t)v13,
      v15);
    v8 = v15;
    sprintf_s(
      &DstBuf,
      0x400ui64,
      "CLogTypeDBTaskManager::Push( BYTE byQueryType(%u), char * pcData, WORD wSize(%u) ) : m_kPool.GetEmpty() is NULL!",
      (unsigned int8_t)v13);
    ServerProgramExit(&DstBuf, 1);
  }
  if ( CLogTypeDBTask::Set(pTask, v13, pcDataa, v15) )
  {
    result = CLogTypeDBTaskPool::SetProc(&v12->m_kPool, pTask, v12->m_pkLogger);
  }
  else
  {
    CLogTypeDBTaskManager::Log(
      v12,
      "CLogTypeDBTaskManager::Push( BYTE byQueryType(%u), BYTE * pcData, WORD wSize(%u) ) : pkTask->Set( byQueryType, pcD"
      "ata, wSize ) Fail!",
      (unsigned int8_t)v13,
      v15);
    result = 0;
  }
  return result;
}
