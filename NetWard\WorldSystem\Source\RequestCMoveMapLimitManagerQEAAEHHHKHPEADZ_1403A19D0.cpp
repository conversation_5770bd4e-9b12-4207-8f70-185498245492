#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Request@CMoveMapLimitManager@@QEAAEHHHKHPEAD@Z
 * Address: 0x1403A19D0

char  CMoveMapLimitManager::Request(CMoveMapLimitManager *this, int iLimitType, int iRequetType, int iMapInx, unsigned int dwStoreRecordIndex, int iUserInx, char *pRequest)
{
  int64_t *v7; // rdi@1
  signed int64_t i; // rcx@1
  CMoveMapLimitRightInfo *v9; // rax@4
  int64_t v11; // [sp+0h] [bp-48h]@1
  CMoveMapLimitManager *v12; // [sp+50h] [bp+8h]@1
  int iLimitTypea; // [sp+58h] [bp+10h]@1
  int iRequetTypea; // [sp+60h] [bp+18h]@1
  int iMapInxa; // [sp+68h] [bp+20h]@1

  iMapInxa = iMapInx;
  iRequetTypea = iRequetType;
  iLimitTypea = iLimitType;
  v12 = this;
  v7 = &v11;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v7 = -858993460;
    v7 = (int64_t *)((char *)v7 + 4);
  }
  v9 = CMoveMapLimitRightInfoList::Get(&v12->m_kRightInfo, iUserInx);
  return CMoveMapLimitInfoList::Request(
           &v12->m_kLimitInfo,
           iLimitTypea,
           iRequetTypea,
           iMapInxa,
           dwStoreRecordIndex,
           iUserInx,
           pRequest,
           v9);
}
