#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?SetGuildMaintainMoney@CUnmannedTraderTaxRateManager@@QEAAXEKK@Z
 * Address: 0x14000EA48

void  CUnmannedTraderTaxRateManager::SetGuildMaintainMoney(CUnmannedTraderTaxRateManager *this, char byRace, unsigned int dwTax, unsigned int dwSeller)
{
  CUnmannedTraderTaxRateManager::SetGuildMaintainMoney(this, byRace, dwTax, dwSeller);
}
