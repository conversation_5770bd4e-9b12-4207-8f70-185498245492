#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SellComplete@CUnmannedTraderRegistItemInfo@@QEAAXKKK_JQEBD1@Z
 * Address: 0x14035F990

void  CUnmannedTraderRegistItemInfo::SellComplete(CUnmannedTraderRegistItemInfo *this, unsigned int dwPrice, unsigned int dwBuyerSerial, unsigned int dwTax, int64_t tResultTime, const char *const wszBuyerName, const char *const szBuyerAccount)
{
  int64_t *v7; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v9; // [sp+0h] [bp-28h]@1
  CUnmannedTraderRegistItemInfo *v10; // [sp+30h] [bp+8h]@1

  v10 = this;
  v7 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v7 = -*********;
    v7 = (int64_t *)((char *)v7 + 4);
  }
  v10->m_dwPrice = dwPrice;
  v10->m_dwBuyerSerial = dwBuyerSerial;
  v10->m_dwTax = dwTax;
  v10->m_tResultTime = tResultTime;
  strcpy_s(v10->m_wszBuyerName, 0x11ui64, wszBuyerName);
  strcpy_s(v10->m_szBuyerAccount, 0xDui64, szBuyerAccount);
  CUnmannedTraderItemState::Set(&v10->m_kState, 3);
}
