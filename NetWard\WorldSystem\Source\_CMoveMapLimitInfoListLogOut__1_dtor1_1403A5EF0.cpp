#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CMoveMapLimitInfoList::LogOut_::_1_::dtor$1
 * Address: 0x1403A5EF0

void  CMoveMapLimitInfoList::LogOut_::_1_::dtor_1(int64_t a1, int64_t a2)
{
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>((std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)(a2 + 56));
}
