#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?wa_EnterWorld@@YAXPEAU_WA_AVATOR_CODE@@G@Z
 * Address: 0x140046110

void  wa_EnterWorld(_WA_AVATOR_CODE *pData, unsigned int16_t wZoneIndex)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-38h]@1
  CPartyPlayer *v5; // [sp+20h] [bp-18h]@4
  _WA_AVATOR_CODE *pDataa; // [sp+40h] [bp+8h]@1

  pDataa = pData;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v5 = (CPartyPlayer *)((char *)&g_PartyPlayer + 128 * (unsigned int64_t)pDataa->m_id.wIndex);
  if ( !v5->m_bLogin )
    CPartyPlayer::EnterWorld(v5, pDataa, wZoneIndex);
}
