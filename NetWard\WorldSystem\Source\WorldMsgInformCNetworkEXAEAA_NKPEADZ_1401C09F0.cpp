#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?WorldMsgInform@CNetworkEX@@AEAA_NKPEAD@Z
 * Address: 0x1401C09F0

char  CNetworkEX::WorldMsgInform(CNetworkEX *this, unsigned int n, char *pMsg)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-578h]@1
  char *v7; // [sp+20h] [bp-558h]@4
  char Dst[1312]; // [sp+40h] [bp-538h]@6
  unsigned int64_t v9; // [sp+560h] [bp-18h]@4

  v3 = &v6;
  for ( i = 348i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v9 = (unsigned int64_t)&v6 ^ _security_cookie;
  v7 = pMsg;
  if ( (signed int)*(uint16_t*)pMsg < 1280 )
  {
    memcpy_0(Dst, v7 + 2, *(uint16_t*)v7);
    Dst[*(uint16_t*)v7] = 0;
    CMainThread::pc_AllUserMsgInform(&g_Main, Dst);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
