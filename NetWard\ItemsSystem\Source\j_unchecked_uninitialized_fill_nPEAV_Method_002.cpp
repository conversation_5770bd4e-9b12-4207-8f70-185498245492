#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCUnmannedTraderRegistItemInfo@@_KV1@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@stdext@@YAXPEAVCUnmannedTraderRegistItemInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@Z
 * Address: 0x1400054B1

void  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderRegistItemInfo *,unsigned int64_t,CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(CUnmannedTraderRegistItemInfo *_First, unsigned int64_t _Count, CUnmannedTraderRegistItemInfo *_Val, std::allocator<CUnmannedTraderRegistItemInfo> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderRegistItemInfo *,unsigned int64_t,CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(
    _First,
    _Count,
    _Val,
    _Al);
}
