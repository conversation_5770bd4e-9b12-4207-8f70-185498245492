#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?invoke@?$void2val@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@SAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAX@Z
 * Address: 0x140013377

CLuaSignalReActor *( * lua_tinker::void2val<CLuaSignalReActor * (CMonster::*)(void)>::invoke(lua_tinker::void2val<CLuaSignalReActor * ( CMonster::*)(void)> *this, void *input))(CMonster *this)
{
  return lua_tinker::void2val<CLuaSignalReActor * (CMonster::*)(void)>::invoke(this, input);
}
