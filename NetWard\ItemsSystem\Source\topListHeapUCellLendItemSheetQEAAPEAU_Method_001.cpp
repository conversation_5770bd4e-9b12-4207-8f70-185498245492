#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?top@?$ListHeap@UCell@LendItemSheet@@@@QEAAPEAUCell@LendItemSheet@@XZ
 * Address: 0x14030F720

LendItemSheet::Cell * ListHeap<LendItemSheet::Cell>::top(ListHeap<LendItemSheet::Cell> *this)
{
  LendItemSheet::Cell *result; // rax@2

  if ( this->_listData.m_Head.m_pNext == &this->_listData.m_Tail )
    result = 0i64;
  else
    result = &this->_pBuf[this->_listData.m_Head.m_pNext->m_dwIndex];
  return result;
}
