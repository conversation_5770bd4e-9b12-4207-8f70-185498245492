#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?Push@CIndexListEx@?$ListHeap@UCell@LendItemSheet@@@@QEAA_NPEAU_index_node@CNetIndexList@@K@Z
 * Address: 0x140010316

bool  ListHeap<LendItemSheet::Cell>::CIndexListEx::Push(ListHeap<LendItemSheet::Cell>::CIndexListEx *this, CNetIndexList::_index_node *pos, unsigned int dwIndex)
{
  return ListHeap<LendItemSheet::Cell>::CIndexListEx::Push(this, pos, dwIndex);
}
