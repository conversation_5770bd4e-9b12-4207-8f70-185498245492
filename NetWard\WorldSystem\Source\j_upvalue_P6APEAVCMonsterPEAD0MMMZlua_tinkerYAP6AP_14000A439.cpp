#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$upvalue_@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@YAP6APEAVCMonster@@PEAD0MMM@ZPEAUlua_State@@@Z
 * Address: 0x14000A439

CMonster *( * lua_tinker::upvalue_<CMonster * (*)(char *,char *,float,float,float)>(struct lua_State *L))(char *, char *, float, float, float)
{
  return lua_tinker::upvalue_<CMonster * (*)(char *,char *,float,float,float)>(L);
}
