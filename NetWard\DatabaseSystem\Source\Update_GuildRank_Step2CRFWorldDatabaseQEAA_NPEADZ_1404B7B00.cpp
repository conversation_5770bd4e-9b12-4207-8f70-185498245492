#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_GuildRank_Step2@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404B7B00

char  CRFWorldDatabase::Update_GuildRank_Step2(CRFWorldDatabase *this, char *szDate)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v5; // [sp+0h] [bp-468h]@1
  char *v6; // [sp+20h] [bp-448h]@4
  char DstBuf; // [sp+40h] [bp-428h]@4
  char v8; // [sp+41h] [bp-427h]@4
  unsigned int64_t v9; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+470h] [bp+8h]@1
  char *v11; // [sp+478h] [bp+10h]@1

  v11 = szDate;
  v10 = this;
  v2 = &v5;
  for ( i = 280i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v9 = (unsigned int64_t)&v5 ^ _security_cookie;
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v10->vfptr,
    "CRFWorldDatabase::Update_GuildRank_Step2(char* szDate(%s)) : Start Update Rate",
    szDate);
  DstBuf = 0;
  memset(&v8, 0, 0x3FFui64);
  v6 = v11;
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "update tbl_GuildRank%s set Rate = ( (Rank*100)/(select count(*) from tbl_GuildRank%s) )",
    v11);
  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &DstBuf, 0) )
  {
    sprintf_s(&DstBuf, 0x400ui64, "update tbl_GuildRank%s set Grade = 2 where rate <= 95 and GuildPower >= 300", v11);
    if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &DstBuf, 0) )
    {
      sprintf_s(&DstBuf, 0x400ui64, "update tbl_GuildRank%s set Grade = 3 where rate <= 85 and GuildPower >= 1500", v11);
      if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &DstBuf, 0) )
      {
        sprintf_s(
          &DstBuf,
          0x400ui64,
          "update tbl_GuildRank%s set Grade = 4 where rate <= 65 and GuildPower >= 3000",
          v11);
        if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &DstBuf, 0) )
        {
          sprintf_s(
            &DstBuf,
            0x400ui64,
            "update tbl_GuildRank%s set Grade = 5 where rate <= 35 and GuildPower >= 10000",
            v11);
          if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &DstBuf, 0) )
          {
            sprintf_s(
              &DstBuf,
              0x400ui64,
              "update tbl_GuildRank%s set Grade = 6 where rate <= 15 and GuildPower >= 25000",
              v11);
            if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &DstBuf, 0) )
            {
              sprintf_s(
                &DstBuf,
                0x400ui64,
                "update tbl_GuildRank%s set Grade = 7 where rate <= 5 and GuildPower >= 30000",
                v11);
              if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &DstBuf, 0) )
              {
                CRFNewDatabase::FmtLog(
                  (CRFNewDatabase *)&v10->vfptr,
                  "CRFWorldDatabase::Update_GuildRank_Step2(char* szDate(%s)) : End Update Rate",
                  v11);
                result = 1;
              }
              else
              {
                CRFNewDatabase::FmtLog(
                  (CRFNewDatabase *)&v10->vfptr,
                  "CRFWorldDatabase::Update_GuildRank_Step2(char* szDate(%s)) : %s Fail!",
                  v11,
                  &DstBuf);
                result = 0;
              }
            }
            else
            {
              CRFNewDatabase::FmtLog(
                (CRFNewDatabase *)&v10->vfptr,
                "CRFWorldDatabase::Update_GuildRank_Step2(char* szDate(%s)) : %s Fail!",
                v11,
                &DstBuf);
              result = 0;
            }
          }
          else
          {
            CRFNewDatabase::FmtLog(
              (CRFNewDatabase *)&v10->vfptr,
              "CRFWorldDatabase::Update_GuildRank_Step2(char* szDate(%s)) : %s Fail!",
              v11,
              &DstBuf);
            result = 0;
          }
        }
        else
        {
          CRFNewDatabase::FmtLog(
            (CRFNewDatabase *)&v10->vfptr,
            "CRFWorldDatabase::Update_GuildRank_Step2(char* szDate(%s)) : %s Fail!",
            v11,
            &DstBuf);
          result = 0;
        }
      }
      else
      {
        CRFNewDatabase::FmtLog(
          (CRFNewDatabase *)&v10->vfptr,
          "CRFWorldDatabase::Update_GuildRank_Step2(char* szDate(%s)) : %s Fail!",
          v11,
          &DstBuf);
        result = 0;
      }
    }
    else
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v10->vfptr,
        "CRFWorldDatabase::Update_GuildRank_Step2(char* szDate(%s)) : %s Fail!",
        v11,
        &DstBuf);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v10->vfptr,
      "CRFWorldDatabase::Update_GuildRank_Step2(char* szDate(%s)) : %s Fail!",
      v11,
      &DstBuf);
    result = 0;
  }
  return result;
}
