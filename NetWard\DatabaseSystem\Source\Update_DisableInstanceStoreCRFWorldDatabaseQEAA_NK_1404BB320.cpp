#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_DisableInstanceStore@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404BB320

bool  CRFWorldDatabase::Update_DisableInstanceStore(CRFWorldDatabase *this, unsigned int dwSerial)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-D8h]@1
  char DstBuf; // [sp+30h] [bp-A8h]@4
  char v7; // [sp+31h] [bp-A7h]@4
  unsigned int64_t v8; // [sp+C0h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+E0h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 52i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v8 = (unsigned int64_t)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v7, 0, 0x7Fui64);
  sprintf_s(&DstBuf, 0x80ui64, "Update tbl_StoreLimitItem_061212 set dck=1 where serial=%d", dwSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 1);
}
