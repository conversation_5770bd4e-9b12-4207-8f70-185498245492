#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_db_Load_GoldBoxItem@CMainThread@@AEAAEPEAUqry_case_select_golden_box_item@@PEAH@Z
 * Address: 0x14000DC24

char  CMainThread::_db_Load_GoldBoxItem(CMainThread *this, qry_case_select_golden_box_item *pDbGoldenboxitem, int *pnDBSerial)
{
  return CMainThread::_db_Load_GoldBoxItem(this, pDbGoldenboxitem, pnDBSerial);
}
