#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Uninit_fill_n@PEAPEAVCUnmannedTraderDivisionInfo@@_KPEAV1@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@_KAEBQEAV1@AEAV?$allocator@PEAVCUnmannedTraderDivisionInfo@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140011D4C

void  std::_Uninit_fill_n<CUnmannedTraderDivisionInfo * *,unsigned int64_t,CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(CUnmannedTraderDivisionInfo **_First, unsigned int64_t _Count, CUnmannedTraderDivisionInfo *const *_Val, std::allocator<CUnmannedTraderDivisionInfo *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CUnmannedTraderDivisionInfo * *,unsigned int64_t,CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
