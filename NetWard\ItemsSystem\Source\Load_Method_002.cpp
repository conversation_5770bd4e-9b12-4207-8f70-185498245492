#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Load@CUnmannedTraderScheduler@@QEAA_NXZ
 * Address: 0x1403935E0

char  CUnmannedTraderScheduler::Load(CUnmannedTraderScheduler *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v4; // [sp+0h] [bp-148h]@1
  char Dst; // [sp+30h] [bp-118h]@4
  char v6; // [sp+134h] [bp-14h]@4
  CUnmannedTraderScheduler *v7; // [sp+150h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 80i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  memset_0(&Dst, 0, 0xF8ui64);
  v6 = CRFWorldDatabase::Select_UnmannedTraderReservedSchedule(
         pkDB,
         CUnmannedTraderEnvironmentValue::Unmanned_Trader_Max_Schdule_Cnt,
         (_unmannedtrader_reserved_schedule_info *)&Dst);
  if ( v6 == 2 )
  {
    v7->m_bLoad = 1;
    result = 1;
  }
  else if ( v6 )
  {
    result = 0;
  }
  else
  {
    CUnmannedTraderScheduler::Update(v7, (_unmannedtrader_reserved_schedule_info *)&Dst);
    v7->m_bLoad = 1;
    result = 1;
  }
  return result;
}
