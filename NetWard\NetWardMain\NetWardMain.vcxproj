<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>

  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}</ProjectGuid>
    <RootNamespace>NetWardMain</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
  </PropertyGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>ws2_32.lib;winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>ws2_32.lib;winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>

  <ItemGroup>
    <ClCompile Include="main.cpp" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AuthenticationSystem\AuthenticationSystem.vcxproj">
      <Project>{B2C3D4E5-F6G7-8901-BCDE-F23456789012}</Project>
    </ProjectReference>
    <ProjectReference Include="..\CombatSystem\CombatSystem.vcxproj">
      <Project>{C3D4E5F6-G7H8-9012-CDEF-345678901234}</Project>
    </ProjectReference>
    <ProjectReference Include="..\DatabaseSystem\DatabaseSystem.vcxproj">
      <Project>{D4E5F6G7-H8I9-0123-DEF0-456789012345}</Project>
    </ProjectReference>
    <ProjectReference Include="..\EconomySystem\EconomySystem.vcxproj">
      <Project>{E5F6G7H8-I9J0-1234-EF01-567890123456}</Project>
    </ProjectReference>
    <ProjectReference Include="..\ItemsSystem\ItemsSystem.vcxproj">
      <Project>{F6G7H8I9-J0K1-2345-F012-678901234567}</Project>
    </ProjectReference>
    <ProjectReference Include="..\NetworkSystem\NetworkSystem.vcxproj">
      <Project>{G7H8I9J0-K1L2-3456-0123-789012345678}</Project>
    </ProjectReference>
    <ProjectReference Include="..\PlayerSystem\PlayerSystem.vcxproj">
      <Project>{H8I9J0K1-L2M3-4567-1234-890123456789}</Project>
    </ProjectReference>
    <ProjectReference Include="..\SecuritySystem\SecuritySystem.vcxproj">
      <Project>{I9J0K1L2-M3N4-5678-2345-901234567890}</Project>
    </ProjectReference>
    <ProjectReference Include="..\SystemSystem\SystemSystem.vcxproj">
      <Project>{J0K1L2M3-N4O5-6789-3456-012345678901}</Project>
    </ProjectReference>
    <ProjectReference Include="..\WorldSystem\WorldSystem.vcxproj">
      <Project>{K1L2M3N4-O5P6-7890-4567-123456789012}</Project>
    </ProjectReference>
  </ItemGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>