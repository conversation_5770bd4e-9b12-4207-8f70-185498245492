#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_RaceRank_Step5@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404B5D20

char  CRFWorldDatabase::Update_RaceRank_Step5(CRFWorldDatabase *this, char *szDate)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v5; // [sp+0h] [bp-458h]@1
  char DstBuf; // [sp+30h] [bp-428h]@4
  char v7; // [sp+31h] [bp-427h]@4
  unsigned int64_t v8; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+460h] [bp+8h]@1
  char *szDatea; // [sp+468h] [bp+10h]@1

  szDatea = szDate;
  v9 = this;
  v2 = &v5;
  for ( i = 276i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v8 = (unsigned int64_t)&v5 ^ _security_cookie;
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v9->vfptr,
    "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : Start Set Grade tbl_PvpRank%s",
    szDate,
    szDate);
  DstBuf = 0;
  memset(&v7, 0, 0x3FFui64);
  sprintf_s(&DstBuf, 0x400ui64, "update tbl_PvpRank%s set grade=0", szDatea);
  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
  {
    sprintf_s(
      &DstBuf,
      0x400ui64,
      "update tbl_PvpRank%s set grade=3 where lv >= 30 and lv <= 34 and rate <= 6500",
      szDatea);
    if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
    {
      sprintf_s(
        &DstBuf,
        0x400ui64,
        "update tbl_PvpRank%s set grade=2 where lv >= 30 and lv <= 34 and rate > 6500 and rate <= 8500",
        szDatea);
      if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
      {
        sprintf_s(
          &DstBuf,
          0x400ui64,
          "update tbl_PvpRank%s set grade=1 where lv >= 30 and lv <= 34 and rate > 8500 and rate <= 9500",
          szDatea);
        if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
        {
          sprintf_s(
            &DstBuf,
            0x400ui64,
            "update tbl_PvpRank%s set grade=4 where lv >= 35 and lv <= 39 and rate <= 3500",
            szDatea);
          if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
          {
            sprintf_s(
              &DstBuf,
              0x400ui64,
              "update tbl_PvpRank%s set grade=3 where lv >= 35 and lv <= 39 and rate > 3500 and rate <= 6500",
              szDatea);
            if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
            {
              sprintf_s(
                &DstBuf,
                0x400ui64,
                "update tbl_PvpRank%s set grade=2 where lv >= 35 and lv <= 39 and rate > 6500 and rate <= 8500",
                szDatea);
              if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
              {
                sprintf_s(
                  &DstBuf,
                  0x400ui64,
                  "update tbl_PvpRank%s set grade=1 where lv >= 35 and lv <= 39 and rate > 8500 and rate <= 9500",
                  szDatea);
                if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                {
                  sprintf_s(
                    &DstBuf,
                    0x400ui64,
                    "update tbl_PvpRank%s set grade=5 where lv >= 40 and lv <= 44 and rate <= 1500",
                    szDatea);
                  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                  {
                    sprintf_s(
                      &DstBuf,
                      0x400ui64,
                      "update tbl_PvpRank%s set grade=4 where lv >= 40 and lv <= 44 and rate > 1500 and rate <= 3500",
                      szDatea);
                    if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                    {
                      sprintf_s(
                        &DstBuf,
                        0x400ui64,
                        "update tbl_PvpRank%s set grade=3 where lv >= 40 and lv <= 44 and rate > 3500 and rate <= 6500",
                        szDatea);
                      if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                      {
                        sprintf_s(
                          &DstBuf,
                          0x400ui64,
                          "update tbl_PvpRank%s set grade=2 where lv >= 40 and lv <= 44 and rate > 6500 and rate <= 8500",
                          szDatea);
                        if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                        {
                          sprintf_s(
                            &DstBuf,
                            0x400ui64,
                            "update tbl_PvpRank%s set grade=1 where lv >= 40 and lv <= 44 and rate > 8500 and rate <= 9500",
                            szDatea);
                          if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                          {
                            sprintf_s(
                              &DstBuf,
                              0x400ui64,
                              "update tbl_PvpRank%s set grade=6 where lv >= 45 and lv <= 49 and rate <= 500",
                              szDatea);
                            if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                            {
                              sprintf_s(
                                &DstBuf,
                                0x400ui64,
                                "update tbl_PvpRank%s set grade=5 where lv >= 45 and lv <= 49 and rate > 500 and rate <= 1500",
                                szDatea);
                              if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                              {
                                sprintf_s(
                                  &DstBuf,
                                  0x400ui64,
                                  "update tbl_PvpRank%s set grade=4 where lv >= 45 and lv <= 49 and rate > 1500 and rate <= 3500",
                                  szDatea);
                                if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                                {
                                  sprintf_s(
                                    &DstBuf,
                                    0x400ui64,
                                    "update tbl_PvpRank%s set grade=3 where lv >= 45 and lv <= 49 and rate > 3500 and rate <= 6500",
                                    szDatea);
                                  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                                  {
                                    sprintf_s(
                                      &DstBuf,
                                      0x400ui64,
                                      "update tbl_PvpRank%s set grade=2 where lv >= 45 and lv <= 49 and rate > 6500 and rate <= 8500",
                                      szDatea);
                                    if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                                    {
                                      sprintf_s(
                                        &DstBuf,
                                        0x400ui64,
                                        "update tbl_PvpRank%s set grade=1 where lv >= 45 and lv <= 49 and rate > 8500 and rate <= 9500",
                                        szDatea);
                                      if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                                      {
                                        sprintf_s(
                                          &DstBuf,
                                          0x400ui64,
                                          "update tbl_PvpRank%s set grade=7 where lv >= 50 and rate <= 100",
                                          szDatea);
                                        if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                                        {
                                          sprintf_s(
                                            &DstBuf,
                                            0x400ui64,
                                            "update tbl_PvpRank%s set grade=6 where lv >= 50 and rate > 100 and rate <= 500",
                                            szDatea);
                                          if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
                                          {
                                            sprintf_s(
                                              &DstBuf,
                                              0x400ui64,
                                              "update tbl_PvpRank%s set grade=5 where lv >= 50 and rate > 500 and rate <= 1500",
                                              szDatea);
                                            if ( CRFNewDatabase::ExecUpdateQuery(
                                                   (CRFNewDatabase *)&v9->vfptr,
                                                   &DstBuf,
                                                   0) )
                                            {
                                              sprintf_s(
                                                &DstBuf,
                                                0x400ui64,
                                                "update tbl_PvpRank%s set grade=4 where lv >= 50 and rate > 1500 and rate <= 3500",
                                                szDatea);
                                              if ( CRFNewDatabase::ExecUpdateQuery(
                                                     (CRFNewDatabase *)&v9->vfptr,
                                                     &DstBuf,
                                                     0) )
                                              {
                                                sprintf_s(
                                                  &DstBuf,
                                                  0x400ui64,
                                                  "update tbl_PvpRank%s set grade=3 where lv >= 50 and rate > 3500 and rate <= 6500",
                                                  szDatea);
                                                if ( CRFNewDatabase::ExecUpdateQuery(
                                                       (CRFNewDatabase *)&v9->vfptr,
                                                       &DstBuf,
                                                       0) )
                                                {
                                                  sprintf_s(
                                                    &DstBuf,
                                                    0x400ui64,
                                                    "update tbl_PvpRank%s set grade=2 where lv >= 50 and rate > 6500 and rate <= 8500",
                                                    szDatea);
                                                  if ( CRFNewDatabase::ExecUpdateQuery(
                                                         (CRFNewDatabase *)&v9->vfptr,
                                                         &DstBuf,
                                                         0) )
                                                  {
                                                    sprintf_s(
                                                      &DstBuf,
                                                      0x400ui64,
                                                      "update tbl_PvpRank%s set grade=1 where lv >= 50 and rate > 8500 and rate <= 9500",
                                                      szDatea);
                                                    if ( CRFNewDatabase::ExecUpdateQuery(
                                                           (CRFNewDatabase *)&v9->vfptr,
                                                           &DstBuf,
                                                           0) )
                                                    {
                                                      CRFNewDatabase::FmtLog(
                                                        (CRFNewDatabase *)&v9->vfptr,
                                                        "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : End Set Gr"
                                                        "ade tbl_PvpRank%s",
                                                        szDatea,
                                                        szDatea);
                                                      result = 1;
                                                    }
                                                    else
                                                    {
                                                      CRFNewDatabase::FmtLog(
                                                        (CRFNewDatabase *)&v9->vfptr,
                                                        "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                                        szDatea,
                                                        &DstBuf);
                                                      CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                                                      result = 0;
                                                    }
                                                  }
                                                  else
                                                  {
                                                    CRFNewDatabase::FmtLog(
                                                      (CRFNewDatabase *)&v9->vfptr,
                                                      "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                                      szDatea,
                                                      &DstBuf);
                                                    CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                                                    result = 0;
                                                  }
                                                }
                                                else
                                                {
                                                  CRFNewDatabase::FmtLog(
                                                    (CRFNewDatabase *)&v9->vfptr,
                                                    "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                                    szDatea,
                                                    &DstBuf);
                                                  CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                                                  result = 0;
                                                }
                                              }
                                              else
                                              {
                                                CRFNewDatabase::FmtLog(
                                                  (CRFNewDatabase *)&v9->vfptr,
                                                  "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                                  szDatea,
                                                  &DstBuf);
                                                CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                                                result = 0;
                                              }
                                            }
                                            else
                                            {
                                              CRFNewDatabase::FmtLog(
                                                (CRFNewDatabase *)&v9->vfptr,
                                                "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                                szDatea,
                                                &DstBuf);
                                              CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                                              result = 0;
                                            }
                                          }
                                          else
                                          {
                                            CRFNewDatabase::FmtLog(
                                              (CRFNewDatabase *)&v9->vfptr,
                                              "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                              szDatea,
                                              &DstBuf);
                                            CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                                            result = 0;
                                          }
                                        }
                                        else
                                        {
                                          CRFNewDatabase::FmtLog(
                                            (CRFNewDatabase *)&v9->vfptr,
                                            "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                            szDatea,
                                            &DstBuf);
                                          CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                                          result = 0;
                                        }
                                      }
                                      else
                                      {
                                        CRFNewDatabase::FmtLog(
                                          (CRFNewDatabase *)&v9->vfptr,
                                          "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                          szDatea,
                                          &DstBuf);
                                        CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                                        result = 0;
                                      }
                                    }
                                    else
                                    {
                                      CRFNewDatabase::FmtLog(
                                        (CRFNewDatabase *)&v9->vfptr,
                                        "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                        szDatea,
                                        &DstBuf);
                                      CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                                      result = 0;
                                    }
                                  }
                                  else
                                  {
                                    CRFNewDatabase::FmtLog(
                                      (CRFNewDatabase *)&v9->vfptr,
                                      "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                      szDatea,
                                      &DstBuf);
                                    CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                                    result = 0;
                                  }
                                }
                                else
                                {
                                  CRFNewDatabase::FmtLog(
                                    (CRFNewDatabase *)&v9->vfptr,
                                    "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                    szDatea,
                                    &DstBuf);
                                  CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                                  result = 0;
                                }
                              }
                              else
                              {
                                CRFNewDatabase::FmtLog(
                                  (CRFNewDatabase *)&v9->vfptr,
                                  "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                  szDatea,
                                  &DstBuf);
                                CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                                result = 0;
                              }
                            }
                            else
                            {
                              CRFNewDatabase::FmtLog(
                                (CRFNewDatabase *)&v9->vfptr,
                                "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                                szDatea,
                                &DstBuf);
                              CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                              result = 0;
                            }
                          }
                          else
                          {
                            CRFNewDatabase::FmtLog(
                              (CRFNewDatabase *)&v9->vfptr,
                              "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                              szDatea,
                              &DstBuf);
                            CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                            result = 0;
                          }
                        }
                        else
                        {
                          CRFNewDatabase::FmtLog(
                            (CRFNewDatabase *)&v9->vfptr,
                            "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                            szDatea,
                            &DstBuf);
                          CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                          result = 0;
                        }
                      }
                      else
                      {
                        CRFNewDatabase::FmtLog(
                          (CRFNewDatabase *)&v9->vfptr,
                          "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                          szDatea,
                          &DstBuf);
                        CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                        result = 0;
                      }
                    }
                    else
                    {
                      CRFNewDatabase::FmtLog(
                        (CRFNewDatabase *)&v9->vfptr,
                        "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                        szDatea,
                        &DstBuf);
                      CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                      result = 0;
                    }
                  }
                  else
                  {
                    CRFNewDatabase::FmtLog(
                      (CRFNewDatabase *)&v9->vfptr,
                      "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                      szDatea,
                      &DstBuf);
                    CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                    result = 0;
                  }
                }
                else
                {
                  CRFNewDatabase::FmtLog(
                    (CRFNewDatabase *)&v9->vfptr,
                    "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                    szDatea,
                    &DstBuf);
                  CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                  result = 0;
                }
              }
              else
              {
                CRFNewDatabase::FmtLog(
                  (CRFNewDatabase *)&v9->vfptr,
                  "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                  szDatea,
                  &DstBuf);
                CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
                result = 0;
              }
            }
            else
            {
              CRFNewDatabase::FmtLog(
                (CRFNewDatabase *)&v9->vfptr,
                "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
                szDatea,
                &DstBuf);
              CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
              result = 0;
            }
          }
          else
          {
            CRFNewDatabase::FmtLog(
              (CRFNewDatabase *)&v9->vfptr,
              "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
              szDatea,
              &DstBuf);
            CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
            result = 0;
          }
        }
        else
        {
          CRFNewDatabase::FmtLog(
            (CRFNewDatabase *)&v9->vfptr,
            "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
            szDatea,
            &DstBuf);
          CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
          result = 0;
        }
      }
      else
      {
        CRFNewDatabase::FmtLog(
          (CRFNewDatabase *)&v9->vfptr,
          "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
          szDatea,
          &DstBuf);
        CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
        result = 0;
      }
    }
    else
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v9->vfptr,
        "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
        szDatea,
        &DstBuf);
      CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v9->vfptr,
      "CRFWorldDatabase::Update_RaceRank_Step5(szDate(%s)) : %s Fail!",
      szDatea,
      &DstBuf);
    CRFWorldDatabase::Update_RaceRank_Step6(v9, szDatea);
    result = 0;
  }
  return result;
}
