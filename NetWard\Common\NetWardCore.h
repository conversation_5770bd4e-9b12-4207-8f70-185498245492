#pragma once

#ifndef NETWARD_CORE_H
#define NETWARD_CORE_H

// NetWard Server Core Header
// Main include file for RF Online Zone Server Implementation
// Generated from ZoneServerUD_x64 decompiled source

// Standard includes
#include <windows.h>
#include <stdint.h>
#include <memory>
#include <vector>
#include <string>
#include <map>
#include <list>
#include <iostream>
#include <thread>
#include <mutex>

// NetWard version information
#define NETWARD_VERSION_MAJOR 1
#define NETWARD_VERSION_MINOR 0
#define NETWARD_VERSION_PATCH 0
#define NETWARD_VERSION_STRING "1.0.0"

// Common type definitions
typedef unsigned char       BYTE;
typedef unsigned short      WORD;
typedef unsigned long       DWORD;
typedef unsigned long long  QWORD;

// System headers
#include "NetWardAuthentication.h"
#include "NetWardCombat.h"
#include "NetWardDatabase.h"
#include "NetWardGuild.h"
#include "NetWardItems.h"
#include "NetWardNetwork.h"
#include "NetWardQuest.h"
#include "NetWardSecurity.h"
#include "NetWardSystem.h"
#include "NetWardWorld.h"

// Common utility macros
#define SAFE_DELETE(p)       { if(p) { delete (p); (p) = nullptr; } }
#define SAFE_DELETE_ARRAY(p) { if(p) { delete[] (p); (p) = nullptr; } }
#define SAFE_RELEASE(p)      { if(p) { (p)->Release(); (p) = nullptr; } }

// NetWard namespace
namespace NetWard {
    // Server states
    enum class ServerState {
        Stopped,
        Starting,
        Running,
        Stopping,
        Error
    };

    // Common result codes
    enum class Result {
        Success = 0,
        Failed = -1,
        InvalidParameter = -2,
        OutOfMemory = -3,
        NotImplemented = -4
    };
}

#endif // NETWARD_CORE_H
