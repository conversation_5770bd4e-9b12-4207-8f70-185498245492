#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Load@?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@CryptoPP@@UEAAXAEBV?$DL_GroupPrecomputation@UEC2NPoint@CryptoPP@@@2@AEAVBufferedTransformation@2@@Z
 * Address: 0x140575E20

void  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::Load(int64_t a1, int64_t a2, struct CryptoPP::BufferedTransformation *a3)
{
  int64_t v3; // rax@3
  int64_t v4; // rax@6
  uint8_t*v5; // rax@6
  char v6; // [sp+30h] [bp-108h]@1
  CryptoPP::BERGeneralDecoder v7; // [sp+40h] [bp-F8h]@1
  CryptoPP::EC2NPoint v8; // [sp+80h] [bp-B8h]@3
  CryptoPP::EC2NPoint v9; // [sp+B8h] [bp-80h]@6
  int64_t v10; // [sp+F0h] [bp-48h]@1
  int64_t v11; // [sp+F8h] [bp-40h]@1
  int64_t v12; // [sp+100h] [bp-38h]@3
  int64_t v13; // [sp+108h] [bp-30h]@3
  int64_t v14; // [sp+110h] [bp-28h]@6
  uint8_t*v15; // [sp+118h] [bp-20h]@6
  uint8_t*v16; // [sp+120h] [bp-18h]@6
  int64_t v17; // [sp+140h] [bp+8h]@1
  int64_t v18; // [sp+148h] [bp+10h]@1

  v18 = a2;
  v17 = a1;
  v10 = -2i64;
  CryptoPP::BERSequenceDecoder::BERSequenceDecoder((CryptoPP::BERSequenceDecoder *)&v7, a3, 0x30u);
  CryptoPP::BERDecodeUnsigned<unsigned int>((CryptoPP *)&v7, (int *)&v6, 2u, 1u, 1u);
  v11 = *(uint64_t*)(v17 + 72);
  (*(void ( **)(signed int64_t, CryptoPP::BERGeneralDecoder *))(v11 + 8))(v17 + 72, &v7);
  *(uint32_t*)(v17 + 64) = CryptoPP::Integer::BitCount((CryptoPP::Integer *)(v17 + 72)) - 1;
  std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::clear(v17 + 112);
  while ( !CryptoPP::BERGeneralDecoder::EndReached(&v7) )
  {
    LODWORD(v3) = (*(int ( **)(int64_t, CryptoPP::EC2NPoint *, CryptoPP::BERGeneralDecoder *))(*(uint64_t*)v18 + 32i64))(
                    v18,
                    &v8,
                    &v7);
    v12 = v3;
    v13 = v3;
    std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::push_back(v17 + 112, v3);
    CryptoPP::EC2NPoint::~EC2NPoint(&v8);
  }
  if ( !std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::empty(v17 + 112)
    && (unsigned int8_t)(**(int ( ***)(_QWORD))v18)(v18) )
  {
    LODWORD(v4) = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator[](v17 + 112, 0i64);
    v14 = *(uint64_t*)v18;
    LODWORD(v5) = (*(int ( **)(int64_t, CryptoPP::EC2NPoint *, int64_t))(v14 + 16))(v18, &v9, v4);
    v15 = v5;
    v16 = v5;
    CryptoPP::EC2NPoint::operator=((uint8_t*)(v17 + 8), v5);
    CryptoPP::EC2NPoint::~EC2NPoint(&v9);
  }
  CryptoPP::BERGeneralDecoder::MessageEnd(&v7);
  CryptoPP::BERSequenceDecoder::~BERSequenceDecoder((CryptoPP::BERSequenceDecoder *)&v7);
}
