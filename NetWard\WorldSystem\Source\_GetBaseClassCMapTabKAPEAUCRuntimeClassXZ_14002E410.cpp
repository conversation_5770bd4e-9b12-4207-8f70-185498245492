#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_GetBaseClass@CMapTab@@KAPEAUCRuntimeClass@@XZ
 * Address: 0x14002E410

CRuntimeClass * CMapTab::_GetBaseClass()
{
  int64_t *v0; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-28h]@1

  v0 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v0 = -858993460;
    v0 = (int64_t *)((char *)v0 + 4);
  }
  return CPropertyPage::GetThisClass();
}
