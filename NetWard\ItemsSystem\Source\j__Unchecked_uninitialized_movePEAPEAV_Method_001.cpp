#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@stdext@@YAPEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@Z
 * Address: 0x140003BED

CUnmannedTraderDivisionInfo ** stdext::_Unchecked_uninitialized_move<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *,std::allocator<CUnmannedTraderDivisionInfo *>>(CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last, CUnmannedTraderDivisionInfo **_Dest, std::allocator<CUnmannedTraderDivisionInfo *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *,std::allocator<CUnmannedTraderDivisionInfo *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
