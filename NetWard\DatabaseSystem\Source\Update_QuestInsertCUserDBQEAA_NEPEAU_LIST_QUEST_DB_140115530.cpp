#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_QuestInsert@CUserDB@@QEAA_NEPEAU_LIST@_QUEST_DB_BASE@@@Z
 * Address: 0x140115530

char  CUserDB::Update_QuestInsert(CUserDB *this, char bySlotIndex, _QUEST_DB_BASE::_LIST *pSlotData)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-28h]@1
  CUserDB *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned int8_t)bySlotIndex < 30 )
  {
    if ( v7->m_AvatorData.dbQuest.m_List[(unsigned int8_t)bySlotIndex].byQuestType == 255 )
    {
      memcpy_0((char *)&v7->m_AvatorData.dbQuest + 13 * (unsigned int8_t)bySlotIndex, pSlotData, 0xDui64);
      v7->m_bDataUpdate = 1;
      result = 1;
    }
    else
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_QuestInsert(EXIST) : slot : %d",
        v7->m_aszAvatorName,
        (unsigned int8_t)bySlotIndex);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_QuestInsert(SlotIndex OVER) : slot : %d",
      v7->m_aszAvatorName,
      (unsigned int8_t)bySlotIndex);
    result = 0;
  }
  return result;
}
