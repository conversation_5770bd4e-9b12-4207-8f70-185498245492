#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ReRegist@CUnmannedTraderController@@QEAAXGPEAU_unmannedtrader_re_regist_request_clzo@@@Z
 * Address: 0x1401D4B40

void  CUnmannedTraderController::ReRegist(CUnmannedTraderController *this, unsigned int16_t wInx, _unmannedtrader_re_regist_request_clzo *pRequest)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  CUnmannedTraderUserInfoTable *v5; // rax@4
  int64_t v6; // [sp+0h] [bp-28h]@1
  unsigned int16_t v7; // [sp+38h] [bp+10h]@1
  _unmannedtrader_re_regist_request_clzo *pRequesta; // [sp+40h] [bp+18h]@1

  pRequesta = pRequest;
  v7 = wInx;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v5 = CUnmannedTraderUserInfoTable::Instance();
  CUnmannedTraderUserInfoTable::ReRegist(v5, v7, 0, pRequesta);
}
