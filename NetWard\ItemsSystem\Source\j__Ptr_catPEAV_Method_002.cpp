#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Ptr_cat@PEAVCUnmannedTraderRegistItemInfo@@PEAV1@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAPEAVCUnmannedTraderRegistItemInfo@@0@Z
 * Address: 0x14000A87B

std::_Nonscalar_ptr_iterator_tag  std::_Ptr_cat<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *>(CUnmannedTraderRegistItemInfo **__formal, CUnmannedTraderRegistItemInfo **a2)
{
  return std::_Ptr_cat<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *>(__formal, a2);
}
