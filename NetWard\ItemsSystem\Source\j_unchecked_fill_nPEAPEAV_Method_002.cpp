#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_fill_n@PEAPEAVCUnmannedTraderSortType@@_KPEAV1@@stdext@@YAXPEAPEAVCUnmannedTraderSortType@@_KAEBQEAV1@@Z
 * Address: 0x140002C93

void  stdext::unchecked_fill_n<CUnmannedTraderSortType * *,unsigned int64_t,CUnmannedTraderSortType *>(CUnmannedTraderSortType **_First, unsigned int64_t _Count, CUnmannedTraderSortType *const *_Val)
{
  stdext::unchecked_fill_n<CUnmannedTraderSortType * *,unsigned int64_t,CUnmannedTraderSortType *>(_First, _Count, _Val);
}
