#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Set_Event_Status@CGoldenBoxItemMgr@@QEAAXE@Z
 * Address: 0x140412280

void  CGoldenBoxItemMgr::Set_Event_Status(CGoldenBoxItemMgr *this, char byStatus)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  unsigned int v4; // ecx@4
  int64_t v5; // [sp+0h] [bp-38h]@1
  CLogFile *v6; // [sp+20h] [bp-18h]@4
  CGoldenBoxItemMgr *v7; // [sp+40h] [bp+8h]@1
  char v8; // [sp+48h] [bp+10h]@1

  v8 = byStatus;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v4 = v7->m_golden_box_event.m_event_status;
  v6 = &v7->m_golden_box_event.m_event_log;
  CLogFile::Write(&v7->m_golden_box_event.m_event_log, "[EventStateChange : %d -> %d ]", v4, (unsigned int8_t)byStatus);
  v7->m_golden_box_event.m_event_status = v8;
}
