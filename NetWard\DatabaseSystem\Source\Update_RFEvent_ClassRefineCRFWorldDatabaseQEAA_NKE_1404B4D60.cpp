#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_RFEvent_ClassRefine@CRFWorldDatabase@@QEAA_NKEK@Z
 * Address: 0x1404B4D60

bool  CRFWorldDatabase::Update_RFEvent_ClassRefine(CRFWorldDatabase *this, unsigned int dwAvatorSerial, char byRefineCnt, unsigned int dwRefineDate)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-168h]@1
  unsigned int v8; // [sp+20h] [bp-148h]@4
  unsigned int v9; // [sp+28h] [bp-140h]@4
  char DstBuf; // [sp+40h] [bp-128h]@4
  char v11; // [sp+41h] [bp-127h]@4
  unsigned int64_t v12; // [sp+150h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+170h] [bp+8h]@1

  v13 = this;
  v4 = &v7;
  for ( i = 88i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v12 = (unsigned int64_t)&v7 ^ _security_cookie;
  DstBuf = 0;
  memset(&v11, 0, 0xFFui64);
  v9 = dwAvatorSerial;
  v8 = dwRefineDate;
  sprintf_s(
    &DstBuf,
    0x100ui64,
    "update [dbo].[tbl_event] set ClassRefineCnt=%d,ClassRefineDate=%d where avatorserial = %d",
    (unsigned int8_t)byRefineCnt);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v13->vfptr, &DstBuf, 1);
}
