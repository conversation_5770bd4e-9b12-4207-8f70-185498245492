#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: lua_getmetatable
 * Address: 0x140533A30

signed int64_t  lua_getmetatable(int64_t a1, signed int a2)
{
  uint32_t*v2; // rax@1
  int64_t v3; // r10@1
  int64_t v4; // rcx@1
  int64_t v5; // rcx@3
  signed int64_t result; // rax@6
  int64_t v7; // rax@7

  v2 = sub_140532CD0(a1, a2);
  v4 = v2[2];
  if ( (_DWORD)v4 == 5 || (_DWORD)v4 == 7 )
    v5 = *(uint64_t*)(*(uint64_t*)v2 + 16i64);
  else
    v5 = *(uint64_t*)(*(uint64_t*)(v3 + 32) + 8 * v4 + 224);
  if ( v5 )
  {
    v7 = *(uint64_t*)(v3 + 16);
    *(uint64_t*)v7 = v5;
    *(uint32_t*)(v7 + 8) = 5;
    *(uint64_t*)(v3 + 16) += 16i64;
    result = 1i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
