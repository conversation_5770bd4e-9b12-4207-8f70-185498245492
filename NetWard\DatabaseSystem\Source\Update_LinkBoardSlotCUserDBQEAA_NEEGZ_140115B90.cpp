#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_LinkBoardSlot@CUserDB@@QEAA_NEEG@Z
 * Address: 0x140115B90

char  CUserDB::Update_LinkBoardSlot(CUserDB *this, char bySlot, char byLinkCode, unsigned int16_t wIndex)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v7; // [sp+0h] [bp-28h]@1
  CUserDB *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned int8_t)bySlot < 50 )
  {
    if ( (unsigned int8_t)byLinkCode == 255 )
    {
      _LINKKEY::SetRelease((_LINKKEY *)&v8->m_AvatorData.dbLink + (unsigned int8_t)bySlot);
    }
    else if ( (signed int)(unsigned int8_t)byLinkCode < 7 )
    {
      _LINKKEY::SetData(
        (_LINKKEY *)&v8->m_AvatorData.dbLink + (unsigned int8_t)bySlot,
        (unsigned int8_t)byLinkCode,
        wIndex);
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
