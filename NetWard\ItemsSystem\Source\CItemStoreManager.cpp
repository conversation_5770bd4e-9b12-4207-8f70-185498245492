// CItemStoreManager Implementation
// NetWard Server - RF Online Zone Server
// Generated from decompiled IDA Pro source

#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

// CItemStoreManager class implementation

// Function: ??0CItemStoreManager@@QEAA@XZ
// Address: 0x140348020
CItemStoreManager::CItemStoreManager()
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-38h]@1
  int64_t v4; // [sp+20h] [bp-18h]@4
  CItemStoreManager *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12LL; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4 = -2LL;
  CMyTimer::CMyTimer(&v5->m_tmrCheckTime);
  CMyTimer::CMyTimer(&v5->m_tmrSaveTime);
  _qry_case_all_store_limit_item::_qry_case_all_store_limit_item(&v5->m_Sheet);
  v5->m_nInstanceItemStoreListNum = 0;
  v5->m_InstanceItemStoreList = 0;
  CRecordData::CRecordData(&v5->m_tblItemStore);
  v5->m_nMapItemStoreListNum = 0;
  v5->m_MapItemStoreList = 0;
}

// Function: ??1CItemStoreManager@@QEAA@XZ
// Address: 0x140348170
CItemStoreManager::~CItemStoreManager()
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-68h]@1
  CMapItemStoreList *v4; // [sp+20h] [bp-48h]@5
  CMapItemStoreList *v5; // [sp+28h] [bp-40h]@5
  CMapItemStoreList *v6; // [sp+30h] [bp-38h]@10
  CMapItemStoreList *v7; // [sp+38h] [bp-30h]@10
  int64_t v8; // [sp+40h] [bp-28h]@4
  void *v9; // [sp+48h] [bp-20h]@6
  void *v10; // [sp+50h] [bp-18h]@11
  CItemStoreManager *v11; // [sp+70h] [bp+8h]@1

  v11 = this;
  v1 = &v3;
  for ( i = 24LL; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v8 = -2LL;
  if ( v11->m_MapItemStoreList )
  {
    v5 = v11->m_MapItemStoreList;
    v4 = v5;
    if ( v5 )
      v9 = CMapItemStoreList::~CMapItemStoreList(v4);
    else
      v9 = 0;
    v11->m_MapItemStoreList = 0;
  }
  if ( v11->m_InstanceItemStoreList )
  {
    v7 = v11->m_InstanceItemStoreList;
    v6 = v7;
    if ( v7 )
      v10 = CMapItemStoreList::~CMapItemStoreList(v6);
    else
      v10 = 0;
    v11->m_InstanceItemStoreList = 0;
  }
  CRecordData::~CRecordData(&v11->m_tblItemStore);
  _qry_case_all_store_limit_item::~_qry_case_all_store_limit_item(&v11->m_Sheet);
  CMyTimer::~CMyTimer(&v11->m_tmrSaveTime);
  CMyTimer::~CMyTimer(&v11->m_tmrCheckTime);
}