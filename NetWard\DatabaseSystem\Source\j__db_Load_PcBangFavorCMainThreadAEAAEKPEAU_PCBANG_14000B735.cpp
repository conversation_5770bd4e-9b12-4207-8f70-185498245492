#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_db_Load_PcBangFavor@CMainThread@@AEAAEKPEAU_PCBANG_FAVOR_ITEM_DB_BASE@@@Z
 * Address: 0x14000B735

char  CMainThread::_db_Load_PcBangFavor(CMainThread *this, unsigned int dwSerial, _PCBANG_FAVOR_ITEM_DB_BASE *pDbPcBangFavor)
{
  return CMainThread::_db_Load_PcBangFavor(this, dwSerial, pDbPcBangFavor);
}
