#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?insert@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEAAXV?$_Vector_iterator@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@2@_KAEBQEAVCMoveMapLimitInfo@@@Z
 * Address: 0x1400024BE

void  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::insert(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *_Where, unsigned int64_t _Count, CMoveMapLimitInfo *const *_Val)
{
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::insert(this, _Where, _Count, _Val);
}
