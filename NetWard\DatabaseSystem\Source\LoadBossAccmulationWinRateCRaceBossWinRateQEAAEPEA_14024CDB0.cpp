#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?LoadBossAccmulationWinRate@CRaceBossWinRate@@QEAAEPEAU_qry_case_raceboss_accumulation_winrate@@@Z
 * Address: 0x14024CDB0

char  CRaceBossWinRate::LoadBossAccmulationWinRate(CRaceBossWinRate *this, _qry_case_raceboss_accumulation_winrate *pData)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@9
  int64_t v6; // [sp+0h] [bp-58h]@1
  char v7; // [sp+20h] [bp-38h]@4
  _raceboss_acc_winrate windata; // [sp+38h] [bp-20h]@13
  unsigned int dwBossSerial; // [sp+44h] [bp-14h]@6
  unsigned int j; // [sp+48h] [bp-10h]@6
  char v11; // [sp+4Ch] [bp-Ch]@13
  CRaceBossWinRate *v12; // [sp+60h] [bp+8h]@1
  _qry_case_raceboss_accumulation_winrate *v13; // [sp+68h] [bp+10h]@1

  v13 = pData;
  v12 = this;
  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v7 = 1;
  if ( pData )
    v7 = 0;
  dwBossSerial = -1;
  for ( j = 0; (signed int)j < 3; ++j )
  {
    v4 = CPvpUserAndGuildRankingSystem::Instance();
    dwBossSerial = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, j, 0);
    if ( dwBossSerial != -1 )
    {
      if ( v7 )
      {
        v12->m_dwAccBattleCnt[j] = 0;
        v12->m_dwAccWinCnt[j] = 0;
      }
      else
      {
        v13->dwTotalCnt[j] = 0;
        v13->dwWinCnt[j] = 0;
      }
      v11 = CRFWorldDatabase::Select_RaceBossAccumulationWinRate(pkDB, j, dwBossSerial, &windata);
      if ( v11 == 1 )
      {
        CLogFile::Write(
          &stru_1799C8E78,
          "CRaceBossWinRate::LoadBossAccmulationWinRate() : Select_RaceBossAccumulationWinRate( %d, %d ) Boss Accumulatio"
          "n Win Rate Load Error",
          j,
          dwBossSerial);
      }
      else if ( v7 )
      {
        v12->m_dwAccBattleCnt[j] = windata.dwTotalCnt;
        v12->m_dwAccWinCnt[j] = windata.dwWinCnt;
      }
      else
      {
        v13->dwTotalCnt[j] = windata.dwTotalCnt;
        v13->dwWinCnt[j] = windata.dwWinCnt;
      }
    }
  }
  return 0;
}
