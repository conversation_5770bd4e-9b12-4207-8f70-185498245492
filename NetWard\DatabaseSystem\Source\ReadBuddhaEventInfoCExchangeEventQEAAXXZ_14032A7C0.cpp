#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ReadBuddhaEventInfo@CExchangeEvent@@QEAAXXZ
 * Address: 0x14032A7C0

void  CExchangeEvent::ReadBuddhaEventInfo(CExchangeEvent *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-88h]@1
  char ReturnedString; // [sp+38h] [bp-50h]@4
  char v5; // [sp+39h] [bp-4Fh]@4
  char Str1; // [sp+58h] [bp-30h]@4
  char v7; // [sp+59h] [bp-2Fh]@4
  bool v8; // [sp+70h] [bp-18h]@4
  bool v9; // [sp+71h] [bp-17h]@4
  unsigned int64_t v10; // [sp+78h] [bp-10h]@4
  CExchangeEvent *v11; // [sp+90h] [bp+8h]@1

  v11 = this;
  v1 = &v3;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v10 = (unsigned int64_t)&v3 ^ _security_cookie;
  ReturnedString = 0;
  memset(&v5, 0, 7ui64);
  Str1 = 0;
  memset(&v7, 0, 7ui64);
  GetPrivateProfileStringA("Buddha Event", "Enable", "FALSE", &ReturnedString, 8u, ".\\Initialize\\WorldSystem.ini");
  GetPrivateProfileStringA("Buddha Event", "Delete", "TRUE", &Str1, 8u, ".\\Initialize\\WorldSystem.ini");
  GetPrivateProfileStringA(
    "Buddha Event",
    "GiveItem",
    &byte_1407E662F,
    (LPSTR)v11->m_ModifyItemCode,
    0x40u,
    ".\\Initialize\\WorldSystem.ini");
  GetPrivateProfileStringA(
    "Buddha Event",
    "ExchangeItem",
    &byte_1407E6641,
    v11->m_ModifyItemCode[1],
    0x40u,
    ".\\Initialize\\WorldSystem.ini");
  GetPrivateProfileStringA(
    "Buddha Event",
    "DeleteItem1",
    &byte_1407E6642,
    v11->m_ModifyItemCode[2],
    0x40u,
    ".\\Initialize\\WorldSystem.ini");
  GetPrivateProfileStringA(
    "Buddha Event",
    "DeleteItem2",
    &byte_1407E6643,
    v11->m_ModifyItemCode[3],
    0x40u,
    ".\\Initialize\\WorldSystem.ini");
  v8 = strcmp_0(&ReturnedString, "TRUE") == 0;
  v11->m_bModifyEnable = v8;
  v9 = strcmp_0(&Str1, "TRUE") == 0;
  v11->m_bModifyDelete = v9;
}
