#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Destroy@VCMoveMapLimitRightInfo@@@std@@YAXPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403A38D0

void  std::_Destroy<CMoveMapLimitRightInfo>(CMoveMapLimitRightInfo *_Ptr)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfo *v4; // [sp+30h] [bp+8h]@1

  v4 = _Ptr;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  CMoveMapLimitRightInfo::`scalar deleting destructor'(v4, 0);
}
