#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Iter_random@PEAVCUnmannedTraderItemCodeInfo@@PEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAVCUnmannedTraderItemCodeInfo@@0@Z
 * Address: 0x14000D0E9

std::random_access_iterator_tag  std::_Iter_random<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *>(CUnmannedTraderItemCodeInfo *const *__formal, CUnmannedTraderItemCodeInfo *const *a2)
{
  return std::_Iter_random<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *>(__formal, a2);
}
