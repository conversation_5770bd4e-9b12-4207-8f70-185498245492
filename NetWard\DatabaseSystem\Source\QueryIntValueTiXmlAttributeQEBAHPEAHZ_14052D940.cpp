#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?QueryIntValue@TiXmlAttribute@@QEBAHPEAH@Z
 * Address: 0x14052D940

int64_t  TiXmlAttribute::QueryIntValue(TiXmlAttribute *this, int *a2)
{
  int v2; // eax@1
  signed int v3; // ecx@1

  v2 = sscanf(this->value.rep_->str, "%d", a2);
  v3 = 2;
  if ( v2 == 1 )
    v3 = 0;
  return (unsigned int)v3;
}
