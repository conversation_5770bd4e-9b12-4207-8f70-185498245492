#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Loop@CUnmannedTraderTaxRateManager@@QEAAXXZ
 * Address: 0x14038DE90

void  CUnmannedTraderTaxRateManager::Loop(CUnmannedTraderTaxRateManager *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  TRC_AutoTrade **v3; // rax@10
  int64_t v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@8
  CUnmannedTraderTaxRateManager *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v1 = -*********;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v6->m_pkTimer
    && !std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v6->m_vecTRC)
    && CMyTimer::CountingTimer(v6->m_pkTimer) )
  {
    for ( j = 0; j < 3; ++j )
    {
      v3 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v6->m_vecTRC, j);
      TRC_AutoTrade::ChangeTaxRate(*v3);
    }
  }
}
