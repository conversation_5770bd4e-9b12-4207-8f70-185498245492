#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Move_cat@PEAVCUnmannedTraderSchedule@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAVCUnmannedTraderSchedule@@@Z
 * Address: 0x14000B7EE

std::_Undefined_move_tag  std::_Move_cat<CUnmannedTraderSchedule *>(CUnmannedTraderSchedule *const *__formal)
{
  return std::_Move_cat<CUnmannedTraderSchedule *>(__formal);
}
