#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLValidDSNW
 * Address: 0x1404DB0E8

int  SQLValidDSNW(const unsigned int16_t *lpszDSN)
{
  const unsigned int16_t *v1; // rbx@1
  int64_t ( *v2)(); // rax@1
  int result; // eax@2

  v1 = lpszDSN;
  v2 = ODBC___GetSetupProc("SQLValidDSNW");
  if ( v2 )
    result = ((int ( *)(const unsigned int16_t *))v2)(v1);
  else
    result = 0;
  return result;
}
