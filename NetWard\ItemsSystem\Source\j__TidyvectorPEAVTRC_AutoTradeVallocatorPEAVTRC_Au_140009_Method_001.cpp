#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Tidy@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@IEAAXXZ
 * Address: 0x140009CF5

void  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Tidy(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Tidy(this);
}
