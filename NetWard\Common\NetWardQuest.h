#pragma once

#ifndef NETWARD_QUEST_H
#define NETWARD_QUEST_H

// NetWard Quest System Header
// Generated from RF Online ZoneServerUD_x64 decompiled source

#include <windows.h>
#include <stdint.h>
#include <memory>
#include <vector>
#include <string>

// Forward declarations
struct CEventLootTable;
struct CEventLootTable::_event_drop;
struct CEventLootTableVtbl;
struct _NPCQuestIndexTempData::_IndexData;
struct _NPCQuestIndexTempData;

// Class definitions

// CEventLootTable
struct CEventLootTable
{
 CEventLootTableVtbl *vfptr;
 CEventLootTable::_event_drop *m_pEventDropList;
};

// CEventLootTable::_event_drop
struct CEventLootTable::_event_drop
{
 char strCode[64];
 unsigned int16_t wMagnifications;
 unsigned int16_t wRange;
 unsigned int16_t wDropCntOnce;
 unsigned int16_t wDropDelay;
 CEventLootTable::_event_drop *pNext;
};

// CEventLootTableVtbl
struct CEventLootTableVtbl
{
 void *( *__vecDelDtor)(CEventLootTable *this, unsigned int);
};

// _NPCQuestIndexTempData::_IndexData
struct _NPCQuestIndexTempData::_IndexData
{
 unsigned int dwQuestHappenIndex;
 unsigned int dwQuestIndex;
};

// _NPCQuestIndexTempData
struct _NPCQuestIndexTempData
{
 int nQuestNum;
 _NPCQuestIndexTempData::_IndexData IndexData[30];
};


#endif // NETWARD_QUEST_H
