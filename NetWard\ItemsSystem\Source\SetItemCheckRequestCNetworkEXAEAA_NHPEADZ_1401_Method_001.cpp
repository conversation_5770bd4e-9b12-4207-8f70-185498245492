#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetItemCheckRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB620

char  CNetworkEX::SetItemCheckRequest(CNetworkEX *this, int n, char *pBuf)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-48h]@1
  bool bSet; // [sp+20h] [bp-28h]@6
  char *v8; // [sp+30h] [bp-18h]@4
  CPlayer *v9; // [sp+38h] [bp-10h]@4

  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v8 = pBuf;
  v9 = &g_Player + n;
  if ( v9->m_bOper )
  {
    bSet = *v8;
    CPlayer::pc_SetItemCheckRequest(v9, *(uint32_t*)(v8 + 1), v8[5], v8[6], bSet);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
