#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Uninit_move@PEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV1@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@U_Undefined_move_tag@3@@std@@YAPEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000AF92

CUnmannedTraderGroupDivisionVersionInfo * std::_Uninit_move<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>,std::_Undefined_move_tag>(CUnmannedTraderGroupDivisionVersionInfo *_First, CUnmannedTraderGroupDivisionVersionInfo *_Last, CUnmannedTraderGroupDivisionVersionInfo *_Dest, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
