#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Iter_random@PEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCUnmannedTraderClassInfo@@0@Z
 * Address: 0x1400106A9

std::random_access_iterator_tag  std::_Iter_random<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *>(CUnmannedTraderClassInfo **const *__formal, CUnmannedTraderClassInfo **const *a2)
{
  return std::_Iter_random<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *>(__formal, a2);
}
