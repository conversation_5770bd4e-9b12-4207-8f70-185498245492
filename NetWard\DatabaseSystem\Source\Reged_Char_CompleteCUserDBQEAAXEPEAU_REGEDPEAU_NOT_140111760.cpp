#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Reged_Char_Complete@CUserDB@@QEAAXEPEAU_REGED@@PEAU_NOT_ARRANGED_AVATOR_DB@@@Z
 * Address: 0x140111760

void  CUserDB::Reged_Char_Complete(CUserDB *this, char byRetCode, _REGED *pRegedList, _NOT_ARRANGED_AVATOR_DB *pArrangedList)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  unsigned int16_t v6; // ax@26
  unsigned int16_t v7; // ax@35
  int64_t v8; // [sp+0h] [bp-F38h]@1
  int j; // [sp+30h] [bp-F08h]@5
  _REGED *v10; // [sp+38h] [bp-F00h]@8
  _reged_char_result_zone Dst; // [sp+50h] [bp-EE8h]@10
  int nCharNum; // [sp+134h] [bp-E04h]@10
  int k; // [sp+138h] [bp-E00h]@11
  char v14; // [sp+13Ch] [bp-DFCh]@20
  char pbyType; // [sp+144h] [bp-DF4h]@26
  char v16; // [sp+145h] [bp-DF3h]@26
  _not_arranged_char_inform_zocl v17; // [sp+170h] [bp-DC8h]@30
  int l; // [sp+EF4h] [bp-44h]@30
  char v19; // [sp+F04h] [bp-34h]@35
  char v20; // [sp+F05h] [bp-33h]@35
  unsigned int64_t v21; // [sp+F20h] [bp-18h]@4
  CUserDB *v22; // [sp+F40h] [bp+8h]@1
  char v23; // [sp+F48h] [bp+10h]@1
  _REGED *Src; // [sp+F50h] [bp+18h]@1
  _NOT_ARRANGED_AVATOR_DB *v25; // [sp+F58h] [bp+20h]@1

  v25 = pArrangedList;
  Src = pRegedList;
  v23 = byRetCode;
  v22 = this;
  v4 = &v8;
  for ( i = 972i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v21 = (unsigned int64_t)&v8 ^ _security_cookie;
  v22->m_bDBWaitState = 0;
  if ( !byRetCode )
  {
    memcpy_0(v22->m_RegedList, pRegedList, 0x327ui64);
    for ( j = 0; j < 3; ++j )
    {
      v10 = &v22->m_RegedList[j];
      if ( v22->m_RegedList[j].m_bySlotIndex != 255 )
        _REGED::UpdateEquipLv(v10);
    }
  }
  _reged_char_result_zone::_reged_char_result_zone(&Dst);
  Dst.byRetCode = v23;
  nCharNum = 0;
  if ( !v23 )
  {
    for ( k = 0; k < 3; ++k )
    {
      if ( v22->m_RegedList[k].m_bySlotIndex != 255 )
      {
        memcpy_0(&Dst.RegedList[nCharNum], &v22->m_RegedList[k], 0x45ui64);
        if ( Dst.RegedList[nCharNum].m_dwDalant > 0x77359400 )
          Dst.RegedList[nCharNum].m_dwDalant = 2000000000;
        if ( Dst.RegedList[nCharNum].m_dwGold > 0x7A120 )
          Dst.RegedList[nCharNum].m_dwGold = 500000;
        if ( !v22->m_RegedList[k].m_byLevel )
        {
          Dst.RegedList[nCharNum].m_dwDalant = 0;
          Dst.RegedList[nCharNum].m_dwGold = 0;
        }
        v14 = CUserDB::IsExistRequestMoveCharacterList(v22, Src[k].m_dwRecordNum);
        if ( v14 == 1 )
        {
          Dst.iLockType[k] = 1;
        }
        else
        {
          v14 = 2;
          Dst.iLockType[k] = 2;
        }
        ++nCharNum;
      }
    }
    Dst.byCharNum = nCharNum;
  }
  pbyType = 1;
  v16 = 9;
  v6 = _reged_char_result_zone::size(&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, v22->m_idWorld.wIndex, &pbyType, &Dst.byRetCode, v6);
  memcpy_0(v22->m_NotArrangedChar, v25, 0xD7Aui64);
  CMgrAccountLobbyHistory::reged_char_complete(
    &CUserDB::s_MgrLobbyHistory,
    v23,
    nCharNum,
    Src,
    v22->m_szLobbyHistoryFileName);
  if ( !v23 && v22->m_bChatLock )
    CUserDB::SetChatLock(v22, 1);
  if ( !v23 )
  {
    _not_arranged_char_inform_zocl::_not_arranged_char_inform_zocl(&v17);
    nCharNum = 0;
    for ( l = 0; l < 50; ++l )
    {
      if ( v25[l].dwSerial != -1 )
        memcpy_0(&v17.CharList[nCharNum++], &v25[l], 0x45ui64);
    }
    v17.byCharNum = nCharNum;
    v19 = 1;
    v20 = 21;
    v7 = _not_arranged_char_inform_zocl::size(&v17);
    CNetProcess::LoadSendMsg(unk_1414F2088, v22->m_idWorld.wIndex, &v19, &v17.byCharNum, v7);
  }
  if ( !v22->m_byUserDgr )
    CUserDB::SendMsg_Inform_UILock(v22);
}
