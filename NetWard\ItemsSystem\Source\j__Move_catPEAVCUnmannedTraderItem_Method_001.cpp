#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Move_cat@PEAVCUnmannedTraderItemCodeInfo@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAVCUnmannedTraderItemCodeInfo@@@Z
 * Address: 0x140005A6F

std::_Undefined_move_tag  std::_Move_cat<CUnmannedTraderItemCodeInfo *>(CUnmannedTraderItemCodeInfo *const *__formal)
{
  return std::_Move_cat<CUnmannedTraderItemCodeInfo *>(__formal);
}
