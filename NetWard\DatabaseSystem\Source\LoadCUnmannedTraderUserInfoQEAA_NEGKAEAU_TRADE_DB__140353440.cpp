#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Load@CUnmannedTraderUserInfo@@QEAA_NEGKAEAU_TRADE_DB_BASE@@PEAVCLogFile@@@Z
 * Address: 0x140353440

bool  CUnmannedTraderUserInfo::Load(CUnmannedTraderUserInfo *this, char byType, unsigned int16_t wInx, unsigned int dwSerial, _TRADE_DB_BASE *kInfo, CLogFile *pkLogger)
{
  int64_t *v6; // rdi@1
  signed int64_t i; // rcx@1
  bool result; // al@5
  int64_t v9; // [sp+0h] [bp-48h]@1
  CPlayer *v10; // [sp+30h] [bp-18h]@6
  int v11; // [sp+38h] [bp-10h]@7
  CUnmannedTraderUserInfo *v12; // [sp+50h] [bp+8h]@1
  char v13; // [sp+58h] [bp+10h]@1
  unsigned int dwSeriala; // [sp+68h] [bp+20h]@1

  dwSeriala = dwSerial;
  v13 = byType;
  v12 = this;
  v6 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v6 = -858993460;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  if ( (signed int)wInx < 2532 )
  {
    v10 = &g_Player + wInx;
    if ( CPlayerDB::GetPtrCurClass(&v10->m_Param)->m_nClass == 3 )
      v11 = 10;
    else
      v11 = CUnmannedTraderEnvironmentValue::Unmanned_Trader_Nomal_Max_Regist_Item_Cnt;
    v12->m_byMaxRegistCnt = v11;
    result = CUnmannedTraderUserInfo::SetLoadInfo(v12, v13, dwSeriala, kInfo, pkLogger);
  }
  else
  {
    result = 0;
  }
  return result;
}
