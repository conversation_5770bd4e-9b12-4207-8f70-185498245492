#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?personal_amine_stop@CMgrAvatorItemHistory@@QEAAXPEBKHEGPEAD@Z
 * Address: 0x14023FF30

void  CMgrAvatorItemHistory::personal_amine_stop(CMgrAvatorItemHistory *this, const unsigned int *pdwMineCnt, int nMaxOreNum, char byTblCode, unsigned int16_t wItemIndex, char *szFileName)
{
  int64_t *v6; // rdi@1
  signed int64_t i; // rcx@1
  char *v8; // rax@4
  char *v9; // rax@6
  size_t v10; // rax@6
  int64_t v11; // [sp+0h] [bp-58h]@1
  unsigned int v12; // [sp+20h] [bp-38h]@6
  char *DstBuf; // [sp+30h] [bp-28h]@4
  int nItemIndex; // [sp+38h] [bp-20h]@4
  int64_t v15; // [sp+40h] [bp-18h]@6
  CMgrAvatorItemHistory *v16; // [sp+60h] [bp+8h]@1
  const unsigned int *v17; // [sp+68h] [bp+10h]@1
  int v18; // [sp+70h] [bp+18h]@1
  char v19; // [sp+78h] [bp+20h]@1

  v19 = byTblCode;
  v18 = nMaxOreNum;
  v17 = pdwMineCnt;
  v16 = this;
  v6 = &v11;
  for ( i = 20i64; i; --i )
  {
    *(uint32_t*)v6 = -858993460;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  memset_0(sData, 0, 0x4E20ui64);
  v8 = GetItemKorName((unsigned int8_t)v19, wItemIndex);
  sprintf_s(sData, 0x4E20ui64, "[PERSONAL_AMINE_STOP] - %s\r\n", v8);
  DstBuf = &sData[strlen_0(sData)];
  for ( nItemIndex = 0; nItemIndex < v18; ++nItemIndex )
  {
    v15 = nItemIndex;
    v9 = GetItemKorName(17, nItemIndex);
    v12 = v17[v15];
    sprintf_s(DstBuf, 20000 - (DstBuf - sData), "%s >> num:%d\r\n", v9);
    v10 = strlen_0(DstBuf);
    DstBuf += v10;
  }
  CMgrAvatorItemHistory::WriteFile(v16, szFileName, sData);
}
