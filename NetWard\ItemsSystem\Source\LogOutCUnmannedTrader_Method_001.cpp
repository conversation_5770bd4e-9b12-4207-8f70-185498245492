#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?LogOut@CUnmannedTraderController@@QEAAXGK@Z
 * Address: 0x14007A270

void  CUnmannedTraderController::LogOut(CUnmannedTraderController *this, unsigned int16_t wInx, unsigned int dwSerial)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  CUnmannedTraderUserInfoTable *v5; // rax@4
  int64_t v6; // [sp+0h] [bp-28h]@1
  unsigned int16_t v7; // [sp+38h] [bp+10h]@1
  unsigned int dwSeriala; // [sp+40h] [bp+18h]@1

  dwSeriala = dwSerial;
  v7 = wInx;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v5 = CUnmannedTraderUserInfoTable::Instance();
  CUnmannedTraderUserInfoTable::LogOut(v5, v7, dwSeriala);
}
