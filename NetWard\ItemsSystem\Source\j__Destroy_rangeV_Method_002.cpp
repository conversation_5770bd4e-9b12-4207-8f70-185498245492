#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Destroy_range@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@YAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@0AEAV?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z
 * Address: 0x140012828

void  std::_Destroy_range<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(CUnmannedTraderGroupDivisionVersionInfo *_First, CUnmannedTraderGroupDivisionVersionInfo *_Last, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal)
{
  std::_Destroy_range<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(
    _First,
    _Last,
    _Al,
    __formal);
}
