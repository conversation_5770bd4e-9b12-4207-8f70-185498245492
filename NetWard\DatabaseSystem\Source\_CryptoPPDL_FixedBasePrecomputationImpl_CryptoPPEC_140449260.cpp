#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint_::_DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__::_1_::dtor$1
 * Address: 0x140449260

void  CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint_::_DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__::_1_::dtor_1(int64_t a1, int64_t a2)
{
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(*(uint64_t*)(a2 + 64) + 104i64));
}
