#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SearchSlotIndex@ORDER_INC@?$CArrayEx@VCLuaLooting_Novus_Item@@U_State@1@@US@@SAKPEAV23@AEBU_State@CLuaLooting_Novus_Item@@@Z
 * Address: 0x140405FD0

signed int64_t  US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::ORDER_INC::SearchSlotIndex(US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *p, CLuaLooting_Novus_Item::_State *state)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  unsigned int v4; // eax@5
  int64_t v6; // [sp+0h] [bp-38h]@1
  unsigned int dwIndex; // [sp+20h] [bp-18h]@4
  CLuaLooting_Novus_Item::_State *v8; // [sp+28h] [bp-10h]@6
  US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *v9; // [sp+40h] [bp+8h]@1
  CLuaLooting_Novus_Item::_State *rhs; // [sp+48h] [bp+10h]@1

  rhs = state;
  v9 = p;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  for ( dwIndex = 0; ; ++dwIndex )
  {
    v4 = US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::GetSize(v9);
    if ( dwIndex >= v4 )
      break;
    v8 = US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::GetStateAtPtr(v9, dwIndex);
    if ( CLuaLooting_Novus_Item::_State::operator==(v8, rhs) )
      return dwIndex;
  }
  return 0xFFFFFFFFi64;
}
