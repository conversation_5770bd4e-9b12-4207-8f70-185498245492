#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?SetStoreLimitItemData@CItemStoreManager@@QEAAXPEAU__list@_qry_case_all_store_limit_item@@@Z
 * Address: 0x14001339F

void  CItemStoreManager::SetStoreLimitItemData(CItemStoreManager *this, _qry_case_all_store_limit_item::__list *pData)
{
  CItemStoreManager::SetStoreLimitItemData(this, pData);
}
