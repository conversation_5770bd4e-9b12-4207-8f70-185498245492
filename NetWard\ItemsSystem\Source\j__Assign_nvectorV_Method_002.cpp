#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Assign_n@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@IEAAX_KAEBVCUnmannedTraderSchedule@@@Z
 * Address: 0x14000ADC6

void  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Assign_n(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, unsigned int64_t _Count, CUnmannedTraderSchedule *_Val)
{
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Assign_n(this, _Count, _Val);
}
