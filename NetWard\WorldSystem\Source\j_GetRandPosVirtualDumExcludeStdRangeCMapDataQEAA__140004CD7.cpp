#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?GetRandPosVirtualDumExcludeStdRange@CMapData@@QEAA_NPEAMHH0@Z
 * Address: 0x140004CD7

bool  CMapData::GetRandPosVirtualDumExcludeStdRange(CMapData *this, float *pStdPos, int nRange, int iExcludeRange, float *pNewPos)
{
  return CMapData::GetRandPosVirtualDumExcludeStdRange(this, pStdPos, nRange, iExcludeRange, pNewPos);
}
