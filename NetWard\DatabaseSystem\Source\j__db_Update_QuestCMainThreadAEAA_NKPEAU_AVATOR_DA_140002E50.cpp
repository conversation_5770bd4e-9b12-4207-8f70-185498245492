#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_db_Update_Quest@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD@Z
 * Address: 0x140002E50

bool  CMainThread::_db_Update_Quest(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pSzQuery)
{
  return CMainThread::_db_Update_Quest(this, dwSerial, pNewData, pOldData, pSzQuery);
}
