#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Buy@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAA_N_K@Z
 * Address: 0x140001947

bool  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Buy(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, unsigned int64_t _Capacity)
{
  return std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Buy(this, _Capacity);
}
