#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?tuning_unit@CMgrAvatorItemHistory@@QEAAXHEPEAU_LIST@_UNIT_DB_BASE@@PEAHKKPEAD@Z
 * Address: 0x14023CC00

void  CMgrAvatorItemHistory::tuning_unit(CMgrAvatorItemHistory *this, int n, char bySlotIndex, _UNIT_DB_BASE::_LIST *pData, int *pnPayMoney, unsigned int dwNewDalant, unsigned int dwNewGold, char *pszFileName)
{
  int64_t *v8; // rdi@1
  signed int64_t i; // rcx@1
  int v10; // edx@4
  int v11; // er8@4
  int v12; // er9@4
  int v13; // er10@4
  int v14; // er11@4
  int v15; // ebx@4
  unsigned int v16; // edi@4
  int64_t v17; // [sp+0h] [bp-A8h]@1
  int v18; // [sp+20h] [bp-88h]@4
  int v19; // [sp+28h] [bp-80h]@4
  int v20; // [sp+30h] [bp-78h]@4
  int v21; // [sp+38h] [bp-70h]@4
  int v22; // [sp+40h] [bp-68h]@4
  int v23; // [sp+48h] [bp-60h]@4
  int v24; // [sp+50h] [bp-58h]@4
  int v25; // [sp+58h] [bp-50h]@4
  unsigned int v26; // [sp+60h] [bp-48h]@4
  unsigned int v27; // [sp+68h] [bp-40h]@4
  char *v28; // [sp+70h] [bp-38h]@4
  char *v29; // [sp+78h] [bp-30h]@4
  char *v30; // [sp+80h] [bp-28h]@4
  CMgrAvatorItemHistory *v31; // [sp+B0h] [bp+8h]@1
  char v32; // [sp+C0h] [bp+18h]@1
  _UNIT_DB_BASE::_LIST *v33; // [sp+C8h] [bp+20h]@1

  v33 = pData;
  v32 = bySlotIndex;
  v31 = this;
  v8 = &v17;
  for ( i = 36i64; i; --i )
  {
    *(uint32_t*)v8 = -858993460;
    v8 = (int64_t *)((char *)v8 + 4);
  }
  v30 = pData->byPart;
  v10 = pData->byPart[5];
  v11 = pData->byPart[4];
  v12 = pData->byPart[3];
  v13 = v33->byPart[2];
  v14 = v33->byPart[1];
  v15 = v33->byPart[0];
  v16 = v33->byFrame;
  v29 = v31->m_szCurTime;
  v28 = v31->m_szCurDate;
  v27 = dwNewGold;
  v26 = dwNewDalant;
  v25 = pnPayMoney[1];
  v24 = *pnPayMoney;
  v23 = v10;
  v22 = v11;
  v21 = v12;
  v20 = v13;
  v19 = v14;
  v18 = v15;
  sprintf(
    sData,
    "UNIT TUNING: %d>fr:%d %d/%d/%d/%d/%d/%d pay(D:%u G:%u) $D:%u $G:%u [%s %s]\r\n",
    (unsigned int8_t)v32,
    v16);
  CMgrAvatorItemHistory::WriteFile(v31, pszFileName, sData);
}
