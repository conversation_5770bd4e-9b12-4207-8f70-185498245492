#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Copy_opt@PEAVCUnmannedTraderItemCodeInfo@@PEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAVCUnmannedTraderItemCodeInfo@@PEAV1@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000C72F

CUnmannedTraderItemCodeInfo * std::_Copy_opt<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *,std::random_access_iterator_tag>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, CUnmannedTraderItemCodeInfo *_Dest, std::random_access_iterator_tag __formal, std::_Nonscalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Copy_opt<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *,std::random_access_iterator_tag>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
