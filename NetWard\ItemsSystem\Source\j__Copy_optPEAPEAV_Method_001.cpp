#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Copy_opt@PEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@00Urandom_access_iterator_tag@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000A7FE

CUnmannedTraderDivisionInfo ** std::_Copy_opt<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *,std::random_access_iterator_tag>(CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last, CUnmannedTraderDivisionInfo **_Dest, std::random_access_iterator_tag __formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Copy_opt<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *,std::random_access_iterator_tag>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
