#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Move_cat@PEAVCMoveMapLimitRightInfo@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x14000DADA

std::_Undefined_move_tag  std::_Move_cat<CMoveMapLimitRightInfo *>(CMoveMapLimitRightInfo *const *__formal)
{
  return std::_Move_cat<CMoveMapLimitRightInfo *>(__formal);
}
