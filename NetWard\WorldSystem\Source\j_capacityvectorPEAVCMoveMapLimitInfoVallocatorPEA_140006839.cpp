#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?capacity@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x140006839

unsigned int64_t  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::capacity(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  return std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::capacity(this);
}
