#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Move_backward_opt@PEAVCUnmannedTraderRegistItemInfo@@PEAV1@Urandom_access_iterator_tag@std@@U_Undefined_move_tag@3@@std@@YAPEAVCUnmannedTraderRegistItemInfo@@PEAV1@00Urandom_access_iterator_tag@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14001000A

CUnmannedTraderRegistItemInfo * std::_Move_backward_opt<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::random_access_iterator_tag,std::_Undefined_move_tag>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Dest, std::random_access_iterator_tag _First_dest_cat, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Move_backward_opt<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::random_access_iterator_tag,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _First_dest_cat,
           __formal,
           a6);
}
