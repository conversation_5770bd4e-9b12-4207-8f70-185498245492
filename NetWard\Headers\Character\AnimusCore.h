// NetWard RF Online Server - Character System (Animus)
// File: AnimusCore.h - Animus character type core functionality
// Compatible with Visual Studio 2022 (C++20)

#pragma once

#include "../Common/NetWardTypes.h"
#include "../Data/RecordManager.h"
#include <memory>
#include <algorithm>

namespace NetWard::Character {

// ============================================================================
// FORWARD DECLARATIONS
// ============================================================================

class AnimusCharacter;
class CharacterRecord;
class CharacterManager;

// ============================================================================
// ANIMUS CONSTANTS
// ============================================================================

constexpr float32 DEFAULT_ANIMUS_ATTACK_GAP = 1.0f;
constexpr float32 MIN_ATTACK_GAP = 0.1f;
constexpr float32 MAX_ATTACK_GAP = 10.0f;

constexpr uint32 ANIMUS_BASE_HEALTH = 1000;
constexpr uint32 ANIMUS_BASE_MANA = 500;
constexpr uint32 ANIMUS_BASE_STAMINA = 800;

// ============================================================================
// ANIMUS CHARACTER CLASS
// ============================================================================

class AnimusCharacter {
public:
    AnimusCharacter() = default;
    explicit AnimusCharacter(CharacterID characterId);
    virtual ~AnimusCharacter() = default;

    NETWARD_DISABLE_COPY_AND_MOVE(AnimusCharacter);

    // Core character information
    CharacterID GetCharacterId() const noexcept { return m_characterId; }
    void SetCharacterId(CharacterID id) noexcept { m_characterId = id; }

    const String& GetCharacterName() const noexcept { return m_characterName; }
    void SetCharacterName(const String& name) { m_characterName = name; }

    // Animus-specific functionality
    float32 GetWeaponAdjustment() const noexcept;
    float32 GetWeaponAdjustmentSafe() const noexcept;

    // Character record management
    void SetCharacterRecord(SharedPtr<Data::CharacterRecord> record) noexcept { m_characterRecord = record; }
    SharedPtr<Data::CharacterRecord> GetCharacterRecord() const noexcept { return m_characterRecord; }

    // Validation
    bool IsValidAnimus() const noexcept;
    bool IsInitialized() const noexcept { return m_characterRecord != nullptr; }

    // Stats and attributes
    uint32 GetLevel() const noexcept;
    uint64 GetExperience() const noexcept;
    uint32 GetHealth() const noexcept;
    uint32 GetMana() const noexcept;
    uint32 GetStamina() const noexcept;

    // Combat stats
    uint32 GetAttack() const noexcept;
    uint32 GetDefense() const noexcept;
    uint32 GetAccuracy() const noexcept;
    uint32 GetDodge() const noexcept;
    uint32 GetCritical() const noexcept;

    // Equipment and durability
    uint32 GetDurability() const noexcept;
    uint32 GetMaxDurability() const noexcept;
    void SetDurability(uint32 durability) noexcept;

    // Money and inventory
    Money GetMoney() const noexcept;
    void SetMoney(Money amount) noexcept;
    bool AddMoney(Money amount) noexcept;
    bool SubtractMoney(Money amount) noexcept;

private:
    CharacterID m_characterId = 0;
    String m_characterName;
    SharedPtr<Data::CharacterRecord> m_characterRecord;

    // Cached values for performance
    mutable float32 m_cachedWeaponAdjustment = DEFAULT_ANIMUS_ATTACK_GAP;
    mutable bool m_weaponAdjustmentCached = false;
};

// ============================================================================
// ANIMUS MANAGER CLASS
// ============================================================================

class AnimusManager {
public:
    static AnimusManager& GetInstance();

    // Character creation and management
    SharedPtr<AnimusCharacter> CreateAnimus(CharacterID characterId, const String& characterName);
    SharedPtr<AnimusCharacter> GetAnimus(CharacterID characterId) const;
    bool RemoveAnimus(CharacterID characterId);

    // Bulk operations
    Vector<SharedPtr<AnimusCharacter>> GetAllAnimus() const;
    size_t GetAnimusCount() const;

    // Validation and utilities
    bool IsValidAnimusId(CharacterID characterId) const;
    void UpdateAnimusRecord(CharacterID characterId, SharedPtr<Data::CharacterRecord> record);

    // Statistics
    uint32 GetTotalAnimusCharacters() const;
    uint32 GetOnlineAnimusCharacters() const;

private:
    AnimusManager() = default;
    ~AnimusManager() = default;

    NETWARD_DISABLE_COPY_AND_MOVE(AnimusManager);

    HashMap<CharacterID, SharedPtr<AnimusCharacter>> m_animusCharacters;
    mutable std::mutex m_mutex;
};

// ============================================================================
// ANIMUS FACTORY CLASS
// ============================================================================

class AnimusFactory {
public:
    // Character creation
    static SharedPtr<AnimusCharacter> CreateAnimus(
        CharacterID characterId,
        const String& characterName,
        uint32 level = 1);

    static SharedPtr<AnimusCharacter> CreateAnimusFromRecord(
        SharedPtr<Data::CharacterRecord> record);

    // Default stat calculation
    static uint32 CalculateBaseHealth(uint32 level, uint32 constitution);
    static uint32 CalculateBaseMana(uint32 level, uint32 intelligence);
    static uint32 CalculateBaseStamina(uint32 level, uint32 dexterity);

    // Weapon adjustment calculation
    static float32 CalculateWeaponAdjustment(
        uint32 level,
        uint32 strength,
        uint32 dexterity,
        float32 baseAdjustment = DEFAULT_ANIMUS_ATTACK_GAP);

private:
    AnimusFactory() = delete;
};

// ============================================================================
// ANIMUS UTILITIES
// ============================================================================

namespace AnimusUtils {
    // Validation functions
    bool IsValidAnimusName(const String& name);
    bool IsValidAnimusLevel(uint32 level);
    bool IsValidAnimusStats(uint32 str, uint32 dex, uint32 intel, uint32 con);

    // Conversion functions
    String AnimusLevelToString(uint32 level);
    uint32 StringToAnimusLevel(const String& levelStr);

    // Combat calculations
    float32 CalculateDamageMultiplier(uint32 attackerLevel, uint32 defenderLevel);
    uint32 CalculateExperienceGain(uint32 characterLevel, uint32 enemyLevel, uint32 baseExp);

    // Equipment functions
    bool CanEquipItem(const AnimusCharacter& character, uint32 itemLevel, uint32 requiredLevel);
    float32 CalculateEquipmentBonus(uint32 itemLevel, uint32 characterLevel);
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

using AnimusPtr = SharedPtr<AnimusCharacter>;
using AnimusList = Vector<AnimusPtr>;
using AnimusMap = HashMap<CharacterID, AnimusPtr>;

} // namespace NetWard::Character
