#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ShareItemToMonster@CDarkHoleChannel@@QEAAXXZ
 * Address: 0x140268990

void  CDarkHoleChannel::ShareItemToMonster(CDarkHoleChannel *this@<rcx>, signed int64_t a2@<rax>)
{
  void *v2; // rsp@1
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  unsigned int32_t v5; // ecx@14
  int64_t v6; // [sp-20h] [bp-50F8h]@1
  _dh_mission_setup *v7; // [sp+0h] [bp-50D8h]@4
  int v8; // [sp+8h] [bp-50D0h]@6
  int64_t v9[1280]; // [sp+20h] [bp-50B8h]@15
  int j; // [sp+2824h] [bp-28B4h]@6
  char *v11; // [sp+2828h] [bp-28B0h]@9
  int k; // [sp+2830h] [bp-28A8h]@16
  char *v13; // [sp+2838h] [bp-28A0h]@19
  unsigned int v14; // [sp+2840h] [bp-2898h]@20
  CMonster *v15; // [sp+2860h] [bp-2878h]@35
  int l; // [sp+5064h] [bp-74h]@20
  CMonster *v17; // [sp+5068h] [bp-70h]@23
  int64_t v18; // [sp+5070h] [bp-68h]@28
  char v19; // [sp+5078h] [bp-60h]@28
  int m; // [sp+507Ch] [bp-5Ch]@28
  unsigned int n; // [sp+5080h] [bp-58h]@37
  unsigned int16_t v22; // [sp+5084h] [bp-54h]@39
  unsigned int16_t v23; // [sp+5088h] [bp-50h]@39
  CMonster *v24; // [sp+5090h] [bp-48h]@39
  _event_loot_item pItem; // [sp+50A4h] [bp-34h]@40
  unsigned int v26; // [sp+50B4h] [bp-24h]@40
  unsigned int v27; // [sp+50B8h] [bp-20h]@40
  unsigned int ii; // [sp+50BCh] [bp-1Ch]@40
  unsigned int jj; // [sp+50C0h] [bp-18h]@42
  CExtDummy *v30; // [sp+50C8h] [bp-10h]@14
  CDarkHoleChannel *v31; // [sp+50E0h] [bp+8h]@1

  v31 = this;
  v2 = alloca(a2);
  v3 = &v6;
  for ( i = 5180i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v7 = v31->m_MissionMgr.pCurMssionPtr;
  if ( v7 && v7->nLootItemNum > 0 )
  {
    v8 = 0;
    for ( j = 0; j < 30000; ++j )
    {
      v11 = (char *)g_Monster + 6424 * j;
      if ( v11[24] )
      {
        if ( (CMapData *)*((uint64_t*)v11 + 11) == v31->m_pQuestSetup->pUseMap
          && *((uint16_t*)v11 + 52) == v31->m_wLayerIndex )
        {
          if ( !v7->pAreaDummy
            || (v5 = v7->pAreaDummy->m_wLineIndex,
                v30 = &v31->m_pQuestSetup->pUseMap->m_Dummy,
                CExtDummy::IsInBBox(v30, v5, (float *const )v11 + 10)) )
          {
            v9[v8++] = (int64_t)v11;
          }
        }
      }
    }
    for ( k = 0; ; ++k )
    {
      if ( k >= v7->nLootItemNum )
        return;
      v13 = &v7->pLootItem[k]->byItemTableCode;
      if ( !*((uint32_t*)v13 + 4) )
        continue;
      v14 = 0;
      for ( l = 0; l < v8; ++l )
      {
        v17 = (CMonster *)v9[l];
        if ( *((uint32_t*)v13 + 6) == 2 )
        {
          if ( !*((uint64_t*)v13 + 4) || v17->m_pMonRec == (_monster_fld *)*((uint64_t*)v13 + 4) )
            goto LABEL_35;
        }
        else if ( *((uint32_t*)v13 + 6) == 4 )
        {
          v18 = *((uint64_t*)v13 + 4);
          v19 = 0;
          for ( m = 0; m < *(uint32_t*)(v18 + 8); ++m )
          {
            if ( *(_monster_fld **)(v18 + 8i64 * m + 16) == v17->m_pMonRec )
            {
              v19 = 1;
              break;
            }
          }
LABEL_35:
          *(&v15 + (signed int)v14++) = v17;
          continue;
        }
      }
      if ( v14 )
      {
        for ( n = 0; (signed int)n < (signed int)v14; ++n )
        {
          v22 = rand() % (signed int)v14;
          v23 = rand() % (signed int)v14;
          v24 = *(&v15 + v23);
          *(&v15 + v23) = *(&v15 + v22);
          *(&v15 + v22) = v24;
        }
        pItem.byItemTable = *v13;
        pItem.wItemIndex = **((uint16_t**)v13 + 1);
        v26 = *((uint32_t*)v13 + 4) / v14;
        v27 = *((uint32_t*)v13 + 4) % v14;
        for ( ii = 0; (signed int)ii < (signed int)v26; ++ii )
        {
          for ( jj = 0; (signed int)jj < (signed int)v14; ++jj )
          {
            CMonster::AddEventItem(*(&v15 + (signed int)jj), &pItem);
            __trace(
              "eventitem: mon: %s, item: %s",
              (*(&v15 + (signed int)jj))->m_pMonRec->m_strCode,
              *((uint64_t*)v13 + 1) + 4i64);
          }
        }
        for ( ii = 0; (signed int)ii < (signed int)v27; ++ii )
          CMonster::AddEventItem(*(&v15 + (signed int)ii), &pItem);
      }
    }
  }
}
