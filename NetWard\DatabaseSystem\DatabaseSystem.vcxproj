<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>

  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{D4E5F6G7-H8I9-0123-DEF0-************}</ProjectGuid>
    <RootNamespace>DatabaseSystem</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemGroup>
    <ClCompile Include="Source\0allocatorPEAVCLogTypeDBTaskstdQEAAAEBV01Z_1402C67E0.cpp" />
    <ClCompile Include="Source\0allocatorPEAVCLogTypeDBTaskstdQEAAXZ_1402C5C00.cpp" />
    <ClCompile Include="Source\0CashDbWorkerQEAAXZ_14022B750.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerBRQEAAXZ_14022F290.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerCNQEAAXZ_140230BB0.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerESQEAAXZ_140231B00.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerGBQEAAXZ_14022C340.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerIDQEAAXZ_14022CCA0.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerJPQEAAXZ_14022D630.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerKRQEAAXZ_14022B6F0.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerNULLQEAAXZ_1402F3020.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerPHQEAAXZ_14022E130.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerRUQEAAXZ_14022EA70.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerTHQEAAXZ_140232490.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerTWQEAAXZ_140230440.cpp" />
    <ClCompile Include="Source\0CCashDbWorkerUSQEAAXZ_140231600.cpp" />
    <ClCompile Include="Source\0CCashDBWorkManagerAEAAXZ_1402F31B0.cpp" />
    <ClCompile Include="Source\0CDummyPosTableQEAAXZ_14018A0D0.cpp" />
    <ClCompile Include="Source\0CellLendItemSheetQEAAEPEAU_db_con_STORAGE_LISTZ_14030F040.cpp" />
    <ClCompile Include="Source\0CEnglandBillingMgrQEAAXZ_1403196B0.cpp" />
    <ClCompile Include="Source\0CEventLootTableQEAAXZ_140202640.cpp" />
    <ClCompile Include="Source\0CItemLootTableQEAAXZ_1402024D0.cpp" />
    <ClCompile Include="Source\0CItemUpgradeTableQEAAXZ_140202BC0.cpp" />
    <ClCompile Include="Source\0CLogTypeDBTaskManagerIEAAXZ_1402C2A90.cpp" />
    <ClCompile Include="Source\0CLogTypeDBTaskPoolQEAAXZ_1402C1EA0.cpp" />
    <ClCompile Include="Source\0CLogTypeDBTaskQEAAXZ_1402C1CA0.cpp" />
    <ClCompile Include="Source\0CMapDataTableQEAAXZ_140198740.cpp" />
    <ClCompile Include="Source\0CMonsterSPGroupTableQEAAXZ_140202470.cpp" />
    <ClCompile Include="Source\0CNationCodeStrTableQEAAXZ_140204AD0.cpp" />
    <ClCompile Include="Source\0COreCuttingTableQEAAXZ_140202A10.cpp" />
    <ClCompile Include="Source\0CRecordDataQEAAXZ_14007F490.cpp" />
    <ClCompile Include="Source\0CRFDBItemLogQEAAKZ_140485420.cpp" />
    <ClCompile Include="Source\0CRFNewDatabaseQEAAXZ_140485F80.cpp" />
    <ClCompile Include="Source\0CRFWorldDatabaseQEAAXZ_140489680.cpp" />
    <ClCompile Include="Source\0CTotalGuildRankRecordQEAAXZ_1402C85D0.cpp" />
    <ClCompile Include="Source\0CTSingletonVCCashDBWorkManagerIEAAXZ_1402F3740.cpp" />
    <ClCompile Include="Source\0CUnmannedTraderClassInfoTableCodeTypeQEAAKZ_140377040.cpp" />
    <ClCompile Include="Source\0CUnmannedTraderClassInfoTableTypeQEAAKZ_14037CC10.cpp" />
    <ClCompile Include="Source\0CUnmannedTraderGroupItemInfoTableIEAAXZ_14036AFC0.cpp" />
    <ClCompile Include="Source\0CUnmannedTraderUserInfoTableIEAAXZ_140366B70.cpp" />
    <ClCompile Include="Source\0CUserDBQEAAXZ_14010FB90.cpp" />
    <ClCompile Include="Source\0CWeeklyGuildRankRecordQEAAXZ_1402CF910.cpp" />
    <ClCompile Include="Source\0DL_FixedBasePrecomputationImplUEC2NPointCryptoPPC_14055DCC0.cpp" />
    <ClCompile Include="Source\0DL_FixedBasePrecomputationImplUECPPointCryptoPPCr_14044BE60.cpp" />
    <ClCompile Include="Source\0DL_FixedBasePrecomputationImplVIntegerCryptoPPCry_14055F2A0.cpp" />
    <ClCompile Include="Source\0DL_FixedBasePrecomputationImplVIntegerCryptoPPCry_14055F550.cpp" />
    <ClCompile Include="Source\0DL_FixedBasePrecomputationUEC2NPointCryptoPPCrypt_14055E430.cpp" />
    <ClCompile Include="Source\0DL_FixedBasePrecomputationUECPPointCryptoPPCrypto_14044C4E0.cpp" />
    <ClCompile Include="Source\0DL_FixedBasePrecomputationVIntegerCryptoPPCryptoP_14055FAE0.cpp" />
    <ClCompile Include="Source\0DL_FixedBasePrecomputationVIntegerCryptoPPCryptoP_14055FBF0.cpp" />
    <ClCompile Include="Source\0DL_GroupParametersImplVEcPrecomputationVEC2NCrypt_14055CC00.cpp" />
    <ClCompile Include="Source\0DL_GroupParametersImplVEcPrecomputationVECPCrypto_140454780.cpp" />
    <ClCompile Include="Source\0GeneratableCryptoMaterialCryptoPPQEAAAEBV01Z_140560410.cpp" />
    <ClCompile Include="Source\0GeneratableCryptoMaterialCryptoPPQEAAXZ_14044C9F0.cpp" />
    <ClCompile Include="Source\0SingletonVvectorGVallocatorGstdstdUNewPrimeTableC_14064CC50.cpp" />
    <ClCompile Include="Source\0tablelua_tinkerQEAAAEBU01Z_140446720.cpp" />
    <ClCompile Include="Source\0tablelua_tinkerQEAAPEAUlua_StateHZ_140446630.cpp" />
    <ClCompile Include="Source\0tablelua_tinkerQEAAPEAUlua_StatePEBDZ_1404464C0.cpp" />
    <ClCompile Include="Source\0tablelua_tinkerQEAAPEAUlua_StateZ_1404463D0.cpp" />
    <ClCompile Include="Source\0table_objlua_tinkerQEAAPEAUlua_StateHZ_140446140.cpp" />
    <ClCompile Include="Source\0UseCellTimeLimitJadeQEAAPEAU_db_con_STORAGE_LISTG_1403FB370.cpp" />
    <ClCompile Include="Source\0UseCellTimeLimitJadeQEAAPEAU_db_con_STORAGE_LISTZ_1403FB3D0.cpp" />
    <ClCompile Include="Source\0vectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTypeDBT_1402C47C0.cpp" />
    <ClCompile Include="Source\0WaitableCryptoPPQEAAAEBV01Z_14054B570.cpp" />
    <ClCompile Include="Source\0WaitableCryptoPPQEAAXZ_140453D70.cpp" />
    <ClCompile Include="Source\0WaitCellTimeLimitJadeQEAAPEAU_db_con_STORAGE_LIST_1403FB2E0.cpp" />
    <ClCompile Include="Source\0WaitCellTimeLimitJadeQEAAPEAU_db_con_STORAGE_LIST_1403FB340.cpp" />
    <ClCompile Include="Source\0_100_per_random_tableQEAAXZ_1400726C0.cpp" />
    <ClCompile Include="Source\0_ANIMUS_DB_BASEQEAAXZ_1400761E0.cpp" />
    <ClCompile Include="Source\0_animus_db_loadQEAAXZ_14010DD50.cpp" />
    <ClCompile Include="Source\0_AVATOR_DB_BASEQEAAXZ_1400753C0.cpp" />
    <ClCompile Include="Source\0_bag_db_loadQEAAXZ_14010DA90.cpp" />
    <ClCompile Include="Source\0_BUDDY_DB_BASEQEAAXZ_140076BD0.cpp" />
    <ClCompile Include="Source\0_CRYMSG_DB_BASEQEAAXZ_140077760.cpp" />
    <ClCompile Include="Source\0_CUTTING_DB_BASEQEAAXZ_140076650.cpp" />
    <ClCompile Include="Source\0_db_con_STORAGE_LISTQEAAXZ_1400981F0.cpp" />
    <ClCompile Include="Source\0_db_golden_box_itemQEAAXZ_140416A60.cpp" />
    <ClCompile Include="Source\0_DB_LOAD_AUTOMINE_MACHINEQEAAXZ_1402D4100.cpp" />
    <ClCompile Include="Source\0_DB_QRY_SYN_DATAQEAAXZ_1402024A0.cpp" />
    <ClCompile Include="Source\0_DELPOST_DB_BASEQEAAXZ_1400775D0.cpp" />
    <ClCompile Include="Source\0_eff_list_worlddb_sf_delay_infoQEAAXZ_140077EF0.cpp" />
    <ClCompile Include="Source\0_embellish_db_loadQEAAXZ_14010DBF0.cpp" />
    <ClCompile Include="Source\0_EMBELLISH_LIST_EQUIP_DB_BASEQEAAXZ_140075A80.cpp" />
    <ClCompile Include="Source\0_EQUIP_DB_BASEQEAAXZ_140075A10.cpp" />
    <ClCompile Include="Source\0_equip_db_loadQEAAXZ_14010DB40.cpp" />
    <ClCompile Include="Source\0_event_dropCEventLootTableQEAAXZ_140203E10.cpp" />
    <ClCompile Include="Source\0_Exttrunk_db_loadQEAAXZ_14010DF60.cpp" />
    <ClCompile Include="Source\0_FORCE_DB_BASEQEAAXZ_140075FF0.cpp" />
    <ClCompile Include="Source\0_force_db_loadQEAAXZ_14010DCA0.cpp" />
    <ClCompile Include="Source\0_goldbox_indexQEAAXZ_140416C60.cpp" />
    <ClCompile Include="Source\0_INVEN_DB_BASEQEAAXZ_140075C10.cpp" />
    <ClCompile Include="Source\0_ITEMCOMBINE_DB_BASEQEAAXZ_140077180.cpp" />
    <ClCompile Include="Source\0_limit_item_db_dataQEAAXZ_14034BDD0.cpp" />
    <ClCompile Include="Source\0_LINK_DB_BASEQEAAXZ_140075870.cpp" />
    <ClCompile Include="Source\0_LIST_ANIMUS_DB_BASEQEAAXZ_140076250.cpp" />
    <ClCompile Include="Source\0_LIST_BUDDY_DB_BASEQEAAXZ_140076C40.cpp" />
    <ClCompile Include="Source\0_LIST_CRYMSG_DB_BASEQEAAXZ_1400777D0.cpp" />
    <ClCompile Include="Source\0_LIST_CUTTING_DB_BASEQEAAXZ_1400766D0.cpp" />
    <ClCompile Include="Source\0_LIST_FORCE_DB_BASEQEAAXZ_140076060.cpp" />
    <ClCompile Include="Source\0_LIST_INVEN_DB_BASEQEAAXZ_140075C80.cpp" />
    <ClCompile Include="Source\0_LIST_ITEMCOMBINE_DB_BASEQEAAXZ_140077200.cpp" />
    <ClCompile Include="Source\0_LIST_LINK_DB_BASEQEAAXZ_1400758E0.cpp" />
    <ClCompile Include="Source\0_LIST_PERSONALAMINE_INVEN_DB_BASEQEAAXZ_140075EC0.cpp" />
    <ClCompile Include="Source\0_LIST_QUEST_DB_BASEQEAAXZ_1400768A0.cpp" />
    <ClCompile Include="Source\0_LIST_TRADE_DB_BASEQEAAXZ_140076DB0.cpp" />
    <ClCompile Include="Source\0_LIST_TRUNK_DB_BASEQEAAXZ_140076F90.cpp" />
    <ClCompile Include="Source\0_LIST_UNIT_DB_BASEQEAAXZ_140076460.cpp" />
    <ClCompile Include="Source\0_mas_list_worlddb_sf_delay_infoQEAAXZ_140077F30.cpp" />
    <ClCompile Include="Source\0_NOT_ARRANGED_AVATOR_DBQEAAXZ_14011F020.cpp" />
    <ClCompile Include="Source\0_NPC_QUEST_HISTORY_QUEST_DB_BASEQEAAXZ_140076980.cpp" />
    <ClCompile Include="Source\0_ore_cut_listCOreCuttingTableQEAAXZ_140204550.cpp" />
    <ClCompile Include="Source\0_PERSONALAMINE_INVEN_DB_BASEQEAAXZ_140075E40.cpp" />
    <ClCompile Include="Source\0_personal_amine_inven_db_loadQEAAXZ_14010DEB0.cpp" />
    <ClCompile Include="Source\0_POSTDATA_DB_BASEQEAAXZ_140077390.cpp" />
    <ClCompile Include="Source\0_POSTSTORAGE_DB_BASEQEAAXZ_140077420.cpp" />
    <ClCompile Include="Source\0_POTION_NEXT_USE_TIME_DB_BASEQEAAXZ_140077960.cpp" />
    <ClCompile Include="Source\0_pt_query_appoint_zoclQEAAXZ_1402B9C60.cpp" />
    <ClCompile Include="Source\0_PVPPOINT_LIMIT_DB_BASEQEAAXZ_1400776F0.cpp" />
    <ClCompile Include="Source\0_QUEST_DB_BASEQEAAXZ_140076800.cpp" />
    <ClCompile Include="Source\0_RanitPEAVCLogTypeDBTask_JPEBQEAV1AEBQEAV1stdQEAA_1402C5FF0.cpp" />
    <ClCompile Include="Source\0_RanitPEAVCLogTypeDBTask_JPEBQEAV1AEBQEAV1stdQEAA_1402C6AD0.cpp" />
    <ClCompile Include="Source\0_record_bin_headerQEAAXZ_140044720.cpp" />
    <ClCompile Include="Source\0_REGED_AVATOR_DBQEAAXZ_140075480.cpp" />
    <ClCompile Include="Source\0_RETURNPOST_DB_BASEQEAAXZ_140077520.cpp" />
    <ClCompile Include="Source\0_SFCONT_DB_BASEQEAAXZ_140076AB0.cpp" />
    <ClCompile Include="Source\0_START_NPC_QUEST_HISTORY_QUEST_DB_BASEQEAAXZ_1400CFCA0.cpp" />
    <ClCompile Include="Source\0_STAT_DB_BASEQEAAXZ_1400745F0.cpp" />
    <ClCompile Include="Source\0_SUPPLEMENT_DB_BASEQEAAXZ_1400778C0.cpp" />
    <ClCompile Include="Source\0_TRADE_DB_BASEQEAAXZ_140076D40.cpp" />
    <ClCompile Include="Source\0_TRUNK_DB_BASEQEAAXZ_140076EF0.cpp" />
    <ClCompile Include="Source\0_trunk_db_loadQEAAXZ_14010DE00.cpp" />
    <ClCompile Include="Source\0_UNIT_DB_BASEQEAAXZ_1400763F0.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorPEAVCLogTypeDBTaskVallocato_1402C5F80.cpp" />
    <ClCompile Include="Source\0_Vector_const_iteratorPEAVCLogTypeDBTaskVallocato_1402C6A70.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEAVC_1402C5CB0.cpp" />
    <ClCompile Include="Source\0_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEAVC_1402C5F20.cpp" />
    <ClCompile Include="Source\0_Vector_valPEAVCLogTypeDBTaskVallocatorPEAVCLogTy_1402C5B90.cpp" />
    <ClCompile Include="Source\0_worlddb_arrange_char_infoQEAAXZ_1401BF040.cpp" />
    <ClCompile Include="Source\0_worlddb_npc_quest_complete_historyQEAAXZ_1401BF130.cpp" />
    <ClCompile Include="Source\0_worlddb_potion_delay_infoQEAAXZ_1401BF480.cpp" />
    <ClCompile Include="Source\0_worlddb_sf_delay_infoQEAAXZ_140077E60.cpp" />
    <ClCompile Include="Source\0__list_worlddb_npc_quest_complete_historyQEAAXZ_1401BF190.cpp" />
    <ClCompile Include="Source\0__list_worlddb_start_npc_quest_complete_historyQE_1401BF200.cpp" />
    <ClCompile Include="Source\1CashDbWorkerUEAAXZ_14022B8D0.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerBRUEAAXZ_14022F360.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerCNUEAAXZ_140230C80.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerESUEAAXZ_140231BD0.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerGBUEAAXZ_14022C410.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerIDUEAAXZ_14022CD70.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerJPUEAAXZ_14022D700.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerKRUEAAXZ_14022BA10.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerNULLUEAAXZ_1402F3170.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerPHUEAAXZ_14022E200.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerRUUEAAXZ_14022EB40.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerTHUEAAXZ_140232560.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerTWUEAAXZ_140230510.cpp" />
    <ClCompile Include="Source\1CCashDbWorkerUSUEAAXZ_1402316D0.cpp" />
    <ClCompile Include="Source\1CCashDBWorkManagerEEAAXZ_1402F35D0.cpp" />
    <ClCompile Include="Source\1CDummyPosTableUEAAXZ_14018A110.cpp" />
    <ClCompile Include="Source\1CEnglandBillingMgrQEAAXZ_1403196F0.cpp" />
    <ClCompile Include="Source\1CEventLootTableUEAAXZ_1402026F0.cpp" />
    <ClCompile Include="Source\1CItemLootTableUEAAXZ_1402027A0.cpp" />
    <ClCompile Include="Source\1CItemUpgradeTableUEAAXZ_140202C50.cpp" />
    <ClCompile Include="Source\1CLogTypeDBTaskManagerIEAAXZ_1402C2B10.cpp" />
    <ClCompile Include="Source\1CLogTypeDBTaskPoolQEAAXZ_1402C1FC0.cpp" />
    <ClCompile Include="Source\1CLogTypeDBTaskQEAAXZ_1402C1D00.cpp" />
    <ClCompile Include="Source\1CMapDataTableUEAAXZ_140198790.cpp" />
    <ClCompile Include="Source\1CMonsterSPGroupTableQEAAXZ_14015EA60.cpp" />
    <ClCompile Include="Source\1CNationCodeStrTableQEAAXZ_140204B20.cpp" />
    <ClCompile Include="Source\1COreCuttingTableUEAAXZ_140202AA0.cpp" />
    <ClCompile Include="Source\1CRecordDataUEAAXZ_14007F550.cpp" />
    <ClCompile Include="Source\1CRFDBItemLogUEAAXZ_1404854B0.cpp" />
    <ClCompile Include="Source\1CRFNewDatabaseUEAAXZ_140486150.cpp" />
    <ClCompile Include="Source\1CRFWorldDatabaseUEAAXZ_1404896D0.cpp" />
    <ClCompile Include="Source\1CTotalGuildRankRecordQEAAXZ_1402C8610.cpp" />
    <ClCompile Include="Source\1CTSingletonVCCashDBWorkManagerMEAAXZ_1402F3770.cpp" />
    <ClCompile Include="Source\1CUnmannedTraderClassInfoTableCodeTypeQEAAXZ_140377100.cpp" />
    <ClCompile Include="Source\1CUnmannedTraderClassInfoTableTypeQEAAXZ_14037CCE0.cpp" />
    <ClCompile Include="Source\1CUnmannedTraderGroupItemInfoTableIEAAXZ_14036B060.cpp" />
    <ClCompile Include="Source\1CUnmannedTraderUserInfoTableIEAAXZ_140366C50.cpp" />
    <ClCompile Include="Source\1CUserDBUEAAXZ_14010FED0.cpp" />
    <ClCompile Include="Source\1CWeeklyGuildRankRecordQEAAXZ_1402CF900.cpp" />
    <ClCompile Include="Source\1DL_FixedBasePrecomputationImplUEC2NPointCryptoPPC_14055DD70.cpp" />
    <ClCompile Include="Source\1DL_FixedBasePrecomputationImplUECPPointCryptoPPCr_1404491C0.cpp" />
    <ClCompile Include="Source\1DL_FixedBasePrecomputationImplVIntegerCryptoPPCry_14055F350.cpp" />
    <ClCompile Include="Source\1DL_GroupParametersImplVEcPrecomputationVEC2NCrypt_14055CEA0.cpp" />
    <ClCompile Include="Source\1DL_GroupParametersImplVEcPrecomputationVECPCrypto_140449CD0.cpp" />
    <ClCompile Include="Source\1GeneratableCryptoMaterialCryptoPPUEAAXZ_140449F80.cpp" />
    <ClCompile Include="Source\1tablelua_tinkerQEAAXZ_140446780.cpp" />
    <ClCompile Include="Source\1table_objlua_tinkerQEAAXZ_1404461D0.cpp" />
    <ClCompile Include="Source\1vectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTypeDBT_1402C4840.cpp" />
    <ClCompile Include="Source\1WaitableCryptoPPUEAAXZ_14044D9A0.cpp" />
    <ClCompile Include="Source\1_PVPPOINT_LIMIT_DB_BASEQEAAXZ_140077750.cpp" />
    <ClCompile Include="Source\1_RanitPEAVCLogTypeDBTask_JPEBQEAV1AEBQEAV1stdQEAA_1402C41A0.cpp" />
    <ClCompile Include="Source\1_Vector_const_iteratorPEAVCLogTypeDBTaskVallocato_1402C4160.cpp" />
    <ClCompile Include="Source\1_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEAVC_1402C40B0.cpp" />
    <ClCompile Include="Source\4CUnmannedTraderClassInfoTableCodeTypeQEAAAEBV0AEB_1403775E0.cpp" />
    <ClCompile Include="Source\4CUnmannedTraderClassInfoTableTypeQEAAAEBVCUnmanne_14037D710.cpp" />
    <ClCompile Include="Source\4DL_FixedBasePrecomputationImplUEC2NPointCryptoPPC_14055E4E0.cpp" />
    <ClCompile Include="Source\4DL_FixedBasePrecomputationImplUECPPointCryptoPPCr_14045BC80.cpp" />
    <ClCompile Include="Source\4DL_FixedBasePrecomputationImplVIntegerCryptoPPCry_14055E6C0.cpp" />
    <ClCompile Include="Source\4DL_FixedBasePrecomputationUEC2NPointCryptoPPCrypt_14055EC50.cpp" />
    <ClCompile Include="Source\4DL_FixedBasePrecomputationUECPPointCryptoPPCrypto_14045C0E0.cpp" />
    <ClCompile Include="Source\4DL_FixedBasePrecomputationVIntegerCryptoPPCryptoP_14055EE50.cpp" />
    <ClCompile Include="Source\4DL_GroupParametersImplVEcPrecomputationVEC2NCrypt_14055DE00.cpp" />
    <ClCompile Include="Source\4DL_GroupParametersImplVEcPrecomputationVECPCrypto_14045C350.cpp" />
    <ClCompile Include="Source\4GeneratableCryptoMaterialCryptoPPQEAAAEAV01AEBV01_14045C630.cpp" />
    <ClCompile Include="Source\4_INVEN_DB_BASEQEAAAEAU0AEAU0Z_1401BF600.cpp" />
    <ClCompile Include="Source\4_LIST_INVEN_DB_BASEQEAAAEAU01AEAU01Z_1401BF6A0.cpp" />
    <ClCompile Include="Source\8_Vector_const_iteratorPEAVCLogTypeDBTaskVallocato_1402C5EB0.cpp" />
    <ClCompile Include="Source\9_Vector_const_iteratorPEAVCLogTypeDBTaskVallocato_1402C5190.cpp" />
    <ClCompile Include="Source\AccessBasePrecomputationDL_GroupParametersImplVEcP_1404549A0.cpp" />
    <ClCompile Include="Source\AccessBasePrecomputationDL_GroupParametersImplVEcP_14055CD70.cpp" />
    <ClCompile Include="Source\AccessBasePrecomputationDL_GroupParameters_ECVEC2N_140557710.cpp" />
    <ClCompile Include="Source\AccessBasePrecomputationDL_GroupParameters_ECVECPC_14044F7E0.cpp" />
    <ClCompile Include="Source\AccessPublicPrecomputationDL_PublicKeyImplVDL_Grou_140451130.cpp" />
    <ClCompile Include="Source\AccessPublicPrecomputationDL_PublicKeyImplVDL_Grou_140558A50.cpp" />
    <ClCompile Include="Source\AccessPublicPrecomputationDL_PublicKeyImplVDL_Grou_1405695C0.cpp" />
    <ClCompile Include="Source\AccessPublicPrecomputationDL_PublicKeyImplVDL_Grou_140637080.cpp" />
    <ClCompile Include="Source\AccessPublicPrecomputationDL_PublicKeyImplVDL_Grou_140637A30.cpp" />
    <ClCompile Include="Source\AddBagRequestCNetworkEXAEAA_NHPEADZ_1401CAE10.cpp" />
    <ClCompile Include="Source\AddRecordCEventLootTableQEAAXPEAU_event_drop1Z_140203E70.cpp" />
    <ClCompile Include="Source\add_char_completeCMgrAccountLobbyHistoryQEAAXEPEAU_1402340D0.cpp" />
    <ClCompile Include="Source\Add_PvpPointCRFWorldDatabaseQEAA_NKKKZ_1404987E0.cpp" />
    <ClCompile Include="Source\add_storage_failCMgrAvatorItemHistoryQEAAXHPEAU_db_140240B40.cpp" />
    <ClCompile Include="Source\AgreeWithEphemeralPrivateKeyDL_KeyAgreementAlgorit_140461090.cpp" />
    <ClCompile Include="Source\AgreeWithEphemeralPrivateKeyDL_KeyAgreementAlgorit_14058EE30.cpp" />
    <ClCompile Include="Source\AliveCBillingIDUEAAXPEAVCUserDBZ_14028E1E0.cpp" />
    <ClCompile Include="Source\AliveCBillingJPUEAAXPEAVCUserDBZ_14028EA00.cpp" />
    <ClCompile Include="Source\AliveCBillingManagerQEAAXPEAVCUserDBZ_14007C1F0.cpp" />
    <ClCompile Include="Source\AliveCBillingNULLUEAAXPEAVCUserDBZ_14028DBE0.cpp" />
    <ClCompile Include="Source\AliveCBillingUEAAXPEAVCUserDBZ_14028CBA0.cpp" />
    <ClCompile Include="Source\Alive_Char_CompleteCMainThreadQEAAXPEAU_DB_QRY_SYN_1401F3FF0.cpp" />
    <ClCompile Include="Source\Alive_Char_CompleteCUserDBQEAAXEEKPEAU_REGEDZ_14011AFC0.cpp" />
    <ClCompile Include="Source\Alive_Char_RequestCUserDBQEAA_NEKPEADEZ_14011ABF0.cpp" />
    <ClCompile Include="Source\allocateallocatorPEAVCLogTypeDBTaskstdQEAAPEAPEAVC_1402C5C60.cpp" />
    <ClCompile Include="Source\AllocSelectHandleCRFNewDatabaseQEAA_NXZ_1404875A0.cpp" />
    <ClCompile Include="Source\AllocUpdateHandleCRFNewDatabaseQEAA_NXZ_140487620.cpp" />
    <ClCompile Include="Source\ApplyUpdatedBossInfoCPvpUserAndGuildRankingSystemQ_1402B99C0.cpp" />
    <ClCompile Include="Source\ATL_dynamic_initializer_for__atlTraceDBClient___1406E65E0.cpp" />
    <ClCompile Include="Source\ATL_dynamic_initializer_for__atlTraceDBProvider___1406E6600.cpp" />
    <ClCompile Include="Source\auto_trade_buyCMgrAvatorItemHistoryQEAAXPEBDK0KPEA_14023A040.cpp" />
    <ClCompile Include="Source\auto_trade_sellCMgrAvatorItemHistoryQEAAXPEBDK0KPE_14023A200.cpp" />
    <ClCompile Include="Source\AvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTypeDBT_1402C4D50.cpp" />
    <ClCompile Include="Source\backvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogType_1402C4D80.cpp" />
    <ClCompile Include="Source\back_trap_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_c_1402405C0.cpp" />
    <ClCompile Include="Source\beginvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTyp_1402C4B80.cpp" />
    <ClCompile Include="Source\BuyCUnmannedTraderUserInfoTableQEAAXGEPEAU_unmanne_1401D4980.cpp" />
    <ClCompile Include="Source\buy_unitCMgrAvatorItemHistoryQEAAXHEPEAU_LIST_UNIT_14023C900.cpp" />
    <ClCompile Include="Source\CalcNewSerialNumber_db_con_STORAGE_LISTCAKXZ_14010E1F0.cpp" />
    <ClCompile Include="Source\CalcRadarDelayCUserDBQEAAXXZ_14011B730.cpp" />
    <ClCompile Include="Source\CallFunc_Item_BuyCEnglandBillingMgrQEAAHAEAU_param_140319C80.cpp" />
    <ClCompile Include="Source\CancelRegistCUnmannedTraderUserInfoTableQEAAXGEPEA_1401D4870.cpp" />
    <ClCompile Include="Source\capacityvectorPEAVCLogTypeDBTaskVallocatorPEAVCLog_1402C5200.cpp" />
    <ClCompile Include="Source\CashDBInitCMainThreadAEAA_NPEAD000KZ_1401ED110.cpp" />
    <ClCompile Include="Source\cash_item_useCMgrAvatorItemHistoryQEAAXHPEAU_db_co_14023BCE0.cpp" />
    <ClCompile Include="Source\CheatCancelRegistCUnmannedTraderUserInfoTableQEAA__140363A00.cpp" />
    <ClCompile Include="Source\cheat_add_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_c_14023C0C0.cpp" />
    <ClCompile Include="Source\cheat_del_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_c_14023C2A0.cpp" />
    <ClCompile Include="Source\cheat_make_item_no_materialCMgrAvatorItemHistoryQE_14023B910.cpp" />
    <ClCompile Include="Source\CheckAndCreateTodayGuildRankTableCGuildRankingAEAA_140339FC0.cpp" />
    <ClCompile Include="Source\CheckAndCreateTodayPvpRankTableCUserRankingProcess_1403426F0.cpp" />
    <ClCompile Include="Source\CheckDBCSCompleteStringCNationSettingDataIEAA_NHPE_140211E00.cpp" />
    <ClCompile Include="Source\CheckDBItemStateCUnmannedTraderControllerIEAAEEKAE_1403506C0.cpp" />
    <ClCompile Include="Source\CheckDiffCCheckSumCharacAccountTrunkDataQEAAHPEAVC_1402C08E0.cpp" />
    <ClCompile Include="Source\CheckDiffCCheckSumGuildDataQEAAHPEAVCRFWorldDataba_1402C0F70.cpp" />
    <ClCompile Include="Source\CheckHeroesDummyCDummyPosTableSA_NPEAVCGameObjectE_14018A010.cpp" />
    <ClCompile Include="Source\CheckLogFileHourCRFNewDatabaseQEAAXXZ_140487A50.cpp" />
    <ClCompile Include="Source\CheckMixItemCTalkCrystalCombineManagerIEAAEPEAU_db_140431140.cpp" />
    <ClCompile Include="Source\CheckNPCQuestStartableCQuestMgrQEAAPEAU_happen_eve_1402881F0.cpp" />
    <ClCompile Include="Source\CheckwIndexAndTypeCUnmannedTraderUserInfoTableAEAA_1401D4660.cpp" />
    <ClCompile Include="Source\check_dbsyn_data_sizeCMainThreadAEAA_NXZ_1401F9460.cpp" />
    <ClCompile Include="Source\Check_GuildMemberCountCRFWorldDatabaseQEAA_NKZ_14049D990.cpp" />
    <ClCompile Include="Source\check_machineAutominePersonalMgrQEAA_NHKPEAVAutomi_1402DEC40.cpp" />
    <ClCompile Include="Source\CleanUpCLogTypeDBTaskManagerAEAAXXZ_1402C3550.cpp" />
    <ClCompile Include="Source\CleanUpCUnmannedTraderClassInfoTableTypeIEAAXXZ_14037D900.cpp" />
    <ClCompile Include="Source\ClearBillingDataCUserDBQEAAXXZ_140118090.cpp" />
    <ClCompile Include="Source\ClearCLogTypeDBTaskQEAAXXZ_1402C3FC0.cpp" />
    <ClCompile Include="Source\ClearCTotalGuildRankRecordQEAAXXZ_1402C8650.cpp" />
    <ClCompile Include="Source\ClearCWeeklyGuildRankRecordQEAAXXZ_1402CA510.cpp" />
    <ClCompile Include="Source\ClearDB_CombineResultItemCombineMgrQEAAEXZ_1402AD5F0.cpp" />
    <ClCompile Include="Source\ClearLogLogOutStateCUnmannedTraderUserInfoTableAEA_140365CC0.cpp" />
    <ClCompile Include="Source\ClearRequestCUnmannedTraderUserInfoTableQEAAXGKZ_140364970.cpp" />
    <ClCompile Include="Source\clearvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTyp_1402C4FE0.cpp" />
    <ClCompile Include="Source\Clear_guild_query_info_result_zoclQEAAXXZ_14025D190.cpp" />
    <ClCompile Include="Source\Clear_LIST_TRADE_DB_BASEQEAAXXZ_140076E00.cpp" />
    <ClCompile Include="Source\Clear_TRADE_DB_BASEQEAAXXZ_140076E70.cpp" />
    <ClCompile Include="Source\combine_ex_reward_itemCMgrAvatorItemHistoryQEAAXHE_14023D700.cpp" />
    <ClCompile Include="Source\combine_ex_using_materialCMgrAvatorItemHistoryQEAA_14023D440.cpp" />
    <ClCompile Include="Source\combine_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_con_14023BAB0.cpp" />
    <ClCompile Include="Source\CommitTransactionCRFNewDatabaseQEAA_NXZ_1404874C0.cpp" />
    <ClCompile Include="Source\CompleteBuyCUnmannedTraderUserInfoTableQEAAXEPEADP_140363F80.cpp" />
    <ClCompile Include="Source\CompleteCancelRegistCUnmannedTraderUserInfoTableQE_140363E40.cpp" />
    <ClCompile Include="Source\CompleteCreateCUnmannedTraderUserInfoTableQEAAXGZ_140363B30.cpp" />
    <ClCompile Include="Source\CompleteInsertPatriarchPatriarchElectProcessorQEAA_1402BBC40.cpp" />
    <ClCompile Include="Source\CompleteItemChargeRefundPatriarchElectProcessorQEA_1402BBBA0.cpp" />
    <ClCompile Include="Source\CompleteRegistCUnmannedTraderUserInfoTableQEAAXEPE_140363BC0.cpp" />
    <ClCompile Include="Source\CompleteRepriceCUnmannedTraderUserInfoTableQEAAXEP_140363D70.cpp" />
    <ClCompile Include="Source\CompleteRequestRefundPatriarchElectProcessorQEAAXP_1402BB9A0.cpp" />
    <ClCompile Include="Source\CompleteReRegistCUnmannedTraderUserInfoTableQEAAXP_140364520.cpp" />
    <ClCompile Include="Source\CompleteReRegistRollBackCUnmannedTraderUserInfoTab_1403648A0.cpp" />
    <ClCompile Include="Source\CompleteSearchCUnmannedTraderUserInfoTableQEAAXEEP_1403642F0.cpp" />
    <ClCompile Include="Source\CompleteTimeOutClearCUnmannedTraderUserInfoTableQE_140364400.cpp" />
    <ClCompile Include="Source\CompleteUpdateCheatRegistTimeCUnmannedTraderUserIn_1403647F0.cpp" />
    <ClCompile Include="Source\CompleteUpdateStateCUnmannedTraderUserInfoTableQEA_140363AB0.cpp" />
    <ClCompile Include="Source\CompleteWorkCashDbWorkerUEAAXXZ_1402EED20.cpp" />
    <ClCompile Include="Source\CompleteWorkCCashDbWorkerGBUEAAXXZ_140318EC0.cpp" />
    <ClCompile Include="Source\CompleteWorkCCashDbWorkerNULLUEAAXXZ_1402F30A0.cpp" />
    <ClCompile Include="Source\CompleteWorkCCashDBWorkManagerQEAAXXZ_1402F33A0.cpp" />
    <ClCompile Include="Source\Complete_DB_Update_CommitteeCGuildQEAAXPEADZ_1402597B0.cpp" />
    <ClCompile Include="Source\Complete_db_Update_Data_For_TradeCMainThreadAEAAXP_1401B93C0.cpp" />
    <ClCompile Include="Source\Complete_RenameChar_DB_SelectCPotionMgrQEAAXEPEADZ_14039F190.cpp" />
    <ClCompile Include="Source\Complete_RenameChar_DB_UpdateCPotionMgrQEAAXEPEADZ_14039F2A0.cpp" />
    <ClCompile Include="Source\ConfigUserODBCCRFNewDatabaseQEAA_NPEBD00GZ_1404880C0.cpp" />
    <ClCompile Include="Source\ConfigUserODBCCRusiaBillingMgrQEAAHPEAD00GZ_140321710.cpp" />
    <ClCompile Include="Source\constructallocatorPEAVCLogTypeDBTaskstdQEAAXPEAPEA_1402C8310.cpp" />
    <ClCompile Include="Source\consume_del_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_14023C5D0.cpp" />
    <ClCompile Include="Source\Cont_UserSave_CompleteCMainThreadQEAAXPEAU_DB_QRY__1401F3C90.cpp" />
    <ClCompile Include="Source\Cont_UserSave_CompleteCUserDBQEAAXEPEAU_AVATOR_DAT_1401141C0.cpp" />
    <ClCompile Include="Source\ConvertCodeIntoItemYAPEAU_db_con_STORAGE_LISTPEADE_140166EA0.cpp" />
    <ClCompile Include="Source\ConvertErrorCodeCashDbWorkerMEAAHDZ_1402F0CD0.cpp" />
    <ClCompile Include="Source\ConvertErrorCodeCCashDbWorkerJPMEAAHDZ_1403206A0.cpp" />
    <ClCompile Include="Source\ConvertErrorCodeCCashDbWorkerNULLMEAAHDZ_1402F1780.cpp" />
    <ClCompile Include="Source\ConvertLocalToWorldDummyCMapDataQEAA_NPEAVCDummyPo_140185050.cpp" />
    <ClCompile Include="Source\coupon_use_buy_itemCMgrAvatorItemHistoryQEAAXPEAU__140240950.cpp" />
    <ClCompile Include="Source\CovDBKey_ANIMUSKEYQEAAEXZ_1401BF2E0.cpp" />
    <ClCompile Include="Source\CovDBKey_COMBINEKEYQEAAHXZ_1401BF320.cpp" />
    <ClCompile Include="Source\CovDBKey_EMBELLKEYQEAAHXZ_1401BF2A0.cpp" />
    <ClCompile Include="Source\CovDBKey_EQUIPKEYQEAAFXZ_1401BF280.cpp" />
    <ClCompile Include="Source\CovDBKey_FORCEKEYQEAAHXZ_1401BF2C0.cpp" />
    <ClCompile Include="Source\CovDBKey_INVENKEYQEAAHXZ_1400CA7B0.cpp" />
    <ClCompile Include="Source\CovDBKey_LINKKEYQEAAFXZ_1401BF300.cpp" />
    <ClCompile Include="Source\CreateCMonsterSPGroupTableQEAA_NPEAVCRecordData000_14015EAC0.cpp" />
    <ClCompile Include="Source\CreateCUnmannedTraderClassInfoTableCodeTypeUEAAPEA_140377980.cpp" />
    <ClCompile Include="Source\CreateCUnmannedTraderClassInfoTableTypeUEAAPEAVCUn_14037DFD0.cpp" />
    <ClCompile Include="Source\CreateDBTableAutominePersonalMgrQEAA_NXZ_1402DE950.cpp" />
    <ClCompile Include="Source\CreateTblLtdCRFDBItemLogQEAA_NHZ_140485500.cpp" />
    <ClCompile Include="Source\CreateTblLtd_ItemInfoCRFDBItemLogQEAA_NHZ_140485630.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataBRUEAAPEAVCashDbWork_14022EEA0.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataCNUEAAPEAVCashDbWork_140230710.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataESUEAAPEAVCashDbWork_140231840.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataGBUEAAPEAVCashDbWork_14022C020.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataIDUEAAPEAVCashDbWork_14022C8E0.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataJPUEAAPEAVCashDbWork_14022D280.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataKRUEAAPEAVCashDbWork_14022B340.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataNULLUEAAPEAVCashDbWo_140212FD0.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataPHUEAAPEAVCashDbWork_14022DD10.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataRUUEAAPEAVCashDbWork_14022E6C0.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataTHUEAAPEAVCashDbWork_1402321C0.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataTWUEAAPEAVCashDbWork_140230040.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataUEAAPEAVCashDbWorker_140211BE0.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingDataUSUEAAPEAVCashDbWork_140231340.cpp" />
    <ClCompile Include="Source\CreateWorkerCNationSettingManagerQEAAPEAVCashDbWor_1402F36E0.cpp" />
    <ClCompile Include="Source\create_amine_personalCRFWorldDatabaseQEAA_NXZ_1404A9D10.cpp" />
    <ClCompile Include="Source\create_automine_tableCRFWorldDatabaseQEAA_NXZ_1404A8760.cpp" />
    <ClCompile Include="Source\Create_PvpPointGuildRankTableCRFWorldDatabaseQEAA__1404A6D20.cpp" />
    <ClCompile Include="Source\create_sumtotal_dungeonCRFWorldDatabaseQEAA_NHPEAP_1404A98A0.cpp" />
    <ClCompile Include="Source\create_table_atrade_taxrateCRFWorldDatabaseQEAA_NX_1404A8140.cpp" />
    <ClCompile Include="Source\CrtDbgReportCAtlTraceModuleQEAAXP6AHHPEBDH00ZZZ_1406760D0.cpp" />
    <ClCompile Include="Source\CrtDbgReportCAtlTraceModuleQEBAP6AHHPEBDH00ZZXZ_140670050.cpp" />
    <ClCompile Include="Source\cut_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_con_STO_14023B400.cpp" />
    <ClCompile Include="Source\DatabaseInitCMainThreadAEAA_NPEAD0Z_1401ED230.cpp" />
    <ClCompile Include="Source\DataValidCheckReviseCUserDBSA_NPEAU_AVATOR_DATAPEA_140118940.cpp" />
    <ClCompile Include="Source\DBProcessCLogTypeDBTaskManagerQEAAXXZ_1402C3130.cpp" />
    <ClCompile Include="Source\db_Add_PvpPointCMainThreadQEAAEKKKZ_1401B0B90.cpp" />
    <ClCompile Include="Source\db_buy_emblemCMainThreadQEAAEKHKKKPEAN0PEAEPEAD1Z_1401B1E60.cpp" />
    <ClCompile Include="Source\db_char_set_aliveCMainThreadQEAAEKEKPEADEPEAU_REGE_1401B23F0.cpp" />
    <ClCompile Include="Source\db_Delete_AvatorCMainThreadQEAAEKEZ_1401A3470.cpp" />
    <ClCompile Include="Source\db_disjoint_guildCMainThreadQEAAEKZ_1401B2390.cpp" />
    <ClCompile Include="Source\db_GM_GreetingMsgCMainThreadQEAAEPEAU_qry_case_gm__1401B2270.cpp" />
    <ClCompile Include="Source\db_GUILD_GreetingMsgCMainThreadQEAAEPEAU_qry_case__1401B2330.cpp" />
    <ClCompile Include="Source\db_input_guild_moneyCMainThreadQEAAEKKKKPEAN0PEAEP_1401B0FF0.cpp" />
    <ClCompile Include="Source\db_input_guild_money_atradetaxCMainThreadQEAAEKKKP_1401B1B80.cpp" />
    <ClCompile Include="Source\db_Insert_AvatorCMainThreadQEAAEKPEADPEAU_REGED_AV_1401A3280.cpp" />
    <ClCompile Include="Source\db_Insert_ChangeClass_AfterInitClassCMainThreadQEA_1401B2A20.cpp" />
    <ClCompile Include="Source\db_Insert_CharacSelect_LogCMainThreadQEAAEKPEADK0G_1401B2860.cpp" />
    <ClCompile Include="Source\db_Insert_Economy_HistoryCMainThreadQEAAEKPEAU_wor_1401A64E0.cpp" />
    <ClCompile Include="Source\db_Insert_guildCMainThreadQEAAEPEAKPEADE0Z_1401B0C80.cpp" />
    <ClCompile Include="Source\db_Insert_ItemCMainThreadQEAAEKKKKEZ_1401B0C00.cpp" />
    <ClCompile Include="Source\db_LoadGreetingMsgCMainThreadQEAA_NXZ_1401B4AE0.cpp" />
    <ClCompile Include="Source\db_Load_AvatorCMainThreadQEAAEKKPEAU_AVATOR_DATAPE_1401A34D0.cpp" />
    <ClCompile Include="Source\db_Load_ContentCMainThreadQEAAEPEADZ_1401B3F00.cpp" />
    <ClCompile Include="Source\db_load_invenAutominePersonalMgrQEAA_NKPEAU_PERSON_1402E05D0.cpp" />
    <ClCompile Include="Source\db_Load_PostStorageCMainThreadQEAAEPEADZ_1401B2D80.cpp" />
    <ClCompile Include="Source\db_Load_ReturnPostCMainThreadQEAAEPEADZ_1401B37B0.cpp" />
    <ClCompile Include="Source\db_Log_UserNumCMainThreadQEAAEHHZ_1401A65B0.cpp" />
    <ClCompile Include="Source\db_output_guild_moneyCMainThreadQEAAEKKKKPEAN0PEAE_1401B16E0.cpp" />
    <ClCompile Include="Source\db_RACE_GreetingMsgCMainThreadQEAAEPEAU_qry_case_r_1401B22D0.cpp" />
    <ClCompile Include="Source\db_Reged_AvatorCMainThreadQEAAEKPEAU_REGEDPEAU_NOT_1401A2C70.cpp" />
    <ClCompile Include="Source\db_Select_Economy_HistoryCMainThreadQEAAEPEAU_econ_1401A27D0.cpp" />
    <ClCompile Include="Source\db_Update_AvatorCMainThreadQEAAEKPEAU_AVATOR_DATA0_1401A5360.cpp" />
    <ClCompile Include="Source\DB_Update_GuildMasterCGuildQEAA_NPEAU_guild_member_140252BD0.cpp" />
    <ClCompile Include="Source\db_update_guildmasterCMainThreadQEAAEPEAU_qry_case_1401B2BB0.cpp" />
    <ClCompile Include="Source\DB_Update_GuildMaster_CompleteCGuildQEAAXKEKEZ_140252D10.cpp" />
    <ClCompile Include="Source\db_update_guildmember_addCMainThreadQEAAEKKEHZ_1401B0E90.cpp" />
    <ClCompile Include="Source\db_update_guildmember_delCMainThreadQEAAEKKHZ_1401B0F50.cpp" />
    <ClCompile Include="Source\db_Update_PostStorageCMainThreadQEAAEKPEAU_AVATOR__1401B4110.cpp" />
    <ClCompile Include="Source\db_Update_PvpInfoCMainThreadQEAAEKEPEAFNZ_1401B0B20.cpp" />
    <ClCompile Include="Source\deallocateallocatorPEAVCLogTypeDBTaskstdQEAAXPEAPE_1402C5C10.cpp" />
    <ClCompile Include="Source\dec_reftable_objlua_tinkerQEAAXXZ_140446260.cpp" />
    <ClCompile Include="Source\DeleteLinkLendItemMngQEAA_NGEPEAU_db_con_STORAGE_L_14030DD80.cpp" />
    <ClCompile Include="Source\DeleteLinkLendItemSheetAEAA_NEPEAU_db_con_STORAGE__14030F080.cpp" />
    <ClCompile Include="Source\DeleteListTimeLimitJadeMngQEAA_NGPEAU_db_con_STORA_1403FAC50.cpp" />
    <ClCompile Include="Source\DeleteUseListTimeLimitJadeAEAA_NPEAU_db_con_STORAG_1403FA860.cpp" />
    <ClCompile Include="Source\DeleteWaitListTimeLimitJadeAEAA_NPEAU_db_con_STORA_1403FA710.cpp" />
    <ClCompile Include="Source\Delete_Avator_CompleteCMainThreadQEAAXPEAU_DB_QRY__1401F37E0.cpp" />
    <ClCompile Include="Source\Delete_Char_CompleteCUserDBQEAAXEEZ_1401124D0.cpp" />
    <ClCompile Include="Source\Delete_Char_RequestCUserDBQEAA_NEZ_140112230.cpp" />
    <ClCompile Include="Source\Delete_GuildCRFWorldDatabaseQEAA_NKZ_14049C2D0.cpp" />
    <ClCompile Include="Source\Delete_ItemChargeCRFWorldDatabaseQEAA_NKZ_1404943F0.cpp" />
    <ClCompile Include="Source\delete_npc_quest_itemCMgrAvatorItemHistoryQEAAXHPE_1402406A0.cpp" />
    <ClCompile Include="Source\Delete_PatriarchCommCRFWorldDatabaseQEAAEKPEADZ_1404C0120.cpp" />
    <ClCompile Include="Source\Delete_TrunkItemChargeCRFWorldDatabaseQEAA_NKZ_1404A4330.cpp" />
    <ClCompile Include="Source\Delete_TrunkItemCharge_ExtendCRFWorldDatabaseQEAA__1404A48A0.cpp" />
    <ClCompile Include="Source\DelPostDataCUserDBQEAAXKZ_140117BE0.cpp" />
    <ClCompile Include="Source\DelUnit_LIST_UNIT_DB_BASEQEAAXXZ_1401082C0.cpp" />
    <ClCompile Include="Source\destroyallocatorPEAVCLogTypeDBTaskstdQEAAXPEAPEAVC_1402C8370.cpp" />
    <ClCompile Include="Source\DestroyCLogTypeDBTaskManagerSAXXZ_1402C2C50.cpp" />
    <ClCompile Include="Source\DestroyCLogTypeDBTaskPoolAEAAXXZ_1402C28D0.cpp" />
    <ClCompile Include="Source\DestroyCUnmannedTraderGroupItemInfoTableSAXXZ_14036B1B0.cpp" />
    <ClCompile Include="Source\DestroyCUnmannedTraderUserInfoTableSAXXZ_140363780.cpp" />
    <ClCompile Include="Source\DiagRecALogCRFNewDatabaseIEAAXFFPEAXZ_1404862C0.cpp" />
    <ClCompile Include="Source\DiagRecWLogCRFNewDatabaseIEAAXFFPEAXZ_140486420.cpp" />
    <ClCompile Include="Source\DiscardBytesRandomNumberGeneratorCryptoPPUEAAX_KZ_1405F4110.cpp" />
    <ClCompile Include="Source\DividedByIntegerCryptoPPQEBAAV12AEBV12Z_1405EA480.cpp" />
    <ClCompile Include="Source\DividedByIntegerCryptoPPQEBAAV12_KZ_1405EA980.cpp" />
    <ClCompile Include="Source\DividedByPolynomialMod2CryptoPPQEBAAV12AEBV12Z_140627F30.cpp" />
    <ClCompile Include="Source\DoWorkCashDbWorkerMEAAXXZ_1402F0B80.cpp" />
    <ClCompile Include="Source\DoWorkCCashDbWorkerGBMEAAXXZ_1403193D0.cpp" />
    <ClCompile Include="Source\DoWorkCCashDbWorkerNULLMEAAXXZ_1402F30F0.cpp" />
    <ClCompile Include="Source\dtor000CD2DBitmapBrushQEAAPEAVCRenderTargetPEAUD2D_140548DF0.cpp" />
    <ClCompile Include="Source\dtor00_AfxQueryStatusOleCommandHelperYAJPEAVCCmdTa_14054E120.cpp" />
    <ClCompile Include="Source\dtor100CD2DBitmapBrushQEAAPEAVCRenderTargetPEAUD2D_140548E10.cpp" />
    <ClCompile Include="Source\dtor10_AfxQueryStatusOleCommandHelperYAJPEAVCCmdTa_14054E140.cpp" />
    <ClCompile Include="Source\dtor20_AfxQueryStatusOleCommandHelperYAJPEAVCCmdTa_14054E160.cpp" />
    <ClCompile Include="Source\DummyCreateCUserDBQEAAXKZ_140110550.cpp" />
    <ClCompile Include="Source\DXUtil_ReadBoolRegKeyYAJPEAUHKEY__PEADPEAHHZ_140436830.cpp" />
    <ClCompile Include="Source\D_Vector_const_iteratorPEAVCLogTypeDBTaskVallocato_1402C5E60.cpp" />
    <ClCompile Include="Source\D_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEAVC_1402C5100.cpp" />
    <ClCompile Include="Source\emptyvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTyp_1402C4CE0.cpp" />
    <ClCompile Include="Source\EndDataBaseCRFNewDatabaseQEAAXXZ_1404869C0.cpp" />
    <ClCompile Include="Source\endvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTypeD_1402C4BF0.cpp" />
    <ClCompile Include="Source\Enter_AccountCUserDBQEAA_NKKKPEAKZ_1401105B0.cpp" />
    <ClCompile Include="Source\erasevectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTyp_1402C5600.cpp" />
    <ClCompile Include="Source\EroorActionProcSQL_ERRORCRFNewDatabaseIEAA_NPEAXZ_140486D40.cpp" />
    <ClCompile Include="Source\ErrFmtLogCRFNewDatabaseIEAAXPEADZZ_140484E30.cpp" />
    <ClCompile Include="Source\ErrFmtLogCRFNewDatabaseIEAAXPEA_WZZ_140489450.cpp" />
    <ClCompile Include="Source\ErrLogCRFNewDatabaseIEAAXPEADZ_1404894F0.cpp" />
    <ClCompile Include="Source\ErrorActionCRFNewDatabaseIEAAXFPEAXZ_140486C70.cpp" />
    <ClCompile Include="Source\ErrorMsgLogCRFNewDatabaseIEAAXFPEAD0PEAXZ_140486EC0.cpp" />
    <ClCompile Include="Source\ErrorMsgLogCRFNewDatabaseIEAAXFPEA_W0PEAXZ_140487060.cpp" />
    <ClCompile Include="Source\exchange_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_co_14023BE30.cpp" />
    <ClCompile Include="Source\ExecUpdateBinaryQueryCRFNewDatabaseQEAA_NPEADPEAXH_140488250.cpp" />
    <ClCompile Include="Source\ExecUpdateQueryCRFNewDatabaseQEAA_NPEAD_NZ_1404872F0.cpp" />
    <ClCompile Include="Source\ExecUpdateQueryCRFNewDatabaseQEAA_NPEA_W_NZ_1404871A0.cpp" />
    <ClCompile Include="Source\exist_aminpersonal_invenCRFWorldDatabaseQEAAHKZ_1404A9DC0.cpp" />
    <ClCompile Include="Source\exist_automineCRFWorldDatabaseQEAAHEEZ_1404A87C0.cpp" />
    <ClCompile Include="Source\Exit_Account_CompleteCUserDBQEAAXEZ_1401113E0.cpp" />
    <ClCompile Include="Source\Exit_Account_RequestCUserDBQEAAXXZ_140111020.cpp" />
    <ClCompile Include="Source\extractAP_BatterySlotQEAA_NPEAU_db_con_STORAGE_LIS_1402D9E20.cpp" />
    <ClCompile Include="Source\extract_batteryAutominePersonalQEAA_NEPEAU_db_con__1402DA880.cpp" />
    <ClCompile Include="Source\E_Vector_const_iteratorPEAVCLogTypeDBTaskVallocato_1402C5E80.cpp" />
    <ClCompile Include="Source\E_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEAVC_1402C5140.cpp" />
    <ClCompile Include="Source\FeedbackSizeNameCryptoPPYAPEBDXZ_14061CE30.cpp" />
    <ClCompile Include="Source\FileSizeCRecordDataQEAAKPEBDZ_1400441C0.cpp" />
    <ClCompile Include="Source\fillPEAPEAVCLogTypeDBTaskPEAV1stdYAXPEAPEAVCLogTyp_1402C70F0.cpp" />
    <ClCompile Include="Source\FindByIndexCUnmannedTraderUserInfoTableAEAAPEAVCUn_140366DB0.cpp" />
    <ClCompile Include="Source\FindCUnmannedTraderUserInfoTableAEAAPEAVCUnmannedT_140365A90.cpp" />
    <ClCompile Include="Source\FindDummyCDummyPosTableSA_NPEAD0PEAU_dummy_positio_14018A780.cpp" />
    <ClCompile Include="Source\FindUserCUnmannedTraderUserInfoTableAEAAPEAVCUnman_140366CA0.cpp" />
    <ClCompile Include="Source\FirstSettingDataCUserDBAEAA_NXZ_14011A600.cpp" />
    <ClCompile Include="Source\FixUnit_WEAPON_PARAMQEAAXPEAU_LIST_UNIT_DB_BASEZ_14007D2A0.cpp" />
    <ClCompile Include="Source\FixWeapon_WEAPON_PARAMQEAAXPEAU_db_con_STORAGE_LIS_14007BA30.cpp" />
    <ClCompile Include="Source\FmtLogCRFNewDatabaseIEAAXPEADZZ_140484D90.cpp" />
    <ClCompile Include="Source\FmtLogCRFNewDatabaseIEAAXPEA_WZZ_140489550.cpp" />
    <ClCompile Include="Source\ForceCloseCommandCUserDBQEAAXEK_NPEADZ_140110350.cpp" />
    <ClCompile Include="Source\FreeCEnglandBillingMgrQEAA_NXZ_140319730.cpp" />
    <ClCompile Include="Source\FreeSelectHandleCRFNewDatabaseQEAA_NXZ_1404876A0.cpp" />
    <ClCompile Include="Source\FreeUpdateHandleCRFNewDatabaseQEAA_NXZ_140487730.cpp" />
    <ClCompile Include="Source\GenerateRandomGeneratableCryptoMaterialCryptoPPUEA_14044BB80.cpp" />
    <ClCompile Include="Source\GenerateRandomWithKeySizeGeneratableCryptoMaterial_1405F5D60.cpp" />
    <ClCompile Include="Source\GetActPointCUserDBQEAAKEZ_14007B850.cpp" />
    <ClCompile Include="Source\GetBaseDL_FixedBasePrecomputationImplUEC2NPointCry_140556170.cpp" />
    <ClCompile Include="Source\GetBaseDL_FixedBasePrecomputationImplUECPPointCryp_14044EAC0.cpp" />
    <ClCompile Include="Source\GetBaseDL_FixedBasePrecomputationImplVIntegerCrypt_140551630.cpp" />
    <ClCompile Include="Source\GetBasePrecomputationDL_GroupParametersImplVEcPrec_140454980.cpp" />
    <ClCompile Include="Source\GetBasePrecomputationDL_GroupParametersImplVEcPrec_14055CD50.cpp" />
    <ClCompile Include="Source\GetBasePrecomputationDL_GroupParameters_ECVEC2NCry_1405576F0.cpp" />
    <ClCompile Include="Source\GetBasePrecomputationDL_GroupParameters_ECVECPCryp_14044F7C0.cpp" />
    <ClCompile Include="Source\GetBillingTypeCUserDBQEAAHXZ_14007DA20.cpp" />
    <ClCompile Include="Source\GetCashDBDBIPCNationSettingManagerQEAAPEBDXZ_1402F2C10.cpp" />
    <ClCompile Include="Source\GetCashDBIDCNationSettingManagerQEAAPEBDXZ_1402F2C50.cpp" />
    <ClCompile Include="Source\GetCashDBNameCNationSettingManagerQEAAPEBDXZ_1402F2C30.cpp" />
    <ClCompile Include="Source\GetCashDBPortCNationSettingManagerQEAAGXZ_1402F2C90.cpp" />
    <ClCompile Include="Source\GetCashDBPWCNationSettingManagerQEAAPEBDXZ_1402F2C70.cpp" />
    <ClCompile Include="Source\GetCheatTableCNationSettingManagerQEAAPEAUCHEAT_CO_14029D450.cpp" />
    <ClCompile Include="Source\GetCodeCNationCodeStrTableQEAAHPEBDZ_14020ACA0.cpp" />
    <ClCompile Include="Source\GetCompleteCLogTypeDBTaskPoolQEAAPEAVCLogTypeDBTas_1402C25D0.cpp" />
    <ClCompile Include="Source\GetCRecordData_SetItemCSUItemSystemQEAAPEAVCRecord_1402E4680.cpp" />
    <ClCompile Include="Source\GetDataCLogTypeDBTaskQEAAPEADXZ_1402C4250.cpp" />
    <ClCompile Include="Source\GetDBProcCLogTypeDBTaskManagerQEAA_NXZ_1402C4360.cpp" />
    <ClCompile Include="Source\GetDBRetCLogTypeDBTaskQEAAEXZ_1402C42B0.cpp" />
    <ClCompile Include="Source\GetDBTaskDataStatusCLogTypeDBTaskManagerQEAA_NXZ_1402C3850.cpp" />
    <ClCompile Include="Source\GetEffectCode_LIST_SFCONT_DB_BASEQEAAEXZ_14007A480.cpp" />
    <ClCompile Include="Source\GetEffectIndex_LIST_SFCONT_DB_BASEQEAAGXZ_14007A4A0.cpp" />
    <ClCompile Include="Source\GetEmptyCLogTypeDBTaskPoolQEAAPEAVCLogTypeDBTaskXZ_1402C2430.cpp" />
    <ClCompile Include="Source\GetEmptyRecordSerialCUnmannedTraderControllerIEAAE_140350270.cpp" />
    <ClCompile Include="Source\GetEquipCEquipItemSFAgentQEAAPEAU_db_con_STORAGE_L_1401210A0.cpp" />
    <ClCompile Include="Source\GetGodBoxItemInfoPtrCGoldenBoxItemMgrQEAAPEAU_db_g_1402059F0.cpp" />
    <ClCompile Include="Source\GetGoldBoxConsumableCHolyStoneSystemQEAA_NXZ_140416C40.cpp" />
    <ClCompile Include="Source\GetGoldBoxItemIndexCGoldenBoxItemMgrQEAAGGZ_1400D3E70.cpp" />
    <ClCompile Include="Source\GetGoldBoxItemPtrCGoldenBoxItemMgrQEAAPEADXZ_140415230.cpp" />
    <ClCompile Include="Source\GetGroupIDCUnmannedTraderClassInfoTableCodeTypeUEA_1403773A0.cpp" />
    <ClCompile Include="Source\GetGroupIDCUnmannedTraderClassInfoTableCodeTypeUEA_140377A50.cpp" />
    <ClCompile Include="Source\GetGroupIDCUnmannedTraderClassInfoTableTypeUEAA_NE_14037D2B0.cpp" />
    <ClCompile Include="Source\GetGroupIDCUnmannedTraderClassInfoTableTypeUEAA_NE_14037D4D0.cpp" />
    <ClCompile Include="Source\GetGroupIDCUnmannedTraderGroupItemInfoTableQEAA_NE_1403600C0.cpp" />
    <ClCompile Include="Source\GetGroupPrecomputationDL_GroupParametersImplVEcPre_1404502C0.cpp" />
    <ClCompile Include="Source\GetGroupPrecomputationDL_GroupParametersImplVEcPre_140558040.cpp" />
    <ClCompile Include="Source\GetInxCLogTypeDBTaskQEAAKXZ_1402C4010.cpp" />
    <ClCompile Include="Source\GetItemTableCodeYAHPEADZ_1400362B0.cpp" />
    <ClCompile Include="Source\GetLeftTime_LIST_SFCONT_DB_BASEQEAAGXZ_14007A4E0.cpp" />
    <ClCompile Include="Source\GetLocalHourCRFNewDatabaseIEAAEXZ_140487000.cpp" />
    <ClCompile Include="Source\GetLv_LIST_SFCONT_DB_BASEQEAAEXZ_14007A4C0.cpp" />
    <ClCompile Include="Source\GetMachineInfoAutoMineMachineQEAAXPEAU_DB_LOAD_AUT_1402D1250.cpp" />
    <ClCompile Include="Source\GetMaxRegistCntCUnmannedTraderUserInfoTableQEAAEGK_140365830.cpp" />
    <ClCompile Include="Source\GetNpcRecordCItemStoreQEAAPEAU_base_fldXZ_140262400.cpp" />
    <ClCompile Include="Source\GetOrder_LIST_SFCONT_DB_BASEQEAAEXZ_14007A460.cpp" />
    <ClCompile Include="Source\GetOreIndexFromRateCOreCuttingTableQEAAKKKZ_1400D3F50.cpp" />
    <ClCompile Include="Source\GetPdbDll_1404DEAB0.cpp" />
    <ClCompile Include="Source\GetPrimeTableCryptoPPYAPEBGAEAIZ_140640560.cpp" />
    <ClCompile Include="Source\GetProcCLogTypeDBTaskPoolQEAAPEAVCLogTypeDBTaskXZ_1402C2510.cpp" />
    <ClCompile Include="Source\GetProcRetCLogTypeDBTaskQEAAEXZ_1402C42D0.cpp" />
    <ClCompile Include="Source\GetPtrActPointCUserDBQEAAPEAKXZ_1400F7800.cpp" />
    <ClCompile Include="Source\GetPtrFromItemCode_STORAGE_LISTQEAAPEAU_db_con1PEA_14010F6D0.cpp" />
    <ClCompile Include="Source\GetPtrFromItemInfo_STORAGE_LISTQEAAPEAU_db_con1EGZ_14010F530.cpp" />
    <ClCompile Include="Source\GetPtrFromSerial_STORAGE_LISTQEAAPEAU_db_con1GZ_14010EE40.cpp" />
    <ClCompile Include="Source\GetPublicPrecomputationDL_PublicKeyImplVDL_GroupPa_140451110.cpp" />
    <ClCompile Include="Source\GetPublicPrecomputationDL_PublicKeyImplVDL_GroupPa_140558A30.cpp" />
    <ClCompile Include="Source\GetPublicPrecomputationDL_PublicKeyImplVDL_GroupPa_1405695A0.cpp" />
    <ClCompile Include="Source\GetPublicPrecomputationDL_PublicKeyImplVDL_GroupPa_140637060.cpp" />
    <ClCompile Include="Source\GetPublicPrecomputationDL_PublicKeyImplVDL_GroupPa_140637A10.cpp" />
    <ClCompile Include="Source\GetQueryCUnmannedTraderSortTypeQEBAPEBDXZ_14035F6F0.cpp" />
    <ClCompile Include="Source\GetQueryTypeCLogTypeDBTaskQEAAHXZ_1402C4030.cpp" />
    <ClCompile Include="Source\GetRand_100_per_random_tableQEAAGXZ_1400B81D0.cpp" />
    <ClCompile Include="Source\GetRecordByHashCRecordDataQEAAPEAU_base_fldPEBDHHZ_14008F2F0.cpp" />
    <ClCompile Include="Source\GetRecordCDummyPosTableQEAAPEAU_dummy_positionHZ_14018A740.cpp" />
    <ClCompile Include="Source\GetRecordCDummyPosTableQEAAPEAU_dummy_positionPEAD_14018A600.cpp" />
    <ClCompile Include="Source\GetRecordCEventLootTableQEAAPEAU_event_drop1PEBDZ_140136C80.cpp" />
    <ClCompile Include="Source\GetRecordCItemUpgradeTableQEAAPEAU_ItemUpgrade_fld_14007AEE0.cpp" />
    <ClCompile Include="Source\GetRecordCMapDataTableQEAAPEAU_map_fldKZ_1400EF2D0.cpp" />
    <ClCompile Include="Source\GetRecordCMonsterSPGroupTableQEAAPEAU_monster_sp_g_14015EE60.cpp" />
    <ClCompile Include="Source\GetRecordCMonsterSPGroupTableQEAAPEAU_monster_sp_g_14015EF00.cpp" />
    <ClCompile Include="Source\GetRecordCRecordDataQEAAPEAU_base_fldHZ_1400347C0.cpp" />
    <ClCompile Include="Source\GetRecordCRecordDataQEAAPEAU_base_fldPEBDHZ_14008F410.cpp" />
    <ClCompile Include="Source\GetRecordCRecordDataQEAAPEAU_base_fldPEBDZ_140044B60.cpp" />
    <ClCompile Include="Source\GetRecordFromResCItemUpgradeTableQEAAPEAU_ItemUpgr_1400B84E0.cpp" />
    <ClCompile Include="Source\GetRecordNumCDummyPosTableQEAAHXZ_14018A5E0.cpp" />
    <ClCompile Include="Source\GetRecordNumCMapDataTableQEAAHXZ_140199050.cpp" />
    <ClCompile Include="Source\GetRecordNumCRecordDataQEAAHXZ_140044B00.cpp" />
    <ClCompile Include="Source\GetRegItemInfoCUnmannedTraderUserInfoTableQEAAPEBV_140365910.cpp" />
    <ClCompile Include="Source\GetSerialNumber_db_con_STORAGE_LISTQEAAKXZ_14010E1D0.cpp" />
    <ClCompile Include="Source\GetSetItemTableInfoCSUItemSystemQEAAHKPEADHZ_1402E4280.cpp" />
    <ClCompile Include="Source\GetSizeCItemUpgradeTableQEAAHXZ_14029D4B0.cpp" />
    <ClCompile Include="Source\GetSortTypeCUnmannedTraderGroupItemInfoTableQEAAPE_14035F760.cpp" />
    <ClCompile Include="Source\GetStaticIndexedBufferCEntityQEAAPEAVCIndexBufferX_1404F5930.cpp" />
    <ClCompile Include="Source\GetStatIndex_STAT_DB_BASESAHEEZ_14007C560.cpp" />
    <ClCompile Include="Source\GetStrCNationCodeStrTableQEAAPEBDHZ_14020ADB0.cpp" />
    <ClCompile Include="Source\GetTableCodeCUnmannedTraderRegistItemInfoQEBAEXZ_140243B00.cpp" />
    <ClCompile Include="Source\GetUseCashQueryStrCashDbWorkerUEAAXAEAU_param_cash_1402EEEB0.cpp" />
    <ClCompile Include="Source\GetUseCashQueryStrCCashDbWorkerBRUEAAXAEAU_param_c_140321F40.cpp" />
    <ClCompile Include="Source\GetUseCashQueryStrCCashDbWorkerESUEAAXAEAU_param_c_140322400.cpp" />
    <ClCompile Include="Source\GetUseCashQueryStrCCashDbWorkerGBUEAAXAEAU_param_c_1403190B0.cpp" />
    <ClCompile Include="Source\GetUseCashQueryStrCCashDbWorkerIDUEAAXAEAU_param_c_14031FDD0.cpp" />
    <ClCompile Include="Source\GetUseCashQueryStrCCashDbWorkerJPUEAAXAEAU_param_c_14031FF50.cpp" />
    <ClCompile Include="Source\GetUseCashQueryStrCCashDbWorkerNULLUEAAXAEAU_param_1402F30B0.cpp" />
    <ClCompile Include="Source\GetUseCashQueryStrCCashDbWorkerTHUEAAXAEAU_param_c_1403225A0.cpp" />
    <ClCompile Include="Source\GetUseCashQueryStrCCashDbWorkerTWUEAAXAEAU_param_c_1403220C0.cpp" />
    <ClCompile Include="Source\GetUseCashQueryStrCCashDbWorkerUSUEAAXAEAU_param_c_140322260.cpp" />
    <ClCompile Include="Source\GetUseCashQueryStrCCashDBWorkManagerQEAAXAEAU_para_1402F3430.cpp" />
    <ClCompile Include="Source\GetVersionCUnmannedTraderGroupItemInfoTableQEAA_NE_140360890.cpp" />
    <ClCompile Include="Source\GetWeaponTolType_WEAPON_PARAMQEAAHPEAU_db_con_STOR_14007BD10.cpp" />
    <ClCompile Include="Source\GetWebDBIDCMsgRACE_BOSS_MSGQEAAKXZ_1402A2D90.cpp" />
    <ClCompile Include="Source\GetWorldDBIDCNationSettingManagerQEAAPEBDXZ_1402057D0.cpp" />
    <ClCompile Include="Source\GetWorldDBPWCNationSettingManagerQEAAPEBDXZ_1402057F0.cpp" />
    <ClCompile Include="Source\get_AdjustableGradeCGuildMasterEffectQEAAEXZ_1400EF310.cpp" />
    <ClCompile Include="Source\get_batteryAP_BatterySlotQEAAPEAU_db_con_STORAGE_L_1402DDE60.cpp" />
    <ClCompile Include="Source\get_itemAutominePersonalQEAAPEAU_db_con_STORAGE_LI_1402E1810.cpp" />
    <ClCompile Include="Source\grade_down_itemCMgrAvatorItemHistoryQEAAXHPEAU_db__14023AC20.cpp" />
    <ClCompile Include="Source\grade_up_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_co_14023A6E0.cpp" />
    <ClCompile Include="Source\GuildQueryInfoRequestCNetworkEXAEAA_NHPEADZ_1401C8380.cpp" />
    <ClCompile Include="Source\G_Vector_const_iteratorPEAVCLogTypeDBTaskVallocato_1402C6B80.cpp" />
    <ClCompile Include="Source\G_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEAVC_1402C5D10.cpp" />
    <ClCompile Include="Source\G_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEAVC_1402C6A20.cpp" />
    <ClCompile Include="Source\HashEndianCorrectedBlockIteratedHashWithStaticTran_14044EEB0.cpp" />
    <ClCompile Include="Source\HashEndianCorrectedBlockIteratedHashWithStaticTran_140463EF0.cpp" />
    <ClCompile Include="Source\H_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEAVC_1402C6870.cpp" />
    <ClCompile Include="Source\InAtradTaxMoneyCMainThreadAEAAXPEAU_DB_QRY_SYN_DAT_1401F3600.cpp" />
    <ClCompile Include="Source\IncreaseVersionCUnmannedTraderGroupItemInfoTableQE_14036B360.cpp" />
    <ClCompile Include="Source\IncreaseVersionCUnmannedTraderGroupItemInfoTableQE_14036B580.cpp" />
    <ClCompile Include="Source\IncreaseVersionCUnmannedTraderGroupItemInfoTableQE_14036B8F0.cpp" />
    <ClCompile Include="Source\inc_reftable_objlua_tinkerQEAAXXZ_140446230.cpp" />
    <ClCompile Include="Source\IndexingCItemLootTableAEAA_NPEAVCRecordDataPEADZ_140203F20.cpp" />
    <ClCompile Include="Source\IndexingCItemUpgradeTableAEAA_NPEAVCRecordDataPEAD_1402049A0.cpp" />
    <ClCompile Include="Source\IndexingCOreCuttingTableAEAA_NPEAVCRecordData0PEAD_140204580.cpp" />
    <ClCompile Include="Source\Inform_For_Exit_By_FireguardBlockCUserDBQEAAXXZ_1401114A0.cpp" />
    <ClCompile Include="Source\InitClassCUserDBQEAA_NPEADZ_1401160E0.cpp" />
    <ClCompile Include="Source\InitCLogTypeDBTaskManagerQEAA_NXZ_1402C2CD0.cpp" />
    <ClCompile Include="Source\InitCLogTypeDBTaskPoolQEAA_NIIAEAVCLogFileZ_1402C2110.cpp" />
    <ClCompile Include="Source\InitCLogTypeDBTaskQEAA_NKIZ_1402C1D80.cpp" />
    <ClCompile Include="Source\InitCNationCodeStrTableQEAA_NXZ_14020AC40.cpp" />
    <ClCompile Include="Source\InitCPvpCashPointQEAAXPEAU_PVP_ORDER_VIEW_DB_BASEZ_1403F5010.cpp" />
    <ClCompile Include="Source\InitCUnmannedTraderGroupItemInfoTableQEAA_NXZ_14036B230.cpp" />
    <ClCompile Include="Source\InitCUnmannedTraderUserInfoTableQEAA_NXZ_140363800.cpp" />
    <ClCompile Include="Source\InitCUserDBQEAAXKZ_140110030.cpp" />
    <ClCompile Include="Source\InitDBCLogTypeDBTaskManagerQEAA_NPEBD0Z_1402C2E50.cpp" />
    <ClCompile Include="Source\InitializeCashDbWorkerUEAA_NXZ_1402EEC00.cpp" />
    <ClCompile Include="Source\InitializeCCashDbWorkerGBUEAA_NXZ_140318E40.cpp" />
    <ClCompile Include="Source\InitializeCCashDbWorkerNULLUEAA_NXZ_1402F3090.cpp" />
    <ClCompile Include="Source\InitializeCCashDBWorkManagerQEAA_NXZ_1402F3210.cpp" />
    <ClCompile Include="Source\InitializeWorkerCCashDBWorkManagerQEAA_NXZ_1402F32A0.cpp" />
    <ClCompile Include="Source\InitLogDBLtdWriterQEAA_NPEAD0Z_14024A850.cpp" />
    <ClCompile Include="Source\InitLoggerCLogTypeDBTaskManagerAEAA_NXZ_1402C36E0.cpp" />
    <ClCompile Include="Source\InitMasteryFormulaYAXPEAVCRecordData0Z_14003EA40.cpp" />
    <ClCompile Include="Source\Init_ANIMUS_DB_BASEQEAAXXZ_140076370.cpp" />
    <ClCompile Include="Source\Init_AVATOR_DB_BASEQEAAXXZ_1400756A0.cpp" />
    <ClCompile Include="Source\Init_BUDDY_DB_BASEQEAAXXZ_140076CC0.cpp" />
    <ClCompile Include="Source\Init_CRYMSG_DB_BASEQEAAXXZ_140077840.cpp" />
    <ClCompile Include="Source\Init_CUTTING_DB_BASEQEAAXXZ_140076780.cpp" />
    <ClCompile Include="Source\Init_db_con_STORAGE_LISTQEAAXXZ_1402082B0.cpp" />
    <ClCompile Include="Source\Init_DELPOST_DB_BASEQEAAXXZ_140077620.cpp" />
    <ClCompile Include="Source\Init_EMBELLISH_LIST_EQUIP_DB_BASEQEAAXXZ_140075AD0.cpp" />
    <ClCompile Include="Source\Init_EQUIP_DB_BASEQEAAXXZ_140075B90.cpp" />
    <ClCompile Include="Source\Init_FORCE_DB_BASEQEAAXXZ_140076160.cpp" />
    <ClCompile Include="Source\Init_INVEN_DB_BASEQEAAXXZ_140075DC0.cpp" />
    <ClCompile Include="Source\Init_ITEMCOMBINE_DB_BASEQEAAXXZ_1400772D0.cpp" />
    <ClCompile Include="Source\Init_LINK_DB_BASEQEAAXXZ_140075990.cpp" />
    <ClCompile Include="Source\Init_LIST_ANIMUS_DB_BASEQEAAXXZ_1400762A0.cpp" />
    <ClCompile Include="Source\Init_LIST_BUDDY_DB_BASEQEAAXXZ_140076C90.cpp" />
    <ClCompile Include="Source\Init_LIST_CRYMSG_DB_BASEQEAAXXZ_140077820.cpp" />
    <ClCompile Include="Source\Init_LIST_CUTTING_DB_BASEQEAAXXZ_140076730.cpp" />
    <ClCompile Include="Source\Init_LIST_FORCE_DB_BASEQEAAXXZ_1400760B0.cpp" />
    <ClCompile Include="Source\Init_LIST_INVEN_DB_BASEQEAAXXZ_140075CF0.cpp" />
    <ClCompile Include="Source\Init_LIST_ITEMCOMBINE_DB_BASEQEAAXXZ_140077250.cpp" />
    <ClCompile Include="Source\Init_LIST_LINK_DB_BASEQEAAXXZ_140075930.cpp" />
    <ClCompile Include="Source\Init_LIST_PERSONALAMINE_INVEN_DB_BASEQEAAXXZ_140075F20.cpp" />
    <ClCompile Include="Source\Init_LIST_QUEST_DB_BASEQEAAXXZ_1400768F0.cpp" />
    <ClCompile Include="Source\Init_LIST_SFCONT_DB_BASEQEAAXXZ_140076BB0.cpp" />
    <ClCompile Include="Source\Init_LIST_TRUNK_DB_BASEQEAAXXZ_140076FF0.cpp" />
    <ClCompile Include="Source\Init_LIST_UNIT_DB_BASEQEAAXEZ_1400764B0.cpp" />
    <ClCompile Include="Source\Init_MASTERY_PARAMQEAA_NPEAU_STAT_DB_BASEEZ_1400781D0.cpp" />
    <ClCompile Include="Source\Init_NOT_ARRANGED_AVATOR_DBQEAAXXZ_14011F070.cpp" />
    <ClCompile Include="Source\Init_NPC_QUEST_HISTORY_QUEST_DB_BASEQEAAXXZ_1400769D0.cpp" />
    <ClCompile Include="Source\Init_PCBANG_FAVOR_ITEM_DB_BASEQEAAXXZ_140077DF0.cpp" />
    <ClCompile Include="Source\Init_PERSONALAMINE_INVEN_DB_BASEQEAAXXZ_140075F70.cpp" />
    <ClCompile Include="Source\Init_POSTDATA_DB_BASEQEAAXXZ_140077680.cpp" />
    <ClCompile Include="Source\Init_POSTSTORAGE_DB_BASEQEAAXXZ_140077470.cpp" />
    <ClCompile Include="Source\Init_POTION_NEXT_USE_TIME_DB_BASEQEAAXXZ_1400779B0.cpp" />
    <ClCompile Include="Source\Init_PVPPOINT_LIMIT_DB_BASEQEAAXXZ_140077CE0.cpp" />
    <ClCompile Include="Source\Init_PVP_ORDER_VIEW_DB_BASEQEAAXXZ_140077D50.cpp" />
    <ClCompile Include="Source\init_questCMgrAvatorQuestHistoryQEAAXPEADPEAU_QUES_1402479D0.cpp" />
    <ClCompile Include="Source\Init_QUEST_DB_BASEQEAAXXZ_140076A30.cpp" />
    <ClCompile Include="Source\Init_REGED_AVATOR_DBQEAAXXZ_1400754D0.cpp" />
    <ClCompile Include="Source\Init_RETURNPOST_DB_BASEQEAAXXZ_140077570.cpp" />
    <ClCompile Include="Source\Init_SFCONT_DB_BASEQEAAXXZ_140076B00.cpp" />
    <ClCompile Include="Source\Init_START_NPC_QUEST_HISTORY_QUEST_DB_BASEQEAAXXZ_1400CFCF0.cpp" />
    <ClCompile Include="Source\Init_STAT_DB_BASEQEAAXXZ_140074640.cpp" />
    <ClCompile Include="Source\Init_SUPPLEMENT_DB_BASEQEAAXXZ_140077910.cpp" />
    <ClCompile Include="Source\Init_TRADE_DB_BASEQEAAXXZ_140077CA0.cpp" />
    <ClCompile Include="Source\Init_TRUNK_DB_BASEQEAAXXZ_1400770A0.cpp" />
    <ClCompile Include="Source\Init_UNIT_DB_BASEQEAAXXZ_1400765C0.cpp" />
    <ClCompile Include="Source\insertAP_BatterySlotQEAAHPEAU_db_con_STORAGE_LISTZ_1402D9D70.cpp" />
    <ClCompile Include="Source\InsertCCheckSumGuildDataQEAA_NPEAVCRFWorldDatabase_1402C1320.cpp" />
    <ClCompile Include="Source\InsertChangeClassLogAfterInitClassCRFWorldDatabase_1404A0E90.cpp" />
    <ClCompile Include="Source\InsertCharacDataCCheckSumCharacAccountTrunkDataIEA_1402C0CC0.cpp" />
    <ClCompile Include="Source\InsertDefalutRecordCUnmannedTraderControllerIEAA_N_140350610.cpp" />
    <ClCompile Include="Source\InsertDefaultPSRecordCPostSystemManagerQEAA_NXZ_140325190.cpp" />
    <ClCompile Include="Source\InsertDefaultWeeklyPvpPointSumRecordCWeeklyGuildRa_1402CE0E0.cpp" />
    <ClCompile Include="Source\InsertItem_PCBANG_FAVOR_ITEM_DB_BASEQEAA_NAEAU_db__14040CB90.cpp" />
    <ClCompile Include="Source\InsertLinkLendItemMngQEAA_NGEPEAU_db_con_STORAGE_L_14030DC70.cpp" />
    <ClCompile Include="Source\InsertLinkLendItemSheetAEAA_NEPEAU_db_con_STORAGE__14030EFD0.cpp" />
    <ClCompile Include="Source\InsertListTimeLimitJadeMngQEAA_NGPEAU_db_con_STORA_1403FABD0.cpp" />
    <ClCompile Include="Source\InsertNotEnoughLimitItemRecordCItemStoreManagerQEA_14034A020.cpp" />
    <ClCompile Include="Source\InsertRenamePotionCPotionMgrQEAA_NPEAVCRFWorldData_14039F6F0.cpp" />
    <ClCompile Include="Source\InsertSettlementOwnerCWeeklyGuildRankManagerQEAA_N_1402CCE80.cpp" />
    <ClCompile Include="Source\InsertStateRecordCUnmannedTraderControllerIEAA_NXZ_140350320.cpp" />
    <ClCompile Include="Source\InsertTrunkDataCCheckSumCharacAccountTrunkDataIEAA_1402C0D20.cpp" />
    <ClCompile Include="Source\InsertUseListTimeLimitJadeAEAA_NPEAU_db_con_STORAG_1403FA7D0.cpp" />
    <ClCompile Include="Source\insertvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTy_1402C52D0.cpp" />
    <ClCompile Include="Source\InsertWaitListTimeLimitJadeAEAA_NPEAU_db_con_STORA_1403FA640.cpp" />
    <ClCompile Include="Source\Insert_AccountTrunkCRFWorldDatabaseQEAA_NKZ_14048E0A0.cpp" />
    <ClCompile Include="Source\Insert_AccountTrunkExtendCRFWorldDatabaseQEAA_NKZ_14048E150.cpp" />
    <ClCompile Include="Source\insert_amine_newownerCRFWorldDatabaseQEAA_NEEKZ_1404A9280.cpp" />
    <ClCompile Include="Source\insert_amine_personalCRFWorldDatabaseQEAA_NKZ_1404AA7E0.cpp" />
    <ClCompile Include="Source\Insert_AnimusDataCRFWorldDatabaseQEAA_NKPEANZ_1404A0130.cpp" />
    <ClCompile Include="Source\Insert_AnimusLogCRFWorldDatabaseQEAA_NKPEADENNZ_1404A0370.cpp" />
    <ClCompile Include="Source\insert_atrade_taxrateCRFWorldDatabaseQEAA_NEKPEADK_1404A8650.cpp" />
    <ClCompile Include="Source\Insert_Avator_CompleteCMainThreadQEAAXPEAU_DB_QRY__1401F3730.cpp" />
    <ClCompile Include="Source\Insert_BossCryRecordCRFWorldDatabaseQEAA_NKZ_1404BB810.cpp" />
    <ClCompile Include="Source\Insert_BuddyCRFWorldDatabaseQEAA_NKZ_14049E100.cpp" />
    <ClCompile Include="Source\Insert_CashLimSaleCRFWorldDatabaseQEAA_NXZ_1404C6BF0.cpp" />
    <ClCompile Include="Source\Insert_Char_CompleteCUserDBQEAAXEPEAU_REGED_AVATOR_1401120E0.cpp" />
    <ClCompile Include="Source\Insert_Char_RequestCUserDBQEAA_NPEADEE0KZ_140111C90.cpp" />
    <ClCompile Include="Source\Insert_DefaultWeeklyGuildPvpPointSumRecordCRFWorld_1404A6EB0.cpp" />
    <ClCompile Include="Source\Insert_Economy_HistoryCRFWorldDatabaseQEAA_NKPEAU__140493810.cpp" />
    <ClCompile Include="Source\Insert_GoldenBoxItemCRFWorldDatabaseQEAA_NXZ_1404C9840.cpp" />
    <ClCompile Include="Source\Insert_GreetingRecordCRFWorldDatabaseQEAA_NHPEAD0Z_14049BF70.cpp" />
    <ClCompile Include="Source\Insert_GuidRoomCRFWorldDatabaseQEAA_NKEEZ_1404B07C0.cpp" />
    <ClCompile Include="Source\Insert_GuildBatlleResultLogBattelInfoCRFWorldDatab_1404B0C10.cpp" />
    <ClCompile Include="Source\Insert_GuildBatlleResultLogCRFWorldDatabaseQEAA_NP_1404B0A20.cpp" />
    <ClCompile Include="Source\Insert_GuildCRFWorldDatabaseQEAA_NPEADEZ_1404989A0.cpp" />
    <ClCompile Include="Source\Insert_GuildMoneyHistoryCRFWorldDatabaseQEAA_NKNNN_14049AEF0.cpp" />
    <ClCompile Include="Source\Insert_ItemChargeInGameCRFWorldDatabaseQEAA_NKK_KK_1404988C0.cpp" />
    <ClCompile Include="Source\Insert_ItemCombineExCRFWorldDatabaseQEAA_NKZ_1404A4950.cpp" />
    <ClCompile Include="Source\insert_iteminfoCRFDBItemLogQEAA_NPEAU_LTD_ITEMINFO_1404859A0.cpp" />
    <ClCompile Include="Source\Insert_LimitItemRecordCRFWorldDatabaseQEAA_NPEAKZ_1404BAF90.cpp" />
    <ClCompile Include="Source\insert_ltdCRFDBItemLogQEAA_NPEAU_LTDZ_140485810.cpp" />
    <ClCompile Include="Source\Insert_MacroDataCRFWorldDatabaseQEAA_NKZ_1404A5140.cpp" />
    <ClCompile Include="Source\Insert_NpcDataCRFWorldDatabaseQEAA_NKPEAKZ_14049EF50.cpp" />
    <ClCompile Include="Source\Insert_NpcDataCRFWorldDatabaseQEAA_NKZ_14049EEA0.cpp" />
    <ClCompile Include="Source\Insert_NpcLogCRFWorldDatabaseQEAA_NKPEADEKKZ_14049F4C0.cpp" />
    <ClCompile Include="Source\Insert_NpcQuest_HistoryCRFWorldDatabaseQEAA_NKZ_1404C3270.cpp" />
    <ClCompile Include="Source\Insert_OreCuttingCRFWorldDatabaseQEAA_NKZ_1404C5D10.cpp" />
    <ClCompile Include="Source\Insert_OreReset_LogCRFWorldDatabaseQEAA_NEHKKZ_1404C9900.cpp" />
    <ClCompile Include="Source\Insert_PatriarchCommCRFWorldDatabaseQEAAEKKPEADZ_1404BFF10.cpp" />
    <ClCompile Include="Source\Insert_PcBangFavorItemCRFWorldDatabaseQEAA_NKZ_1404C66C0.cpp" />
    <ClCompile Include="Source\Insert_PostStorageRecordCRFWorldDatabaseQEAA_NXZ_1404B2B10.cpp" />
    <ClCompile Include="Source\Insert_PotionDelayCRFWorldDatabaseQEAA_NKZ_1404C5730.cpp" />
    <ClCompile Include="Source\Insert_PrimiumPlayTimeCRFWorldDatabaseQEAA_NKZ_1404C50B0.cpp" />
    <ClCompile Include="Source\Insert_PSDefaultRecordCRFWorldDatabaseQEAA_NKZ_1404B1BF0.cpp" />
    <ClCompile Include="Source\Insert_PvpOrderViewInfoCRFWorldDatabaseQEAA_NKZ_1404C2260.cpp" />
    <ClCompile Include="Source\Insert_PvpPointGuildRankDataCRFWorldDatabaseQEAA_N_1404A6DF0.cpp" />
    <ClCompile Include="Source\Insert_PvpPointLimitInfoRecordCRFWorldDatabaseQEAA_1404B01A0.cpp" />
    <ClCompile Include="Source\Insert_QuestCRFWorldDatabaseQEAA_NKZ_1404950D0.cpp" />
    <ClCompile Include="Source\Insert_RenamePotionLogCRFWorldDatabaseQEAA_NKPEAD0_1404C5DD0.cpp" />
    <ClCompile Include="Source\Insert_RFEvent_ClassRefineCRFWorldDatabaseQEAA_NKZ_1404B4CA0.cpp" />
    <ClCompile Include="Source\Insert_SettlementOwnerLogCRFWorldDatabaseQEAA_NEEK_1404B0250.cpp" />
    <ClCompile Include="Source\Insert_Set_Limit_RunCRFWorldDatabaseQEAA_NPEAEHZ_1404C8A10.cpp" />
    <ClCompile Include="Source\Insert_SFDelayInfoCRFWorldDatabaseQEAA_NKPEAU_worl_1404C2310.cpp" />
    <ClCompile Include="Source\Insert_Start_NpcQuest_HistoryCRFWorldDatabaseQEAA__1404C3760.cpp" />
    <ClCompile Include="Source\Insert_SupplementCRFWorldDatabaseQEAA_NKZ_1404C4BA0.cpp" />
    <ClCompile Include="Source\Insert_UnitCRFWorldDatabaseQEAA_NKZ_1404901B0.cpp" />
    <ClCompile Include="Source\Insert_UnitDataCRFWorldDatabaseQEAA_NKPEANZ_1404A0890.cpp" />
    <ClCompile Include="Source\Insert_UnitLogCRFWorldDatabaseQEAA_NKPEADENNZ_1404A0A30.cpp" />
    <ClCompile Include="Source\Insert_UnmannedTraderItemStateRecordCRFWorldDataba_1404AB4A0.cpp" />
    <ClCompile Include="Source\Insert_UnmannedTraderSingleDefaultRecordCRFWorldDa_1404AB9E0.cpp" />
    <ClCompile Include="Source\Insert_UserInterfaceCRFWorldDatabaseQEAA_NKZ_1404982E0.cpp" />
    <ClCompile Include="Source\Insert_UserNum_LogCRFWorldDatabaseQEAA_NHHZ_140493E60.cpp" />
    <ClCompile Include="Source\Insert_WeeklyGuildPvpPointSumCRFWorldDatabaseQEAA__1404A6F60.cpp" />
    <ClCompile Include="Source\InstanceCLogTypeDBTaskManagerSAPEAV1XZ_1402C2B90.cpp" />
    <ClCompile Include="Source\InstanceCTSingletonVCCashDBWorkManagerSAPEAVCCashD_1401C7780.cpp" />
    <ClCompile Include="Source\InstanceCUnmannedTraderGroupItemInfoTableSAPEAV1XZ_14036B0F0.cpp" />
    <ClCompile Include="Source\InstanceCUnmannedTraderUserInfoTableSAPEAV1XZ_1403636C0.cpp" />
    <ClCompile Include="Source\IsCashDBDSNSettedCNationSettingDataQEAA_NXZ_1402F2D00.cpp" />
    <ClCompile Include="Source\IsCashDBDSNSettedCNationSettingManagerQEAA_NXZ_1402F2CB0.cpp" />
    <ClCompile Include="Source\IsCashDBInitCNationSettingDataQEAA_NXZ_1402F26A0.cpp" />
    <ClCompile Include="Source\IsCashDBInitCNationSettingManagerQEAA_NXZ_1402F2650.cpp" />
    <ClCompile Include="Source\IsCashDBUseExtRefCNationSettingDataQEAA_NXZ_1402F3680.cpp" />
    <ClCompile Include="Source\IsCashDBUseExtRefCNationSettingManagerQEAA_NXZ_1402F3630.cpp" />
    <ClCompile Include="Source\IsCombineData_ITEMCOMBINE_DB_BASEQEAA_NXZ_1402AF610.cpp" />
    <ClCompile Include="Source\IsConectionActiveCRFNewDatabaseQEAA_NXZ_1401C7760.cpp" />
    <ClCompile Include="Source\IsContPushBeforeCUserDBQEAAPEAU_AVATOR_DATAXZ_140110EC0.cpp" />
    <ClCompile Include="Source\IsConvertableToLongIntegerCryptoPPQEBA_NXZ_1405E4760.cpp" />
    <ClCompile Include="Source\IsDBCSLeadByteEx_0_1404DEDD2.cpp" />
    <ClCompile Include="Source\IsDbUpdateRFEventBaseUEAA_NKZ_140329500.cpp" />
    <ClCompile Include="Source\IsDbUpdateRFEvent_ClassRefineUEAA_NKZ_140329600.cpp" />
    <ClCompile Include="Source\IsDeleteItem_PCBANG_FAVOR_ITEM_DB_BASEQEAA_NAEAU_d_14040CC30.cpp" />
    <ClCompile Include="Source\IsEmpty_LIST_TRADE_DB_BASEQEAA_NXZ_14035FDF0.cpp" />
    <ClCompile Include="Source\IsExistGroupIDCUnmannedTraderClassInfoTableTypeUEA_14037D300.cpp" />
    <ClCompile Include="Source\IsExistGroupIDCUnmannedTraderGroupItemInfoTableQEA_140360A40.cpp" />
    <ClCompile Include="Source\IsFilled_LIST_BUDDY_DB_BASEQEAA_NXZ_140075110.cpp" />
    <ClCompile Include="Source\IsFilled_LIST_SFCONT_DB_BASEQEAA_NXZ_14007A500.cpp" />
    <ClCompile Include="Source\IsInitializedCLogTypeDBTaskManagerQEAA_NXZ_140205810.cpp" />
    <ClCompile Include="Source\IsInitializedDL_FixedBasePrecomputationImplUEC2NPo_140556130.cpp" />
    <ClCompile Include="Source\IsInitializedDL_FixedBasePrecomputationImplUECPPoi_14044EA50.cpp" />
    <ClCompile Include="Source\IsInitializedDL_FixedBasePrecomputationImplVIntege_1405515F0.cpp" />
    <ClCompile Include="Source\IsNULLCashDbWorkerQEAA_NXZ_1402EEB10.cpp" />
    <ClCompile Include="Source\IsRangePerMastery_STAT_DB_BASESA_NEEZ_14007C660.cpp" />
    <ClCompile Include="Source\IsReturnPostUpdateCUserDBQEAA_NXZ_140117A50.cpp" />
    <ClCompile Include="Source\IsSQLValidStringYA_NPEBDZ_1400408A0.cpp" />
    <ClCompile Include="Source\IsTableOpenCRecordDataQEAA_NXZ_140204B60.cpp" />
    <ClCompile Include="Source\IsValidIDCUnmannedTraderClassInfoTableTypeIEAA_NKZ_14037DA00.cpp" />
    <ClCompile Include="Source\IsVotable_suggested_matterQEAA_NKZ_1400AD200.cpp" />
    <ClCompile Include="Source\is_private_itemAP_BatterySlotAEAA_NPEAU_db_con_STO_1402D9C70.cpp" />
    <ClCompile Include="Source\j_0allocatorPEAVCLogTypeDBTaskstdQEAAAEBV01Z_14000B744.cpp" />
    <ClCompile Include="Source\j_0allocatorPEAVCLogTypeDBTaskstdQEAAXZ_140001CC1.cpp" />
    <ClCompile Include="Source\j_0CashDbWorkerQEAAXZ_14000E098.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerBRQEAAXZ_140010D0C.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerCNQEAAXZ_140011CBB.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerESQEAAXZ_140011CB6.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerGBQEAAXZ_140006B86.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerIDQEAAXZ_140009CE6.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerJPQEAAXZ_14001389A.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerKRQEAAXZ_140011CC0.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerNULLQEAAXZ_14000FCBD.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerPHQEAAXZ_14000B960.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerRUQEAAXZ_14000F88A.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerTHQEAAXZ_14000DF58.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerTWQEAAXZ_1400095D9.cpp" />
    <ClCompile Include="Source\j_0CCashDbWorkerUSQEAAXZ_140007D51.cpp" />
    <ClCompile Include="Source\j_0CCashDBWorkManagerAEAAXZ_140013D9A.cpp" />
    <ClCompile Include="Source\j_0CDummyPosTableQEAAXZ_14000C4C3.cpp" />
    <ClCompile Include="Source\j_0CellLendItemSheetQEAAEPEAU_db_con_STORAGE_LISTZ_14000E07A.cpp" />
    <ClCompile Include="Source\j_0CEnglandBillingMgrQEAAXZ_1400029BE.cpp" />
    <ClCompile Include="Source\j_0CEventLootTableQEAAXZ_14000DA99.cpp" />
    <ClCompile Include="Source\j_0CItemLootTableQEAAXZ_140011B35.cpp" />
    <ClCompile Include="Source\j_0CItemUpgradeTableQEAAXZ_140007932.cpp" />
    <ClCompile Include="Source\j_0CLogTypeDBTaskManagerIEAAXZ_14000A8C1.cpp" />
    <ClCompile Include="Source\j_0CLogTypeDBTaskPoolQEAAXZ_1400020A4.cpp" />
    <ClCompile Include="Source\j_0CLogTypeDBTaskQEAAXZ_140009EFD.cpp" />
    <ClCompile Include="Source\j_0CMapDataTableQEAAXZ_14000A574.cpp" />
    <ClCompile Include="Source\j_0CMonsterSPGroupTableQEAAXZ_140006AFA.cpp" />
    <ClCompile Include="Source\j_0CNationCodeStrTableQEAAXZ_14000D332.cpp" />
    <ClCompile Include="Source\j_0COreCuttingTableQEAAXZ_140010F41.cpp" />
    <ClCompile Include="Source\j_0CRecordDataQEAAXZ_140004D22.cpp" />
    <ClCompile Include="Source\j_0CRFDBItemLogQEAAKZ_140004EC6.cpp" />
    <ClCompile Include="Source\j_0CRFNewDatabaseQEAAXZ_140008404.cpp" />
    <ClCompile Include="Source\j_0CRFWorldDatabaseQEAAXZ_14001082A.cpp" />
    <ClCompile Include="Source\j_0CTotalGuildRankRecordQEAAXZ_14000BE1F.cpp" />
    <ClCompile Include="Source\j_0CTSingletonVCCashDBWorkManagerIEAAXZ_140001A73.cpp" />
    <ClCompile Include="Source\j_0CUnmannedTraderClassInfoTableCodeTypeQEAAKZ_140006CD5.cpp" />
    <ClCompile Include="Source\j_0CUnmannedTraderClassInfoTableTypeQEAAKZ_14000A7A9.cpp" />
    <ClCompile Include="Source\j_0CUnmannedTraderGroupItemInfoTableIEAAXZ_14000E81D.cpp" />
    <ClCompile Include="Source\j_0CUnmannedTraderUserInfoTableIEAAXZ_14000CA45.cpp" />
    <ClCompile Include="Source\j_0CUserDBQEAAXZ_140010E83.cpp" />
    <ClCompile Include="Source\j_0CWeeklyGuildRankRecordQEAAXZ_1400042D2.cpp" />
    <ClCompile Include="Source\j_0DL_FixedBasePrecomputationImplUECPPointCryptoPP_140012125.cpp" />
    <ClCompile Include="Source\j_0DL_FixedBasePrecomputationUECPPointCryptoPPCryp_140006E5B.cpp" />
    <ClCompile Include="Source\j_0DL_GroupParametersImplVEcPrecomputationVECPCryp_140009926.cpp" />
    <ClCompile Include="Source\j_0GeneratableCryptoMaterialCryptoPPQEAAXZ_14000749B.cpp" />
    <ClCompile Include="Source\j_0tablelua_tinkerQEAAAEBU01Z_1400016A9.cpp" />
    <ClCompile Include="Source\j_0tablelua_tinkerQEAAPEAUlua_StateHZ_140007996.cpp" />
    <ClCompile Include="Source\j_0tablelua_tinkerQEAAPEAUlua_StatePEBDZ_14000547A.cpp" />
    <ClCompile Include="Source\j_0tablelua_tinkerQEAAPEAUlua_StateZ_140011D7E.cpp" />
    <ClCompile Include="Source\j_0table_objlua_tinkerQEAAPEAUlua_StateHZ_14000ED4F.cpp" />
    <ClCompile Include="Source\j_0UseCellTimeLimitJadeQEAAPEAU_db_con_STORAGE_LIS_140001F28.cpp" />
    <ClCompile Include="Source\j_0UseCellTimeLimitJadeQEAAPEAU_db_con_STORAGE_LIS_14000C0B8.cpp" />
    <ClCompile Include="Source\j_0vectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTypeD_1400103C0.cpp" />
    <ClCompile Include="Source\j_0WaitableCryptoPPQEAAXZ_14000E7F0.cpp" />
    <ClCompile Include="Source\j_0WaitCellTimeLimitJadeQEAAPEAU_db_con_STORAGE_LI_1400082DD.cpp" />
    <ClCompile Include="Source\j_0WaitCellTimeLimitJadeQEAAPEAU_db_con_STORAGE_LI_14000D936.cpp" />
    <ClCompile Include="Source\j_0_100_per_random_tableQEAAXZ_140002086.cpp" />
    <ClCompile Include="Source\j_0_ANIMUS_DB_BASEQEAAXZ_140010596.cpp" />
    <ClCompile Include="Source\j_0_animus_db_loadQEAAXZ_14000E7AA.cpp" />
    <ClCompile Include="Source\j_0_AVATOR_DB_BASEQEAAXZ_140009138.cpp" />
    <ClCompile Include="Source\j_0_bag_db_loadQEAAXZ_14000AC13.cpp" />
    <ClCompile Include="Source\j_0_BUDDY_DB_BASEQEAAXZ_14000ACC2.cpp" />
    <ClCompile Include="Source\j_0_CRYMSG_DB_BASEQEAAXZ_14000638E.cpp" />
    <ClCompile Include="Source\j_0_CUTTING_DB_BASEQEAAXZ_140003341.cpp" />
    <ClCompile Include="Source\j_0_db_con_STORAGE_LISTQEAAXZ_140013DC2.cpp" />
    <ClCompile Include="Source\j_0_db_golden_box_itemQEAAXZ_14000A696.cpp" />
    <ClCompile Include="Source\j_0_DB_LOAD_AUTOMINE_MACHINEQEAAXZ_14000F7EA.cpp" />
    <ClCompile Include="Source\j_0_DB_QRY_SYN_DATAQEAAXZ_14000EFA7.cpp" />
    <ClCompile Include="Source\j_0_DELPOST_DB_BASEQEAAXZ_1400028F6.cpp" />
    <ClCompile Include="Source\j_0_eff_list_worlddb_sf_delay_infoQEAAXZ_140003B9D.cpp" />
    <ClCompile Include="Source\j_0_embellish_db_loadQEAAXZ_140007BB2.cpp" />
    <ClCompile Include="Source\j_0_EMBELLISH_LIST_EQUIP_DB_BASEQEAAXZ_140007A6D.cpp" />
    <ClCompile Include="Source\j_0_EQUIP_DB_BASEQEAAXZ_14000F790.cpp" />
    <ClCompile Include="Source\j_0_equip_db_loadQEAAXZ_140004BF1.cpp" />
    <ClCompile Include="Source\j_0_event_dropCEventLootTableQEAAXZ_14000D319.cpp" />
    <ClCompile Include="Source\j_0_Exttrunk_db_loadQEAAXZ_1400013CF.cpp" />
    <ClCompile Include="Source\j_0_FORCE_DB_BASEQEAAXZ_14000930E.cpp" />
    <ClCompile Include="Source\j_0_force_db_loadQEAAXZ_1400123F5.cpp" />
    <ClCompile Include="Source\j_0_goldbox_indexQEAAXZ_14000AF88.cpp" />
    <ClCompile Include="Source\j_0_INVEN_DB_BASEQEAAXZ_140010465.cpp" />
    <ClCompile Include="Source\j_0_ITEMCOMBINE_DB_BASEQEAAXZ_1400110C7.cpp" />
    <ClCompile Include="Source\j_0_limit_item_db_dataQEAAXZ_1400048EF.cpp" />
    <ClCompile Include="Source\j_0_LINK_DB_BASEQEAAXZ_1400137D2.cpp" />
    <ClCompile Include="Source\j_0_LIST_ANIMUS_DB_BASEQEAAXZ_14000364D.cpp" />
    <ClCompile Include="Source\j_0_LIST_BUDDY_DB_BASEQEAAXZ_140002B4E.cpp" />
    <ClCompile Include="Source\j_0_LIST_CRYMSG_DB_BASEQEAAXZ_140005ABF.cpp" />
    <ClCompile Include="Source\j_0_LIST_CUTTING_DB_BASEQEAAXZ_14000BCFD.cpp" />
    <ClCompile Include="Source\j_0_LIST_FORCE_DB_BASEQEAAXZ_14000F1F5.cpp" />
    <ClCompile Include="Source\j_0_LIST_INVEN_DB_BASEQEAAXZ_14000399A.cpp" />
    <ClCompile Include="Source\j_0_LIST_ITEMCOMBINE_DB_BASEQEAAXZ_140013868.cpp" />
    <ClCompile Include="Source\j_0_LIST_LINK_DB_BASEQEAAXZ_140012436.cpp" />
    <ClCompile Include="Source\j_0_LIST_PERSONALAMINE_INVEN_DB_BASEQEAAXZ_140013FE8.cpp" />
    <ClCompile Include="Source\j_0_LIST_QUEST_DB_BASEQEAAXZ_140004AAC.cpp" />
    <ClCompile Include="Source\j_0_LIST_TRADE_DB_BASEQEAAXZ_140008599.cpp" />
    <ClCompile Include="Source\j_0_LIST_TRUNK_DB_BASEQEAAXZ_1400085D0.cpp" />
    <ClCompile Include="Source\j_0_LIST_UNIT_DB_BASEQEAAXZ_1400091A1.cpp" />
    <ClCompile Include="Source\j_0_mas_list_worlddb_sf_delay_infoQEAAXZ_140012A12.cpp" />
    <ClCompile Include="Source\j_0_NOT_ARRANGED_AVATOR_DBQEAAXZ_140006483.cpp" />
    <ClCompile Include="Source\j_0_NPC_QUEST_HISTORY_QUEST_DB_BASEQEAAXZ_140009C14.cpp" />
    <ClCompile Include="Source\j_0_ore_cut_listCOreCuttingTableQEAAXZ_140005DC1.cpp" />
    <ClCompile Include="Source\j_0_PERSONALAMINE_INVEN_DB_BASEQEAAXZ_1400100C3.cpp" />
    <ClCompile Include="Source\j_0_personal_amine_inven_db_loadQEAAXZ_14000EF57.cpp" />
    <ClCompile Include="Source\j_0_POSTDATA_DB_BASEQEAAXZ_140011FA9.cpp" />
    <ClCompile Include="Source\j_0_POSTSTORAGE_DB_BASEQEAAXZ_14000E5A2.cpp" />
    <ClCompile Include="Source\j_0_POTION_NEXT_USE_TIME_DB_BASEQEAAXZ_14000FEB1.cpp" />
    <ClCompile Include="Source\j_0_pt_query_appoint_zoclQEAAXZ_140001B7C.cpp" />
    <ClCompile Include="Source\j_0_PVPPOINT_LIMIT_DB_BASEQEAAXZ_140001B95.cpp" />
    <ClCompile Include="Source\j_0_QUEST_DB_BASEQEAAXZ_14000403E.cpp" />
    <ClCompile Include="Source\j_0_RanitPEAVCLogTypeDBTask_JPEBQEAV1AEBQEAV1stdQE_14000598E.cpp" />
    <ClCompile Include="Source\j_0_RanitPEAVCLogTypeDBTask_JPEBQEAV1AEBQEAV1stdQE_140011B94.cpp" />
    <ClCompile Include="Source\j_0_record_bin_headerQEAAXZ_1400052A9.cpp" />
    <ClCompile Include="Source\j_0_REGED_AVATOR_DBQEAAXZ_140011720.cpp" />
    <ClCompile Include="Source\j_0_RETURNPOST_DB_BASEQEAAXZ_14000EB1F.cpp" />
    <ClCompile Include="Source\j_0_SFCONT_DB_BASEQEAAXZ_140001BCC.cpp" />
    <ClCompile Include="Source\j_0_START_NPC_QUEST_HISTORY_QUEST_DB_BASEQEAAXZ_14000D0CB.cpp" />
    <ClCompile Include="Source\j_0_STAT_DB_BASEQEAAXZ_140012283.cpp" />
    <ClCompile Include="Source\j_0_SUPPLEMENT_DB_BASEQEAAXZ_140004F70.cpp" />
    <ClCompile Include="Source\j_0_TRADE_DB_BASEQEAAXZ_14000C39C.cpp" />
    <ClCompile Include="Source\j_0_TRUNK_DB_BASEQEAAXZ_140002F4F.cpp" />
    <ClCompile Include="Source\j_0_trunk_db_loadQEAAXZ_14000561E.cpp" />
    <ClCompile Include="Source\j_0_UNIT_DB_BASEQEAAXZ_140007072.cpp" />
    <ClCompile Include="Source\j_0_Vector_const_iteratorPEAVCLogTypeDBTaskValloca_14000D909.cpp" />
    <ClCompile Include="Source\j_0_Vector_const_iteratorPEAVCLogTypeDBTaskValloca_140011545.cpp" />
    <ClCompile Include="Source\j_0_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEA_140001938.cpp" />
    <ClCompile Include="Source\j_0_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEA_14000B5D7.cpp" />
    <ClCompile Include="Source\j_0_Vector_valPEAVCLogTypeDBTaskVallocatorPEAVCLog_140002D79.cpp" />
    <ClCompile Include="Source\j_0_worlddb_arrange_char_infoQEAAXZ_140013B65.cpp" />
    <ClCompile Include="Source\j_0_worlddb_npc_quest_complete_historyQEAAXZ_14000BA23.cpp" />
    <ClCompile Include="Source\j_0_worlddb_potion_delay_infoQEAAXZ_14000AE70.cpp" />
    <ClCompile Include="Source\j_0_worlddb_sf_delay_infoQEAAXZ_1400129CC.cpp" />
    <ClCompile Include="Source\j_0__list_worlddb_npc_quest_complete_historyQEAAXZ_140004F16.cpp" />
    <ClCompile Include="Source\j_0__list_worlddb_start_npc_quest_complete_history_140001695.cpp" />
    <ClCompile Include="Source\j_1CashDbWorkerUEAAXZ_1400066AE.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerBRUEAAXZ_14001357F.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerCNUEAAXZ_140005F47.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerESUEAAXZ_14001308E.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerGBUEAAXZ_140002C9D.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerIDUEAAXZ_14000C7ED.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerJPUEAAXZ_140011838.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerKRUEAAXZ_140007455.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerNULLUEAAXZ_1400086E3.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerPHUEAAXZ_140005E6B.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerRUUEAAXZ_140001CB7.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerTHUEAAXZ_140005D5D.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerTWUEAAXZ_140005993.cpp" />
    <ClCompile Include="Source\j_1CCashDbWorkerUSUEAAXZ_14000B2EE.cpp" />
    <ClCompile Include="Source\j_1CCashDBWorkManagerEEAAXZ_140008CBF.cpp" />
    <ClCompile Include="Source\j_1CDummyPosTableUEAAXZ_1400085A3.cpp" />
    <ClCompile Include="Source\j_1CEnglandBillingMgrQEAAXZ_140005213.cpp" />
    <ClCompile Include="Source\j_1CEventLootTableUEAAXZ_1400083A5.cpp" />
    <ClCompile Include="Source\j_1CItemLootTableUEAAXZ_140002C34.cpp" />
    <ClCompile Include="Source\j_1CItemUpgradeTableUEAAXZ_14000EFC0.cpp" />
    <ClCompile Include="Source\j_1CLogTypeDBTaskManagerIEAAXZ_14000E27D.cpp" />
    <ClCompile Include="Source\j_1CLogTypeDBTaskPoolQEAAXZ_140010E47.cpp" />
    <ClCompile Include="Source\j_1CLogTypeDBTaskQEAAXZ_140009575.cpp" />
    <ClCompile Include="Source\j_1CMapDataTableUEAAXZ_140008477.cpp" />
    <ClCompile Include="Source\j_1CMonsterSPGroupTableQEAAXZ_140013EDA.cpp" />
    <ClCompile Include="Source\j_1CNationCodeStrTableQEAAXZ_1400086CA.cpp" />
    <ClCompile Include="Source\j_1COreCuttingTableUEAAXZ_14000BE01.cpp" />
    <ClCompile Include="Source\j_1CRecordDataUEAAXZ_140006AD2.cpp" />
    <ClCompile Include="Source\j_1CRFDBItemLogUEAAXZ_140005FE2.cpp" />
    <ClCompile Include="Source\j_1CRFNewDatabaseUEAAXZ_14000DE9F.cpp" />
    <ClCompile Include="Source\j_1CRFWorldDatabaseUEAAXZ_14000599D.cpp" />
    <ClCompile Include="Source\j_1CTotalGuildRankRecordQEAAXZ_140013147.cpp" />
    <ClCompile Include="Source\j_1CTSingletonVCCashDBWorkManagerMEAAXZ_14000212B.cpp" />
    <ClCompile Include="Source\j_1CUnmannedTraderClassInfoTableCodeTypeQEAAXZ_140003125.cpp" />
    <ClCompile Include="Source\j_1CUnmannedTraderClassInfoTableTypeQEAAXZ_14001140F.cpp" />
    <ClCompile Include="Source\j_1CUnmannedTraderGroupItemInfoTableIEAAXZ_1400052EA.cpp" />
    <ClCompile Include="Source\j_1CUnmannedTraderUserInfoTableIEAAXZ_14001097E.cpp" />
    <ClCompile Include="Source\j_1CUserDBUEAAXZ_14000FEF7.cpp" />
    <ClCompile Include="Source\j_1CWeeklyGuildRankRecordQEAAXZ_140013F61.cpp" />
    <ClCompile Include="Source\j_1DL_FixedBasePrecomputationImplUECPPointCryptoPP_1400096BF.cpp" />
    <ClCompile Include="Source\j_1DL_GroupParametersImplVEcPrecomputationVECPCryp_14000357B.cpp" />
    <ClCompile Include="Source\j_1GeneratableCryptoMaterialCryptoPPUEAAXZ_14000C5DB.cpp" />
    <ClCompile Include="Source\j_1tablelua_tinkerQEAAXZ_14000278E.cpp" />
    <ClCompile Include="Source\j_1table_objlua_tinkerQEAAXZ_1400136D3.cpp" />
    <ClCompile Include="Source\j_1vectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTypeD_14000BC6C.cpp" />
    <ClCompile Include="Source\j_1WaitableCryptoPPUEAAXZ_140007289.cpp" />
    <ClCompile Include="Source\j_1_PVPPOINT_LIMIT_DB_BASEQEAAXZ_140009A9D.cpp" />
    <ClCompile Include="Source\j_1_RanitPEAVCLogTypeDBTask_JPEBQEAV1AEBQEAV1stdQE_140012891.cpp" />
    <ClCompile Include="Source\j_1_Vector_const_iteratorPEAVCLogTypeDBTaskValloca_14000AEDE.cpp" />
    <ClCompile Include="Source\j_1_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEA_14000D65C.cpp" />
    <ClCompile Include="Source\j_4CUnmannedTraderClassInfoTableCodeTypeQEAAAEBV0A_1400135DE.cpp" />
    <ClCompile Include="Source\j_4CUnmannedTraderClassInfoTableTypeQEAAAEBVCUnman_1400045A7.cpp" />
    <ClCompile Include="Source\j_4DL_FixedBasePrecomputationImplUECPPointCryptoPP_140001F5F.cpp" />
    <ClCompile Include="Source\j_4DL_FixedBasePrecomputationUECPPointCryptoPPCryp_14000491C.cpp" />
    <ClCompile Include="Source\j_4DL_GroupParametersImplVEcPrecomputationVECPCryp_140006564.cpp" />
    <ClCompile Include="Source\j_4GeneratableCryptoMaterialCryptoPPQEAAAEAV01AEBV_140008846.cpp" />
    <ClCompile Include="Source\j_4_INVEN_DB_BASEQEAAAEAU0AEAU0Z_140008D0F.cpp" />
    <ClCompile Include="Source\j_4_LIST_INVEN_DB_BASEQEAAAEAU01AEAU01Z_14000CE3C.cpp" />
    <ClCompile Include="Source\j_8_Vector_const_iteratorPEAVCLogTypeDBTaskValloca_1400064DD.cpp" />
    <ClCompile Include="Source\j_9_Vector_const_iteratorPEAVCLogTypeDBTaskValloca_140009C91.cpp" />
    <ClCompile Include="Source\j_AccessBasePrecomputationDL_GroupParametersImplVE_14000FEE3.cpp" />
    <ClCompile Include="Source\j_AccessBasePrecomputationDL_GroupParameters_ECVEC_1400112DE.cpp" />
    <ClCompile Include="Source\j_AccessPublicPrecomputationDL_PublicKeyImplVDL_Gr_1400088E6.cpp" />
    <ClCompile Include="Source\j_AddBagRequestCNetworkEXAEAA_NHPEADZ_14000134D.cpp" />
    <ClCompile Include="Source\j_AddRecordCEventLootTableQEAAXPEAU_event_drop1Z_14001035C.cpp" />
    <ClCompile Include="Source\j_add_char_completeCMgrAccountLobbyHistoryQEAAXEPE_14001201C.cpp" />
    <ClCompile Include="Source\j_Add_PvpPointCRFWorldDatabaseQEAA_NKKKZ_1400085B2.cpp" />
    <ClCompile Include="Source\j_add_storage_failCMgrAvatorItemHistoryQEAAXHPEAU__140012C6A.cpp" />
    <ClCompile Include="Source\j_AgreeWithEphemeralPrivateKeyDL_KeyAgreementAlgor_14000A6E1.cpp" />
    <ClCompile Include="Source\j_AliveCBillingIDUEAAXPEAVCUserDBZ_140007BA3.cpp" />
    <ClCompile Include="Source\j_AliveCBillingJPUEAAXPEAVCUserDBZ_1400063CA.cpp" />
    <ClCompile Include="Source\j_AliveCBillingManagerQEAAXPEAVCUserDBZ_1400038CD.cpp" />
    <ClCompile Include="Source\j_AliveCBillingNULLUEAAXPEAVCUserDBZ_14000A1C8.cpp" />
    <ClCompile Include="Source\j_AliveCBillingUEAAXPEAVCUserDBZ_14000B55F.cpp" />
    <ClCompile Include="Source\j_Alive_Char_CompleteCMainThreadQEAAXPEAU_DB_QRY_S_14000F1A5.cpp" />
    <ClCompile Include="Source\j_Alive_Char_CompleteCUserDBQEAAXEEKPEAU_REGEDZ_14000C897.cpp" />
    <ClCompile Include="Source\j_Alive_Char_RequestCUserDBQEAA_NEKPEADEZ_1400020DB.cpp" />
    <ClCompile Include="Source\j_allocateallocatorPEAVCLogTypeDBTaskstdQEAAPEAPEA_140010014.cpp" />
    <ClCompile Include="Source\j_AllocSelectHandleCRFNewDatabaseQEAA_NXZ_140009101.cpp" />
    <ClCompile Include="Source\j_AllocUpdateHandleCRFNewDatabaseQEAA_NXZ_140006375.cpp" />
    <ClCompile Include="Source\j_ApplyUpdatedBossInfoCPvpUserAndGuildRankingSyste_140005466.cpp" />
    <ClCompile Include="Source\j_auto_trade_buyCMgrAvatorItemHistoryQEAAXPEBDK0KP_140006F5A.cpp" />
    <ClCompile Include="Source\j_auto_trade_sellCMgrAvatorItemHistoryQEAAXPEBDK0K_140009D0E.cpp" />
    <ClCompile Include="Source\j_AvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTypeD_1400083DC.cpp" />
    <ClCompile Include="Source\j_backvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTy_1400022E8.cpp" />
    <ClCompile Include="Source\j_back_trap_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_14000FE66.cpp" />
    <ClCompile Include="Source\j_beginvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogT_140001A55.cpp" />
    <ClCompile Include="Source\j_BuyCUnmannedTraderUserInfoTableQEAAXGEPEAU_unman_1400013B6.cpp" />
    <ClCompile Include="Source\j_buy_unitCMgrAvatorItemHistoryQEAAXHEPEAU_LIST_UN_140003BA7.cpp" />
    <ClCompile Include="Source\j_CalcNewSerialNumber_db_con_STORAGE_LISTCAKXZ_14000A038.cpp" />
    <ClCompile Include="Source\j_CalcRadarDelayCUserDBQEAAXXZ_14000C595.cpp" />
    <ClCompile Include="Source\j_CallFunc_Item_BuyCEnglandBillingMgrQEAAHAEAU_par_14001365B.cpp" />
    <ClCompile Include="Source\j_CancelRegistCUnmannedTraderUserInfoTableQEAAXGEP_14000906B.cpp" />
    <ClCompile Include="Source\j_capacityvectorPEAVCLogTypeDBTaskVallocatorPEAVCL_140013BF1.cpp" />
    <ClCompile Include="Source\j_CashDBInitCMainThreadAEAA_NPEAD000KZ_1400050A1.cpp" />
    <ClCompile Include="Source\j_cash_item_useCMgrAvatorItemHistoryQEAAXHPEAU_db__14000272F.cpp" />
    <ClCompile Include="Source\j_CheatCancelRegistCUnmannedTraderUserInfoTableQEA_1400027AC.cpp" />
    <ClCompile Include="Source\j_cheat_add_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_140010FC3.cpp" />
    <ClCompile Include="Source\j_cheat_del_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_1400043D6.cpp" />
    <ClCompile Include="Source\j_cheat_make_item_no_materialCMgrAvatorItemHistory_14000DB6B.cpp" />
    <ClCompile Include="Source\j_CheckAndCreateTodayGuildRankTableCGuildRankingAE_14000A655.cpp" />
    <ClCompile Include="Source\j_CheckAndCreateTodayPvpRankTableCUserRankingProce_140006F3C.cpp" />
    <ClCompile Include="Source\j_CheckDBCSCompleteStringCNationSettingDataIEAA_NH_14000D981.cpp" />
    <ClCompile Include="Source\j_CheckDBItemStateCUnmannedTraderControllerIEAAEEK_140001735.cpp" />
    <ClCompile Include="Source\j_CheckDiffCCheckSumCharacAccountTrunkDataQEAAHPEA_140007595.cpp" />
    <ClCompile Include="Source\j_CheckDiffCCheckSumGuildDataQEAAHPEAVCRFWorldData_1400084F4.cpp" />
    <ClCompile Include="Source\j_CheckHeroesDummyCDummyPosTableSA_NPEAVCGameObjec_14000C2CF.cpp" />
    <ClCompile Include="Source\j_CheckLogFileHourCRFNewDatabaseQEAAXXZ_14000435E.cpp" />
    <ClCompile Include="Source\j_CheckMixItemCTalkCrystalCombineManagerIEAAEPEAU__14000542F.cpp" />
    <ClCompile Include="Source\j_CheckNPCQuestStartableCQuestMgrQEAAPEAU_happen_e_140011789.cpp" />
    <ClCompile Include="Source\j_CheckwIndexAndTypeCUnmannedTraderUserInfoTableAE_14000EAB1.cpp" />
    <ClCompile Include="Source\j_check_dbsyn_data_sizeCMainThreadAEAA_NXZ_14000416A.cpp" />
    <ClCompile Include="Source\j_Check_GuildMemberCountCRFWorldDatabaseQEAA_NKZ_140013E80.cpp" />
    <ClCompile Include="Source\j_check_machineAutominePersonalMgrQEAA_NHKPEAVAuto_140013BA6.cpp" />
    <ClCompile Include="Source\j_CleanUpCLogTypeDBTaskManagerAEAAXXZ_140006DCA.cpp" />
    <ClCompile Include="Source\j_CleanUpCUnmannedTraderClassInfoTableTypeIEAAXXZ_14000B8E8.cpp" />
    <ClCompile Include="Source\j_ClearBillingDataCUserDBQEAAXXZ_140012BE8.cpp" />
    <ClCompile Include="Source\j_ClearCLogTypeDBTaskQEAAXXZ_140010ABE.cpp" />
    <ClCompile Include="Source\j_ClearCTotalGuildRankRecordQEAAXXZ_14000C158.cpp" />
    <ClCompile Include="Source\j_ClearCWeeklyGuildRankRecordQEAAXXZ_1400074DC.cpp" />
    <ClCompile Include="Source\j_ClearDB_CombineResultItemCombineMgrQEAAEXZ_1400090F7.cpp" />
    <ClCompile Include="Source\j_ClearLogLogOutStateCUnmannedTraderUserInfoTableA_14000972D.cpp" />
    <ClCompile Include="Source\j_ClearRequestCUnmannedTraderUserInfoTableQEAAXGKZ_1400083D2.cpp" />
    <ClCompile Include="Source\j_clearvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogT_1400123B4.cpp" />
    <ClCompile Include="Source\j_Clear_guild_query_info_result_zoclQEAAXXZ_140010528.cpp" />
    <ClCompile Include="Source\j_Clear_LIST_TRADE_DB_BASEQEAAXXZ_140004F25.cpp" />
    <ClCompile Include="Source\j_Clear_TRADE_DB_BASEQEAAXXZ_140012661.cpp" />
    <ClCompile Include="Source\j_combine_ex_reward_itemCMgrAvatorItemHistoryQEAAX_1400071A3.cpp" />
    <ClCompile Include="Source\j_combine_ex_using_materialCMgrAvatorItemHistoryQE_140013AD9.cpp" />
    <ClCompile Include="Source\j_combine_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_c_14000AFB5.cpp" />
    <ClCompile Include="Source\j_CommitTransactionCRFNewDatabaseQEAA_NXZ_140001348.cpp" />
    <ClCompile Include="Source\j_CompleteBuyCUnmannedTraderUserInfoTableQEAAXEPEA_1400074AA.cpp" />
    <ClCompile Include="Source\j_CompleteCancelRegistCUnmannedTraderUserInfoTable_14000443A.cpp" />
    <ClCompile Include="Source\j_CompleteCreateCUnmannedTraderUserInfoTableQEAAXG_140004F4D.cpp" />
    <ClCompile Include="Source\j_CompleteInsertPatriarchPatriarchElectProcessorQE_14000D0F3.cpp" />
    <ClCompile Include="Source\j_CompleteItemChargeRefundPatriarchElectProcessorQ_14000754A.cpp" />
    <ClCompile Include="Source\j_CompleteRegistCUnmannedTraderUserInfoTableQEAAXE_14000EB33.cpp" />
    <ClCompile Include="Source\j_CompleteRepriceCUnmannedTraderUserInfoTableQEAAX_14000EA98.cpp" />
    <ClCompile Include="Source\j_CompleteRequestRefundPatriarchElectProcessorQEAA_140004854.cpp" />
    <ClCompile Include="Source\j_CompleteReRegistCUnmannedTraderUserInfoTableQEAA_1400012D5.cpp" />
    <ClCompile Include="Source\j_CompleteReRegistRollBackCUnmannedTraderUserInfoT_14000C9B9.cpp" />
    <ClCompile Include="Source\j_CompleteSearchCUnmannedTraderUserInfoTableQEAAXE_1400079FF.cpp" />
    <ClCompile Include="Source\j_CompleteTimeOutClearCUnmannedTraderUserInfoTable_140002423.cpp" />
    <ClCompile Include="Source\j_CompleteUpdateCheatRegistTimeCUnmannedTraderUser_1400011C7.cpp" />
    <ClCompile Include="Source\j_CompleteUpdateStateCUnmannedTraderUserInfoTableQ_140006C8A.cpp" />
    <ClCompile Include="Source\j_CompleteWorkCashDbWorkerUEAAXXZ_14000BF2D.cpp" />
    <ClCompile Include="Source\j_CompleteWorkCCashDbWorkerGBUEAAXXZ_14000A524.cpp" />
    <ClCompile Include="Source\j_CompleteWorkCCashDbWorkerNULLUEAAXXZ_14000BBC2.cpp" />
    <ClCompile Include="Source\j_CompleteWorkCCashDBWorkManagerQEAAXXZ_140003B6B.cpp" />
    <ClCompile Include="Source\j_Complete_DB_Update_CommitteeCGuildQEAAXPEADZ_140006BC7.cpp" />
    <ClCompile Include="Source\j_Complete_db_Update_Data_For_TradeCMainThreadAEAA_140002469.cpp" />
    <ClCompile Include="Source\j_Complete_RenameChar_DB_SelectCPotionMgrQEAAXEPEA_14000C7D9.cpp" />
    <ClCompile Include="Source\j_Complete_RenameChar_DB_UpdateCPotionMgrQEAAXEPEA_14000A4B6.cpp" />
    <ClCompile Include="Source\j_ConfigUserODBCCRFNewDatabaseQEAA_NPEBD00GZ_14000E18D.cpp" />
    <ClCompile Include="Source\j_ConfigUserODBCCRusiaBillingMgrQEAAHPEAD00GZ_140007432.cpp" />
    <ClCompile Include="Source\j_constructallocatorPEAVCLogTypeDBTaskstdQEAAXPEAP_14000BCF3.cpp" />
    <ClCompile Include="Source\j_consume_del_itemCMgrAvatorItemHistoryQEAAXHPEAU__14000BAFF.cpp" />
    <ClCompile Include="Source\j_Cont_UserSave_CompleteCMainThreadQEAAXPEAU_DB_QR_140011B12.cpp" />
    <ClCompile Include="Source\j_Cont_UserSave_CompleteCUserDBQEAAXEPEAU_AVATOR_D_14000EE71.cpp" />
    <ClCompile Include="Source\j_ConvertCodeIntoItemYAPEAU_db_con_STORAGE_LISTPEA_140008904.cpp" />
    <ClCompile Include="Source\j_ConvertErrorCodeCashDbWorkerMEAAHDZ_140004DDB.cpp" />
    <ClCompile Include="Source\j_ConvertErrorCodeCCashDbWorkerJPMEAAHDZ_14000D157.cpp" />
    <ClCompile Include="Source\j_ConvertErrorCodeCCashDbWorkerNULLMEAAHDZ_140007BE4.cpp" />
    <ClCompile Include="Source\j_ConvertLocalToWorldDummyCMapDataQEAA_NPEAVCDummy_14000DA2B.cpp" />
    <ClCompile Include="Source\j_coupon_use_buy_itemCMgrAvatorItemHistoryQEAAXPEA_140003AE4.cpp" />
    <ClCompile Include="Source\j_CovDBKey_ANIMUSKEYQEAAEXZ_1400136F1.cpp" />
    <ClCompile Include="Source\j_CovDBKey_COMBINEKEYQEAAHXZ_140012233.cpp" />
    <ClCompile Include="Source\j_CovDBKey_EMBELLKEYQEAAHXZ_1400076A3.cpp" />
    <ClCompile Include="Source\j_CovDBKey_EQUIPKEYQEAAFXZ_140005AA6.cpp" />
    <ClCompile Include="Source\j_CovDBKey_FORCEKEYQEAAHXZ_14000970A.cpp" />
    <ClCompile Include="Source\j_CovDBKey_INVENKEYQEAAHXZ_140004453.cpp" />
    <ClCompile Include="Source\j_CovDBKey_LINKKEYQEAAFXZ_14000225C.cpp" />
    <ClCompile Include="Source\j_CreateCMonsterSPGroupTableQEAA_NPEAVCRecordData0_14000B10E.cpp" />
    <ClCompile Include="Source\j_CreateCUnmannedTraderClassInfoTableCodeTypeUEAAP_140010758.cpp" />
    <ClCompile Include="Source\j_CreateCUnmannedTraderClassInfoTableTypeUEAAPEAVC_14000432C.cpp" />
    <ClCompile Include="Source\j_CreateDBTableAutominePersonalMgrQEAA_NXZ_14000A583.cpp" />
    <ClCompile Include="Source\j_CreateTblLtdCRFDBItemLogQEAA_NHZ_1400127BF.cpp" />
    <ClCompile Include="Source\j_CreateTblLtd_ItemInfoCRFDBItemLogQEAA_NHZ_140004E8F.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataBRUEAAPEAVCashDbWo_140007D1A.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataCNUEAAPEAVCashDbWo_140002509.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataESUEAAPEAVCashDbWo_140008193.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataGBUEAAPEAVCashDbWo_140008BAC.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataIDUEAAPEAVCashDbWo_1400062D0.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataJPUEAAPEAVCashDbWo_1400089F4.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataKRUEAAPEAVCashDbWo_1400028C4.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataNULLUEAAPEAVCashDb_140005911.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataPHUEAAPEAVCashDbWo_140006A2D.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataRUUEAAPEAVCashDbWo_14000D030.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataTHUEAAPEAVCashDbWo_14000EB01.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataTWUEAAPEAVCashDbWo_1400099EE.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataUEAAPEAVCashDbWork_1400070E5.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingDataUSUEAAPEAVCashDbWo_140004863.cpp" />
    <ClCompile Include="Source\j_CreateWorkerCNationSettingManagerQEAAPEAVCashDbW_140001D70.cpp" />
    <ClCompile Include="Source\j_create_amine_personalCRFWorldDatabaseQEAA_NXZ_1400052B8.cpp" />
    <ClCompile Include="Source\j_create_automine_tableCRFWorldDatabaseQEAA_NXZ_140001609.cpp" />
    <ClCompile Include="Source\j_Create_PvpPointGuildRankTableCRFWorldDatabaseQEA_140002108.cpp" />
    <ClCompile Include="Source\j_create_sumtotal_dungeonCRFWorldDatabaseQEAA_NHPE_140006DDE.cpp" />
    <ClCompile Include="Source\j_create_table_atrade_taxrateCRFWorldDatabaseQEAA__1400084A9.cpp" />
    <ClCompile Include="Source\j_cut_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_con_S_140006C71.cpp" />
    <ClCompile Include="Source\j_DatabaseInitCMainThreadAEAA_NPEAD0Z_14000FFCE.cpp" />
    <ClCompile Include="Source\j_DataValidCheckReviseCUserDBSA_NPEAU_AVATOR_DATAP_1400108B1.cpp" />
    <ClCompile Include="Source\j_DBProcessCLogTypeDBTaskManagerQEAAXXZ_14000D472.cpp" />
    <ClCompile Include="Source\j_db_Add_PvpPointCMainThreadQEAAEKKKZ_1400101C2.cpp" />
    <ClCompile Include="Source\j_db_buy_emblemCMainThreadQEAAEKHKKKPEAN0PEAEPEAD1_14000FC4A.cpp" />
    <ClCompile Include="Source\j_db_char_set_aliveCMainThreadQEAAEKEKPEADEPEAU_RE_14000836E.cpp" />
    <ClCompile Include="Source\j_db_Delete_AvatorCMainThreadQEAAEKEZ_14000FACE.cpp" />
    <ClCompile Include="Source\j_db_disjoint_guildCMainThreadQEAAEKZ_14000E679.cpp" />
    <ClCompile Include="Source\j_db_GM_GreetingMsgCMainThreadQEAAEPEAU_qry_case_g_140005CFE.cpp" />
    <ClCompile Include="Source\j_db_GUILD_GreetingMsgCMainThreadQEAAEPEAU_qry_cas_14001362E.cpp" />
    <ClCompile Include="Source\j_db_input_guild_moneyCMainThreadQEAAEKKKKPEAN0PEA_140003B61.cpp" />
    <ClCompile Include="Source\j_db_input_guild_money_atradetaxCMainThreadQEAAEKK_1400115F4.cpp" />
    <ClCompile Include="Source\j_db_Insert_AvatorCMainThreadQEAAEKPEADPEAU_REGED__14000D4CC.cpp" />
    <ClCompile Include="Source\j_db_Insert_ChangeClass_AfterInitClassCMainThreadQ_14000CE64.cpp" />
    <ClCompile Include="Source\j_db_Insert_CharacSelect_LogCMainThreadQEAAEKPEADK_140007022.cpp" />
    <ClCompile Include="Source\j_db_Insert_Economy_HistoryCMainThreadQEAAEKPEAU_w_140005619.cpp" />
    <ClCompile Include="Source\j_db_Insert_guildCMainThreadQEAAEPEAKPEADE0Z_1400076F3.cpp" />
    <ClCompile Include="Source\j_db_Insert_ItemCMainThreadQEAAEKKKKEZ_14000E183.cpp" />
    <ClCompile Include="Source\j_db_LoadGreetingMsgCMainThreadQEAA_NXZ_1400073E2.cpp" />
    <ClCompile Include="Source\j_db_Load_AvatorCMainThreadQEAAEKKPEAU_AVATOR_DATA_140005259.cpp" />
    <ClCompile Include="Source\j_db_Load_ContentCMainThreadQEAAEPEADZ_14000FB9B.cpp" />
    <ClCompile Include="Source\j_db_load_invenAutominePersonalMgrQEAA_NKPEAU_PERS_14000F858.cpp" />
    <ClCompile Include="Source\j_db_Load_PostStorageCMainThreadQEAAEPEADZ_14000BAFA.cpp" />
    <ClCompile Include="Source\j_db_Load_ReturnPostCMainThreadQEAAEPEADZ_14000FCF4.cpp" />
    <ClCompile Include="Source\j_db_Log_UserNumCMainThreadQEAAEHHZ_14000A632.cpp" />
    <ClCompile Include="Source\j_db_output_guild_moneyCMainThreadQEAAEKKKKPEAN0PE_14000E16A.cpp" />
    <ClCompile Include="Source\j_db_RACE_GreetingMsgCMainThreadQEAAEPEAU_qry_case_14000436D.cpp" />
    <ClCompile Include="Source\j_db_Reged_AvatorCMainThreadQEAAEKPEAU_REGEDPEAU_N_14000DEF4.cpp" />
    <ClCompile Include="Source\j_db_Select_Economy_HistoryCMainThreadQEAAEPEAU_ec_140003A7B.cpp" />
    <ClCompile Include="Source\j_db_Update_AvatorCMainThreadQEAAEKPEAU_AVATOR_DAT_14000181B.cpp" />
    <ClCompile Include="Source\j_DB_Update_GuildMasterCGuildQEAA_NPEAU_guild_memb_14000FF56.cpp" />
    <ClCompile Include="Source\j_db_update_guildmasterCMainThreadQEAAEPEAU_qry_ca_140004CFF.cpp" />
    <ClCompile Include="Source\j_DB_Update_GuildMaster_CompleteCGuildQEAAXKEKEZ_140008B98.cpp" />
    <ClCompile Include="Source\j_db_update_guildmember_addCMainThreadQEAAEKKEHZ_140007081.cpp" />
    <ClCompile Include="Source\j_db_update_guildmember_delCMainThreadQEAAEKKHZ_14000335A.cpp" />
    <ClCompile Include="Source\j_db_Update_PostStorageCMainThreadQEAAEKPEAU_AVATO_14000D67F.cpp" />
    <ClCompile Include="Source\j_db_Update_PvpInfoCMainThreadQEAAEKEPEAFNZ_1400062B7.cpp" />
    <ClCompile Include="Source\j_deallocateallocatorPEAVCLogTypeDBTaskstdQEAAXPEA_140011D60.cpp" />
    <ClCompile Include="Source\j_dec_reftable_objlua_tinkerQEAAXXZ_14000E5A7.cpp" />
    <ClCompile Include="Source\j_DeleteLinkLendItemMngQEAA_NGEPEAU_db_con_STORAGE_14000F0DD.cpp" />
    <ClCompile Include="Source\j_DeleteLinkLendItemSheetAEAA_NEPEAU_db_con_STORAG_14001212A.cpp" />
    <ClCompile Include="Source\j_DeleteListTimeLimitJadeMngQEAA_NGPEAU_db_con_STO_14000AAE2.cpp" />
    <ClCompile Include="Source\j_DeleteUseListTimeLimitJadeAEAA_NPEAU_db_con_STOR_140005CE0.cpp" />
    <ClCompile Include="Source\j_DeleteWaitListTimeLimitJadeAEAA_NPEAU_db_con_STO_14000394F.cpp" />
    <ClCompile Include="Source\j_Delete_Avator_CompleteCMainThreadQEAAXPEAU_DB_QR_14000C644.cpp" />
    <ClCompile Include="Source\j_Delete_Char_CompleteCUserDBQEAAXEEZ_140005C4A.cpp" />
    <ClCompile Include="Source\j_Delete_Char_RequestCUserDBQEAA_NEZ_140012A1C.cpp" />
    <ClCompile Include="Source\j_Delete_GuildCRFWorldDatabaseQEAA_NKZ_140005B41.cpp" />
    <ClCompile Include="Source\j_Delete_ItemChargeCRFWorldDatabaseQEAA_NKZ_140002478.cpp" />
    <ClCompile Include="Source\j_delete_npc_quest_itemCMgrAvatorItemHistoryQEAAXH_14000BBE0.cpp" />
    <ClCompile Include="Source\j_Delete_PatriarchCommCRFWorldDatabaseQEAAEKPEADZ_1400024E6.cpp" />
    <ClCompile Include="Source\j_Delete_TrunkItemChargeCRFWorldDatabaseQEAA_NKZ_14000C68A.cpp" />
    <ClCompile Include="Source\j_Delete_TrunkItemCharge_ExtendCRFWorldDatabaseQEA_14000D86E.cpp" />
    <ClCompile Include="Source\j_DelPostDataCUserDBQEAAXKZ_140007C57.cpp" />
    <ClCompile Include="Source\j_DelUnit_LIST_UNIT_DB_BASEQEAAXXZ_14000D3AF.cpp" />
    <ClCompile Include="Source\j_destroyallocatorPEAVCLogTypeDBTaskstdQEAAXPEAPEA_14000D2C9.cpp" />
    <ClCompile Include="Source\j_DestroyCLogTypeDBTaskManagerSAXXZ_140002491.cpp" />
    <ClCompile Include="Source\j_DestroyCLogTypeDBTaskPoolAEAAXXZ_1400114AF.cpp" />
    <ClCompile Include="Source\j_DestroyCUnmannedTraderGroupItemInfoTableSAXXZ_14000D599.cpp" />
    <ClCompile Include="Source\j_DestroyCUnmannedTraderUserInfoTableSAXXZ_140005808.cpp" />
    <ClCompile Include="Source\j_DiagRecALogCRFNewDatabaseIEAAXFFPEAXZ_14000E359.cpp" />
    <ClCompile Include="Source\j_DiagRecWLogCRFNewDatabaseIEAAXFFPEAXZ_140008C33.cpp" />
    <ClCompile Include="Source\j_DoWorkCashDbWorkerMEAAXXZ_140010AE6.cpp" />
    <ClCompile Include="Source\j_DoWorkCCashDbWorkerGBMEAAXXZ_140011A9A.cpp" />
    <ClCompile Include="Source\j_DoWorkCCashDbWorkerNULLMEAAXXZ_1400128FF.cpp" />
    <ClCompile Include="Source\j_DummyCreateCUserDBQEAAXKZ_140004E9E.cpp" />
    <ClCompile Include="Source\j_DXUtil_ReadBoolRegKeyYAJPEAUHKEY__PEADPEAHHZ_14000F4CA.cpp" />
    <ClCompile Include="Source\j_D_Vector_const_iteratorPEAVCLogTypeDBTaskValloca_14000C2C0.cpp" />
    <ClCompile Include="Source\j_D_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEA_140012BBB.cpp" />
    <ClCompile Include="Source\j_emptyvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogT_14000BEBA.cpp" />
    <ClCompile Include="Source\j_EndDataBaseCRFNewDatabaseQEAAXXZ_140011F22.cpp" />
    <ClCompile Include="Source\j_endvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTyp_140005894.cpp" />
    <ClCompile Include="Source\j_Enter_AccountCUserDBQEAA_NKKKPEAKZ_140009E58.cpp" />
    <ClCompile Include="Source\j_erasevectorPEAVCLogTypeDBTaskVallocatorPEAVCLogT_1400012B7.cpp" />
    <ClCompile Include="Source\j_EroorActionProcSQL_ERRORCRFNewDatabaseIEAA_NPEAX_140003F76.cpp" />
    <ClCompile Include="Source\j_ErrFmtLogCRFNewDatabaseIEAAXPEADZZ_1400132FF.cpp" />
    <ClCompile Include="Source\j_ErrFmtLogCRFNewDatabaseIEAAXPEA_WZZ_14000894F.cpp" />
    <ClCompile Include="Source\j_ErrLogCRFNewDatabaseIEAAXPEADZ_140011397.cpp" />
    <ClCompile Include="Source\j_ErrorActionCRFNewDatabaseIEAAXFPEAXZ_14000EDB8.cpp" />
    <ClCompile Include="Source\j_ErrorMsgLogCRFNewDatabaseIEAAXFPEAD0PEAXZ_140005303.cpp" />
    <ClCompile Include="Source\j_ErrorMsgLogCRFNewDatabaseIEAAXFPEA_W0PEAXZ_140009066.cpp" />
    <ClCompile Include="Source\j_exchange_itemCMgrAvatorItemHistoryQEAAXHPEAU_db__140006640.cpp" />
    <ClCompile Include="Source\j_ExecUpdateBinaryQueryCRFNewDatabaseQEAA_NPEADPEA_1400135D9.cpp" />
    <ClCompile Include="Source\j_ExecUpdateQueryCRFNewDatabaseQEAA_NPEAD_NZ_14000604B.cpp" />
    <ClCompile Include="Source\j_ExecUpdateQueryCRFNewDatabaseQEAA_NPEA_W_NZ_14000C833.cpp" />
    <ClCompile Include="Source\j_exist_aminpersonal_invenCRFWorldDatabaseQEAAHKZ_1400127E2.cpp" />
    <ClCompile Include="Source\j_exist_automineCRFWorldDatabaseQEAAHEEZ_1400053A3.cpp" />
    <ClCompile Include="Source\j_Exit_Account_CompleteCUserDBQEAAXEZ_14000C03B.cpp" />
    <ClCompile Include="Source\j_Exit_Account_RequestCUserDBQEAAXXZ_140003684.cpp" />
    <ClCompile Include="Source\j_extractAP_BatterySlotQEAA_NPEAU_db_con_STORAGE_L_140009B6A.cpp" />
    <ClCompile Include="Source\j_extract_batteryAutominePersonalQEAA_NEPEAU_db_co_1400086BB.cpp" />
    <ClCompile Include="Source\j_E_Vector_const_iteratorPEAVCLogTypeDBTaskValloca_140010BFE.cpp" />
    <ClCompile Include="Source\j_E_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEA_14000CD47.cpp" />
    <ClCompile Include="Source\j_FileSizeCRecordDataQEAAKPEBDZ_14000E9BC.cpp" />
    <ClCompile Include="Source\j_fillPEAPEAVCLogTypeDBTaskPEAV1stdYAXPEAPEAVCLogT_140004232.cpp" />
    <ClCompile Include="Source\j_FindByIndexCUnmannedTraderUserInfoTableAEAAPEAVC_140002563.cpp" />
    <ClCompile Include="Source\j_FindCUnmannedTraderUserInfoTableAEAAPEAVCUnmanne_1400117CF.cpp" />
    <ClCompile Include="Source\j_FindDummyCDummyPosTableSA_NPEAD0PEAU_dummy_posit_140010979.cpp" />
    <ClCompile Include="Source\j_FindUserCUnmannedTraderUserInfoTableAEAAPEAVCUnm_14000B1C7.cpp" />
    <ClCompile Include="Source\j_FirstSettingDataCUserDBAEAA_NXZ_140004426.cpp" />
    <ClCompile Include="Source\j_FixUnit_WEAPON_PARAMQEAAXPEAU_LIST_UNIT_DB_BASEZ_140006974.cpp" />
    <ClCompile Include="Source\j_FixWeapon_WEAPON_PARAMQEAAXPEAU_db_con_STORAGE_L_14001162B.cpp" />
    <ClCompile Include="Source\j_FmtLogCRFNewDatabaseIEAAXPEADZZ_14001020D.cpp" />
    <ClCompile Include="Source\j_FmtLogCRFNewDatabaseIEAAXPEA_WZZ_140008E86.cpp" />
    <ClCompile Include="Source\j_ForceCloseCommandCUserDBQEAAXEK_NPEADZ_14000C202.cpp" />
    <ClCompile Include="Source\j_FreeCEnglandBillingMgrQEAA_NXZ_140009EAD.cpp" />
    <ClCompile Include="Source\j_FreeSelectHandleCRFNewDatabaseQEAA_NXZ_14000960B.cpp" />
    <ClCompile Include="Source\j_FreeUpdateHandleCRFNewDatabaseQEAA_NXZ_14000FA33.cpp" />
    <ClCompile Include="Source\j_GenerateRandomGeneratableCryptoMaterialCryptoPPU_14000AB19.cpp" />
    <ClCompile Include="Source\j_GetActPointCUserDBQEAAKEZ_14000DAF8.cpp" />
    <ClCompile Include="Source\j_GetBaseDL_FixedBasePrecomputationImplUECPPointCr_14001034D.cpp" />
    <ClCompile Include="Source\j_GetBasePrecomputationDL_GroupParametersImplVEcPr_140006B18.cpp" />
    <ClCompile Include="Source\j_GetBasePrecomputationDL_GroupParameters_ECVECPCr_140001D75.cpp" />
    <ClCompile Include="Source\j_GetBillingTypeCUserDBQEAAHXZ_1400087DD.cpp" />
    <ClCompile Include="Source\j_GetCashDBDBIPCNationSettingManagerQEAAPEBDXZ_140006578.cpp" />
    <ClCompile Include="Source\j_GetCashDBIDCNationSettingManagerQEAAPEBDXZ_14000A010.cpp" />
    <ClCompile Include="Source\j_GetCashDBNameCNationSettingManagerQEAAPEBDXZ_1400114A5.cpp" />
    <ClCompile Include="Source\j_GetCashDBPortCNationSettingManagerQEAAGXZ_140005105.cpp" />
    <ClCompile Include="Source\j_GetCashDBPWCNationSettingManagerQEAAPEBDXZ_1400042F0.cpp" />
    <ClCompile Include="Source\j_GetCheatTableCNationSettingManagerQEAAPEAUCHEAT__14000C329.cpp" />
    <ClCompile Include="Source\j_GetCodeCNationCodeStrTableQEAAHPEBDZ_14000DA58.cpp" />
    <ClCompile Include="Source\j_GetCompleteCLogTypeDBTaskPoolQEAAPEAVCLogTypeDBT_140008DC8.cpp" />
    <ClCompile Include="Source\j_GetCRecordData_SetItemCSUItemSystemQEAAPEAVCReco_140013E0D.cpp" />
    <ClCompile Include="Source\j_GetDataCLogTypeDBTaskQEAAPEADXZ_14000FE2F.cpp" />
    <ClCompile Include="Source\j_GetDBProcCLogTypeDBTaskManagerQEAA_NXZ_140009DC7.cpp" />
    <ClCompile Include="Source\j_GetDBRetCLogTypeDBTaskQEAAEXZ_140013D6D.cpp" />
    <ClCompile Include="Source\j_GetDBTaskDataStatusCLogTypeDBTaskManagerQEAA_NXZ_140008431.cpp" />
    <ClCompile Include="Source\j_GetEffectCode_LIST_SFCONT_DB_BASEQEAAEXZ_14000C76B.cpp" />
    <ClCompile Include="Source\j_GetEffectIndex_LIST_SFCONT_DB_BASEQEAAGXZ_14000702C.cpp" />
    <ClCompile Include="Source\j_GetEmptyCLogTypeDBTaskPoolQEAAPEAVCLogTypeDBTask_14000A416.cpp" />
    <ClCompile Include="Source\j_GetEmptyRecordSerialCUnmannedTraderControllerIEA_14000430E.cpp" />
    <ClCompile Include="Source\j_GetEquipCEquipItemSFAgentQEAAPEAU_db_con_STORAGE_140008594.cpp" />
    <ClCompile Include="Source\j_GetGodBoxItemInfoPtrCGoldenBoxItemMgrQEAAPEAU_db_1400127D8.cpp" />
    <ClCompile Include="Source\j_GetGoldBoxConsumableCHolyStoneSystemQEAA_NXZ_14001187E.cpp" />
    <ClCompile Include="Source\j_GetGoldBoxItemIndexCGoldenBoxItemMgrQEAAGGZ_14001395D.cpp" />
    <ClCompile Include="Source\j_GetGoldBoxItemPtrCGoldenBoxItemMgrQEAAPEADXZ_14001209E.cpp" />
    <ClCompile Include="Source\j_GetGroupIDCUnmannedTraderClassInfoTableCodeTypeU_14000B3D9.cpp" />
    <ClCompile Include="Source\j_GetGroupIDCUnmannedTraderClassInfoTableCodeTypeU_14000BAEB.cpp" />
    <ClCompile Include="Source\j_GetGroupIDCUnmannedTraderClassInfoTableTypeUEAA__14000D021.cpp" />
    <ClCompile Include="Source\j_GetGroupIDCUnmannedTraderClassInfoTableTypeUEAA__14000DD78.cpp" />
    <ClCompile Include="Source\j_GetGroupIDCUnmannedTraderGroupItemInfoTableQEAA__14000B032.cpp" />
    <ClCompile Include="Source\j_GetGroupPrecomputationDL_GroupParametersImplVEcP_1400022F7.cpp" />
    <ClCompile Include="Source\j_GetInxCLogTypeDBTaskQEAAKXZ_1400049DF.cpp" />
    <ClCompile Include="Source\j_GetItemTableCodeYAHPEADZ_1400123B9.cpp" />
    <ClCompile Include="Source\j_GetLeftTime_LIST_SFCONT_DB_BASEQEAAGXZ_14000BBAE.cpp" />
    <ClCompile Include="Source\j_GetLocalHourCRFNewDatabaseIEAAEXZ_1400112B6.cpp" />
    <ClCompile Include="Source\j_GetLv_LIST_SFCONT_DB_BASEQEAAEXZ_140002A4A.cpp" />
    <ClCompile Include="Source\j_GetMachineInfoAutoMineMachineQEAAXPEAU_DB_LOAD_A_140004B6A.cpp" />
    <ClCompile Include="Source\j_GetMaxRegistCntCUnmannedTraderUserInfoTableQEAAE_1400125DF.cpp" />
    <ClCompile Include="Source\j_GetNpcRecordCItemStoreQEAAPEAU_base_fldXZ_14000EC4B.cpp" />
    <ClCompile Include="Source\j_GetOrder_LIST_SFCONT_DB_BASEQEAAEXZ_1400066D1.cpp" />
    <ClCompile Include="Source\j_GetOreIndexFromRateCOreCuttingTableQEAAKKKZ_14000E1BA.cpp" />
    <ClCompile Include="Source\j_GetProcCLogTypeDBTaskPoolQEAAPEAVCLogTypeDBTaskX_14000B267.cpp" />
    <ClCompile Include="Source\j_GetProcRetCLogTypeDBTaskQEAAEXZ_1400125DA.cpp" />
    <ClCompile Include="Source\j_GetPtrActPointCUserDBQEAAPEAKXZ_140002301.cpp" />
    <ClCompile Include="Source\j_GetPtrFromItemCode_STORAGE_LISTQEAAPEAU_db_con1P_14000EEC6.cpp" />
    <ClCompile Include="Source\j_GetPtrFromItemInfo_STORAGE_LISTQEAAPEAU_db_con1E_14000E8F4.cpp" />
    <ClCompile Include="Source\j_GetPtrFromSerial_STORAGE_LISTQEAAPEAU_db_con1GZ_14000D481.cpp" />
    <ClCompile Include="Source\j_GetPublicPrecomputationDL_PublicKeyImplVDL_Group_14000348B.cpp" />
    <ClCompile Include="Source\j_GetQueryCUnmannedTraderSortTypeQEBAPEBDXZ_140005786.cpp" />
    <ClCompile Include="Source\j_GetQueryTypeCLogTypeDBTaskQEAAHXZ_14000E6D3.cpp" />
    <ClCompile Include="Source\j_GetRand_100_per_random_tableQEAAGXZ_140011A13.cpp" />
    <ClCompile Include="Source\j_GetRecordByHashCRecordDataQEAAPEAU_base_fldPEBDH_1400104EC.cpp" />
    <ClCompile Include="Source\j_GetRecordCDummyPosTableQEAAPEAU_dummy_positionHZ_14000544D.cpp" />
    <ClCompile Include="Source\j_GetRecordCDummyPosTableQEAAPEAU_dummy_positionPE_14000A6D7.cpp" />
    <ClCompile Include="Source\j_GetRecordCEventLootTableQEAAPEAU_event_drop1PEBD_140005F24.cpp" />
    <ClCompile Include="Source\j_GetRecordCItemUpgradeTableQEAAPEAU_ItemUpgrade_f_14000DC47.cpp" />
    <ClCompile Include="Source\j_GetRecordCMapDataTableQEAAPEAU_map_fldKZ_14000BDE3.cpp" />
    <ClCompile Include="Source\j_GetRecordCMonsterSPGroupTableQEAAPEAU_monster_sp_1400014DD.cpp" />
    <ClCompile Include="Source\j_GetRecordCMonsterSPGroupTableQEAAPEAU_monster_sp_140006983.cpp" />
    <ClCompile Include="Source\j_GetRecordCRecordDataQEAAPEAU_base_fldHZ_140004444.cpp" />
    <ClCompile Include="Source\j_GetRecordCRecordDataQEAAPEAU_base_fldPEBDHZ_1400061CC.cpp" />
    <ClCompile Include="Source\j_GetRecordCRecordDataQEAAPEAU_base_fldPEBDZ_14000635C.cpp" />
    <ClCompile Include="Source\j_GetRecordFromResCItemUpgradeTableQEAAPEAU_ItemUp_14000826A.cpp" />
    <ClCompile Include="Source\j_GetRecordNumCDummyPosTableQEAAHXZ_14000752C.cpp" />
    <ClCompile Include="Source\j_GetRecordNumCMapDataTableQEAAHXZ_140008F0D.cpp" />
    <ClCompile Include="Source\j_GetRecordNumCRecordDataQEAAHXZ_140011B76.cpp" />
    <ClCompile Include="Source\j_GetRegItemInfoCUnmannedTraderUserInfoTableQEAAPE_140004FD9.cpp" />
    <ClCompile Include="Source\j_GetSerialNumber_db_con_STORAGE_LISTQEAAKXZ_14000F515.cpp" />
    <ClCompile Include="Source\j_GetSetItemTableInfoCSUItemSystemQEAAHKPEADHZ_14000F812.cpp" />
    <ClCompile Include="Source\j_GetSizeCItemUpgradeTableQEAAHXZ_14000F19B.cpp" />
    <ClCompile Include="Source\j_GetSortTypeCUnmannedTraderGroupItemInfoTableQEAA_140012EDB.cpp" />
    <ClCompile Include="Source\j_GetStatIndex_STAT_DB_BASESAHEEZ_14000F191.cpp" />
    <ClCompile Include="Source\j_GetStrCNationCodeStrTableQEAAPEBDHZ_14000341D.cpp" />
    <ClCompile Include="Source\j_GetTableCodeCUnmannedTraderRegistItemInfoQEBAEXZ_140010FE6.cpp" />
    <ClCompile Include="Source\j_GetUseCashQueryStrCashDbWorkerUEAAXAEAU_param_ca_140002CDE.cpp" />
    <ClCompile Include="Source\j_GetUseCashQueryStrCCashDbWorkerBRUEAAXAEAU_param_14001278D.cpp" />
    <ClCompile Include="Source\j_GetUseCashQueryStrCCashDbWorkerESUEAAXAEAU_param_140013C55.cpp" />
    <ClCompile Include="Source\j_GetUseCashQueryStrCCashDbWorkerGBUEAAXAEAU_param_140002C7F.cpp" />
    <ClCompile Include="Source\j_GetUseCashQueryStrCCashDbWorkerIDUEAAXAEAU_param_1400071B2.cpp" />
    <ClCompile Include="Source\j_GetUseCashQueryStrCCashDbWorkerJPUEAAXAEAU_param_14000A7AE.cpp" />
    <ClCompile Include="Source\j_GetUseCashQueryStrCCashDbWorkerNULLUEAAXAEAU_par_140009E9E.cpp" />
    <ClCompile Include="Source\j_GetUseCashQueryStrCCashDbWorkerTHUEAAXAEAU_param_140005FAB.cpp" />
    <ClCompile Include="Source\j_GetUseCashQueryStrCCashDbWorkerTWUEAAXAEAU_param_140007D56.cpp" />
    <ClCompile Include="Source\j_GetUseCashQueryStrCCashDbWorkerUSUEAAXAEAU_param_140009F39.cpp" />
    <ClCompile Include="Source\j_GetUseCashQueryStrCCashDBWorkManagerQEAAXAEAU_pa_14000A0E2.cpp" />
    <ClCompile Include="Source\j_GetVersionCUnmannedTraderGroupItemInfoTableQEAA__14000C923.cpp" />
    <ClCompile Include="Source\j_GetWeaponTolType_WEAPON_PARAMQEAAHPEAU_db_con_ST_140003841.cpp" />
    <ClCompile Include="Source\j_GetWebDBIDCMsgRACE_BOSS_MSGQEAAKXZ_14000A920.cpp" />
    <ClCompile Include="Source\j_GetWorldDBIDCNationSettingManagerQEAAPEBDXZ_14000F4ED.cpp" />
    <ClCompile Include="Source\j_GetWorldDBPWCNationSettingManagerQEAAPEBDXZ_1400030E9.cpp" />
    <ClCompile Include="Source\j_get_AdjustableGradeCGuildMasterEffectQEAAEXZ_1400081E8.cpp" />
    <ClCompile Include="Source\j_get_batteryAP_BatterySlotQEAAPEAU_db_con_STORAGE_14000A7DB.cpp" />
    <ClCompile Include="Source\j_get_itemAutominePersonalQEAAPEAU_db_con_STORAGE__14000B65E.cpp" />
    <ClCompile Include="Source\j_grade_down_itemCMgrAvatorItemHistoryQEAAXHPEAU_d_1400109B5.cpp" />
    <ClCompile Include="Source\j_grade_up_itemCMgrAvatorItemHistoryQEAAXHPEAU_db__1400025F9.cpp" />
    <ClCompile Include="Source\j_GuildQueryInfoRequestCNetworkEXAEAA_NHPEADZ_140003819.cpp" />
    <ClCompile Include="Source\j_G_Vector_const_iteratorPEAVCLogTypeDBTaskValloca_1400032FB.cpp" />
    <ClCompile Include="Source\j_G_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEA_140004B47.cpp" />
    <ClCompile Include="Source\j_G_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEA_14000EB15.cpp" />
    <ClCompile Include="Source\j_HashEndianCorrectedBlockIteratedHashWithStaticTr_140008648.cpp" />
    <ClCompile Include="Source\j_HashEndianCorrectedBlockIteratedHashWithStaticTr_140013331.cpp" />
    <ClCompile Include="Source\j_H_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEA_140003B89.cpp" />
    <ClCompile Include="Source\j_InAtradTaxMoneyCMainThreadAEAAXPEAU_DB_QRY_SYN_D_14001154F.cpp" />
    <ClCompile Include="Source\j_IncreaseVersionCUnmannedTraderGroupItemInfoTable_140009999.cpp" />
    <ClCompile Include="Source\j_IncreaseVersionCUnmannedTraderGroupItemInfoTable_1400099E4.cpp" />
    <ClCompile Include="Source\j_IncreaseVersionCUnmannedTraderGroupItemInfoTable_140009CE1.cpp" />
    <ClCompile Include="Source\j_inc_reftable_objlua_tinkerQEAAXXZ_140010807.cpp" />
    <ClCompile Include="Source\j_IndexingCItemLootTableAEAA_NPEAVCRecordDataPEADZ_140009E21.cpp" />
    <ClCompile Include="Source\j_IndexingCItemUpgradeTableAEAA_NPEAVCRecordDataPE_14000A4E3.cpp" />
    <ClCompile Include="Source\j_IndexingCOreCuttingTableAEAA_NPEAVCRecordData0PE_1400088A5.cpp" />
    <ClCompile Include="Source\j_Inform_For_Exit_By_FireguardBlockCUserDBQEAAXXZ_14000AFA1.cpp" />
    <ClCompile Include="Source\j_InitClassCUserDBQEAA_NPEADZ_140009F2F.cpp" />
    <ClCompile Include="Source\j_InitCLogTypeDBTaskManagerQEAA_NXZ_140012D55.cpp" />
    <ClCompile Include="Source\j_InitCLogTypeDBTaskPoolQEAA_NIIAEAVCLogFileZ_140008D37.cpp" />
    <ClCompile Include="Source\j_InitCLogTypeDBTaskQEAA_NKIZ_140013322.cpp" />
    <ClCompile Include="Source\j_InitCNationCodeStrTableQEAA_NXZ_14000E1EC.cpp" />
    <ClCompile Include="Source\j_InitCPvpCashPointQEAAXPEAU_PVP_ORDER_VIEW_DB_BAS_14000DB0C.cpp" />
    <ClCompile Include="Source\j_InitCUnmannedTraderGroupItemInfoTableQEAA_NXZ_14000EED5.cpp" />
    <ClCompile Include="Source\j_InitCUnmannedTraderUserInfoTableQEAA_NXZ_140002793.cpp" />
    <ClCompile Include="Source\j_InitCUserDBQEAAXKZ_14000EBF6.cpp" />
    <ClCompile Include="Source\j_InitDBCLogTypeDBTaskManagerQEAA_NPEBD0Z_140013DC7.cpp" />
    <ClCompile Include="Source\j_InitializeCashDbWorkerUEAA_NXZ_140005CF4.cpp" />
    <ClCompile Include="Source\j_InitializeCCashDbWorkerGBUEAA_NXZ_140008319.cpp" />
    <ClCompile Include="Source\j_InitializeCCashDbWorkerNULLUEAA_NXZ_14000E499.cpp" />
    <ClCompile Include="Source\j_InitializeCCashDBWorkManagerQEAA_NXZ_140003B5C.cpp" />
    <ClCompile Include="Source\j_InitializeWorkerCCashDBWorkManagerQEAA_NXZ_14000164F.cpp" />
    <ClCompile Include="Source\j_InitLogDBLtdWriterQEAA_NPEAD0Z_14000D32D.cpp" />
    <ClCompile Include="Source\j_InitLoggerCLogTypeDBTaskManagerAEAA_NXZ_140001019.cpp" />
    <ClCompile Include="Source\j_InitMasteryFormulaYAXPEAVCRecordData0Z_140009534.cpp" />
    <ClCompile Include="Source\j_Init_ANIMUS_DB_BASEQEAAXXZ_14000AC40.cpp" />
    <ClCompile Include="Source\j_Init_AVATOR_DB_BASEQEAAXXZ_14000FAAB.cpp" />
    <ClCompile Include="Source\j_Init_BUDDY_DB_BASEQEAAXXZ_140002C07.cpp" />
    <ClCompile Include="Source\j_Init_CRYMSG_DB_BASEQEAAXXZ_14000F015.cpp" />
    <ClCompile Include="Source\j_Init_CUTTING_DB_BASEQEAAXXZ_14000798C.cpp" />
    <ClCompile Include="Source\j_Init_db_con_STORAGE_LISTQEAAXXZ_14000102D.cpp" />
    <ClCompile Include="Source\j_Init_DELPOST_DB_BASEQEAAXXZ_14000AB32.cpp" />
    <ClCompile Include="Source\j_Init_EMBELLISH_LIST_EQUIP_DB_BASEQEAAXXZ_140004016.cpp" />
    <ClCompile Include="Source\j_Init_EQUIP_DB_BASEQEAAXXZ_140006E42.cpp" />
    <ClCompile Include="Source\j_Init_FORCE_DB_BASEQEAAXXZ_14001311A.cpp" />
    <ClCompile Include="Source\j_Init_INVEN_DB_BASEQEAAXXZ_1400042DC.cpp" />
    <ClCompile Include="Source\j_Init_ITEMCOMBINE_DB_BASEQEAAXXZ_14000C126.cpp" />
    <ClCompile Include="Source\j_Init_LINK_DB_BASEQEAAXXZ_140011351.cpp" />
    <ClCompile Include="Source\j_Init_LIST_ANIMUS_DB_BASEQEAAXXZ_140006E38.cpp" />
    <ClCompile Include="Source\j_Init_LIST_BUDDY_DB_BASEQEAAXXZ_14000ABBE.cpp" />
    <ClCompile Include="Source\j_Init_LIST_CRYMSG_DB_BASEQEAAXXZ_14000F20E.cpp" />
    <ClCompile Include="Source\j_Init_LIST_CUTTING_DB_BASEQEAAXXZ_14000AB37.cpp" />
    <ClCompile Include="Source\j_Init_LIST_FORCE_DB_BASEQEAAXXZ_14000B929.cpp" />
    <ClCompile Include="Source\j_Init_LIST_INVEN_DB_BASEQEAAXXZ_140009845.cpp" />
    <ClCompile Include="Source\j_Init_LIST_ITEMCOMBINE_DB_BASEQEAAXXZ_1400087BA.cpp" />
    <ClCompile Include="Source\j_Init_LIST_LINK_DB_BASEQEAAXXZ_140008715.cpp" />
    <ClCompile Include="Source\j_Init_LIST_PERSONALAMINE_INVEN_DB_BASEQEAAXXZ_14000B514.cpp" />
    <ClCompile Include="Source\j_Init_LIST_QUEST_DB_BASEQEAAXXZ_1400125F3.cpp" />
    <ClCompile Include="Source\j_Init_LIST_SFCONT_DB_BASEQEAAXXZ_14000A25E.cpp" />
    <ClCompile Include="Source\j_Init_LIST_TRUNK_DB_BASEQEAAXXZ_14001316A.cpp" />
    <ClCompile Include="Source\j_Init_LIST_UNIT_DB_BASEQEAAXEZ_1400023BA.cpp" />
    <ClCompile Include="Source\j_Init_MASTERY_PARAMQEAA_NPEAU_STAT_DB_BASEEZ_14000D049.cpp" />
    <ClCompile Include="Source\j_Init_NOT_ARRANGED_AVATOR_DBQEAAXXZ_140005CA9.cpp" />
    <ClCompile Include="Source\j_Init_NPC_QUEST_HISTORY_QUEST_DB_BASEQEAAXXZ_140012E09.cpp" />
    <ClCompile Include="Source\j_Init_PCBANG_FAVOR_ITEM_DB_BASEQEAAXXZ_140002B62.cpp" />
    <ClCompile Include="Source\j_Init_PERSONALAMINE_INVEN_DB_BASEQEAAXXZ_140009BA1.cpp" />
    <ClCompile Include="Source\j_Init_POSTDATA_DB_BASEQEAAXXZ_140008CCE.cpp" />
    <ClCompile Include="Source\j_Init_POSTSTORAGE_DB_BASEQEAAXXZ_140012170.cpp" />
    <ClCompile Include="Source\j_Init_POTION_NEXT_USE_TIME_DB_BASEQEAAXXZ_14000C8E2.cpp" />
    <ClCompile Include="Source\j_Init_PVPPOINT_LIMIT_DB_BASEQEAAXXZ_140001C85.cpp" />
    <ClCompile Include="Source\j_Init_PVP_ORDER_VIEW_DB_BASEQEAAXXZ_14000C32E.cpp" />
    <ClCompile Include="Source\j_init_questCMgrAvatorQuestHistoryQEAAXPEADPEAU_QU_1400088F5.cpp" />
    <ClCompile Include="Source\j_Init_QUEST_DB_BASEQEAAXXZ_1400119A5.cpp" />
    <ClCompile Include="Source\j_Init_REGED_AVATOR_DBQEAAXXZ_140010CEE.cpp" />
    <ClCompile Include="Source\j_Init_RETURNPOST_DB_BASEQEAAXXZ_140013D7C.cpp" />
    <ClCompile Include="Source\j_Init_SFCONT_DB_BASEQEAAXXZ_14000A3E9.cpp" />
    <ClCompile Include="Source\j_Init_START_NPC_QUEST_HISTORY_QUEST_DB_BASEQEAAXX_14000EB51.cpp" />
    <ClCompile Include="Source\j_Init_STAT_DB_BASEQEAAXXZ_140003CBA.cpp" />
    <ClCompile Include="Source\j_Init_SUPPLEMENT_DB_BASEQEAAXXZ_14000EB65.cpp" />
    <ClCompile Include="Source\j_Init_TRADE_DB_BASEQEAAXXZ_140002928.cpp" />
    <ClCompile Include="Source\j_Init_TRUNK_DB_BASEQEAAXXZ_14000975A.cpp" />
    <ClCompile Include="Source\j_Init_UNIT_DB_BASEQEAAXXZ_1400094A8.cpp" />
    <ClCompile Include="Source\j_insertAP_BatterySlotQEAAHPEAU_db_con_STORAGE_LIS_1400120F3.cpp" />
    <ClCompile Include="Source\j_InsertCCheckSumGuildDataQEAA_NPEAVCRFWorldDataba_14000B1D1.cpp" />
    <ClCompile Include="Source\j_InsertChangeClassLogAfterInitClassCRFWorldDataba_1400029F0.cpp" />
    <ClCompile Include="Source\j_InsertCharacDataCCheckSumCharacAccountTrunkDataI_140013A43.cpp" />
    <ClCompile Include="Source\j_InsertDefalutRecordCUnmannedTraderControllerIEAA_140011C5C.cpp" />
    <ClCompile Include="Source\j_InsertDefaultPSRecordCPostSystemManagerQEAA_NXZ_140008E4F.cpp" />
    <ClCompile Include="Source\j_InsertDefaultWeeklyPvpPointSumRecordCWeeklyGuild_140003823.cpp" />
    <ClCompile Include="Source\j_InsertItem_PCBANG_FAVOR_ITEM_DB_BASEQEAA_NAEAU_d_140010299.cpp" />
    <ClCompile Include="Source\j_InsertLinkLendItemMngQEAA_NGEPEAU_db_con_STORAGE_1400019D8.cpp" />
    <ClCompile Include="Source\j_InsertLinkLendItemSheetAEAA_NEPEAU_db_con_STORAG_140013D22.cpp" />
    <ClCompile Include="Source\j_InsertListTimeLimitJadeMngQEAA_NGPEAU_db_con_STO_140012805.cpp" />
    <ClCompile Include="Source\j_InsertNotEnoughLimitItemRecordCItemStoreManagerQ_1400110F4.cpp" />
    <ClCompile Include="Source\j_InsertRenamePotionCPotionMgrQEAA_NPEAVCRFWorldDa_14000B00A.cpp" />
    <ClCompile Include="Source\j_InsertSettlementOwnerCWeeklyGuildRankManagerQEAA_14000812A.cpp" />
    <ClCompile Include="Source\j_InsertStateRecordCUnmannedTraderControllerIEAA_N_140005E25.cpp" />
    <ClCompile Include="Source\j_InsertTrunkDataCCheckSumCharacAccountTrunkDataIE_1400063C5.cpp" />
    <ClCompile Include="Source\j_InsertUseListTimeLimitJadeAEAA_NPEAU_db_con_STOR_140011A9F.cpp" />
    <ClCompile Include="Source\j_insertvectorPEAVCLogTypeDBTaskVallocatorPEAVCLog_140004683.cpp" />
    <ClCompile Include="Source\j_InsertWaitListTimeLimitJadeAEAA_NPEAU_db_con_STO_140007C1B.cpp" />
    <ClCompile Include="Source\j_Insert_AccountTrunkCRFWorldDatabaseQEAA_NKZ_140010019.cpp" />
    <ClCompile Include="Source\j_Insert_AccountTrunkExtendCRFWorldDatabaseQEAA_NK_140007149.cpp" />
    <ClCompile Include="Source\j_insert_amine_newownerCRFWorldDatabaseQEAA_NEEKZ_14000155A.cpp" />
    <ClCompile Include="Source\j_insert_amine_personalCRFWorldDatabaseQEAA_NKZ_14000D81E.cpp" />
    <ClCompile Include="Source\j_Insert_AnimusDataCRFWorldDatabaseQEAA_NKPEANZ_14001239B.cpp" />
    <ClCompile Include="Source\j_Insert_AnimusLogCRFWorldDatabaseQEAA_NKPEADENNZ_14000CA1D.cpp" />
    <ClCompile Include="Source\j_insert_atrade_taxrateCRFWorldDatabaseQEAA_NEKPEA_14000C630.cpp" />
    <ClCompile Include="Source\j_Insert_Avator_CompleteCMainThreadQEAAXPEAU_DB_QR_140012D05.cpp" />
    <ClCompile Include="Source\j_Insert_BossCryRecordCRFWorldDatabaseQEAA_NKZ_14000314D.cpp" />
    <ClCompile Include="Source\j_Insert_BuddyCRFWorldDatabaseQEAA_NKZ_14001346C.cpp" />
    <ClCompile Include="Source\j_Insert_CashLimSaleCRFWorldDatabaseQEAA_NXZ_140005FC4.cpp" />
    <ClCompile Include="Source\j_Insert_Char_CompleteCUserDBQEAAXEPEAU_REGED_AVAT_14000A196.cpp" />
    <ClCompile Include="Source\j_Insert_Char_RequestCUserDBQEAA_NPEADEE0KZ_140011D47.cpp" />
    <ClCompile Include="Source\j_Insert_DefaultWeeklyGuildPvpPointSumRecordCRFWor_140002414.cpp" />
    <ClCompile Include="Source\j_Insert_Economy_HistoryCRFWorldDatabaseQEAA_NKPEA_140001CC6.cpp" />
    <ClCompile Include="Source\j_Insert_GoldenBoxItemCRFWorldDatabaseQEAA_NXZ_14000F236.cpp" />
    <ClCompile Include="Source\j_Insert_GreetingRecordCRFWorldDatabaseQEAA_NHPEAD_14000D846.cpp" />
    <ClCompile Include="Source\j_Insert_GuidRoomCRFWorldDatabaseQEAA_NKEEZ_14000E0ED.cpp" />
    <ClCompile Include="Source\j_Insert_GuildBatlleResultLogBattelInfoCRFWorldDat_1400135BB.cpp" />
    <ClCompile Include="Source\j_Insert_GuildBatlleResultLogCRFWorldDatabaseQEAA__1400024D2.cpp" />
    <ClCompile Include="Source\j_Insert_GuildCRFWorldDatabaseQEAA_NPEADEZ_140012279.cpp" />
    <ClCompile Include="Source\j_Insert_GuildMoneyHistoryCRFWorldDatabaseQEAA_NKN_1400126BB.cpp" />
    <ClCompile Include="Source\j_Insert_ItemChargeInGameCRFWorldDatabaseQEAA_NKK__14000A66E.cpp" />
    <ClCompile Include="Source\j_Insert_ItemCombineExCRFWorldDatabaseQEAA_NKZ_140013E3A.cpp" />
    <ClCompile Include="Source\j_insert_iteminfoCRFDBItemLogQEAA_NPEAU_LTD_ITEMIN_14000DFFD.cpp" />
    <ClCompile Include="Source\j_Insert_LimitItemRecordCRFWorldDatabaseQEAA_NPEAK_140010848.cpp" />
    <ClCompile Include="Source\j_insert_ltdCRFDBItemLogQEAA_NPEAU_LTDZ_14000B76C.cpp" />
    <ClCompile Include="Source\j_Insert_MacroDataCRFWorldDatabaseQEAA_NKZ_1400112E8.cpp" />
    <ClCompile Include="Source\j_Insert_NpcDataCRFWorldDatabaseQEAA_NKPEAKZ_140004A8E.cpp" />
    <ClCompile Include="Source\j_Insert_NpcDataCRFWorldDatabaseQEAA_NKZ_140005AEC.cpp" />
    <ClCompile Include="Source\j_Insert_NpcLogCRFWorldDatabaseQEAA_NKPEADEKKZ_14000FD3F.cpp" />
    <ClCompile Include="Source\j_Insert_NpcQuest_HistoryCRFWorldDatabaseQEAA_NKZ_14000ABEB.cpp" />
    <ClCompile Include="Source\j_Insert_OreCuttingCRFWorldDatabaseQEAA_NKZ_1400089DB.cpp" />
    <ClCompile Include="Source\j_Insert_OreReset_LogCRFWorldDatabaseQEAA_NEHKKZ_140012C06.cpp" />
    <ClCompile Include="Source\j_Insert_PatriarchCommCRFWorldDatabaseQEAAEKKPEADZ_14000C572.cpp" />
    <ClCompile Include="Source\j_Insert_PcBangFavorItemCRFWorldDatabaseQEAA_NKZ_14000E403.cpp" />
    <ClCompile Include="Source\j_Insert_PostStorageRecordCRFWorldDatabaseQEAA_NXZ_140013D04.cpp" />
    <ClCompile Include="Source\j_Insert_PotionDelayCRFWorldDatabaseQEAA_NKZ_14000FB37.cpp" />
    <ClCompile Include="Source\j_Insert_PrimiumPlayTimeCRFWorldDatabaseQEAA_NKZ_14000C70C.cpp" />
    <ClCompile Include="Source\j_Insert_PSDefaultRecordCRFWorldDatabaseQEAA_NKZ_140011103.cpp" />
    <ClCompile Include="Source\j_Insert_PvpOrderViewInfoCRFWorldDatabaseQEAA_NKZ_14000FB50.cpp" />
    <ClCompile Include="Source\j_Insert_PvpPointGuildRankDataCRFWorldDatabaseQEAA_14000A24F.cpp" />
    <ClCompile Include="Source\j_Insert_PvpPointLimitInfoRecordCRFWorldDatabaseQE_140010C03.cpp" />
    <ClCompile Include="Source\j_Insert_QuestCRFWorldDatabaseQEAA_NKZ_1400094E4.cpp" />
    <ClCompile Include="Source\j_Insert_RenamePotionLogCRFWorldDatabaseQEAA_NKPEA_14001343F.cpp" />
    <ClCompile Include="Source\j_Insert_RFEvent_ClassRefineCRFWorldDatabaseQEAA_N_1400136E2.cpp" />
    <ClCompile Include="Source\j_Insert_SettlementOwnerLogCRFWorldDatabaseQEAA_NE_1400090F2.cpp" />
    <ClCompile Include="Source\j_Insert_Set_Limit_RunCRFWorldDatabaseQEAA_NPEAEHZ_140002914.cpp" />
    <ClCompile Include="Source\j_Insert_SFDelayInfoCRFWorldDatabaseQEAA_NKPEAU_wo_140002815.cpp" />
    <ClCompile Include="Source\j_Insert_Start_NpcQuest_HistoryCRFWorldDatabaseQEA_14000BDBB.cpp" />
    <ClCompile Include="Source\j_Insert_SupplementCRFWorldDatabaseQEAA_NKZ_14000D134.cpp" />
    <ClCompile Include="Source\j_Insert_UnitCRFWorldDatabaseQEAA_NKZ_140008F44.cpp" />
    <ClCompile Include="Source\j_Insert_UnitDataCRFWorldDatabaseQEAA_NKPEANZ_14000CDBA.cpp" />
    <ClCompile Include="Source\j_Insert_UnitLogCRFWorldDatabaseQEAA_NKPEADENNZ_140004E03.cpp" />
    <ClCompile Include="Source\j_Insert_UnmannedTraderItemStateRecordCRFWorldData_140010230.cpp" />
    <ClCompile Include="Source\j_Insert_UnmannedTraderSingleDefaultRecordCRFWorld_14001262A.cpp" />
    <ClCompile Include="Source\j_Insert_UserInterfaceCRFWorldDatabaseQEAA_NKZ_140001406.cpp" />
    <ClCompile Include="Source\j_Insert_UserNum_LogCRFWorldDatabaseQEAA_NHHZ_140002220.cpp" />
    <ClCompile Include="Source\j_Insert_WeeklyGuildPvpPointSumCRFWorldDatabaseQEA_1400010C8.cpp" />
    <ClCompile Include="Source\j_InstanceCLogTypeDBTaskManagerSAPEAV1XZ_140009494.cpp" />
    <ClCompile Include="Source\j_InstanceCTSingletonVCCashDBWorkManagerSAPEAVCCas_140009971.cpp" />
    <ClCompile Include="Source\j_InstanceCUnmannedTraderGroupItemInfoTableSAPEAV1_1400086A2.cpp" />
    <ClCompile Include="Source\j_InstanceCUnmannedTraderUserInfoTableSAPEAV1XZ_14000CAAE.cpp" />
    <ClCompile Include="Source\j_IsCashDBDSNSettedCNationSettingDataQEAA_NXZ_14001231E.cpp" />
    <ClCompile Include="Source\j_IsCashDBDSNSettedCNationSettingManagerQEAA_NXZ_1400119FF.cpp" />
    <ClCompile Include="Source\j_IsCashDBInitCNationSettingDataQEAA_NXZ_140005D58.cpp" />
    <ClCompile Include="Source\j_IsCashDBInitCNationSettingManagerQEAA_NXZ_140008FBC.cpp" />
    <ClCompile Include="Source\j_IsCashDBUseExtRefCNationSettingDataQEAA_NXZ_140007414.cpp" />
    <ClCompile Include="Source\j_IsCashDBUseExtRefCNationSettingManagerQEAA_NXZ_14000739C.cpp" />
    <ClCompile Include="Source\j_IsCombineData_ITEMCOMBINE_DB_BASEQEAA_NXZ_140008FA8.cpp" />
    <ClCompile Include="Source\j_IsConectionActiveCRFNewDatabaseQEAA_NXZ_140012AAD.cpp" />
    <ClCompile Include="Source\j_IsContPushBeforeCUserDBQEAAPEAU_AVATOR_DATAXZ_1400074A5.cpp" />
    <ClCompile Include="Source\j_IsDbUpdateRFEventBaseUEAA_NKZ_14000E78C.cpp" />
    <ClCompile Include="Source\j_IsDbUpdateRFEvent_ClassRefineUEAA_NKZ_140011D42.cpp" />
    <ClCompile Include="Source\j_IsDeleteItem_PCBANG_FAVOR_ITEM_DB_BASEQEAA_NAEAU_14000C7A2.cpp" />
    <ClCompile Include="Source\j_IsEmpty_LIST_TRADE_DB_BASEQEAA_NXZ_1400098C7.cpp" />
    <ClCompile Include="Source\j_IsExistGroupIDCUnmannedTraderClassInfoTableTypeU_14000E7F5.cpp" />
    <ClCompile Include="Source\j_IsExistGroupIDCUnmannedTraderGroupItemInfoTableQ_14000451B.cpp" />
    <ClCompile Include="Source\j_IsFilled_LIST_BUDDY_DB_BASEQEAA_NXZ_140003369.cpp" />
    <ClCompile Include="Source\j_IsFilled_LIST_SFCONT_DB_BASEQEAA_NXZ_140011AFE.cpp" />
    <ClCompile Include="Source\j_IsInitializedCLogTypeDBTaskManagerQEAA_NXZ_14000A961.cpp" />
    <ClCompile Include="Source\j_IsInitializedDL_FixedBasePrecomputationImplUECPP_14000367F.cpp" />
    <ClCompile Include="Source\j_IsNULLCashDbWorkerQEAA_NXZ_14000DBCF.cpp" />
    <ClCompile Include="Source\j_IsRangePerMastery_STAT_DB_BASESA_NEEZ_14000B456.cpp" />
    <ClCompile Include="Source\j_IsReturnPostUpdateCUserDBQEAA_NXZ_14000193D.cpp" />
    <ClCompile Include="Source\j_IsSQLValidStringYA_NPEBDZ_140008346.cpp" />
    <ClCompile Include="Source\j_IsTableOpenCRecordDataQEAA_NXZ_1400023C9.cpp" />
    <ClCompile Include="Source\j_IsValidIDCUnmannedTraderClassInfoTableTypeIEAA_N_14000EABB.cpp" />
    <ClCompile Include="Source\j_IsVotable_suggested_matterQEAA_NKZ_140008D5A.cpp" />
    <ClCompile Include="Source\j_is_private_itemAP_BatterySlotAEAA_NPEAU_db_con_S_14000858A.cpp" />
    <ClCompile Include="Source\j_LoadAllGuildDataCGuildRankingAEAA_NPEAU_worlddb__14000D03A.cpp" />
    <ClCompile Include="Source\j_LoadBossAccmulationWinRateCRaceBossWinRateQEAAEP_14000F02E.cpp" />
    <ClCompile Include="Source\j_LoadBossCurrentWinRateCRaceBossWinRateQEAA_NXZ_140009818.cpp" />
    <ClCompile Include="Source\j_LoadCCheckSumCharacAccountTrunkDataQEAAHPEAVCRFW_140013FBB.cpp" />
    <ClCompile Include="Source\j_LoadCCheckSumGuildDataQEAAHPEAVCRFWorldDatabaseA_140013944.cpp" />
    <ClCompile Include="Source\j_LoadCUnmannedTraderControllerQEAA_NGKAEAU_TRADE__140007856.cpp" />
    <ClCompile Include="Source\j_LoadCUnmannedTraderUserInfoQEAA_NEGKAEAU_TRADE_D_14001348F.cpp" />
    <ClCompile Include="Source\j_LoadCUnmannedTraderUserInfoTableQEAA_NEGKAEAU_TR_1400033CD.cpp" />
    <ClCompile Include="Source\j_LoadDatabaseAutoMineMachineQEAA_NPEAU_DB_LOAD_AU_140009DD6.cpp" />
    <ClCompile Include="Source\j_LoadDatabaseCandidateMgrQEAA_NXZ_140006442.cpp" />
    <ClCompile Include="Source\j_LoadDBCHonorGuildQEAA_NXZ_14000FEC5.cpp" />
    <ClCompile Include="Source\j_LoadDBCompleteAutominePersonalQEAAXXZ_140005F92.cpp" />
    <ClCompile Include="Source\j_LoadDBCRaceBossWinRateQEAA_NXZ_14000873D.cpp" />
    <ClCompile Include="Source\j_LoadDBKey_ANIMUSKEYQEAAXEZ_1400020C2.cpp" />
    <ClCompile Include="Source\j_LoadDBKey_COMBINEKEYQEAAXHZ_1400049B2.cpp" />
    <ClCompile Include="Source\j_LoadDBKey_EMBELLKEYQEAAXHZ_140013C46.cpp" />
    <ClCompile Include="Source\j_LoadDBKey_EQUIPKEYQEAAXFZ_140009F25.cpp" />
    <ClCompile Include="Source\j_LoadDBKey_FORCEKEYQEAAXHZ_14000BBF4.cpp" />
    <ClCompile Include="Source\j_LoadDBKey_INVENKEYQEAAXHZ_14000D76A.cpp" />
    <ClCompile Include="Source\j_LoadDBKey_LINKKEYQEAAXFZ_14000FBBE.cpp" />
    <ClCompile Include="Source\j_LoadDB_CombineResultItemCombineMgrQEAAEPEAU_comb_140008210.cpp" />
    <ClCompile Include="Source\j_LoadDummyPositionCDummyPosTableQEAA_NPEAD0Z_140013DA4.cpp" />
    <ClCompile Include="Source\j_LoadGreetingMsgCRFWorldDatabaseQEAA_NPEAD0000000_14000C8FB.cpp" />
    <ClCompile Include="Source\j_LoadPrevTableCWeeklyGuildRankManagerAEAA_NPEADAE_140009E53.cpp" />
    <ClCompile Include="Source\j_LoadRecordDataCRecordDataAEAA_NPEAXPEADZ_140007013.cpp" />
    <ClCompile Include="Source\j_LoadRecordHeaderCRecordDataAEAA_NPEAXPEADZ_14000A1D2.cpp" />
    <ClCompile Include="Source\j_LoadXMLCUnmannedTraderClassInfoTableCodeTypeUEAA_140013304.cpp" />
    <ClCompile Include="Source\j_LoadXMLCUnmannedTraderClassInfoTableTypeUEAA_NPE_1400024FF.cpp" />
    <ClCompile Include="Source\j_Load_dbCGuildRoomSystemQEAA_NXZ_140012882.cpp" />
    <ClCompile Include="Source\j_Lobby_Account_CompleteCMainThreadQEAAXPEAU_DB_QR_1400042FF.cpp" />
    <ClCompile Include="Source\j_Lobby_Char_CompleteCUserDBQEAAXEZ_140006F46.cpp" />
    <ClCompile Include="Source\j_Lobby_Char_RequestCUserDBQEAA_NXZ_14000122B.cpp" />
    <ClCompile Include="Source\j_LogCLogTypeDBTaskManagerAEAAXPEADZZ_140012B66.cpp" />
    <ClCompile Include="Source\j_LogCRFNewDatabaseIEAAXPEADZ_14000FA8D.cpp" />
    <ClCompile Include="Source\j_LogCUnmannedTraderGroupItemInfoTableAEAAXPEADZZ_1400012C6.cpp" />
    <ClCompile Include="Source\j_LogCUnmannedTraderUserInfoTableAEAAXPEADZZ_140011536.cpp" />
    <ClCompile Include="Source\j_LogoutCBillingIDUEAAXPEAVCUserDBZ_140013E5D.cpp" />
    <ClCompile Include="Source\j_LogoutCBillingJPUEAAXPEAVCUserDBZ_14000BC0D.cpp" />
    <ClCompile Include="Source\j_LogoutCBillingManagerQEAAXPEAVCUserDBZ_14000BB4A.cpp" />
    <ClCompile Include="Source\j_LogoutCBillingNULLUEAAXPEAVCUserDBZ_1400139EE.cpp" />
    <ClCompile Include="Source\j_LogoutCBillingUEAAXPEAVCUserDBZ_14000EFD9.cpp" />
    <ClCompile Include="Source\j_LogOutCUnmannedTraderUserInfoTableQEAAXGKZ_140007545.cpp" />
    <ClCompile Include="Source\j_Logout_Account_CompleteCMainThreadQEAAXPEAU_DB_Q_14000E0C0.cpp" />
    <ClCompile Include="Source\j_LoopCLogTypeDBTaskManagerQEAAXXZ_14000C504.cpp" />
    <ClCompile Include="Source\j_MakeHashCRecordDataSAKPEBDHZ_140007F4A.cpp" />
    <ClCompile Include="Source\j_MakeHashTableCRecordDataQEAA_NHHPEADZ_14000129E.cpp" />
    <ClCompile Include="Source\j_MakeLimitItemUpdateQueryCItemStoreManagerQEAAXKE_140013EBC.cpp" />
    <ClCompile Include="Source\j_MakeLinkTableTimeItemQEAA_NPEADHZ_14000E68D.cpp" />
    <ClCompile Include="Source\j_MakeLootYAPEAU_db_con_STORAGE_LISTEGZ_14000D922.cpp" />
    <ClCompile Include="Source\j_MakeNewItemsItemCombineMgrIEAAEPEAU_ITEMCOMBINE__14000A8DF.cpp" />
    <ClCompile Include="Source\j_make_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_con__140011E41.cpp" />
    <ClCompile Include="Source\j_max_sizeallocatorPEAVCLogTypeDBTaskstdQEBA_KXZ_140012C24.cpp" />
    <ClCompile Include="Source\j_max_sizevectorPEAVCLogTypeDBTaskVallocatorPEAVCL_140006771.cpp" />
    <ClCompile Include="Source\j_ModifyPriceCUnmannedTraderUserInfoTableQEAAXGEPE_140003F3F.cpp" />
    <ClCompile Include="Source\j_OnLButtonDblClkCDisplayViewIEAAXIVCPointZ_1400134E4.cpp" />
    <ClCompile Include="Source\j_OnLoop_StaticCUserDBSAXXZ_140012CE7.cpp" />
    <ClCompile Include="Source\j_ParamInitCUserDBQEAAXXZ_14000A380.cpp" />
    <ClCompile Include="Source\j_personal_amine_installCMgrAvatorItemHistoryQEAAX_140009034.cpp" />
    <ClCompile Include="Source\j_personal_amine_uninstallCMgrAvatorItemHistoryQEA_14000F542.cpp" />
    <ClCompile Include="Source\j_popUtablelua_tinkerlua_tinkerYAAUtable0PEAUlua_S_14000CE96.cpp" />
    <ClCompile Include="Source\j_post_getpresentCMgrAvatorItemHistoryQEAAXPEADKPE_140013E21.cpp" />
    <ClCompile Include="Source\j_post_returnCMgrAvatorItemHistoryQEAAXPEADKPEAU_d_14000940D.cpp" />
    <ClCompile Include="Source\j_price_auto_tradeCMgrAvatorItemHistoryQEAAXHKPEAU_1400025A4.cpp" />
    <ClCompile Include="Source\j_ProcCompleteCLogTypeDBTaskManagerAEAAXXZ_14000C194.cpp" />
    <ClCompile Include="Source\j_ProcThreadCLogTypeDBTaskManagerCAXPEAXZ_140011531.cpp" />
    <ClCompile Include="Source\j_PushCLogTypeDBTaskManagerQEAA_NEPEADGZ_14000EDA4.cpp" />
    <ClCompile Include="Source\j_PushCTalkCrystalCombineManagerIEAAEPEAU_db_con_S_140012AF3.cpp" />
    <ClCompile Include="Source\j_PushDQSDataCMainThreadQEAAPEAU_DB_QRY_SYN_DATAKP_14000386E.cpp" />
    <ClCompile Include="Source\j_PushList_TOWER_PARAMQEAA_NPEAU_db_con_STORAGE_LI_14000E967.cpp" />
    <ClCompile Include="Source\j_PushRenamePotionDBLogCPotionMgrAEAAXPEADZ_140006CC6.cpp" />
    <ClCompile Include="Source\j_PushSettlementOwnerDBLogCWeeklyGuildRankManagerA_140013480.cpp" />
    <ClCompile Include="Source\j_PushTaskCashDbWorkerQEAA_NHPEAE_KZ_140006A00.cpp" />
    <ClCompile Include="Source\j_PushTaskCCashDBWorkManagerQEAA_NHPEAE_KZ_1400083FF.cpp" />
    <ClCompile Include="Source\j_PushUpdateBuyRollBackCUnmannedTraderUserInfoTabl_14000BF5F.cpp" />
    <ClCompile Include="Source\j_pushUtablelua_tinkerlua_tinkerYAXPEAUlua_StateUt_14000AC6D.cpp" />
    <ClCompile Include="Source\j_push_backvectorPEAVCLogTypeDBTaskVallocatorPEAVC_140001C71.cpp" />
    <ClCompile Include="Source\j_Push_talk_crystal_matrial_combine_nodeQEAA_NPEAU_1400017E9.cpp" />
    <ClCompile Include="Source\j_QryCaseAddpvppointCMainThreadQEAAXPEAU_DB_QRY_SY_14000933B.cpp" />
    <ClCompile Include="Source\j_ReadBuddhaEventInfoCExchangeEventQEAAXXZ_14000A79A.cpp" />
    <ClCompile Include="Source\j_ReadRecordCEventLootTableQEAA_NXZ_14000D1B1.cpp" />
    <ClCompile Include="Source\j_ReadRecordCItemLootTableQEAA_NPEADPEAVCRecordDat_1400077D4.cpp" />
    <ClCompile Include="Source\j_ReadRecordCItemUpgradeTableQEAA_NPEADPEAVCRecord_14000982C.cpp" />
    <ClCompile Include="Source\j_ReadRecordCOreCuttingTableQEAA_NPEADPEAVCRecordD_140012779.cpp" />
    <ClCompile Include="Source\j_ReadRecordCRecordDataQEAA_NPEADK0Z_140002FEF.cpp" />
    <ClCompile Include="Source\j_ReadRecord_ExCRecordDataQEAA_NPEAD0K0Z_140009548.cpp" />
    <ClCompile Include="Source\j_ReadScriptCMapDataTableQEAA_NPEADZ_140004408.cpp" />
    <ClCompile Include="Source\j_readUtablelua_tinkerlua_tinkerYAAUtable0PEAUlua__14000C2CA.cpp" />
    <ClCompile Include="Source\j_Rebirth_BaseCRFWorldDatabaseQEAA_NKPEADZ_14000203B.cpp" />
    <ClCompile Include="Source\j_Reged_Avator_CompleteCMainThreadQEAAXPEAU_DB_QRY_14000628F.cpp" />
    <ClCompile Include="Source\j_Reged_Char_CompleteCUserDBQEAAXEPEAU_REGEDPEAU_N_14000596B.cpp" />
    <ClCompile Include="Source\j_Reged_Char_RequestCUserDBQEAA_NXZ_14000BAD7.cpp" />
    <ClCompile Include="Source\j_RegistCheatEndRecordCNationSettingFactoryIEAAXPE_140003210.cpp" />
    <ClCompile Include="Source\j_RegistCheatTableCNationSettingFactoryIEAA_NPEAVC_14000750E.cpp" />
    <ClCompile Include="Source\j_RegistCheatTableOnlyInternalCNationSettingFactor_140010C76.cpp" />
    <ClCompile Include="Source\j_RegistCheatTableUnionCNationSettingFactoryIEAA_N_140003DD7.cpp" />
    <ClCompile Include="Source\j_RegistCodeCNationCodeStrTableAEAAHXZ_140009DDB.cpp" />
    <ClCompile Include="Source\j_RegistCUnmannedTraderUserInfoTableQEAAXGEPEAU_a__140008AD0.cpp" />
    <ClCompile Include="Source\j_Regist_UnmannedTraderSingleItemCRFWorldDatabaseQ_14000FCB8.cpp" />
    <ClCompile Include="Source\j_reg_auto_tradeCMgrAvatorItemHistoryQEAAXHKPEAU_d_14000CAD1.cpp" />
    <ClCompile Include="Source\j_ReleaseCashDbWorkerUEAAXXZ_140008701.cpp" />
    <ClCompile Include="Source\j_ReleaseCCashDbWorkerGBUEAAXXZ_140010744.cpp" />
    <ClCompile Include="Source\j_ReleaseCCashDbWorkerNULLUEAAXXZ_14000AF97.cpp" />
    <ClCompile Include="Source\j_ReleaseCCashDbWorkerRUUEAAXXZ_140011BCB.cpp" />
    <ClCompile Include="Source\j_Release_EMBELLISH_LIST_EQUIP_DB_BASEQEAA_NXZ_14000E21E.cpp" />
    <ClCompile Include="Source\j_Release_LIST_ANIMUS_DB_BASEQEAA_NXZ_1400126F7.cpp" />
    <ClCompile Include="Source\j_Release_LIST_FORCE_DB_BASEQEAA_NXZ_14000821F.cpp" />
    <ClCompile Include="Source\j_Release_LIST_INVEN_DB_BASEQEAA_NXZ_14000C8CE.cpp" />
    <ClCompile Include="Source\j_Release_LIST_PERSONALAMINE_INVEN_DB_BASEQEAA_NXZ_140011441.cpp" />
    <ClCompile Include="Source\j_Release_LIST_TRUNK_DB_BASEQEAA_NXZ_14000E2AF.cpp" />
    <ClCompile Include="Source\j_request_db_queryAutoMineMachineMngQEAAEPEADZ_140007D3D.cpp" />
    <ClCompile Include="Source\j_request_queryAutominePersonalMgrQEAAHPEADZ_14000A7BD.cpp" />
    <ClCompile Include="Source\j_ReRangeClientIndexCUserDBSAXPEAU_AVATOR_DATAZ_14000E13D.cpp" />
    <ClCompile Include="Source\j_ReRegistCUnmannedTraderUserInfoTableQEAAXGEPEAU__14001381D.cpp" />
    <ClCompile Include="Source\j_reservevectorPEAVCLogTypeDBTaskVallocatorPEAVCLo_14001175C.cpp" />
    <ClCompile Include="Source\j_ReSetOldDataLoad_CUTTING_DB_BASEQEAAXXZ_140013D2C.cpp" />
    <ClCompile Include="Source\j_reset_100_per_random_tableQEAAXXZ_14000F439.cpp" />
    <ClCompile Include="Source\j_result_db_queryAutoMineMachineMngQEAAXEPEADZ_14001182E.cpp" />
    <ClCompile Include="Source\j_result_queryAutominePersonalMgrQEAAXEPEADZ_14000C1A8.cpp" />
    <ClCompile Include="Source\j_reward_add_itemCMgrAvatorItemHistoryQEAAXHPEADPE_14000E6F1.cpp" />
    <ClCompile Include="Source\j_re_reg_auto_tradeCMgrAvatorItemHistoryQEAAXHKPEA_14000CEFA.cpp" />
    <ClCompile Include="Source\j_RollbackTransactionCRFNewDatabaseQEAA_NXZ_14000DB93.cpp" />
    <ClCompile Include="Source\j_SearchAvatorWithCMSYAPEAVCUserDBPEAV1HPEADZ_14000160E.cpp" />
    <ClCompile Include="Source\j_SearchAvatorWithNameYAPEAVCUserDBPEAV1HPEADZ_14000FA10.cpp" />
    <ClCompile Include="Source\j_SearchCUnmannedTraderUserInfoTableQEAAXGEPEAU_un_14000B7A3.cpp" />
    <ClCompile Include="Source\j_SelectAllGuildSerialCRFWorldDatabaseQEAA_NPEAK0Z_14000C806.cpp" />
    <ClCompile Include="Source\j_SelectAllGuildSerialGradeCRFWorldDatabaseQEAA_NP_140001091.cpp" />
    <ClCompile Include="Source\j_SelectCleanUpCRFNewDatabaseQEAAXPEADZ_1400101A4.cpp" />
    <ClCompile Include="Source\j_SelectSearchListCUnmannedTraderControllerQEAAEPE_140008EBD.cpp" />
    <ClCompile Include="Source\j_SelectTotalRecordNumCItemStoreManagerQEAA_NPEAKZ_1400090FC.cpp" />
    <ClCompile Include="Source\j_SelectUsedRecordNumCItemStoreManagerQEAA_NPEAKZ_14000E895.cpp" />
    <ClCompile Include="Source\j_Select_AccountByAvatorNameCRFWorldDatabaseQEAA_N_140013674.cpp" />
    <ClCompile Include="Source\j_Select_AccountItemChargeCRFWorldDatabaseQEAA_NKP_14000B55A.cpp" />
    <ClCompile Include="Source\j_Select_AccountItemCharge_ExtendCRFWorldDatabaseQ_140007252.cpp" />
    <ClCompile Include="Source\j_Select_AccountSerialCRFWorldDatabaseQEAA_NPEAD0P_140008A5D.cpp" />
    <ClCompile Include="Source\j_Select_AccountTrunkCRFWorldDatabaseQEAAEKEPEAU_w_14000FF5B.cpp" />
    <ClCompile Include="Source\j_Select_AccountTrunkExtendCRFWorldDatabaseQEAAEKP_1400070F9.cpp" />
    <ClCompile Include="Source\j_Select_AllGuildDataCRFWorldDatabaseQEAA_NPEAU_wo_14000515A.cpp" />
    <ClCompile Include="Source\j_Select_AllGuildNumCRFWorldDatabaseQEAAGXZ_140012963.cpp" />
    <ClCompile Include="Source\j_select_amine_personalCRFWorldDatabaseQEAAHKPEAU__14000D099.cpp" />
    <ClCompile Include="Source\j_select_amine_personalCRFWorldDatabaseQEAAHKZ_14000A024.cpp" />
    <ClCompile Include="Source\j_Select_AnimusDataCRFWorldDatabaseQEAAEKEPEANZ_140013D31.cpp" />
    <ClCompile Include="Source\j_Select_ArrangeInfoCRFWorldDatabaseQEAA_NKZ_140007121.cpp" />
    <ClCompile Include="Source\j_select_atrade_taxrateCRFWorldDatabaseQEAAHEPEADA_140011AB8.cpp" />
    <ClCompile Include="Source\j_select_automineCRFWorldDatabaseQEAAHPEAU_DB_LOAD_14000B663.cpp" />
    <ClCompile Include="Source\j_Select_Avator_CompleteCMainThreadQEAAXPEAU_DB_QR_140012A35.cpp" />
    <ClCompile Include="Source\j_Select_BossCryMsgCRFWorldDatabaseQEAAEKPEAU_worl_14000D9EF.cpp" />
    <ClCompile Include="Source\j_Select_BuddyCRFWorldDatabaseQEAAEKPEAU_worlddb_b_14000DBA7.cpp" />
    <ClCompile Include="Source\j_Select_CashLimSaleCRFWorldDatabaseQEAAHPEAU_worl_140003116.cpp" />
    <ClCompile Include="Source\j_Select_CharNumInWorldCRFWorldDatabaseQEAAEKAEAEZ_140008CE2.cpp" />
    <ClCompile Include="Source\j_Select_Char_CompleteCUserDBQEAAXEPEAU_AVATOR_DAT_140002612.cpp" />
    <ClCompile Include="Source\j_Select_Char_RequestCUserDBQEAA_NEZ_140004EA8.cpp" />
    <ClCompile Include="Source\j_Select_CheckGreetRecordCRFWorldDatabaseQEAAEHZ_14000DAB2.cpp" />
    <ClCompile Include="Source\j_Select_CheckSumValueCRFWorldDatabaseQEAA_NKPEAKZ_14000146A.cpp" />
    <ClCompile Include="Source\j_Select_ChracterSerialRaceCRFWorldDatabaseQEAA_NP_140002A45.cpp" />
    <ClCompile Include="Source\j_Select_ClearHonorGuildCRFWorldDatabaseQEAAHEAEAK_140012242.cpp" />
    <ClCompile Include="Source\j_Select_Economy_HistoryCRFWorldDatabaseQEAAEPEAU__1400034C2.cpp" />
    <ClCompile Include="Source\j_Select_Equal_DeleteName_NoArrangedCRFWorldDataba_14000DC0B.cpp" />
    <ClCompile Include="Source\j_Select_Equal_NameCRFWorldDatabaseQEAA_NPEADZ_14000BEE2.cpp" />
    <ClCompile Include="Source\j_Select_Exist_EconomyCRFWorldDatabaseQEAAEKPEAU_w_14000A43E.cpp" />
    <ClCompile Include="Source\j_Select_GetCharSerialByNameRaceCRFWorldDatabaseQE_140003B84.cpp" />
    <ClCompile Include="Source\j_Select_GodenBoxItemCRFWorldDatabaseQEAAHPEAU_wor_140001B18.cpp" />
    <ClCompile Include="Source\j_Select_GuildDataCRFWorldDatabaseQEAA_NKPEAU__gui_14000C69E.cpp" />
    <ClCompile Include="Source\j_Select_GuildMasterLastConnCRFWorldDatabaseQEAAEK_140003E72.cpp" />
    <ClCompile Include="Source\j_Select_GuildMemberDataCRFWorldDatabaseQEAA_NGKPE_1400092F5.cpp" />
    <ClCompile Include="Source\j_Select_GuildMoneyIODataCRFWorldDatabaseQEAA_NKPE_1400064AB.cpp" />
    <ClCompile Include="Source\j_Select_GuildRoomInfoCRFWorldDatabaseQEAA_NPEAU_g_14000B2F3.cpp" />
    <ClCompile Include="Source\j_Select_GuildSerialCRFWorldDatabaseQEAA_NPEADPEAK_140007284.cpp" />
    <ClCompile Include="Source\j_Select_HonorGuildCRFWorldDatabaseQEAAHEPEAU_guil_14000DDC3.cpp" />
    <ClCompile Include="Source\j_Select_InvenCRFWorldDatabaseQEAAEKGPEAU_worlddb__14000BA00.cpp" />
    <ClCompile Include="Source\j_Select_IsValidCharCRFWorldDatabaseQEAAHKAEAKZ_140001690.cpp" />
    <ClCompile Include="Source\j_Select_ItemChargeCRFWorldDatabaseQEAA_NKPEAEPEAK_140004B79.cpp" />
    <ClCompile Include="Source\j_Select_ItemCombineExCRFWorldDatabaseQEAAEKPEAU_w_140012058.cpp" />
    <ClCompile Include="Source\j_Select_LimitInfoCRFWorldDatabaseQEAAEPEAE_KZ_1400058BC.cpp" />
    <ClCompile Include="Source\j_Select_LimitItemEmptyRecordCRFWorldDatabaseQEAAE_140013700.cpp" />
    <ClCompile Include="Source\j_Select_LimitItemUsedRecordCRFWorldDatabaseQEAAEE_140012C8D.cpp" />
    <ClCompile Include="Source\j_Select_Limit_Run_RecordCRFWorldDatabaseQEAAEXZ_140006DFC.cpp" />
    <ClCompile Include="Source\j_Select_MacroDataCRFWorldDatabaseQEAAEKPEAU_AIOC__1400080D0.cpp" />
    <ClCompile Include="Source\j_Select_NextHourDateCRFNewDatabaseQEAA_NEPEADZ_14000E336.cpp" />
    <ClCompile Include="Source\j_Select_NpcDataCRFWorldDatabaseQEAAEKPEAKZ_14000FBFA.cpp" />
    <ClCompile Include="Source\j_Select_NpcQuest_HistoryCRFWorldDatabaseQEAAEKPEA_140001B77.cpp" />
    <ClCompile Include="Source\j_Select_OldVerPatriarchGroupCRFWorldDatabaseQEAAH_140009F4D.cpp" />
    <ClCompile Include="Source\j_Select_OreCuttingCRFWorldDatabaseQEAAHKPEAU_worl_14000A6A5.cpp" />
    <ClCompile Include="Source\j_Select_PatriarchCandidateCRFWorldDatabaseQEAAEKE_14000B56E.cpp" />
    <ClCompile Include="Source\j_Select_PatriarchCommCountCRFWorldDatabaseQEAAHKP_140007261.cpp" />
    <ClCompile Include="Source\j_Select_PatriarchCommCRFWorldDatabaseQEAAHKPEAU_p_1400011E0.cpp" />
    <ClCompile Include="Source\j_Select_PatriarchElectStateCRFWorldDatabaseQEAAHP_140007374.cpp" />
    <ClCompile Include="Source\j_Select_PatriarchGroupCRFWorldDatabaseQEAAEEPEAU__140004ACA.cpp" />
    <ClCompile Include="Source\j_Select_PatriarchRefundCountCRFWorldDatabaseQEAAH_1400017A8.cpp" />
    <ClCompile Include="Source\j_Select_PatriarchVotedCRFWorldDatabaseQEAAHEKAEA__14000C1C1.cpp" />
    <ClCompile Include="Source\j_Select_PatriarchWinCntCRFWorldDatabaseQEAAHEKAEA_1400082AB.cpp" />
    <ClCompile Include="Source\j_Select_PcBangFavorItemCRFWorldDatabaseQEAAHKPEAU_140004359.cpp" />
    <ClCompile Include="Source\j_Select_PostContentCRFWorldDatabaseQEAAEKPEADHZ_14000BB36.cpp" />
    <ClCompile Include="Source\j_Select_PostRegistryDataCRFWorldDatabaseQEAAEKPEA_14000CBD5.cpp" />
    <ClCompile Include="Source\j_Select_PostStorageEmptyRecordCRFWorldDatabaseQEA_1400069A6.cpp" />
    <ClCompile Include="Source\j_Select_PostStorageEmptyRecordSerialCRFWorldDatab_14000F9C0.cpp" />
    <ClCompile Include="Source\j_Select_PostStorageListCRFWorldDatabaseQEAAEKPEAU_1400094D0.cpp" />
    <ClCompile Include="Source\j_Select_PostStorageRecordCheckCRFWorldDatabaseQEA_140005254.cpp" />
    <ClCompile Include="Source\j_Select_PotionDelayCRFWorldDatabaseQEAAHKPEAU_wor_14000302B.cpp" />
    <ClCompile Include="Source\j_Select_PrimiumPlayTimeCRFWorldDatabaseQEAAHKAEAU_1400081B6.cpp" />
    <ClCompile Include="Source\j_Select_PunishmentCountCRFWorldDatabaseQEAAHEKPEA_1400020E0.cpp" />
    <ClCompile Include="Source\j_Select_PunishmentCRFWorldDatabaseQEAAHKPEAK0Z_140007603.cpp" />
    <ClCompile Include="Source\j_Select_PvpOrderViewInfoCRFWorldDatabaseQEAAHKAEA_140008139.cpp" />
    <ClCompile Include="Source\j_Select_PvpPointGuildRankCRFWorldDatabaseQEAAEPEA_14000E296.cpp" />
    <ClCompile Include="Source\j_Select_PvpPointLimitInfoCRFWorldDatabaseQEAAEKAE_14000EBBA.cpp" />
    <ClCompile Include="Source\j_Select_PvpRankInfoCRFWorldDatabaseQEAAEEPEADPEAU_1400107FD.cpp" />
    <ClCompile Include="Source\j_Select_PvpRateCRFWorldDatabaseQEAAEKPEADPEAKPEAG_140009589.cpp" />
    <ClCompile Include="Source\j_Select_QuestCRFWorldDatabaseQEAAEKPEAU_worlddb_q_14000C1AD.cpp" />
    <ClCompile Include="Source\j_Select_RaceBossAccumulationWinRateCRFWorldDataba_14000700E.cpp" />
    <ClCompile Include="Source\j_Select_RaceBossCurrentWinRateCRFWorldDatabaseQEA_14000224D.cpp" />
    <ClCompile Include="Source\j_Select_RegeAvator_For_Lobby_LogoutCRFWorldDataba_140012864.cpp" />
    <ClCompile Include="Source\j_Select_ReturnPostCRFWorldDatabaseQEAAEKKPEAU_ret_14000AFB0.cpp" />
    <ClCompile Include="Source\j_Select_RFEvent_ClassRefineCRFWorldDatabaseQEAAHK_140006596.cpp" />
    <ClCompile Include="Source\j_Select_SFDelayInfoCRFWorldDatabaseQEAAEKPEAU_wor_140004386.cpp" />
    <ClCompile Include="Source\j_Select_Start_NpcQuest_HistoryCRFWorldDatabaseQEA_140007365.cpp" />
    <ClCompile Include="Source\j_Select_Start_NpcQuest_History_CountCRFWorldDatab_140001E4C.cpp" />
    <ClCompile Include="Source\j_Select_StoreLimitItemCRFWorldDatabaseQEAAEPEAU_q_140005CBD.cpp" />
    <ClCompile Include="Source\j_Select_TakeItemCRFWorldDatabaseQEAAEKPEAU_worldd_140005FF1.cpp" />
    <ClCompile Include="Source\j_Select_TotalGuildRankCRFWorldDatabaseQEAAEPEADPE_140012B8E.cpp" />
    <ClCompile Include="Source\j_Select_TotalRecordNumCRFWorldDatabaseQEAAEPEAKZ_14000C29D.cpp" />
    <ClCompile Include="Source\j_Select_TradeCRFWorldDatabaseQEAAEEKEPEAU_worlddb_14000E444.cpp" />
    <ClCompile Include="Source\j_Select_TrunkMoneyCRFWorldDatabaseQEAA_NKPEANZ_140008F17.cpp" />
    <ClCompile Include="Source\j_Select_UnitCRFWorldDatabaseQEAAEKPEAU_worlddb_un_1400077CF.cpp" />
    <ClCompile Include="Source\j_Select_UnitDataCRFWorldDatabaseQEAAEKPEANZ_14000ABB4.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderBuySingleItemInfoCRFWorldDa_14000F62D.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderItemRecordCntByStateCRFWorl_1400064BF.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderItemStateCRFWorldDatabaseQE_14000CC3E.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderItemStateInfoCntCRFWorldDat_140013507.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderItemStateInfoCRFWorldDataba_14000A065.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderRegisterCRFWorldDatabaseQEA_1400099A3.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderReservedScheduleCRFWorldDat_14000D27E.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderSearchGroupTotalRowCountCRF_14000DCE7.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderSearchPageInfoCRFWorldDatab_1400102E4.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderSellInfoCRFWorldDatabaseQEA_140010DB1.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderSingleItemBottomSerialCRFWo_140009566.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderSingleItemEmptyRecordCntCRF_14000BAD2.cpp" />
    <ClCompile Include="Source\j_Select_UnmannedTraderSingleItemEmptyRecordSerial_140006E92.cpp" />
    <ClCompile Include="Source\j_Select_UsedLimitItemRecordNumCRFWorldDatabaseQEA_140006780.cpp" />
    <ClCompile Include="Source\j_Select_UserCountInfoCRFWorldDatabaseQEAA_NPEAD0P_140006122.cpp" />
    <ClCompile Include="Source\j_Select_UserInterfaceCRFWorldDatabaseQEAAEKPEAU_w_140004A2F.cpp" />
    <ClCompile Include="Source\j_Select_utSellWaitItems_SalesTotalsCRFWorldDataba_1400031C0.cpp" />
    <ClCompile Include="Source\j_Select_WaitItemCRFWorldDatabaseQEAAEKPEAU_worldd_14000D1ED.cpp" />
    <ClCompile Include="Source\j_Select_WeeklyGuildRankOwnerGuildCRFWorldDatabase_140008BFC.cpp" />
    <ClCompile Include="Source\j_self_cancel_auto_tradeCMgrAvatorItemHistoryQEAAX_140004E3A.cpp" />
    <ClCompile Include="Source\j_ServiceLogCUnmannedTraderUserInfoTableAEAAXPEADZ_140003E40.cpp" />
    <ClCompile Include="Source\j_SetActPointCUserDBQEAAXEKZ_140004B1F.cpp" />
    <ClCompile Include="Source\j_SetAutoCommitModeCRFNewDatabaseQEAAX_NZ_140009D36.cpp" />
    <ClCompile Include="Source\j_SetBillingDataCUserDBQEAAXPEADFJPEAU_SYSTEMTIMEZ_140007C6B.cpp" />
    <ClCompile Include="Source\j_SetBillingDataCUserDBQEAAXPEAU_BILLING_INFOZ_14001219D.cpp" />
    <ClCompile Include="Source\j_SetBillingNoLogoutCUserDBQEAAX_NZ_140013EAD.cpp" />
    <ClCompile Include="Source\j_SetCahsDBUseExtRefFlagCNationSettingDataQEAAXXZ_140003076.cpp" />
    <ClCompile Include="Source\j_SetCashDBDSNCNationSettingDataQEAAXPEAD000KZ_140006C7B.cpp" />
    <ClCompile Include="Source\j_SetCashDBDSNCNationSettingManagerQEAAXPEAD000KZ_14000613B.cpp" />
    <ClCompile Include="Source\j_SetCashDBDSNSetFlagCNationSettingDataQEAAXXZ_14000A588.cpp" />
    <ClCompile Include="Source\j_SetCashDBInitFlagCNationSettingDataQEAAXXZ_1400108D4.cpp" />
    <ClCompile Include="Source\j_SetCashDBInitStateCNationSettingManagerQEAAXXZ_140007356.cpp" />
    <ClCompile Include="Source\j_SetChatLockCUserDBQEAAX_NZ_140005B0F.cpp" />
    <ClCompile Include="Source\j_SetCLogTypeDBTaskQEAA_NEPEADGZ_140008CC4.cpp" />
    <ClCompile Include="Source\j_SetCompleteCLogTypeDBTaskPoolQEAA_NPEAVCLogTypeD_14000BA0A.cpp" />
    <ClCompile Include="Source\j_SetCompleteCLogTypeDBTaskQEAAXXZ_140001BA9.cpp" />
    <ClCompile Include="Source\j_SetCPvpPointLimiterQEAA_NNPEAV_PVPPOINT_LIMIT_DB_14000FA1A.cpp" />
    <ClCompile Include="Source\j_SetCUnmannedTraderRegistItemInfoQEAA_NGEIAEAU_TR_140010C17.cpp" />
    <ClCompile Include="Source\j_SetDBPostDataCUserDBQEAAXHKHEH_KKK_N0Z_140004980.cpp" />
    <ClCompile Include="Source\j_SetDBSerialCGoldenBoxItemMgrQEAAXHZ_14000A722.cpp" />
    <ClCompile Include="Source\j_SetEmptyCLogTypeDBTaskPoolQEAA_NPEAVCLogTypeDBTa_140003157.cpp" />
    <ClCompile Include="Source\j_SetEmptyCLogTypeDBTaskQEAAXXZ_14000F097.cpp" />
    <ClCompile Include="Source\j_SetFeedbackSizeCipherModeBaseCryptoPPMEAAXIZ_140011801.cpp" />
    <ClCompile Include="Source\j_SetGoldBoxConsumableCHolyStoneSystemQEAAX_NZ_1400060E1.cpp" />
    <ClCompile Include="Source\j_SetGoldBoxItemIndexCGoldenBoxItemMgrQEAA_NXZ_14000D783.cpp" />
    <ClCompile Include="Source\j_SetItemType_InitCSetItemTypeQEAA_NPEAVCRecordDat_14000694C.cpp" />
    <ClCompile Include="Source\j_SetKey_LIST_SFCONT_DB_BASEQEAAXEEGEGZ_140006B4A.cpp" />
    <ClCompile Include="Source\j_SetKorTimeCRFDBItemLogQEAAXKZ_1400013C5.cpp" />
    <ClCompile Include="Source\j_SetLeftTime_LIST_SFCONT_DB_BASEQEAAXGZ_14000FD53.cpp" />
    <ClCompile Include="Source\j_SetLoadAllGuildInfoCGuildRankingAEAA_NPEAU_world_140006F91.cpp" />
    <ClCompile Include="Source\j_SetLoadInfoCUnmannedTraderUserInfoAEAA_NEKAEAU_T_1400130D9.cpp" />
    <ClCompile Include="Source\j_SetLogFileCRFNewDatabaseQEAAXPEBD0Z_1400107CB.cpp" />
    <ClCompile Include="Source\j_SetLoggerCUnmannedTraderGroupItemInfoTableQEAAXP_140013746.cpp" />
    <ClCompile Include="Source\j_SetLoggerCUnmannedTraderUserInfoTableQEAAXPEAVCL_1400072C0.cpp" />
    <ClCompile Include="Source\j_SetMemory_STORAGE_LISTQEAAXPEAU_db_con1HHHZ_14000EBA6.cpp" />
    <ClCompile Include="Source\j_SetNewDBPostDataCUserDBQEAAXHKHEPEAD000H_KKK1Z_140013CF0.cpp" />
    <ClCompile Include="Source\j_SetOrder_LIST_SFCONT_DB_BASEQEAAXEZ_140001DCF.cpp" />
    <ClCompile Include="Source\j_SetPoolPointerCEnglandBillingMgrQEAAXPEAVTaskPoo_14000D08A.cpp" />
    <ClCompile Include="Source\j_SetProcCLogTypeDBTaskPoolQEAA_NPEAVCLogTypeDBTas_140003E27.cpp" />
    <ClCompile Include="Source\j_SetRadarDelayCUserDBQEAAXKZ_140001D98.cpp" />
    <ClCompile Include="Source\j_SetRemainTimeCUserDBQEAAXJZ_140006F96.cpp" />
    <ClCompile Include="Source\j_SetRetCLogTypeDBTaskQEAAXEEZ_140011234.cpp" />
    <ClCompile Include="Source\j_SetSerialNumber_db_con_STORAGE_LISTQEAAXKZ_14000F78B.cpp" />
    <ClCompile Include="Source\j_SetStaticMember_MASTERY_PARAMSAXPEAVCRecordData0_14000C699.cpp" />
    <ClCompile Include="Source\j_SetStaticMember_WEAPON_PARAMSAXPEAVCRecordDataZ_140013B1A.cpp" />
    <ClCompile Include="Source\j_Setting_ClassCUserDBQEAA_NPEADZ_14000D89B.cpp" />
    <ClCompile Include="Source\j_SetUpdateDBDataDoNotCheckCItemStoreManagerQEAAXX_14000D7C4.cpp" />
    <ClCompile Include="Source\j_SetUseCLogTypeDBTaskQEAAXXZ_140010CE4.cpp" />
    <ClCompile Include="Source\j_SetWorldCLIDCUserDBQEAAXKPEAKZ_14000C068.cpp" />
    <ClCompile Include="Source\j_Set_EMBELLISH_LIST_EQUIP_DB_BASEQEAA_NPEBU_db_co_140012850.cpp" />
    <ClCompile Include="Source\j_Set_LIST_ANIMUS_DB_BASEQEAA_NPEBU_db_con_STORAGE_140008BCF.cpp" />
    <ClCompile Include="Source\j_Set_LIST_FORCE_DB_BASEQEAA_NPEBU_db_con_STORAGE__1400091BF.cpp" />
    <ClCompile Include="Source\j_Set_LIST_INVEN_DB_BASEQEAA_NPEBU_db_con_STORAGE__140009A2A.cpp" />
    <ClCompile Include="Source\j_Set_LIST_PERSONALAMINE_INVEN_DB_BASEQEAA_NPEBU_d_14000CB8A.cpp" />
    <ClCompile Include="Source\j_Set_LIST_TRUNK_DB_BASEQEAA_NPEBU_db_con_STORAGE__14000D477.cpp" />
    <ClCompile Include="Source\j_Set_REGEDQEAA_NEPEBU_db_con_STORAGE_LISTZ_1400012F3.cpp" />
    <ClCompile Include="Source\j_sizevectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTy_14000F5B5.cpp" />
    <ClCompile Include="Source\j_size_cashdb_setting_request_wracQEAAHXZ_14000526D.cpp" />
    <ClCompile Include="Source\j_size_check_queryQEAAHXZ_140013999.cpp" />
    <ClCompile Include="Source\j_size_DB_LOAD_AUTOMINE_MACHINEQEAAHXZ_14000B4D8.cpp" />
    <ClCompile Include="Source\j_size_pt_automine_charge_money_db_update_fail_zoc_140012FD5.cpp" />
    <ClCompile Include="Source\j_size_pt_query_appoint_zoclQEAAHXZ_14001037F.cpp" />
    <ClCompile Include="Source\j_SQLExecDirect_RetErrCodeCRFNewDatabaseQEAAEPEADZ_14000EA57.cpp" />
    <ClCompile Include="Source\j_SQLFetch_RetErrCodeCRFNewDatabaseQEAAEPEADZ_14000AB73.cpp" />
    <ClCompile Include="Source\j_SQLGetData_Binary_RetErrCodeCRFNewDatabaseQEAAEP_14000F51A.cpp" />
    <ClCompile Include="Source\j_SQLGetData_RetErrCodeCRFNewDatabaseQEAAEPEADAEAG_140002752.cpp" />
    <ClCompile Include="Source\j_StartCCashDBWorkManagerQEAAXXZ_14001299F.cpp" />
    <ClCompile Include="Source\j_StartDataBaseCRFNewDatabaseQEAA_NPEBD00Z_14000F15A.cpp" />
    <ClCompile Include="Source\j_StartFieldModeCUserDBQEAAXXZ_14001245E.cpp" />
    <ClCompile Include="Source\j_SubCompleteBuyIncreaseVesionCUnmannedTraderUserI_140003283.cpp" />
    <ClCompile Include="Source\j_SubCompleteBuyProcBuyResultCUnmannedTraderUserIn_14000F44D.cpp" />
    <ClCompile Include="Source\j_SynchINIANDDBCGoldenBoxItemMgrQEAA_NXZ_1400060AA.cpp" />
    <ClCompile Include="Source\j_TableExistCRFNewDatabaseQEAA_NPEADZ_1400107F3.cpp" />
    <ClCompile Include="Source\j_take_ground_itemCMgrAvatorItemHistoryQEAAXHEPEAU_140013967.cpp" />
    <ClCompile Include="Source\j_throw_ground_itemCMgrAvatorItemHistoryQEAAXHPEAU_140008805.cpp" />
    <ClCompile Include="Source\j_time_jade_effect_logCMgrAvatorItemHistoryQEAAXPE_140011761.cpp" />
    <ClCompile Include="Source\j_time_out_cancel_auto_tradeCMgrAvatorItemHistoryQ_1400021F8.cpp" />
    <ClCompile Include="Source\j_TotalPlayMinCheckCUserDBQEAAXXZ_140001CFD.cpp" />
    <ClCompile Include="Source\j_tradeCMgrAvatorItemHistoryQEAAXHPEAU_db_con_STOR_14000B5DC.cpp" />
    <ClCompile Include="Source\j_trans_ground_itemCMgrAvatorItemHistoryQEAAXPEAU__14000A85D.cpp" />
    <ClCompile Include="Source\j_Truncate_UnmannedTraderItemStateRecordCRFWorldDa_1400039B3.cpp" />
    <ClCompile Include="Source\j_trunk_io_itemCMgrAvatorItemHistoryQEAAXHPEAU_db__14000E59D.cpp" />
    <ClCompile Include="Source\j_trunk_swap_itemCMgrAvatorItemHistoryQEAAXHPEAU_d_140004EAD.cpp" />
    <ClCompile Include="Source\j_tuning_unitCMgrAvatorItemHistoryQEAAXHEPEAU_LIST_14000F9ED.cpp" />
    <ClCompile Include="Source\j_UILockInfo_InitCUserDBQEAAXPEADZ_14000F90C.cpp" />
    <ClCompile Include="Source\j_UILockInfo_UpdateCUserDBQEAAXPEADZ_14001136F.cpp" />
    <ClCompile Include="Source\j_unchecked_copyPEAPEAVCLogTypeDBTaskPEAPEAV1stdex_140008797.cpp" />
    <ClCompile Include="Source\j_unchecked_fill_nPEAPEAVCLogTypeDBTask_KPEAV1stde_140008A4E.cpp" />
    <ClCompile Include="Source\j_unchecked_uninitialized_copyPEAPEAVCLogTypeDBTas_140006BA9.cpp" />
    <ClCompile Include="Source\j_unchecked_uninitialized_copyV_Vector_iteratorPEA_140007BB7.cpp" />
    <ClCompile Include="Source\j_unchecked_uninitialized_fill_nPEAPEAVCLogTypeDBT_140002F40.cpp" />
    <ClCompile Include="Source\j_UpdateCCheckSumCharacAccountTrunkDataQEAA_NPEAVC_1400090AC.cpp" />
    <ClCompile Include="Source\j_UpdateCCheckSumGuildDataQEAA_NPEAVCRFWorldDataba_140003F17.cpp" />
    <ClCompile Include="Source\j_UpdateClearDanglingOwnerRecordCUnmannedTraderCon_140003305.cpp" />
    <ClCompile Include="Source\j_UpdateContUserSaveCUserDBQEAA_N_NZ_140011315.cpp" />
    <ClCompile Include="Source\j_UpdateDB_CombineResultItemCombineMgrQEAAEPEAU_co_140005344.cpp" />
    <ClCompile Include="Source\j_UpdateDisappearOwnerRecordCPostSystemManagerQEAA_140009F57.cpp" />
    <ClCompile Include="Source\j_UpdateGuildMoneyCRFWorldDatabaseQEAA_NKNNZ_14000BD7A.cpp" />
    <ClCompile Include="Source\j_UpdateInit_POSTDATA_DB_BASEQEAAXXZ_140012E36.cpp" />
    <ClCompile Include="Source\j_UpdateInit_POSTSTORAGE_DB_BASEQEAAXXZ_1400051FA.cpp" />
    <ClCompile Include="Source\j_UpdateKillerListCPvpCashPointQEAAXPEAU_PVP_ORDER_14000B73A.cpp" />
    <ClCompile Include="Source\j_UpdateRankDBRecordCWeeklyGuildRankManagerAEAA_NP_1400026C1.cpp" />
    <ClCompile Include="Source\j_UpdateServerResetTokenCRFWorldDatabaseQEAA_NKGKZ_1400097C8.cpp" />
    <ClCompile Include="Source\j_UpdateTodayTableCWeeklyGuildRankManagerAEAAHPEAD_14001256C.cpp" />
    <ClCompile Include="Source\j_Updatet_Account_Vote_AvailableCRFWorldDatabaseQE_14000B9EC.cpp" />
    <ClCompile Include="Source\j_UpdateVotedReset_CheatCRFWorldDatabaseQEAA_NKZ_1400102C6.cpp" />
    <ClCompile Include="Source\j_UpdateVotedReset_GeneralCRFWorldDatabaseQEAA_NKZ_140002824.cpp" />
    <ClCompile Include="Source\j_UpdateVotedReset_SupplementCRFWorldDatabaseQEAA__14000D8AA.cpp" />
    <ClCompile Include="Source\j_Update_AddBuddyCUserDBQEAA_NEKPEADZ_14000A45C.cpp" />
    <ClCompile Include="Source\j_Update_AlterPvPCashBagCUserDBQEAA_NNZ_14000BBF9.cpp" />
    <ClCompile Include="Source\j_Update_AlterPvPPointCUserDBQEAA_NNZ_14000D454.cpp" />
    <ClCompile Include="Source\j_update_amine_batteryCRFWorldDatabaseQEAA_NEEKKZ_140012B75.cpp" />
    <ClCompile Include="Source\j_update_amine_dckCRFWorldDatabaseQEAA_NEEKZ_14000605F.cpp" />
    <ClCompile Include="Source\j_update_amine_mineoreCRFWorldDatabaseQEAA_NEEKEKE_140013F5C.cpp" />
    <ClCompile Include="Source\j_update_amine_moveoreCRFWorldDatabaseQEAA_NEEKEKE_140002E91.cpp" />
    <ClCompile Include="Source\j_update_amine_personalCRFWorldDatabaseQEAA_NPEADZ_1400060B9.cpp" />
    <ClCompile Include="Source\j_update_amine_seloreCRFWorldDatabaseQEAA_NEEKEZ_14000C31F.cpp" />
    <ClCompile Include="Source\j_update_amine_workstateCRFWorldDatabaseQEAA_NEEK__14000BB9A.cpp" />
    <ClCompile Include="Source\j_Update_AnimusDataCRFWorldDatabaseQEAA_NKEPEANZ_140011F04.cpp" />
    <ClCompile Include="Source\j_Update_AutoTradeAllClearCUserDBQEAA_NXZ_14000C5D6.cpp" />
    <ClCompile Include="Source\j_Update_BagNumCUserDBQEAA_NEZ_14000F092.cpp" />
    <ClCompile Include="Source\j_Update_BindCUserDBQEAA_NPEAD0_NZ_14000A7F4.cpp" />
    <ClCompile Include="Source\j_Update_BossCryMsgCUserDBQEAAXEPEADZ_14000989A.cpp" />
    <ClCompile Include="Source\j_Update_CharSlotCRFWorldDatabaseQEAA_NKZ_14001217F.cpp" />
    <ClCompile Include="Source\j_Update_ClassCUserDBQEAA_NPEADEGZ_14000F623.cpp" />
    <ClCompile Include="Source\j_Update_ClearWeeklyPvpPointSumCRFWorldDatabaseQEA_140006B8B.cpp" />
    <ClCompile Include="Source\j_Update_CombineExResult_PopCUserDBQEAA_NXZ_14001142D.cpp" />
    <ClCompile Include="Source\j_Update_CombineExResult_PushCUserDBQEAA_NPEAU_ITE_1400104A1.cpp" />
    <ClCompile Include="Source\j_Update_CopyAllCUserDBQEAA_NPEAU_AVATOR_DATAZ_14000939A.cpp" />
    <ClCompile Include="Source\j_Update_CuttingEmptyCUserDBQEAA_NXZ_140010EB5.cpp" />
    <ClCompile Include="Source\j_Update_CuttingPushCUserDBQEAA_NEPEAU_LIST_CUTTIN_140012CDD.cpp" />
    <ClCompile Include="Source\j_Update_CuttingTransCUserDBQEAA_NGGZ_14000555B.cpp" />
    <ClCompile Include="Source\j_Update_DalantCRFWorldDatabaseQEAA_NKKZ_1400053E9.cpp" />
    <ClCompile Include="Source\j_Update_DelBuddyCUserDBQEAA_NEZ_140010A78.cpp" />
    <ClCompile Include="Source\j_Update_DelPostCUserDBQEAA_NKHZ_14000D91D.cpp" />
    <ClCompile Include="Source\j_Update_DisableInstanceStoreCRFWorldDatabaseQEAA__140012F62.cpp" />
    <ClCompile Include="Source\j_Update_DisappearOwnerRecordCRFWorldDatabaseQEAA__1400044E4.cpp" />
    <ClCompile Include="Source\j_Update_ExtTrunkSlotNumCUserDBQEAA_NEZ_140003B43.cpp" />
    <ClCompile Include="Source\j_Update_GmGreetCRFWorldDatabaseQEAA_NPEAU_qry_cas_14000F911.cpp" />
    <ClCompile Include="Source\j_Update_GoldCRFWorldDatabaseQEAA_NKKZ_14000610E.cpp" />
    <ClCompile Include="Source\j_Update_GuildEmblemCRFWorldDatabaseQEAA_NKNKKZ_140009B06.cpp" />
    <ClCompile Include="Source\j_Update_GuildGradeCRFWorldDatabaseQEAA_NXZ_14000B172.cpp" />
    <ClCompile Include="Source\j_Update_GuildGreetCRFWorldDatabaseQEAA_NPEAU_qry__1400129BD.cpp" />
    <ClCompile Include="Source\j_Update_GuildMasterCRFWorldDatabaseQEAA_NKKEZ_14000E723.cpp" />
    <ClCompile Include="Source\j_Update_GuildMemberCountCRFWorldDatabaseQEAA_NKGZ_14000E7C3.cpp" />
    <ClCompile Include="Source\j_Update_GuildRankCRFWorldDatabaseQEAA_NPEADZ_1400036F7.cpp" />
    <ClCompile Include="Source\j_Update_GuildRank_Step1CRFWorldDatabaseQEAA_NPEAD_1400102F3.cpp" />
    <ClCompile Include="Source\j_Update_GuildRank_Step2CRFWorldDatabaseQEAA_NPEAD_14000339B.cpp" />
    <ClCompile Include="Source\j_Update_GuildRank_Step3CRFWorldDatabaseQEAA_NPEAD_140007B71.cpp" />
    <ClCompile Include="Source\j_Update_GuildRoomCRFWorldDatabaseQEAA_NKZ_1400096F1.cpp" />
    <ClCompile Include="Source\j_Update_IncreaseWeeklyGuildKillPvpPointSumCRFWorl_14000479B.cpp" />
    <ClCompile Include="Source\j_Update_InputGuildMoneyCRFWorldDatabaseQEAA_NKKKZ_140010DAC.cpp" />
    <ClCompile Include="Source\j_Update_ItemAddCUserDBQEAA_NEEPEBU_db_con_STORAGE_1400067AD.cpp" />
    <ClCompile Include="Source\j_Update_ItemDeleteCUserDBQEAA_NEE_NZ_1400086E8.cpp" />
    <ClCompile Include="Source\j_Update_ItemDurCUserDBQEAA_NEE_K_NZ_14000A475.cpp" />
    <ClCompile Include="Source\j_Update_ItemSlotCUserDBQEAA_NEEEZ_140011437.cpp" />
    <ClCompile Include="Source\j_Update_ItemUpgradeCUserDBQEAA_NEEK_NZ_14000E40D.cpp" />
    <ClCompile Include="Source\j_Update_LastAttBuffCUserDBQEAAX_NZ_14000CE2D.cpp" />
    <ClCompile Include="Source\j_Update_LimitItemNumCRFWorldDatabaseQEAA_NPEADZ_14000623F.cpp" />
    <ClCompile Include="Source\j_Update_LinkBoardLockCUserDBQEAA_NEZ_14000A407.cpp" />
    <ClCompile Include="Source\j_Update_LinkBoardSlotCUserDBQEAA_NEEGZ_14000A493.cpp" />
    <ClCompile Include="Source\j_Update_MacroCUserDBQEAA_NPEADZ_1400111F8.cpp" />
    <ClCompile Include="Source\j_Update_MacroDataCRFWorldDatabaseQEAA_NKPEAU_AIOC_1400068B1.cpp" />
    <ClCompile Include="Source\j_Update_MapCUserDBQEAA_NEPEAMZ_14000BF73.cpp" />
    <ClCompile Include="Source\j_Update_MoneyCUserDBQEAA_NKKZ_1400120B2.cpp" />
    <ClCompile Include="Source\j_Update_NpcDataCRFWorldDatabaseQEAA_NKPEAKZ_14000863E.cpp" />
    <ClCompile Include="Source\j_Update_NPCQuestHistoryCUserDBQEAA_NEPEAU_NPC_QUE_140009BE2.cpp" />
    <ClCompile Include="Source\j_Update_OutputGuildMoneyCRFWorldDatabaseQEAA_NKKK_140008918.cpp" />
    <ClCompile Include="Source\j_Update_ParamCUserDBQEAA_NPEAU_EXIT_ALTER_PARAMZ_140011086.cpp" />
    <ClCompile Include="Source\j_Update_PatriarchCommCRFWorldDatabaseQEAAEKKPEADZ_1400130FC.cpp" />
    <ClCompile Include="Source\j_Update_PlayTimeCUserDBQEAA_NKZ_14001251C.cpp" />
    <ClCompile Include="Source\j_Update_PostCRFWorldDatabaseQEAA_NPEADZ_14000B433.cpp" />
    <ClCompile Include="Source\j_Update_PostCUserDBQEAAXHKHEH_KKK0Z_140013061.cpp" />
    <ClCompile Include="Source\j_Update_PostRegistryCRFWorldDatabaseQEAA_NKKPEAD0_140002D2E.cpp" />
    <ClCompile Include="Source\j_Update_PostRegistryDisableCRFWorldDatabaseQEAA_N_14000A31C.cpp" />
    <ClCompile Include="Source\j_Update_PotionNextUseTimeCUserDBQEAAXEKZ_140011F13.cpp" />
    <ClCompile Include="Source\j_Update_PunishmentCRFWorldDatabaseQEAA_NPEADZ_140013EF8.cpp" />
    <ClCompile Include="Source\j_Update_PvpPointGuildRankRecordCRFWorldDatabaseQE_140009110.cpp" />
    <ClCompile Include="Source\j_Update_PvpPointGuildRankSumLvCRFWorldDatabaseQEA_140006E51.cpp" />
    <ClCompile Include="Source\j_Update_PvpPointInfoCRFWorldDatabaseQEAA_NKPEAFNZ_14000C1EE.cpp" />
    <ClCompile Include="Source\j_Update_PvpPointLeakCUserDBQEAAXNZ_140002CF7.cpp" />
    <ClCompile Include="Source\j_Update_QuestDeleteCUserDBQEAA_NEZ_140011077.cpp" />
    <ClCompile Include="Source\j_Update_QuestInsertCUserDBQEAA_NEPEAU_LIST_QUEST__14000AAD3.cpp" />
    <ClCompile Include="Source\j_Update_QuestUpdateCUserDBQEAA_NEPEAU_LIST_QUEST__140006BD1.cpp" />
    <ClCompile Include="Source\j_Update_RaceGreetCRFWorldDatabaseQEAA_NPEAU_qry_c_14001371E.cpp" />
    <ClCompile Include="Source\j_Update_RaceRankCRFWorldDatabaseQEAA_NPEADZ_14000BC9E.cpp" />
    <ClCompile Include="Source\j_Update_RaceRank_Step1CRFWorldDatabaseQEAA_NPEADZ_140006357.cpp" />
    <ClCompile Include="Source\j_Update_RaceRank_Step2CRFWorldDatabaseQEAA_NPEADZ_140004F6B.cpp" />
    <ClCompile Include="Source\j_Update_RaceRank_Step3CRFWorldDatabaseQEAA_NPEADZ_14000CE4B.cpp" />
    <ClCompile Include="Source\j_Update_RaceRank_Step4CRFWorldDatabaseQEAA_NPEADZ_1400043F4.cpp" />
    <ClCompile Include="Source\j_Update_RaceRank_Step5CRFWorldDatabaseQEAA_NPEADZ_14000C8A6.cpp" />
    <ClCompile Include="Source\j_Update_RaceRank_Step6CRFWorldDatabaseQEAA_NPEADZ_14000F3C6.cpp" />
    <ClCompile Include="Source\j_Update_RaceRank_Step7CRFWorldDatabaseQEAA_NPEADZ_140010CDF.cpp" />
    <ClCompile Include="Source\j_Update_RaceRank_Step8CRFWorldDatabaseQEAA_NPEADZ_140001776.cpp" />
    <ClCompile Include="Source\j_Update_RaceRank_Step9CRFWorldDatabaseQEAA_NPEADZ_140008A7B.cpp" />
    <ClCompile Include="Source\j_Update_RaceRank_Step_6_1CRFWorldDatabaseQEAA_NPE_14000CA04.cpp" />
    <ClCompile Include="Source\j_Update_RaceRank_Step_6_2CRFWorldDatabaseQEAA_NPE_140002847.cpp" />
    <ClCompile Include="Source\j_Update_RaceRank_Step_6_3CRFWorldDatabaseQEAA_NPE_14000180C.cpp" />
    <ClCompile Include="Source\j_Update_RaceVoteInfoInitCUserDBQEAA_NXZ_140003E8B.cpp" />
    <ClCompile Include="Source\j_Update_RankInGuildCRFWorldDatabaseQEAA_NKPEAU_wo_14001068B.cpp" />
    <ClCompile Include="Source\j_Update_RankInGuild_Step1CRFWorldDatabaseQEAAEKZ_14000BB4F.cpp" />
    <ClCompile Include="Source\j_Update_RankInGuild_Step2CRFWorldDatabaseQEAA_NKZ_14000E601.cpp" />
    <ClCompile Include="Source\j_Update_RankInGuild_Step3CRFWorldDatabaseQEAA_NKZ_14000F8AD.cpp" />
    <ClCompile Include="Source\j_Update_RankInGuild_Step4CRFWorldDatabaseQEAA_NKZ_14000A812.cpp" />
    <ClCompile Include="Source\j_Update_RankInGuild_Step5CRFWorldDatabaseQEAA_NKP_1400128E6.cpp" />
    <ClCompile Include="Source\j_Update_RankInGuild_Step6CRFWorldDatabaseQEAA_NXZ_140006CDF.cpp" />
    <ClCompile Include="Source\j_Update_RankInGuild_Step7CRFWorldDatabaseQEAA_NXZ_14000698D.cpp" />
    <ClCompile Include="Source\j_Update_RankInGuild_Step8CRFWorldDatabaseQEAA_NXZ_140003D14.cpp" />
    <ClCompile Include="Source\j_Update_RankInGuild_Step9CRFWorldDatabaseQEAA_NXZ_14000F849.cpp" />
    <ClCompile Include="Source\j_Update_ReturnPostCUserDBQEAAXKZ_140001D39.cpp" />
    <ClCompile Include="Source\j_Update_RFEvent_ClassRefineCRFWorldDatabaseQEAA_N_140012904.cpp" />
    <ClCompile Include="Source\j_Update_SetActiveCRFWorldDatabaseQEAA_NKPEADEZ_14000AE89.cpp" />
    <ClCompile Include="Source\j_Update_SetGuildMoneyCRFWorldDatabaseQEAA_NKNNZ_140007D4C.cpp" />
    <ClCompile Include="Source\j_Update_Set_Limit_RunCRFWorldDatabaseQEAA_NPEAEHZ_1400055C4.cpp" />
    <ClCompile Include="Source\j_Update_SFContDeleteCUserDBQEAA_NEEZ_140008D32.cpp" />
    <ClCompile Include="Source\j_Update_SFContInsertCUserDBQEAA_NEEEGEGZ_140004AFC.cpp" />
    <ClCompile Include="Source\j_Update_SFContUpdateCUserDBQEAA_NEEG_NZ_1400027B1.cpp" />
    <ClCompile Include="Source\j_Update_SFDelayInfoCRFWorldDatabaseQEAA_NKPEAU_wo_140004417.cpp" />
    <ClCompile Include="Source\j_Update_StartNPCQuestHistoryCUserDBQEAA_NEPEAU_ST_140010208.cpp" />
    <ClCompile Include="Source\j_Update_Start_NpcQuest_HistoryCRFWorldDatabaseQEA_1400027A7.cpp" />
    <ClCompile Include="Source\j_Update_StatCUserDBQEAA_NEK_NZ_140004DC7.cpp" />
    <ClCompile Include="Source\j_Update_TakeLastCriTicketCUserDBQEAA_NKZ_14000C518.cpp" />
    <ClCompile Include="Source\j_Update_TakeLastMentalTicketCUserDBQEAA_NKZ_14001363D.cpp" />
    <ClCompile Include="Source\j_Update_TrunkHintCUserDBQEAA_NEPEADZ_14001309D.cpp" />
    <ClCompile Include="Source\j_Update_TrunkMoneyCUserDBQEAA_NNNZ_14000F411.cpp" />
    <ClCompile Include="Source\j_Update_TrunkSlotNumCUserDBQEAA_NEZ_14000E5E8.cpp" />
    <ClCompile Include="Source\j_Update_UnitDataCRFWorldDatabaseQEAA_NKPEANZ_14000D4E5.cpp" />
    <ClCompile Include="Source\j_Update_UnitDataCUserDBQEAA_NEPEAU_LIST_UNIT_DB_B_140013E58.cpp" />
    <ClCompile Include="Source\j_Update_UnitDeleteCUserDBQEAA_NEZ_140002AF4.cpp" />
    <ClCompile Include="Source\j_Update_UnitInsertCUserDBQEAA_NEPEAU_LIST_UNIT_DB_14000F538.cpp" />
    <ClCompile Include="Source\j_Update_UnmannedTraderCheatUpdateRegistDateCRFWor_1400057A4.cpp" />
    <ClCompile Include="Source\j_Update_UnmannedTraderClearDanglingOwnerRecordCRF_14000F5A6.cpp" />
    <ClCompile Include="Source\j_Update_UnmannedTraderItemStateCRFWorldDatabaseQE_140009DD1.cpp" />
    <ClCompile Include="Source\j_Update_UnmannedTraderReRegistCRFWorldDatabaseQEA_140001F82.cpp" />
    <ClCompile Include="Source\j_Update_UnmannedTraderResutlInfoCRFWorldDatabaseQ_14000A178.cpp" />
    <ClCompile Include="Source\j_Update_UnmannedTraderSellInfoCRFWorldDatabaseQEA_14000FEC0.cpp" />
    <ClCompile Include="Source\j_Update_UnmannedTraderSellInfoPriceCRFWorldDataba_140009854.cpp" />
    <ClCompile Include="Source\j_Update_UnmannedTraderSingleItemInfoCRFWorldDatab_140006DD9.cpp" />
    <ClCompile Include="Source\j_Update_UnmannedTraderSingleTypeClearUseCompleteR_140001E1F.cpp" />
    <ClCompile Include="Source\j_Update_UserFatigueCUserDBQEAA_NKZ_140003486.cpp" />
    <ClCompile Include="Source\j_Update_UserGetScanerCUserDBQEAA_NGGZ_14000C626.cpp" />
    <ClCompile Include="Source\j_Update_UserGuildDataCRFWorldDatabaseQEAA_NKKEZ_14000E5CA.cpp" />
    <ClCompile Include="Source\j_Update_UserPlayTimeCUserDBQEAA_NKZ_1400063ED.cpp" />
    <ClCompile Include="Source\j_Update_UserTLStatusCUserDBQEAA_NEZ_140005821.cpp" />
    <ClCompile Include="Source\j_Update_UserVoteDataCUserDBQEAA_NXZ_1400026F3.cpp" />
    <ClCompile Include="Source\j_Update_User_Action_PointCUserDBQEAA_NEKZ_140003BFC.cpp" />
    <ClCompile Include="Source\j_Update_WindowInfoCUserDBQEAA_NPEAK000K0Z_140003A21.cpp" />
    <ClCompile Include="Source\j_WriteLog_ChangeClassAfterInitClassCUserDBQEAAXEP_140012D3C.cpp" />
    <ClCompile Include="Source\j_WriteLog_CharSelectCUserDBQEAAXXZ_14001048D.cpp" />
    <ClCompile Include="Source\j_WriteTableDataPartYA_NHPEAVCRecordDataPEADZ_14000CDB5.cpp" />
    <ClCompile Include="Source\j_WriteTableDataYA_NHPEAVCRecordData_NPEADZ_14000D080.cpp" />
    <ClCompile Include="Source\j_Y_Vector_const_iteratorPEAVCLogTypeDBTaskValloca_140001CDA.cpp" />
    <ClCompile Include="Source\j_Y_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEA_140012E13.cpp" />
    <ClCompile Include="Source\j_Z_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEA_14000A641.cpp" />
    <ClCompile Include="Source\j__AllocatePEAVCLogTypeDBTaskstdYAPEAPEAVCLogTypeD_1400042BE.cpp" />
    <ClCompile Include="Source\j__all_rollbackCashDbWorkerMEAAXPEBU_param_cash_up_14000A71D.cpp" />
    <ClCompile Include="Source\j__all_rollbackCCashDbWorkerJPMEAAXPEBU_param_cash_14000BB45.cpp" />
    <ClCompile Include="Source\j__all_rollbackCCashDbWorkerNULLMEAAXPEBU_param_ca_14001037A.cpp" />
    <ClCompile Include="Source\j__BuyvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTy_14000ED18.cpp" />
    <ClCompile Include="Source\j__complete_tsk_cash_rollbackCashDbWorkerIEAAXPEAV_1400077C0.cpp" />
    <ClCompile Include="Source\j__complete_tsk_cash_selectCashDbWorkerIEAAXPEAVTa_140005AF6.cpp" />
    <ClCompile Include="Source\j__complete_tsk_cash_total_selling_selectCashDbWor_14000FCEA.cpp" />
    <ClCompile Include="Source\j__complete_tsk_cash_updateCashDbWorkerIEAAXPEAVTa_14000EA25.cpp" />
    <ClCompile Include="Source\j__ConstructPEAVCLogTypeDBTaskPEAV1stdYAXPEAPEAVCL_1400015FF.cpp" />
    <ClCompile Include="Source\j__Copy_backward_optPEAPEAVCLogTypeDBTaskPEAPEAV1U_1400109DD.cpp" />
    <ClCompile Include="Source\j__Copy_optPEAPEAVCLogTypeDBTaskPEAPEAV1Urandom_ac_1400104A6.cpp" />
    <ClCompile Include="Source\j__CreateTableLtdWriterAEAAXXZ_140002FF9.cpp" />
    <ClCompile Include="Source\j__DbgOutYAJPEADKJ0Z_14000D819.cpp" />
    <ClCompile Include="Source\j__db_Check_NpcDataCMainThreadAEAAEKPEAU_AVATOR_DA_14000C7F2.cpp" />
    <ClCompile Include="Source\j__db_complete_event_classrefineCMainThreadQEAAXGK_14000284C.cpp" />
    <ClCompile Include="Source\j__db_complete_update_event_classrefineCMainThread_140005F10.cpp" />
    <ClCompile Include="Source\j__db_GuildRoom_InsertCMainThreadAEAAEPEAU_qry_cas_14000D387.cpp" />
    <ClCompile Include="Source\j__db_GuildRoom_UpdateCMainThreadAEAA_NPEAU_qry_ca_14000CF36.cpp" />
    <ClCompile Include="Source\j__db_init_classrefine_countCMainThreadQEAAHXZ_14000E2D2.cpp" />
    <ClCompile Include="Source\j__db_loadTRC_AutoTradeQEAA_NEZ_14000E525.cpp" />
    <ClCompile Include="Source\j__db_Load_BaseCMainThreadAEAAEKPEAU_AVATOR_DATAZ_140006082.cpp" />
    <ClCompile Include="Source\j__db_Load_BuddyCMainThreadAEAAEKPEAU_BUDDY_DB_BAS_14000402F.cpp" />
    <ClCompile Include="Source\j__db_Load_Cash_LimSaleCMainThreadAEAAEPEAUqry_cas_14000553D.cpp" />
    <ClCompile Include="Source\j__db_Load_CryMsgCMainThreadAEAAEKPEAU_CRYMSG_DB_B_14000F367.cpp" />
    <ClCompile Include="Source\j__db_load_event_classrefineCMainThreadQEAAEKAEAEA_1400109D3.cpp" />
    <ClCompile Include="Source\j__db_Load_GeneralCMainThreadAEAAEKEPEAU_AVATOR_DA_140009EDA.cpp" />
    <ClCompile Include="Source\j__db_Load_GoldBoxItemCMainThreadAEAAEPEAUqry_case_14000DC24.cpp" />
    <ClCompile Include="Source\j__db_Load_InvenCMainThreadAEAAEKHPEAU_INVEN_DB_BA_14000C766.cpp" />
    <ClCompile Include="Source\j__db_Load_ItemCombineExCMainThreadAEAAEKPEAU_ITEM_140012940.cpp" />
    <ClCompile Include="Source\j__db_Load_MacroDataCMainThreadAEAAEKPEAU_AIOC_A_M_14001248B.cpp" />
    <ClCompile Include="Source\j__db_Load_NpcQuest_HistoryCMainThreadAEAAEKPEAU_Q_140001AF0.cpp" />
    <ClCompile Include="Source\j__db_Load_OreCuttingCMainThreadAEAAEKPEAU_CUTTING_140007851.cpp" />
    <ClCompile Include="Source\j__db_Load_PatriarchCommCMainThreadAEAAHPEADZ_14000AEF2.cpp" />
    <ClCompile Include="Source\j__db_Load_PcBangFavorCMainThreadAEAAEKPEAU_PCBANG_14000B735.cpp" />
    <ClCompile Include="Source\j__db_Load_PotionDelayCMainThreadAEAAEKPEAU_POTION_14000D733.cpp" />
    <ClCompile Include="Source\j__db_Load_PrimiumPlayTimeCMainThreadAEAAEKAEAU_PC_14000F777.cpp" />
    <ClCompile Include="Source\j__db_load_punishmentCMainThreadAEAAEKPEAU_AVATOR__14000CB1C.cpp" />
    <ClCompile Include="Source\j__db_Load_PvpOrderViewCMainThreadAEAAEKAEAU_PVP_O_14000144C.cpp" />
    <ClCompile Include="Source\j__db_Load_PvpPointLimitDataCMainThreadAEAAEKAEAV__140003297.cpp" />
    <ClCompile Include="Source\j__db_Load_QuestCMainThreadAEAAEKPEAU_QUEST_DB_BAS_140010F73.cpp" />
    <ClCompile Include="Source\j__db_load_racebossCMainThreadAEAAEKPEAU_AVATOR_DA_1400077AC.cpp" />
    <ClCompile Include="Source\j__db_Load_SFDelayDataCMainThreadAEAAEKPEAU_worldd_14000E70F.cpp" />
    <ClCompile Include="Source\j__db_Load_Start_NpcQuest_HistoryCMainThreadAEAAEK_14000BC17.cpp" />
    <ClCompile Include="Source\j__db_Load_SupplementCMainThreadAEAAEKPEAU_SUPPLEM_1400135ED.cpp" />
    <ClCompile Include="Source\j__db_Load_TimeLimitInfoCMainThreadAEAAEKPEAU_TIME_140006FB9.cpp" />
    <ClCompile Include="Source\j__db_Load_TradeCMainThreadAEAAEEKPEAU_TRADE_DB_BA_1400051E6.cpp" />
    <ClCompile Include="Source\j__db_Load_TrunkCMainThreadAEAAEKKEPEAU_TRUNK_DB_B_1400135A2.cpp" />
    <ClCompile Include="Source\j__db_Load_UICMainThreadAEAAEKPEAU_LINK_DB_BASEPEA_14000E25F.cpp" />
    <ClCompile Include="Source\j__db_Load_UnitCMainThreadAEAAEKPEAU_UNIT_DB_BASEZ_14000303A.cpp" />
    <ClCompile Include="Source\j__db_qry_insert_newownerAutoMineMachineMngAEAAEPE_140010834.cpp" />
    <ClCompile Include="Source\j__db_qry_update_battery_chargeAutoMineMachineMngA_14000DBE8.cpp" />
    <ClCompile Include="Source\j__db_qry_update_battery_dischargeAutoMineMachineM_14000209A.cpp" />
    <ClCompile Include="Source\j__db_qry_update_mineoreAutoMineMachineMngAEAAEPEA_14000368E.cpp" />
    <ClCompile Include="Source\j__db_qry_update_moveoreAutoMineMachineMngAEAAEPEA_14000F745.cpp" />
    <ClCompile Include="Source\j__db_qry_update_seloreAutoMineMachineMngAEAAEPEAD_140012305.cpp" />
    <ClCompile Include="Source\j__db_qry_update_workstateAutoMineMachineMngAEAAEP_140008143.cpp" />
    <ClCompile Include="Source\j__db_Select_RegeAvator_For_Lobby_LogoutCMainThrea_140005A92.cpp" />
    <ClCompile Include="Source\j__db_Update_BaseCMainThreadAEAA_NKPEAU_AVATOR_DAT_14000DFDF.cpp" />
    <ClCompile Include="Source\j__db_Update_BuddyCMainThreadAEAA_NKPEAU_AVATOR_DA_14000BD6B.cpp" />
    <ClCompile Include="Source\j__db_Update_Cash_LimSaleCMainThreadAEAAEPEAU_db_c_14000D4C7.cpp" />
    <ClCompile Include="Source\j__db_Update_CryMsgCMainThreadAEAA_NKPEAU_AVATOR_D_1400103CA.cpp" />
    <ClCompile Include="Source\j__db_Update_Data_For_TradeCMainThreadAEAAEPEADZ_140004304.cpp" />
    <ClCompile Include="Source\j__db_update_event_classrefineCMainThreadQEAAEGKEK_140005A51.cpp" />
    <ClCompile Include="Source\j__db_Update_GeneralCMainThreadAEAA_NKPEAU_AVATOR__140009741.cpp" />
    <ClCompile Include="Source\j__db_Update_GoldBoxItemCMainThreadAEAAEHPEAU_db_g_14000A65A.cpp" />
    <ClCompile Include="Source\j__db_Update_InvenCMainThreadAEAA_NKPEAU_AVATOR_DA_140001474.cpp" />
    <ClCompile Include="Source\j__db_update_inven_AMPCMainThreadAEAA_NKPEAU_AVATO_14000D490.cpp" />
    <ClCompile Include="Source\j__db_Update_ItemCombineExCMainThreadAEAA_NKPEAU_A_14000DC33.cpp" />
    <ClCompile Include="Source\j__db_Update_MacroDataCMainThreadAEAA_NKPEAU_AIOC__14000F966.cpp" />
    <ClCompile Include="Source\j__db_Update_NpcDataCMainThreadAEAA_NKPEAU_AVATOR__140013F7F.cpp" />
    <ClCompile Include="Source\j__db_Update_NpcQuest_HistoryCMainThreadAEAA_NKPEA_1400130F7.cpp" />
    <ClCompile Include="Source\j__db_Update_OreCuttingCMainThreadAEAA_NKPEAU_AVAT_140009796.cpp" />
    <ClCompile Include="Source\j__db_Update_PcBangFavorCMainThreadAEAA_NKPEAU_AVA_1400085F3.cpp" />
    <ClCompile Include="Source\j__db_Update_PotionDelayCMainThreadAEAA_NKPEAU_AVA_14000966F.cpp" />
    <ClCompile Include="Source\j__db_Update_PrimiumPlayTimeCMainThreadAEAA_NKPEAU_140006F6E.cpp" />
    <ClCompile Include="Source\j__db_Update_PvpOrderViewCMainThreadAEAA_NKPEAU_AV_14000E7BE.cpp" />
    <ClCompile Include="Source\j__db_Update_PvpPointLimitCMainThreadAEAA_NKPEAU_A_140013228.cpp" />
    <ClCompile Include="Source\j__db_Update_QuestCMainThreadAEAA_NKPEAU_AVATOR_DA_140002E50.cpp" />
    <ClCompile Include="Source\j__db_Update_Set_Limit_RunCMainThreadAEAAEXZ_1400068FC.cpp" />
    <ClCompile Include="Source\j__db_Update_SFDelayDataCMainThreadAEAA_NKPEAU_AVA_140008B6B.cpp" />
    <ClCompile Include="Source\j__db_Update_Start_NpcQuest_HistoryCMainThreadAEAA_140011F9F.cpp" />
    <ClCompile Include="Source\j__db_Update_SupplementCMainThreadAEAA_NKPEAU_AVAT_14001030C.cpp" />
    <ClCompile Include="Source\j__db_Update_TimeLimitInfoCMainThreadAEAAEKPEAU_AV_1400115D6.cpp" />
    <ClCompile Include="Source\j__db_Update_TrunkCMainThreadAEAA_NKPEAU_AVATOR_DA_14000B1E5.cpp" />
    <ClCompile Include="Source\j__db_Update_Trunk_ExtendCMainThreadAEAA_NKPEAU_AV_14000FE34.cpp" />
    <ClCompile Include="Source\j__db_Update_UICMainThreadAEAA_NKPEAU_AVATOR_DATA0_14000114F.cpp" />
    <ClCompile Include="Source\j__db_Update_UnitCMainThreadAEAA_NKPEAU_AVATOR_DAT_1400124FE.cpp" />
    <ClCompile Include="Source\j__DDL_GroupParametersImplVEcPrecomputationVECPCry_1400139C1.cpp" />
    <ClCompile Include="Source\j__DestroyPEAVCLogTypeDBTaskstdYAXPEAPEAVCLogTypeD_1400093EA.cpp" />
    <ClCompile Include="Source\j__DestroyvectorPEAVCLogTypeDBTaskVallocatorPEAVCL_14000119A.cpp" />
    <ClCompile Include="Source\j__Destroy_rangePEAVCLogTypeDBTaskVallocatorPEAVCL_140006474.cpp" />
    <ClCompile Include="Source\j__Destroy_rangePEAVCLogTypeDBTaskVallocatorPEAVCL_140011874.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerBRUEAAPEAXIZ_0_14000E77D.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerBRUEAAPEAXIZ_140005F8D.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerIDUEAAPEAXIZ_0_14000F682.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerIDUEAAPEAXIZ_140009638.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerJPUEAAPEAXIZ_0_140008B43.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerJPUEAAPEAXIZ_140007090.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerKRUEAAPEAXIZ_0_14000C144.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerKRUEAAPEAXIZ_14000603C.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerPHUEAAPEAXIZ_0_140002BA8.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerPHUEAAPEAXIZ_14000103C.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerRUUEAAPEAXIZ_0_14001305C.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerRUUEAAPEAXIZ_140002E00.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerTHUEAAPEAXIZ_0_1400105A5.cpp" />
    <ClCompile Include="Source\j__ECCashDbWorkerTHUEAAPEAXIZ_14000502E.cpp" />
    <ClCompile Include="Source\j__ECCashDBWorkManagerEEAAPEAXIZ_0_1400054F2.cpp" />
    <ClCompile Include="Source\j__ECCashDBWorkManagerEEAAPEAXIZ_140002D06.cpp" />
    <ClCompile Include="Source\j__ECItemLootTableUEAAPEAXIZ_0_1400131BF.cpp" />
    <ClCompile Include="Source\j__ECItemLootTableUEAAPEAXIZ_1400120D5.cpp" />
    <ClCompile Include="Source\j__ECItemUpgradeTableUEAAPEAXIZ_0_14000DC15.cpp" />
    <ClCompile Include="Source\j__ECItemUpgradeTableUEAAPEAXIZ_14000AE57.cpp" />
    <ClCompile Include="Source\j__ECMapDataTableUEAAPEAXIZ_0_14000C969.cpp" />
    <ClCompile Include="Source\j__ECMapDataTableUEAAPEAXIZ_1400029E6.cpp" />
    <ClCompile Include="Source\j__ECOreCuttingTableUEAAPEAXIZ_0_1400088AA.cpp" />
    <ClCompile Include="Source\j__ECOreCuttingTableUEAAPEAXIZ_140004F75.cpp" />
    <ClCompile Include="Source\j__ECRecordDataUEAAPEAXIZ_140002955.cpp" />
    <ClCompile Include="Source\j__ECRFDBItemLogUEAAPEAXIZ_0_14000C7BB.cpp" />
    <ClCompile Include="Source\j__ECRFDBItemLogUEAAPEAXIZ_14000B3AC.cpp" />
    <ClCompile Include="Source\j__ECRFNewDatabaseUEAAPEAXIZ_0_14000F65F.cpp" />
    <ClCompile Include="Source\j__ECRFNewDatabaseUEAAPEAXIZ_1400050EC.cpp" />
    <ClCompile Include="Source\j__ECRFWorldDatabaseUEAAPEAXIZ_0_14000D8BE.cpp" />
    <ClCompile Include="Source\j__ECRFWorldDatabaseUEAAPEAXIZ_140005097.cpp" />
    <ClCompile Include="Source\j__ECTSingletonVCCashDBWorkManagerMEAAPEAXIZ_0_140013606.cpp" />
    <ClCompile Include="Source\j__ECTSingletonVCCashDBWorkManagerMEAAPEAXIZ_140007DE7.cpp" />
    <ClCompile Include="Source\j__ECUserDBUEAAPEAXIZ_14000CAE5.cpp" />
    <ClCompile Include="Source\j__EDL_GroupParametersImplVEcPrecomputationVECPCry_140012666.cpp" />
    <ClCompile Include="Source\j__FillPEAPEAVCLogTypeDBTaskPEAV1stdYAXPEAPEAVCLog_1400050C9.cpp" />
    <ClCompile Include="Source\j__Fill_nPEAPEAVCLogTypeDBTask_KPEAV1stdYAXPEAPEAV_140008724.cpp" />
    <ClCompile Include="Source\j__Fill_nPEAPEAVCLogTypeDBTask_KPEAV1Urandom_acces_1400014D8.cpp" />
    <ClCompile Include="Source\j__GameDataBaseInitCMainThreadAEAA_NXZ_140001C1C.cpp" />
    <ClCompile Include="Source\j__GCashDbWorkerUEAAPEAXIZ_0_140004A34.cpp" />
    <ClCompile Include="Source\j__GCashDbWorkerUEAAPEAXIZ_140002707.cpp" />
    <ClCompile Include="Source\j__GCCashDbWorkerCNUEAAPEAXIZ_0_14000F565.cpp" />
    <ClCompile Include="Source\j__GCCashDbWorkerCNUEAAPEAXIZ_14000954D.cpp" />
    <ClCompile Include="Source\j__GCCashDbWorkerESUEAAPEAXIZ_0_140009778.cpp" />
    <ClCompile Include="Source\j__GCCashDbWorkerESUEAAPEAXIZ_1400022FC.cpp" />
    <ClCompile Include="Source\j__GCCashDbWorkerGBUEAAPEAXIZ_0_14000DB5C.cpp" />
    <ClCompile Include="Source\j__GCCashDbWorkerGBUEAAPEAXIZ_1400020EF.cpp" />
    <ClCompile Include="Source\j__GCCashDbWorkerNULLUEAAPEAXIZ_0_140003161.cpp" />
    <ClCompile Include="Source\j__GCCashDbWorkerNULLUEAAPEAXIZ_1400012DA.cpp" />
    <ClCompile Include="Source\j__GCCashDbWorkerTWUEAAPEAXIZ_0_140008B93.cpp" />
    <ClCompile Include="Source\j__GCCashDbWorkerTWUEAAPEAXIZ_140005F29.cpp" />
    <ClCompile Include="Source\j__GCCashDbWorkerUSUEAAPEAXIZ_0_14001124D.cpp" />
    <ClCompile Include="Source\j__GCCashDbWorkerUSUEAAPEAXIZ_14000B0B9.cpp" />
    <ClCompile Include="Source\j__GCDummyPosTableUEAAPEAXIZ_0_1400041FB.cpp" />
    <ClCompile Include="Source\j__GCDummyPosTableUEAAPEAXIZ_140002D9C.cpp" />
    <ClCompile Include="Source\j__GCEventLootTableUEAAPEAXIZ_0_14000E5B1.cpp" />
    <ClCompile Include="Source\j__GCEventLootTableUEAAPEAXIZ_140003F3A.cpp" />
    <ClCompile Include="Source\j__GCLogTypeDBTaskManagerIEAAPEAXIZ_140006F82.cpp" />
    <ClCompile Include="Source\j__GCLogTypeDBTaskQEAAPEAXIZ_1400072D9.cpp" />
    <ClCompile Include="Source\j__GCRecordDataUEAAPEAXIZ_140012F99.cpp" />
    <ClCompile Include="Source\j__GCTotalGuildRankRecordQEAAPEAXIZ_140011563.cpp" />
    <ClCompile Include="Source\j__GCUnmannedTraderGroupItemInfoTableIEAAPEAXIZ_140001582.cpp" />
    <ClCompile Include="Source\j__GCUnmannedTraderUserInfoTableIEAAPEAXIZ_140012DC3.cpp" />
    <ClCompile Include="Source\j__GCUserDBUEAAPEAXIZ_14000C3E7.cpp" />
    <ClCompile Include="Source\j__GCWeeklyGuildRankRecordQEAAPEAXIZ_14000C5C7.cpp" />
    <ClCompile Include="Source\j__GDL_GroupParametersImplVEcPrecomputationVECPCry_14000E24B.cpp" />
    <ClCompile Include="Source\j__GDL_GroupParametersImplVEcPrecomputationVECPCry_14000F808.cpp" />
    <ClCompile Include="Source\j__Gtable_objlua_tinkerQEAAPEAXIZ_140006325.cpp" />
    <ClCompile Include="Source\j__init_databaseCashDbWorkerMEAA_NXZ_1400086A7.cpp" />
    <ClCompile Include="Source\j__init_databaseCCashDbWorkerGBMEAA_NXZ_14000AC4A.cpp" />
    <ClCompile Include="Source\j__init_databaseCCashDbWorkerNULLMEAA_NXZ_1400126ED.cpp" />
    <ClCompile Include="Source\j__init_databaseCCashDbWorkerRUMEAA_NXZ_1400135C5.cpp" />
    <ClCompile Include="Source\j__init_loggersCashDbWorkerAEAA_NXZ_140012765.cpp" />
    <ClCompile Include="Source\j__Insert_nvectorPEAVCLogTypeDBTaskVallocatorPEAVC_1400134C1.cpp" />
    <ClCompile Include="Source\j__Iter_catPEAPEAVCLogTypeDBTaskstdYAAUrandom_acce_14000A05B.cpp" />
    <ClCompile Include="Source\j__Iter_randomPEAPEAVCLogTypeDBTaskPEAPEAV1stdYAAU_140013494.cpp" />
    <ClCompile Include="Source\j__LoadBindCMapDataAEAA_NPEADZ_140012936.cpp" />
    <ClCompile Include="Source\j__LoadBspSecCMapDataAEAA_NPEADZ_14000C473.cpp" />
    <ClCompile Include="Source\j__Move_backward_optPEAPEAVCLogTypeDBTaskPEAPEAV1U_1400136B5.cpp" />
    <ClCompile Include="Source\j__Move_catPEAPEAVCLogTypeDBTaskstdYAAU_Undefined__1400113D8.cpp" />
    <ClCompile Include="Source\j__Ptr_catPEAPEAVCLogTypeDBTaskPEAPEAV1stdYAAU_Sca_14000C81A.cpp" />
    <ClCompile Include="Source\j__Ptr_catV_Vector_iteratorPEAVCLogTypeDBTaskVallo_14000C8EC.cpp" />
    <ClCompile Include="Source\j__SetItemInfoLtdWriterAEAAXEPEAU_db_con_STORAGE_L_14000D7CE.cpp" />
    <ClCompile Include="Source\j__SetLtdLtdWriterAEAAXPEAVCUserDBPEAU_LTD_N2Z_140001DA2.cpp" />
    <ClCompile Include="Source\j__TidyvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogT_14000EC3C.cpp" />
    <ClCompile Include="Source\j__UfillvectorPEAVCLogTypeDBTaskVallocatorPEAVCLog_14000EF0C.cpp" />
    <ClCompile Include="Source\j__UmovePEAPEAVCLogTypeDBTaskvectorPEAVCLogTypeDBT_140007A5E.cpp" />
    <ClCompile Include="Source\j__UmoveV_Vector_iteratorPEAVCLogTypeDBTaskValloca_140007F40.cpp" />
    <ClCompile Include="Source\j__Unchecked_move_backwardPEAPEAVCLogTypeDBTaskPEA_1400127BA.cpp" />
    <ClCompile Include="Source\j__Unchecked_uninitialized_movePEAPEAVCLogTypeDBTa_140005AA1.cpp" />
    <ClCompile Include="Source\j__Unchecked_uninitialized_moveV_Vector_iteratorPE_14000A565.cpp" />
    <ClCompile Include="Source\j__Uninit_copyPEAPEAVCLogTypeDBTaskPEAPEAV1Valloca_1400120F8.cpp" />
    <ClCompile Include="Source\j__Uninit_copyV_Vector_iteratorPEAVCLogTypeDBTaskV_1400056F0.cpp" />
    <ClCompile Include="Source\j__Uninit_fill_nPEAPEAVCLogTypeDBTask_KPEAV1Valloc_140001A87.cpp" />
    <ClCompile Include="Source\j__Uninit_movePEAPEAVCLogTypeDBTaskPEAPEAV1Valloca_140010A6E.cpp" />
    <ClCompile Include="Source\j__Uninit_moveV_Vector_iteratorPEAVCLogTypeDBTaskV_140003E31.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_buy_dblogCashDbWorkerIEAAHPEAVTas_14000BD7F.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_buy_dblogCCashDbWorkerNULLIEAAHPE_14000D8F5.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_rollbackCashDbWorkerMEAAHPEAVTask_1400068D4.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_rollbackCCashDbWorkerJPMEAAHPEAVT_1400113DD.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_rollbackCCashDbWorkerNULLMEAAHPEA_14000938B.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_rollbackCCashDbWorkerRUMEAAHPEAVT_14000355D.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_selectCashDbWorkerMEAAHPEAVTaskZ_140001AD7.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_selectCCashDbWorkerGBIEAAHPEAVTas_14000E4A3.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_selectCCashDbWorkerJPMEAAHPEAVTas_14000AE7F.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_selectCCashDbWorkerNULLMEAAHPEAVT_140006118.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_selectCCashDbWorkerRUMEAAHPEAVTas_14000EB83.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_updateCashDbWorkerMEAAHPEAVTaskZ_140010262.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_updateCCashDbWorkerGBIEAAHPEAVTas_140004E7B.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_updateCCashDbWorkerJPMEAAHPEAVTas_140013DEF.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_updateCCashDbWorkerNULLMEAAHPEAVT_14000111D.cpp" />
    <ClCompile Include="Source\j__wait_tsk_cash_updateCCashDbWorkerRUMEAAHPEAVTas_140007A40.cpp" />
    <ClCompile Include="Source\j__wait_tst_cash_total_selling_selectCashDbWorkerI_1400098CC.cpp" />
    <ClCompile Include="Source\j__wait_tst_cash_total_selling_selectCCashDbWorker_14000B357.cpp" />
    <ClCompile Include="Source\j__WriteDBLtdWriterAEAAXKZ_1400085EE.cpp" />
    <ClCompile Include="Source\j__XlenvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogT_140009714.cpp" />
    <ClCompile Include="Source\LoadAllGuildDataCGuildRankingAEAA_NPEAU_worlddb_gu_14033A5D0.cpp" />
    <ClCompile Include="Source\LoadBmpYAPEAEPEADPEAH1Z_1404E96B0.cpp" />
    <ClCompile Include="Source\LoadBossAccmulationWinRateCRaceBossWinRateQEAAEPEA_14024CDB0.cpp" />
    <ClCompile Include="Source\LoadBossCurrentWinRateCRaceBossWinRateQEAA_NXZ_14024CBD0.cpp" />
    <ClCompile Include="Source\LoadBspCBspQEAAXPEADZ_1404FE030.cpp" />
    <ClCompile Include="Source\LoadCCheckSumCharacAccountTrunkDataQEAAHPEAVCRFWor_1402C0750.cpp" />
    <ClCompile Include="Source\LoadCCheckSumGuildDataQEAAHPEAVCRFWorldDatabaseAEA_1402C0EA0.cpp" />
    <ClCompile Include="Source\LoadCUnmannedTraderControllerQEAA_NGKAEAU_TRADE_DB_140078C10.cpp" />
    <ClCompile Include="Source\LoadCUnmannedTraderUserInfoQEAA_NEGKAEAU_TRADE_DB__140353440.cpp" />
    <ClCompile Include="Source\LoadCUnmannedTraderUserInfoTableQEAA_NEGKAEAU_TRAD_140363940.cpp" />
    <ClCompile Include="Source\LoadDatabaseAutoMineMachineQEAA_NPEAU_DB_LOAD_AUTO_1402D0940.cpp" />
    <ClCompile Include="Source\LoadDatabaseCandidateMgrQEAA_NXZ_1402B1730.cpp" />
    <ClCompile Include="Source\LoadDBCHonorGuildQEAA_NXZ_14025E710.cpp" />
    <ClCompile Include="Source\LoadDBCompleteAutominePersonalQEAAXXZ_1402DA620.cpp" />
    <ClCompile Include="Source\LoadDBCRaceBossWinRateQEAA_NXZ_14024CB70.cpp" />
    <ClCompile Include="Source\LoadDBKey_ANIMUSKEYQEAAXEZ_140120520.cpp" />
    <ClCompile Include="Source\LoadDBKey_COMBINEKEYQEAAXHZ_1401BF260.cpp" />
    <ClCompile Include="Source\LoadDBKey_EMBELLKEYQEAAXHZ_1401BF0D0.cpp" />
    <ClCompile Include="Source\LoadDBKey_EQUIPKEYQEAAXFZ_1401BF020.cpp" />
    <ClCompile Include="Source\LoadDBKey_FORCEKEYQEAAXHZ_1401BF0F0.cpp" />
    <ClCompile Include="Source\LoadDBKey_INVENKEYQEAAXHZ_1401BF0B0.cpp" />
    <ClCompile Include="Source\LoadDBKey_LINKKEYQEAAXFZ_1401BF110.cpp" />
    <ClCompile Include="Source\LoadDB_CombineResultItemCombineMgrQEAAEPEAU_combin_1402AD400.cpp" />
    <ClCompile Include="Source\LoadDL_FixedBasePrecomputationImplUEC2NPointCrypto_140575E20.cpp" />
    <ClCompile Include="Source\LoadDL_FixedBasePrecomputationImplUECPPointCryptoP_140578470.cpp" />
    <ClCompile Include="Source\LoadDL_FixedBasePrecomputationImplVIntegerCryptoPP_14056F250.cpp" />
    <ClCompile Include="Source\LoadDummyPositionCDummyPosTableQEAA_NPEAD0Z_14018A180.cpp" />
    <ClCompile Include="Source\LoadGreetingMsgCRFWorldDatabaseQEAA_NPEAD0000000Z_14049B110.cpp" />
    <ClCompile Include="Source\LoadPrevTableCWeeklyGuildRankManagerAEAA_NPEADAEAU_1402CE140.cpp" />
    <ClCompile Include="Source\LoadRecordDataCRecordDataAEAA_NPEAXPEADZ_140044770.cpp" />
    <ClCompile Include="Source\LoadRecordHeaderCRecordDataAEAA_NPEAXPEADZ_140044450.cpp" />
    <ClCompile Include="Source\LoadXMLCUnmannedTraderClassInfoTableCodeTypeUEAA_N_140377190.cpp" />
    <ClCompile Include="Source\LoadXMLCUnmannedTraderClassInfoTableTypeUEAA_NPEAV_14037CDB0.cpp" />
    <ClCompile Include="Source\Load_dbCGuildRoomSystemQEAA_NXZ_1402E9690.cpp" />
    <ClCompile Include="Source\Lobby_Account_CompleteCMainThreadQEAAXPEAU_DB_QRY__1401F3B20.cpp" />
    <ClCompile Include="Source\Lobby_Char_CompleteCUserDBQEAAXEZ_140113850.cpp" />
    <ClCompile Include="Source\Lobby_Char_RequestCUserDBQEAA_NXZ_140113500.cpp" />
    <ClCompile Include="Source\LogCLogTypeDBTaskManagerAEAAXPEADZZ_1402C3650.cpp" />
    <ClCompile Include="Source\LogCRFNewDatabaseIEAAXPEADZ_140484D30.cpp" />
    <ClCompile Include="Source\LogCUnmannedTraderGroupItemInfoTableAEAAXPEADZZ_14036B980.cpp" />
    <ClCompile Include="Source\LogCUnmannedTraderUserInfoTableAEAAXPEADZZ_140365FD0.cpp" />
    <ClCompile Include="Source\LogoutCBillingIDUEAAXPEAVCUserDBZ_14028E350.cpp" />
    <ClCompile Include="Source\LogoutCBillingJPUEAAXPEAVCUserDBZ_14028EB70.cpp" />
    <ClCompile Include="Source\LogoutCBillingManagerQEAAXPEAVCUserDBZ_14007A400.cpp" />
    <ClCompile Include="Source\LogoutCBillingNULLUEAAXPEAVCUserDBZ_14028DBF0.cpp" />
    <ClCompile Include="Source\LogoutCBillingUEAAXPEAVCUserDBZ_14028CD20.cpp" />
    <ClCompile Include="Source\LogOutCUnmannedTraderUserInfoTableQEAAXGKZ_1403659F0.cpp" />
    <ClCompile Include="Source\Logout_Account_CompleteCMainThreadQEAAXPEAU_DB_QRY_1401F3A80.cpp" />
    <ClCompile Include="Source\LoopCLogTypeDBTaskManagerQEAAXXZ_1402C2F40.cpp" />
    <ClCompile Include="Source\luaL_findtable_140539700.cpp" />
    <ClCompile Include="Source\luaL_loadbuffer_140539F90.cpp" />
    <ClCompile Include="Source\luaL_newmetatable_140539520.cpp" />
    <ClCompile Include="Source\luaopen_table_140535D30.cpp" />
    <ClCompile Include="Source\luaV_gettable_14053EA10.cpp" />
    <ClCompile Include="Source\luaV_settable_14053EB20.cpp" />
    <ClCompile Include="Source\lua_createtable_1405339C0.cpp" />
    <ClCompile Include="Source\lua_getmetatable_140533A30.cpp" />
    <ClCompile Include="Source\lua_gettable_1405338A0.cpp" />
    <ClCompile Include="Source\lua_setmetatable_140533CC0.cpp" />
    <ClCompile Include="Source\lua_settable_140533B10.cpp" />
    <ClCompile Include="Source\MakeHashCRecordDataSAKPEBDHZ_1400442B0.cpp" />
    <ClCompile Include="Source\MakeHashTableCRecordDataQEAA_NHHPEADZ_140044020.cpp" />
    <ClCompile Include="Source\MakeLimitItemUpdateQueryCItemStoreManagerQEAAXKEHK_14034A7E0.cpp" />
    <ClCompile Include="Source\MakeLinkTableTimeItemQEAA_NPEADHZ_14030E270.cpp" />
    <ClCompile Include="Source\MakeLootYAPEAU_db_con_STORAGE_LISTEGZ_1401FB840.cpp" />
    <ClCompile Include="Source\MakeNewItemsItemCombineMgrIEAAEPEAU_ITEMCOMBINE_DB_1402AD780.cpp" />
    <ClCompile Include="Source\make_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_con_ST_14023B5B0.cpp" />
    <ClCompile Include="Source\max_sizeallocatorPEAVCLogTypeDBTaskstdQEBA_KXZ_1402C6800.cpp" />
    <ClCompile Include="Source\max_sizevectorPEAVCLogTypeDBTaskVallocatorPEAVCLog_1402C5280.cpp" />
    <ClCompile Include="Source\ModifyPriceCUnmannedTraderUserInfoTableQEAAXGEPEAU_1401D4760.cpp" />
    <ClCompile Include="Source\ODBC___GetSetupProc_1404DB8B8.cpp" />
    <ClCompile Include="Source\OnLButtonDblClkCDisplayViewIEAAXIVCPointZ_14002C1A0.cpp" />
    <ClCompile Include="Source\OnLButtonDblClkCWndIEAAXIVCPointZ_0_1404DC252.cpp" />
    <ClCompile Include="Source\OnLoop_StaticCUserDBSAXXZ_14010FFF0.cpp" />
    <ClCompile Include="Source\OnQueryCancelCPropertyPageUEAAHXZ_0_1404DC336.cpp" />
    <ClCompile Include="Source\OnWizardBackCPropertyPageUEAA_JXZ_0_1404DC33C.cpp" />
    <ClCompile Include="Source\ParamInitCUserDBQEAAXXZ_1401100A0.cpp" />
    <ClCompile Include="Source\personal_amine_installCMgrAvatorItemHistoryQEAAXEG_140240090.cpp" />
    <ClCompile Include="Source\personal_amine_uninstallCMgrAvatorItemHistoryQEAAX_140240250.cpp" />
    <ClCompile Include="Source\popUtablelua_tinkerlua_tinkerYAAUtable0PEAUlua_Sta_140445D80.cpp" />
    <ClCompile Include="Source\post_getpresentCMgrAvatorItemHistoryQEAAXPEADKPEAU_14023E420.cpp" />
    <ClCompile Include="Source\post_returnCMgrAvatorItemHistoryQEAAXPEADKPEAU_db__14023E650.cpp" />
    <ClCompile Include="Source\PrecomputeDL_FixedBasePrecomputationImplUEC2NPoint_140575BA0.cpp" />
    <ClCompile Include="Source\PrecomputeDL_FixedBasePrecomputationImplUECPPointC_1405781E0.cpp" />
    <ClCompile Include="Source\PrecomputeDL_FixedBasePrecomputationImplVIntegerCr_14056EFD0.cpp" />
    <ClCompile Include="Source\price_auto_tradeCMgrAvatorItemHistoryQEAAXHKPEAU_d_140239950.cpp" />
    <ClCompile Include="Source\ProcCompleteCLogTypeDBTaskManagerAEAAXXZ_1402C3390.cpp" />
    <ClCompile Include="Source\ProcThreadCLogTypeDBTaskManagerCAXPEAXZ_1402C3480.cpp" />
    <ClCompile Include="Source\PushCLogTypeDBTaskManagerQEAA_NEPEADGZ_1402C2FA0.cpp" />
    <ClCompile Include="Source\PushCTalkCrystalCombineManagerIEAAEPEAU_db_con_STO_1404312E0.cpp" />
    <ClCompile Include="Source\PushDQSDataCMainThreadQEAAPEAU_DB_QRY_SYN_DATAKPEA_1401F4C50.cpp" />
    <ClCompile Include="Source\PushList_TOWER_PARAMQEAA_NPEAU_db_con_STORAGE_LIST_140079110.cpp" />
    <ClCompile Include="Source\PushRenamePotionDBLogCPotionMgrAEAAXPEADZ_14039F780.cpp" />
    <ClCompile Include="Source\PushSettlementOwnerDBLogCWeeklyGuildRankManagerAEA_1402CDE90.cpp" />
    <ClCompile Include="Source\PushTaskCashDbWorkerQEAA_NHPEAE_KZ_1402EECA0.cpp" />
    <ClCompile Include="Source\PushTaskCCashDBWorkManagerQEAA_NHPEAE_KZ_1402F3340.cpp" />
    <ClCompile Include="Source\PushUpdateBuyRollBackCUnmannedTraderUserInfoTableA_140364A10.cpp" />
    <ClCompile Include="Source\pushUtablelua_tinkerlua_tinkerYAXPEAUlua_StateUtab_140445CE0.cpp" />
    <ClCompile Include="Source\push_backvectorPEAVCLogTypeDBTaskVallocatorPEAVCLo_1402C4ED0.cpp" />
    <ClCompile Include="Source\Push_talk_crystal_matrial_combine_nodeQEAA_NPEAU_d_1404309C0.cpp" />
    <ClCompile Include="Source\QryCaseAddpvppointCMainThreadQEAAXPEAU_DB_QRY_SYN__1401F41F0.cpp" />
    <ClCompile Include="Source\QueryDoubleAttributeTiXmlElementQEBAHPEBDPEANZ_14052FC30.cpp" />
    <ClCompile Include="Source\QueryDoubleValueTiXmlAttributeQEBAHPEANZ_14052D970.cpp" />
    <ClCompile Include="Source\QueryIntAttributeTiXmlElementQEBAHPEBDPEAHZ_14052FB40.cpp" />
    <ClCompile Include="Source\QueryIntValueTiXmlAttributeQEBAHPEAHZ_14052D940.cpp" />
    <ClCompile Include="Source\QueryPerformanceCounter_0_1404DEE32.cpp" />
    <ClCompile Include="Source\QueryPerformanceFrequency_0_1404DEE38.cpp" />
    <ClCompile Include="Source\rdbufbasic_iosDUchar_traitsDstdstdQEBAPEAVbasic_st_1406762EA.cpp" />
    <ClCompile Include="Source\ReadBuddhaEventInfoCExchangeEventQEAAXXZ_14032A7C0.cpp" />
    <ClCompile Include="Source\ReadRecordCEventLootTableQEAA_NXZ_140203AC0.cpp" />
    <ClCompile Include="Source\ReadRecordCItemLootTableQEAA_NPEADPEAVCRecordData0_140203900.cpp" />
    <ClCompile Include="Source\ReadRecordCItemUpgradeTableQEAA_NPEADPEAVCRecordDa_140204850.cpp" />
    <ClCompile Include="Source\ReadRecordCOreCuttingTableQEAA_NPEADPEAVCRecordDat_140204360.cpp" />
    <ClCompile Include="Source\ReadRecordCRecordDataQEAA_NPEADK0Z_140043D60.cpp" />
    <ClCompile Include="Source\ReadRecord_ExCRecordDataQEAA_NPEAD0K0Z_1402AF040.cpp" />
    <ClCompile Include="Source\ReadScriptCMapDataTableQEAA_NPEADZ_140198900.cpp" />
    <ClCompile Include="Source\readUtablelua_tinkerlua_tinkerYAAUtable0PEAUlua_St_140445700.cpp" />
    <ClCompile Include="Source\Rebirth_BaseCRFWorldDatabaseQEAA_NKPEADZ_140493F10.cpp" />
    <ClCompile Include="Source\RefSingletonVvectorGVallocatorGstdstdUNewPrimeTabl_14064D4E0.cpp" />
    <ClCompile Include="Source\Reged_Avator_CompleteCMainThreadQEAAXPEAU_DB_QRY_S_1401F3670.cpp" />
    <ClCompile Include="Source\Reged_Char_CompleteCUserDBQEAAXEPEAU_REGEDPEAU_NOT_140111760.cpp" />
    <ClCompile Include="Source\Reged_Char_RequestCUserDBQEAA_NXZ_140111580.cpp" />
    <ClCompile Include="Source\RegistCheatEndRecordCNationSettingFactoryIEAAXPEAV_140217710.cpp" />
    <ClCompile Include="Source\RegistCheatTableCNationSettingFactoryIEAA_NPEAVCNa_140213530.cpp" />
    <ClCompile Include="Source\RegistCheatTableOnlyInternalCNationSettingFactoryI_1402168B0.cpp" />
    <ClCompile Include="Source\RegistCheatTableUnionCNationSettingFactoryIEAA_NPE_1402135B0.cpp" />
    <ClCompile Include="Source\RegistCodeCNationCodeStrTableAEAAHXZ_14020AE30.cpp" />
    <ClCompile Include="Source\RegistCUnmannedTraderUserInfoTableQEAAXGEPEAU_a_tr_1401D45B0.cpp" />
    <ClCompile Include="Source\Regist_UnmannedTraderSingleItemCRFWorldDatabaseQEA_1404ACC00.cpp" />
    <ClCompile Include="Source\RegQueryValueExA_0_1404DF00C.cpp" />
    <ClCompile Include="Source\reg_auto_tradeCMgrAvatorItemHistoryQEAAXHKPEAU_db__140239670.cpp" />
    <ClCompile Include="Source\ReleaseCashDbWorkerUEAAXXZ_1402EEB60.cpp" />
    <ClCompile Include="Source\ReleaseCCashDbWorkerGBUEAAXXZ_140318DE0.cpp" />
    <ClCompile Include="Source\ReleaseCCashDbWorkerNULLUEAAXXZ_1402F3080.cpp" />
    <ClCompile Include="Source\ReleaseCCashDbWorkerRUUEAAXXZ_1403208D0.cpp" />
    <ClCompile Include="Source\Release_EMBELLISH_LIST_EQUIP_DB_BASEQEAA_NXZ_140120730.cpp" />
    <ClCompile Include="Source\Release_LIST_ANIMUS_DB_BASEQEAA_NXZ_140120940.cpp" />
    <ClCompile Include="Source\Release_LIST_FORCE_DB_BASEQEAA_NXZ_1401208B0.cpp" />
    <ClCompile Include="Source\Release_LIST_INVEN_DB_BASEQEAA_NXZ_1401207C0.cpp" />
    <ClCompile Include="Source\Release_LIST_PERSONALAMINE_INVEN_DB_BASEQEAA_NXZ_140120850.cpp" />
    <ClCompile Include="Source\Release_LIST_TRUNK_DB_BASEQEAA_NXZ_1401209D0.cpp" />
    <ClCompile Include="Source\request_db_queryAutoMineMachineMngQEAAEPEADZ_1402D6C30.cpp" />
    <ClCompile Include="Source\request_queryAutominePersonalMgrQEAAHPEADZ_1402E0720.cpp" />
    <ClCompile Include="Source\ReRangeClientIndexCUserDBSAXPEAU_AVATOR_DATAZ_140119ED0.cpp" />
    <ClCompile Include="Source\ReRegistCUnmannedTraderUserInfoTableQEAAXGEPEAU_un_1401D4BA0.cpp" />
    <ClCompile Include="Source\reservevectorPEAVCLogTypeDBTaskVallocatorPEAVCLogT_1402C4880.cpp" />
    <ClCompile Include="Source\ReSetOldDataLoad_CUTTING_DB_BASEQEAAXXZ_140077C80.cpp" />
    <ClCompile Include="Source\reset_100_per_random_tableQEAAXXZ_1400727E0.cpp" />
    <ClCompile Include="Source\result_db_queryAutoMineMachineMngQEAAXEPEADZ_1402D6D30.cpp" />
    <ClCompile Include="Source\result_queryAutominePersonalMgrQEAAXEPEADZ_1402E09A0.cpp" />
    <ClCompile Include="Source\reward_add_itemCMgrAvatorItemHistoryQEAAXHPEADPEAU_14023C7B0.cpp" />
    <ClCompile Include="Source\re_reg_auto_tradeCMgrAvatorItemHistoryQEAAXHKPEAU__1402397E0.cpp" />
    <ClCompile Include="Source\RNewPrimeTableCryptoPPQEBAPEAVvectorGVallocatorGst_14064D5D0.cpp" />
    <ClCompile Include="Source\RollbackTransactionCRFNewDatabaseQEAA_NXZ_140487530.cpp" />
    <ClCompile Include="Source\SaveDL_FixedBasePrecomputationImplUEC2NPointCrypto_1405760C0.cpp" />
    <ClCompile Include="Source\SaveDL_FixedBasePrecomputationImplUECPPointCryptoP_140578720.cpp" />
    <ClCompile Include="Source\SaveDL_FixedBasePrecomputationImplVIntegerCryptoPP_14056F4F0.cpp" />
    <ClCompile Include="Source\SearchAvatorWithCMSYAPEAVCUserDBPEAV1HPEADZ_14011B610.cpp" />
    <ClCompile Include="Source\SearchAvatorWithNameYAPEAVCUserDBPEAV1HPEADZ_14011B4F0.cpp" />
    <ClCompile Include="Source\SearchCUnmannedTraderUserInfoTableQEAAXGEPEAU_unma_1401D4A90.cpp" />
    <ClCompile Include="Source\SelectAllGuildSerialCRFWorldDatabaseQEAA_NPEAK0Z_1404B73E0.cpp" />
    <ClCompile Include="Source\SelectAllGuildSerialGradeCRFWorldDatabaseQEAA_NPEA_1404B9780.cpp" />
    <ClCompile Include="Source\SelectCleanUpCRFNewDatabaseQEAAXPEADZ_140485300.cpp" />
    <ClCompile Include="Source\SelectSearchListCUnmannedTraderControllerQEAAEPEAD_14034DEA0.cpp" />
    <ClCompile Include="Source\SelectTotalRecordNumCItemStoreManagerQEAA_NPEAKZ_140349FB0.cpp" />
    <ClCompile Include="Source\SelectUsedRecordNumCItemStoreManagerQEAA_NPEAKZ_140349F40.cpp" />
    <ClCompile Include="Source\Select_AccountByAvatorNameCRFWorldDatabaseQEAA_NPE_140494D70.cpp" />
    <ClCompile Include="Source\Select_AccountItemChargeCRFWorldDatabaseQEAA_NKPEA_1404A3E40.cpp" />
    <ClCompile Include="Source\Select_AccountItemCharge_ExtendCRFWorldDatabaseQEA_1404A43E0.cpp" />
    <ClCompile Include="Source\Select_AccountSerialCRFWorldDatabaseQEAA_NPEAD0PEA_14048A380.cpp" />
    <ClCompile Include="Source\Select_AccountTrunkCRFWorldDatabaseQEAAEKEPEAU_wor_14048D360.cpp" />
    <ClCompile Include="Source\Select_AccountTrunkExtendCRFWorldDatabaseQEAAEKPEA_14048DAD0.cpp" />
    <ClCompile Include="Source\Select_AllGuildDataCRFWorldDatabaseQEAA_NPEAU_worl_140499690.cpp" />
    <ClCompile Include="Source\Select_AllGuildNumCRFWorldDatabaseQEAAGXZ_140499340.cpp" />
    <ClCompile Include="Source\select_amine_personalCRFWorldDatabaseQEAAHKPEAU_pe_1404AA360.cpp" />
    <ClCompile Include="Source\select_amine_personalCRFWorldDatabaseQEAAHKZ_1404AA090.cpp" />
    <ClCompile Include="Source\Select_AnimusDataCRFWorldDatabaseQEAAEKEPEANZ_14049F5A0.cpp" />
    <ClCompile Include="Source\Select_ArrangeInfoCRFWorldDatabaseQEAA_NKZ_14049EB30.cpp" />
    <ClCompile Include="Source\select_atrade_taxrateCRFWorldDatabaseQEAAHEPEADAEA_1404A8210.cpp" />
    <ClCompile Include="Source\select_automineCRFWorldDatabaseQEAAHPEAU_DB_LOAD_A_1404A8AB0.cpp" />
    <ClCompile Include="Source\Select_Avator_CompleteCMainThreadQEAAXPEAU_DB_QRY__1401F3890.cpp" />
    <ClCompile Include="Source\Select_BossCryMsgCRFWorldDatabaseQEAAEKPEAU_worldd_1404BB3E0.cpp" />
    <ClCompile Include="Source\Select_BuddyCRFWorldDatabaseQEAAEKPEAU_worlddb_bud_14049E1B0.cpp" />
    <ClCompile Include="Source\Select_CashLimSaleCRFWorldDatabaseQEAAHPEAU_worldd_1404C6780.cpp" />
    <ClCompile Include="Source\Select_CharNumInWorldCRFWorldDatabaseQEAAEKAEAEZ_14048E200.cpp" />
    <ClCompile Include="Source\Select_Char_CompleteCUserDBQEAAXEPEAU_AVATOR_DATAP_1401129C0.cpp" />
    <ClCompile Include="Source\Select_Char_RequestCUserDBQEAA_NEZ_1401125E0.cpp" />
    <ClCompile Include="Source\Select_CheckGreetRecordCRFWorldDatabaseQEAAEHZ_14049BC10.cpp" />
    <ClCompile Include="Source\Select_CheckSumValueCRFWorldDatabaseQEAA_NKPEAKZ_14049DDA0.cpp" />
    <ClCompile Include="Source\Select_ChracterSerialRaceCRFWorldDatabaseQEAA_NPEA_140489F60.cpp" />
    <ClCompile Include="Source\Select_ClearHonorGuildCRFWorldDatabaseQEAAHEAEAKZ_1404C0780.cpp" />
    <ClCompile Include="Source\Select_Economy_HistoryCRFWorldDatabaseQEAAEPEAU_wo_140492A90.cpp" />
    <ClCompile Include="Source\Select_Equal_DeleteName_NoArrangedCRFWorldDatabase_140490510.cpp" />
    <ClCompile Include="Source\Select_Equal_NameCRFWorldDatabaseQEAA_NPEADZ_140490260.cpp" />
    <ClCompile Include="Source\Select_Exist_EconomyCRFWorldDatabaseQEAAEKPEAU_wor_140493130.cpp" />
    <ClCompile Include="Source\Select_GetCharSerialByNameRaceCRFWorldDatabaseQEAA_1404BF350.cpp" />
    <ClCompile Include="Source\Select_GodenBoxItemCRFWorldDatabaseQEAAHPEAU_world_1404C9160.cpp" />
    <ClCompile Include="Source\Select_GuildDataCRFWorldDatabaseQEAA_NKPEAU__guild_140499D70.cpp" />
    <ClCompile Include="Source\Select_GuildMasterLastConnCRFWorldDatabaseQEAAEKKP_1404C3F80.cpp" />
    <ClCompile Include="Source\Select_GuildMemberDataCRFWorldDatabaseQEAA_NGKPEAU_14049A150.cpp" />
    <ClCompile Include="Source\Select_GuildMoneyIODataCRFWorldDatabaseQEAA_NKPEAU_14049A5A0.cpp" />
    <ClCompile Include="Source\Select_GuildRoomInfoCRFWorldDatabaseQEAA_NPEAU_gui_1404B03E0.cpp" />
    <ClCompile Include="Source\Select_GuildSerialCRFWorldDatabaseQEAA_NPEADPEAKZ_140498A70.cpp" />
    <ClCompile Include="Source\Select_HonorGuildCRFWorldDatabaseQEAAHEPEAU_guild__1404C01D0.cpp" />
    <ClCompile Include="Source\Select_InvenCRFWorldDatabaseQEAAEKGPEAU_worlddb_in_14048C660.cpp" />
    <ClCompile Include="Source\Select_IsValidCharCRFWorldDatabaseQEAAHKAEAKZ_1404C17D0.cpp" />
    <ClCompile Include="Source\Select_ItemChargeCRFWorldDatabaseQEAA_NKPEAEPEAKPE_140493FC0.cpp" />
    <ClCompile Include="Source\Select_ItemCombineExCRFWorldDatabaseQEAAEKPEAU_wor_1404A4A00.cpp" />
    <ClCompile Include="Source\Select_LimitInfoCRFWorldDatabaseQEAAEPEAE_KZ_1404C8AE0.cpp" />
    <ClCompile Include="Source\Select_LimitItemEmptyRecordCRFWorldDatabaseQEAAEPE_1404BA170.cpp" />
    <ClCompile Include="Source\Select_LimitItemUsedRecordCRFWorldDatabaseQEAAEEKK_1404BA4F0.cpp" />
    <ClCompile Include="Source\Select_Limit_Run_RecordCRFWorldDatabaseQEAAEXZ_1404C87D0.cpp" />
    <ClCompile Include="Source\Select_MacroDataCRFWorldDatabaseQEAAEKPEAU_AIOC_A__1404A52F0.cpp" />
    <ClCompile Include="Source\Select_NextHourDateCRFNewDatabaseQEAA_NEPEADZ_140487D50.cpp" />
    <ClCompile Include="Source\Select_NpcDataCRFWorldDatabaseQEAAEKPEAKZ_14049F050.cpp" />
    <ClCompile Include="Source\Select_NpcQuest_HistoryCRFWorldDatabaseQEAAEKPEAU__1404C3320.cpp" />
    <ClCompile Include="Source\Select_OldVerPatriarchGroupCRFWorldDatabaseQEAAHEP_1404BC610.cpp" />
    <ClCompile Include="Source\Select_OreCuttingCRFWorldDatabaseQEAAHKPEAU_worldd_1404C57F0.cpp" />
    <ClCompile Include="Source\Select_PatriarchCandidateCRFWorldDatabaseQEAAEKEPE_1404BCBA0.cpp" />
    <ClCompile Include="Source\Select_PatriarchCommCountCRFWorldDatabaseQEAAHKPEA_1404BFB50.cpp" />
    <ClCompile Include="Source\Select_PatriarchCommCRFWorldDatabaseQEAAHKPEAU_pat_1404BF720.cpp" />
    <ClCompile Include="Source\Select_PatriarchElectStateCRFWorldDatabaseQEAAHPEA_1404BB8D0.cpp" />
    <ClCompile Include="Source\Select_PatriarchGroupCRFWorldDatabaseQEAAEEPEAU_ca_1404BDD10.cpp" />
    <ClCompile Include="Source\Select_PatriarchRefundCountCRFWorldDatabaseQEAAHEK_1404BBE50.cpp" />
    <ClCompile Include="Source\Select_PatriarchVotedCRFWorldDatabaseQEAAHEKAEA_NZ_1404BC210.cpp" />
    <ClCompile Include="Source\Select_PatriarchWinCntCRFWorldDatabaseQEAAHEKAEAKZ_1404BE4F0.cpp" />
    <ClCompile Include="Source\Select_PcBangFavorItemCRFWorldDatabaseQEAAHKPEAU_w_1404C6340.cpp" />
    <ClCompile Include="Source\Select_PostContentCRFWorldDatabaseQEAAEKPEADHZ_1404B43F0.cpp" />
    <ClCompile Include="Source\Select_PostRegistryDataCRFWorldDatabaseQEAAEKPEAVC_1404B11C0.cpp" />
    <ClCompile Include="Source\Select_PostStorageEmptyRecordCRFWorldDatabaseQEAAH_1404B18A0.cpp" />
    <ClCompile Include="Source\Select_PostStorageEmptyRecordSerialCRFWorldDatabas_1404B2BD0.cpp" />
    <ClCompile Include="Source\Select_PostStorageListCRFWorldDatabaseQEAAEKPEAU_p_1404B3490.cpp" />
    <ClCompile Include="Source\Select_PostStorageRecordCheckCRFWorldDatabaseQEAA__1404B2790.cpp" />
    <ClCompile Include="Source\Select_PotionDelayCRFWorldDatabaseQEAAHKPEAU_world_1404C5220.cpp" />
    <ClCompile Include="Source\Select_PrimiumPlayTimeCRFWorldDatabaseQEAAHKAEAU_P_1404C4C60.cpp" />
    <ClCompile Include="Source\Select_PunishmentCountCRFWorldDatabaseQEAAHEKPEAKZ_1404BEF90.cpp" />
    <ClCompile Include="Source\Select_PunishmentCRFWorldDatabaseQEAAHKPEAK0Z_1404BE980.cpp" />
    <ClCompile Include="Source\Select_PvpOrderViewInfoCRFWorldDatabaseQEAAHKAEAU__1404C1B80.cpp" />
    <ClCompile Include="Source\Select_PvpPointGuildRankCRFWorldDatabaseQEAAEPEADP_1404A71C0.cpp" />
    <ClCompile Include="Source\Select_PvpPointLimitInfoCRFWorldDatabaseQEAAEKAEAU_1404AFC70.cpp" />
    <ClCompile Include="Source\Select_PvpRankInfoCRFWorldDatabaseQEAAEEPEADPEAU_P_140497510.cpp" />
    <ClCompile Include="Source\Select_PvpRateCRFWorldDatabaseQEAAEKPEADPEAKPEAGZ_1404971A0.cpp" />
    <ClCompile Include="Source\Select_QuestCRFWorldDatabaseQEAAEKPEAU_worlddb_que_140495180.cpp" />
    <ClCompile Include="Source\Select_RaceBossAccumulationWinRateCRFWorldDatabase_1404C2DA0.cpp" />
    <ClCompile Include="Source\Select_RaceBossCurrentWinRateCRFWorldDatabaseQEAAE_1404C28F0.cpp" />
    <ClCompile Include="Source\Select_RegeAvator_For_Lobby_LogoutCRFWorldDatabase_1404C99F0.cpp" />
    <ClCompile Include="Source\Select_ReturnPostCRFWorldDatabaseQEAAEKKPEAU_retur_1404B3A70.cpp" />
    <ClCompile Include="Source\Select_RFEvent_ClassRefineCRFWorldDatabaseQEAAHKAE_1404B47E0.cpp" />
    <ClCompile Include="Source\Select_SFDelayInfoCRFWorldDatabaseQEAAEKPEAU_world_1404C2490.cpp" />
    <ClCompile Include="Source\Select_Start_NpcQuest_HistoryCRFWorldDatabaseQEAAE_1404C3C40.cpp" />
    <ClCompile Include="Source\Select_Start_NpcQuest_History_CountCRFWorldDatabas_1404C3940.cpp" />
    <ClCompile Include="Source\Select_StoreLimitItemCRFWorldDatabaseQEAAEPEAU_qry_1404BA8A0.cpp" />
    <ClCompile Include="Source\Select_TakeItemCRFWorldDatabaseQEAAEKPEAU_worlddb__140494930.cpp" />
    <ClCompile Include="Source\Select_TotalGuildRankCRFWorldDatabaseQEAAEPEADPEAU_1404A66A0.cpp" />
    <ClCompile Include="Source\Select_TotalRecordNumCRFWorldDatabaseQEAAEPEAKZ_1404B9DF0.cpp" />
    <ClCompile Include="Source\Select_TradeCRFWorldDatabaseQEAAEEKEPEAU_worlddb_t_14048CB70.cpp" />
    <ClCompile Include="Source\Select_TrunkMoneyCRFWorldDatabaseQEAA_NKPEANZ_14049F9F0.cpp" />
    <ClCompile Include="Source\Select_UnitCRFWorldDatabaseQEAAEKPEAU_worlddb_unit_14048F6F0.cpp" />
    <ClCompile Include="Source\Select_UnitDataCRFWorldDatabaseQEAAEKPEANZ_1404A0450.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderBuySingleItemInfoCRFWorldData_1404ADB10.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderItemRecordCntByStateCRFWorldD_1404AF100.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderItemStateCRFWorldDatabaseQEAA_1404ACDB0.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderItemStateInfoCntCRFWorldDatab_1404AADF0.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderItemStateInfoCRFWorldDatabase_1404AB110.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderRegisterCRFWorldDatabaseQEAAE_1404AF6B0.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderReservedScheduleCRFWorldDatab_1404AC300.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderSearchGroupTotalRowCountCRFWo_1404AE570.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderSearchPageInfoCRFWorldDatabas_1404AE8B0.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderSellInfoCRFWorldDatabaseQEAAE_1404AD210.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderSingleItemBottomSerialCRFWorl_1404ABCC0.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderSingleItemEmptyRecordCntCRFWo_1404AB6A0.cpp" />
    <ClCompile Include="Source\Select_UnmannedTraderSingleItemEmptyRecordSerialCR_1404ABFC0.cpp" />
    <ClCompile Include="Source\Select_UsedLimitItemRecordNumCRFWorldDatabaseQEAAE_1404B9A70.cpp" />
    <ClCompile Include="Source\Select_UserCountInfoCRFWorldDatabaseQEAA_NPEAD0PEA_140498390.cpp" />
    <ClCompile Include="Source\Select_UserInterfaceCRFWorldDatabaseQEAAEKPEAU_wor_140497A50.cpp" />
    <ClCompile Include="Source\Select_utSellWaitItems_SalesTotalsCRFWorldDatabase_1404C8C60.cpp" />
    <ClCompile Include="Source\Select_WaitItemCRFWorldDatabaseQEAAEKPEAU_worlddb__1404944A0.cpp" />
    <ClCompile Include="Source\Select_WeeklyGuildRankOwnerGuildCRFWorldDatabaseQE_1404A7AC0.cpp" />
    <ClCompile Include="Source\self_cancel_auto_tradeCMgrAvatorItemHistoryQEAAXHK_140239AC0.cpp" />
    <ClCompile Include="Source\ServiceLogCUnmannedTraderUserInfoTableAEAAXPEADZZ_140366060.cpp" />
    <ClCompile Include="Source\SetActPointCUserDBQEAAXEKZ_14011BE90.cpp" />
    <ClCompile Include="Source\SetAutoCommitModeCRFNewDatabaseQEAAX_NZ_140487440.cpp" />
    <ClCompile Include="Source\SetBaseDL_FixedBasePrecomputationImplUEC2NPointCry_1405759B0.cpp" />
    <ClCompile Include="Source\SetBaseDL_FixedBasePrecomputationImplUECPPointCryp_140577FC0.cpp" />
    <ClCompile Include="Source\SetBaseDL_FixedBasePrecomputationImplVIntegerCrypt_14056EDF0.cpp" />
    <ClCompile Include="Source\SetBillingDataCUserDBQEAAXPEADFJPEAU_SYSTEMTIMEZ_140118150.cpp" />
    <ClCompile Include="Source\SetBillingDataCUserDBQEAAXPEAU_BILLING_INFOZ_1401180E0.cpp" />
    <ClCompile Include="Source\SetBillingNoLogoutCUserDBQEAAX_NZ_14007BF80.cpp" />
    <ClCompile Include="Source\SetCahsDBUseExtRefFlagCNationSettingDataQEAAXXZ_14022C740.cpp" />
    <ClCompile Include="Source\SetCashDBDSNCNationSettingDataQEAAXPEAD000KZ_140211B00.cpp" />
    <ClCompile Include="Source\SetCashDBDSNCNationSettingManagerQEAAXPEAD000KZ_140205750.cpp" />
    <ClCompile Include="Source\SetCashDBDSNSetFlagCNationSettingDataQEAAXXZ_140212940.cpp" />
    <ClCompile Include="Source\SetCashDBInitFlagCNationSettingDataQEAAXXZ_1402F2DB0.cpp" />
    <ClCompile Include="Source\SetCashDBInitStateCNationSettingManagerQEAAXXZ_1402F2D60.cpp" />
    <ClCompile Include="Source\SetChatLockCUserDBQEAAX_NZ_1401104A0.cpp" />
    <ClCompile Include="Source\SetCLogTypeDBTaskQEAA_NEPEADGZ_1402C1E10.cpp" />
    <ClCompile Include="Source\SetCompleteCLogTypeDBTaskPoolQEAA_NPEAVCLogTypeDBT_1402C2750.cpp" />
    <ClCompile Include="Source\SetCompleteCLogTypeDBTaskQEAAXXZ_1402C4070.cpp" />
    <ClCompile Include="Source\SetCPvpPointLimiterQEAA_NNPEAV_PVPPOINT_LIMIT_DB_B_140125120.cpp" />
    <ClCompile Include="Source\SetCUnmannedTraderRegistItemInfoQEAA_NGEIAEAU_TRAD_140351EB0.cpp" />
    <ClCompile Include="Source\SetDBPostDataCUserDBQEAAXHKHEH_KKK_N0Z_1401174D0.cpp" />
    <ClCompile Include="Source\SetDBSerialCGoldenBoxItemMgrQEAAXHZ_140412170.cpp" />
    <ClCompile Include="Source\SetEmptyCLogTypeDBTaskPoolQEAA_NPEAVCLogTypeDBTask_1402C2810.cpp" />
    <ClCompile Include="Source\SetEmptyCLogTypeDBTaskQEAAXXZ_1402C4090.cpp" />
    <ClCompile Include="Source\SetFeedbackSizeCFB_ModePolicyCryptoPPMEAAXIZ_14061A8A0.cpp" />
    <ClCompile Include="Source\SetFeedbackSizeCipherModeBaseCryptoPPMEAAXIZ_140452BB0.cpp" />
    <ClCompile Include="Source\SetGoldBoxConsumableCHolyStoneSystemQEAAX_NZ_1400F77D0.cpp" />
    <ClCompile Include="Source\SetGoldBoxItemIndexCGoldenBoxItemMgrQEAA_NXZ_140415250.cpp" />
    <ClCompile Include="Source\SetItemType_InitCSetItemTypeQEAA_NPEAVCRecordDataZ_1402E1D40.cpp" />
    <ClCompile Include="Source\SetKey_LIST_SFCONT_DB_BASEQEAAXEEGEGZ_140120B60.cpp" />
    <ClCompile Include="Source\SetKorTimeCRFDBItemLogQEAAXKZ_14024BF20.cpp" />
    <ClCompile Include="Source\SetLeftTime_LIST_SFCONT_DB_BASEQEAAXGZ_140120C30.cpp" />
    <ClCompile Include="Source\SetLoadAllGuildInfoCGuildRankingAEAA_NPEAU_worlddb_14033A720.cpp" />
    <ClCompile Include="Source\SetLoadInfoCUnmannedTraderUserInfoAEAA_NEKAEAU_TRA_140358590.cpp" />
    <ClCompile Include="Source\SetLogFileCRFNewDatabaseQEAAXPEBD0Z_140488640.cpp" />
    <ClCompile Include="Source\SetLoggerCUnmannedTraderGroupItemInfoTableQEAAXPEA_140351980.cpp" />
    <ClCompile Include="Source\SetLoggerCUnmannedTraderUserInfoTableQEAAXPEAVCLog_140351910.cpp" />
    <ClCompile Include="Source\SetMemory_STORAGE_LISTQEAAXPEAU_db_con1HHHZ_14010EA40.cpp" />
    <ClCompile Include="Source\SetNewDBPostDataCUserDBQEAAXHKHEPEAD000H_KKK1Z_1401176C0.cpp" />
    <ClCompile Include="Source\SetOrder_LIST_SFCONT_DB_BASEQEAAXEZ_14007D320.cpp" />
    <ClCompile Include="Source\SetPoolPointerCEnglandBillingMgrQEAAXPEAVTaskPoolZ_140319790.cpp" />
    <ClCompile Include="Source\SetProcCLogTypeDBTaskPoolQEAA_NPEAVCLogTypeDBTaskA_1402C2690.cpp" />
    <ClCompile Include="Source\SetRadarDelayCUserDBQEAAXKZ_14011B700.cpp" />
    <ClCompile Include="Source\SetRemainTimeCUserDBQEAAXJZ_14028DB40.cpp" />
    <ClCompile Include="Source\SetRetCLogTypeDBTaskQEAAXEEZ_1402C4270.cpp" />
    <ClCompile Include="Source\SetSerialNumber_db_con_STORAGE_LISTQEAAXKZ_14010E160.cpp" />
    <ClCompile Include="Source\SetStaticMember_MASTERY_PARAMSAXPEAVCRecordData0Z_140204DD0.cpp" />
    <ClCompile Include="Source\SetStaticMember_WEAPON_PARAMSAXPEAVCRecordDataZ_140204DB0.cpp" />
    <ClCompile Include="Source\Setting_ClassCUserDBQEAA_NPEADZ_140118010.cpp" />
    <ClCompile Include="Source\SetUpdateDBDataDoNotCheckCItemStoreManagerQEAAXXZ_14034ABB0.cpp" />
    <ClCompile Include="Source\SetUseCLogTypeDBTaskQEAAXXZ_1402C4050.cpp" />
    <ClCompile Include="Source\SetWorldCLIDCUserDBQEAAXKPEAKZ_1401102A0.cpp" />
    <ClCompile Include="Source\Set_EMBELLISH_LIST_EQUIP_DB_BASEQEAA_NPEBU_db_con__140120020.cpp" />
    <ClCompile Include="Source\Set_LIST_ANIMUS_DB_BASEQEAA_NPEBU_db_con_STORAGE_L_140120430.cpp" />
    <ClCompile Include="Source\Set_LIST_FORCE_DB_BASEQEAA_NPEBU_db_con_STORAGE_LI_1401202D0.cpp" />
    <ClCompile Include="Source\Set_LIST_INVEN_DB_BASEQEAA_NPEBU_db_con_STORAGE_LI_140120110.cpp" />
    <ClCompile Include="Source\Set_LIST_PERSONALAMINE_INVEN_DB_BASEQEAA_NPEBU_db__140120220.cpp" />
    <ClCompile Include="Source\Set_LIST_TRUNK_DB_BASEQEAA_NPEBU_db_con_STORAGE_LI_140120540.cpp" />
    <ClCompile Include="Source\Set_REGEDQEAA_NEPEBU_db_con_STORAGE_LISTZ_14011FEF0.cpp" />
    <ClCompile Include="Source\sizevectorPEAVCLogTypeDBTaskVallocatorPEAVCLogType_1402C4C60.cpp" />
    <ClCompile Include="Source\size_cashdb_setting_request_wracQEAAHXZ_140212960.cpp" />
    <ClCompile Include="Source\size_check_queryQEAAHXZ_14047DB10.cpp" />
    <ClCompile Include="Source\size_DB_LOAD_AUTOMINE_MACHINEQEAAHXZ_1402D4070.cpp" />
    <ClCompile Include="Source\size_pt_automine_charge_money_db_update_fail_zoclQ_1402D4020.cpp" />
    <ClCompile Include="Source\size_pt_query_appoint_zoclQEAAHXZ_1402B9CB0.cpp" />
    <ClCompile Include="Source\SQLAllocHandle_0_1404DB9EC.cpp" />
    <ClCompile Include="Source\SQLBindParameter_0_1404DBA10.cpp" />
    <ClCompile Include="Source\SQLCloseCursor_0_1404DB9B0.cpp" />
    <ClCompile Include="Source\SQLConfigDataSourceW_1404DAF68.cpp" />
    <ClCompile Include="Source\SQLConfigDataSource_1404DA820.cpp" />
    <ClCompile Include="Source\SQLConfigDriverW_1404DB28C.cpp" />
    <ClCompile Include="Source\SQLConfigDriver_1404DABB4.cpp" />
    <ClCompile Include="Source\SQLCreateDataSourceW_1404DB040.cpp" />
    <ClCompile Include="Source\SQLCreateDataSource_1404DA940.cpp" />
    <ClCompile Include="Source\SQLEndTran_0_1404DB9FE.cpp" />
    <ClCompile Include="Source\SQLExecDirectA_0_1404DB9C2.cpp" />
    <ClCompile Include="Source\SQLExecDirectW_0_1404DB9F8.cpp" />
    <ClCompile Include="Source\SQLExecDirect_0_1404DB9BC.cpp" />
    <ClCompile Include="Source\SQLExecDirect_RetErrCodeCRFNewDatabaseQEAAEPEADZ_140484ED0.cpp" />
    <ClCompile Include="Source\SQLFetch_0_1404DB9B6.cpp" />
    <ClCompile Include="Source\SQLFetch_RetErrCodeCRFNewDatabaseQEAAEPEADZ_140485020.cpp" />
    <ClCompile Include="Source\SQLFreeHandle_0_1404DB9E6.cpp" />
    <ClCompile Include="Source\SQLGetAvailableDriversW_1404DAF24.cpp" />
    <ClCompile Include="Source\SQLGetAvailableDrivers_1404DA7DC.cpp" />
    <ClCompile Include="Source\SQLGetConfigMode_1404DADA8.cpp" />
    <ClCompile Include="Source\SQLGetData_0_1404DB9AA.cpp" />
    <ClCompile Include="Source\SQLGetData_Binary_RetErrCodeCRFNewDatabaseQEAAEPEA_1404DA460.cpp" />
    <ClCompile Include="Source\SQLGetData_RetErrCodeCRFNewDatabaseQEAAEPEADAEAGFP_140485130.cpp" />
    <ClCompile Include="Source\SQLGetDiagRecA_0_1404DB9C8.cpp" />
    <ClCompile Include="Source\SQLGetDiagRecW_0_1404DB9CE.cpp" />
    <ClCompile Include="Source\SQLGetInstalledDriversW_1404DAEEC.cpp" />
    <ClCompile Include="Source\SQLGetInstalledDrivers_1404DA7A4.cpp" />
    <ClCompile Include="Source\SQLGetPrivateProfileStringW_1404DB150.cpp" />
    <ClCompile Include="Source\SQLGetPrivateProfileString_1404DAA50.cpp" />
    <ClCompile Include="Source\SQLGetTranslatorW_1404DB070.cpp" />
    <ClCompile Include="Source\SQLGetTranslator_1404DA970.cpp" />
    <ClCompile Include="Source\SQLInstallDriverExW_1404DB414.cpp" />
    <ClCompile Include="Source\SQLInstallDriverEx_1404DAD3C.cpp" />
    <ClCompile Include="Source\SQLInstallDriverManagerW_1404DAEB4.cpp" />
    <ClCompile Include="Source\SQLInstallDriverManager_1404DA76C.cpp" />
    <ClCompile Include="Source\SQLInstallDriverW_1404DAE64.cpp" />
    <ClCompile Include="Source\SQLInstallDriver_1404DA71C.cpp" />
    <ClCompile Include="Source\SQLInstallerErrorW_1404DB2F8.cpp" />
    <ClCompile Include="Source\SQLInstallerError_1404DAC20.cpp" />
    <ClCompile Include="Source\SQLInstallODBCW_1404DB000.cpp" />
    <ClCompile Include="Source\SQLInstallODBC_1404DA8D8.cpp" />
    <ClCompile Include="Source\SQLInstallTranslatorExW_1404DB480.cpp" />
    <ClCompile Include="Source\SQLInstallTranslatorEx_1404DADF8.cpp" />
    <ClCompile Include="Source\SQLInstallTranslatorW_1404DB1AC.cpp" />
    <ClCompile Include="Source\SQLInstallTranslator_1404DAAD4.cpp" />
    <ClCompile Include="Source\SQLManageDataSources_1404DA918.cpp" />
    <ClCompile Include="Source\SQLParamData_0_1404DBA0A.cpp" />
    <ClCompile Include="Source\SQLPostInstallerErrorW_1404DB348.cpp" />
    <ClCompile Include="Source\SQLPostInstallerError_1404DAC70.cpp" />
    <ClCompile Include="Source\SQLPutData_0_1404DBA04.cpp" />
    <ClCompile Include="Source\SQLReadFileDSNW_1404DB378.cpp" />
    <ClCompile Include="Source\SQLReadFileDSN_1404DACA0.cpp" />
    <ClCompile Include="Source\SQLRemoveDefaultDataSource_1404DA860.cpp" />
    <ClCompile Include="Source\SQLRemoveDriverManager_1404DAAAC.cpp" />
    <ClCompile Include="Source\SQLRemoveDriverW_1404DB254.cpp" />
    <ClCompile Include="Source\SQLRemoveDriver_1404DAB7C.cpp" />
    <ClCompile Include="Source\SQLRemoveDSNFromIniW_1404DAFD8.cpp" />
    <ClCompile Include="Source\SQLRemoveDSNFromIni_1404DA8B0.cpp" />
    <ClCompile Include="Source\SQLRemoveTranslatorW_1404DB224.cpp" />
    <ClCompile Include="Source\SQLRemoveTranslator_1404DAB4C.cpp" />
    <ClCompile Include="Source\SQLSetConfigMode_1404DADD0.cpp" />
    <ClCompile Include="Source\SQLSetEnvAttr_0_1404DB9E0.cpp" />
    <ClCompile Include="Source\SQLValidDSNW_1404DB0E8.cpp" />
    <ClCompile Include="Source\SQLValidDSN_1404DA9E8.cpp" />
    <ClCompile Include="Source\SQLWriteDSNToIniW_1404DAFA8.cpp" />
    <ClCompile Include="Source\SQLWriteDSNToIni_1404DA880.cpp" />
    <ClCompile Include="Source\SQLWriteFileDSNW_1404DB3D4.cpp" />
    <ClCompile Include="Source\SQLWriteFileDSN_1404DACFC.cpp" />
    <ClCompile Include="Source\SQLWritePrivateProfileStringW_1404DB110.cpp" />
    <ClCompile Include="Source\SQLWritePrivateProfileString_1404DAA10.cpp" />
    <ClCompile Include="Source\StartCCashDBWorkManagerQEAAXXZ_1402F3300.cpp" />
    <ClCompile Include="Source\StartDataBaseCRFNewDatabaseQEAA_NPEBD00Z_140486580.cpp" />
    <ClCompile Include="Source\StartFieldModeCUserDBQEAAXXZ_140110330.cpp" />
    <ClCompile Include="Source\SubCompleteBuyIncreaseVesionCUnmannedTraderUserInf_1403657B0.cpp" />
    <ClCompile Include="Source\SubCompleteBuyProcBuyResultCUnmannedTraderUserInfo_140365650.cpp" />
    <ClCompile Include="Source\sub_140534DB0_140534DB0.cpp" />
    <ClCompile Include="Source\sub_140537DB0_140537DB0.cpp" />
    <ClCompile Include="Source\sub_14053DB10_14053DB10.cpp" />
    <ClCompile Include="Source\sub_14053EDB0_14053EDB0.cpp" />
    <ClCompile Include="Source\sub_140542DB0_140542DB0.cpp" />
    <ClCompile Include="Source\sub_140543DB0_140543DB0.cpp" />
    <ClCompile Include="Source\sub_14055BDB0_14055BDB0.cpp" />
    <ClCompile Include="Source\sub_14055FDB0_14055FDB0.cpp" />
    <ClCompile Include="Source\sub_14057DB50_14057DB50.cpp" />
    <ClCompile Include="Source\sub_14059ADB0_14059ADB0.cpp" />
    <ClCompile Include="Source\sub_140610DB0_140610DB0.cpp" />
    <ClCompile Include="Source\sub_14065DB40_14065DB40.cpp" />
    <ClCompile Include="Source\sub_14065DB60_14065DB60.cpp" />
    <ClCompile Include="Source\sub_14065DB80_14065DB80.cpp" />
    <ClCompile Include="Source\sub_14065DDB0_14065DDB0.cpp" />
    <ClCompile Include="Source\sub_1406DDB20_1406DDB20.cpp" />
    <ClCompile Include="Source\sub_1406DDB60_1406DDB60.cpp" />
    <ClCompile Include="Source\sub_1406DDBA0_1406DDBA0.cpp" />
    <ClCompile Include="Source\sub_1406DDBE0_1406DDBE0.cpp" />
    <ClCompile Include="Source\SymFunctionTableAccess64_0_14066D89E.cpp" />
    <ClCompile Include="Source\SynchINIANDDBCGoldenBoxItemMgrQEAA_NXZ_140412560.cpp" />
    <ClCompile Include="Source\TableExistCRFNewDatabaseQEAA_NPEADZ_1404877C0.cpp" />
    <ClCompile Include="Source\take_ground_itemCMgrAvatorItemHistoryQEAAXHEPEAU_d_1402381B0.cpp" />
    <ClCompile Include="Source\throw_ground_itemCMgrAvatorItemHistoryQEAAXHPEAU_d_140238770.cpp" />
    <ClCompile Include="Source\time_jade_effect_logCMgrAvatorItemHistoryQEAAXPEAD_140240A40.cpp" />
    <ClCompile Include="Source\time_out_cancel_auto_tradeCMgrAvatorItemHistoryQEA_140239C10.cpp" />
    <ClCompile Include="Source\TotalPlayMinCheckCUserDBQEAAXXZ_140113AC0.cpp" />
    <ClCompile Include="Source\tradeCMgrAvatorItemHistoryQEAAXHPEAU_db_con_STORAG_140238D20.cpp" />
    <ClCompile Include="Source\trans_ground_itemCMgrAvatorItemHistoryQEAAXPEAU_db_140238600.cpp" />
    <ClCompile Include="Source\Truncate_UnmannedTraderItemStateRecordCRFWorldData_1404AB400.cpp" />
    <ClCompile Include="Source\trunk_io_itemCMgrAvatorItemHistoryQEAAXHPEAU_db_co_14023CD70.cpp" />
    <ClCompile Include="Source\trunk_swap_itemCMgrAvatorItemHistoryQEAAXHPEAU_db__14023CF40.cpp" />
    <ClCompile Include="Source\tuning_unitCMgrAvatorItemHistoryQEAAXHEPEAU_LIST_U_14023CC00.cpp" />
    <ClCompile Include="Source\UILockInfo_InitCUserDBQEAAXPEADZ_140118340.cpp" />
    <ClCompile Include="Source\UILockInfo_UpdateCUserDBQEAAXPEADZ_1401184D0.cpp" />
    <ClCompile Include="Source\unchecked_copyPEAPEAVCLogTypeDBTaskPEAPEAV1stdextY_1402C6DE0.cpp" />
    <ClCompile Include="Source\unchecked_fill_nPEAPEAVCLogTypeDBTask_KPEAV1stdext_1402C7A00.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_copyPEAPEAVCLogTypeDBTaskP_1402C7EE0.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_copyV_Vector_iteratorPEAVC_1402C7BE0.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_fill_nPEAPEAVCLogTypeDBTas_1402C6F20.cpp" />
    <ClCompile Include="Source\UpdateCCheckSumCharacAccountTrunkDataQEAA_NPEAVCRF_1402C0B60.cpp" />
    <ClCompile Include="Source\UpdateCCheckSumGuildDataQEAA_NPEAVCRFWorldDatabase_1402C10F0.cpp" />
    <ClCompile Include="Source\UpdateClearDanglingOwnerRecordCUnmannedTraderContr_1403505B0.cpp" />
    <ClCompile Include="Source\UpdateContUserSaveCUserDBQEAA_N_NZ_140113DF0.cpp" />
    <ClCompile Include="Source\UpdateDB_CombineResultItemCombineMgrQEAAEPEAU_comb_1402AD230.cpp" />
    <ClCompile Include="Source\UpdateDisappearOwnerRecordCPostSystemManagerQEAA_N_140325240.cpp" />
    <ClCompile Include="Source\UpdateGuildMoneyCRFWorldDatabaseQEAA_NKNNZ_1404A0B10.cpp" />
    <ClCompile Include="Source\UpdateInit_POSTDATA_DB_BASEQEAAXXZ_14011FC50.cpp" />
    <ClCompile Include="Source\UpdateInit_POSTSTORAGE_DB_BASEQEAAXXZ_14011FCC0.cpp" />
    <ClCompile Include="Source\UpdateKillerListCPvpCashPointQEAAXPEAU_PVP_ORDER_V_1403F56E0.cpp" />
    <ClCompile Include="Source\UpdateRankDBRecordCWeeklyGuildRankManagerAEAA_NPEA_1402CE4E0.cpp" />
    <ClCompile Include="Source\UpdateServerResetTokenCRFWorldDatabaseQEAA_NKGKZ_1404C7510.cpp" />
    <ClCompile Include="Source\UpdateTodayTableCWeeklyGuildRankManagerAEAAHPEADPE_1402CE250.cpp" />
    <ClCompile Include="Source\Updatet_Account_Vote_AvailableCRFWorldDatabaseQEAA_1404C7100.cpp" />
    <ClCompile Include="Source\UpdateVotedReset_CheatCRFWorldDatabaseQEAA_NKZ_1404C76F0.cpp" />
    <ClCompile Include="Source\UpdateVotedReset_GeneralCRFWorldDatabaseQEAA_NKZ_1404C7760.cpp" />
    <ClCompile Include="Source\UpdateVotedReset_SupplementCRFWorldDatabaseQEAA_NK_1404C7940.cpp" />
    <ClCompile Include="Source\Update_AddBuddyCUserDBQEAA_NEKPEADZ_140116390.cpp" />
    <ClCompile Include="Source\Update_AlterPvPCashBagCUserDBQEAA_NNZ_140115FC0.cpp" />
    <ClCompile Include="Source\Update_AlterPvPPointCUserDBQEAA_NNZ_140115F80.cpp" />
    <ClCompile Include="Source\update_amine_batteryCRFWorldDatabaseQEAA_NEEKKZ_1404A9360.cpp" />
    <ClCompile Include="Source\update_amine_dckCRFWorldDatabaseQEAA_NEEKZ_1404A91A0.cpp" />
    <ClCompile Include="Source\update_amine_mineoreCRFWorldDatabaseQEAA_NEEKEKEKZ_1404A9450.cpp" />
    <ClCompile Include="Source\update_amine_moveoreCRFWorldDatabaseQEAA_NEEKEKEEK_1404A9570.cpp" />
    <ClCompile Include="Source\update_amine_personalCRFWorldDatabaseQEAA_NPEADZ_1404AA8A0.cpp" />
    <ClCompile Include="Source\update_amine_seloreCRFWorldDatabaseQEAA_NEEKEZ_1404A97B0.cpp" />
    <ClCompile Include="Source\update_amine_workstateCRFWorldDatabaseQEAA_NEEK_NZ_1404A96C0.cpp" />
    <ClCompile Include="Source\Update_AnimusDataCRFWorldDatabaseQEAA_NKEPEANZ_1404A0260.cpp" />
    <ClCompile Include="Source\Update_AutoTradeAllClearCUserDBQEAA_NXZ_140116EC0.cpp" />
    <ClCompile Include="Source\Update_BagNumCUserDBQEAA_NEZ_140116480.cpp" />
    <ClCompile Include="Source\Update_BindCUserDBQEAA_NPEAD0_NZ_1401162F0.cpp" />
    <ClCompile Include="Source\Update_BossCryMsgCUserDBQEAAXEPEADZ_14011B7F0.cpp" />
    <ClCompile Include="Source\Update_CharSlotCRFWorldDatabaseQEAA_NKZ_140498DD0.cpp" />
    <ClCompile Include="Source\Update_ClassCUserDBQEAA_NPEADEGZ_140116000.cpp" />
    <ClCompile Include="Source\Update_ClearWeeklyPvpPointSumCRFWorldDatabaseQEAA__1404A7820.cpp" />
    <ClCompile Include="Source\Update_CombineExResult_PopCUserDBQEAA_NXZ_1401171B0.cpp" />
    <ClCompile Include="Source\Update_CombineExResult_PushCUserDBQEAA_NPEAU_ITEMC_140117140.cpp" />
    <ClCompile Include="Source\Update_CopyAllCUserDBQEAA_NPEAU_AVATOR_DATAZ_140117E30.cpp" />
    <ClCompile Include="Source\Update_CuttingEmptyCUserDBQEAA_NXZ_140116800.cpp" />
    <ClCompile Include="Source\Update_CuttingPushCUserDBQEAA_NEPEAU_LIST_CUTTING__140116560.cpp" />
    <ClCompile Include="Source\Update_CuttingTransCUserDBQEAA_NGGZ_1401166A0.cpp" />
    <ClCompile Include="Source\Update_DalantCRFWorldDatabaseQEAA_NKKZ_1404C9F10.cpp" />
    <ClCompile Include="Source\Update_DelBuddyCUserDBQEAA_NEZ_140116420.cpp" />
    <ClCompile Include="Source\Update_DelPostCUserDBQEAA_NKHZ_140117B00.cpp" />
    <ClCompile Include="Source\Update_DisableInstanceStoreCRFWorldDatabaseQEAA_NK_1404BB320.cpp" />
    <ClCompile Include="Source\Update_DisappearOwnerRecordCRFWorldDatabaseQEAA_NX_1404B1CF0.cpp" />
    <ClCompile Include="Source\Update_ExtTrunkSlotNumCUserDBQEAA_NEZ_140116F70.cpp" />
    <ClCompile Include="Source\Update_GmGreetCRFWorldDatabaseQEAA_NPEAU_qry_case__14049C050.cpp" />
    <ClCompile Include="Source\Update_GoldCRFWorldDatabaseQEAA_NKKZ_1404C9FE0.cpp" />
    <ClCompile Include="Source\Update_GuildEmblemCRFWorldDatabaseQEAA_NKNKKZ_14049B020.cpp" />
    <ClCompile Include="Source\Update_GuildGradeCRFWorldDatabaseQEAA_NXZ_14049CBE0.cpp" />
    <ClCompile Include="Source\Update_GuildGreetCRFWorldDatabaseQEAA_NPEAU_qry_ca_14049C200.cpp" />
    <ClCompile Include="Source\Update_GuildMasterCRFWorldDatabaseQEAA_NKKEZ_1404A65E0.cpp" />
    <ClCompile Include="Source\Update_GuildMemberCountCRFWorldDatabaseQEAA_NKGZ_14049DCD0.cpp" />
    <ClCompile Include="Source\Update_GuildRankCRFWorldDatabaseQEAA_NPEADZ_14049C390.cpp" />
    <ClCompile Include="Source\Update_GuildRank_Step1CRFWorldDatabaseQEAA_NPEADZ_1404B7670.cpp" />
    <ClCompile Include="Source\Update_GuildRank_Step2CRFWorldDatabaseQEAA_NPEADZ_1404B7B00.cpp" />
    <ClCompile Include="Source\Update_GuildRank_Step3CRFWorldDatabaseQEAA_NPEADZ_1404B7E80.cpp" />
    <ClCompile Include="Source\Update_GuildRoomCRFWorldDatabaseQEAA_NKZ_1404B0960.cpp" />
    <ClCompile Include="Source\Update_IncreaseWeeklyGuildKillPvpPointSumCRFWorldD_1404A7020.cpp" />
    <ClCompile Include="Source\Update_InputGuildMoneyCRFWorldDatabaseQEAA_NKKKZ_14049AC40.cpp" />
    <ClCompile Include="Source\Update_ItemAddCUserDBQEAA_NEEPEBU_db_con_STORAGE_L_140114350.cpp" />
    <ClCompile Include="Source\Update_ItemDeleteCUserDBQEAA_NEE_NZ_140114850.cpp" />
    <ClCompile Include="Source\Update_ItemDurCUserDBQEAA_NEE_K_NZ_140114AA0.cpp" />
    <ClCompile Include="Source\Update_ItemSlotCUserDBQEAA_NEEEZ_140115800.cpp" />
    <ClCompile Include="Source\Update_ItemUpgradeCUserDBQEAA_NEEK_NZ_140114F10.cpp" />
    <ClCompile Include="Source\Update_LastAttBuffCUserDBQEAAX_NZ_14007E100.cpp" />
    <ClCompile Include="Source\Update_LimitItemNumCRFWorldDatabaseQEAA_NPEADZ_1404BAF40.cpp" />
    <ClCompile Include="Source\Update_LinkBoardLockCUserDBQEAA_NEZ_140115C40.cpp" />
    <ClCompile Include="Source\Update_LinkBoardSlotCUserDBQEAA_NEEGZ_140115B90.cpp" />
    <ClCompile Include="Source\Update_MacroCUserDBQEAA_NPEADZ_140117200.cpp" />
    <ClCompile Include="Source\Update_MacroDataCRFWorldDatabaseQEAA_NKPEAU_AIOC_A_1404A5CE0.cpp" />
    <ClCompile Include="Source\Update_MapCUserDBQEAA_NEPEAMZ_140116240.cpp" />
    <ClCompile Include="Source\Update_MoneyCUserDBQEAA_NKKZ_1401161F0.cpp" />
    <ClCompile Include="Source\Update_NpcDataCRFWorldDatabaseQEAA_NKPEAKZ_14049F3C0.cpp" />
    <ClCompile Include="Source\Update_NPCQuestHistoryCUserDBQEAA_NEPEAU_NPC_QUEST_14011B880.cpp" />
    <ClCompile Include="Source\Update_OutputGuildMoneyCRFWorldDatabaseQEAA_NKKKZ_14049AD20.cpp" />
    <ClCompile Include="Source\Update_ParamCUserDBQEAA_NPEAU_EXIT_ALTER_PARAMZ_140116990.cpp" />
    <ClCompile Include="Source\Update_PatriarchCommCRFWorldDatabaseQEAAEKKPEADZ_1404C0050.cpp" />
    <ClCompile Include="Source\Update_PlayTimeCUserDBQEAA_NKZ_140116960.cpp" />
    <ClCompile Include="Source\Update_PostCRFWorldDatabaseQEAA_NPEADZ_1404B4790.cpp" />
    <ClCompile Include="Source\Update_PostCUserDBQEAAXHKHEH_KKK0Z_140117900.cpp" />
    <ClCompile Include="Source\Update_PostRegistryCRFWorldDatabaseQEAA_NKKPEAD000_1404B1DB0.cpp" />
    <ClCompile Include="Source\Update_PostRegistryDisableCRFWorldDatabaseQEAA_NKZ_1404B1F20.cpp" />
    <ClCompile Include="Source\Update_PotionNextUseTimeCUserDBQEAAXEKZ_14011BA90.cpp" />
    <ClCompile Include="Source\Update_PunishmentCRFWorldDatabaseQEAA_NPEADZ_1404BEE00.cpp" />
    <ClCompile Include="Source\Update_PvpPointGuildRankRecordCRFWorldDatabaseQEAA_1404A78D0.cpp" />
    <ClCompile Include="Source\Update_PvpPointGuildRankSumLvCRFWorldDatabaseQEAA__1404A79B0.cpp" />
    <ClCompile Include="Source\Update_PvpPointInfoCRFWorldDatabaseQEAA_NKPEAFNZ_140496FF0.cpp" />
    <ClCompile Include="Source\Update_PvpPointLeakCUserDBQEAAXNZ_14011BA60.cpp" />
    <ClCompile Include="Source\Update_QuestDeleteCUserDBQEAA_NEZ_140115620.cpp" />
    <ClCompile Include="Source\Update_QuestInsertCUserDBQEAA_NEPEAU_LIST_QUEST_DB_140115530.cpp" />
    <ClCompile Include="Source\Update_QuestUpdateCUserDBQEAA_NEPEAU_LIST_QUEST_DB_140115700.cpp" />
    <ClCompile Include="Source\Update_RaceGreetCRFWorldDatabaseQEAA_NPEAU_qry_cas_14049C120.cpp" />
    <ClCompile Include="Source\Update_RaceRankCRFWorldDatabaseQEAA_NPEADZ_1404956E0.cpp" />
    <ClCompile Include="Source\Update_RaceRank_Step1CRFWorldDatabaseQEAA_NPEADZ_1404B4E40.cpp" />
    <ClCompile Include="Source\Update_RaceRank_Step2CRFWorldDatabaseQEAA_NPEADZ_1404B51B0.cpp" />
    <ClCompile Include="Source\Update_RaceRank_Step3CRFWorldDatabaseQEAA_NPEADZ_1404B5570.cpp" />
    <ClCompile Include="Source\Update_RaceRank_Step4CRFWorldDatabaseQEAA_NPEADZ_1404B5990.cpp" />
    <ClCompile Include="Source\Update_RaceRank_Step5CRFWorldDatabaseQEAA_NPEADZ_1404B5D20.cpp" />
    <ClCompile Include="Source\Update_RaceRank_Step6CRFWorldDatabaseQEAA_NPEADZ_1404B6D90.cpp" />
    <ClCompile Include="Source\Update_RaceRank_Step7CRFWorldDatabaseQEAA_NPEADZ_1404B6E70.cpp" />
    <ClCompile Include="Source\Update_RaceRank_Step8CRFWorldDatabaseQEAA_NPEADZ_1404B70A0.cpp" />
    <ClCompile Include="Source\Update_RaceRank_Step9CRFWorldDatabaseQEAA_NPEADZ_1404B71F0.cpp" />
    <ClCompile Include="Source\Update_RaceRank_Step_6_1CRFWorldDatabaseQEAA_NPEAD_1404B6A30.cpp" />
    <ClCompile Include="Source\Update_RaceRank_Step_6_2CRFWorldDatabaseQEAA_NPEAD_1404B6B50.cpp" />
    <ClCompile Include="Source\Update_RaceRank_Step_6_3CRFWorldDatabaseQEAA_NPEAD_1404B6C70.cpp" />
    <ClCompile Include="Source\Update_RaceVoteInfoInitCUserDBQEAA_NXZ_14011BB90.cpp" />
    <ClCompile Include="Source\Update_RankInGuildCRFWorldDatabaseQEAA_NKPEAU_worl_14049CC90.cpp" />
    <ClCompile Include="Source\Update_RankInGuild_Step1CRFWorldDatabaseQEAAEKZ_1404B8070.cpp" />
    <ClCompile Include="Source\Update_RankInGuild_Step2CRFWorldDatabaseQEAA_NKZ_1404B8230.cpp" />
    <ClCompile Include="Source\Update_RankInGuild_Step3CRFWorldDatabaseQEAA_NKZ_1404B8B50.cpp" />
    <ClCompile Include="Source\Update_RankInGuild_Step4CRFWorldDatabaseQEAA_NKZ_1404B8CB0.cpp" />
    <ClCompile Include="Source\Update_RankInGuild_Step5CRFWorldDatabaseQEAA_NKPEA_1404B8D50.cpp" />
    <ClCompile Include="Source\Update_RankInGuild_Step6CRFWorldDatabaseQEAA_NXZ_1404B9520.cpp" />
    <ClCompile Include="Source\Update_RankInGuild_Step7CRFWorldDatabaseQEAA_NXZ_1404B95D0.cpp" />
    <ClCompile Include="Source\Update_RankInGuild_Step8CRFWorldDatabaseQEAA_NXZ_1404B9660.cpp" />
    <ClCompile Include="Source\Update_RankInGuild_Step9CRFWorldDatabaseQEAA_NXZ_1404B96F0.cpp" />
    <ClCompile Include="Source\Update_ReturnPostCUserDBQEAAXKZ_140117A80.cpp" />
    <ClCompile Include="Source\Update_RFEvent_ClassRefineCRFWorldDatabaseQEAA_NKE_1404B4D60.cpp" />
    <ClCompile Include="Source\Update_SetActiveCRFWorldDatabaseQEAA_NKPEADEZ_14049EA70.cpp" />
    <ClCompile Include="Source\Update_SetGuildMoneyCRFWorldDatabaseQEAA_NKNNZ_14049AE00.cpp" />
    <ClCompile Include="Source\Update_Set_Limit_RunCRFWorldDatabaseQEAA_NPEAEHZ_1404C8940.cpp" />
    <ClCompile Include="Source\Update_SFContDeleteCUserDBQEAA_NEEZ_140116C30.cpp" />
    <ClCompile Include="Source\Update_SFContInsertCUserDBQEAA_NEEEGEGZ_140116AD0.cpp" />
    <ClCompile Include="Source\Update_SFContUpdateCUserDBQEAA_NEEG_NZ_140116D80.cpp" />
    <ClCompile Include="Source\Update_SFDelayInfoCRFWorldDatabaseQEAA_NKPEAU_worl_1404C23D0.cpp" />
    <ClCompile Include="Source\Update_StartNPCQuestHistoryCUserDBQEAA_NEPEAU_STAR_14011B920.cpp" />
    <ClCompile Include="Source\Update_Start_NpcQuest_HistoryCRFWorldDatabaseQEAA__1404C3850.cpp" />
    <ClCompile Include="Source\Update_StatCUserDBQEAA_NEK_NZ_140116850.cpp" />
    <ClCompile Include="Source\Update_TakeLastCriTicketCUserDBQEAA_NKZ_140117E00.cpp" />
    <ClCompile Include="Source\Update_TakeLastMentalTicketCUserDBQEAA_NKZ_140117DD0.cpp" />
    <ClCompile Include="Source\Update_TrunkHintCUserDBQEAA_NEPEADZ_1401170C0.cpp" />
    <ClCompile Include="Source\Update_TrunkMoneyCUserDBQEAA_NNNZ_140117040.cpp" />
    <ClCompile Include="Source\Update_TrunkSlotNumCUserDBQEAA_NEZ_140116F10.cpp" />
    <ClCompile Include="Source\Update_UnitDataCRFWorldDatabaseQEAA_NKPEANZ_1404A0960.cpp" />
    <ClCompile Include="Source\Update_UnitDataCUserDBQEAA_NEPEAU_LIST_UNIT_DB_BAS_140115440.cpp" />
    <ClCompile Include="Source\Update_UnitDeleteCUserDBQEAA_NEZ_140115360.cpp" />
    <ClCompile Include="Source\Update_UnitInsertCUserDBQEAA_NEPEAU_LIST_UNIT_DB_B_140115270.cpp" />
    <ClCompile Include="Source\Update_UnmannedTraderCheatUpdateRegistDateCRFWorld_1404AFBB0.cpp" />
    <ClCompile Include="Source\Update_UnmannedTraderClearDanglingOwnerRecordCRFWo_1404AB600.cpp" />
    <ClCompile Include="Source\Update_UnmannedTraderItemStateCRFWorldDatabaseQEAA_1404AC7D0.cpp" />
    <ClCompile Include="Source\Update_UnmannedTraderReRegistCRFWorldDatabaseQEAAE_1404AF990.cpp" />
    <ClCompile Include="Source\Update_UnmannedTraderResutlInfoCRFWorldDatabaseQEA_1404AE410.cpp" />
    <ClCompile Include="Source\Update_UnmannedTraderSellInfoCRFWorldDatabaseQEAA__1404ACA80.cpp" />
    <ClCompile Include="Source\Update_UnmannedTraderSellInfoPriceCRFWorldDatabase_1404AD140.cpp" />
    <ClCompile Include="Source\Update_UnmannedTraderSingleItemInfoCRFWorldDatabas_1404AC910.cpp" />
    <ClCompile Include="Source\Update_UnmannedTraderSingleTypeClearUseCompleteRec_1404AF3E0.cpp" />
    <ClCompile Include="Source\Update_UserFatigueCUserDBQEAA_NKZ_14011BDC0.cpp" />
    <ClCompile Include="Source\Update_UserGetScanerCUserDBQEAA_NGGZ_14011BC50.cpp" />
    <ClCompile Include="Source\Update_UserGuildDataCRFWorldDatabaseQEAA_NKKEZ_14049AB60.cpp" />
    <ClCompile Include="Source\Update_UserPlayTimeCUserDBQEAA_NKZ_14011BC10.cpp" />
    <ClCompile Include="Source\Update_UserTLStatusCUserDBQEAA_NEZ_14011BDF0.cpp" />
    <ClCompile Include="Source\Update_UserVoteDataCUserDBQEAA_NXZ_14011BD80.cpp" />
    <ClCompile Include="Source\Update_User_Action_PointCUserDBQEAA_NEKZ_14011BE20.cpp" />
    <ClCompile Include="Source\Update_WindowInfoCUserDBQEAA_NPEAK000K0Z_140115C70.cpp" />
    <ClCompile Include="Source\VirtualQuery_0_1404DEE86.cpp" />
    <ClCompile Include="Source\WriteLog_ChangeClassAfterInitClassCUserDBQEAAXEPEA_14011B280.cpp" />
    <ClCompile Include="Source\WriteLog_CharSelectCUserDBQEAAXXZ_140113C00.cpp" />
    <ClCompile Include="Source\WriteTableDataPartYA_NHPEAVCRecordDataPEADZ_140036A60.cpp" />
    <ClCompile Include="Source\WriteTableDataYA_NHPEAVCRecordData_NPEADZ_140036830.cpp" />
    <ClCompile Include="Source\Y_Vector_const_iteratorPEAVCLogTypeDBTaskVallocato_1402C6BB0.cpp" />
    <ClCompile Include="Source\Y_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEAVC_1402C6B20.cpp" />
    <ClCompile Include="Source\Z_Vector_iteratorPEAVCLogTypeDBTaskVallocatorPEAVC_1402C69C0.cpp" />
    <ClCompile Include="Source\_AllocatePEAVCLogTypeDBTaskstdYAPEAPEAVCLogTypeDBT_1402C6FD0.cpp" />
    <ClCompile Include="Source\_all_rollbackCashDbWorkerMEAAXPEBU_param_cash_upda_1402F0440.cpp" />
    <ClCompile Include="Source\_all_rollbackCCashDbWorkerJPMEAAXPEBU_param_cash_u_140320350.cpp" />
    <ClCompile Include="Source\_all_rollbackCCashDbWorkerNULLMEAAXPEBU_param_cash_1402F30D0.cpp" />
    <ClCompile Include="Source\_BuyvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogType_1402C57D0.cpp" />
    <ClCompile Include="Source\_CashDbWorkerCashDbWorker__1_dtor0_14022B800.cpp" />
    <ClCompile Include="Source\_CashDbWorker_all_rollback__1_dtor0_1402F06F0.cpp" />
    <ClCompile Include="Source\_CashDbWorker_CashDbWorker__1_dtor0_14022B950.cpp" />
    <ClCompile Include="Source\_CashDbWorker_complete_tsk_cash_update__1_dtor0_1402EFCD0.cpp" />
    <ClCompile Include="Source\_CashDbWorker_init_database__1_dtor0_1402F0B50.cpp" />
    <ClCompile Include="Source\_CCashDbWorkerGB_init_database__1_dtor0_1403193A0.cpp" />
    <ClCompile Include="Source\_CCashDbWorkerJP_all_rollback__1_dtor0_140320670.cpp" />
    <ClCompile Include="Source\_CCashDbWorkerRU_init_database__1_dtor0_140320CA0.cpp" />
    <ClCompile Include="Source\_CDummyPosTableLoadDummyPosition__1_dtor0_14018A5B0.cpp" />
    <ClCompile Include="Source\_CEnglandBillingMgrCallFunc_Item_Buy__1_dtor0_14031A140.cpp" />
    <ClCompile Include="Source\_CEventLootTableReadRecord__1_dtor0_140203D40.cpp" />
    <ClCompile Include="Source\_CGoldenBoxItemMgrSetGoldBoxItemIndex__1_dtor0_1404154D0.cpp" />
    <ClCompile Include="Source\_CItemLootTableCItemLootTable__1_dtor0_1402025A0.cpp" />
    <ClCompile Include="Source\_CItemLootTableCItemLootTable__1_dtor1_1402025D0.cpp" />
    <ClCompile Include="Source\_CItemLootTable_CItemLootTable__1_dtor0_140202910.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskManagerInitLogger__1_dtor0_1402C3820.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskManagerInit__1_dtor0_1402C2E20.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskManagerInstance__1_dtor0_1402C2C20.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskManagerProcThread__1_filt0_1402C3520.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskManager_CLogTypeDBTaskManager__1_dt_1402C2B60.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskPoolCLogTypeDBTaskPool__1_dtor0_1402C1F30.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskPoolCLogTypeDBTaskPool__1_dtor1_1402C1F60.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskPoolCLogTypeDBTaskPool__1_dtor2_1402C1F90.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskPoolDestroy__1_dtor0_1402C2A30.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskPoolDestroy__1_dtor1_1402C2A60.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskPoolInit__1_dtor0_1402C2400.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskPool_CLogTypeDBTaskPool__1_dtor0_1402C2050.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskPool_CLogTypeDBTaskPool__1_dtor1_1402C2080.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskPool_CLogTypeDBTaskPool__1_dtor2_1402C20B0.cpp" />
    <ClCompile Include="Source\_CLogTypeDBTaskPool_CLogTypeDBTaskPool__1_dtor3_1402C20E0.cpp" />
    <ClCompile Include="Source\_CMainThreadDatabaseInit__1_dtor0_1401ED4B0.cpp" />
    <ClCompile Include="Source\_CMainThreaddb_buy_emblem__1_dtor0_1401B2240.cpp" />
    <ClCompile Include="Source\_CMainThreaddb_input_guild_money_atradetax__1_dtor_1401B1E30.cpp" />
    <ClCompile Include="Source\_CMainThreaddb_input_guild_money__1_dtor0_1401B1310.cpp" />
    <ClCompile Include="Source\_CMainThreaddb_Insert_guild__1_dtor0_1401B0E60.cpp" />
    <ClCompile Include="Source\_CMainThreaddb_output_guild_money__1_dtor0_1401B1B50.cpp" />
    <ClCompile Include="Source\_CMainThreaddb_Update_Avator__1_dtor0_1401A64B0.cpp" />
    <ClCompile Include="Source\_CMainThread_db_Check_NpcData__1_dtor0_1401A9D30.cpp" />
    <ClCompile Include="Source\_CMainThread_db_Check_NpcData__1_dtor1_1401A9D60.cpp" />
    <ClCompile Include="Source\_CMainThread_db_Load_Start_NpcQuest_History__1_dto_1401A85F0.cpp" />
    <ClCompile Include="Source\_CMainThread_db_Load_Start_NpcQuest_History__1_dto_1401A8620.cpp" />
    <ClCompile Include="Source\_CMapDataTableReadScript__1_dtor0_140198E60.cpp" />
    <ClCompile Include="Source\_CMapData_LoadBind__1_dtor0_140183580.cpp" />
    <ClCompile Include="Source\_CMonsterSPGroupTableCreate__1_dtor0_14015EE30.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableGetCode__1_dtor0_14020AD80.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableRegistCode__1_dtor0_14020B470.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableRegistCode__1_dtor10_14020B650.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableRegistCode__1_dtor11_14020B680.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableRegistCode__1_dtor1_14020B4A0.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableRegistCode__1_dtor2_14020B4D0.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableRegistCode__1_dtor3_14020B500.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableRegistCode__1_dtor4_14020B530.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableRegistCode__1_dtor5_14020B560.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableRegistCode__1_dtor6_14020B590.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableRegistCode__1_dtor7_14020B5C0.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableRegistCode__1_dtor8_14020B5F0.cpp" />
    <ClCompile Include="Source\_CNationCodeStrTableRegistCode__1_dtor9_14020B620.cpp" />
    <ClCompile Include="Source\_CNationSettingFactoryRegistCheatEndRecord__1_dtor_140217870.cpp" />
    <ClCompile Include="Source\_complete_tsk_cash_rollbackCashDbWorkerIEAAXPEAVTa_1402EFE80.cpp" />
    <ClCompile Include="Source\_complete_tsk_cash_selectCashDbWorkerIEAAXPEAVTask_1402EF070.cpp" />
    <ClCompile Include="Source\_complete_tsk_cash_total_selling_selectCashDbWorke_1402F03B0.cpp" />
    <ClCompile Include="Source\_complete_tsk_cash_updateCashDbWorkerIEAAXPEAVTask_1402EF290.cpp" />
    <ClCompile Include="Source\_ConstructPEAVCLogTypeDBTaskPEAV1stdYAXPEAPEAVCLog_1402C83C0.cpp" />
    <ClCompile Include="Source\_Copy_backward_optPEAPEAVCLogTypeDBTaskPEAPEAV1Ura_1402C7B10.cpp" />
    <ClCompile Include="Source\_Copy_optPEAPEAVCLogTypeDBTaskPEAPEAV1Urandom_acce_1402C74F0.cpp" />
    <ClCompile Include="Source\_COreCuttingTableReadRecord__1_dtor0_1402044C0.cpp" />
    <ClCompile Include="Source\_CreateTableLtdWriterAEAAXXZ_14024A3D0.cpp" />
    <ClCompile Include="Source\_CRecordDataReadRecord_Ex__1_dtor0_1402AF490.cpp" />
    <ClCompile Include="Source\_CRecordDataReadRecord_Ex__1_dtor1_1402AF4C0.cpp" />
    <ClCompile Include="Source\_CRFNewDatabaseCRFNewDatabase__1_dtor0_1404860C0.cpp" />
    <ClCompile Include="Source\_CRFNewDatabaseCRFNewDatabase__1_dtor1_1404860F0.cpp" />
    <ClCompile Include="Source\_CRFNewDatabaseCRFNewDatabase__1_dtor2_140486120.cpp" />
    <ClCompile Include="Source\_CRFNewDatabase_CRFNewDatabase__1_dtor0_140486200.cpp" />
    <ClCompile Include="Source\_CRFNewDatabase_CRFNewDatabase__1_dtor1_140486230.cpp" />
    <ClCompile Include="Source\_CRFNewDatabase_CRFNewDatabase__1_dtor2_140486260.cpp" />
    <ClCompile Include="Source\_CRFNewDatabase_CRFNewDatabase__1_dtor3_140486290.cpp" />
    <ClCompile Include="Source\_CrtDbgReportW_0_1404DC904.cpp" />
    <ClCompile Include="Source\_CrtDbgReport_0_140676DA4.cpp" />
    <ClCompile Include="Source\_CryptoPPCipherModeBaseSetFeedbackSize__1_dtor0_140452C70.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_FixedBasePrecomputationImpl_CryptoPPEC_140449230.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_FixedBasePrecomputationImpl_CryptoPPEC_140449260.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_FixedBasePrecomputationImpl_CryptoPPEC_14044BEF0.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_FixedBasePrecomputationImpl_CryptoPPEC_14044BF20.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_GroupParametersImpl_CryptoPPEcPrecompu_140449D40.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_GroupParametersImpl_CryptoPPEcPrecompu_140449D70.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_GroupParametersImpl_CryptoPPEcPrecompu_140454880.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_GroupParametersImpl_CryptoPPEcPrecompu_1404548C0.cpp" />
    <ClCompile Include="Source\_CryptoPPDL_GroupParametersImpl_CryptoPPEcPrecompu_1404548F0.cpp" />
    <ClCompile Include="Source\_CryptoPPGeneratableCryptoMaterialGenerateRandom___14044BC30.cpp" />
    <ClCompile Include="Source\_CTSingleton_CCashDBWorkManager_Instance__1_dtor0_1401C7840.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableCodeTypeCreate__1_dt_140377A00.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableCodeTypeCUnmannedTra_1403770D0.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableCodeTypeGetGroupID___140377580.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableCodeTypeGetGroupID___1403775B0.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableCodeTypeLoadXML__1_d_140377370.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableCodeTypeoperator___1_140377750.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableCodeTypeoperator___1_140377780.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableCodeType_CUnmannedTr_140377160.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableTypeCreate__1_dtor0_14037E050.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableTypeCUnmannedTraderC_14037CCB0.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableTypeGetGroupID__1_dt_14037D6B0.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableTypeGetGroupID__1_dt_14037D6E0.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableTypeIsExistGroupID___14037D470.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableTypeIsExistGroupID___14037D4A0.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableTypeIsValidID__1_dto_14037DB80.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableTypeIsValidID__1_dto_14037DBB0.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableTypeLoadXML__1_dtor0_14037D280.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableTypeoperator___1_dto_14037D8A0.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableTypeoperator___1_dto_14037D8D0.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableType_CUnmannedTrader_14037CD50.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderClassInfoTableType_CUnmannedTrader_14037CD80.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderGroupItemInfoTableCUnmannedTraderG_14036B030.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderGroupItemInfoTableInit__1_dtor0_14036B330.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderGroupItemInfoTableInstance__1_dtor_14036B180.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderGroupItemInfoTable_CUnmannedTrader_14036B0C0.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderUserInfoTableFind__1_dtor0_140365C30.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderUserInfoTableFind__1_dtor2_140365C60.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderUserInfoTableFind__1_dtor3_140365C90.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderUserInfoTableInit__1_dtor0_140363910.cpp" />
    <ClCompile Include="Source\_CUnmannedTraderUserInfoTableInstance__1_dtor0_140363750.cpp" />
    <ClCompile Include="Source\_CUserDBCUserDB__1_dtor0_14010FE00.cpp" />
    <ClCompile Include="Source\_CUserDBCUserDB__1_dtor1_14010FE30.cpp" />
    <ClCompile Include="Source\_CUserDBCUserDB__1_dtor2_14010FE60.cpp" />
    <ClCompile Include="Source\_CUserDBCUserDB__1_dtor3_14010FE90.cpp" />
    <ClCompile Include="Source\_CUserDBExit_Account_Request__1_dtor0_1401113B0.cpp" />
    <ClCompile Include="Source\_CUserDBLobby_Char_Request__1_dtor0_140113820.cpp" />
    <ClCompile Include="Source\_CUserDBReRangeClientIndex__1_dtor0_14011A5D0.cpp" />
    <ClCompile Include="Source\_CUserDBSelect_Char_Complete__1_dtor0_1401134D0.cpp" />
    <ClCompile Include="Source\_CUserDBSelect_Char_Request__1_dtor0_140112990.cpp" />
    <ClCompile Include="Source\_CUserDBUpdateContUserSave__1_dtor0_140114190.cpp" />
    <ClCompile Include="Source\_CUserDB_CUserDB__1_dtor0_14010FF60.cpp" />
    <ClCompile Include="Source\_CUserDB_CUserDB__1_dtor1_14010FF90.cpp" />
    <ClCompile Include="Source\_CUserDB_CUserDB__1_dtor2_14010FFC0.cpp" />
    <ClCompile Include="Source\_DbgOutYAJPEADKJ0Z_140437210.cpp" />
    <ClCompile Include="Source\_db_Check_NpcDataCMainThreadAEAAEKPEAU_AVATOR_DATA_1401A9B20.cpp" />
    <ClCompile Include="Source\_db_complete_event_classrefineCMainThreadQEAAXGKEK_1401BFC10.cpp" />
    <ClCompile Include="Source\_db_complete_update_event_classrefineCMainThreadQE_1401BFD30.cpp" />
    <ClCompile Include="Source\_db_GuildRoom_InsertCMainThreadAEAAEPEAU_qry_case__1401B2CC0.cpp" />
    <ClCompile Include="Source\_db_GuildRoom_UpdateCMainThreadAEAA_NPEAU_qry_case_1401B2D30.cpp" />
    <ClCompile Include="Source\_db_init_classrefine_countCMainThreadQEAAHXZ_1401BFDB0.cpp" />
    <ClCompile Include="Source\_db_loadTRC_AutoTradeQEAA_NEZ_1402D8EA0.cpp" />
    <ClCompile Include="Source\_db_Load_BaseCMainThreadAEAAEKPEAU_AVATOR_DATAZ_1401A6620.cpp" />
    <ClCompile Include="Source\_db_Load_BuddyCMainThreadAEAAEKPEAU_BUDDY_DB_BASEZ_1401A8650.cpp" />
    <ClCompile Include="Source\_db_Load_Cash_LimSaleCMainThreadAEAAEPEAUqry_case__1401B7740.cpp" />
    <ClCompile Include="Source\_db_Load_CryMsgCMainThreadAEAAEKPEAU_CRYMSG_DB_BAS_1401A97E0.cpp" />
    <ClCompile Include="Source\_db_load_event_classrefineCMainThreadQEAAEKAEAEAEA_1401BFB50.cpp" />
    <ClCompile Include="Source\_db_Load_GeneralCMainThreadAEAAEKEPEAU_AVATOR_DATA_1401A6870.cpp" />
    <ClCompile Include="Source\_db_Load_GoldBoxItemCMainThreadAEAAEPEAUqry_case_s_1401B8500.cpp" />
    <ClCompile Include="Source\_db_Load_InvenCMainThreadAEAAEKHPEAU_INVEN_DB_BASE_1401A75B0.cpp" />
    <ClCompile Include="Source\_db_Load_ItemCombineExCMainThreadAEAAEKPEAU_ITEMCO_1401A90B0.cpp" />
    <ClCompile Include="Source\_db_Load_MacroDataCMainThreadAEAAEKPEAU_AIOC_A_MAC_1401A9350.cpp" />
    <ClCompile Include="Source\_db_Load_NpcQuest_HistoryCMainThreadAEAAEKPEAU_QUE_1401A80D0.cpp" />
    <ClCompile Include="Source\_db_Load_OreCuttingCMainThreadAEAAEKPEAU_CUTTING_D_1401B6DA0.cpp" />
    <ClCompile Include="Source\_db_Load_PatriarchCommCMainThreadAEAAHPEADZ_1401A7250.cpp" />
    <ClCompile Include="Source\_db_Load_PcBangFavorCMainThreadAEAAEKPEAU_PCBANG_F_1401B73F0.cpp" />
    <ClCompile Include="Source\_db_Load_PotionDelayCMainThreadAEAAEKPEAU_POTION_N_1401B6990.cpp" />
    <ClCompile Include="Source\_db_Load_PrimiumPlayTimeCMainThreadAEAAEKAEAU_PCBA_1401B65E0.cpp" />
    <ClCompile Include="Source\_db_load_punishmentCMainThreadAEAAEKPEAU_AVATOR_DA_1401A9970.cpp" />
    <ClCompile Include="Source\_db_Load_PvpOrderViewCMainThreadAEAAEKAEAU_PVP_ORD_1401B4CB0.cpp" />
    <ClCompile Include="Source\_db_Load_PvpPointLimitDataCMainThreadAEAAEKAEAV_PV_1401A96B0.cpp" />
    <ClCompile Include="Source\_db_Load_QuestCMainThreadAEAAEKPEAU_QUEST_DB_BASEZ_1401A7E70.cpp" />
    <ClCompile Include="Source\_db_load_racebossCMainThreadAEAAEKPEAU_AVATOR_DATA_1401A7160.cpp" />
    <ClCompile Include="Source\_db_Load_SFDelayDataCMainThreadAEAAEKPEAU_worlddb__1401B54A0.cpp" />
    <ClCompile Include="Source\_db_Load_Start_NpcQuest_HistoryCMainThreadAEAAEKEP_1401A82C0.cpp" />
    <ClCompile Include="Source\_db_Load_SupplementCMainThreadAEAAEKPEAU_SUPPLEMEN_1401B55B0.cpp" />
    <ClCompile Include="Source\_db_Load_TimeLimitInfoCMainThreadAEAAEKPEAU_TIMELI_1401B7C60.cpp" />
    <ClCompile Include="Source\_db_Load_TradeCMainThreadAEAAEEKPEAU_TRADE_DB_BASE_1401A8DA0.cpp" />
    <ClCompile Include="Source\_db_Load_TrunkCMainThreadAEAAEKKEPEAU_TRUNK_DB_BAS_1401A8830.cpp" />
    <ClCompile Include="Source\_db_Load_UICMainThreadAEAAEKPEAU_LINK_DB_BASEPEAU__1401A7BA0.cpp" />
    <ClCompile Include="Source\_db_Load_UnitCMainThreadAEAAEKPEAU_UNIT_DB_BASEZ_1401A77C0.cpp" />
    <ClCompile Include="Source\_db_qry_insert_newownerAutoMineMachineMngAEAAEPEAD_1402D6DC0.cpp" />
    <ClCompile Include="Source\_db_qry_update_battery_chargeAutoMineMachineMngAEA_1402D6ED0.cpp" />
    <ClCompile Include="Source\_db_qry_update_battery_dischargeAutoMineMachineMng_1402D6FC0.cpp" />
    <ClCompile Include="Source\_db_qry_update_mineoreAutoMineMachineMngAEAAEPEADZ_1402D7040.cpp" />
    <ClCompile Include="Source\_db_qry_update_moveoreAutoMineMachineMngAEAAEPEADZ_1402D71F0.cpp" />
    <ClCompile Include="Source\_db_qry_update_seloreAutoMineMachineMngAEAAEPEADZ_1402D7170.cpp" />
    <ClCompile Include="Source\_db_qry_update_workstateAutoMineMachineMngAEAAEPEA_1402D70F0.cpp" />
    <ClCompile Include="Source\_db_Select_RegeAvator_For_Lobby_LogoutCMainThreadA_1401B8D70.cpp" />
    <ClCompile Include="Source\_db_Update_BaseCMainThreadAEAA_NKPEAU_AVATOR_DATA0_1401A9D90.cpp" />
    <ClCompile Include="Source\_db_Update_BuddyCMainThreadAEAA_NKPEAU_AVATOR_DATA_1401AE1F0.cpp" />
    <ClCompile Include="Source\_db_Update_Cash_LimSaleCMainThreadAEAAEPEAU_db_cas_1401B7920.cpp" />
    <ClCompile Include="Source\_db_Update_CryMsgCMainThreadAEAA_NKPEAU_AVATOR_DAT_1401B0910.cpp" />
    <ClCompile Include="Source\_db_Update_Data_For_TradeCMainThreadAEAAEPEADZ_1401B91D0.cpp" />
    <ClCompile Include="Source\_db_update_event_classrefineCMainThreadQEAAEGKEKZ_1401BFCC0.cpp" />
    <ClCompile Include="Source\_db_Update_GeneralCMainThreadAEAA_NKPEAU_AVATOR_DA_1401AA500.cpp" />
    <ClCompile Include="Source\_db_Update_GoldBoxItemCMainThreadAEAAEHPEAU_db_gol_1401B87D0.cpp" />
    <ClCompile Include="Source\_db_Update_InvenCMainThreadAEAA_NKPEAU_AVATOR_DATA_1401ABFC0.cpp" />
    <ClCompile Include="Source\_db_update_inven_AMPCMainThreadAEAA_NKPEAU_AVATOR__1401AFA70.cpp" />
    <ClCompile Include="Source\_db_Update_ItemCombineExCMainThreadAEAA_NKPEAU_AVA_1401B02A0.cpp" />
    <ClCompile Include="Source\_db_Update_MacroDataCMainThreadAEAA_NKPEAU_AIOC_A__1401B0660.cpp" />
    <ClCompile Include="Source\_db_Update_NpcDataCMainThreadAEAA_NKPEAU_AVATOR_DA_1401AE3F0.cpp" />
    <ClCompile Include="Source\_db_Update_NpcQuest_HistoryCMainThreadAEAA_NKPEAU__1401ADCA0.cpp" />
    <ClCompile Include="Source\_db_Update_OreCuttingCMainThreadAEAA_NKPEAU_AVATOR_1401B6F60.cpp" />
    <ClCompile Include="Source\_db_Update_PcBangFavorCMainThreadAEAA_NKPEAU_AVATO_1401B7530.cpp" />
    <ClCompile Include="Source\_db_Update_PotionDelayCMainThreadAEAA_NKPEAU_AVATO_1401B6AF0.cpp" />
    <ClCompile Include="Source\_db_Update_PrimiumPlayTimeCMainThreadAEAA_NKPEAU_A_1401B66A0.cpp" />
    <ClCompile Include="Source\_db_Update_PvpOrderViewCMainThreadAEAA_NKPEAU_AVAT_1401B4EA0.cpp" />
    <ClCompile Include="Source\_db_Update_PvpPointLimitCMainThreadAEAA_NKPEAU_AVA_1401AFE60.cpp" />
    <ClCompile Include="Source\_db_Update_QuestCMainThreadAEAA_NKPEAU_AVATOR_DATA_1401AD800.cpp" />
    <ClCompile Include="Source\_db_Update_Set_Limit_RunCMainThreadAEAAEXZ_1401B7FC0.cpp" />
    <ClCompile Include="Source\_db_Update_SFDelayDataCMainThreadAEAA_NKPEAU_AVATO_1401B5540.cpp" />
    <ClCompile Include="Source\_db_Update_Start_NpcQuest_HistoryCMainThreadAEAA_N_1401ADF60.cpp" />
    <ClCompile Include="Source\_db_Update_SupplementCMainThreadAEAA_NKPEAU_AVATOR_1401B5900.cpp" />
    <ClCompile Include="Source\_db_Update_TimeLimitInfoCMainThreadAEAAEKPEAU_AVAT_1401B7D60.cpp" />
    <ClCompile Include="Source\_db_Update_TrunkCMainThreadAEAA_NKPEAU_AVATOR_DATA_1401AE630.cpp" />
    <ClCompile Include="Source\_db_Update_Trunk_ExtendCMainThreadAEAA_NKPEAU_AVAT_1401AF150.cpp" />
    <ClCompile Include="Source\_db_Update_UICMainThreadAEAA_NKPEAU_AVATOR_DATA0PE_1401AD0D0.cpp" />
    <ClCompile Include="Source\_db_Update_UnitCMainThreadAEAA_NKPEAU_AVATOR_DATA0_1401AC7C0.cpp" />
    <ClCompile Include="Source\_DDL_GroupParametersImplVEcPrecomputationVEC2NCryp_1405ADEA0.cpp" />
    <ClCompile Include="Source\_DDL_GroupParametersImplVEcPrecomputationVECPCrypt_14046A540.cpp" />
    <ClCompile Include="Source\_DestroyPEAVCLogTypeDBTaskstdYAXPEAPEAVCLogTypeDBT_1402C8460.cpp" />
    <ClCompile Include="Source\_DestroyvectorPEAVCLogTypeDBTaskVallocatorPEAVCLog_1402C58D0.cpp" />
    <ClCompile Include="Source\_Destroy_rangePEAVCLogTypeDBTaskVallocatorPEAVCLog_1402C6EA0.cpp" />
    <ClCompile Include="Source\_Destroy_rangePEAVCLogTypeDBTaskVallocatorPEAVCLog_1402C75B0.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CCashDbWorkerNULLm_1406E9140.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CUserDBs_logAvator_1406E84B0.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CUserDBs_MgrLobbyH_1406E84F0.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__CUserDBs_MoveLobby_1406E8470.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CCashDbWorkerNULLms_NULL_1406E1B40.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CUserDBs_logAvatorDB___1406DB5C0.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CUserDBs_MgrLobbyHistory_1406DB610.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__CUserDBs_MoveLobbyDelay__1406DB570.cpp" />
    <ClCompile Include="Source\_ECCashDbWorkerBRUEAAPEAXIZ_14022F2F0.cpp" />
    <ClCompile Include="Source\_ECCashDbWorkerIDUEAAPEAXIZ_14022CD00.cpp" />
    <ClCompile Include="Source\_ECCashDbWorkerJPUEAAPEAXIZ_14022D690.cpp" />
    <ClCompile Include="Source\_ECCashDbWorkerKRUEAAPEAXIZ_14022B9A0.cpp" />
    <ClCompile Include="Source\_ECCashDbWorkerPHUEAAPEAXIZ_14022E190.cpp" />
    <ClCompile Include="Source\_ECCashDbWorkerRUUEAAPEAXIZ_14022EAD0.cpp" />
    <ClCompile Include="Source\_ECCashDbWorkerTHUEAAPEAXIZ_1402324F0.cpp" />
    <ClCompile Include="Source\_ECCashDBWorkManagerEEAAPEAXIZ_1402F3560.cpp" />
    <ClCompile Include="Source\_ECItemLootTableUEAAPEAXIZ_1402029A0.cpp" />
    <ClCompile Include="Source\_ECItemUpgradeTableUEAAPEAXIZ_140202D00.cpp" />
    <ClCompile Include="Source\_ECMapDataTableUEAAPEAXIZ_140198820.cpp" />
    <ClCompile Include="Source\_ECOreCuttingTableUEAAPEAXIZ_140202B50.cpp" />
    <ClCompile Include="Source\_ECRecordDataUEAAPEAXIZ_1401891F0.cpp" />
    <ClCompile Include="Source\_ECRFDBItemLogUEAAPEAXIZ_140485F10.cpp" />
    <ClCompile Include="Source\_ECRFNewDatabaseUEAAPEAXIZ_1404893E0.cpp" />
    <ClCompile Include="Source\_ECRFWorldDatabaseUEAAPEAXIZ_1404DA330.cpp" />
    <ClCompile Include="Source\_ECTSingletonVCCashDBWorkManagerMEAAPEAXIZ_1402F3790.cpp" />
    <ClCompile Include="Source\_ECUserDBUEAAPEAXIZ_140203230.cpp" />
    <ClCompile Include="Source\_EDL_GroupParametersImplVEcPrecomputationVEC2NCryp_1405ADE40.cpp" />
    <ClCompile Include="Source\_EDL_GroupParametersImplVEcPrecomputationVEC2NCryp_1405ADE50.cpp" />
    <ClCompile Include="Source\_EDL_GroupParametersImplVEcPrecomputationVECPCrypt_14046A4A0.cpp" />
    <ClCompile Include="Source\_FillPEAPEAVCLogTypeDBTaskPEAV1stdYAXPEAPEAVCLogTy_1402C76E0.cpp" />
    <ClCompile Include="Source\_Fill_nPEAPEAVCLogTypeDBTask_KPEAV1stdYAXPEAPEAVCL_1402C81E0.cpp" />
    <ClCompile Include="Source\_Fill_nPEAPEAVCLogTypeDBTask_KPEAV1Urandom_access__1402C7E60.cpp" />
    <ClCompile Include="Source\_GameDataBaseInitCMainThreadAEAA_NXZ_1401ED4E0.cpp" />
    <ClCompile Include="Source\_GCashDbWorkerUEAAPEAXIZ_14022B860.cpp" />
    <ClCompile Include="Source\_GCCashDbWorkerCNUEAAPEAXIZ_140230C10.cpp" />
    <ClCompile Include="Source\_GCCashDbWorkerESUEAAPEAXIZ_140231B60.cpp" />
    <ClCompile Include="Source\_GCCashDbWorkerGBUEAAPEAXIZ_14022C3A0.cpp" />
    <ClCompile Include="Source\_GCCashDbWorkerNULLUEAAPEAXIZ_1402F3100.cpp" />
    <ClCompile Include="Source\_GCCashDbWorkerTWUEAAPEAXIZ_1402304A0.cpp" />
    <ClCompile Include="Source\_GCCashDbWorkerUSUEAAPEAXIZ_140231660.cpp" />
    <ClCompile Include="Source\_GCDummyPosTableUEAAPEAXIZ_14018AE20.cpp" />
    <ClCompile Include="Source\_GCEventLootTableUEAAPEAXIZ_140202680.cpp" />
    <ClCompile Include="Source\_GCLogTypeDBTaskManagerIEAAPEAXIZ_1402C41E0.cpp" />
    <ClCompile Include="Source\_GCLogTypeDBTaskQEAAPEAXIZ_1402C40F0.cpp" />
    <ClCompile Include="Source\_GCRecordDataUEAAPEAXIZ_14007F6D0.cpp" />
    <ClCompile Include="Source\_GCTotalGuildRankRecordQEAAPEAXIZ_1402CA320.cpp" />
    <ClCompile Include="Source\_GCUnmannedTraderGroupItemInfoTableIEAAPEAXIZ_14036BD20.cpp" />
    <ClCompile Include="Source\_GCUnmannedTraderUserInfoTableIEAAPEAXIZ_140366BE0.cpp" />
    <ClCompile Include="Source\_GCUserDBUEAAPEAXIZ_14011F170.cpp" />
    <ClCompile Include="Source\_GCWeeklyGuildRankRecordQEAAPEAXIZ_1402CF890.cpp" />
    <ClCompile Include="Source\_GDL_GroupParametersImplVEcPrecomputationVECPCrypt_14046A4B0.cpp" />
    <ClCompile Include="Source\_Gtable_objlua_tinkerQEAAPEAXIZ_1404471D0.cpp" />
    <ClCompile Include="Source\_init_databaseCashDbWorkerMEAA_NXZ_1402F0720.cpp" />
    <ClCompile Include="Source\_init_databaseCCashDbWorkerGBMEAA_NXZ_1403192E0.cpp" />
    <ClCompile Include="Source\_init_databaseCCashDbWorkerNULLMEAA_NXZ_1402F30E0.cpp" />
    <ClCompile Include="Source\_init_databaseCCashDbWorkerRUMEAA_NXZ_140320BE0.cpp" />
    <ClCompile Include="Source\_init_loggersCashDbWorkerAEAA_NXZ_1402F0E50.cpp" />
    <ClCompile Include="Source\_Insert_nvectorPEAVCLogTypeDBTaskVallocatorPEAVCLo_1402C6050.cpp" />
    <ClCompile Include="Source\_IsNonwritableInCurrentImagefilt0_1404DE680.cpp" />
    <ClCompile Include="Source\_IsNonwritableInCurrentImage_1404DE5F0.cpp" />
    <ClCompile Include="Source\_Iter_catPEAPEAVCLogTypeDBTaskstdYAAUrandom_access_1402C7E00.cpp" />
    <ClCompile Include="Source\_Iter_randomPEAPEAVCLogTypeDBTaskPEAPEAV1stdYAAUra_1402C7430.cpp" />
    <ClCompile Include="Source\_LoadBindCMapDataAEAA_NPEADZ_1401832E0.cpp" />
    <ClCompile Include="Source\_LoadBspSecCMapDataAEAA_NPEADZ_1401815A0.cpp" />
    <ClCompile Include="Source\_LtdWriterInitLogDB__1_dtor0_14024A9B0.cpp" />
    <ClCompile Include="Source\_lua_tinkertabletable__1_dtor0_0_140446600.cpp" />
    <ClCompile Include="Source\_lua_tinkertabletable__1_dtor0_140446490.cpp" />
    <ClCompile Include="Source\_lua_tinkertabletable__1_dtor0_1_1404466F0.cpp" />
    <ClCompile Include="Source\_Move_backward_optPEAPEAVCLogTypeDBTaskPEAPEAV1Ura_1402C7790.cpp" />
    <ClCompile Include="Source\_Move_catPEAPEAVCLogTypeDBTaskstdYAAU_Undefined_mo_1402C7730.cpp" />
    <ClCompile Include="Source\_Ptr_catPEAPEAVCLogTypeDBTaskPEAPEAV1stdYAAU_Scala_1402C7490.cpp" />
    <ClCompile Include="Source\_Ptr_catV_Vector_iteratorPEAVCLogTypeDBTaskValloca_1402C7F90.cpp" />
    <ClCompile Include="Source\_SetItemInfoLtdWriterAEAAXEPEAU_db_con_STORAGE_LIS_14024B760.cpp" />
    <ClCompile Include="Source\_SetLtdLtdWriterAEAAXPEAVCUserDBPEAU_LTD_N2Z_14024C0A0.cpp" />
    <ClCompile Include="Source\_stdextunchecked_uninitialized_copy_std_Vector_ite_1402C7D10.cpp" />
    <ClCompile Include="Source\_stdextunchecked_uninitialized_copy_std_Vector_ite_1402C7D40.cpp" />
    <ClCompile Include="Source\_stdextunchecked_uninitialized_copy_std_Vector_ite_1402C7D70.cpp" />
    <ClCompile Include="Source\_stdext_Unchecked_uninitialized_move_std_Vector_it_1402C7340.cpp" />
    <ClCompile Include="Source\_stdext_Unchecked_uninitialized_move_std_Vector_it_1402C7370.cpp" />
    <ClCompile Include="Source\_stdext_Unchecked_uninitialized_move_std_Vector_it_1402C73A0.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C4A60.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C4A90.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C4E30.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C4E60.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C50A0.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C5480.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C54B0.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C54F0.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C5520.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C56E0.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C5710.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C5740.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C5B30.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C6560.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C6590.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C65F0.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C6CF0.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C6D20.cpp" />
    <ClCompile Include="Source\_stdvector_CLogTypeDBTask_____ptr64_stdallocator_C_1402C6D50.cpp" />
    <ClCompile Include="Source\_std_Uninit_copy_std_Vector_iterator_CLogTypeDBTas_1402C80C0.cpp" />
    <ClCompile Include="Source\_std_Uninit_copy_std_Vector_iterator_CLogTypeDBTas_1402C80F0.cpp" />
    <ClCompile Include="Source\_std_Uninit_copy_std_Vector_iterator_CLogTypeDBTas_1402C8120.cpp" />
    <ClCompile Include="Source\_std_Uninit_move_std_Vector_iterator_CLogTypeDBTas_1402C7920.cpp" />
    <ClCompile Include="Source\_std_Uninit_move_std_Vector_iterator_CLogTypeDBTas_1402C7950.cpp" />
    <ClCompile Include="Source\_std_Uninit_move_std_Vector_iterator_CLogTypeDBTas_1402C7980.cpp" />
    <ClCompile Include="Source\_std_Vector_iterator_CLogTypeDBTask_____ptr64_stda_1402C5DB0.cpp" />
    <ClCompile Include="Source\_std_Vector_iterator_CLogTypeDBTask_____ptr64_stda_1402C5DE0.cpp" />
    <ClCompile Include="Source\_std_Vector_iterator_CLogTypeDBTask_____ptr64_stda_1402C6910.cpp" />
    <ClCompile Include="Source\_std_Vector_iterator_CLogTypeDBTask_____ptr64_stda_1402C6940.cpp" />
    <ClCompile Include="Source\_TidyvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTyp_1402C5940.cpp" />
    <ClCompile Include="Source\_TimeItemMakeLinkTable__1_dtor0_14030E4E0.cpp" />
    <ClCompile Include="Source\_UfillvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTy_1402C5A20.cpp" />
    <ClCompile Include="Source\_UmovePEAPEAVCLogTypeDBTaskvectorPEAVCLogTypeDBTas_1402C7080.cpp" />
    <ClCompile Include="Source\_UmoveV_Vector_iteratorPEAVCLogTypeDBTaskVallocato_1402C6BF0.cpp" />
    <ClCompile Include="Source\_Unchecked_move_backwardPEAPEAVCLogTypeDBTaskPEAPE_1402C7150.cpp" />
    <ClCompile Include="Source\_Unchecked_uninitialized_movePEAPEAVCLogTypeDBTask_1402C7630.cpp" />
    <ClCompile Include="Source\_Unchecked_uninitialized_moveV_Vector_iteratorPEAV_1402C7210.cpp" />
    <ClCompile Include="Source\_Uninit_copyPEAPEAVCLogTypeDBTaskPEAPEAV1Vallocato_1402C8250.cpp" />
    <ClCompile Include="Source\_Uninit_copyV_Vector_iteratorPEAVCLogTypeDBTaskVal_1402C7FF0.cpp" />
    <ClCompile Include="Source\_Uninit_fill_nPEAPEAVCLogTypeDBTask_KPEAV1Vallocat_1402C75D0.cpp" />
    <ClCompile Include="Source\_Uninit_movePEAPEAVCLogTypeDBTaskPEAPEAV1Vallocato_1402C7AA0.cpp" />
    <ClCompile Include="Source\_Uninit_moveV_Vector_iteratorPEAVCLogTypeDBTaskVal_1402C7840.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_buy_dblogCashDbWorkerIEAAHPEAVTaskZ_1402F0070.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_buy_dblogCCashDbWorkerNULLIEAAHPEAV_1402F1740.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_rollbackCashDbWorkerMEAAHPEAVTaskZ_1402EFD00.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_rollbackCCashDbWorkerJPMEAAHPEAVTas_140320200.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_rollbackCCashDbWorkerNULLMEAAHPEAVT_1402F1720.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_rollbackCCashDbWorkerRUMEAAHPEAVTas_140320A80.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_selectCashDbWorkerMEAAHPEAVTaskZ_1402EF000.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_selectCCashDbWorkerGBIEAAHPEAVTaskH_140319040.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_selectCCashDbWorkerJPMEAAHPEAVTaskZ_1403200A0.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_selectCCashDbWorkerNULLMEAAHPEAVTas_1402F16E0.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_selectCCashDbWorkerRUMEAAHPEAVTaskZ_140320930.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_updateCashDbWorkerMEAAHPEAVTaskZ_1402EF1A0.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_updateCCashDbWorkerGBIEAAHPEAVTaskH_140319200.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_updateCCashDbWorkerJPMEAAHPEAVTaskZ_140320110.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_updateCCashDbWorkerNULLMEAAHPEAVTas_1402F1700.cpp" />
    <ClCompile Include="Source\_wait_tsk_cash_updateCCashDbWorkerRUMEAAHPEAVTaskZ_1403209A0.cpp" />
    <ClCompile Include="Source\_wait_tst_cash_total_selling_selectCashDbWorkerIEA_1402F0340.cpp" />
    <ClCompile Include="Source\_wait_tst_cash_total_selling_selectCCashDbWorkerNU_1402F1760.cpp" />
    <ClCompile Include="Source\_WriteDBLtdWriterAEAAXKZ_14024ABE0.cpp" />
    <ClCompile Include="Source\_XlenvectorPEAVCLogTypeDBTaskVallocatorPEAVCLogTyp_1402C5AA0.cpp" />
  </ItemGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>