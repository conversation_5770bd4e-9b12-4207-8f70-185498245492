#pragma once

#ifndef NETWARD_SYSTEM_H
#define NETWARD_SYSTEM_H

// NetWard System System Header
// Generated from RF Online ZoneServerUD_x64 decompiled source

#include <windows.h>
#include <stdint.h>
#include <memory>
#include <vector>
#include <string>

// Forward declarations
struct CFrameRateVtbl;
struct CIniFileVtbl;
struct CMainThreadVtbl;
struct CTimer;
struct CIniFile;

// Class definitions

// CFrameRateVtbl
struct CFrameRateVtbl
{
 void *( *__vecDelDtor)(CFrameRate *this, unsigned int);
};

// CIniFileVtbl
struct CIniFileVtbl
{
 void *( *__vecDelDtor)(CIniFile *this, unsigned int);
};

// CMainThreadVtbl
struct CMainThreadVtbl
{
 void *( *__vecDelDtor)(CMainThread *this, unsigned int);
};

// CTimer
struct CTimer
{
 float mLoopTime;
 float mTime;
 float mRealTime;
 float mMinFPS;
 float mfLoopHop;
 unsigned int mOldTime;
 unsigned int mLoopCnt;
 unsigned int mLoopHop;
 int m_bUsingQPF;
 int m_bTimerInitialized;
 float mFPS;
 unsigned int mLoopFPSCnt;
 float mFPSTime;
 float m_fTicksPerSec;
 float m_fFramesPerSec;
 float m_fAverageFramesPerSec;
 float m_fSecsPerFrame;
 float m_fLamTime;
 unsigned int64_t m_qwTicks;
 unsigned int64_t m_qwStartTicks;
 unsigned int64_t m_qwTicksPerSec;
 unsigned int64_t m_qwTicksPerFrame;
};

// CIniFile
struct CIniFile
{
 CIniFileVtbl *vfptr;
 char m_strPath[260];
 std::vector<INI_Section *,std::allocator<INI_Section *> > m_SectionList;
};


#endif // NETWARD_SYSTEM_H
