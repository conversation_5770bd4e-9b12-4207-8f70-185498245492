#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Uninit_copy@PEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@YAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderClassInfo@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400131FB

CUnmannedTraderClassInfo ** std::_Uninit_copy<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo **_Dest, std::allocator<CUnmannedTraderClassInfo *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
