#pragma once

#ifndef NETWARD_DATABASE_H
#define NETWARD_DATABASE_H

// NetWard Database System Header
// Generated from RF Online ZoneServerUD_x64 decompiled source

#include <windows.h>
#include <stdint.h>
#include <memory>
#include <vector>
#include <string>

// Forward declarations
struct CRFNewDatabaseVtbl;
struct CUserDBVtbl;

// Class definitions

// CRFNewDatabaseVtbl
struct CRFNewDatabaseVtbl
{
 void *( *__vecDelDtor)(CRFNewDatabase *this, unsigned int);
};

// CUserDBVtbl
struct CUserDBVtbl
{
 void *( *__vecDelDtor)(CUserDB *this, unsigned int);
};


#endif // NETWARD_DATABASE_H
