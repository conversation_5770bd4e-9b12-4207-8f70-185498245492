#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_RaceRank_Step6@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404B6D90

char  CRFWorldDatabase::Update_RaceRank_Step6(CRFWorldDatabase *this, char *szDate)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v5; // [sp+0h] [bp-28h]@1
  CRFWorldDatabase *v6; // [sp+30h] [bp+8h]@1
  char *szDatea; // [sp+38h] [bp+10h]@1

  szDatea = szDate;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v6->vfptr,
    "CRFWorldDatabase::Update_RaceRank_Step6(szDate(%s)) : Start drop #tbl_PvpRankB,C,A Table",
    szDate);
  if ( CRFWorldDatabase::Update_RaceRank_Step_6_1(v6, szDatea) )
  {
    if ( CRFWorldDatabase::Update_RaceRank_Step_6_2(v6, szDatea) )
    {
      if ( CRFWorldDatabase::Update_RaceRank_Step_6_3(v6, szDatea) )
      {
        CRFNewDatabase::FmtLog(
          (CRFNewDatabase *)&v6->vfptr,
          "CRFWorldDatabase::Update_RaceRank_Step6(szDate(%s)) : End drop #tbl_PvpRankB,C,A Table",
          szDatea);
        result = 1;
      }
      else
      {
        CRFWorldDatabase::Update_RaceRank_Step_6_3(v6, szDatea);
        result = 0;
      }
    }
    else
    {
      CRFWorldDatabase::Update_RaceRank_Step_6_2(v6, szDatea);
      result = 0;
    }
  }
  else
  {
    CRFWorldDatabase::Update_RaceRank_Step_6_1(v6, szDatea);
    result = 0;
  }
  return result;
}
