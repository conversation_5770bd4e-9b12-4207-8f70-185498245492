#pragma once

#ifndef NETWARD_WORLD_H
#define NETWARD_WORLD_H

// NetWard World System Header
// Generated from RF Online ZoneServerUD_x64 decompiled source

#include <windows.h>
#include <stdint.h>
#include <memory>
#include <vector>
#include <string>

// Forward declarations
struct CMapOperationVtbl;
struct CDummyPosTableVtbl;
struct CMapOperation;
struct CMapPtrToPtr::CAssoc;
struct CMapExtend;
struct CDummyDrawVtbl;
struct CWorldSchedule;
struct CMapItemStoreList;
struct CMapStringToPtr::CAssoc;
struct CMapStringToString::CPair;
struct CMapStringToOb::CAssoc;

// Class definitions

// CMapOperationVtbl
struct CMapOperationVtbl
{
 void *( *__vecDelDtor)(CMapOperation *this, unsigned int);
};

// CDummyPosTableVtbl
struct CDummyPosTableVtbl
{
 void *( *__vecDelDtor)(CDummyPosTable *this, unsigned int);
};

// CMapOperation
struct CMapOperation
{
 CMapOperationVtbl *vfptr;
 int m_nLoopStartPoint;
 unsigned int m_dwSpeedHackStdTime;
 CMapDataTable m_tblMapData;
 int m_nMapNum;
 int m_nStdMapNum;
 std::vector<std::pair<int,int>,std::allocator<std::pair<int,int> > > m_vecStandardMapCodeTable;
 CMapData *m_Map;
 int m_nRegionNum;
 _region_data m_RegionData[100];
 bool m_bReSpawnMonster;
 CMyTimer m_tmrObjTerm;
 CMyTimer m_tmrSystem;
 CMyTimer m_tmrRecover;
 unsigned int m_dwLastObjLoopTime;
 CMapData *m_SettlementMapData[3][2];
};

// CMapPtrToPtr::CAssoc
struct CMapPtrToPtr::CAssoc
{
 CMapPtrToPtr::CAssoc *pNext;
 void *key;
 void *value;
};

// CMapExtend
struct CMapExtend
{
 CPoint m_ptStartMap;
 CPoint m_ptEndMap;
 CPoint m_ptCenter;
 bool m_bExtendMode;
 CRect m_rcExtend;
 CPoint m_ptStartScreen;
 CPoint m_ptEndScreen;
 CPoint m_ptMoveScreen;
 CSize m_sizeExtend;
 HPEN__ *m_hPen;
 CSurface **m_pSF;
 _map_rate m_Rate;
 int m_bSetArea;
 int m_bMove;
};

// CDummyDrawVtbl
struct CDummyDrawVtbl
{
 void *( *__vecDelDtor)(CDummyDraw *this, unsigned int);
};

// CWorldSchedule
struct CWorldSchedule
{
 bool m_bOper;
 CRecordData m_tblSch;
 int m_nMaxSchNum;
 int m_nSchCursor;
 int m_nCurHour;
 int m_nCurMin;
 int m_nCurMilSec;
 unsigned int m_dwLastCheckTime;
 CMyTimer m_tmrCheck;
};

// CMapItemStoreList
struct CMapItemStoreList
{
 bool m_bUse;
 char m_byType;
 int m_nSerial;
 int m_nItemStoreNum;
 CItemStore *m_ItemStore;
};

// CMapStringToPtr::CAssoc
struct CMapStringToPtr::CAssoc
{
 CMapStringToPtr::CAssoc *pNext;
 unsigned int nHashValue;
 ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char> > > key;
 void *value;
};

// CMapStringToString::CPair
struct CMapStringToString::CPair
{
 ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char> > > key;
 ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char> > > value;
};

// CMapStringToOb::CAssoc
struct CMapStringToOb::CAssoc
{
 CMapStringToOb::CAssoc *pNext;
 unsigned int nHashValue;
 ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char> > > key;
 CObject *value;
};


#endif // NETWARD_WORLD_H
