#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?deallocate@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEAAXPEAPEAVCMoveMapLimitRight@@_K@Z
 * Address: 0x14001327D

void  std::allocator<CMoveMapLimitRight *>::deallocate(std::allocator<CMoveMapLimitRight *> *this, CMoveMapLimitRight **_Ptr, unsigned int64_t __formal)
{
  std::allocator<CMoveMapLimitRight *>::deallocate(this, _Ptr, __formal);
}
