#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVCLogTypeDBTask@@_KPEAV1@V?$allocator@PEAVCLogTypeDBTask@@@std@@@stdext@@YAXPEAPEAVCLogTypeDBTask@@_KAEBQEAV1@AEAV?$allocator@PEAVCLogTypeDBTask@@@std@@@Z
 * Address: 0x140002F40

void  stdext::unchecked_uninitialized_fill_n<CLogTypeDBTask * *,unsigned int64_t,CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(CLogTypeDBTask **_First, unsigned int64_t _Count, CLogTypeDBTask *const *_Val, std::allocator<CLogTypeDBTask *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CLogTypeDBTask * *,unsigned int64_t,CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
