#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_fill_n@PEAPEAVCUnmannedTraderClassInfo@@_KPEAV1@@stdext@@YAXPEAPEAVCUnmannedTraderClassInfo@@_KAEBQEAV1@@Z
 * Address: 0x14000BD02

void  stdext::unchecked_fill_n<CUnmannedTraderClassInfo * *,unsigned int64_t,CUnmannedTraderClassInfo *>(CUnmannedTraderClassInfo **_First, unsigned int64_t _Count, CUnmannedTraderClassInfo *const *_Val)
{
  stdext::unchecked_fill_n<CUnmannedTraderClassInfo * *,unsigned int64_t,CUnmannedTraderClassInfo *>(
    _First,
    _Count,
    _Val);
}
