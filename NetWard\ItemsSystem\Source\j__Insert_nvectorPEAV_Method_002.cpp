#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Insert_n@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAAXV?$_Vector_iterator@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@2@_KAEBQEAVCUnmannedTraderSortType@@@Z
 * Address: 0x1400068A7

void  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Insert_n(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *_Where, unsigned int64_t _Count, CUnmannedTraderSortType *const *_Val)
{
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Insert_n(
    this,
    _Where,
    _Count,
    _Val);
}
