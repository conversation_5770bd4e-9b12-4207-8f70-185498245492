#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_Ufill@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAPEAVCMoveMapLimitRightInfo@@PEAV3@_KAEBV3@@Z
 * Address: 0x1403B1730

CMoveMapLimitRightInfo * std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Ufill(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, CMoveMapLimitRightInfo *_Ptr, unsigned int64_t _Count, CMoveMapLimitRightInfo *_Val)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v8; // [sp+30h] [bp+8h]@1
  CMoveMapLimitRightInfo *_First; // [sp+38h] [bp+10h]@1
  unsigned int64_t _Counta; // [sp+40h] [bp+18h]@1

  _Counta = _Count;
  _First = _Ptr;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  stdext::unchecked_uninitialized_fill_n<CMoveMapLimitRightInfo *,unsigned int64_t,CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(
    _Ptr,
    _Count,
    _Val,
    &v8->_Alval);
  return &_First[_Counta];
}
