#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_db_Load_ItemCombineEx@CMainThread@@AEAAEKPEAU_ITEMCOMBINE_DB_BASE@@@Z
 * Address: 0x140012940

char  CMainThread::_db_Load_ItemCombineEx(CMainThread *this, unsigned int dwSerial, _ITEMCOMBINE_DB_BASE *pCombineEx)
{
  return CMainThread::_db_Load_ItemCombineEx(this, dwSerial, pCombineEx);
}
