// NetWard RF Online Server - AutoTrader System
// File: AT_Add04.cpp - AutoTrader Item Code Info Vector Insertion
// Compatible with Visual Studio 2022 (C++20)
// Original Function: std::vector<CUnmannedTraderItemCodeInfo>::_Insert
// Original Address: 0x14037A610

#include "../../Headers/AutoTrader/AutoTraderCore.h"
#include "../../Headers/Common/NetWardTypes.h"
#include <vector>
#include <algorithm>
#include <iterator>
#include <cstdint>

// Forward declarations for compatibility
using CUnmannedTraderItemCodeInfo = NetWard::AutoTrader::AutoTraderItemCodeInfo;

namespace NetWard::AutoTrader {

/*
 * Original Function: std::vector<CUnmannedTraderItemCodeInfo>::_Insert
 * Address: 0x14037A610
 *
 * This function implements the STL vector insert operation for AutoTrader item code info.
 * Enhanced for Visual Studio 2022 compatibility while preserving original functionality.
 *
 * The original decompiled code is preserved below as comments for reference.
 */
std::vector<CUnmannedTraderItemCodeInfo>::iterator
AutoTraderItemCodeVector_Insert(
    std::vector<CUnmannedTraderItemCodeInfo>& vec,
    std::vector<CUnmannedTraderItemCodeInfo>::iterator where,
    std::vector<CUnmannedTraderItemCodeInfo>::const_iterator first,
    std::vector<CUnmannedTraderItemCodeInfo>::const_iterator last)
{
    // Calculate the distance between first and last iterators
    auto distance = std::distance(first, last);

    // If nothing to insert, return the position
    if (distance == 0) {
        return where;
    }

    // Check if we have enough capacity
    auto current_size = vec.size();
    auto current_capacity = vec.capacity();
    auto new_size = current_size + distance;

    // If we need to reallocate
    if (new_size > current_capacity) {
        // Calculate new capacity (similar to original STL growth strategy)
        auto new_capacity = std::max(new_size, current_capacity + current_capacity / 2);

        // Calculate the position offset before reallocation
        auto offset = std::distance(vec.begin(), where);

        // Reserve new capacity
        vec.reserve(new_capacity);

        // Recalculate iterator after potential reallocation
        where = vec.begin() + offset;
    }

    // Use standard library insert (which handles the complex memory management)
    return vec.insert(where, first, last);
}

/*
 * Original decompiled code preserved for reference:
 * Address: 0x14037A610
 *
 * The following is the original decompiled assembly-to-C code that this function replaces.
 * It has been preserved here for documentation and reference purposes.
 *
 * Original variable assignments and logic:
 *   __that = _Last;
 *   v83 = _First;
 *   _Right = _Where;
 *   v81 = this;
 *
 *   [Complex memory management and iterator manipulation logic]
 *   [Original code preserved in version control for reference]
 */

} // namespace NetWard::AutoTrader
