#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_Tidy@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAXXZ
 * Address: 0x1403A26B0

void  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Tidy(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v4->_Myfirst )
  {
    std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Destroy(v4, v4->_Myfirst, v4->_Mylast);
    std::allocator<CMoveMapLimitRightInfo>::deallocate(
      &v4->_Alval,
      v4->_Myfirst,
      (unsigned int)((char *)v4->_Myend - (char *)v4->_Myfirst) / 40i64);
  }
  v4->_Myfirst = 0i64;
  v4->_Mylast = 0i64;
  v4->_Myend = 0i64;
}
