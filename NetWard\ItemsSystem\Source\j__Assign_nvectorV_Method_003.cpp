#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Assign_n@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@IEAAX_KAEBVCUnmannedTraderUserInfo@@@Z
 * Address: 0x14000FBC8

void  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Assign_n(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, unsigned int64_t _Count, CUnmannedTraderUserInfo *_Val)
{
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Assign_n(this, _Count, _<PERSON>);
}
