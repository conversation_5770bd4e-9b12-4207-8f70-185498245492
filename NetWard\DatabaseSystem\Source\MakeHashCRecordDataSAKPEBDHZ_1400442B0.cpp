#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?MakeHash@CRecordData@@SAKPEBDH@Z
 * Address: 0x1400442B0

int64_t  CRecordData::MakeHash(const char *p, int len)
{
  int *v2; // rdi@1
  signed int64_t i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  unsigned int v6; // [sp+4h] [bp-14h]@15
  int k; // [sp+8h] [bp-10h]@15
  int v8; // [sp+Ch] [bp-Ch]@17
  const char *v9; // [sp+20h] [bp+8h]@1

  v9 = p;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  if ( !`CRecordData::MakeHash'::`2'::bHashTableLoad )
  {
    for ( j = 0; j < 26; ++j )
      `CRecordData::MakeHash'::`2'::byHashTable[j + 97] = j;
    for ( j = 0; j < 26; ++j )
      `CRecordData::MakeHash'::`2'::byHashTable[j + 65] = j;
    for ( j = 0; j < 10; ++j )
      `CRecordData::MakeHash'::`2'::byHashTable[j + 48] = j + 26;
    `CRecordData::MakeHash'::`2'::bHashTableLoad = 1;
  }
  v6 = 0;
  for ( k = 0; k < len; ++k )
  {
    v8 = (unsigned int8_t)`CRecordData::MakeHash'::`2'::byHashTable[*v9++];
    v6 |= v8 << 6 * k;
  }
  return v6;
}
