#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetCompleteInfo@CUnmannedTraderUserInfo@@AEAAXPEAVCLogFile@@@Z
 * Address: 0x140358B50

void  CUnmannedTraderUserInfo::SetCompleteInfo(CUnmannedTraderUserInfo *this, CLogFile *pkLogger)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  CUnmannedTraderRegistItemInfo *v4; // rax@7
  CUnmannedTraderRegistItemInfo *v5; // rax@8
  CUnmannedTraderRegistItemInfo *v6; // rax@10
  unsigned int v7; // eax@10
  CUnmannedTraderRegistItemInfo *v8; // rax@11
  unsigned int v9; // eax@11
  unsigned int v10; // ecx@11
  unsigned int v11; // edx@11
  CUnmannedTraderRegistItemInfo *v12; // rax@13
  CUnmannedTraderRegistItemInfo *v13; // rax@13
  int64_t v14; // [sp+0h] [bp-88h]@1
  _DWORD wItemSerial[2]; // [sp+20h] [bp-68h]@10
  char byItemTableCode[8]; // [sp+28h] [bp-60h]@10
  _DWORD wItemTableIndex[4]; // [sp+30h] [bp-58h]@10
  int v18; // [sp+40h] [bp-48h]@4
  int j; // [sp+44h] [bp-44h]@4
  int64_t v20; // [sp+48h] [bp-40h]@11
  CPlayer *v21; // [sp+50h] [bp-38h]@11
  char *v22; // [sp+58h] [bp-30h]@11
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v23; // [sp+60h] [bp-28h]@11
  CUnmannedTraderRegistItemInfo *rhs; // [sp+68h] [bp-20h]@13
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v25; // [sp+70h] [bp-18h]@13
  CUnmannedTraderUserInfo *v26; // [sp+90h] [bp+8h]@1
  CLogFile *v27; // [sp+98h] [bp+10h]@1

  v27 = pkLogger;
  v26 = this;
  v2 = &v14;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  CUnmannedTraderUserInfo::SetAllItemState(v26, 0, v26->m_byMaxRegistCnt);
  v18 = 0;
  for ( j = 0; j < 20; ++j )
  {
    v4 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
           &v26->m_vecLoadItemInfo,
           j);
    if ( !CUnmannedTraderRegistItemInfo::IsRegist(v4) )
    {
      v5 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v26->m_vecLoadItemInfo,
             j);
      if ( !CUnmannedTraderRegistItemInfo::IsWaitNoitfyClose(v5) )
        continue;
    }
    if ( v26->m_byRegistCnt <= v26->m_byMaxRegistCnt )
    {
      rhs = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
              &v26->m_vecLoadItemInfo,
              j);
      v25 = &v26->m_vecRegistItemInfo;
      v12 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
              &v26->m_vecRegistItemInfo,
              v18);
      CUnmannedTraderRegistItemInfo::operator=(v12, rhs);
      ++v18;
      v13 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
              &v26->m_vecLoadItemInfo,
              j);
      if ( CUnmannedTraderRegistItemInfo::IsRegist(v13) )
        ++v26->m_byRegistCnt;
    }
    else
    {
      v6 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v26->m_vecLoadItemInfo,
             j);
      v7 = CUnmannedTraderRegistItemInfo::GetRegistSerial(v6);
      LOWORD(wItemTableIndex[0]) = -1;
      byItemTableCode[0] = -1;
      LOWORD(wItemSerial[0]) = -1;
      CUnmannedTraderItemState::PushUpdateState(0, v7, 7, v26->m_dwUserSerial, 0xFFFFu, -1, 0xFFFFu);
      if ( v27 )
      {
        v20 = 50856i64 * v26->m_wInx;
        v21 = &g_Player;
        v22 = CPlayerDB::GetCharNameA((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * v26->m_wInx));
        v23 = &v26->m_vecLoadItemInfo;
        v8 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
               &v26->m_vecLoadItemInfo,
               j);
        v9 = CUnmannedTraderRegistItemInfo::GetRegistSerial(v8);
        v10 = v26->m_byMaxRegistCnt;
        v11 = v26->m_byRegistCnt;
        wItemTableIndex[0] = v21[(unsigned int64_t)v20 / 0xC6A8].m_id.dwSerial;
        *(uint64_t*)byItemTableCode = v22;
        wItemSerial[0] = v9;
        CLogFile::Write(
          v27,
          "CUnmannedTraderUserInfo::SetCompleteInfo( ... )\r\n"
          "\t\tm_byRegistCnt(%u) > m_byMaxRegistCnt(%u)\r\n"
          "\t\tCUnmannedTraderItemState::PushUpdateState( CUnmannedTraderItemState::SINGLE\r\n"
          "\t\t, m_vecLoadItemInfo[i].GetRegistSerial()(%u)\r\n"
          "\t\t, CUnmannedTraderItemState::CANCELREGISTFORSERVERINTERNALERROR, dwSerial )\r\n"
          "\t\tName(%s) Serial(%u) !\r\n",
          v11,
          v10);
      }
    }
  }
  if ( v26->m_byRegistCnt > v26->m_byMaxRegistCnt )
    v26->m_byRegistCnt = v26->m_byMaxRegistCnt;
}
