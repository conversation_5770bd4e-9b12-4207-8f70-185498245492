#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?trade@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@HKK0HKKPEADK1KK1PEAM1@Z
 * Address: 0x14000B5DC

void  CMgrAvatorItemHistory::trade(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pOutItem, int nOutItemNum, unsigned int dwOutDalant, unsigned int dwOutGold, _STORAGE_LIST::_db_con *pInItem, int nInItemNum, unsigned int dwInDalant, unsigned int dwInGold, char *pszDstName, unsigned int dwDstSerial, char *pszDstID, unsigned int dwSumDalant, unsigned int dwSumGold, char *pMapCode, float *pfPos, char *pszFileName)
{
  CMgrAvatorItemHistory::trade(
    this,
    n,
    pOutItem,
    nOutItemNum,
    dwOutDalant,
    dwOutGold,
    pInItem,
    nInItemNum,
    dwInDalant,
    dwInGold,
    pszDstName,
    dwDstSerial,
    pszDstID,
    dwSumDalant,
    dwSumGold,
    pMapCode,
    pfPos,
    pszFileName);
}
