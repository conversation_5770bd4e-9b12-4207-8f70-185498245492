#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CLogTypeDBTaskPool::Destroy_::_1_::dtor$0
 * Address: 0x1402C2A30

void  CLogTypeDBTaskPool::Destroy_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>((std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)(a2 + 40));
}
