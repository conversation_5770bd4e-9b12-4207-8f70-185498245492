#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::_Vector_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::operator__::_1_::dtor$1
 * Address: 0x1403B0AC0

void  std::_Vector_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::operator__::_1_::dtor_1(int64_t a1, int64_t a2)
{
  if ( *(uint32_t*)(a2 + 68) & 1 )
  {
    *(uint32_t*)(a2 + 68) &= 0xFFFFFFFE;
    std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(*(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 104));
  }
}
