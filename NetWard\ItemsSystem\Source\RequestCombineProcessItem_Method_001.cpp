#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?RequestCombineProcess@ItemCombineMgr@@QEAAEPEAU_combine_ex_item_request_clzo@@PEAU_combine_ex_item_result_zocl@@@Z
 * Address: 0x1402AB9F0

char  ItemCombineMgr::RequestCombineProcess(ItemCombineMgr *this, _combine_ex_item_request_clzo *pRecv, _combine_ex_item_result_zocl *pSend)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  unsigned int v5; // eax@8
  int v6; // eax@13
  unsigned int v7; // eax@15
  int64_t v9; // [sp+0h] [bp-168h]@1
  int j; // [sp+40h] [bp-128h]@4
  char v11; // [sp+44h] [bp-124h]@4
  char bySelectLinkIndex; // [sp+54h] [bp-114h]@4
  int n; // [sp+64h] [bp-104h]@4
  char v14; // [sp+68h] [bp-100h]@7
  _combine_ex_item_request_clzo::_list *pipMaterials; // [sp+70h] [bp-F8h]@7
  int64_t Dst[6]; // [sp+88h] [bp-E0h]@7
  _ItemCombine_exp_fld *v17; // [sp+B8h] [bp-B0h]@7
  char strLinkItem[8]; // [sp+D0h] [bp-98h]@17
  int v19; // [sp+D8h] [bp-90h]@40
  int v20[18]; // [sp+DCh] [bp-8Ch]@39
  DWORD v21; // [sp+124h] [bp-44h]@8
  int k; // [sp+128h] [bp-40h]@26
  char v23; // [sp+12Ch] [bp-3Ch]@31
  _base_fld *v24; // [sp+130h] [bp-38h]@31
  int l; // [sp+138h] [bp-30h]@35
  char v26; // [sp+13Ch] [bp-2Ch]@56
  int v27; // [sp+148h] [bp-20h]@5
  int X; // [sp+14Ch] [bp-1Ch]@8
  unsigned int64_t v29; // [sp+150h] [bp-18h]@4
  ItemCombineMgr *v30; // [sp+170h] [bp+8h]@1
  _combine_ex_item_result_zocl *v31; // [sp+180h] [bp+18h]@1

  v31 = pSend;
  v30 = this;
  v3 = &v9;
  for ( i = 88i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v29 = (unsigned int64_t)&v9 ^ _security_cookie;
  j = 0;
  v11 = 0;
  bySelectLinkIndex = 0;
  n = pRecv->wManualIndex;
  if ( pRecv->byCombineSlotNum >= 5 )
    v27 = 5;
  else
    v27 = pRecv->byCombineSlotNum;
  v14 = v27;
  pipMaterials = pRecv->iCombineSlot;
  v17 = (_ItemCombine_exp_fld *)CRecordData::GetRecord(&ItemCombineMgr::ms_tbl_ItemCombine, n);
  memset_0(Dst, 0, 0x28ui64);
  if ( _ITEMCOMBINE_DB_BASE::IsCombineData(&v30->m_pMaster->m_Param.m_ItemCombineDB)
    && (v21 = timeGetTime() & 0xFFFFFF,
        v5 = GetCheckTimeFromCombineExCheckKey(v30->m_pMaster->m_Param.m_ItemCombineDB.m_dwCheckKey),
        X = v21 - v5 < 0x5265C00,
        abs_0(X)) )
  {
    v11 = 17;
  }
  else if ( v17 && v17->m_bCombineExist )
  {
    v6 = CPlayerDB::GetRaceSexCode(&v30->m_pMaster->m_Param);
    if ( v17->m_strCivil[v6] == 49 )
    {
      v7 = CPlayerDB::GetDalant(&v30->m_pMaster->m_Param);
      if ( v17->m_dwCommit <= v7 )
      {
        memcpy_0(strLinkItem, v17->m_Material, 0x50ui64);
        for ( j = 0; j < (unsigned int8_t)v14; ++j )
        {
          Dst[j] = (int64_t)_STORAGE_LIST::GetPtrFromSerial(
                              (_STORAGE_LIST *)&v30->m_pMaster->m_Param.m_dbInven.m_nListNum,
                              pipMaterials[j].wItemSerial);
          if ( !Dst[j] )
          {
            CPlayer::SendMsg_AdjustAmountInform(v30->m_pMaster, 0, pipMaterials[j].wItemSerial, 0);
            v11 = 4;
            goto LABEL_55;
          }
          if ( *(uint8_t*)(Dst[j] + 19) )
          {
            v11 = 5;
            goto LABEL_55;
          }
          if ( IsOverLapItem(*(uint8_t*)(Dst[j] + 1))
            && (unsigned int64_t)pipMaterials[j].byAmount > *(uint64_t*)(Dst[j] + 5) )
          {
            CPlayer::SendMsg_AdjustAmountInform(v30->m_pMaster, 0, pipMaterials[j].wItemSerial, *(uint32_t*)(Dst[j] + 5));
            v11 = 6;
            goto LABEL_55;
          }
          for ( k = 0; k < j; ++k )
          {
            if ( pipMaterials[k].wItemSerial == pipMaterials[j].wItemSerial )
            {
              v11 = 6;
              goto LABEL_55;
            }
          }
          v23 = 0;
          v24 = 0i64;
          if ( (signed int)*(uint8_t*)(Dst[j] + 1) < 37 )
            v24 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + *(uint8_t*)(Dst[j] + 1), *(uint16_t*)(Dst[j] + 3));
          if ( !v24 )
          {
            v11 = 1;
            goto LABEL_55;
          }
          for ( l = 0; l < 5; ++l )
          {
            if ( _CheckSameItem(&strLinkItem[16 * l], v24->m_strCode, &bySelectLinkIndex, 1) && v20[4 * l] > 0 )
            {
              v20[4 * l] -= pipMaterials[j].byAmount;
              if ( *(&v19 + 4 * l) != -1 && *(&v19 + 4 * l) == *(uint32_t*)(Dst[j] + 13) )
                *(&v19 + 4 * l) = -1;
              v23 = 1;
              break;
            }
          }
          if ( !v23 )
          {
            v11 = 8;
            goto LABEL_55;
          }
        }
        for ( j = 0; j < 5; ++j )
        {
          if ( v20[4 * j] > 0 )
          {
            v11 = 9;
            break;
          }
          if ( *(&v19 + 4 * j) != -1 )
          {
            v11 = 9;
            break;
          }
        }
      }
      else
      {
        v11 = 3;
      }
    }
    else
    {
      v11 = 2;
    }
  }
  else
  {
    v11 = 1;
  }
LABEL_55:
  v31->byErrCode = v11;
  if ( v11 )
  {
    v31->byDlgType = -1;
    v31->byErrCode = v11;
    return v11;
  }
  v26 = 0;
  v26 = ItemCombineMgr::ConsumeMeterial_And_CalculateNewItems(
          v30,
          (_STORAGE_LIST::_db_con **)Dst,
          v14,
          pipMaterials,
          v31,
          v17,
          bySelectLinkIndex,
          1);
  if ( !v26 )
  {
    v26 = ItemCombineMgr::UpdateDB_CombineResult(v30, v31);
    if ( v26 )
    {
      v31->byDlgType = -1;
      v31->byErrCode = v26;
      return v26;
    }
    return v11;
  }
  if ( v26 == 10 )
  {
    v31->byDlgType = 2;
    v31->byErrCode = 0;
  }
  else
  {
    v31->byDlgType = -1;
    v31->byErrCode = v26;
  }
  return v26;
}
