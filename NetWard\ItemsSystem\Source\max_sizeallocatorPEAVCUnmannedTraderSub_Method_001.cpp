#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?max_size@?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@QEBA_KXZ
 * Address: 0x140380020

signed int64_t  std::allocator<CUnmannedTraderSubClassInfo *>::max_size(std::allocator<CUnmannedTraderSubClassInfo *> *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-18h]@1

  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  return 0x1FFFFFFFFFFFFFFFi64;
}
