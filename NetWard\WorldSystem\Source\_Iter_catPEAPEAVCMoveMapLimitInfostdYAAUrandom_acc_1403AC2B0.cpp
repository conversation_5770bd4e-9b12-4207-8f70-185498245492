#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$_Iter_cat@PEAPEAVCMoveMapLimitInfo@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCMoveMapLimitInfo@@@Z
 * Address: 0x1403AC2B0

CMoveMapLimitInfo **const * std::_Iter_cat<CMoveMapLimitInfo * *>(CMoveMapLimitInfo **const *__formal)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-48h]@1
  CMoveMapLimitInfo **const *v5; // [sp+50h] [bp+8h]@1

  v5 = __formal;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  return v5;
}
