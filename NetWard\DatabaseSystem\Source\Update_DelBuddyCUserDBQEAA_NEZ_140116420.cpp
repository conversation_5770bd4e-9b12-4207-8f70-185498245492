#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_DelBuddy@CUserDB@@QEAA_NE@Z
 * Address: 0x140116420

char  CUserDB::Update_DelBuddy(CUserDB *this, char bySlotIndex)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-28h]@1
  CUserDB *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  _BUDDY_DB_BASE::_LIST::Init((_BUDDY_DB_BASE::_LIST *)&v6->m_AvatorData.dbBuddy + (unsigned int8_t)bySlotIndex);
  v6->m_bDataUpdate = 1;
  return 1;
}
