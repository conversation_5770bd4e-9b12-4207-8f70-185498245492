#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$unchecked_uninitialized_fill_n@PEAVCUnmannedTraderItemCodeInfo@@_KV1@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@stdext@@YAXPEAVCUnmannedTraderItemCodeInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@Z
 * Address: 0x14037A240

void  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderItemCodeInfo *,unsigned int64_t,CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(CUnmannedTraderItemCodeInfo *_First, unsigned int64_t _Count, CUnmannedTraderItemCodeInfo *_Val, std::allocator<CUnmannedTraderItemCodeInfo> *_Al)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v8; // [sp+31h] [bp-17h]@4
  CUnmannedTraderItemCodeInfo *__formal; // [sp+50h] [bp+8h]@1
  unsigned int64_t _Counta; // [sp+58h] [bp+10h]@1
  CUnmannedTraderItemCodeInfo *_Vala; // [sp+60h] [bp+18h]@1
  std::allocator<CUnmannedTraderItemCodeInfo> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  _Vala = _Val;
  _Counta = _Count;
  __formal = _First;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  memset(&v7, 0, sizeof(v7));
  v8 = std::_Ptr_cat<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *>(&__formal, &__formal);
  std::_Uninit_fill_n<CUnmannedTraderItemCodeInfo *,unsigned int64_t,CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    __formal,
    _Counta,
    _Vala,
    _Ala,
    v8,
    v7);
}
