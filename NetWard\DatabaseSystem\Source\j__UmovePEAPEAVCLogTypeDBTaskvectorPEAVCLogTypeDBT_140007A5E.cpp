#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Umove@PEAPEAVCLogTypeDBTask@@@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAAPEAPEAVCLogTypeDBTask@@PEAPEAV2@00@Z
 * Address: 0x140007A5E

CLogTypeDBTask ** std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Umove<CLogTypeDBTask * *>(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, CLogTypeDBTask **_First, CLogTypeDBTask **_Last, CLogTypeDBTask **_Ptr)
{
  return std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Umove<CLogTypeDBTask * *>(
           this,
           _First,
           _Last,
           _Ptr);
}
