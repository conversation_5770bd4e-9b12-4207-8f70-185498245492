#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetCurPos@CPlayerDB@@QEAAXPEAM@Z
 * Address: 0x140079EE0

void  CPlayerDB::SetCurPos(CPlayerDB *this, float *fPos)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-28h]@1
  CPlayerDB *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  memcpy_0(v5->m_dbChar.m_fStartPos, fPos, 0xCui64);
}
