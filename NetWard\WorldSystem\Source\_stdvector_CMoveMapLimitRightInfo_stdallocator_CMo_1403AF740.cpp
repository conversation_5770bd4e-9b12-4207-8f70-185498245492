#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Assign_n_::_1_::dtor$0
 * Address: 0x1403AF740

void  std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Assign_n_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  CMoveMapLimitRightInfo::~CMoveMapLimitRightInfo((CMoveMapLimitRightInfo *)(a2 + 40));
}
