// NetWard RF Online Server - AutoTrader System Core
// File: AutoTraderCore.h - Core definitions for the automated trading system
// Compatible with Visual Studio 2022 (C++20)

#pragma once

#include "../Common/NetWardTypes.h"
#include <vector>
#include <string>
#include <memory>
#include <chrono>
#include <mutex>

namespace NetWard::AutoTrader {

// ============================================================================
// FORWARD DECLARATIONS
// ============================================================================

class AutoTraderManager;
class AutoTraderController;
class AutoTraderSubClassInfo;
class AutoTraderItemCodeInfo;
class AutoTraderUserInfo;
class AutoTraderScheduler;
class AutoTraderClassInfoVector;
class AutoTraderSubClassInfoVector;
class AutoTraderItemCodeInfoVector;
class AutoTraderItemCodeInfo;
class GuildManager;
class LogManager;

// ============================================================================
// AUTOTRADER ENUMS AND CONSTANTS
// ============================================================================

enum class TraderState : uint8 {
    Inactive = 0,
    Active = 1,
    Suspended = 2,
    Maintenance = 3,
    Error = 4
};

enum class TradeType : uint8 {
    Buy = 0,
    Sell = 1,
    Both = 2
};

enum class PriceAdjustment : uint8 {
    None = 0,
    Increase = 1,
    Decrease = 2,
    Market = 3
};

enum class GuildMoneyOutputType : uint8 {
    None = 0,
    Trade = 1,
    Tax = 2,
    AutoTradeTax = 6,
    Penalty = 7
};

// Constants
constexpr uint32 MAX_TRADER_ITEMS = 100;
constexpr uint32 MAX_TRADER_NAME_LENGTH = 32;
constexpr uint32 DEFAULT_TAX_RATE_PERCENT = 5;
constexpr uint64 MIN_TRADE_AMOUNT = 1000;
constexpr uint64 MAX_TRADE_AMOUNT = 999999999999ULL;

// Character constants
constexpr float32 DEFAULT_ANIMUS_ATTACK_GAP = 1.0f;
constexpr float32 MIN_ATTACK_GAP = 0.1f;
constexpr float32 MAX_ATTACK_GAP = 10.0f;

// ============================================================================
// AUTOTRADER ITEM CODE INFO
// ============================================================================

class AutoTraderItemCodeInfo {
public:
    AutoTraderItemCodeInfo() = default;
    explicit AutoTraderItemCodeInfo(const String& itemCode);
    ~AutoTraderItemCodeInfo() = default;

    // Copy and move semantics
    AutoTraderItemCodeInfo(const AutoTraderItemCodeInfo& other) = default;
    AutoTraderItemCodeInfo& operator=(const AutoTraderItemCodeInfo& other) = default;
    AutoTraderItemCodeInfo(AutoTraderItemCodeInfo&& other) noexcept = default;
    AutoTraderItemCodeInfo& operator=(AutoTraderItemCodeInfo&& other) noexcept = default;

    // Accessors
    const String& GetItemCode() const noexcept { return m_itemCode; }
    void SetItemCode(const String& code) { m_itemCode = code; }

    // Operators
    bool operator==(const String& code) const noexcept { return m_itemCode == code; }
    bool operator==(const AutoTraderItemCodeInfo& other) const noexcept { 
        return m_itemCode == other.m_itemCode; 
    }

private:
    String m_itemCode;
};

// ============================================================================
// AUTOTRADER SUBCLASS INFO
// ============================================================================

class AutoTraderSubClassInfo {
public:
    AutoTraderSubClassInfo();
    explicit AutoTraderSubClassInfo(uint32 groupId);
    ~AutoTraderSubClassInfo() = default;

    NETWARD_DISABLE_COPY_AND_MOVE(AutoTraderSubClassInfo);

    // Core functionality
    bool GetGroupID(uint8 tableCode, uint16 itemTableIndex, uint8* subClassId) noexcept;
    bool AddItemCode(const String& itemCode);
    bool RemoveItemCode(const String& itemCode);
    void ClearItemCodes();

    // Accessors
    uint32 GetGroupId() const noexcept { return m_groupId; }
    void SetGroupId(uint32 groupId) noexcept { m_groupId = groupId; }
    
    size_t GetItemCodeCount() const noexcept { return m_codeList.size(); }
    bool HasItemCode(const String& itemCode) const;

    // Iterator support for range-based loops
    auto begin() const { return m_codeList.begin(); }
    auto end() const { return m_codeList.end(); }
    auto begin() { return m_codeList.begin(); }
    auto end() { return m_codeList.end(); }

private:
    uint32 m_groupId;
    Vector<AutoTraderItemCodeInfo> m_codeList;
    mutable std::mutex m_mutex; // Thread safety
};

// ============================================================================
// AUTOTRADER USER INFO
// ============================================================================

struct AutoTraderUserInfo {
    CharacterID characterId;
    String characterName;
    RaceType race;
    uint32 guildId;
    String guildName;
    Money currentMoney;
    Money totalTradeVolume;
    uint32 tradeCount;
    std::chrono::system_clock::time_point lastTradeTime;
    std::chrono::system_clock::time_point registrationTime;
    TraderState state;
    
    AutoTraderUserInfo() 
        : characterId(0)
        , race(RaceType::Invalid)
        , guildId(0)
        , currentMoney(0)
        , totalTradeVolume(0)
        , tradeCount(0)
        , lastTradeTime(std::chrono::system_clock::now())
        , registrationTime(std::chrono::system_clock::now())
        , state(TraderState::Inactive)
    {}
};

// ============================================================================
// AUTOTRADER ITEM ENTRY
// ============================================================================

struct AutoTraderItemEntry {
    ItemSerial itemSerial;
    ItemCode itemCode;
    ItemIndex itemIndex;
    ItemCount quantity;
    TradePrice unitPrice;
    TradePrice totalPrice;
    TradeType tradeType;
    std::chrono::system_clock::time_point listingTime;
    std::chrono::system_clock::time_point expirationTime;
    bool isActive;
    
    AutoTraderItemEntry()
        : itemSerial(0)
        , itemIndex(0)
        , quantity(0)
        , unitPrice(0)
        , totalPrice(0)
        , tradeType(TradeType::Sell)
        , listingTime(std::chrono::system_clock::now())
        , expirationTime(std::chrono::system_clock::now())
        , isActive(false)
    {}
};

// ============================================================================
// AUTOTRADER SCHEDULE INFO
// ============================================================================

struct AutoTraderScheduleInfo {
    uint32 scheduleId;
    CharacterID ownerId;
    std::chrono::system_clock::time_point startTime;
    std::chrono::system_clock::time_point endTime;
    TradeType allowedTradeType;
    TaxRate taxRate;
    bool isActive;
    String description;
    
    AutoTraderScheduleInfo()
        : scheduleId(0)
        , ownerId(0)
        , startTime(std::chrono::system_clock::now())
        , endTime(std::chrono::system_clock::now())
        , allowedTradeType(TradeType::Both)
        , taxRate(DEFAULT_TAX_RATE_PERCENT / 100.0f)
        , isActive(false)
    {}
};

// ============================================================================
// TYPE ALIASES
// ============================================================================

using AutoTraderItemList = Vector<AutoTraderItemEntry>;
using AutoTraderUserList = Vector<AutoTraderUserInfo>;
using AutoTraderScheduleList = Vector<AutoTraderScheduleInfo>;

using AutoTraderItemPtr = SharedPtr<AutoTraderItemEntry>;
using AutoTraderUserPtr = SharedPtr<AutoTraderUserInfo>;
using AutoTraderSchedulePtr = SharedPtr<AutoTraderScheduleInfo>;

// ============================================================================
// AUTOTRADER CONTROLLER CLASS
// ============================================================================

class AutoTraderController {
public:
    AutoTraderController() = default;
    ~AutoTraderController() = default;

    NETWARD_DISABLE_COPY_AND_MOVE(AutoTraderController);

    // Guild Dalant management
    void AddGuildDalant(const uint8_t* tradeData) noexcept;

    // Accessors
    void SetOwnerGuild(SharedPtr<class Guild> guild) noexcept { m_ownerGuild = guild; }
    SharedPtr<class Guild> GetOwnerGuild() const noexcept { return m_ownerGuild; }

private:
    SharedPtr<class Guild> m_ownerGuild;
};

// ============================================================================
// AUTOTRADER VECTOR UTILITY CLASSES
// ============================================================================

class AutoTraderClassInfoVector {
public:
    // Template function for range insertion
    template<typename InputIterator>
    static void InsertRange(
        Vector<AutoTraderClassInfo*>& targetVector,
        typename Vector<AutoTraderClassInfo*>::iterator insertPosition,
        InputIterator rangeBegin,
        InputIterator rangeEnd) noexcept;

    // Convenience functions
    static void InsertVector(
        Vector<AutoTraderClassInfo*>& targetVector,
        typename Vector<AutoTraderClassInfo*>::iterator insertPosition,
        const Vector<AutoTraderClassInfo*>& sourceVector) noexcept;

    static typename Vector<AutoTraderClassInfo*>::iterator AddClassInfo(
        Vector<AutoTraderClassInfo*>& targetVector,
        AutoTraderClassInfo* classInfo) noexcept;
};

class AutoTraderSubClassInfoVector {
public:
    // Template function for range insertion
    template<typename InputIterator>
    static void InsertRange(
        Vector<AutoTraderSubClassInfo*>& targetVector,
        typename Vector<AutoTraderSubClassInfo*>::iterator insertPosition,
        InputIterator rangeBegin,
        InputIterator rangeEnd) noexcept;

    // Convenience functions
    static void InsertVector(
        Vector<AutoTraderSubClassInfo*>& targetVector,
        typename Vector<AutoTraderSubClassInfo*>::iterator insertPosition,
        const Vector<AutoTraderSubClassInfo*>& sourceVector) noexcept;

    static typename Vector<AutoTraderSubClassInfo*>::iterator AddSubClassInfo(
        Vector<AutoTraderSubClassInfo*>& targetVector,
        AutoTraderSubClassInfo* subClassInfo) noexcept;

    static bool RemoveSubClassInfo(
        Vector<AutoTraderSubClassInfo*>& targetVector,
        AutoTraderSubClassInfo* subClassInfo) noexcept;
};

class AutoTraderItemCodeInfoVector {
public:
    // Template function for range insertion
    template<typename InputIterator>
    static void InsertRange(
        Vector<AutoTraderItemCodeInfo>& targetVector,
        typename Vector<AutoTraderItemCodeInfo>::iterator insertPosition,
        InputIterator rangeBegin,
        InputIterator rangeEnd) noexcept;

    // Convenience functions
    static typename Vector<AutoTraderItemCodeInfo>::iterator AddItemCodeInfo(
        Vector<AutoTraderItemCodeInfo>& targetVector,
        const AutoTraderItemCodeInfo& itemCodeInfo) noexcept;
};

// ============================================================================
// VECTOR INSERTION FUNCTIONS (Original decompiled functions)
// ============================================================================

// Forward declaration for compatibility with original decompiled code
using CUnmannedTraderItemCodeInfo = AutoTraderItemCodeInfo;

// Original function: std::vector<CUnmannedTraderItemCodeInfo>::_Insert
// Address: 0x14037A610
// Enhanced original decompiled function preserving all RF Online server logic
void AutoTraderItemCodeVector_Insert(
    std::vector<CUnmannedTraderItemCodeInfo>* this_ptr,
    std::vector<CUnmannedTraderItemCodeInfo>::iterator _Where,
    std::vector<CUnmannedTraderItemCodeInfo>::const_iterator _First,
    std::vector<CUnmannedTraderItemCodeInfo>::const_iterator _Last);

// Forward declaration for compatibility with original decompiled code
using CUnmannedTraderClassInfo = AutoTraderClassInfo;

// Original function: std::vector<CUnmannedTraderClassInfo*>::_Insert_n
// Address: 0x140371940
// Enhanced original decompiled function preserving all RF Online server logic
void AutoTraderClassInfoVector_Insert_n(
    std::vector<CUnmannedTraderClassInfo*>* this_ptr,
    std::vector<CUnmannedTraderClassInfo*>::iterator _Where,
    uint64_t _Count,
    CUnmannedTraderClassInfo* const* _Val);

// ============================================================================
// AUTOTRADER CLASS INFO (Forward declaration for vector operations)
// ============================================================================

class AutoTraderClassInfo {
public:
    AutoTraderClassInfo() = default;
    virtual ~AutoTraderClassInfo() = default;

    // Basic interface
    virtual uint32 GetClassId() const noexcept = 0;
    virtual String GetClassName() const noexcept = 0;
    virtual bool IsValid() const noexcept = 0;
};

} // namespace NetWard::AutoTrader
