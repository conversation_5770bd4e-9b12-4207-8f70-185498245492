#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLRemoveDriver
 * Address: 0x1404DAB7C

int  SQLRemoveDriver(const char *lpszDriver, int fRemoveDSN, unsigned int *lpdwUsageCount)
{
  const char *v3; // rsi@1
  unsigned int *v4; // rbx@1
  int v5; // edi@1
  int64_t ( *v6)(); // rax@1
  int result; // eax@2

  v3 = lpszDriver;
  v4 = lpdwUsageCount;
  v5 = fRemoveDSN;
  v6 = ODBC___GetSetupProc("SQLRemoveDriver");
  if ( v6 )
    result = ((int ( *)(const char *, _QWORD, unsigned int *))v6)(v3, (unsigned int)v5, v4);
  else
    result = 0;
  return result;
}
