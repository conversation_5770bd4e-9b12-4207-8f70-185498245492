#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SaveAll@BossSchedule_Map@@QEAA_NXZ
 * Address: 0x140419FB0

char  BossSchedule_Map::SaveAll(BossSchedule_Map *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  BossSchedule *pSchedule; // [sp+28h] [bp-10h]@7
  BossSchedule_Map *pMapSchedule; // [sp+40h] [bp+8h]@1

  pMapSchedule = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( pMapSchedule->m_ScheduleList )
  {
    for ( j = 0; j < pMapSchedule->m_nCount; ++j )
    {
      pSchedule = pMapSchedule->m_ScheduleList[j];
      if ( !pSchedule )
        return 0;
      if ( !pMapSchedule->m_pSystem )
        return 0;
      CBossMonsterScheduleSystem::Savechedule(pMapSchedule->m_pSystem, pMapSchedule, pSchedule);
    }
  }
  return 1;
}
