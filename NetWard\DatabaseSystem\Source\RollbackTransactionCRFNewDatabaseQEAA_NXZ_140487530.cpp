#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?RollbackTransaction@CRFNewDatabase@@QEAA_NXZ
 * Address: 0x140487530

bool  CRFNewDatabase::RollbackTransaction(CRFNewDatabase *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-38h]@1
  int16_t v5; // [sp+20h] [bp-18h]@4
  CRFNewDatabase *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v5 = SQLEndTran_0(2, v6->m_hDbc, 1);
  return !v5 || v5 == 1;
}
