#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ResetAggro@CMonsterAggroMgr@@QEAAXXZ
 * Address: 0x14015E900

void  CMonsterAggroMgr::ResetAggro(CMonsterAggroMgr *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-28h]@1
  CMonsterAggroMgr *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4->m_dwShortRankLastTime = v4->m_dwShortRankTimer + GetLoopTime();
}
