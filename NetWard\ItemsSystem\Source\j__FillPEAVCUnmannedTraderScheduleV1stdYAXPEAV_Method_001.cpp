#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Fill@PEAVCUnmannedTraderSchedule@@V1@@std@@YAXPEAVCUnmannedTraderSchedule@@0AEBV1@@Z
 * Address: 0x140004EBC

void  std::_Fill<CUnmannedTraderSchedule *,CUnmannedTraderSchedule>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Val)
{
  std::_Fill<CUnmannedTraderSchedule *,CUnmannedTraderSchedule>(_First, _Last, _Val);
}
