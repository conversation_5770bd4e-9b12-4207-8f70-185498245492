#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?lenditem_del_from_inven@CMgrAvatorItemHistory@@QEAAXEG_KPEAD@Z
 * Address: 0x140240BD0

void  CMgrAvatorItemHistory::lenditem_del_from_inven(CMgrAvatorItemHistory *this, char byTblCode, unsigned int16_t wItemIndex, unsigned int64_t lnUID, char *pFN)
{
  int64_t *v5; // rdi@1
  signed int64_t i; // rcx@1
  char *v7; // rax@5
  int64_t v8; // [sp+0h] [bp-88h]@1
  unsigned int64_t v9; // [sp+20h] [bp-68h]@5
  unsigned int v10; // [sp+28h] [bp-60h]@5
  int v11; // [sp+30h] [bp-58h]@5
  _base_fld *v12; // [sp+40h] [bp-48h]@4
  __time32_t Time; // [sp+54h] [bp-34h]@4
  struct tm *v14; // [sp+68h] [bp-20h]@4
  int v15; // [sp+70h] [bp-18h]@5
  unsigned int v16; // [sp+74h] [bp-14h]@5
  int nTableCode; // [sp+78h] [bp-10h]@5
  CMgrAvatorItemHistory *v18; // [sp+90h] [bp+8h]@1
  char v19; // [sp+98h] [bp+10h]@1
  unsigned int16_t v20; // [sp+A0h] [bp+18h]@1
  unsigned int64_t v21; // [sp+A8h] [bp+20h]@1

  v21 = lnUID;
  v20 = wItemIndex;
  v19 = byTblCode;
  v18 = this;
  v5 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t*)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  sData[0] = 0;
  v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned int8_t)byTblCode, wItemIndex);
  _time32(&Time);
  v14 = _localtime32(&Time);
  if ( v12 )
  {
    v15 = GetItemTableCode(v12->m_strCode);
    v16 = GetKorLocalTime();
    nTableCode = (unsigned int8_t)v19;
    v7 = GetItemKorName((unsigned int8_t)v19, v20);
    v11 = v14->tm_sec;
    v10 = v16;
    v9 = v21;
    sprintf(sData, "[DEL_LENDITEM] : %s(%s) [UID:%I64u][T:%u/%d]\r\n", v12->m_strCode, v7);
  }
  else
  {
    v10 = GetKorLocalTime();
    v9 = v21;
    sprintf(sData, "[DEL_LENDITEM] : Tbl:%d Idx:%d [UID:%I64u][%u]\r\n", (unsigned int8_t)v19, v20);
  }
  CMgrAvatorItemHistory::WriteFile(v18, pFN, sData);
}
