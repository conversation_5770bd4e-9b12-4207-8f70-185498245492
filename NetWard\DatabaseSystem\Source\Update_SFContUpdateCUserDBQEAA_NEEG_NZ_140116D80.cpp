#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_SFContUpdate@CUserDB@@QEAA_NEEG_N@Z
 * Address: 0x140116D80

char  CUserDB::Update_SFContUpdate(CUserDB *this, char byContCode, char bySlotIndex, unsigned int16_t wTime, bool bUpdate)
{
  int64_t *v5; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v8; // [sp+0h] [bp-38h]@1
  int v9; // [sp+20h] [bp-18h]@9
  CUserDB *v10; // [sp+40h] [bp+8h]@1
  char v11; // [sp+48h] [bp+10h]@1
  char v12; // [sp+50h] [bp+18h]@1
  unsigned int16_t v13; // [sp+58h] [bp+20h]@1

  v13 = wTime;
  v12 = bySlotIndex;
  v11 = byContCode;
  v10 = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  if ( (signed int)(unsigned int8_t)byContCode < 2 )
  {
    if ( (signed int)(unsigned int8_t)bySlotIndex < 8 )
    {
      if ( _SFCONT_DB_BASE::_LIST::IsFilled((_SFCONT_DB_BASE::_LIST *)&v10->m_AvatorData.dbSfcont + 8 * (unsigned int8_t)byContCode + (unsigned int8_t)bySlotIndex) )
      {
        _SFCONT_DB_BASE::_LIST::SetLeftTime(
          (_SFCONT_DB_BASE::_LIST *)&v10->m_AvatorData.dbSfcont + 8 * (unsigned int8_t)v11 + (unsigned int8_t)v12,
          v13);
        result = 1;
      }
      else
      {
        v9 = (unsigned int8_t)v12;
        CLogFile::Write(
          &stru_1799C8E78,
          "%s : Update_SFContUpdate(NOTHING) : code : %d, slot : %d",
          v10->m_aszAvatorName,
          (unsigned int8_t)v11);
        result = 0;
      }
    }
    else
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_SFContUpdate(SlotIndex OVER) : slot : %d",
        v10->m_aszAvatorName,
        (unsigned int8_t)bySlotIndex);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_SFContUpdate(byContCode OVER) : code : %d",
      v10->m_aszAvatorName,
      (unsigned int8_t)byContCode);
    result = 0;
  }
  return result;
}
