#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?RegistItem@CUnmannedTraderUserInfo@@AEAAEEPEAU_a_trade_reg_item_request_clzo@@EEEEKK@Z
 * Address: 0x14035A520

char  CUnmannedTraderUserInfo::RegistItem(CUnmannedTraderUserInfo *this, char byType, _a_trade_reg_item_request_clzo *pRequest, char byTempSlotIndex, char byDivision, char byClass, char bySubClass, unsigned int dwListIndex, unsigned int dwTax)
{
  int64_t *v9; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int v12; // eax@7
  CMoneySupplyMgr *v13; // rax@13
  _INVENKEY *v14; // rax@14
  int64_t v15; // [sp+0h] [bp-138h]@1
  bool bUpdate[8]; // [sp+20h] [bp-118h]@7
  bool bSend[4]; // [sp+28h] [bp-110h]@7
  CPlayer *v18; // [sp+30h] [bp-108h]@4
  void *Src; // [sp+38h] [bp-100h]@4
  int16_t v20; // [sp+40h] [bp-F8h]@6
  unsigned int16_t v21; // [sp+44h] [bp-F4h]@6
  char v22; // [sp+48h] [bp-F0h]@6
  _STORAGE_LIST::_db_con Dst; // [sp+58h] [bp-E0h]@7
  int v24; // [sp+94h] [bp-A4h]@9
  char pQryData[2]; // [sp+B0h] [bp-88h]@14
  int16_t v26; // [sp+B2h] [bp-86h]@14
  unsigned int v27; // [sp+B4h] [bp-84h]@14
  unsigned int16_t v28; // [sp+B8h] [bp-80h]@14
  char v29; // [sp+BAh] [bp-7Eh]@14
  unsigned int v30; // [sp+BCh] [bp-7Ch]@14
  char v31; // [sp+C0h] [bp-78h]@14
  char v32; // [sp+C1h] [bp-77h]@14
  char v33; // [sp+C9h] [bp-6Fh]@14
  char v34; // [sp+CAh] [bp-6Eh]@14
  char v35; // [sp+CBh] [bp-6Dh]@14
  unsigned int v36; // [sp+CCh] [bp-6Ch]@14
  unsigned int v37; // [sp+D0h] [bp-68h]@14
  char v38; // [sp+D4h] [bp-64h]@17
  int v39; // [sp+D8h] [bp-60h]@14
  int64_t v40; // [sp+E0h] [bp-58h]@14
  int v41; // [sp+E8h] [bp-50h]@14
  char v42; // [sp+ECh] [bp-4Ch]@15
  char v43; // [sp+EDh] [bp-4Bh]@17
  char v44; // [sp+EEh] [bp-4Ah]@17
  char v45; // [sp+EFh] [bp-49h]@17
  char v46; // [sp+F0h] [bp-48h]@17
  unsigned int v47; // [sp+100h] [bp-38h]@17
  _base_fld *v48; // [sp+118h] [bp-20h]@15
  _INVENKEY v49; // [sp+120h] [bp-18h]@14
  int nLv; // [sp+124h] [bp-14h]@13
  int v51; // [sp+128h] [bp-10h]@13
  CUnmannedTraderUserInfo *v52; // [sp+140h] [bp+8h]@1
  char v53; // [sp+148h] [bp+10h]@1
  _a_trade_reg_item_request_clzo *v54; // [sp+150h] [bp+18h]@1
  char v55; // [sp+158h] [bp+20h]@1

  v55 = byTempSlotIndex;
  v54 = pRequest;
  v53 = byType;
  v52 = this;
  v9 = &v15;
  for ( i = 76i64; i; --i )
  {
    *(uint32_t*)v9 = -858993460;
    v9 = (int64_t *)((char *)v9 + 4);
  }
  v18 = &g_Player + v52->m_wInx;
  Src = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v18->m_Param.m_dbInven.m_nListNum, pRequest->wItemSerial);
  if ( Src )
  {
    v20 = *(uint16_t*)((char *)Src + 17);
    v21 = -1;
    v22 = *((uint8_t*)Src + 49);
    if ( (unsigned int8_t)v55 == 255
      || (v12 = -v54->byAmount,
          bSend[0] = 0,
          bUpdate[0] = 0,
          CPlayer::Emb_AlterDurPoint(v18, 0, *((uint8_t*)Src + 49), v12, 0, 0),
          _STORAGE_LIST::_db_con::_db_con(&Dst),
          memcpy_0(&Dst, Src, 0x32ui64),
          Dst.m_dwDur = v54->byAmount,
          Dst.m_wSerial = CPlayerDB::GetNewItemSerial(&v18->m_Param),
          v21 = Dst.m_wSerial,
          (Src = CPlayer::Emb_AddStorage(v18, 0, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 0, 0)) != 0i64) )
    {
      _STORAGE_LIST::_storage_con::lock((_STORAGE_LIST::_storage_con *)Src, 1);
      CPlayer::SubDalant(v18, dwTax);
      v24 = CPlayerDB::GetLevel(&v18->m_Param);
      if ( v24 == 30 || v24 == 40 || v24 == 50 || v24 == 60 )
      {
        nLv = CPlayerDB::GetLevel(&v18->m_Param);
        v51 = CPlayerDB::GetRaceCode(&v18->m_Param);
        v13 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateFeeMoneyData(v13, v51, nLv, dwTax);
      }
      *(uint16_t*)pQryData = v52->m_wInx;
      v26 = v20;
      v27 = CPlayerDB::GetDalant(&v18->m_Param);
      v28 = v21;
      v29 = v54->byAmount;
      v30 = dwListIndex;
      v31 = v22;
      v32 = *((uint8_t*)Src + 49);
      v33 = v53;
      v34 = CUnmannedTraderEnvironmentValue::Unmanned_Trader_Default_Sell_Turm;
      v35 = CPlayerDB::GetRaceCode(&v18->m_Param);
      v37 = v54->dwPrice;
      v36 = v18->m_dwObjSerial;
      _INVENKEY::_INVENKEY(&v49, 0, *((uint8_t*)Src + 1), *(uint16_t*)((char *)Src + 3));
      v39 = _INVENKEY::CovDBKey(v14);
      v40 = *(uint64_t*)((char *)Src + 5);
      v41 = *(uint32_t*)((char *)Src + 13);
      if ( *((uint8_t*)Src + 1) == 15 )
      {
        v48 = CRecordData::GetRecord(
                &stru_1799C8410 + 1,
                *((uint32_t*)CPlayer::s_pnLinkForceItemToEffect + *(uint16_t*)((char *)Src + 3)));
        v42 = v48[4].m_strCode[60];
      }
      else
      {
        v42 = GetItemEquipLevel(*((uint8_t*)Src + 1), *(uint16_t*)((char *)Src + 3));
      }
      v38 = *((uint8_t*)Src + 49);
      v43 = GetItemGrade(*((uint8_t*)Src + 1), *(uint16_t*)((char *)Src + 3));
      v44 = byDivision;
      v45 = byClass;
      v46 = bySubClass;
      v47 = dwTax;
      CUnmannedTraderRequestLimiter::SetRequest(&v52->m_kRequestState, 0);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 60, pQryData, 88);
      result = 0;
    }
    else
    {
      CPlayer::Emb_AlterDurPoint(v18, 0, ::v31, v54->byAmount, 0, 0);
      result = 8;
    }
  }
  else
  {
    result = 8;
  }
  return result;
}
