#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?erase@?$vector@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@QEAA?AV?$_Vector_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@2@V32@0@Z
 * Address: 0x14000148D

std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > * std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::erase(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this, std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *result, std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_First, std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Last)
{
  return std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::erase(
           this,
           result,
           _First,
           _Last);
}
