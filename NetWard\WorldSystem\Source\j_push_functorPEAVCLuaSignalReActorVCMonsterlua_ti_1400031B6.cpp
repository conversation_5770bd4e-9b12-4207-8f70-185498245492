#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$push_functor@PEAVCLuaSignalReActor@@VCMonster@@@lua_tinker@@YAXPEAUlua_State@@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@Z
 * Address: 0x1400031B6

void  lua_tinker::push_functor<CLuaSignalReActor *,CMonster>(struct lua_State *L, CLuaSignalReActor *( *func)(CMonster *this))
{
  lua_tinker::push_functor<CLuaSignalReActor *,CMonster>(L, func);
}
