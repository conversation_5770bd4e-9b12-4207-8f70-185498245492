#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?trans_ground_item@CMgrAvatorItemHistory@@QEAAXPEAU_db_con@_STORAGE_LIST@@PEADK11@Z
 * Address: 0x14000A85D

void  CMgrAvatorItemHistory::trans_ground_item(CMgrAvatorItemHistory *this, _STORAGE_LIST::_db_con *pItem, char *pszTakerName, unsigned int dwTakerSerial, char *pszTakerID, char *pszFileName)
{
  CMgrAvatorItemHistory::trans_ground_item(this, pItem, pszTakerName, dwTakerSerial, pszTakerID, pszFileName);
}
