#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$fill@PEAVCUnmannedTraderItemCodeInfo@@V1@@std@@YAXPEAVCUnmannedTraderItemCodeInfo@@0AEBV1@@Z
 * Address: 0x14000506A

void  std::fill<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, CUnmannedTraderItemCodeInfo *_Val)
{
  std::fill<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo>(_First, _Last, _Val);
}
