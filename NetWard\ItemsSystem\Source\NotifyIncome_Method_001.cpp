#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?NotifyIncome@CUnmannedTraderTradeInfo@@AEAAXXZ
 * Address: 0x1403924B0

void  CUnmannedTraderTradeInfo::NotifyIncome(CUnmannedTraderTradeInfo *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  CandidateMgr *v3; // rax@6
  CNationSettingManager *v4; // rax@8
  int v5; // eax@14
  int64_t v6; // [sp+0h] [bp-F8h]@1
  char pbyType; // [sp+34h] [bp-C4h]@4
  char v8; // [sp+35h] [bp-C3h]@4
  char szMsg[8]; // [sp+60h] [bp-98h]@4
  char Dst; // [sp+68h] [bp-90h]@4
  char v11; // [sp+69h] [bp-8Fh]@4
  char v12; // [sp+6Ah] [bp-8Eh]@4
  char v13; // [sp+6Bh] [bp-8Dh]@4
  char v14; // [sp+6Ch] [bp-8Ch]@4
  char v15; // [sp+6Dh] [bp-8Bh]@4
  char v16; // [sp+79h] [bp-7Fh]@4
  _candidate_info *v17; // [sp+B8h] [bp-40h]@4
  int j; // [sp+C0h] [bp-38h]@4
  int k; // [sp+C4h] [bp-34h]@10
  CPlayer *v20; // [sp+C8h] [bp-30h]@13
  char *Src; // [sp+D8h] [bp-20h]@7
  unsigned int64_t v22; // [sp+E0h] [bp-18h]@4
  CUnmannedTraderTradeInfo *v23; // [sp+100h] [bp+8h]@1

  v23 = this;
  v1 = &v6;
  for ( i = 60i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v22 = (unsigned int64_t)&v6 ^ _security_cookie;
  pbyType = 13;
  v8 = 102;
  *(uint64_t*)szMsg = v23->m_ui64TotalOldIncome;
  Dst = 0;
  v11 = v23->m_ui64TotalOldIncome;
  v12 = 0;
  v13 = v23->m_ui64TotalOldIncome;
  v14 = 0;
  memset(&v15, 0, 0xCui64);
  memset(&v16, 0, 0x32ui64);
  v17 = 0i64;
  for ( j = 0; j < 3; ++j )
  {
    v3 = CandidateMgr::Instance();
    v17 = CandidateMgr::GetPatriarchGroup(v3, j, 0);
    if ( v17 )
    {
      Src = v17->wszName;
    }
    else
    {
      v4 = CTSingleton<CNationSettingManager>::Instance();
      Src = CNationSettingManager::GetNoneString(v4);
    }
    strcpy_s(&Dst + 25 * j, 0x11ui64, Src);
  }
  for ( k = 0; k < 2532; ++k )
  {
    v20 = &g_Player + k;
    if ( v20->m_bLive )
    {
      v5 = CPlayerDB::GetRaceCode(&v20->m_Param);
      CNetProcess::LoadSendMsg(unk_1414F2088, v20->m_ObjID.m_wIndex, &pbyType, &szMsg[25 * v5], 0x19u);
    }
  }
}
