#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_GuildRank_Step1@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404B7670

char  CRFWorldDatabase::Update_GuildRank_Step1(CRFWorldDatabase *this, char *szDate)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@6
  int64_t v5; // [sp+0h] [bp-478h]@1
  void *SQLStmt; // [sp+20h] [bp-458h]@11
  int16_t v7; // [sp+30h] [bp-448h]@7
  char DstBuf; // [sp+50h] [bp-428h]@4
  char v9; // [sp+51h] [bp-427h]@4
  unsigned int64_t v10; // [sp+460h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+480h] [bp+8h]@1
  char *v12; // [sp+488h] [bp+10h]@1

  v12 = szDate;
  v11 = this;
  v2 = &v5;
  for ( i = 284i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v10 = (unsigned int64_t)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v9, 0, 0x3FFui64);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank_Step1(char* szDate(%s)) : Update_GuildRank Start!",
    szDate);
  sprintf_s(&DstBuf, 0x400ui64, "tbl_GuildRank%s", v12);
  if ( CRFNewDatabase::TableExist((CRFNewDatabase *)&v11->vfptr, &DstBuf) )
  {
    sprintf_s(&DstBuf, 0x400ui64, "drop table [dbo].[tbl_GuildRank%s]", v12);
    if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &DstBuf, 1) )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_GuildRank_Step1(szDate(%s)) : Drop tbl_GuildRank%s Table Fail!",
        v12,
        v12);
      return 0;
    }
  }
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 0);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank_Step1(char* szDate(%s)) : Start Create #tbl_GuildRankTemp Table",
    v12);
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "select top %d g.GuildSerial as serial, sum(b.lv) + sum(g.pvppoint)/10000 as GuildPower into #tbl_GuildRankTemp from "
    "tbl_general as g join tbl_base as b on g.serial = b.serial group by g.GuildSerial having g.guildserial >= 0",
    500i64);
  v7 = SQLExecDirect_0(v11->m_hStmtUpdate, &DstBuf, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_GuildRank_Step1(char* szDate(%s)) : Create #tbl_GuildRankTemp Table Fail SQL_ERROR!",
        v12);
      SQLStmt = v11->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &DstBuf, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank_Step1(char* szDate(%s)) : Create #tbl_GuildRankTemp Table Fail NO_DATA!",
      v12);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank_Step1(char* szDate(%s)) : End Create #tbl_GuildRankTemp Table",
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank_Step1(char* szDate(%s)) : Start Create tbl_GuildRank%s Table",
    v12,
    v12);
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "select IDENTITY(int, 1, 1) AS Rank, serial, GuildPower, -1 as Rate, 1 as Grade into [dbo].[tbl_GuildRank%s] from #tb"
    "l_GuildRankTemp order by GuildPower desc",
    v12);
  v7 = SQLExecDirect_0(v11->m_hStmtUpdate, &DstBuf, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_GuildRank_Step1(char* szDate(%s)) : Create tbl_GuildRank%s Table Fail SQL_ERROR!",
        v12,
        v12);
      SQLStmt = v11->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &DstBuf, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank_Step1(char* szDate(%s)) : Create tbl_GuildRank%s Table Fail NO_DATA!",
      v12,
      v12);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank_Step1(char* szDate(%s)) : End Create tbl_GuildRank%s Table",
    v12,
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_GuildRank_Step1(char* szDate(%s)) : Start drop table #tbl_GuildRankTemp",
    v12);
  sprintf(&DstBuf, "drop table #tbl_GuildRankTemp");
  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &DstBuf, 1) )
  {
    CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v11->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank_Step1(char* szDate(%s)) : End drop table #tbl_GuildRankTemp",
      v12);
    result = 1;
  }
  else
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_GuildRank_Step1(char* szDate(%s)) : drop table #tbl_GuildRankTemp Fail!",
      v12);
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
    result = 0;
  }
  return result;
}
