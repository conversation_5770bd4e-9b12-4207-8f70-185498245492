#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ShortRankDelay@CMonsterAggroMgr@@QEAAXK@Z
 * Address: 0x14015E0C0

void  CMonsterAggroMgr::ShortRankDelay(CMonsterAggroMgr *this, unsigned int dwDelayTime)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-38h]@1
  CMonsterAggroMgr *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v5->m_dwShortRankLastTime = dwDelayTime + GetLoopTime();
}
