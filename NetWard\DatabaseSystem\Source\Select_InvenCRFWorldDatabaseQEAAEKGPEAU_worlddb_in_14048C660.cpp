#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Select_Inven@CRFWorldDatabase@@QEAAEKGPEAU_worlddb_inven_info@@@Z
 * Address: 0x14048C660

char  CRFWorldDatabase::Select_Inven(CRFWorldDatabase *this, unsigned int dwCharacterSerial, unsigned int16_t wBagCount, _worlddb_inven_info *pInvenData)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@8
  int64_t v7; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@24
  SQLLEN v10; // [sp+38h] [bp-150h]@24
  int16_t v11; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int j; // [sp+164h] [bp-24h]@4
  int v14; // [sp+168h] [bp-20h]@4
  char v15; // [sp+16Ch] [bp-1Ch]@16
  unsigned int64_t v16; // [sp+178h] [bp-10h]@4
  CRFWorldDatabase *v17; // [sp+190h] [bp+8h]@1
  _worlddb_inven_info *v18; // [sp+1A8h] [bp+20h]@1

  v18 = pInvenData;
  v17 = this;
  v4 = &v7;
  for ( i = 96i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v16 = (unsigned int64_t)&v7 ^ _security_cookie;
  j = 0;
  v14 = 0;
  sprintf(&Dest, "{ CALL pSelect_inven_20061115_%d( %d ) }", wBagCount, dwCharacterSerial);
  if ( v17->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, &Dest);
  if ( v17->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v17->vfptr) )
  {
    v11 = SQLExecDirectA_0(v17->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v17->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v11 = SQLFetch_0(v17->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v15 = 0;
        if ( v11 == 100 )
        {
          v15 = 2;
        }
        else
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
          v15 = 1;
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        result = v15;
      }
      else
      {
        for ( j = 0; j < 100; ++j )
        {
          ++v14;
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v17->m_hStmtSelect, v14, 4, (char *)v18 + 32 * j, 0i64, &v10);
          if ( v11 )
          {
            if ( v11 != 1 )
              break;
          }
          ++v14;
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v17->m_hStmtSelect, v14++, -25, &v18->invenKey[j].dwD, 0i64, &v10);
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v17->m_hStmtSelect, v14++, 4, &v18->invenKey[j].dwU, 0i64, &v10);
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v17->m_hStmtSelect, v14++, -25, &v18->invenKey[j].lnUID, 0i64, &v10);
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v17->m_hStmtSelect, v14, 4, &v18->invenKey[j].dwT, 0i64, &v10);
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        if ( v17->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v17->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v17->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
