#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?allocate@?$allocator@PEAVCMoveMapLimitInfo@@@std@@QEAAPEAPEAVCMoveMapLimitInfo@@_K@Z
 * Address: 0x14000984F

CMoveMapLimitInfo ** std::allocator<CMoveMapLimitInfo *>::allocate(std::allocator<CMoveMapLimitInfo *> *this, unsigned int64_t _Count)
{
  return std::allocator<CMoveMapLimitInfo *>::allocate(this, _Count);
}
