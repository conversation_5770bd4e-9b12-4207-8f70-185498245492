#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Iter_cat@PEAPEAVCMoveMapLimitInfo@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCMoveMapLimitInfo@@@Z
 * Address: 0x14001348A

std::random_access_iterator_tag  std::_Iter_cat<CMoveMapLimitInfo * *>(CMoveMapLimitInfo **const *__formal)
{
  return std::_Iter_cat<CMoveMapLimitInfo * *>(__formal);
}
