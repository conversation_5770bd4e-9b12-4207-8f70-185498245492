#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?_GetMonsterContTime@@YAGEE@Z
 * Address: 0x140147560

int64_t  _GetMonsterContTime(char byEffectCode, char byLv)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-28h]@1
  char v6; // [sp+30h] [bp+8h]@1

  v6 = byEffectCode;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  return (unsigned int8_t)MonsterSetInfoData::GetLevelContSFTime(&g_MonsterSetInfoData, v6, byLv);
}
