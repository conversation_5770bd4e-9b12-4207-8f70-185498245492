#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_Punishment@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404BEE00

bool  CRFWorldDatabase::Update_Punishment(CRFWorldDatabase *this, char *szData)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  bool result; // al@6
  int v5; // eax@8
  int v6; // eax@9
  int64_t v7; // [sp+0h] [bp-198h]@1
  int v8; // [sp+20h] [bp-178h]@8
  int v9; // [sp+28h] [bp-170h]@8
  unsigned int pdwCnt; // [sp+34h] [bp-164h]@4
  char Dest; // [sp+60h] [bp-138h]@8
  char *v12; // [sp+168h] [bp-30h]@4
  int v13; // [sp+170h] [bp-28h]@4
  unsigned int64_t v14; // [sp+180h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+1A0h] [bp+8h]@1

  v15 = this;
  v2 = &v7;
  for ( i = 100i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v14 = (unsigned int64_t)&v7 ^ _security_cookie;
  pdwCnt = 0;
  v12 = szData;
  v13 = CRFWorldDatabase::Select_PunishmentCount(v15, *szData, *((uint32_t*)szData + 7), &pdwCnt);
  if ( v13 != 2 && v13 != 1 )
  {
    if ( pdwCnt )
    {
      v6 = (unsigned int8_t)*v12;
      v9 = *((uint32_t*)v12 + 1);
      v8 = v6;
      sprintf(&Dest, "{ CALL pUpdate_Punishment( %d, %d, %d, %d ) }", *((uint32_t*)v12 + 7), *((uint32_t*)v12 + 8));
    }
    else
    {
      v5 = (unsigned int8_t)*v12;
      v9 = *((uint32_t*)v12 + 1);
      v8 = v5;
      sprintf(&Dest, "{ CALL pInsert_Punishment( %d, %d, %d, %d ) }", *((uint32_t*)v12 + 7), *((uint32_t*)v12 + 8));
    }
    result = CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v15->vfptr, &Dest, 1);
  }
  else
  {
    result = 0;
  }
  return result;
}
