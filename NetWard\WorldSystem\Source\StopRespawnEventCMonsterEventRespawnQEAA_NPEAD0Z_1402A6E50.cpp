#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?StopRespawnEvent@CMonsterEventRespawn@@QEAA_NPEAD0@Z
 * Address: 0x1402A6E50

char  CMonsterEventRespawn::StopRespawnEvent(CMonsterEventRespawn *this, char *pszEventCode, char *pwszErrCode)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@12
  int64_t v6; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  bool *v8; // [sp+28h] [bp-20h]@7
  int k; // [sp+30h] [bp-18h]@14
  bool *v10; // [sp+38h] [bp-10h]@17
  CMonsterEventRespawn *v11; // [sp+50h] [bp+8h]@1
  const char *Str2; // [sp+58h] [bp+10h]@1
  char *Dest; // [sp+60h] [bp+18h]@1

  Dest = pwszErrCode;
  Str2 = pszEventCode;
  v11 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  for ( j = 0; ; ++j )
  {
    if ( j >= v11->m_nLoadEventRespawn )
      return 0;
    v8 = &v11->m_EventRespawn[j].bLoad;
    if ( *v8 && !strcmp_0((const char *)v8 + 4392, Str2) )
      break;
  }
  if ( v8[4456] )
  {
    if ( v8[288] )
    {
      for ( k = 0; k < *((uint32_t*)v8 + 1117); ++k )
      {
        v10 = &v8[24 * k + 4472];
        if ( *(uint64_t*)v10
          && *(uint8_t*)(*(uint64_t*)v10 + 24i64)
          && *(uint32_t*)(*(uint64_t*)v10 + 20i64) == *((uint32_t*)v10 + 2) )
        {
          CMonster::Destroy(*(CMonster **)v10, 1, 0i64);
        }
      }
    }
    v8[4456] = 0;
    result = 1;
  }
  else
  {
    if ( Dest )
      sprintf(Dest, "now stoped");
    result = 0;
  }
  return result;
}
