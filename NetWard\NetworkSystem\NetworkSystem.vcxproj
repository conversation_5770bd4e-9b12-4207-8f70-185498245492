<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>

  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{G7H8I9J0-K1L2-3456-0123-************}</ProjectGuid>
    <RootNamespace>NetworkSystem</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>../Common;$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;NETWARD_SERVER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>4996;4244;4267;4101;4102;4700;4005;4013;4047;4024;4133</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemGroup>
    <ClCompile Include="Source\0AlgorithmImplVDL_SignerBaseUEC2NPointCryptoPPCryp_140569C60.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVDL_SignerBaseUECPPointCryptoPPCrypt_140569B30.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVDL_SignerBaseVIntegerCryptoPPCrypto_140635DD0.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVDL_SignerBaseVIntegerCryptoPPCrypto_140637170.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVDL_VerifierBaseUEC2NPointCryptoPPCr_140569E20.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVDL_VerifierBaseUECPPointCryptoPPCry_140569C40.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVDL_VerifierBaseVIntegerCryptoPPCryp_140636800.cpp" />
    <ClCompile Include="Source\0AlgorithmImplVDL_VerifierBaseVIntegerCryptoPPCryp_140637190.cpp" />
    <ClCompile Include="Source\0allocatorURECV_DATAstdQEAAAEBV01Z_14031FB40.cpp" />
    <ClCompile Include="Source\0allocatorURECV_DATAstdQEAAXZ_14031FAC0.cpp" />
    <ClCompile Include="Source\0auto_ptrVPK_MessageAccumulatorBaseCryptoPPstdQEAA_14056C640.cpp" />
    <ClCompile Include="Source\0auto_ptrVPK_MessageAccumulatorCryptoPPstdQEAAPEAV_1405F78F0.cpp" />
    <ClCompile Include="Source\0CNetSocketQEAAXZ_14047DB60.cpp" />
    <ClCompile Include="Source\0dequeURECV_DATAVallocatorURECV_DATAstdstdQEAAXZ_14031F980.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_SignerBaseUEC2NPointCryptoPP_140568290.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_SignerBaseUECPPointCryptoPPC_1405681D0.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140568070.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_1406327E0.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140635B90.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140635C50.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_VerifierBaseUEC2NPointCrypto_1405682F0.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_VerifierBaseUECPPointCryptoP_140568230.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_140568160.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_140635BF0.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_140635CB0.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_SignerBaseUEC2NPointCryptoPPCryp_140567FF0.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_SignerBaseUECPPointCryptoPPCrypt_140567F30.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140567D10.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140632670.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140635990.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140635A50.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_VerifierBaseUEC2NPointCryptoPPCr_140568050.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_VerifierBaseUECPPointCryptoPPCry_140567F90.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_140567DD0.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_1406359F0.cpp" />
    <ClCompile Include="Source\0DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_140635AB0.cpp" />
    <ClCompile Include="Source\0DL_SignatureMessageEncodingMethod_DSACryptoPPQEAA_14058F7E0.cpp" />
    <ClCompile Include="Source\0DL_SignatureMessageEncodingMethod_NRCryptoPPQEAAX_14063C950.cpp" />
    <ClCompile Include="Source\0DL_SignerImplUDL_SignatureSchemeOptionsUDSACrypto_140561ED0.cpp" />
    <ClCompile Include="Source\0DL_SignerImplUDL_SignatureSchemeOptionsUDSACrypto_140632550.cpp" />
    <ClCompile Include="Source\0DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140564370.cpp" />
    <ClCompile Include="Source\0DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140566010.cpp" />
    <ClCompile Include="Source\0DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140634060.cpp" />
    <ClCompile Include="Source\0DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140634530.cpp" />
    <ClCompile Include="Source\0DL_VerifierImplUDL_SignatureSchemeOptionsUDSACryp_1405632F0.cpp" />
    <ClCompile Include="Source\0DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_1405650B0.cpp" />
    <ClCompile Include="Source\0DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_140566D50.cpp" />
    <ClCompile Include="Source\0DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_140634300.cpp" />
    <ClCompile Include="Source\0DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_1406347D0.cpp" />
    <ClCompile Include="Source\0member_ptrVPK_MessageAccumulatorCryptoPPCryptoPPQ_140600220.cpp" />
    <ClCompile Include="Source\0MessageQueueCryptoPPQEAAIZ_1406542A0.cpp" />
    <ClCompile Include="Source\0pairV_Deque_iteratorUMessageRangeMeterFilterCrypt_140603B50.cpp" />
    <ClCompile Include="Source\0PK_DeterministicSignatureMessageEncodingMethodCry_14058FDF0.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_1405617E0.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140561BF0.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140561D50.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140632460.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140633F90.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140633FD0.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140561880.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140561C90.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140561DF0.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140633FB0.cpp" />
    <ClCompile Include="Source\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140633FF0.cpp" />
    <ClCompile Include="Source\0PK_MessageAccumulatorBaseCryptoPPQEAAXZ_140562F60.cpp" />
    <ClCompile Include="Source\0PK_MessageAccumulatorCryptoPPQEAAXZ_140563160.cpp" />
    <ClCompile Include="Source\0PK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPQEA_140562D60.cpp" />
    <ClCompile Include="Source\0PK_SignatureMessageEncodingMethodCryptoPPQEAAXZ_14058FF00.cpp" />
    <ClCompile Include="Source\0RECV_DATAQEAAXZ_14031A490.cpp" />
    <ClCompile Include="Source\0simple_ptrVDL_SignatureMessageEncodingMethod_DSAC_14058EB60.cpp" />
    <ClCompile Include="Source\0simple_ptrVDL_SignatureMessageEncodingMethod_NRCr_14063C280.cpp" />
    <ClCompile Include="Source\0SingletonVDL_SignatureMessageEncodingMethod_DSACr_14056C710.cpp" />
    <ClCompile Include="Source\0SingletonVDL_SignatureMessageEncodingMethod_NRCry_1406395A0.cpp" />
    <ClCompile Include="Source\0URECV_DATAallocatorPEAURECV_DATAstdQEAAAEBValloca_14031FB60.cpp" />
    <ClCompile Include="Source\0VRandomNumberGeneratorCryptoPPHPK_FinalTemplateVD_140639D90.cpp" />
    <ClCompile Include="Source\0_announ_message_receipt_udpQEAAXZ_140094FE0.cpp" />
    <ClCompile Include="Source\0_apex_send_ipQEAAXZ_140410C00.cpp" />
    <ClCompile Include="Source\0_chat_message_receipt_udpQEAAXZ_140094E90.cpp" />
    <ClCompile Include="Source\0_chat_steal_message_gm_zoclQEAAXZ_1403F8D30.cpp" />
    <ClCompile Include="Source\0_Deque_const_iteratorUMessageRangeMeterFilterCryp_140600CE0.cpp" />
    <ClCompile Include="Source\0_Deque_const_iteratorUMessageRangeMeterFilterCryp_140601830.cpp" />
    <ClCompile Include="Source\0_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031DA90.cpp" />
    <ClCompile Include="Source\0_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031EA30.cpp" />
    <ClCompile Include="Source\0_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140600A50.cpp" />
    <ClCompile Include="Source\0_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406017D0.cpp" />
    <ClCompile Include="Source\0_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031D540.cpp" />
    <ClCompile Include="Source\0_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E370.cpp" />
    <ClCompile Include="Source\0_Deque_mapURECV_DATAVallocatorURECV_DATAstdstdIEA_14031FAD0.cpp" />
    <ClCompile Include="Source\0_Deque_valURECV_DATAVallocatorURECV_DATAstdstdIEA_14031FA30.cpp" />
    <ClCompile Include="Source\0_messageQEAAXZ_140438750.cpp" />
    <ClCompile Include="Source\0_qry_case_post_sendQEAAXZ_1403282A0.cpp" />
    <ClCompile Include="Source\0_qry_case_update_data_for_post_sendQEAAXZ_1400CA7E0.cpp" />
    <ClCompile Include="Source\0_RanitUMessageRangeMeterFilterCryptoPP_JPEBU123AE_140600DB0.cpp" />
    <ClCompile Include="Source\0_RanitURECV_DATA_JPEBU1AEBU1stdQEAAXZ_14031DBF0.cpp" />
    <ClCompile Include="Source\0_socketQEAAXZ_14047F8B0.cpp" />
    <ClCompile Include="Source\0_talik_recvr_listQEAAXZ_1403F6ED0.cpp" />
    <ClCompile Include="Source\0__list_qry_case_post_sendQEAAXZ_1403285E0.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVDL_SignerBaseUEC2NPointCryptoPPCryp_14055F6A0.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVDL_SignerBaseUECPPointCryptoPPCrypt_14055F630.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVDL_SignerBaseVIntegerCryptoPPCrypto_1406329E0.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVDL_SignerBaseVIntegerCryptoPPCrypto_140632BE0.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVDL_VerifierBaseUEC2NPointCryptoPPCr_14055F6F0.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVDL_VerifierBaseUECPPointCryptoPPCry_14055F680.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVDL_VerifierBaseVIntegerCryptoPPCryp_140632A30.cpp" />
    <ClCompile Include="Source\1AlgorithmImplVDL_VerifierBaseVIntegerCryptoPPCryp_140632C00.cpp" />
    <ClCompile Include="Source\1auto_ptrVPK_MessageAccumulatorBaseCryptoPPstdQEAA_14056C660.cpp" />
    <ClCompile Include="Source\1auto_ptrVPK_MessageAccumulatorCryptoPPstdQEAAXZ_1405F7910.cpp" />
    <ClCompile Include="Source\1CNetSocketUEAAXZ_14047DCC0.cpp" />
    <ClCompile Include="Source\1dequeURECV_DATAVallocatorURECV_DATAstdstdQEAAXZ_14031FB80.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_SignerBaseUEC2NPointCryptoPP_14055ED20.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_SignerBaseUECPPointCryptoPPC_14055EC60.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_14055E750.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140632720.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140632860.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_VerifierBaseUEC2NPointCrypto_14055ED80.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_VerifierBaseUECPPointCryptoP_14055ECC0.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_14055E7B0.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_140632780.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_1406328C0.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_SignerBaseUEC2NPointCryptoPPCryp_14055E5B0.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_SignerBaseUECPPointCryptoPPCrypt_14055E570.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_14055E1B0.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140632630.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_1406326A0.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_VerifierBaseUEC2NPointCryptoPPCr_14055E5D0.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_VerifierBaseUECPPointCryptoPPCry_14055E590.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_14055E1D0.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_140632650.cpp" />
    <ClCompile Include="Source\1DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_1406326C0.cpp" />
    <ClCompile Include="Source\1DL_SignatureMessageEncodingMethod_DSACryptoPPUEAA_14058FEB0.cpp" />
    <ClCompile Include="Source\1DL_SignatureMessageEncodingMethod_NRCryptoPPUEAAX_14063D350.cpp" />
    <ClCompile Include="Source\1DL_SignerImplUDL_SignatureSchemeOptionsUDSACrypto_14055D500.cpp" />
    <ClCompile Include="Source\1DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__14055DF60.cpp" />
    <ClCompile Include="Source\1DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__14055DFA0.cpp" />
    <ClCompile Include="Source\1DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140632510.cpp" />
    <ClCompile Include="Source\1DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__1406325B0.cpp" />
    <ClCompile Include="Source\1DL_VerifierImplUDL_SignatureSchemeOptionsUDSACryp_14055D520.cpp" />
    <ClCompile Include="Source\1DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_14055DF80.cpp" />
    <ClCompile Include="Source\1DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_14055DFC0.cpp" />
    <ClCompile Include="Source\1DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_140632530.cpp" />
    <ClCompile Include="Source\1DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_1406325D0.cpp" />
    <ClCompile Include="Source\1member_ptrVPK_MessageAccumulatorCryptoPPCryptoPPQ_140600E80.cpp" />
    <ClCompile Include="Source\1MessageQueueCryptoPPUEAAXZ_1406552F0.cpp" />
    <ClCompile Include="Source\1pairV_Deque_iteratorUMessageRangeMeterFilterCrypt_1406023A0.cpp" />
    <ClCompile Include="Source\1PK_DeterministicSignatureMessageEncodingMethodCry_14058FED0.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_14055C200.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_14055D060.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_14055D0A0.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140632420.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140632490.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_14055C220.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_14055D080.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_14055D0C0.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140632440.cpp" />
    <ClCompile Include="Source\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_1406324B0.cpp" />
    <ClCompile Include="Source\1PK_MessageAccumulatorBaseCryptoPPUEAAXZ_1405631A0.cpp" />
    <ClCompile Include="Source\1PK_MessageAccumulatorCryptoPPUEAAXZ_140563180.cpp" />
    <ClCompile Include="Source\1PK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUEA_1405680D0.cpp" />
    <ClCompile Include="Source\1PK_SignatureMessageEncodingMethodCryptoPPUEAAXZ_14058FEF0.cpp" />
    <ClCompile Include="Source\1simple_ptrVDL_SignatureMessageEncodingMethod_DSAC_14058EB80.cpp" />
    <ClCompile Include="Source\1simple_ptrVDL_SignatureMessageEncodingMethod_NRCr_14063C2A0.cpp" />
    <ClCompile Include="Source\1_Deque_const_iteratorUMessageRangeMeterFilterCryp_1405FE880.cpp" />
    <ClCompile Include="Source\1_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031D4C0.cpp" />
    <ClCompile Include="Source\1_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1405FE860.cpp" />
    <ClCompile Include="Source\1_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031D480.cpp" />
    <ClCompile Include="Source\1_messageQEAAXZ_140438780.cpp" />
    <ClCompile Include="Source\1_RanitUMessageRangeMeterFilterCryptoPP_JPEBU123AE_1405FE8A0.cpp" />
    <ClCompile Include="Source\1_RanitURECV_DATA_JPEBU1AEBU1stdQEAAXZ_14031D500.cpp" />
    <ClCompile Include="Source\1_socketQEAAXZ_14047F900.cpp" />
    <ClCompile Include="Source\4_Deque_const_iteratorUMessageRangeMeterFilterCryp_140602430.cpp" />
    <ClCompile Include="Source\4_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140602400.cpp" />
    <ClCompile Include="Source\4_RanitUMessageRangeMeterFilterCryptoPP_JPEBU123AE_140602480.cpp" />
    <ClCompile Include="Source\8_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603A70.cpp" />
    <ClCompile Include="Source\8_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031E640.cpp" />
    <ClCompile Include="Source\9_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603AC0.cpp" />
    <ClCompile Include="Source\9_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031DB10.cpp" />
    <ClCompile Include="Source\Accept_ClientCNetSocketQEAA_NKZ_14047E750.cpp" />
    <ClCompile Include="Source\Accept_ServerCNetSocketQEAAKXZ_14047E3B0.cpp" />
    <ClCompile Include="Source\AccessHashPK_MessageAccumulatorImplVSHA1CryptoPPCr_14056C620.cpp" />
    <ClCompile Include="Source\AccessKeyDL_ObjectImplBaseVDL_SignerBaseUEC2NPoint_14056C2D0.cpp" />
    <ClCompile Include="Source\AccessKeyDL_ObjectImplBaseVDL_SignerBaseUECPPointC_14056C000.cpp" />
    <ClCompile Include="Source\AccessKeyDL_ObjectImplBaseVDL_SignerBaseVIntegerCr_14056BE90.cpp" />
    <ClCompile Include="Source\AccessKeyDL_ObjectImplBaseVDL_VerifierBaseUEC2NPoi_14056C390.cpp" />
    <ClCompile Include="Source\AccessKeyDL_ObjectImplBaseVDL_VerifierBaseUECPPoin_14056C080.cpp" />
    <ClCompile Include="Source\AccessKeyDL_ObjectImplBaseVDL_VerifierBaseVInteger_14056BF50.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseU_1405645A0.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseU_140566240.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseV_140562140.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseV_140634290.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseV_140634760.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_1405634B0.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_140565270.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_140566F10.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_1406344C0.cpp" />
    <ClCompile Include="Source\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_140634990.cpp" />
    <ClCompile Include="Source\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseUEC_140564560.cpp" />
    <ClCompile Include="Source\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseUEC_140566200.cpp" />
    <ClCompile Include="Source\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseVIn_140562100.cpp" />
    <ClCompile Include="Source\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseVIn_140634250.cpp" />
    <ClCompile Include="Source\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseVIn_140634720.cpp" />
    <ClCompile Include="Source\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseUE_140565230.cpp" />
    <ClCompile Include="Source\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseUE_140566ED0.cpp" />
    <ClCompile Include="Source\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseVI_140563470.cpp" />
    <ClCompile Include="Source\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseVI_140634480.cpp" />
    <ClCompile Include="Source\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseVI_140634950.cpp" />
    <ClCompile Include="Source\AddPassablePacketCMainThreadAEAAXXZ_1401F95E0.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVDL_SignerBaseUEC2NPoint_140566270.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVDL_SignerBaseUECPPointC_1405645D0.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVDL_SignerBaseVIntegerCr_1406342C0.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVDL_SignerBaseVIntegerCr_140634790.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVDL_VerifierBaseUEC2NPoi_140566F40.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVDL_VerifierBaseUECPPoin_1405652A0.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVDL_VerifierBaseVInteger_1406344F0.cpp" />
    <ClCompile Include="Source\AlgorithmNameAlgorithmImplVDL_VerifierBaseVInteger_1406349C0.cpp" />
    <ClCompile Include="Source\allocateallocatorPEAUMessageRangeMeterFilterCrypto_140600CA0.cpp" />
    <ClCompile Include="Source\allocateallocatorPEAURECV_DATAstdQEAAPEAPEAURECV_D_14031AD80.cpp" />
    <ClCompile Include="Source\allocateallocatorUMessageRangeMeterFilterCryptoPPs_1406009E0.cpp" />
    <ClCompile Include="Source\allocateallocatorURECV_DATAstdQEAAPEAURECV_DATA_KZ_14031AB40.cpp" />
    <ClCompile Include="Source\AllowNonrecoverablePartPK_SignatureMessageEncoding_140562C80.cpp" />
    <ClCompile Include="Source\AnsyncConnectCompleteCNetworkEXEEAAXKKHZ_1401DB640.cpp" />
    <ClCompile Include="Source\AnsyncConnectCompleteCNetWorkingUEAAXKKHZ_140482210.cpp" />
    <ClCompile Include="Source\AnyMessagesBufferedTransformationCryptoPPUEBA_NXZ_1405F5210.cpp" />
    <ClCompile Include="Source\AnyRetrievableMessageQueueCryptoPPUEBA_NXZ_140655190.cpp" />
    <ClCompile Include="Source\begindequeUMessageRangeMeterFilterCryptoPPVallocat_1405FFE30.cpp" />
    <ClCompile Include="Source\begindequeURECV_DATAVallocatorURECV_DATAstdstdQEAA_14031D5F0.cpp" />
    <ClCompile Include="Source\CashDBInfoRecvResultCNetworkEXAEAA_NHPEADZ_1401D1DD0.cpp" />
    <ClCompile Include="Source\Cauto_ptrVPK_MessageAccumulatorCryptoPPstdQEBAPEAV_1405F79C0.cpp" />
    <ClCompile Include="Source\ChannelMessageEndBufferedTransformationCryptoPPQEA_1405F7AA0.cpp" />
    <ClCompile Include="Source\ChannelMessageSeriesEndBufferedTransformationCrypt_1405F4BA0.cpp" />
    <ClCompile Include="Source\ChannelMessageSeriesEndEqualityComparisonFilterCry_140654D40.cpp" />
    <ClCompile Include="Source\ChannelMessageSeriesEndInputRejectingVBufferedTran_140453F20.cpp" />
    <ClCompile Include="Source\ChannelMessageSeriesEndInputRejectingVFilterCrypto_14044E9E0.cpp" />
    <ClCompile Include="Source\ChannelMessageSeriesEndOutputProxyCryptoPPUEAA_NAE_1405FF170.cpp" />
    <ClCompile Include="Source\ChatAllRecvYesOrNoCNetworkEXAEAA_NHPEADZ_1401C5A00.cpp" />
    <ClCompile Include="Source\ChatMapRecvYesOrNoCNetworkEXAEAA_NHPEADZ_1401C5810.cpp" />
    <ClCompile Include="Source\CheckSendNewMissionMsgCDarkHoleChannelQEAAXXZ_140267640.cpp" />
    <ClCompile Include="Source\cleardequeUMessageRangeMeterFilterCryptoPPVallocat_1406001C0.cpp" />
    <ClCompile Include="Source\CloseAllCNetSocketAEAAXXZ_14047F060.cpp" />
    <ClCompile Include="Source\CloseSocketCNetProcessQEAAXK_NZ_14047A140.cpp" />
    <ClCompile Include="Source\CloseSocketCNetSocketQEAA_NKZ_14047EC40.cpp" />
    <ClCompile Include="Source\CloseSocketCNetWorkingQEAAXKK_NZ_140481AE0.cpp" />
    <ClCompile Include="Source\closesocket_0_1404DBA40.cpp" />
    <ClCompile Include="Source\Cmember_ptrVPK_MessageAccumulatorCryptoPPCryptoPPQ_140600250.cpp" />
    <ClCompile Include="Source\CombineMessageAndShiftRegisterCFB_DecryptionTempla_140586920.cpp" />
    <ClCompile Include="Source\CombineMessageAndShiftRegisterCFB_DecryptionTempla_1405870D0.cpp" />
    <ClCompile Include="Source\CombineMessageAndShiftRegisterCFB_EncryptionTempla_1405868D0.cpp" />
    <ClCompile Include="Source\CombineMessageAndShiftRegisterCFB_EncryptionTempla_140587080.cpp" />
    <ClCompile Include="Source\CompleteAnsyncConnectCNetProcessQEAAXXZ_140479970.cpp" />
    <ClCompile Include="Source\CompleteSendCPostSystemManagerQEAAXPEADZ_1403261A0.cpp" />
    <ClCompile Include="Source\Complete_db_Update_Data_For_Post_SendCMainThreadAE_1401B90F0.cpp" />
    <ClCompile Include="Source\ComputeMessageRepresentativeDL_SignatureMessageEnc_140630320.cpp" />
    <ClCompile Include="Source\ComputeMessageRepresentativeDL_SignatureMessageEnc_1406304D0.cpp" />
    <ClCompile Include="Source\ConnectCNetSocketQEAAHKPEAUsockaddr_inZ_14047E830.cpp" />
    <ClCompile Include="Source\ConnectCNetWorkingQEAAHKKKGZ_1404819B0.cpp" />
    <ClCompile Include="Source\ConnectionStatusRequestCNetworkEXAEAA_NHZ_1401C7490.cpp" />
    <ClCompile Include="Source\ConnectThreadCNetProcessCAXPEAXZ_14047B230.cpp" />
    <ClCompile Include="Source\ConnectToNcashCEngNetworkBillEXQEAAHXZ_14031A420.cpp" />
    <ClCompile Include="Source\connect_0_1404DBA52.cpp" />
    <ClCompile Include="Source\constructallocatorUMessageRangeMeterFilterCryptoPP_140600A00.cpp" />
    <ClCompile Include="Source\constructallocatorURECV_DATAstdQEAAXPEAURECV_DATAA_14031AB90.cpp" />
    <ClCompile Include="Source\CopyMessagesToBufferedTransformationCryptoPPQEBAIA_1405F5590.cpp" />
    <ClCompile Include="Source\CopyMessagesToMessageQueueCryptoPPQEBAIAEAVBuffere_1406545F0.cpp" />
    <ClCompile Include="Source\CopyMessagesToStoreCryptoPPQEBAIAEAVBufferedTransf_1405FE150.cpp" />
    <ClCompile Include="Source\CopyRangeTo2MessageQueueCryptoPPUEBA_KAEAVBuffered_1406543C0.cpp" />
    <ClCompile Include="Source\copyV_Deque_iteratorURECV_DATAVallocatorURECV_DATA_14031EFD0.cpp" />
    <ClCompile Include="Source\copy_backwardV_Deque_iteratorURECV_DATAVallocatorU_14031EC10.cpp" />
    <ClCompile Include="Source\Dauto_ptrVPK_MessageAccumulatorBaseCryptoPPstdQEBA_14056C6C0.cpp" />
    <ClCompile Include="Source\Dauto_ptrVPK_MessageAccumulatorCryptoPPstdQEBAAEAV_1405F7970.cpp" />
    <ClCompile Include="Source\db_sendwebracebosssmsCMainThreadQEAAEPEAU_qry_case_1401B2B10.cpp" />
    <ClCompile Include="Source\deallocateallocatorPEAUMessageRangeMeterFilterCryp_140600C70.cpp" />
    <ClCompile Include="Source\deallocateallocatorPEAURECV_DATAstdQEAAXPEAPEAUREC_14031AD30.cpp" />
    <ClCompile Include="Source\deallocateallocatorUMessageRangeMeterFilterCryptoP_140600C40.cpp" />
    <ClCompile Include="Source\deallocateallocatorURECV_DATAstdQEAAXPEAURECV_DATA_14031FD20.cpp" />
    <ClCompile Include="Source\destroyallocatorPEAUMessageRangeMeterFilterCryptoP_140600CC0.cpp" />
    <ClCompile Include="Source\destroyallocatorPEAURECV_DATAstdQEAAXPEAPEAURECV_D_14031FD70.cpp" />
    <ClCompile Include="Source\destroyallocatorUMessageRangeMeterFilterCryptoPPst_140600A30.cpp" />
    <ClCompile Include="Source\destroyallocatorURECV_DATAstdQEAAXPEAURECV_DATAZ_14031EAE0.cpp" />
    <ClCompile Include="Source\DigestSizePK_MessageAccumulatorCryptoPPUEBAIXZ_140562E10.cpp" />
    <ClCompile Include="Source\DispatchMessageA_0_140676E76.cpp" />
    <ClCompile Include="Source\Dmember_ptrVPK_MessageAccumulatorCryptoPPCryptoPPQ_140600240.cpp" />
    <ClCompile Include="Source\DoMessageBoxCWinAppUEAAHPEBDIIZ_0_1404DBFB8.cpp" />
    <ClCompile Include="Source\D_Deque_const_iteratorUMessageRangeMeterFilterCryp_140600D30.cpp" />
    <ClCompile Include="Source\D_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031E580.cpp" />
    <ClCompile Include="Source\D_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140600A90.cpp" />
    <ClCompile Include="Source\D_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031D900.cpp" />
    <ClCompile Include="Source\emptydequeUMessageRangeMeterFilterCryptoPPVallocat_1405FFED0.cpp" />
    <ClCompile Include="Source\emptydequeURECV_DATAVallocatorURECV_DATAstdstdQEBA_14031E7F0.cpp" />
    <ClCompile Include="Source\EmptySocketBufferCNetSocketQEAAXKZ_14047EB70.cpp" />
    <ClCompile Include="Source\enddequeUMessageRangeMeterFilterCryptoPPVallocator_1405FFE80.cpp" />
    <ClCompile Include="Source\enddequeURECV_DATAVallocatorURECV_DATAstdstdQEAAAV_14031D670.cpp" />
    <ClCompile Include="Source\erasedequeURECV_DATAVallocatorURECV_DATAstdstdQEAA_14031D700.cpp" />
    <ClCompile Include="Source\erasedequeURECV_DATAVallocatorURECV_DATAstdstdQEAA_14031DC40.cpp" />
    <ClCompile Include="Source\E_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603C20.cpp" />
    <ClCompile Include="Source\E_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031EAB0.cpp" />
    <ClCompile Include="Source\E_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406037F0.cpp" />
    <ClCompile Include="Source\E_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140603810.cpp" />
    <ClCompile Include="Source\E_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031D940.cpp" />
    <ClCompile Include="Source\E_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E3D0.cpp" />
    <ClCompile Include="Source\failwithmessage_1404DDA60.cpp" />
    <ClCompile Include="Source\FindEmptySocketCNetSocketQEAAKXZ_14047F180.cpp" />
    <ClCompile Include="Source\FormatMessageA_0_1404DEE80.cpp" />
    <ClCompile Include="Source\frontdequeUMessageRangeMeterFilterCryptoPPVallocat_1405FFF00.cpp" />
    <ClCompile Include="Source\F_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603C50.cpp" />
    <ClCompile Include="Source\F_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031F940.cpp" />
    <ClCompile Include="Source\F_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406038D0.cpp" />
    <ClCompile Include="Source\F_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031F8F0.cpp" />
    <ClCompile Include="Source\GetBillingDBConnectionStatusCashDbWorkerQEAA_NXZ_1402EEE50.cpp" />
    <ClCompile Include="Source\GetBillingDBConnectionStatusCCashDBWorkManagerQEAA_1402F33F0.cpp" />
    <ClCompile Include="Source\GetBitAfterSetLimSocketYAKEZ_14003E470.cpp" />
    <ClCompile Include="Source\GetCheckRecvTimeCNetWorkingQEAAKKKZ_140481D60.cpp" />
    <ClCompile Include="Source\GetConnectionHookCCmdTargetMEAAPEAUIConnectionPoin_1404DBBF8.cpp" />
    <ClCompile Include="Source\GetConnectionMapCCmdTargetMEBAPEBUAFX_CONNECTIONMA_1404DBBD4.cpp" />
    <ClCompile Include="Source\GetConnectTime_AddBySecYAKHZ_14043CB80.cpp" />
    <ClCompile Include="Source\GetDBTaskConnectionStatusCLogTypeDBTaskManagerQEAA_1402C3870.cpp" />
    <ClCompile Include="Source\GetDefItemUpgSocketNumYAEHHZ_14003BB50.cpp" />
    <ClCompile Include="Source\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseUEC2NP_140566260.cpp" />
    <ClCompile Include="Source\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseUECPPo_1405645C0.cpp" />
    <ClCompile Include="Source\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseVInteg_140562160.cpp" />
    <ClCompile Include="Source\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseVInteg_1406342B0.cpp" />
    <ClCompile Include="Source\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseVInteg_140634780.cpp" />
    <ClCompile Include="Source\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseUEC2_140566F30.cpp" />
    <ClCompile Include="Source\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseUECP_140565290.cpp" />
    <ClCompile Include="Source\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseVInt_1405634D0.cpp" />
    <ClCompile Include="Source\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseVInt_1406344E0.cpp" />
    <ClCompile Include="Source\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseVInt_1406349B0.cpp" />
    <ClCompile Include="Source\GetExtraConnectionPointsCCmdTargetMEAAHPEAVCPtrArr_1404DBBF2.cpp" />
    <ClCompile Include="Source\GetHashIdentifierDL_ObjectImplVDL_SignerBaseUEC2NP_140566190.cpp" />
    <ClCompile Include="Source\GetHashIdentifierDL_ObjectImplVDL_SignerBaseUECPPo_1405644F0.cpp" />
    <ClCompile Include="Source\GetHashIdentifierDL_ObjectImplVDL_SignerBaseVInteg_140562090.cpp" />
    <ClCompile Include="Source\GetHashIdentifierDL_ObjectImplVDL_SignerBaseVInteg_1406341E0.cpp" />
    <ClCompile Include="Source\GetHashIdentifierDL_ObjectImplVDL_SignerBaseVInteg_1406346B0.cpp" />
    <ClCompile Include="Source\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseUEC2_140566E60.cpp" />
    <ClCompile Include="Source\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseUECP_1405651C0.cpp" />
    <ClCompile Include="Source\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseVInt_140563400.cpp" />
    <ClCompile Include="Source\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseVInt_140634410.cpp" />
    <ClCompile Include="Source\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseVInt_1406348E0.cpp" />
    <ClCompile Include="Source\GetItemUpgLimSocketYAEKZ_14003E290.cpp" />
    <ClCompile Include="Source\GetKey1_messageQEAAKXZ_1401C0320.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseUEC2_140566250.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseUECP_1405645B0.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseVInt_140562150.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseVInt_1406342A0.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseVInt_140634770.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseUE_140565280.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseUE_140566F20.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseVI_1405634C0.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseVI_1406344D0.cpp" />
    <ClCompile Include="Source\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseVI_1406349A0.cpp" />
    <ClCompile Include="Source\GetMessageA_0_140676E88.cpp" />
    <ClCompile Include="Source\GetMessageA_messageQEAAKXZ_1401C0300.cpp" />
    <ClCompile Include="Source\GetMessageBarCFrameWndUEAAPEAVCWndXZ_0_1404DBE56.cpp" />
    <ClCompile Include="Source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_1405620C0.cpp" />
    <ClCompile Include="Source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_140564520.cpp" />
    <ClCompile Include="Source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_1405661C0.cpp" />
    <ClCompile Include="Source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_140634210.cpp" />
    <ClCompile Include="Source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_1406346E0.cpp" />
    <ClCompile Include="Source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_140563430.cpp" />
    <ClCompile Include="Source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_1405651F0.cpp" />
    <ClCompile Include="Source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_140566E90.cpp" />
    <ClCompile Include="Source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_140634440.cpp" />
    <ClCompile Include="Source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_140634910.cpp" />
    <ClCompile Include="Source\GetMessageMapCAboutDlgMEBAPEBUAFX_MSGMAPXZ_1400297D0.cpp" />
    <ClCompile Include="Source\GetMessageMapCDisplayViewMEBAPEBUAFX_MSGMAPXZ_14002BD00.cpp" />
    <ClCompile Include="Source\GetMessageMapCGameServerAppMEBAPEBUAFX_MSGMAPXZ_1400293B0.cpp" />
    <ClCompile Include="Source\GetMessageMapCGameServerDocMEBAPEBUAFX_MSGMAPXZ_140029E10.cpp" />
    <ClCompile Include="Source\GetMessageMapCGameServerViewMEBAPEBUAFX_MSGMAPXZ_14002A690.cpp" />
    <ClCompile Include="Source\GetMessageMapCInfoSheetMEBAPEBUAFX_MSGMAPXZ_14002D990.cpp" />
    <ClCompile Include="Source\GetMessageMapCIPXTabMEBAPEBUAFX_MSGMAPXZ_14002DFD0.cpp" />
    <ClCompile Include="Source\GetMessageMapCMainFrameMEBAPEBUAFX_MSGMAPXZ_140027CD0.cpp" />
    <ClCompile Include="Source\GetMessageMapCMapTabMEBAPEBUAFX_MSGMAPXZ_14002E620.cpp" />
    <ClCompile Include="Source\GetMessageMapCObjectSearchDlgMEBAPEBUAFX_MSGMAPXZ_14002F290.cpp" />
    <ClCompile Include="Source\GetMessageMapCObjectTabMEBAPEBUAFX_MSGMAPXZ_14002FF40.cpp" />
    <ClCompile Include="Source\GetMessageMapCOpenDlgMEBAPEBUAFX_MSGMAPXZ_140029210.cpp" />
    <ClCompile Include="Source\GetMessageMapCServerTabMEBAPEBUAFX_MSGMAPXZ_140034E10.cpp" />
    <ClCompile Include="Source\GetMessageMapCTCPTabMEBAPEBUAFX_MSGMAPXZ_140035CB0.cpp" />
    <ClCompile Include="Source\GetMessageMapLicensePopupDlgMEBAPEBUAFX_MSGMAPXZ_140026900.cpp" />
    <ClCompile Include="Source\GetMessageStringCFrameWndUEBAXIAEAVCStringTDVStrTr_1404DBE3E.cpp" />
    <ClCompile Include="Source\GetNextMessageBufferedTransformationCryptoPPUEAA_N_1405F52A0.cpp" />
    <ClCompile Include="Source\GetNextMessageMessageQueueCryptoPPUEAA_NXZ_140654540.cpp" />
    <ClCompile Include="Source\GetNextMessageSeriesBufferedTransformationCryptoPP_14044CF10.cpp" />
    <ClCompile Include="Source\GetNextMessageStoreCryptoPPUEAA_NXZ_1405FE100.cpp" />
    <ClCompile Include="Source\GetRaceWarRecvrCPvpCashPointQEAA_NXZ_140284CF0.cpp" />
    <ClCompile Include="Source\GetSendMsgCMsgListManagerRACE_BOSS_MSGQEAAPEAVCMsg_14029FBA0.cpp" />
    <ClCompile Include="Source\GetSendMsgCMsgListRACE_BOSS_MSGQEAAPEAVCMsg2XZ_14029E920.cpp" />
    <ClCompile Include="Source\GetSendPoint_NET_BUFFERQEAAPEADPEAHPEA_NZ_14047D9B0.cpp" />
    <ClCompile Include="Source\GetSendThreadFrameCNetProcessQEAAKXZ_140479F80.cpp" />
    <ClCompile Include="Source\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseUE_1405644B0.cpp" />
    <ClCompile Include="Source\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseUE_140566150.cpp" />
    <ClCompile Include="Source\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseVI_140562050.cpp" />
    <ClCompile Include="Source\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseVI_1406341A0.cpp" />
    <ClCompile Include="Source\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseVI_140634670.cpp" />
    <ClCompile Include="Source\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_1405633C0.cpp" />
    <ClCompile Include="Source\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_140565180.cpp" />
    <ClCompile Include="Source\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_140566E20.cpp" />
    <ClCompile Include="Source\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_1406343D0.cpp" />
    <ClCompile Include="Source\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_1406348A0.cpp" />
    <ClCompile Include="Source\GetSocketCNetSocketQEAAPEAU_socketKZ_14047D3B0.cpp" />
    <ClCompile Include="Source\GetSocketCNetWorkingQEAAPEAU_socketKKZ_140481BD0.cpp" />
    <ClCompile Include="Source\GetSocketIPAddressCNetSocketQEAAKKZ_14047F150.cpp" />
    <ClCompile Include="Source\GetSocketNameYA_N_KPEADZ_14047FFE0.cpp" />
    <ClCompile Include="Source\GetSocketTypeCNetSocketQEAAPEAU_SOCK_TYPE_PARAMXZ_14047F110.cpp" />
    <ClCompile Include="Source\GetTalikFromSocketYAEKEZ_14003E2E0.cpp" />
    <ClCompile Include="Source\GetTalikRecvrPointCPvpCashMngQEAAHEKZ_1403F6420.cpp" />
    <ClCompile Include="Source\GetTalikRecvrPointCPvpCashMngQEAAHHZ_1403F63F0.cpp" />
    <ClCompile Include="Source\GetTalikRecvrPointCPvpCashPointQEAAHEKZ_1403F5190.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCAboutDlgKAPEBUAFX_MSGMAPXZ_140029810.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCDialogKAPEBUAFX_MSGMAPXZ_0_1404DBD90.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCDisplayViewKAPEBUAFX_MSGMAPXZ_14002BD40.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCDocumentKAPEBUAFX_MSGMAPXZ_0_1404DC042.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCFormViewKAPEBUAFX_MSGMAPXZ_0_1404DC10E.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCFrameWndKAPEBUAFX_MSGMAPXZ_0_1404DBDCC.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCGameServerAppKAPEBUAFX_MSGMAPXZ_1400293F0.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCGameServerDocKAPEBUAFX_MSGMAPXZ_140029E50.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCGameServerViewKAPEBUAFX_MSGMAPXZ_14002A6D0.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCInfoSheetKAPEBUAFX_MSGMAPXZ_14002D9D0.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCIPXTabKAPEBUAFX_MSGMAPXZ_14002E010.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCMainFrameKAPEBUAFX_MSGMAPXZ_140027D10.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCMapTabKAPEBUAFX_MSGMAPXZ_14002E660.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCObjectSearchDlgKAPEBUAFX_MSGMAPX_14002F2D0.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCObjectTabKAPEBUAFX_MSGMAPXZ_14002FF80.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCOpenDlgKAPEBUAFX_MSGMAPXZ_140029250.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCPropertyPageKAPEBUAFX_MSGMAPXZ_0_1404DC360.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCPropertySheetKAPEBUAFX_MSGMAPXZ__1404DC2F4.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCServerTabKAPEBUAFX_MSGMAPXZ_140034E50.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCTCPTabKAPEBUAFX_MSGMAPXZ_140035CF0.cpp" />
    <ClCompile Include="Source\GetThisMessageMapCWinAppKAPEBUAFX_MSGMAPXZ_0_1404DBF16.cpp" />
    <ClCompile Include="Source\GetThisMessageMapLicensePopupDlgKAPEBUAFX_MSGMAPXZ_140026940.cpp" />
    <ClCompile Include="Source\GetTotalCountCNetSocketQEAAPEAU_total_countXZ_14047F130.cpp" />
    <ClCompile Include="Source\G_Deque_const_iteratorUMessageRangeMeterFilterCryp_140601A00.cpp" />
    <ClCompile Include="Source\G_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031EB70.cpp" />
    <ClCompile Include="Source\G_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140601800.cpp" />
    <ClCompile Include="Source\G_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406039B0.cpp" />
    <ClCompile Include="Source\G_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E9E0.cpp" />
    <ClCompile Include="Source\HandleMouseMessagesCD3DArcBallQEAA_JPEAUHWND__I_K__14052C510.cpp" />
    <ClCompile Include="Source\H_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406038F0.cpp" />
    <ClCompile Include="Source\H_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E420.cpp" />
    <ClCompile Include="Source\InitAcceptSocketCNetSocketAEAA_NPEADZ_14047EE00.cpp" />
    <ClCompile Include="Source\InitParam_socketQEAAXXZ_14047F910.cpp" />
    <ClCompile Include="Source\InputRecoverableMessageDL_SignerBaseUEC2NPointCryp_1405662B0.cpp" />
    <ClCompile Include="Source\InputRecoverableMessageDL_SignerBaseUECPPointCrypt_140564610.cpp" />
    <ClCompile Include="Source\InputRecoverableMessageDL_SignerBaseVIntegerCrypto_1405621E0.cpp" />
    <ClCompile Include="Source\InputRecoverableMessageTF_SignerBaseCryptoPPUEBAXA_140622820.cpp" />
    <ClCompile Include="Source\InputSignatureDL_VerifierBaseUEC2NPointCryptoPPCry_140566F80.cpp" />
    <ClCompile Include="Source\InputSignatureDL_VerifierBaseUECPPointCryptoPPCryp_1405652E0.cpp" />
    <ClCompile Include="Source\InputSignatureDL_VerifierBaseVIntegerCryptoPPCrypt_140563540.cpp" />
    <ClCompile Include="Source\InputSignatureTF_VerifierBaseCryptoPPUEBAXAEAVPK_M_140623020.cpp" />
    <ClCompile Include="Source\InternetConnectA_0_14066D832.cpp" />
    <ClCompile Include="Source\IsGetConnectedCEngNetworkBillEXQEAA_NXZ_14022C320.cpp" />
    <ClCompile Include="Source\IsIdleMessageCWinThreadUEAAHPEAUtagMSGZ_0_1404DBF6A.cpp" />
    <ClCompile Include="Source\IsolatedFlushMessageQueueCryptoPPUEAA_N_N0Z_140655100.cpp" />
    <ClCompile Include="Source\IsolatedInitializeMessageQueueCryptoPPUEAAXAEBVNam_140654FB0.cpp" />
    <ClCompile Include="Source\IsolatedMessageSeriesEndBufferedTransformationCryp_14054B910.cpp" />
    <ClCompile Include="Source\IsolatedMessageSeriesEndInputRejectingVBufferedTra_140453E50.cpp" />
    <ClCompile Include="Source\IsolatedMessageSeriesEndInputRejectingVFilterCrypt_14044E910.cpp" />
    <ClCompile Include="Source\IsolatedMessageSeriesEndMessageQueueCryptoPPUEAA_N_140655120.cpp" />
    <ClCompile Include="Source\IsolatedMessageSeriesEndMeterFilterCryptoPPUEAA_N__1405F9D80.cpp" />
    <ClCompile Include="Source\IsRecvableContEffectCGameObjectUEAA_NXZ_14012C770.cpp" />
    <ClCompile Include="Source\IsRecvableContEffectCMonsterUEAA_NXZ_1401469A0.cpp" />
    <ClCompile Include="Source\IsRecvedQuestByNPCCQuestMgrQEAA_NHZ_14028B000.cpp" />
    <ClCompile Include="Source\IsRecvedQuestByNPCCQuestMgrQEAA_NPEBDZ_14028B020.cpp" />
    <ClCompile Include="Source\IsSendFromWebCMsgRACE_BOSS_MSGQEAA_NXZ_1402A2D10.cpp" />
    <ClCompile Include="Source\IsSendTimeCMsgRACE_BOSS_MSGQEAA_NXZ_14029DC50.cpp" />
    <ClCompile Include="Source\iter_swapV_Deque_iteratorUMessageRangeMeterFilterC_1406042F0.cpp" />
    <ClCompile Include="Source\j_0allocatorURECV_DATAstdQEAAAEBV01Z_140002E6E.cpp" />
    <ClCompile Include="Source\j_0allocatorURECV_DATAstdQEAAXZ_140002261.cpp" />
    <ClCompile Include="Source\j_0CNetSocketQEAAXZ_140001370.cpp" />
    <ClCompile Include="Source\j_0dequeURECV_DATAVallocatorURECV_DATAstdstdQEAAXZ_14001200D.cpp" />
    <ClCompile Include="Source\j_0RECV_DATAQEAAXZ_1400108F7.cpp" />
    <ClCompile Include="Source\j_0URECV_DATAallocatorPEAURECV_DATAstdQEAAAEBVallo_140009C1E.cpp" />
    <ClCompile Include="Source\j_0_announ_message_receipt_udpQEAAXZ_1400095CA.cpp" />
    <ClCompile Include="Source\j_0_apex_send_ipQEAAXZ_14000E903.cpp" />
    <ClCompile Include="Source\j_0_chat_message_receipt_udpQEAAXZ_1400054CA.cpp" />
    <ClCompile Include="Source\j_0_chat_steal_message_gm_zoclQEAAXZ_140013836.cpp" />
    <ClCompile Include="Source\j_0_Deque_const_iteratorURECV_DATAVallocatorURECV__140004C5A.cpp" />
    <ClCompile Include="Source\j_0_Deque_const_iteratorURECV_DATAVallocatorURECV__140008B61.cpp" />
    <ClCompile Include="Source\j_0_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_140003503.cpp" />
    <ClCompile Include="Source\j_0_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_1400049EE.cpp" />
    <ClCompile Include="Source\j_0_Deque_mapURECV_DATAVallocatorURECV_DATAstdstdI_14000BEBF.cpp" />
    <ClCompile Include="Source\j_0_Deque_valURECV_DATAVallocatorURECV_DATAstdstdI_1400118EC.cpp" />
    <ClCompile Include="Source\j_0_messageQEAAXZ_140009007.cpp" />
    <ClCompile Include="Source\j_0_qry_case_post_sendQEAAXZ_14000A902.cpp" />
    <ClCompile Include="Source\j_0_qry_case_update_data_for_post_sendQEAAXZ_140010DF7.cpp" />
    <ClCompile Include="Source\j_0_RanitURECV_DATA_JPEBU1AEBU1stdQEAAXZ_1400023A6.cpp" />
    <ClCompile Include="Source\j_0_socketQEAAXZ_1400057CC.cpp" />
    <ClCompile Include="Source\j_0_talik_recvr_listQEAAXZ_140006109.cpp" />
    <ClCompile Include="Source\j_0__list_qry_case_post_sendQEAAXZ_140012D8C.cpp" />
    <ClCompile Include="Source\j_1CNetSocketUEAAXZ_14000322E.cpp" />
    <ClCompile Include="Source\j_1dequeURECV_DATAVallocatorURECV_DATAstdstdQEAAXZ_14000D30F.cpp" />
    <ClCompile Include="Source\j_1_Deque_const_iteratorURECV_DATAVallocatorURECV__14000ED9A.cpp" />
    <ClCompile Include="Source\j_1_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_140008314.cpp" />
    <ClCompile Include="Source\j_1_messageQEAAXZ_14000C12B.cpp" />
    <ClCompile Include="Source\j_1_RanitURECV_DATA_JPEBU1AEBU1stdQEAAXZ_140003229.cpp" />
    <ClCompile Include="Source\j_1_socketQEAAXZ_140011CE3.cpp" />
    <ClCompile Include="Source\j_8_Deque_const_iteratorURECV_DATAVallocatorURECV__140012030.cpp" />
    <ClCompile Include="Source\j_9_Deque_const_iteratorURECV_DATAVallocatorURECV__14000C7AC.cpp" />
    <ClCompile Include="Source\j_Accept_ClientCNetSocketQEAA_NKZ_140004377.cpp" />
    <ClCompile Include="Source\j_Accept_ServerCNetSocketQEAAKXZ_140011EB4.cpp" />
    <ClCompile Include="Source\j_AddPassablePacketCMainThreadAEAAXXZ_14000AE98.cpp" />
    <ClCompile Include="Source\j_allocateallocatorPEAURECV_DATAstdQEAAPEAPEAURECV_1400055A6.cpp" />
    <ClCompile Include="Source\j_allocateallocatorURECV_DATAstdQEAAPEAURECV_DATA__140001B9F.cpp" />
    <ClCompile Include="Source\j_AnsyncConnectCompleteCNetworkEXEEAAXKKHZ_140001ACD.cpp" />
    <ClCompile Include="Source\j_AnsyncConnectCompleteCNetWorkingUEAAXKKHZ_140002BD5.cpp" />
    <ClCompile Include="Source\j_begindequeURECV_DATAVallocatorURECV_DATAstdstdQE_14001151D.cpp" />
    <ClCompile Include="Source\j_CashDBInfoRecvResultCNetworkEXAEAA_NHPEADZ_140009ED5.cpp" />
    <ClCompile Include="Source\j_ChannelMessageSeriesEndInputRejectingVBufferedTr_14000A191.cpp" />
    <ClCompile Include="Source\j_ChannelMessageSeriesEndInputRejectingVFilterCryp_140005DF8.cpp" />
    <ClCompile Include="Source\j_ChatAllRecvYesOrNoCNetworkEXAEAA_NHPEADZ_1400034FE.cpp" />
    <ClCompile Include="Source\j_ChatMapRecvYesOrNoCNetworkEXAEAA_NHPEADZ_1400046C4.cpp" />
    <ClCompile Include="Source\j_CheckSendNewMissionMsgCDarkHoleChannelQEAAXXZ_140001FD7.cpp" />
    <ClCompile Include="Source\j_CloseAllCNetSocketAEAAXXZ_1400139F8.cpp" />
    <ClCompile Include="Source\j_CloseSocketCNetProcessQEAAXK_NZ_140006398.cpp" />
    <ClCompile Include="Source\j_CloseSocketCNetSocketQEAA_NKZ_14000F664.cpp" />
    <ClCompile Include="Source\j_CloseSocketCNetWorkingQEAAXKK_NZ_14000EFCA.cpp" />
    <ClCompile Include="Source\j_CompleteAnsyncConnectCNetProcessQEAAXXZ_140005173.cpp" />
    <ClCompile Include="Source\j_CompleteSendCPostSystemManagerQEAAXPEADZ_140009AD9.cpp" />
    <ClCompile Include="Source\j_Complete_db_Update_Data_For_Post_SendCMainThread_14000DEA9.cpp" />
    <ClCompile Include="Source\j_ConnectCNetSocketQEAAHKPEAUsockaddr_inZ_140012EB3.cpp" />
    <ClCompile Include="Source\j_ConnectCNetWorkingQEAAHKKKGZ_140003DF0.cpp" />
    <ClCompile Include="Source\j_ConnectionStatusRequestCNetworkEXAEAA_NHZ_14000F50B.cpp" />
    <ClCompile Include="Source\j_ConnectThreadCNetProcessCAXPEAXZ_1400135B6.cpp" />
    <ClCompile Include="Source\j_ConnectToNcashCEngNetworkBillEXQEAAHXZ_140010776.cpp" />
    <ClCompile Include="Source\j_constructallocatorURECV_DATAstdQEAAXPEAURECV_DAT_14000433B.cpp" />
    <ClCompile Include="Source\j_copyV_Deque_iteratorURECV_DATAVallocatorURECV_DA_1400136FB.cpp" />
    <ClCompile Include="Source\j_copy_backwardV_Deque_iteratorURECV_DATAVallocato_14001338B.cpp" />
    <ClCompile Include="Source\j_db_sendwebracebosssmsCMainThreadQEAAEPEAU_qry_ca_14000DD4B.cpp" />
    <ClCompile Include="Source\j_deallocateallocatorPEAURECV_DATAstdQEAAXPEAPEAUR_140005A38.cpp" />
    <ClCompile Include="Source\j_deallocateallocatorURECV_DATAstdQEAAXPEAURECV_DA_140005718.cpp" />
    <ClCompile Include="Source\j_destroyallocatorPEAURECV_DATAstdQEAAXPEAPEAURECV_140012869.cpp" />
    <ClCompile Include="Source\j_destroyallocatorURECV_DATAstdQEAAXPEAURECV_DATAZ_14000D913.cpp" />
    <ClCompile Include="Source\j_D_Deque_const_iteratorURECV_DATAVallocatorURECV__14000C2C5.cpp" />
    <ClCompile Include="Source\j_D_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000D6ED.cpp" />
    <ClCompile Include="Source\j_emptydequeURECV_DATAVallocatorURECV_DATAstdstdQE_140002BB7.cpp" />
    <ClCompile Include="Source\j_EmptySocketBufferCNetSocketQEAAXKZ_1400128D2.cpp" />
    <ClCompile Include="Source\j_enddequeURECV_DATAVallocatorURECV_DATAstdstdQEAA_140012887.cpp" />
    <ClCompile Include="Source\j_erasedequeURECV_DATAVallocatorURECV_DATAstdstdQE_1400041BF.cpp" />
    <ClCompile Include="Source\j_erasedequeURECV_DATAVallocatorURECV_DATAstdstdQE_14000C7E8.cpp" />
    <ClCompile Include="Source\j_E_Deque_const_iteratorURECV_DATAVallocatorURECV__140002FF4.cpp" />
    <ClCompile Include="Source\j_E_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000363E.cpp" />
    <ClCompile Include="Source\j_E_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_140009E2B.cpp" />
    <ClCompile Include="Source\j_FindEmptySocketCNetSocketQEAAKXZ_14000589E.cpp" />
    <ClCompile Include="Source\j_F_Deque_const_iteratorURECV_DATAVallocatorURECV__14000AF42.cpp" />
    <ClCompile Include="Source\j_F_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000F902.cpp" />
    <ClCompile Include="Source\j_GetBillingDBConnectionStatusCashDbWorkerQEAA_NXZ_140004AD4.cpp" />
    <ClCompile Include="Source\j_GetBillingDBConnectionStatusCCashDBWorkManagerQE_14000B9C4.cpp" />
    <ClCompile Include="Source\j_GetBitAfterSetLimSocketYAKEZ_14000213F.cpp" />
    <ClCompile Include="Source\j_GetCheckRecvTimeCNetWorkingQEAAKKKZ_140004093.cpp" />
    <ClCompile Include="Source\j_GetConnectTime_AddBySecYAKHZ_140005BA0.cpp" />
    <ClCompile Include="Source\j_GetDBTaskConnectionStatusCLogTypeDBTaskManagerQE_140009F6B.cpp" />
    <ClCompile Include="Source\j_GetDefItemUpgSocketNumYAEHHZ_140009A2F.cpp" />
    <ClCompile Include="Source\j_GetItemUpgLimSocketYAEKZ_140005A24.cpp" />
    <ClCompile Include="Source\j_GetKey1_messageQEAAKXZ_1400108A2.cpp" />
    <ClCompile Include="Source\j_GetMessageA_messageQEAAKXZ_14000353F.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCAboutDlgMEBAPEBUAFX_MSGMAPXZ_14000F259.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCDisplayViewMEBAPEBUAFX_MSGMAPXZ_140002BEE.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCGameServerAppMEBAPEBUAFX_MSGMAPXZ_14000B492.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCGameServerDocMEBAPEBUAFX_MSGMAPXZ_1400116DA.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCGameServerViewMEBAPEBUAFX_MSGMAPXZ_14001189C.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCInfoSheetMEBAPEBUAFX_MSGMAPXZ_14001167B.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCIPXTabMEBAPEBUAFX_MSGMAPXZ_140004692.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCMainFrameMEBAPEBUAFX_MSGMAPXZ_140001401.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCMapTabMEBAPEBUAFX_MSGMAPXZ_14000AEAC.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCObjectSearchDlgMEBAPEBUAFX_MSGMAPX_1400079AF.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCObjectTabMEBAPEBUAFX_MSGMAPXZ_14000D459.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCOpenDlgMEBAPEBUAFX_MSGMAPXZ_14000CBAD.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCServerTabMEBAPEBUAFX_MSGMAPXZ_1400089E0.cpp" />
    <ClCompile Include="Source\j_GetMessageMapCTCPTabMEBAPEBUAFX_MSGMAPXZ_14000DD87.cpp" />
    <ClCompile Include="Source\j_GetMessageMapLicensePopupDlgMEBAPEBUAFX_MSGMAPXZ_14001171B.cpp" />
    <ClCompile Include="Source\j_GetNextMessageSeriesBufferedTransformationCrypto_1400095CF.cpp" />
    <ClCompile Include="Source\j_GetRaceWarRecvrCPvpCashPointQEAA_NXZ_14000758B.cpp" />
    <ClCompile Include="Source\j_GetSendMsgCMsgListManagerRACE_BOSS_MSGQEAAPEAVCM_140013223.cpp" />
    <ClCompile Include="Source\j_GetSendMsgCMsgListRACE_BOSS_MSGQEAAPEAVCMsg2XZ_14000FBB9.cpp" />
    <ClCompile Include="Source\j_GetSendPoint_NET_BUFFERQEAAPEADPEAHPEA_NZ_1400119D2.cpp" />
    <ClCompile Include="Source\j_GetSendThreadFrameCNetProcessQEAAKXZ_140008800.cpp" />
    <ClCompile Include="Source\j_GetSocketCNetSocketQEAAPEAU_socketKZ_14000C8E7.cpp" />
    <ClCompile Include="Source\j_GetSocketCNetWorkingQEAAPEAU_socketKKZ_1400059B1.cpp" />
    <ClCompile Include="Source\j_GetSocketIPAddressCNetSocketQEAAKKZ_1400032EC.cpp" />
    <ClCompile Include="Source\j_GetSocketNameYA_N_KPEADZ_14000215D.cpp" />
    <ClCompile Include="Source\j_GetSocketTypeCNetSocketQEAAPEAU_SOCK_TYPE_PARAMX_140011450.cpp" />
    <ClCompile Include="Source\j_GetTalikFromSocketYAEKEZ_140013DBD.cpp" />
    <ClCompile Include="Source\j_GetTalikRecvrPointCPvpCashMngQEAAHEKZ_14000C3A1.cpp" />
    <ClCompile Include="Source\j_GetTalikRecvrPointCPvpCashMngQEAAHHZ_1400062DF.cpp" />
    <ClCompile Include="Source\j_GetTalikRecvrPointCPvpCashPointQEAAHEKZ_140001CDF.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCAboutDlgKAPEBUAFX_MSGMAPXZ_140012BA2.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCDisplayViewKAPEBUAFX_MSGMAPXZ_140010E6A.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCGameServerAppKAPEBUAFX_MSGMAPX_140012607.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCGameServerDocKAPEBUAFX_MSGMAPX_1400043C7.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCGameServerViewKAPEBUAFX_MSGMAP_14001388B.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCInfoSheetKAPEBUAFX_MSGMAPXZ_140003431.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCIPXTabKAPEBUAFX_MSGMAPXZ_14000636B.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCMainFrameKAPEBUAFX_MSGMAPXZ_1400070A9.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCMapTabKAPEBUAFX_MSGMAPXZ_14000C379.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCObjectSearchDlgKAPEBUAFX_MSGMA_14000153C.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCObjectTabKAPEBUAFX_MSGMAPXZ_1400097D7.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCOpenDlgKAPEBUAFX_MSGMAPXZ_14000C63A.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCServerTabKAPEBUAFX_MSGMAPXZ_1400104C4.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapCTCPTabKAPEBUAFX_MSGMAPXZ_14000F0CE.cpp" />
    <ClCompile Include="Source\j_GetThisMessageMapLicensePopupDlgKAPEBUAFX_MSGMAP_1400079F5.cpp" />
    <ClCompile Include="Source\j_GetTotalCountCNetSocketQEAAPEAU_total_countXZ_14000168B.cpp" />
    <ClCompile Include="Source\j_G_Deque_const_iteratorURECV_DATAVallocatorURECV__140002568.cpp" />
    <ClCompile Include="Source\j_G_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000742D.cpp" />
    <ClCompile Include="Source\j_H_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_140001410.cpp" />
    <ClCompile Include="Source\j_InitAcceptSocketCNetSocketAEAA_NPEADZ_1400136EC.cpp" />
    <ClCompile Include="Source\j_InitParam_socketQEAAXXZ_1400065DC.cpp" />
    <ClCompile Include="Source\j_IsGetConnectedCEngNetworkBillEXQEAA_NXZ_14000F150.cpp" />
    <ClCompile Include="Source\j_IsolatedMessageSeriesEndInputRejectingVBufferedT_1400120DF.cpp" />
    <ClCompile Include="Source\j_IsolatedMessageSeriesEndInputRejectingVFilterCry_140003FFD.cpp" />
    <ClCompile Include="Source\j_IsRecvableContEffectCGameObjectUEAA_NXZ_140010681.cpp" />
    <ClCompile Include="Source\j_IsRecvableContEffectCMonsterUEAA_NXZ_140004B29.cpp" />
    <ClCompile Include="Source\j_IsRecvedQuestByNPCCQuestMgrQEAA_NHZ_140003535.cpp" />
    <ClCompile Include="Source\j_IsRecvedQuestByNPCCQuestMgrQEAA_NPEBDZ_14001136A.cpp" />
    <ClCompile Include="Source\j_IsSendFromWebCMsgRACE_BOSS_MSGQEAA_NXZ_140012495.cpp" />
    <ClCompile Include="Source\j_IsSendTimeCMsgRACE_BOSS_MSGQEAA_NXZ_14000B9BA.cpp" />
    <ClCompile Include="Source\j_LoadSendMsgCNetProcessQEAAHKGPEADGZ_140006C67.cpp" />
    <ClCompile Include="Source\j_LoadSendMsgCNetProcessQEAAHKPEAEPEADGZ_140009278.cpp" />
    <ClCompile Include="Source\j_lobby_disconnectCMgrAccountLobbyHistoryQEAAXPEAU_14000FF10.cpp" />
    <ClCompile Include="Source\j_LoopSubProcSendInformCHonorGuildQEAAXEZ_14000BDB1.cpp" />
    <ClCompile Include="Source\j_LoopSubProcSendInformCOreAmountMgrQEAAXXZ_140009660.cpp" />
    <ClCompile Include="Source\j_MakeBuddyPacketCGuildQEAAXXZ_14001249A.cpp" />
    <ClCompile Include="Source\j_MakeConnectionThreadCEnglandBillingMgrQEAA_NXZ_140004F3E.cpp" />
    <ClCompile Include="Source\j_MakeDownApplierPacketCGuildQEAAXXZ_1400065FA.cpp" />
    <ClCompile Include="Source\j_MakeDownMemberPacketCGuildQEAAXXZ_140011581.cpp" />
    <ClCompile Include="Source\j_MakeMoneyIOPacketCGuildQEAAXXZ_14000221B.cpp" />
    <ClCompile Include="Source\j_MakeQueryInfoPacketCGuildQEAAXXZ_140007D7E.cpp" />
    <ClCompile Include="Source\j_make_minepacketAutominePersonalAEAAXGGEGKZ_140002270.cpp" />
    <ClCompile Include="Source\j_max_sizeallocatorURECV_DATAstdQEBA_KXZ_140010127.cpp" />
    <ClCompile Include="Source\j_max_sizedequeURECV_DATAVallocatorURECV_DATAstdst_1400069BA.cpp" />
    <ClCompile Include="Source\j_MyMessageBoxYAXPEAD0ZZ_140006627.cpp" />
    <ClCompile Include="Source\j_NotifyAllProcessEndCNormalGuildBattleGUILD_BATTL_14000107D.cpp" />
    <ClCompile Include="Source\j_NumberOfMessageSeriesBufferedTransformationCrypt_14000DBD9.cpp" />
    <ClCompile Include="Source\j_NumberOfMessagesInThisSeriesBufferedTransformati_1400125E4.cpp" />
    <ClCompile Include="Source\j_NumberOfMessagesStoreCryptoPPUEBAIXZ_14001047E.cpp" />
    <ClCompile Include="Source\j_OnBUTTONWorldConnectCGameServerViewQEAAXXZ_1400018E8.cpp" />
    <ClCompile Include="Source\j_OnConnectHACKSHEILD_PARAM_ANTICPUEAAXHZ_14000E7EB.cpp" />
    <ClCompile Include="Source\j_OnDisConnectHACKSHEILD_PARAM_ANTICPUEAAXXZ_140010B9A.cpp" />
    <ClCompile Include="Source\j_OnLoopCNetSocketQEAAXXZ_140012BF7.cpp" />
    <ClCompile Include="Source\j_pc_CashDBInfoRecvResultCMainThreadQEAAXPEAD000KZ_140010F9B.cpp" />
    <ClCompile Include="Source\j_PopEmptyBufCMsgDataAEAAPEAU_messageXZ_14000996C.cpp" />
    <ClCompile Include="Source\j_PopMsgCMsgDataAEAAPEAU_messageXZ_14000E3BD.cpp" />
    <ClCompile Include="Source\j_pop_backdequeURECV_DATAVallocatorURECV_DATAstdst_140005D26.cpp" />
    <ClCompile Include="Source\j_pop_frontdequeURECV_DATAVallocatorURECV_DATAstds_1400044E9.cpp" />
    <ClCompile Include="Source\j_PostSendCPostSystemManagerQEAAEPEADZ_14001252B.cpp" />
    <ClCompile Include="Source\j_PostSendRequestCNetworkEXAEAA_NHPEADZ_14000CB3A.cpp" />
    <ClCompile Include="Source\j_post_senditemCMgrAvatorItemHistoryQEAAXPEADPEAU__1400036AC.cpp" />
    <ClCompile Include="Source\j_PotionSocketDivisionRequestCNetworkEXAEAA_NHPEAD_140003D23.cpp" />
    <ClCompile Include="Source\j_PotionSocketSeparationRequestCNetworkEXAEAA_NHPE_1400053C6.cpp" />
    <ClCompile Include="Source\j_ProcessMessageCMsgDataEEAAXPEAU_messageZ_14000EC14.cpp" />
    <ClCompile Include="Source\j_ProcessMessageCMsgProcessEEAAXPEAU_messageZ_14000F592.cpp" />
    <ClCompile Include="Source\j_PumpMessages2SourceTemplateVFileStoreCryptoPPCry_140006645.cpp" />
    <ClCompile Include="Source\j_PushAnsyncConnectCNetProcessQEAA_NKPEAUsockaddr__14000E9C1.cpp" />
    <ClCompile Include="Source\j_pushdata_qry_case_post_sendQEAA_NKEKKPEAD000U_IN_14000446C.cpp" />
    <ClCompile Include="Source\j_PushEmptyBufCMsgDataAEAAXPEAU_messageZ_14000CD15.cpp" />
    <ClCompile Include="Source\j_PushIPCheckListCNetSocketQEAA_NKZ_14000ECAF.cpp" />
    <ClCompile Include="Source\j_PushMsgCMsgDataAEAAXPEAU_messageZ_14000E67E.cpp" />
    <ClCompile Include="Source\j_push_frontdequeURECV_DATAVallocatorURECV_DATAstd_140005E02.cpp" />
    <ClCompile Include="Source\j_ReConnectDataBaseCRFNewDatabaseQEAA_NXZ_140002B67.cpp" />
    <ClCompile Include="Source\j_RecvClientLineCHackShieldExSystemUEAA_NHPEAU_MSG_14000E282.cpp" />
    <ClCompile Include="Source\j_RecvCNetSocketQEAA_NKPEADHPEAHZ_1400134D5.cpp" />
    <ClCompile Include="Source\j_RecvGameGuardDataCNationSettingManagerQEAA_NHPEA_1400121FC.cpp" />
    <ClCompile Include="Source\j_RecvThreadCNetProcessCAXPEAXZ_140007450.cpp" />
    <ClCompile Include="Source\j_Recv_ApexInformCChiNetworkEXQEAAXKKPEADZ_14000C89C.cpp" />
    <ClCompile Include="Source\j_Recv_ApexKillCChiNetworkEXQEAAXKKPEADZ_14000A2F4.cpp" />
    <ClCompile Include="Source\j_ReleaseCNetSocketQEAAXXZ_140003247.cpp" />
    <ClCompile Include="Source\j_Select_PostRecvSerialFromNameCRFWorldDatabaseQEA_140002103.cpp" />
    <ClCompile Include="Source\j_Select_PostRecvStorageCheckCRFWorldDatabaseQEAAH_140006C49.cpp" />
    <ClCompile Include="Source\j_SendBuyErrorResultCUnmannedTraderUserInfoQEAAXGE_140002FCC.cpp" />
    <ClCompile Include="Source\j_SendCancelRegistErrorResultCUnmannedTraderUserIn_14000E205.cpp" />
    <ClCompile Include="Source\j_SendCancelRegistSuccessResultCUnmannedTraderUser_14000E6C9.cpp" />
    <ClCompile Include="Source\j_SendCancelWebCRaceBossMsgControllerIEAAXEPEAVCMs_140004F9D.cpp" />
    <ClCompile Include="Source\j_SendCancleInfomManagerCRaceBossMsgControllerIEAA_14000AF7E.cpp" />
    <ClCompile Include="Source\j_SendCancleInfomSenderCRaceBossMsgControllerIEAAX_14000EFA2.cpp" />
    <ClCompile Include="Source\j_SendCashDBDSNRequestCNationSettingDataGBUEAAXXZ_140009F43.cpp" />
    <ClCompile Include="Source\j_SendCashDBDSNRequestCNationSettingDataKRUEAAXXZ_14000F92F.cpp" />
    <ClCompile Include="Source\j_SendCashDBDSNRequestCNationSettingDataNULLUEAAXX_140008AE4.cpp" />
    <ClCompile Include="Source\j_SendCashDBDSNRequestCNationSettingDataRUUEAAXXZ_140009250.cpp" />
    <ClCompile Include="Source\j_SendCashDBDSNRequestCNationSettingDataUEAAXXZ_14000C851.cpp" />
    <ClCompile Include="Source\j_SendCashDBDSNRequestCNationSettingManagerQEAAXXZ_14000E1B5.cpp" />
    <ClCompile Include="Source\j_SendCChiNetworkEXQEAAHPEAEKPEADGZ_1400102EE.cpp" />
    <ClCompile Include="Source\j_SendCCurrentGuildBattleInfoManagerGUILD_BATTLEQE_140007680.cpp" />
    <ClCompile Include="Source\j_SendCEngNetworkBillEXQEAAHPEAEPEADGZ_14000C077.cpp" />
    <ClCompile Include="Source\j_SendCGuildBattleRankManagerGUILD_BATTLEQEAAXHEKE_14001159F.cpp" />
    <ClCompile Include="Source\j_SendCGuildBattleReservedScheduleListManagerGUILD_140004994.cpp" />
    <ClCompile Include="Source\j_SendChangeAggroDataCMonsterAggroMgrQEAAXXZ_140007392.cpp" />
    <ClCompile Include="Source\j_SendCMsgListManagerRACE_BOSS_MSGQEAAHEKPEBD0AEAP_140008940.cpp" />
    <ClCompile Include="Source\j_SendCNetSocketQEAA_NKPEADHPEAHZ_140005F01.cpp" />
    <ClCompile Include="Source\j_SendCNormalGuildBattleGuildMemberGUILD_BATTLEQEA_140008BD4.cpp" />
    <ClCompile Include="Source\j_SendComfirmWebCRaceBossMsgControllerIEAAXEPEAVCM_14000E921.cpp" />
    <ClCompile Include="Source\j_SendConfirmCtrlCRaceBossMsgControllerIEAAXEPEAVC_14000A033.cpp" />
    <ClCompile Include="Source\j_SendCPossibleBattleGuildListManagerGUILD_BATTLEQ_14000FF7E.cpp" />
    <ClCompile Include="Source\j_SendCRaceBossMsgControllerQEAA_NEKPEBD0KZ_140004E26.cpp" />
    <ClCompile Include="Source\j_SendCReservedGuildScheduleDayGroupGUILD_BATTLEQE_14000C19E.cpp" />
    <ClCompile Include="Source\j_SendCReservedGuildScheduleMapGroupGUILD_BATTLEQE_140005E16.cpp" />
    <ClCompile Include="Source\j_SendCReservedGuildSchedulePageGUILD_BATTLEQEAAXH_140013FC5.cpp" />
    <ClCompile Include="Source\j_SendCTotalGuildRankInfoQEAAXKHEEKZ_14000CC16.cpp" />
    <ClCompile Include="Source\j_SendCurrentBattleInfoRequestCGuildBattleControll_1400067E4.cpp" />
    <ClCompile Include="Source\j_SendCurrHonorGuildListCHonorGuildQEAAXGEEZ_1400059ED.cpp" />
    <ClCompile Include="Source\j_SendCWeeklyGuildRankInfoQEAAXKHEEKZ_14000DEA4.cpp" />
    <ClCompile Include="Source\j_SendDeleteNotifyPositionMemberCNormalGuildBattle_1400046D3.cpp" />
    <ClCompile Include="Source\j_SendDQS_RoomInsertCGuildRoomInfoAEAAXXZ_140013F6B.cpp" />
    <ClCompile Include="Source\j_SendDQS_RoomUpdateCGuildRoomInfoAEAAXXZ_14000C1CB.cpp" />
    <ClCompile Include="Source\j_SendDrawResultCNormalGuildBattleGUILD_BATTLEIEAA_140001627.cpp" />
    <ClCompile Include="Source\j_SendErrorResultCPossibleBattleGuildListManagerGU_140008DA5.cpp" />
    <ClCompile Include="Source\j_SendExternMsgUs_HFSMSAXPEAV1KPEAXHZ_14000A18C.cpp" />
    <ClCompile Include="Source\j_SendFirstCPossibleBattleGuildListManagerGUILD_BA_1400121E8.cpp" />
    <ClCompile Include="Source\j_SendHolyStoneHPToRaceBossCHolyStoneSystemQEAAXXZ_1400075DB.cpp" />
    <ClCompile Include="Source\j_SendInfoCPossibleBattleGuildListManagerGUILD_BAT_14001170C.cpp" />
    <ClCompile Include="Source\j_SendInfomSenderCRaceBossMsgControllerIEAAXKEZ_140002874.cpp" />
    <ClCompile Include="Source\j_SendInformChangeCHonorGuildQEAAXEGZ_14000728E.cpp" />
    <ClCompile Include="Source\j_SendIsArriveDestroyerCHolyStoneSystemQEAAXEZ_140011B80.cpp" />
    <ClCompile Include="Source\j_SendKillInformCNormalGuildBattleGUILD_BATTLEIEAA_140007FA4.cpp" />
    <ClCompile Include="Source\j_SendListCGuildListQEAAXGEEZ_1400017D0.cpp" />
    <ClCompile Include="Source\j_SendLoopYAKPEAXZ_14000334B.cpp" />
    <ClCompile Include="Source\j_SendMemberPositionCNormalGuildBattleManagerGUILD_1400075BD.cpp" />
    <ClCompile Include="Source\j_SendMsgAccount_UILockRefresh_UpdateCUserDBQEAAXX_14000F033.cpp" />
    <ClCompile Include="Source\j_SendMsgAlterStateCGravityStoneRegenerAEAAXXZ_140003071.cpp" />
    <ClCompile Include="Source\j_SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXP_140003C47.cpp" />
    <ClCompile Include="Source\j_SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXP_140003C88.cpp" />
    <ClCompile Include="Source\j_SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXP_140007982.cpp" />
    <ClCompile Include="Source\j_SendMsgCreateCCircleZoneAEAAXXZ_1400119C8.cpp" />
    <ClCompile Include="Source\j_SendMsgGoalCCircleZoneAEAAXXZ_14000776B.cpp" />
    <ClCompile Include="Source\j_SendMsgRequestResultCRaceBossMsgControllerIEAAXG_140010F64.cpp" />
    <ClCompile Include="Source\j_SendMsgSucceedBuyCashDbWorkerAEAAXGAEBU_param_ca_14000710D.cpp" />
    <ClCompile Include="Source\j_SendMsgToMaster_NoCompleteQuestFromNPCCQuestMgrQ_14000EE76.cpp" />
    <ClCompile Include="Source\j_SendMsgToMaster_NoHaveGiveItemCQuestMgrQEAAXEZ_140003C06.cpp" />
    <ClCompile Include="Source\j_SendMsgToMaster_NoHaveReturnItemCQuestMgrQEAAXEZ_14001320F.cpp" />
    <ClCompile Include="Source\j_SendMsgToMaster_ReturnItemAfterQuestCQuestMgrQEA_140004FE8.cpp" />
    <ClCompile Include="Source\j_SendMsgUs_HFSMSAXPEAV1KKPEAXZ_14000F957.cpp" />
    <ClCompile Include="Source\j_SendMsg_AddEffectCNuclearBombQEAAXXZ_1400027F2.cpp" />
    <ClCompile Include="Source\j_SendMsg_AddJoinApplierCGuildQEAAXPEAU_guild_appl_14000BEF6.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterMemberGradeCGuildQEAAXXZ_140006C30.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterMemberStateCGuildQEAAXXZ_140007CA2.cpp" />
    <ClCompile Include="Source\j_SendMsg_AlterTransparCTrapQEAAX_NZ_14000E930.cpp" />
    <ClCompile Include="Source\j_SendMsg_AnimusActHealInformCAnimusQEAAXKHZ_14000EA61.cpp" />
    <ClCompile Include="Source\j_SendMsg_ApplyGuildBattleResultInformCGuildQEAAXE_140011BF3.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackCGuardTowerQEAAXPEAVCAttackZ_14000D161.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackCHolyKeeperQEAAXXZ_140006AC8.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackCNuclearBombQEAAXHHZ_140008729.cpp" />
    <ClCompile Include="Source\j_SendMsg_AttackCTrapQEAAXPEAVCAttackZ_140005835.cpp" />
    <ClCompile Include="Source\j_SendMsg_Attack_ForceCMonsterQEAAXPEAVCMonsterAtt_140007E64.cpp" />
    <ClCompile Include="Source\j_SendMsg_Attack_GenCAnimusQEAAXPEAVCAttackZ_140001465.cpp" />
    <ClCompile Include="Source\j_SendMsg_Attack_GenCMonsterQEAAXPEAVCMonsterAttac_140005772.cpp" />
    <ClCompile Include="Source\j_SendMsg_BillingInfoCUserDBQEAAXXZ_14000EA1B.cpp" />
    <ClCompile Include="Source\j_SendMsg_BreakStopCGameObjectQEAAXXZ_1400019EC.cpp" />
    <ClCompile Include="Source\j_SendMsg_BuyCashItemICsSendInterfaceSAXGPEBU_para_140004EB2.cpp" />
    <ClCompile Include="Source\j_SendMsg_CashDiscountEventInformICsSendInterfaceS_14000CBFD.cpp" />
    <ClCompile Include="Source\j_SendMsg_CashEventInformICsSendInterfaceSAXGEEPEA_1400011B8.cpp" />
    <ClCompile Include="Source\j_SendMsg_ChangeTaxRateCGuildQEAAXEZ_1400032BA.cpp" />
    <ClCompile Include="Source\j_SendMsg_Change_MonsterRotateCMonsterQEAAXXZ_140007A77.cpp" />
    <ClCompile Include="Source\j_SendMsg_Change_MonsterStateCMonsterQEAAXXZ_14000DC10.cpp" />
    <ClCompile Include="Source\j_SendMsg_ChannelCloseCDarkHoleChannelQEAAXXZ_140008689.cpp" />
    <ClCompile Include="Source\j_SendMsg_ConditionalEventInformICsSendInterfaceSA_140005A83.cpp" />
    <ClCompile Include="Source\j_SendMsg_CouponEnsureCCouponMgrQEAAXGEZ_14000AB2D.cpp" />
    <ClCompile Include="Source\j_SendMsg_CouponErrorCCouponMgrQEAAXGEZ_140004AC5.cpp" />
    <ClCompile Include="Source\j_SendMsg_CouponLendResultCCouponMgrQEAAXGPEAU_db__140001721.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateCAnimusQEAAXXZ_14000FAF1.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateCDarkHoleQEAAXXZ_140008A58.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateCGravityStoneAEAAXXZ_140008BC5.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateCGuardTowerQEAAXXZ_14001067C.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateCHolyKeeperQEAAXXZ_14000B46F.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateCHolyStoneQEAAXXZ_140013C8C.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateCItemBoxQEAAXXZ_1400071E9.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateCMerchantQEAAXXZ_140006F7D.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateCMonsterQEAAXXZ_14001013B.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateCParkingUnitQEAAXXZ_14000CA22.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateCReturnGateIEAAXXZ_140013EE9.cpp" />
    <ClCompile Include="Source\j_SendMsg_CreateCTrapQEAAXXZ_14000E728.cpp" />
    <ClCompile Include="Source\j_SendMsg_DelJoinApplierCGuildQEAAXPEAU_guild_appl_140007CD9.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCAnimusQEAAXXZ_14000F8CB.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCDarkHoleQEAAXXZ_140001163.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCGravityStoneAEAAXXZ_14000D382.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCGuardTowerQEAAXEZ_140005088.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCHolyKeeperQEAAXEZ_1400128F5.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCHolyStoneQEAAXEKZ_140013692.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCItemBoxQEAAXXZ_140004B8D.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCMerchantQEAAXXZ_14000D535.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCMonsterQEAAXEZ_14000E9EE.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCParkingUnitQEAAXEZ_14000482C.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCReturnGateIEAAXXZ_1400020D6.cpp" />
    <ClCompile Include="Source\j_SendMsg_DestroyCTrapQEAAXEZ_140010479.cpp" />
    <ClCompile Include="Source\j_SendMsg_DownPacketCGuildQEAAXEPEAU_guild_member__14000FA42.cpp" />
    <ClCompile Include="Source\j_SendMsg_DropMissileCNuclearBombQEAAXXZ_140001A8C.cpp" />
    <ClCompile Include="Source\j_SendMsg_EconomyDataToWebYAXXZ_14000D1F2.cpp" />
    <ClCompile Include="Source\j_SendMsg_Emotion_PresentationCMonsterQEAAXEGGHZ_14000D4BD.cpp" />
    <ClCompile Include="Source\j_SendMsg_EndBattleCHolyStoneSystemQEAAXEZ_140011900.cpp" />
    <ClCompile Include="Source\j_SendMsg_EnterKeeperCHolyStoneSystemQEAAXHZ_14000BAAF.cpp" />
    <ClCompile Include="Source\j_SendMsg_EnterStoneCHolyStoneSystemQEAAXHZ_14000C6DF.cpp" />
    <ClCompile Include="Source\j_SendMsg_ErrorICsSendInterfaceSAXGHZ_140011BDF.cpp" />
    <ClCompile Include="Source\j_SendMsg_ExitStoneCHolyStoneSystemQEAAXXZ_140012931.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionAutominePersonalUEAAXHZ_14000DA94.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCAnimusUEAAXHZ_140004BEC.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCCircleZoneEEAAXHZ_14000BD84.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCDarkHoleUEAAXHZ_14000B7FD.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCGameObjectUEAAXHZ_140013840.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCGravityStoneRegenerEEAAXHZ_140013386.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCGravityStoneUEAAXHZ_140009C8C.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCGuardTowerUEAAXHZ_140005E70.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCHolyKeeperUEAAXHZ_14000128A.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCHolyStoneUEAAXHZ_1400043FE.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCItemBoxUEAAXHZ_14000DE4F.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCMerchantUEAAXHZ_14000344A.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCMonsterUEAAXHZ_14000217B.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCParkingUnitUEAAXHZ_140011E64.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCReturnGateUEAAXHZ_14000C275.cpp" />
    <ClCompile Include="Source\j_SendMsg_FixPositionCTrapUEAAXHZ_140004EDF.cpp" />
    <ClCompile Include="Source\j_SendMsg_GateDestroyCDarkHoleChannelQEAAXPEAEPEAD_140013B9C.cpp" />
    <ClCompile Include="Source\j_SendMsg_GoodsListICsSendInterfaceSAXGPEBU_param__14000390E.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildBattleProposedCGuildQEAAHPEADZ_14000B3B6.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildBattleRefusedCGuildQEAAXPEADZ_14000837D.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildBattleSuggestResultCGuildQEAAXEPEAD_14000BFFA.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildDisjointInformCGuildQEAAXXZ_140004859.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildInfoUpdateInformCGuildQEAAXXZ_14000D643.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildJoinAcceptInformCGuildQEAAXPEAU_gui_14000E845.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildMemberLogoffCGuildQEAAXKZ_14000D977.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildMemberPosInformCGuildQEAAXKGGZ_14000BE10.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildOutputMoneyFailCGuildQEAAXKZ_1400072ED.cpp" />
    <ClCompile Include="Source\j_SendMsg_GuildRoomRentedCGuildQEAAXEZ_14000E06B.cpp" />
    <ClCompile Include="Source\j_SendMsg_HolyKeeperAttackAbleStateCHolyStoneSyste_1400130A2.cpp" />
    <ClCompile Include="Source\j_SendMsg_HolyKeeperStateChaosCHolyStoneSystemQEAA_14000C6D0.cpp" />
    <ClCompile Include="Source\j_SendMsg_HolyStoneSystemStateCHolyStoneSystemQEAA_14000CB9E.cpp" />
    <ClCompile Include="Source\j_SendMsg_InformAttackCNuclearBombQEAAXXZ_14000ED45.cpp" />
    <ClCompile Include="Source\j_SendMsg_InformDropPosCNuclearBombQEAAXXZ_140001771.cpp" />
    <ClCompile Include="Source\j_SendMsg_Inform_UILockCUserDBQEAAXXZ_140009C50.cpp" />
    <ClCompile Include="Source\j_SendMsg_InPcBangTimeCCouponMgrQEAAXGZ_140003706.cpp" />
    <ClCompile Include="Source\j_SendMsg_IOMoneyCGuildQEAAXKNN_NPEAEZ_140005966.cpp" />
    <ClCompile Include="Source\j_SendMsg_JobCountCDarkHoleChannelQEAAXHHZ_140010AAF.cpp" />
    <ClCompile Include="Source\j_SendMsg_JobPassCDarkHoleChannelQEAAXHZ_140002C52.cpp" />
    <ClCompile Include="Source\j_SendMsg_KickForSailCTransportShipQEAAXHZ_14000D85A.cpp" />
    <ClCompile Include="Source\j_SendMsg_LeaveMemberCGuildQEAAXK_N0Z_1400041C4.cpp" />
    <ClCompile Include="Source\j_SendMsg_LimitedsaleEventInformICsSendInterfaceSA_140011C16.cpp" />
    <ClCompile Include="Source\j_SendMsg_MachineInfoAutoMineMachineQEAAXHZ_1400085D5.cpp" />
    <ClCompile Include="Source\j_SendMsg_ManageGuildCommitteeResultCGuildQEAAX_NP_140008986.cpp" />
    <ClCompile Include="Source\j_SendMsg_MasterDieCNuclearBombQEAAXXZ_140010573.cpp" />
    <ClCompile Include="Source\j_SendMsg_MasterElectPossibleCGuildQEAAX_NZ_140009BB5.cpp" />
    <ClCompile Include="Source\j_SendMsg_MissionPassCDarkHoleChannelQEAAXXZ_140013D9F.cpp" />
    <ClCompile Include="Source\j_SendMsg_MoneySupplyDataToWebCMoneySupplyMgrQEAAX_14001401A.cpp" />
    <ClCompile Include="Source\j_SendMsg_MoveCAnimusQEAAXXZ_14000BA8C.cpp" />
    <ClCompile Include="Source\j_SendMsg_MoveCHolyKeeperQEAAXXZ_140008FDF.cpp" />
    <ClCompile Include="Source\j_SendMsg_MoveCMerchantQEAAXXZ_14000E340.cpp" />
    <ClCompile Include="Source\j_SendMsg_MoveCMonsterQEAAXXZ_140008486.cpp" />
    <ClCompile Include="Source\j_SendMsg_NewMissionCDarkHoleChannelQEAAXXZ_1400056A0.cpp" />
    <ClCompile Include="Source\j_SendMsg_NoticeNextQuestCHolyStoneSystemQEAAXHEZ_140001BB8.cpp" />
    <ClCompile Include="Source\j_SendMsg_NotifyHolyKeeperAttackTimeBeKeepKeeperCH_140001ECE.cpp" />
    <ClCompile Include="Source\j_SendMsg_NuclearFindCNuclearBombQEAAXHEZ_14000696A.cpp" />
    <ClCompile Include="Source\j_SendMsg_OpenPortalByReactCDarkHoleChannelQEAAXHZ_14000B307.cpp" />
    <ClCompile Include="Source\j_SendMsg_OpenPortalByResultCDarkHoleChannelQEAAXH_14000FB5A.cpp" />
    <ClCompile Include="Source\j_SendMsg_PatriarchTaxRateTRC_AutoTradeQEAAXHZ_14000367A.cpp" />
    <ClCompile Include="Source\j_SendMsg_PvpCashInformCPvpCashPointQEAAXHEZ_1400097F0.cpp" />
    <ClCompile Include="Source\j_SendMsg_PvpRankListDataCPvpUserRankingInfoAEAAXG_140002A18.cpp" />
    <ClCompile Include="Source\j_SendMsg_PvpRankListNodataCPvpUserRankingInfoAEAA_1400030A8.cpp" />
    <ClCompile Include="Source\j_SendMsg_QueryAppointResultClassOrderProcessorQEA_1400136C4.cpp" />
    <ClCompile Include="Source\j_SendMsg_QueryPacket_InfoCGuildQEAAXHZ_1400131C9.cpp" />
    <ClCompile Include="Source\j_SendMsg_QuestPassCDarkHoleChannelQEAAXXZ_140008823.cpp" />
    <ClCompile Include="Source\j_SendMsg_RealAddLimTimeCDarkHoleChannelQEAAXHPEAD_14000F8DA.cpp" />
    <ClCompile Include="Source\j_SendMsg_RealFixPositionCGameObjectUEAAX_NZ_1400110C2.cpp" />
    <ClCompile Include="Source\j_SendMsg_RealFixPositionCMerchantUEAAX_NZ_14000F222.cpp" />
    <ClCompile Include="Source\j_SendMsg_RealMovePointCAnimusUEAAXHZ_14001230F.cpp" />
    <ClCompile Include="Source\j_SendMsg_RealMovePointCGameObjectUEAAXHZ_14000E859.cpp" />
    <ClCompile Include="Source\j_SendMsg_RealMovePointCHolyKeeperUEAAXHZ_14000A7CC.cpp" />
    <ClCompile Include="Source\j_SendMsg_RealMovePointCMerchantUEAAXHZ_140013912.cpp" />
    <ClCompile Include="Source\j_SendMsg_RealMovePointCMonsterUEAAXHZ_1400138A9.cpp" />
    <ClCompile Include="Source\j_SendMsg_RealMsgInformCDarkHoleChannelQEAAXPEADZ_140012B98.cpp" />
    <ClCompile Include="Source\j_SendMsg_RecoverResultCPvpCashPointQEAAXHEHZ_14000AC27.cpp" />
    <ClCompile Include="Source\j_SendMsg_RemainBufUseTimeCExtPotionBufQEAAX_NGHHH_1400114B9.cpp" />
    <ClCompile Include="Source\j_SendMsg_RemainCouponInformCCouponMgrQEAAXGEZ_14000FFC9.cpp" />
    <ClCompile Include="Source\j_SendMsg_ResultCNuclearBombMgrQEAAXHEZ_140010069.cpp" />
    <ClCompile Include="Source\j_SendMsg_ResultCNuclearBombQEAAXHEZ_14000CD3D.cpp" />
    <ClCompile Include="Source\j_SendMsg_ResultCodeAutoMineMachineMngQEBAXHEEZ_1400138D6.cpp" />
    <ClCompile Include="Source\j_SendMsg_ResultCodeAutoMineMachineQEBAXHEEZ_14000B85C.cpp" />
    <ClCompile Include="Source\j_SendMsg_ResultCodePatriarchElectProcessorQEAAXKE_14000F114.cpp" />
    <ClCompile Include="Source\j_SendMsg_RoomTimeOverCGuildRoomInfoAEAAXXZ_14000EC0A.cpp" />
    <ClCompile Include="Source\j_SendMsg_SetHPInformCGameObjectUEAAXXZ_140005542.cpp" />
    <ClCompile Include="Source\j_SendMsg_StartBattleCHolyStoneSystemQEAAXXZ_14000C77A.cpp" />
    <ClCompile Include="Source\j_SendMsg_StartBillingCBillingIEAAXXZ_1400015EB.cpp" />
    <ClCompile Include="Source\j_SendMsg_StartedVoteInformCVoteSystemQEAAXHK_NZ_140003A44.cpp" />
    <ClCompile Include="Source\j_SendMsg_StateChangeCDarkHoleQEAAXXZ_140002158.cpp" />
    <ClCompile Include="Source\j_SendMsg_StateChangeCItemBoxQEAAXXZ_14000C5AE.cpp" />
    <ClCompile Include="Source\j_SendMsg_StoneAlterOperCHolyStoneQEAAXXZ_140005F6A.cpp" />
    <ClCompile Include="Source\j_SendMsg_StunInformCGameObjectUEAAXXZ_1400012FD.cpp" />
    <ClCompile Include="Source\j_SendMsg_TalikListCPvpCashPointQEAAXHZ_140007F04.cpp" />
    <ClCompile Include="Source\j_sendmsg_taxrateTRC_AutoTradeQEAAXHEZ_14001195F.cpp" />
    <ClCompile Include="Source\j_SendMsg_TicketCheckCTransportShipQEAAXH_NGZ_14000AA65.cpp" />
    <ClCompile Include="Source\j_SendMsg_TimeOutCDarkHoleChannelQEAAXXZ_14000B834.cpp" />
    <ClCompile Include="Source\j_SendMsg_TowerCompleteInformCGuardTowerQEAAXXZ_1400015C3.cpp" />
    <ClCompile Include="Source\j_SendMsg_TransportShipStateCTransportShipQEAAXHZ_14000C757.cpp" />
    <ClCompile Include="Source\j_SendMsg_TransShipTicketNumInformCMerchantQEAAXHZ_140008F1C.cpp" />
    <ClCompile Include="Source\j_SendMsg_TrapCompleteInformCTrapQEAAXXZ_140005F38.cpp" />
    <ClCompile Include="Source\j_SendMsg_VoteCancelInformCGuildQEAAXXZ_14000E6E7.cpp" />
    <ClCompile Include="Source\j_SendMsg_VoteCompleteCGuildQEAAX_NZ_14000E688.cpp" />
    <ClCompile Include="Source\j_SendMsg_VoteProcessInform_ContinueCGuildQEAAXPEA_14000978C.cpp" />
    <ClCompile Include="Source\j_SendMsg_VoteProcessInform_StartCGuildQEAAXXZ_140006410.cpp" />
    <ClCompile Include="Source\j_SendMsg_VoteStateCGuildQEAAXXZ_14000E1E2.cpp" />
    <ClCompile Include="Source\j_SendMsg_VoteStopCGuildQEAAXKZ_140011275.cpp" />
    <ClCompile Include="Source\j_SendMsg_WaitKeeperCHolyStoneSystemQEAAXHEZ_14000E90D.cpp" />
    <ClCompile Include="Source\j_SendMsg_WaitStoneCHolyStoneSystemQEAAXHZ_14000D378.cpp" />
    <ClCompile Include="Source\j_SendMsg_ZoneAliveCheckCBillingManagerQEAAXKZ_14000D517.cpp" />
    <ClCompile Include="Source\j_SendMsg_ZoneAliveCheckCBillingQEAAXKZ_140008544.cpp" />
    <ClCompile Include="Source\j_SendNextHonorGuildListCHonorGuildQEAAXGEZ_140008D4B.cpp" />
    <ClCompile Include="Source\j_SendNotifyCloseItemCUnmannedTraderUserInfoQEAAXG_14000DA6C.cpp" />
    <ClCompile Include="Source\j_SendNotifyHolyStoneDestroyedToRaceBossCHolyStone_14000D3FF.cpp" />
    <ClCompile Include="Source\j_SendPossibleBattleGuildListCGuildBattleControlle_140006A0F.cpp" />
    <ClCompile Include="Source\j_SendPossibleBattleGuildListFirstCGuildBattleCont_1400079AA.cpp" />
    <ClCompile Include="Source\j_SendRaceBossMsgFromWebRequestCNetworkEXAEAA_NHPE_1400018BB.cpp" />
    <ClCompile Include="Source\j_SendRankListCGuildBattleControllerQEAAXHEKIEKZ_14000F41B.cpp" />
    <ClCompile Include="Source\j_SendRegenBallCNormalGuildBattleGuildGUILD_BATTLE_1400072E8.cpp" />
    <ClCompile Include="Source\j_SendRegistItemErrorResultCUnmannedTraderUserInfo_140010767.cpp" />
    <ClCompile Include="Source\j_SendRegistItemSuccessResultCUnmannedTraderUserIn_140012F49.cpp" />
    <ClCompile Include="Source\j_SendRequestWebCRaceBossMsgControllerIEAAXEPEAVCM_14000A8EE.cpp" />
    <ClCompile Include="Source\j_SendReservedScheduleListCGuildBattleControllerQE_1400105B9.cpp" />
    <ClCompile Include="Source\j_SendSearchErrorResultCUnmannedTraderUserInfoQEAA_14000F263.cpp" />
    <ClCompile Include="Source\j_SendSearchResultCUnmannedTraderUserInfoQEAAXGPEA_14000F6E1.cpp" />
    <ClCompile Include="Source\j_SendSellInfomCUnmannedTraderUserInfoQEAAXGGKKKZ_140007892.cpp" />
    <ClCompile Include="Source\j_SendSMS_CompleteQuestCHolyStoneSystemQEAAXEPEADH_14000B285.cpp" />
    <ClCompile Include="Source\j_SendSMS_MineTimeExtendCHolyStoneSystemQEAAXHZ_1400083C3.cpp" />
    <ClCompile Include="Source\j_SendStartNotifyCommitteeMemberPositionCNormalGui_140005DAD.cpp" />
    <ClCompile Include="Source\j_SendTaxRateCUnmannedTraderTaxRateManagerQEAAXHEZ_1400092B9.cpp" />
    <ClCompile Include="Source\j_SendTaxRatePatriarchCUnmannedTraderTaxRateManage_1400135CA.cpp" />
    <ClCompile Include="Source\j_SendThreadCNetProcessCAXPEAXZ_140001AE6.cpp" />
    <ClCompile Include="Source\j_SendWebAddScheduleInfoCNormalGuildBattleGUILD_BA_140006032.cpp" />
    <ClCompile Include="Source\j_SendWebBattleEndInfoCNormalGuildBattleGUILD_BATT_140005FF6.cpp" />
    <ClCompile Include="Source\j_SendWebBattleStartInfoCNormalGuildBattleGUILD_BA_14000895E.cpp" />
    <ClCompile Include="Source\j_SendWebRaceBossSMSCMainThreadQEAAXPEAU_DB_QRY_SY_1400124C7.cpp" />
    <ClCompile Include="Source\j_SendWebRaceBossSMSErrorResultCRaceBossMsgControl_14000C3AB.cpp" />
    <ClCompile Include="Source\j_SendWinLoseResultCNormalGuildBattleGUILD_BATTLEI_1400102CB.cpp" />
    <ClCompile Include="Source\j_send_attackedAutominePersonalQEAAXXZ_14000FF88.cpp" />
    <ClCompile Include="Source\j_send_changed_packetAutominePersonalQEAAXHZ_140007AB3.cpp" />
    <ClCompile Include="Source\j_send_current_stateAutominePersonalQEAAXXZ_1400031E8.cpp" />
    <ClCompile Include="Source\j_send_ecodeAutominePersonalMgrQEAAXHEZ_14000259F.cpp" />
    <ClCompile Include="Source\j_send_ecodeAutominePersonalQEAAXEZ_140009552.cpp" />
    <ClCompile Include="Source\j_send_installedAutominePersonalQEAAXXZ_1400070C2.cpp" />
    <ClCompile Include="Source\j_SetFtpConnectionWheatyExceptionReportQEAAXPEADI0_14000BA19.cpp" />
    <ClCompile Include="Source\j_SetMsg_messageQEAAXKKKKZ_140004C0A.cpp" />
    <ClCompile Include="Source\j_SetPassablePacketCNetworkEXQEAAXKEEZ_140013F98.cpp" />
    <ClCompile Include="Source\j_SetRaceWarRecvrCPvpCashPointQEAAX_NZ_140002EB4.cpp" />
    <ClCompile Include="Source\j_SetReconnectFailExitFlagCRFNewDatabaseQEAAX_NZ_14000588F.cpp" />
    <ClCompile Include="Source\j_SetSocketCNetSocketQEAA_NPEAU_SOCK_TYPE_PARAMPEA_140001C8F.cpp" />
    <ClCompile Include="Source\j_SFContDelMessageCGameObjectUEAAXEE_N0Z_140002BAD.cpp" />
    <ClCompile Include="Source\j_SFContUpdateTimeMessageCGameObjectUEAAXEEHZ_14000CAA9.cpp" />
    <ClCompile Include="Source\j_ShouldPropagateMessageEndFilterCryptoPPMEBA_NXZ_14001122F.cpp" />
    <ClCompile Include="Source\j_ShouldPropagateMessageSeriesEndFilterCryptoPPMEB_14000C45A.cpp" />
    <ClCompile Include="Source\j_size_announ_message_receipt_udpQEAAHXZ_140010B2C.cpp" />
    <ClCompile Include="Source\j_size_apex_send_ipQEAAHXZ_14000E0AC.cpp" />
    <ClCompile Include="Source\j_size_apex_send_logoutQEAAHXZ_140012224.cpp" />
    <ClCompile Include="Source\j_size_apex_send_transQEAAHXZ_1400059D4.cpp" />
    <ClCompile Include="Source\j_size_chat_message_receipt_udpQEAAHXZ_14000EB1A.cpp" />
    <ClCompile Include="Source\j_size_chat_steal_message_gm_zoclQEAAHXZ_140010E9C.cpp" />
    <ClCompile Include="Source\j_size_connection_status_result_zoctQEAAHXZ_14000BF37.cpp" />
    <ClCompile Include="Source\j_size_qry_case_post_sendQEAAHXZ_140013F43.cpp" />
    <ClCompile Include="Source\j_size_qry_case_sendwebracebosssmsQEAAHXZ_14000ED90.cpp" />
    <ClCompile Include="Source\j_size_qry_case_update_data_for_post_sendQEAAHXZ_14000F09C.cpp" />
    <ClCompile Include="Source\j_size_talik_recvr_listQEAAHXZ_140005371.cpp" />
    <ClCompile Include="Source\j_tutorial_process_report_recvCMgrAccountLobbyHist_14000E980.cpp" />
    <ClCompile Include="Source\j_unchecked_fill_nPEAPEAURECV_DATA_KPEAU1stdextYAX_1400020A9.cpp" />
    <ClCompile Include="Source\j_unchecked_uninitialized_copyPEAPEAURECV_DATAPEAP_14000B280.cpp" />
    <ClCompile Include="Source\j_unchecked_uninitialized_fill_nPEAPEAURECV_DATA_K_14001234B.cpp" />
    <ClCompile Include="Source\j_UpdatePacketClassOrderProcessorQEAAXEEZ_1400092C8.cpp" />
    <ClCompile Include="Source\j_UpdateSendCRaceBossMsgControllerIEAAXXZ_14000D20B.cpp" />
    <ClCompile Include="Source\j_Update_PostStorageSendToRecverCRFWorldDatabaseQE_14000B866.cpp" />
    <ClCompile Include="Source\j_Update_RaceWarRecvrCPvpOrderViewQEAAX_NZ_140003A62.cpp" />
    <ClCompile Include="Source\j_Y_Deque_const_iteratorURECV_DATAVallocatorURECV__140004CC8.cpp" />
    <ClCompile Include="Source\j_Y_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000138E.cpp" />
    <ClCompile Include="Source\j__AllocatePEAURECV_DATAstdYAPEAPEAURECV_DATA_KPEA_140007518.cpp" />
    <ClCompile Include="Source\j__AllocateURECV_DATAstdYAPEAURECV_DATA_KPEAU1Z_140006DB6.cpp" />
    <ClCompile Include="Source\j__buybygold_buy_single_item_setsenddataCashItemRe_14000ADAD.cpp" />
    <ClCompile Include="Source\j__CheckSendCNetProcessAEAAXGZ_14000B64F.cpp" />
    <ClCompile Include="Source\j__CkeckRecvBreakCNetProcessAEAAXXZ_14000791E.cpp" />
    <ClCompile Include="Source\j__ConstructURECV_DATAU1stdYAXPEAURECV_DATAAEBU1Z_140005E9D.cpp" />
    <ClCompile Include="Source\j__Copy_backward_optV_Deque_iteratorURECV_DATAVall_1400010F5.cpp" />
    <ClCompile Include="Source\j__Copy_optV_Deque_iteratorURECV_DATAVallocatorURE_140008198.cpp" />
    <ClCompile Include="Source\j__db_Update_Data_For_Post_SendCMainThreadAEAAEPEA_14000E269.cpp" />
    <ClCompile Include="Source\j__DestroyPEAURECV_DATAstdYAXPEAPEAURECV_DATAZ_14000105A.cpp" />
    <ClCompile Include="Source\j__DestroyURECV_DATAstdYAXPEAURECV_DATAZ_140001A46.cpp" />
    <ClCompile Include="Source\j__Destroy_rangePEAURECV_DATAVallocatorPEAURECV_DA_140002F1D.cpp" />
    <ClCompile Include="Source\j__Destroy_rangePEAURECV_DATAVallocatorPEAURECV_DA_14000D3F5.cpp" />
    <ClCompile Include="Source\j__ECNetSocketUEAAPEAXIZ_0_140009C41.cpp" />
    <ClCompile Include="Source\j__ECNetSocketUEAAPEAXIZ_1400011C2.cpp" />
    <ClCompile Include="Source\j__E_messageQEAAPEAXIZ_1400106A4.cpp" />
    <ClCompile Include="Source\j__E_socketQEAAPEAXIZ_140001528.cpp" />
    <ClCompile Include="Source\j__Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV__14000AC77.cpp" />
    <ClCompile Include="Source\j__Fill_nPEAPEAURECV_DATA_KPEAU1Urandom_access_ite_14000FDB2.cpp" />
    <ClCompile Include="Source\j__GrowmapdequeURECV_DATAVallocatorURECV_DATAstdst_14000B442.cpp" />
    <ClCompile Include="Source\j__InternalPacketProcessCNetProcessAEAA_NKPEAU_MSG_1400138F4.cpp" />
    <ClCompile Include="Source\j__Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_it_140006028.cpp" />
    <ClCompile Include="Source\j__Iter_randomV_Deque_iteratorURECV_DATAVallocator_14000E3EA.cpp" />
    <ClCompile Include="Source\j__PopRecvMsgCNetProcessAEAAXGZ_140009FE3.cpp" />
    <ClCompile Include="Source\j__Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_p_140012D64.cpp" />
    <ClCompile Include="Source\j__Ptr_catV_Deque_iteratorURECV_DATAVallocatorUREC_140011676.cpp" />
    <ClCompile Include="Source\j__SendListCandidateRegisterAEAAHGEZ_14000E20F.cpp" />
    <ClCompile Include="Source\j__SendLoopCNetProcessAEAAXKZ_140006FE1.cpp" />
    <ClCompile Include="Source\j__SendSpeedHackCheckMsgCNetProcessAEAAXHZ_140003F35.cpp" />
    <ClCompile Include="Source\j__SendVotePaperAllVoterAEAAXXZ_14000E156.cpp" />
    <ClCompile Include="Source\j__SendVoteScoreAllVoterAEAAXEZ_140003D82.cpp" />
    <ClCompile Include="Source\j__TidydequeURECV_DATAVallocatorURECV_DATAstdstdIE_1400039EA.cpp" />
    <ClCompile Include="Source\j__Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPE_140011B62.cpp" />
    <ClCompile Include="Source\j__Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorP_14000E836.cpp" />
    <ClCompile Include="Source\j__UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_140011662.cpp" />
    <ClCompile Include="Source\j__XlendequeURECV_DATAVallocatorURECV_DATAstdstdKA_14000B9F6.cpp" />
    <ClCompile Include="Source\LoadSendMsgCNetProcessQEAAHKGPEADGZ_140479680.cpp" />
    <ClCompile Include="Source\LoadSendMsgCNetProcessQEAAHKPEAEPEADGZ_140478F90.cpp" />
    <ClCompile Include="Source\lobby_disconnectCMgrAccountLobbyHistoryQEAAXPEAU_q_140234BB0.cpp" />
    <ClCompile Include="Source\LoopSubProcSendInformCHonorGuildQEAAXEZ_14025FBE0.cpp" />
    <ClCompile Include="Source\LoopSubProcSendInformCOreAmountMgrQEAAXXZ_1403F9A90.cpp" />
    <ClCompile Include="Source\MakeBuddyPacketCGuildQEAAXXZ_1402551E0.cpp" />
    <ClCompile Include="Source\MakeConnectionThreadCEnglandBillingMgrQEAA_NXZ_1403197B0.cpp" />
    <ClCompile Include="Source\MakeDownApplierPacketCGuildQEAAXXZ_140254AC0.cpp" />
    <ClCompile Include="Source\MakeDownMemberPacketCGuildQEAAXXZ_140254620.cpp" />
    <ClCompile Include="Source\MakeMoneyIOPacketCGuildQEAAXXZ_140254F20.cpp" />
    <ClCompile Include="Source\MakeQueryInfoPacketCGuildQEAAXXZ_140254DB0.cpp" />
    <ClCompile Include="Source\make_heapV_Deque_iteratorUMessageRangeMeterFilterC_140603360.cpp" />
    <ClCompile Include="Source\make_minepacketAutominePersonalAEAAXGGEGKZ_1402DC130.cpp" />
    <ClCompile Include="Source\MaxRecoverableLengthPK_SignatureMessageEncodingMet_14058F830.cpp" />
    <ClCompile Include="Source\MaxRetrievableMessageQueueCryptoPPUEBA_KXZ_140655160.cpp" />
    <ClCompile Include="Source\max_sizeallocatorUMessageRangeMeterFilterCryptoPPs_140600DD0.cpp" />
    <ClCompile Include="Source\max_sizeallocatorURECV_DATAstdQEBA_KXZ_14031ADD0.cpp" />
    <ClCompile Include="Source\max_sizedequeUMessageRangeMeterFilterCryptoPPVallo_140600AB0.cpp" />
    <ClCompile Include="Source\max_sizedequeURECV_DATAVallocatorURECV_DATAstdstdQ_14031ABF0.cpp" />
    <ClCompile Include="Source\MessageBoxACWndQEAAHPEBD0IZ_0_1404DBEF8.cpp" />
    <ClCompile Include="Source\MessageBoxA_0_1404DEF9A.cpp" />
    <ClCompile Include="Source\MessageEndBERGeneralDecoderCryptoPPQEAAXXZ_14054D430.cpp" />
    <ClCompile Include="Source\MessageEndBufferedTransformationCryptoPPQEAA_NH_NZ_14054F440.cpp" />
    <ClCompile Include="Source\MessageEndDERGeneralEncoderCryptoPPQEAAXXZ_14054D7F0.cpp" />
    <ClCompile Include="Source\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056BED0.cpp" />
    <ClCompile Include="Source\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056BF80.cpp" />
    <ClCompile Include="Source\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056C040.cpp" />
    <ClCompile Include="Source\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056C0B0.cpp" />
    <ClCompile Include="Source\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056C310.cpp" />
    <ClCompile Include="Source\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056C3C0.cpp" />
    <ClCompile Include="Source\MessageRepresentativeBitLengthTF_SignatureSchemeBa_140624300.cpp" />
    <ClCompile Include="Source\MessageRepresentativeBitLengthTF_SignatureSchemeBa_1406243D0.cpp" />
    <ClCompile Include="Source\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056BEB0.cpp" />
    <ClCompile Include="Source\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056BF60.cpp" />
    <ClCompile Include="Source\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056C020.cpp" />
    <ClCompile Include="Source\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056C090.cpp" />
    <ClCompile Include="Source\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056C2F0.cpp" />
    <ClCompile Include="Source\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056C3A0.cpp" />
    <ClCompile Include="Source\MessageRepresentativeLengthTF_SignatureSchemeBaseV_1406242E0.cpp" />
    <ClCompile Include="Source\MessageRepresentativeLengthTF_SignatureSchemeBaseV_1406243B0.cpp" />
    <ClCompile Include="Source\MessageSeriesEndBufferedTransformationCryptoPPUEAA_1405F48F0.cpp" />
    <ClCompile Include="Source\MessageSeriesEndFilterCryptoPPUEAA_NH_NZ_1405F8FE0.cpp" />
    <ClCompile Include="Source\MessageSeriesEndOutputProxyCryptoPPUEAA_NH_NZ_1405FEF50.cpp" />
    <ClCompile Include="Source\MinRepresentativeBitLengthPK_SignatureMessageEncod_14058F810.cpp" />
    <ClCompile Include="Source\MMessageRangeMeterFilterCryptoPPQEBA_NAEBU012Z_140603300.cpp" />
    <ClCompile Include="Source\MyMessageBoxYAXPEAD0ZZ_14043B010.cpp" />
    <ClCompile Include="Source\M_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603B00.cpp" />
    <ClCompile Include="Source\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_140561F60.cpp" />
    <ClCompile Include="Source\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_1405643C0.cpp" />
    <ClCompile Include="Source\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_140566060.cpp" />
    <ClCompile Include="Source\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_1406340B0.cpp" />
    <ClCompile Include="Source\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_140634580.cpp" />
    <ClCompile Include="Source\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140563340.cpp" />
    <ClCompile Include="Source\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140565100.cpp" />
    <ClCompile Include="Source\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140566DA0.cpp" />
    <ClCompile Include="Source\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140634350.cpp" />
    <ClCompile Include="Source\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140634820.cpp" />
    <ClCompile Include="Source\NotifyAllProcessEndCNormalGuildBattleGUILD_BATTLEQ_1403E57A0.cpp" />
    <ClCompile Include="Source\NumberOfMessagesBufferedTransformationCryptoPPUEBA_1405F5190.cpp" />
    <ClCompile Include="Source\NumberOfMessageSeriesBufferedTransformationCryptoP_14044CF70.cpp" />
    <ClCompile Include="Source\NumberOfMessageSeriesMessageQueueCryptoPPUEBAIXZ_140655260.cpp" />
    <ClCompile Include="Source\NumberOfMessagesInThisSeriesBufferedTransformation_14044CF20.cpp" />
    <ClCompile Include="Source\NumberOfMessagesInThisSeriesMessageQueueCryptoPPUE_140655230.cpp" />
    <ClCompile Include="Source\NumberOfMessagesMessageQueueCryptoPPUEBAIXZ_140655200.cpp" />
    <ClCompile Include="Source\NumberOfMessagesStoreCryptoPPUEBAIXZ_140453B40.cpp" />
    <ClCompile Include="Source\OnBUTTONWorldConnectCGameServerViewQEAAXXZ_14002B4E0.cpp" />
    <ClCompile Include="Source\OnConnectHACKSHEILD_PARAM_ANTICPUEAAXHZ_1404179D0.cpp" />
    <ClCompile Include="Source\OnDisConnectHACKSHEILD_PARAM_ANTICPUEAAXXZ_140417A10.cpp" />
    <ClCompile Include="Source\OnLoopCNetSocketQEAAXXZ_14047E2B0.cpp" />
    <ClCompile Include="Source\OutputMessageSeriesEndFilterCryptoPPIEAA_NHH_NAEBV_1405F9330.cpp" />
    <ClCompile Include="Source\PaddedBlockBitLengthTF_CryptoSystemBaseVPK_Decrypt_140624160.cpp" />
    <ClCompile Include="Source\PaddedBlockBitLengthTF_CryptoSystemBaseVPK_Encrypt_140624230.cpp" />
    <ClCompile Include="Source\PaddedBlockByteLengthTF_CryptoSystemBaseVPK_Decryp_140624140.cpp" />
    <ClCompile Include="Source\PaddedBlockByteLengthTF_CryptoSystemBaseVPK_Encryp_140624210.cpp" />
    <ClCompile Include="Source\pc_CashDBInfoRecvResultCMainThreadQEAAXPEAD000KZ_1401F5200.cpp" />
    <ClCompile Include="Source\PeekMessageA_0_140676E8E.cpp" />
    <ClCompile Include="Source\PopEmptyBufCMsgDataAEAAPEAU_messageXZ_140438380.cpp" />
    <ClCompile Include="Source\PopMsgCMsgDataAEAAPEAU_messageXZ_140438220.cpp" />
    <ClCompile Include="Source\pop_backdequeUMessageRangeMeterFilterCryptoPPVallo_140600AD0.cpp" />
    <ClCompile Include="Source\pop_backdequeURECV_DATAVallocatorURECV_DATAstdstdQ_14031E850.cpp" />
    <ClCompile Include="Source\pop_frontdequeUMessageRangeMeterFilterCryptoPPVall_1405FFF80.cpp" />
    <ClCompile Include="Source\pop_frontdequeURECV_DATAVallocatorURECV_DATAstdstd_14031E6D0.cpp" />
    <ClCompile Include="Source\pop_heapV_Deque_iteratorUMessageRangeMeterFilterCr_140605360.cpp" />
    <ClCompile Include="Source\PostQuitMessage_0_140676EAC.cpp" />
    <ClCompile Include="Source\PostSendCPostSystemManagerQEAAEPEADZ_140325EF0.cpp" />
    <ClCompile Include="Source\PostSendRequestCNetworkEXAEAA_NHPEADZ_1401CE6B0.cpp" />
    <ClCompile Include="Source\post_senditemCMgrAvatorItemHistoryQEAAXPEADPEAU_db_14023E200.cpp" />
    <ClCompile Include="Source\PotionSocketDivisionRequestCNetworkEXAEAA_NHPEADZ_1401D6F90.cpp" />
    <ClCompile Include="Source\PotionSocketSeparationRequestCNetworkEXAEAA_NHPEAD_1401D6E90.cpp" />
    <ClCompile Include="Source\PreTranslateMessageCDialogUEAAHPEAUtagMSGZ_0_1404DBD00.cpp" />
    <ClCompile Include="Source\PreTranslateMessageCFormViewMEAAHPEAUtagMSGZ_0_1404DC14A.cpp" />
    <ClCompile Include="Source\PreTranslateMessageCFrameWndUEAAHPEAUtagMSGZ_0_1404DBE02.cpp" />
    <ClCompile Include="Source\PreTranslateMessageCPropertyPageMEAAHPEAUtagMSGZ_0_1404DC318.cpp" />
    <ClCompile Include="Source\PreTranslateMessageCPropertySheetUEAAHPEAUtagMSGZ__1404DC2BE.cpp" />
    <ClCompile Include="Source\PreTranslateMessageCWinThreadUEAAHPEAUtagMSGZ_0_1404DBF58.cpp" />
    <ClCompile Include="Source\ProcessMessageCMsgDataEEAAXPEAU_messageZ_1404387D0.cpp" />
    <ClCompile Include="Source\ProcessMessageCMsgProcessEEAAXPEAU_messageZ_1401BFFD0.cpp" />
    <ClCompile Include="Source\ProcessMessageFilterCWinThreadUEAAHHPEAUtagMSGZ_0_1404DBF7C.cpp" />
    <ClCompile Include="Source\ProcessRecoverableMessagePK_SignatureMessageEncodi_14058F910.cpp" />
    <ClCompile Include="Source\ProcessSemisignaturePK_SignatureMessageEncodingMet_14058F8F0.cpp" />
    <ClCompile Include="Source\PumpMessageCWinThreadUEAAHXZ_0_1404DBF5E.cpp" />
    <ClCompile Include="Source\PumpMessages2SourceTemplateVFileStoreCryptoPPCrypt_140454160.cpp" />
    <ClCompile Include="Source\PumpMessages2SourceTemplateVStringStoreCryptoPPCry_14057E280.cpp" />
    <ClCompile Include="Source\PushAnsyncConnectCNetProcessQEAA_NKPEAUsockaddr_in_140479890.cpp" />
    <ClCompile Include="Source\pushdata_qry_case_post_sendQEAA_NKEKKPEAD000U_INVE_140328330.cpp" />
    <ClCompile Include="Source\PushEmptyBufCMsgDataAEAAXPEAU_messageZ_1404382E0.cpp" />
    <ClCompile Include="Source\PushIPCheckListCNetSocketQEAA_NKZ_14047ED20.cpp" />
    <ClCompile Include="Source\PushMsgCMsgDataAEAAXPEAU_messageZ_140438180.cpp" />
    <ClCompile Include="Source\push_backdequeUMessageRangeMeterFilterCryptoPPVall_140600050.cpp" />
    <ClCompile Include="Source\push_frontdequeURECV_DATAVallocatorURECV_DATAstdst_14031A500.cpp" />
    <ClCompile Include="Source\Put2MessageQueueCryptoPPUEAA_KPEBE_KH_NZ_140655040.cpp" />
    <ClCompile Include="Source\PutMessageNameCryptoPPYAPEBDXZ_1405FF5C0.cpp" />
    <ClCompile Include="Source\PutMessageYAXPEAD0Z_140509BF0.cpp" />
    <ClCompile Include="Source\ReConnectDataBaseCRFNewDatabaseQEAA_NXZ_140486B00.cpp" />
    <ClCompile Include="Source\RecoverablePartFirstPK_SignatureMessageEncodingMet_14058F850.cpp" />
    <ClCompile Include="Source\RecoverAndRestartDL_VerifierBaseUEC2NPointCryptoPP_1405674D0.cpp" />
    <ClCompile Include="Source\RecoverAndRestartDL_VerifierBaseUECPPointCryptoPPC_140565830.cpp" />
    <ClCompile Include="Source\RecoverAndRestartDL_VerifierBaseVIntegerCryptoPPCr_140563A90.cpp" />
    <ClCompile Include="Source\RecoverAndRestartTF_VerifierBaseCryptoPPUEBAAUDeco_1406234C0.cpp" />
    <ClCompile Include="Source\RecoverMessageFromRepresentativePK_SignatureMessag_14058F970.cpp" />
    <ClCompile Include="Source\RecoverMessageFromSemisignaturePK_SignatureMessage_14058FA20.cpp" />
    <ClCompile Include="Source\RecoverMessagePK_VerifierCryptoPPUEBAAUDecodingRes_1405F6430.cpp" />
    <ClCompile Include="Source\RecoverPK_VerifierCryptoPPUEBAAUDecodingResult2PEA_1405F6390.cpp" />
    <ClCompile Include="Source\RecvClientLineCHackShieldExSystemUEAA_NHPEAU_MSG_H_1404172C0.cpp" />
    <ClCompile Include="Source\RecvCNetSocketQEAA_NKPEADHPEAHZ_14047E9C0.cpp" />
    <ClCompile Include="Source\RecvGameGuardDataCNationSettingManagerQEAA_NHPEAU__140229370.cpp" />
    <ClCompile Include="Source\RecvThreadCNetProcessCAXPEAXZ_140478340.cpp" />
    <ClCompile Include="Source\recv_0_1404DBA5E.cpp" />
    <ClCompile Include="Source\Recv_ApexInformCChiNetworkEXQEAAXKKPEADZ_1404103A0.cpp" />
    <ClCompile Include="Source\Recv_ApexKillCChiNetworkEXQEAAXKKPEADZ_140410460.cpp" />
    <ClCompile Include="Source\RefSingletonVDL_SignatureMessageEncodingMethod_DSA_140589FA0.cpp" />
    <ClCompile Include="Source\RefSingletonVDL_SignatureMessageEncodingMethod_NRC_14063AB10.cpp" />
    <ClCompile Include="Source\releaseauto_ptrVPK_MessageAccumulatorBaseCryptoPPs_14056C6D0.cpp" />
    <ClCompile Include="Source\ReleaseCNetSocketQEAAXXZ_14047E140.cpp" />
    <ClCompile Include="Source\releasemember_ptrVPK_MessageAccumulatorCryptoPPCry_140600260.cpp" />
    <ClCompile Include="Source\resetmember_ptrVPK_MessageAccumulatorCryptoPPCrypt_140600EE0.cpp" />
    <ClCompile Include="Source\RestartMessageAccumulatorDL_SignerBaseUEC2NPointCr_14056C2E0.cpp" />
    <ClCompile Include="Source\RestartMessageAccumulatorDL_SignerBaseUECPPointCry_14056C010.cpp" />
    <ClCompile Include="Source\RestartMessageAccumulatorDL_SignerBaseVIntegerCryp_14056BEA0.cpp" />
    <ClCompile Include="Source\RNewObjectVDL_SignatureMessageEncodingMethod_DSACr_14058CE00.cpp" />
    <ClCompile Include="Source\RNewObjectVDL_SignatureMessageEncodingMethod_NRCry_14063BAA0.cpp" />
    <ClCompile Include="Source\Select_PostRecvSerialFromNameCRFWorldDatabaseQEAAE_1404B1FE0.cpp" />
    <ClCompile Include="Source\Select_PostRecvStorageCheckCRFWorldDatabaseQEAAHKZ_1404B23E0.cpp" />
    <ClCompile Include="Source\SendBuyErrorResultCUnmannedTraderUserInfoQEAAXGEZ_140357FE0.cpp" />
    <ClCompile Include="Source\SendCancelRegistErrorResultCUnmannedTraderUserInfo_140357EA0.cpp" />
    <ClCompile Include="Source\SendCancelRegistSuccessResultCUnmannedTraderUserIn_140357F30.cpp" />
    <ClCompile Include="Source\SendCancelWebCRaceBossMsgControllerIEAAXEPEAVCMsgR_1402A17B0.cpp" />
    <ClCompile Include="Source\SendCancleInfomManagerCRaceBossMsgControllerIEAAXG_1402A16C0.cpp" />
    <ClCompile Include="Source\SendCancleInfomSenderCRaceBossMsgControllerIEAAXKZ_1402A1610.cpp" />
    <ClCompile Include="Source\SendCashDBDSNRequestCNationSettingDataGBUEAAXXZ_14022C0F0.cpp" />
    <ClCompile Include="Source\SendCashDBDSNRequestCNationSettingDataKRUEAAXXZ_14022B4C0.cpp" />
    <ClCompile Include="Source\SendCashDBDSNRequestCNationSettingDataNULLUEAAXXZ_1402130F0.cpp" />
    <ClCompile Include="Source\SendCashDBDSNRequestCNationSettingDataRUUEAAXXZ_14022E840.cpp" />
    <ClCompile Include="Source\SendCashDBDSNRequestCNationSettingDataUEAAXXZ_140211D80.cpp" />
    <ClCompile Include="Source\SendCashDBDSNRequestCNationSettingManagerQEAAXXZ_140208060.cpp" />
    <ClCompile Include="Source\SendCChiNetworkEXQEAAHPEAEKPEADGZ_14040FD20.cpp" />
    <ClCompile Include="Source\SendCCurrentGuildBattleInfoManagerGUILD_BATTLEQEAA_1403CE3D0.cpp" />
    <ClCompile Include="Source\SendCEngNetworkBillEXQEAAHPEAEPEADGZ_14031BA40.cpp" />
    <ClCompile Include="Source\SendCGuildBattleRankManagerGUILD_BATTLEQEAAXHEKEEK_1403CA9C0.cpp" />
    <ClCompile Include="Source\SendCGuildBattleReservedScheduleListManagerGUILD_B_1403CD830.cpp" />
    <ClCompile Include="Source\SendChangeAggroDataCMonsterAggroMgrQEAAXXZ_14015E950.cpp" />
    <ClCompile Include="Source\SendCMsgListManagerRACE_BOSS_MSGQEAAHEKPEBD0AEAPEA_14029F930.cpp" />
    <ClCompile Include="Source\SendCNetSocketQEAA_NKPEADHPEAHZ_14047EA70.cpp" />
    <ClCompile Include="Source\SendCNormalGuildBattleGuildMemberGUILD_BATTLEQEAAX_1403E0360.cpp" />
    <ClCompile Include="Source\SendComfirmWebCRaceBossMsgControllerIEAAXEPEAVCMsg_1402A10F0.cpp" />
    <ClCompile Include="Source\SendConfirmCtrlCRaceBossMsgControllerIEAAXEPEAVCMs_1402A1250.cpp" />
    <ClCompile Include="Source\SendCPossibleBattleGuildListManagerGUILD_BATTLEQEA_1403D9990.cpp" />
    <ClCompile Include="Source\SendCRaceBossMsgControllerQEAA_NEKPEBD0KZ_1402A07C0.cpp" />
    <ClCompile Include="Source\SendCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CD040.cpp" />
    <ClCompile Include="Source\SendCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CCAD0.cpp" />
    <ClCompile Include="Source\SendCReservedGuildSchedulePageGUILD_BATTLEQEAAXHKP_1403CBE20.cpp" />
    <ClCompile Include="Source\SendCTotalGuildRankInfoQEAAXKHEEKZ_1402C8E60.cpp" />
    <ClCompile Include="Source\SendCurrentBattleInfoRequestCGuildBattleController_1403D6A80.cpp" />
    <ClCompile Include="Source\SendCurrHonorGuildListCHonorGuildQEAAXGEEZ_14025ED00.cpp" />
    <ClCompile Include="Source\SendCWeeklyGuildRankInfoQEAAXKHEEKZ_1402CAFD0.cpp" />
    <ClCompile Include="Source\SendDeleteNotifyPositionMemberCNormalGuildBattleGu_1403E2D80.cpp" />
    <ClCompile Include="Source\SendDQS_RoomInsertCGuildRoomInfoAEAAXXZ_1402E66F0.cpp" />
    <ClCompile Include="Source\SendDQS_RoomUpdateCGuildRoomInfoAEAAXXZ_1402E6790.cpp" />
    <ClCompile Include="Source\SendDrawResultCNormalGuildBattleGUILD_BATTLEIEAAXX_1403E78E0.cpp" />
    <ClCompile Include="Source\SendErrorResultCPossibleBattleGuildListManagerGUIL_1403CA260.cpp" />
    <ClCompile Include="Source\SendExternMsgUs_HFSMSAXPEAV1KPEAXHZ_140162D30.cpp" />
    <ClCompile Include="Source\SendFirstCPossibleBattleGuildListManagerGUILD_BATT_1403D9920.cpp" />
    <ClCompile Include="Source\SendHolyStoneHPToRaceBossCHolyStoneSystemQEAAXXZ_14027E9C0.cpp" />
    <ClCompile Include="Source\SendInfoCPossibleBattleGuildListManagerGUILD_BATTL_1403CA110.cpp" />
    <ClCompile Include="Source\SendInfomSenderCRaceBossMsgControllerIEAAXKEZ_1402A13B0.cpp" />
    <ClCompile Include="Source\SendInformChangeCHonorGuildQEAAXEGZ_14025F850.cpp" />
    <ClCompile Include="Source\SendIsArriveDestroyerCHolyStoneSystemQEAAXEZ_14027E860.cpp" />
    <ClCompile Include="Source\SendKillInformCNormalGuildBattleGUILD_BATTLEIEAAXX_1403E7590.cpp" />
    <ClCompile Include="Source\SendListCGuildListQEAAXGEEZ_14025DB20.cpp" />
    <ClCompile Include="Source\SendLoopYAKPEAXZ_140319840.cpp" />
    <ClCompile Include="Source\SendMemberPositionCNormalGuildBattleManagerGUILD_B_1403D4CD0.cpp" />
    <ClCompile Include="Source\SendMessageACWndQEAA_JI_K_JZ_0_1404DC40E.cpp" />
    <ClCompile Include="Source\SendMessageA_0_1404DEF6A.cpp" />
    <ClCompile Include="Source\SendMsgAccount_UILockRefresh_UpdateCUserDBQEAAXXZ_140118720.cpp" />
    <ClCompile Include="Source\SendMsgAlterStateCGravityStoneRegenerAEAAXXZ_14012EF50.cpp" />
    <ClCompile Include="Source\SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEA_1403E2730.cpp" />
    <ClCompile Include="Source\SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEA_1403E2800.cpp" />
    <ClCompile Include="Source\SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEA_1403E28E0.cpp" />
    <ClCompile Include="Source\SendMsgCreateCCircleZoneAEAAXXZ_14012DC60.cpp" />
    <ClCompile Include="Source\SendMsgGoalCCircleZoneAEAAXXZ_14012DDA0.cpp" />
    <ClCompile Include="Source\SendMsgRequestResultCRaceBossMsgControllerIEAAXGEZ_1402A1060.cpp" />
    <ClCompile Include="Source\SendMsgSucceedBuyCashDbWorkerAEAAXGAEBU_param_cash_1402F14F0.cpp" />
    <ClCompile Include="Source\SendMsgToMaster_NoCompleteQuestFromNPCCQuestMgrQEA_14028B530.cpp" />
    <ClCompile Include="Source\SendMsgToMaster_NoHaveGiveItemCQuestMgrQEAAXEZ_14028B5C0.cpp" />
    <ClCompile Include="Source\SendMsgToMaster_NoHaveReturnItemCQuestMgrQEAAXEZ_14028B650.cpp" />
    <ClCompile Include="Source\SendMsgToMaster_ReturnItemAfterQuestCQuestMgrQEAAX_14028B480.cpp" />
    <ClCompile Include="Source\SendMsgUs_HFSMSAXPEAV1KKPEAXZ_140162C00.cpp" />
    <ClCompile Include="Source\SendMsg_AddEffectCNuclearBombQEAAXXZ_14013CE00.cpp" />
    <ClCompile Include="Source\SendMsg_AddJoinApplierCGuildQEAAXPEAU_guild_applie_140256750.cpp" />
    <ClCompile Include="Source\SendMsg_AlterMemberGradeCGuildQEAAXXZ_1402576B0.cpp" />
    <ClCompile Include="Source\SendMsg_AlterMemberStateCGuildQEAAXXZ_140257490.cpp" />
    <ClCompile Include="Source\SendMsg_AlterTransparCTrapQEAAX_NZ_14013FD90.cpp" />
    <ClCompile Include="Source\SendMsg_AnimusActHealInformCAnimusQEAAXKHZ_14012A730.cpp" />
    <ClCompile Include="Source\SendMsg_ApplyGuildBattleResultInformCGuildQEAAXEPE_140258550.cpp" />
    <ClCompile Include="Source\SendMsg_AttackCGuardTowerQEAAXPEAVCAttackZ_140130840.cpp" />
    <ClCompile Include="Source\SendMsg_AttackCHolyKeeperQEAAXXZ_140134EF0.cpp" />
    <ClCompile Include="Source\SendMsg_AttackCNuclearBombQEAAXHHZ_14013CDE0.cpp" />
    <ClCompile Include="Source\SendMsg_AttackCTrapQEAAXPEAVCAttackZ_14013FA00.cpp" />
    <ClCompile Include="Source\SendMsg_Attack_ForceCMonsterQEAAXPEAVCMonsterAttac_14014EE20.cpp" />
    <ClCompile Include="Source\SendMsg_Attack_GenCAnimusQEAAXPEAVCAttackZ_14012A4C0.cpp" />
    <ClCompile Include="Source\SendMsg_Attack_GenCMonsterQEAAXPEAVCMonsterAttackZ_14014EC70.cpp" />
    <ClCompile Include="Source\SendMsg_BillingInfoCUserDBQEAAXXZ_140118270.cpp" />
    <ClCompile Include="Source\SendMsg_BreakStopCGameObjectQEAAXXZ_14017B140.cpp" />
    <ClCompile Include="Source\SendMsg_BuyCashItemICsSendInterfaceSAXGPEBU_param__14030C760.cpp" />
    <ClCompile Include="Source\SendMsg_CashDiscountEventInformICsSendInterfaceSAX_14030CC30.cpp" />
    <ClCompile Include="Source\SendMsg_CashEventInformICsSendInterfaceSAXGEEPEAU__14030CE00.cpp" />
    <ClCompile Include="Source\SendMsg_ChangeTaxRateCGuildQEAAXEZ_140255730.cpp" />
    <ClCompile Include="Source\SendMsg_Change_MonsterRotateCMonsterQEAAXXZ_140148790.cpp" />
    <ClCompile Include="Source\SendMsg_Change_MonsterStateCMonsterQEAAXXZ_140148700.cpp" />
    <ClCompile Include="Source\SendMsg_ChannelCloseCDarkHoleChannelQEAAXXZ_14026C4A0.cpp" />
    <ClCompile Include="Source\SendMsg_ConditionalEventInformICsSendInterfaceSAXG_14030D1A0.cpp" />
    <ClCompile Include="Source\SendMsg_CouponEnsureCCouponMgrQEAAXGEZ_1403FDFB0.cpp" />
    <ClCompile Include="Source\SendMsg_CouponErrorCCouponMgrQEAAXGEZ_1403FE110.cpp" />
    <ClCompile Include="Source\SendMsg_CouponLendResultCCouponMgrQEAAXGPEAU_db_co_1403FE230.cpp" />
    <ClCompile Include="Source\SendMsg_CreateCAnimusQEAAXXZ_140129EF0.cpp" />
    <ClCompile Include="Source\SendMsg_CreateCDarkHoleQEAAXXZ_140163F10.cpp" />
    <ClCompile Include="Source\SendMsg_CreateCGravityStoneAEAAXXZ_140165140.cpp" />
    <ClCompile Include="Source\SendMsg_CreateCGuardTowerQEAAXXZ_140130680.cpp" />
    <ClCompile Include="Source\SendMsg_CreateCHolyKeeperQEAAXXZ_140134D70.cpp" />
    <ClCompile Include="Source\SendMsg_CreateCHolyStoneQEAAXXZ_1401379D0.cpp" />
    <ClCompile Include="Source\SendMsg_CreateCItemBoxQEAAXXZ_140166660.cpp" />
    <ClCompile Include="Source\SendMsg_CreateCMerchantQEAAXXZ_1401393E0.cpp" />
    <ClCompile Include="Source\SendMsg_CreateCMonsterQEAAXXZ_140148380.cpp" />
    <ClCompile Include="Source\SendMsg_CreateCParkingUnitQEAAXXZ_140167CE0.cpp" />
    <ClCompile Include="Source\SendMsg_CreateCReturnGateIEAAXXZ_140168D50.cpp" />
    <ClCompile Include="Source\SendMsg_CreateCTrapQEAAXXZ_14013F7F0.cpp" />
    <ClCompile Include="Source\SendMsg_DelJoinApplierCGuildQEAAXPEAU_guild_applie_140256970.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCAnimusQEAAXXZ_14012A020.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCDarkHoleQEAAXXZ_140164040.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCGravityStoneAEAAXXZ_140165220.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCGuardTowerQEAAXEZ_140130780.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCHolyKeeperQEAAXEZ_140134E60.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCHolyStoneQEAAXEKZ_140137AD0.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCItemBoxQEAAXXZ_140166830.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCMerchantQEAAXXZ_1401394D0.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCMonsterQEAAXEZ_1401489D0.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCParkingUnitQEAAXEZ_140167E20.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCReturnGateIEAAXXZ_140168E60.cpp" />
    <ClCompile Include="Source\SendMsg_DestroyCTrapQEAAXEZ_14013F900.cpp" />
    <ClCompile Include="Source\SendMsg_DownPacketCGuildQEAAXEPEAU_guild_member_in_140255470.cpp" />
    <ClCompile Include="Source\SendMsg_DropMissileCNuclearBombQEAAXXZ_14013CD10.cpp" />
    <ClCompile Include="Source\SendMsg_EconomyDataToWebYAXXZ_1402A5110.cpp" />
    <ClCompile Include="Source\SendMsg_Emotion_PresentationCMonsterQEAAXEGGHZ_1401488E0.cpp" />
    <ClCompile Include="Source\SendMsg_EndBattleCHolyStoneSystemQEAAXEZ_14027EC30.cpp" />
    <ClCompile Include="Source\SendMsg_EnterKeeperCHolyStoneSystemQEAAXHZ_14027F510.cpp" />
    <ClCompile Include="Source\SendMsg_EnterStoneCHolyStoneSystemQEAAXHZ_14027F9A0.cpp" />
    <ClCompile Include="Source\SendMsg_ErrorICsSendInterfaceSAXGHZ_14030C590.cpp" />
    <ClCompile Include="Source\SendMsg_ExitStoneCHolyStoneSystemQEAAXXZ_14027EB70.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionAutominePersonalUEAAXHZ_1402DCD60.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCAnimusUEAAXHZ_14012A1E0.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCCircleZoneEEAAXHZ_14012DD00.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCDarkHoleUEAAXHZ_1401642F0.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCGameObjectUEAAXHZ_14013E3F0.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCGravityStoneRegenerEEAAXHZ_14012EE90.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCGravityStoneUEAAXHZ_140164F70.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCGuardTowerUEAAXHZ_140130930.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCHolyKeeperUEAAXHZ_1401351D0.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCHolyStoneUEAAXHZ_140137B80.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCItemBoxUEAAXHZ_1401668B0.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCMerchantUEAAXHZ_140139A80.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCMonsterUEAAXHZ_140148490.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCParkingUnitUEAAXHZ_140167FD0.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCReturnGateUEAAXHZ_140168BD0.cpp" />
    <ClCompile Include="Source\SendMsg_FixPositionCTrapUEAAXHZ_14013FB80.cpp" />
    <ClCompile Include="Source\SendMsg_GateDestroyCDarkHoleChannelQEAAXPEAEPEADHZ_14026CD30.cpp" />
    <ClCompile Include="Source\SendMsg_GoodsListICsSendInterfaceSAXGPEBU_param_ca_14030C620.cpp" />
    <ClCompile Include="Source\SendMsg_GuildBattleProposedCGuildQEAAHPEADZ_14025A120.cpp" />
    <ClCompile Include="Source\SendMsg_GuildBattleRefusedCGuildQEAAXPEADZ_14025A220.cpp" />
    <ClCompile Include="Source\SendMsg_GuildBattleSuggestResultCGuildQEAAXEPEADZ_1402583F0.cpp" />
    <ClCompile Include="Source\SendMsg_GuildDisjointInformCGuildQEAAXXZ_140256FE0.cpp" />
    <ClCompile Include="Source\SendMsg_GuildInfoUpdateInformCGuildQEAAXXZ_140255850.cpp" />
    <ClCompile Include="Source\SendMsg_GuildJoinAcceptInformCGuildQEAAXPEAU_guild_140256AE0.cpp" />
    <ClCompile Include="Source\SendMsg_GuildMemberLogoffCGuildQEAAXKZ_140257250.cpp" />
    <ClCompile Include="Source\SendMsg_GuildMemberPosInformCGuildQEAAXKGGZ_140257360.cpp" />
    <ClCompile Include="Source\SendMsg_GuildOutputMoneyFailCGuildQEAAXKZ_140256ED0.cpp" />
    <ClCompile Include="Source\SendMsg_GuildRoomRentedCGuildQEAAXEZ_1402586A0.cpp" />
    <ClCompile Include="Source\SendMsg_HolyKeeperAttackAbleStateCHolyStoneSystemQ_14027FEE0.cpp" />
    <ClCompile Include="Source\SendMsg_HolyKeeperStateChaosCHolyStoneSystemQEAAXX_14027FE10.cpp" />
    <ClCompile Include="Source\SendMsg_HolyStoneSystemStateCHolyStoneSystemQEAAXH_14027F410.cpp" />
    <ClCompile Include="Source\SendMsg_InformAttackCNuclearBombQEAAXXZ_14013CF70.cpp" />
    <ClCompile Include="Source\SendMsg_InformDropPosCNuclearBombQEAAXXZ_14013CB90.cpp" />
    <ClCompile Include="Source\SendMsg_Inform_UILockCUserDBQEAAXXZ_140118650.cpp" />
    <ClCompile Include="Source\SendMsg_InPcBangTimeCCouponMgrQEAAXGZ_1403FE040.cpp" />
    <ClCompile Include="Source\SendMsg_IOMoneyCGuildQEAAXKNN_NPEAEZ_140256CF0.cpp" />
    <ClCompile Include="Source\SendMsg_JobCountCDarkHoleChannelQEAAXHHZ_14026BCF0.cpp" />
    <ClCompile Include="Source\SendMsg_JobPassCDarkHoleChannelQEAAXHZ_14026C140.cpp" />
    <ClCompile Include="Source\SendMsg_KickForSailCTransportShipQEAAXHZ_1402653D0.cpp" />
    <ClCompile Include="Source\SendMsg_LeaveMemberCGuildQEAAXK_N0Z_140255A30.cpp" />
    <ClCompile Include="Source\SendMsg_LimitedsaleEventInformICsSendInterfaceSAXG_14030D2B0.cpp" />
    <ClCompile Include="Source\SendMsg_MachineInfoAutoMineMachineQEAAXHZ_1402D2830.cpp" />
    <ClCompile Include="Source\SendMsg_ManageGuildCommitteeResultCGuildQEAAX_NPEA_140259B00.cpp" />
    <ClCompile Include="Source\SendMsg_MasterDieCNuclearBombQEAAXXZ_14013D0D0.cpp" />
    <ClCompile Include="Source\SendMsg_MasterElectPossibleCGuildQEAAX_NZ_140259D50.cpp" />
    <ClCompile Include="Source\SendMsg_MissionPassCDarkHoleChannelQEAAXXZ_14026C230.cpp" />
    <ClCompile Include="Source\SendMsg_MoneySupplyDataToWebCMoneySupplyMgrQEAAXPE_14042F5F0.cpp" />
    <ClCompile Include="Source\SendMsg_MoveCAnimusQEAAXXZ_14012A0D0.cpp" />
    <ClCompile Include="Source\SendMsg_MoveCHolyKeeperQEAAXXZ_1401350A0.cpp" />
    <ClCompile Include="Source\SendMsg_MoveCMerchantQEAAXXZ_140139560.cpp" />
    <ClCompile Include="Source\SendMsg_MoveCMonsterQEAAXXZ_140148A70.cpp" />
    <ClCompile Include="Source\SendMsg_NewMissionCDarkHoleChannelQEAAXXZ_14026C580.cpp" />
    <ClCompile Include="Source\SendMsg_NoticeNextQuestCHolyStoneSystemQEAAXHEZ_140280150.cpp" />
    <ClCompile Include="Source\SendMsg_NotifyHolyKeeperAttackTimeBeKeepKeeperCHol_14027C7E0.cpp" />
    <ClCompile Include="Source\SendMsg_NuclearFindCNuclearBombQEAAXHEZ_14013CAF0.cpp" />
    <ClCompile Include="Source\SendMsg_OpenPortalByReactCDarkHoleChannelQEAAXHZ_14026BE00.cpp" />
    <ClCompile Include="Source\SendMsg_OpenPortalByResultCDarkHoleChannelQEAAXHZ_14026BEF0.cpp" />
    <ClCompile Include="Source\SendMsg_PatriarchTaxRateTRC_AutoTradeQEAAXHZ_1402D8480.cpp" />
    <ClCompile Include="Source\SendMsg_PvpCashInformCPvpCashPointQEAAXHEZ_1403F5940.cpp" />
    <ClCompile Include="Source\SendMsg_PvpRankListDataCPvpUserRankingInfoAEAAXGEE_14032E020.cpp" />
    <ClCompile Include="Source\SendMsg_PvpRankListNodataCPvpUserRankingInfoAEAAXG_14032DF70.cpp" />
    <ClCompile Include="Source\SendMsg_QueryAppointResultClassOrderProcessorQEAAX_1402B8E60.cpp" />
    <ClCompile Include="Source\SendMsg_QueryPacket_InfoCGuildQEAAXHZ_1402559B0.cpp" />
    <ClCompile Include="Source\SendMsg_QuestPassCDarkHoleChannelQEAAXXZ_14026C3C0.cpp" />
    <ClCompile Include="Source\SendMsg_RealAddLimTimeCDarkHoleChannelQEAAXHPEADZ_14026CB80.cpp" />
    <ClCompile Include="Source\SendMsg_RealFixPositionCGameObjectUEAAX_NZ_14017B050.cpp" />
    <ClCompile Include="Source\SendMsg_RealFixPositionCMerchantUEAAX_NZ_14013A1E0.cpp" />
    <ClCompile Include="Source\SendMsg_RealMovePointCAnimusUEAAXHZ_14012A330.cpp" />
    <ClCompile Include="Source\SendMsg_RealMovePointCGameObjectUEAAXHZ_14012E0A0.cpp" />
    <ClCompile Include="Source\SendMsg_RealMovePointCHolyKeeperUEAAXHZ_1401352D0.cpp" />
    <ClCompile Include="Source\SendMsg_RealMovePointCMerchantUEAAXHZ_140139BA0.cpp" />
    <ClCompile Include="Source\SendMsg_RealMovePointCMonsterUEAAXHZ_1401485C0.cpp" />
    <ClCompile Include="Source\SendMsg_RealMsgInformCDarkHoleChannelQEAAXPEADZ_14026CA60.cpp" />
    <ClCompile Include="Source\SendMsg_RecoverResultCPvpCashPointQEAAXHEHZ_1403F58A0.cpp" />
    <ClCompile Include="Source\SendMsg_RemainBufUseTimeCExtPotionBufQEAAX_NGHHHZ_14039FC70.cpp" />
    <ClCompile Include="Source\SendMsg_RemainCouponInformCCouponMgrQEAAXGEZ_1403FE1A0.cpp" />
    <ClCompile Include="Source\SendMsg_ResultCNuclearBombMgrQEAAXHEZ_14013B5B0.cpp" />
    <ClCompile Include="Source\SendMsg_ResultCNuclearBombQEAAXHEZ_14013D260.cpp" />
    <ClCompile Include="Source\SendMsg_ResultCodeAutoMineMachineMngQEBAXHEEZ_1402D6B90.cpp" />
    <ClCompile Include="Source\SendMsg_ResultCodeAutoMineMachineQEBAXHEEZ_1402D2A80.cpp" />
    <ClCompile Include="Source\SendMsg_ResultCodePatriarchElectProcessorQEAAXKEZ_1402BAFB0.cpp" />
    <ClCompile Include="Source\SendMsg_RoomTimeOverCGuildRoomInfoAEAAXXZ_1402E6890.cpp" />
    <ClCompile Include="Source\SendMsg_SetHPInformCGameObjectUEAAXXZ_14012C650.cpp" />
    <ClCompile Include="Source\SendMsg_StartBattleCHolyStoneSystemQEAAXXZ_14027FD40.cpp" />
    <ClCompile Include="Source\SendMsg_StartBillingCBillingIEAAXXZ_14028D550.cpp" />
    <ClCompile Include="Source\SendMsg_StartedVoteInformCVoteSystemQEAAXHK_NZ_1402B0890.cpp" />
    <ClCompile Include="Source\SendMsg_StateChangeCDarkHoleQEAAXXZ_140164240.cpp" />
    <ClCompile Include="Source\SendMsg_StateChangeCItemBoxQEAAXXZ_140166A10.cpp" />
    <ClCompile Include="Source\SendMsg_StoneAlterOperCHolyStoneQEAAXXZ_140137CD0.cpp" />
    <ClCompile Include="Source\SendMsg_StunInformCGameObjectUEAAXXZ_140164850.cpp" />
    <ClCompile Include="Source\SendMsg_TalikListCPvpCashPointQEAAXHZ_1403F57A0.cpp" />
    <ClCompile Include="Source\sendmsg_taxrateTRC_AutoTradeQEAAXHEZ_1402D8320.cpp" />
    <ClCompile Include="Source\SendMsg_TicketCheckCTransportShipQEAAXH_NGZ_140265330.cpp" />
    <ClCompile Include="Source\SendMsg_TimeOutCDarkHoleChannelQEAAXXZ_14026C8D0.cpp" />
    <ClCompile Include="Source\SendMsg_TowerCompleteInformCGuardTowerQEAAXXZ_140130A80.cpp" />
    <ClCompile Include="Source\SendMsg_TransportShipStateCTransportShipQEAAXHZ_140265450.cpp" />
    <ClCompile Include="Source\SendMsg_TransShipTicketNumInformCMerchantQEAAXHZ_140139650.cpp" />
    <ClCompile Include="Source\SendMsg_TrapCompleteInformCTrapQEAAXXZ_14013FD10.cpp" />
    <ClCompile Include="Source\SendMsg_VoteCancelInformCGuildQEAAXXZ_140256410.cpp" />
    <ClCompile Include="Source\SendMsg_VoteCompleteCGuildQEAAX_NZ_140256630.cpp" />
    <ClCompile Include="Source\SendMsg_VoteProcessInform_ContinueCGuildQEAAXPEAU__140255F20.cpp" />
    <ClCompile Include="Source\SendMsg_VoteProcessInform_StartCGuildQEAAXXZ_140255B90.cpp" />
    <ClCompile Include="Source\SendMsg_VoteStateCGuildQEAAXXZ_140256270.cpp" />
    <ClCompile Include="Source\SendMsg_VoteStopCGuildQEAAXKZ_140256540.cpp" />
    <ClCompile Include="Source\SendMsg_WaitKeeperCHolyStoneSystemQEAAXHEZ_14027F7C0.cpp" />
    <ClCompile Include="Source\SendMsg_WaitStoneCHolyStoneSystemQEAAXHZ_14027F8C0.cpp" />
    <ClCompile Include="Source\SendMsg_ZoneAliveCheckCBillingManagerQEAAXKZ_1401C42C0.cpp" />
    <ClCompile Include="Source\SendMsg_ZoneAliveCheckCBillingQEAAXKZ_14028D760.cpp" />
    <ClCompile Include="Source\SendNextHonorGuildListCHonorGuildQEAAXGEZ_14025EDB0.cpp" />
    <ClCompile Include="Source\SendNotifyCloseItemCUnmannedTraderUserInfoQEAAXGGK_140357A50.cpp" />
    <ClCompile Include="Source\SendNotifyHolyStoneDestroyedToRaceBossCHolyStoneSy_14027FC00.cpp" />
    <ClCompile Include="Source\SendPossibleBattleGuildListCGuildBattleControllerQ_1403D6910.cpp" />
    <ClCompile Include="Source\SendPossibleBattleGuildListFirstCGuildBattleContro_1403D68B0.cpp" />
    <ClCompile Include="Source\SendRaceBossMsgFromWebRequestCNetworkEXAEAA_NHPEAD_1401DA990.cpp" />
    <ClCompile Include="Source\SendRankListCGuildBattleControllerQEAAXHEKIEKZ_1403D6980.cpp" />
    <ClCompile Include="Source\SendRegenBallCNormalGuildBattleGuildGUILD_BATTLEQE_1403E2150.cpp" />
    <ClCompile Include="Source\SendRegistItemErrorResultCUnmannedTraderUserInfoQE_140357B10.cpp" />
    <ClCompile Include="Source\SendRegistItemSuccessResultCUnmannedTraderUserInfo_140357BE0.cpp" />
    <ClCompile Include="Source\SendRequestWebCRaceBossMsgControllerIEAAXEPEAVCMsg_1402A1480.cpp" />
    <ClCompile Include="Source\SendReservedScheduleListCGuildBattleControllerQEAA_1403D6A00.cpp" />
    <ClCompile Include="Source\SendSearchErrorResultCUnmannedTraderUserInfoQEAAXG_140358150.cpp" />
    <ClCompile Include="Source\SendSearchResultCUnmannedTraderUserInfoQEAAXGPEADZ_140358210.cpp" />
    <ClCompile Include="Source\SendSellInfomCUnmannedTraderUserInfoQEAAXGGKKKZ_140358090.cpp" />
    <ClCompile Include="Source\SendSMS_CompleteQuestCHolyStoneSystemQEAAXEPEADH0E_14027ED20.cpp" />
    <ClCompile Include="Source\SendSMS_MineTimeExtendCHolyStoneSystemQEAAXHZ_14027E720.cpp" />
    <ClCompile Include="Source\SendStartNotifyCommitteeMemberPositionCNormalGuild_1403E2250.cpp" />
    <ClCompile Include="Source\SendTaxRateCUnmannedTraderTaxRateManagerQEAAXHEZ_14038E420.cpp" />
    <ClCompile Include="Source\SendTaxRatePatriarchCUnmannedTraderTaxRateManagerQ_14038E4C0.cpp" />
    <ClCompile Include="Source\SendThreadCNetProcessCAXPEAXZ_140478BD0.cpp" />
    <ClCompile Include="Source\SendWebAddScheduleInfoCNormalGuildBattleGUILD_BATT_1403E8690.cpp" />
    <ClCompile Include="Source\SendWebBattleEndInfoCNormalGuildBattleGUILD_BATTLE_1403E6D50.cpp" />
    <ClCompile Include="Source\SendWebBattleStartInfoCNormalGuildBattleGUILD_BATT_1403E6C90.cpp" />
    <ClCompile Include="Source\SendWebRaceBossSMSCMainThreadQEAAXPEAU_DB_QRY_SYN__1401F40B0.cpp" />
    <ClCompile Include="Source\SendWebRaceBossSMSErrorResultCRaceBossMsgControlle_1402A1890.cpp" />
    <ClCompile Include="Source\SendWinLoseResultCNormalGuildBattleGUILD_BATTLEIEA_1403E79F0.cpp" />
    <ClCompile Include="Source\send_0_1404DBA64.cpp" />
    <ClCompile Include="Source\send_attackedAutominePersonalQEAAXXZ_1402DCB70.cpp" />
    <ClCompile Include="Source\send_changed_packetAutominePersonalQEAAXHZ_1402DC930.cpp" />
    <ClCompile Include="Source\send_current_stateAutominePersonalQEAAXXZ_1402DCEA0.cpp" />
    <ClCompile Include="Source\send_ecodeAutominePersonalMgrQEAAXHEZ_1402E0C20.cpp" />
    <ClCompile Include="Source\send_ecodeAutominePersonalQEAAXEZ_1402DCC90.cpp" />
    <ClCompile Include="Source\send_installedAutominePersonalQEAAXXZ_1402DC9F0.cpp" />
    <ClCompile Include="Source\SetErrorButRunMessageProcYAXP6AXPEADZZ_140509890.cpp" />
    <ClCompile Include="Source\SetErrorMessageProcYAXP6AXPEADZZ_140509870.cpp" />
    <ClCompile Include="Source\SetFtpConnectionWheatyExceptionReportQEAAXPEADI000_14043F640.cpp" />
    <ClCompile Include="Source\SetMsg_messageQEAAXKKKKZ_140438920.cpp" />
    <ClCompile Include="Source\SetPassablePacketCNetworkEXQEAAXKEEZ_140208100.cpp" />
    <ClCompile Include="Source\SetRaceWarRecvrCPvpCashPointQEAAX_NZ_14007C320.cpp" />
    <ClCompile Include="Source\SetReconnectFailExitFlagCRFNewDatabaseQEAAX_NZ_1402F2AB0.cpp" />
    <ClCompile Include="Source\SetSocketCNetSocketQEAA_NPEAU_SOCK_TYPE_PARAMPEADZ_14047DDA0.cpp" />
    <ClCompile Include="Source\SetWarningMessageProcYAXP6AXPEADZZ_140509880.cpp" />
    <ClCompile Include="Source\SFContDelMessageCGameObjectUEAAXEE_N0Z_14012C6A0.cpp" />
    <ClCompile Include="Source\SFContUpdateTimeMessageCGameObjectUEAAXEEHZ_14012C6C0.cpp" />
    <ClCompile Include="Source\ShouldPropagateMessageEndFilterCryptoPPMEBA_NXZ_14044CF90.cpp" />
    <ClCompile Include="Source\ShouldPropagateMessageSeriesEndFilterCryptoPPMEBA__14044CFA0.cpp" />
    <ClCompile Include="Source\SignAndRestartDL_SignerBaseUEC2NPointCryptoPPCrypt_1405663C0.cpp" />
    <ClCompile Include="Source\SignAndRestartDL_SignerBaseUECPPointCryptoPPCrypto_140564720.cpp" />
    <ClCompile Include="Source\SignAndRestartDL_SignerBaseVIntegerCryptoPPCryptoP_1405622F0.cpp" />
    <ClCompile Include="Source\SignAndRestartTF_SignerBaseCryptoPPUEBA_KAEAVRando_140622BF0.cpp" />
    <ClCompile Include="Source\SignMessagePK_SignerCryptoPPUEBA_KAEAVRandomNumber_1405F5FF0.cpp" />
    <ClCompile Include="Source\SignMessageWithRecoveryPK_SignerCryptoPPUEBA_KAEAV_1405F60E0.cpp" />
    <ClCompile Include="Source\SignPK_SignerCryptoPPUEBA_KAEAVRandomNumberGenerat_1405F5F50.cpp" />
    <ClCompile Include="Source\size_announ_message_receipt_udpQEAAHXZ_140095000.cpp" />
    <ClCompile Include="Source\size_apex_send_ipQEAAHXZ_140410C20.cpp" />
    <ClCompile Include="Source\size_apex_send_logoutQEAAHXZ_140410C40.cpp" />
    <ClCompile Include="Source\size_apex_send_transQEAAHXZ_140410C30.cpp" />
    <ClCompile Include="Source\size_chat_message_receipt_udpQEAAHXZ_140094EB0.cpp" />
    <ClCompile Include="Source\size_chat_steal_message_gm_zoclQEAAHXZ_1403F8D50.cpp" />
    <ClCompile Include="Source\size_connection_status_result_zoctQEAAHXZ_1401C7750.cpp" />
    <ClCompile Include="Source\size_qry_case_post_sendQEAAHXZ_140328320.cpp" />
    <ClCompile Include="Source\size_qry_case_sendwebracebosssmsQEAAHXZ_1401DAB00.cpp" />
    <ClCompile Include="Source\size_qry_case_update_data_for_post_sendQEAAHXZ_1400CA7D0.cpp" />
    <ClCompile Include="Source\size_talik_recvr_listQEAAHXZ_1403F6F30.cpp" />
    <ClCompile Include="Source\SkipMessagesBufferedTransformationCryptoPPUEAAIIZ_1405F5340.cpp" />
    <ClCompile Include="Source\socket_0_1404DBA58.cpp" />
    <ClCompile Include="Source\sortV_Deque_iteratorUMessageRangeMeterFilterCrypto_140600FA0.cpp" />
    <ClCompile Include="Source\sort_heapV_Deque_iteratorUMessageRangeMeterFilterC_140603520.cpp" />
    <ClCompile Include="Source\SpyMessageQueueCryptoPPQEBAPEBEAEA_KZ_1406548E0.cpp" />
    <ClCompile Include="Source\SQLConnect_0_1404DB9D4.cpp" />
    <ClCompile Include="Source\SQLDisconnect_0_1404DB9F2.cpp" />
    <ClCompile Include="Source\SQLSetConnectAttr_0_1404DB9DA.cpp" />
    <ClCompile Include="Source\StaticAlgorithmNameDL_SSUDL_Keys_ECDSAVEC2NCryptoP_14056C440.cpp" />
    <ClCompile Include="Source\StaticAlgorithmNameDL_SSUDL_Keys_ECDSAVECPCryptoPP_14056C0F0.cpp" />
    <ClCompile Include="Source\StaticAlgorithmNameDL_SSUDL_SignatureKeys_GFPCrypt_140639280.cpp" />
    <ClCompile Include="Source\StaticAlgorithmNameDL_SSUDL_SignatureKeys_GFPCrypt_1406395B0.cpp" />
    <ClCompile Include="Source\swapMessageQueueCryptoPPQEAAXAEAV12Z_140654890.cpp" />
    <ClCompile Include="Source\swapUMessageRangeMeterFilterCryptoPPstdYAXAEAUMess_140604E80.cpp" />
    <ClCompile Include="Source\TotalBytesRetrievableMessageQueueCryptoPPUEBA_KXZ_1406551D0.cpp" />
    <ClCompile Include="Source\TransferMessagesTo2BufferedTransformationCryptoPPQ_1405F53D0.cpp" />
    <ClCompile Include="Source\TransferMessagesToBufferedTransformationCryptoPPQE_1405F7980.cpp" />
    <ClCompile Include="Source\TransferTo2MessageQueueCryptoPPUEAA_KAEAVBufferedT_140654470.cpp" />
    <ClCompile Include="Source\TranslateMessage_0_140676E7C.cpp" />
    <ClCompile Include="Source\TruncatedFinalPK_MessageAccumulatorCryptoPPUEAAXPE_140562EB0.cpp" />
    <ClCompile Include="Source\tutorial_process_report_recvCMgrAccountLobbyHistor_140234980.cpp" />
    <ClCompile Include="Source\unchecked_copy_backwardV_Deque_iteratorUMessageRan_1406054E0.cpp" />
    <ClCompile Include="Source\unchecked_fill_nPEAPEAUMessageRangeMeterFilterCryp_140603790.cpp" />
    <ClCompile Include="Source\unchecked_fill_nPEAPEAURECV_DATA_KPEAU1stdextYAXPE_14031B3E0.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_copyPEAPEAUMessageRangeMet_140601520.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_copyPEAPEAURECV_DATAPEAPEA_14031AE40.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_fill_nPEAPEAUMessageRangeM_140601590.cpp" />
    <ClCompile Include="Source\unchecked_uninitialized_fill_nPEAPEAURECV_DATA_KPE_14031AEF0.cpp" />
    <ClCompile Include="Source\UpdatePacketClassOrderProcessorQEAAXEEZ_1402B9180.cpp" />
    <ClCompile Include="Source\UpdatePK_MessageAccumulatorBaseCryptoPPUEAAXPEBE_K_1405630D0.cpp" />
    <ClCompile Include="Source\UpdateSendCRaceBossMsgControllerIEAAXXZ_1402A0C80.cpp" />
    <ClCompile Include="Source\Update_PostStorageSendToRecverCRFWorldDatabaseQEAA_1404B2F60.cpp" />
    <ClCompile Include="Source\Update_RaceWarRecvrCPvpOrderViewQEAAX_NZ_1403F8140.cpp" />
    <ClCompile Include="Source\VerifyAndRestartDL_VerifierBaseUEC2NPointCryptoPPC_140567100.cpp" />
    <ClCompile Include="Source\VerifyAndRestartDL_VerifierBaseUECPPointCryptoPPCr_140565460.cpp" />
    <ClCompile Include="Source\VerifyAndRestartDL_VerifierBaseVIntegerCryptoPPCry_1405636C0.cpp" />
    <ClCompile Include="Source\VerifyAndRestartTF_VerifierBaseCryptoPPUEBA_NAEAVP_1406232D0.cpp" />
    <ClCompile Include="Source\VerifyMessagePK_VerifierCryptoPPUEBA_NPEBE_K01Z_1405F6290.cpp" />
    <ClCompile Include="Source\VerifyMessageRepresentativePK_DeterministicSignatu_140622520.cpp" />
    <ClCompile Include="Source\VerifyMessageRepresentativePK_RecoverableSignature_1406226A0.cpp" />
    <ClCompile Include="Source\VerifyPK_VerifierCryptoPPUEBA_NPEAVPK_MessageAccum_1405F6200.cpp" />
    <ClCompile Include="Source\Y_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603C80.cpp" />
    <ClCompile Include="Source\Y_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031EB30.cpp" />
    <ClCompile Include="Source\Y_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140603BC0.cpp" />
    <ClCompile Include="Source\Y_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E980.cpp" />
    <ClCompile Include="Source\Z_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140603BF0.cpp" />
    <ClCompile Include="Source\_Adjust_heapV_Deque_iteratorUMessageRangeMeterFilt_140604F00.cpp" />
    <ClCompile Include="Source\_AllocatePEAUMessageRangeMeterFilterCryptoPPstdYAP_140601750.cpp" />
    <ClCompile Include="Source\_AllocatePEAURECV_DATAstdYAPEAPEAURECV_DATA_KPEAPE_14031B190.cpp" />
    <ClCompile Include="Source\_AllocateUMessageRangeMeterFilterCryptoPPstdYAPEAU_140601650.cpp" />
    <ClCompile Include="Source\_AllocateURECV_DATAstdYAPEAURECV_DATA_KPEAU1Z_14031B020.cpp" />
    <ClCompile Include="Source\_buybygold_buy_single_item_setsenddataCashItemRemo_140300650.cpp" />
    <ClCompile Include="Source\_CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7C0.cpp" />
    <ClCompile Include="Source\_CcrFG_rs_EncryptPacketYAHPEAXPEAEHZ_14066D7D2.cpp" />
    <ClCompile Include="Source\_CheckSendCNetProcessAEAAXGZ_140479A30.cpp" />
    <ClCompile Include="Source\_CkeckRecvBreakCNetProcessAEAAXXZ_14047B010.cpp" />
    <ClCompile Include="Source\_CNetSocketCNetSocket__1_dtor0_14047DC60.cpp" />
    <ClCompile Include="Source\_CNetSocketCNetSocket__1_dtor1_14047DC90.cpp" />
    <ClCompile Include="Source\_CNetSocketSetSocket__1_dtor0_14047E110.cpp" />
    <ClCompile Include="Source\_CNetSocket_CNetSocket__1_dtor0_14047DD40.cpp" />
    <ClCompile Include="Source\_CNetSocket_CNetSocket__1_dtor1_14047DD70.cpp" />
    <ClCompile Include="Source\_ConstructUMessageRangeMeterFilterCryptoPPU123stdY_1406016C0.cpp" />
    <ClCompile Include="Source\_ConstructURECV_DATAU1stdYAXPEAURECV_DATAAEBU1Z_14031B0D0.cpp" />
    <ClCompile Include="Source\_Copy_backward_optV_Deque_iteratorUMessageRangeMet_140605DE0.cpp" />
    <ClCompile Include="Source\_Copy_backward_optV_Deque_iteratorURECV_DATAValloc_14031F460.cpp" />
    <ClCompile Include="Source\_Copy_optV_Deque_iteratorURECV_DATAVallocatorURECV_14031F6A0.cpp" />
    <ClCompile Include="Source\_db_Update_Data_For_Post_SendCMainThreadAEAAEPEADZ_1401B8FD0.cpp" />
    <ClCompile Include="Source\_DestroyPEAUMessageRangeMeterFilterCryptoPPstdYAXP_1406017C0.cpp" />
    <ClCompile Include="Source\_DestroyPEAURECV_DATAstdYAXPEAPEAURECV_DATAZ_14031FDC0.cpp" />
    <ClCompile Include="Source\_DestroyUMessageRangeMeterFilterCryptoPPstdYAXPEAU_140601740.cpp" />
    <ClCompile Include="Source\_DestroyURECV_DATAstdYAXPEAURECV_DATAZ_14031F390.cpp" />
    <ClCompile Include="Source\_Destroy_rangePEAUMessageRangeMeterFilterCryptoPPV_140601600.cpp" />
    <ClCompile Include="Source\_Destroy_rangePEAUMessageRangeMeterFilterCryptoPPV_140602380.cpp" />
    <ClCompile Include="Source\_Destroy_rangePEAURECV_DATAVallocatorPEAURECV_DATA_14031AFA0.cpp" />
    <ClCompile Include="Source\_Destroy_rangePEAURECV_DATAVallocatorPEAURECV_DATA_14031B3C0.cpp" />
    <ClCompile Include="Source\_Dist_typeV_Deque_iteratorUMessageRangeMeterFilter_140604390.cpp" />
    <ClCompile Include="Source\_dynamic_atexit_destructor_for__g_vRecvData___1406E9180.cpp" />
    <ClCompile Include="Source\_dynamic_initializer_for__g_vRecvData___1406E2110.cpp" />
    <ClCompile Include="Source\_ECNetSocketUEAAPEAXIZ_14047F840.cpp" />
    <ClCompile Include="Source\_EDL_SignatureMessageEncodingMethod_DSACryptoPPUEA_14058FDB0.cpp" />
    <ClCompile Include="Source\_EDL_SignerImplUDL_SignatureSchemeOptionsUDSACrypt_1405AE280.cpp" />
    <ClCompile Include="Source\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140567EF0.cpp" />
    <ClCompile Include="Source\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_1405ADDC0.cpp" />
    <ClCompile Include="Source\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_1405ADE20.cpp" />
    <ClCompile Include="Source\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140635950.cpp" />
    <ClCompile Include="Source\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_14063DBF0.cpp" />
    <ClCompile Include="Source\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_14063DF50.cpp" />
    <ClCompile Include="Source\_EDL_VerifierImplUDL_SignatureSchemeOptionsUDSACry_1405AE0E0.cpp" />
    <ClCompile Include="Source\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_1405AD470.cpp" />
    <ClCompile Include="Source\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_1405ADCF0.cpp" />
    <ClCompile Include="Source\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_1406359B0.cpp" />
    <ClCompile Include="Source\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_14063DE30.cpp" />
    <ClCompile Include="Source\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_14063DF30.cpp" />
    <ClCompile Include="Source\_EMessageQueueCryptoPPW7EAAPEAXIZ_14065EFB0.cpp" />
    <ClCompile Include="Source\_E_messageQEAAPEAXIZ_140438850.cpp" />
    <ClCompile Include="Source\_E_socketQEAAPEAXIZ_14047FA70.cpp" />
    <ClCompile Include="Source\_Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140604AF0.cpp" />
    <ClCompile Include="Source\_Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140605760.cpp" />
    <ClCompile Include="Source\_Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV_DA_14031B560.cpp" />
    <ClCompile Include="Source\_Fill_nPEAPEAURECV_DATA_KPEAU1Urandom_access_itera_14031B4E0.cpp" />
    <ClCompile Include="Source\_GDL_SignatureMessageEncodingMethod_NRCryptoPPUEAA_14063D300.cpp" />
    <ClCompile Include="Source\_GDL_SignerImplUDL_SignatureSchemeOptionsUDSACrypt_140567CD0.cpp" />
    <ClCompile Include="Source\_GDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140567FB0.cpp" />
    <ClCompile Include="Source\_GDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140635A10.cpp" />
    <ClCompile Include="Source\_GDL_VerifierImplUDL_SignatureSchemeOptionsUDSACry_140567D90.cpp" />
    <ClCompile Include="Source\_GDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_140567F50.cpp" />
    <ClCompile Include="Source\_GDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_140568010.cpp" />
    <ClCompile Include="Source\_GDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_140635A70.cpp" />
    <ClCompile Include="Source\_GMessageQueueCryptoPPUEAAPEAXIZ_1406552B0.cpp" />
    <ClCompile Include="Source\_GPK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUE_140567D30.cpp" />
    <ClCompile Include="Source\_GrowmapdequeUMessageRangeMeterFilterCryptoPPVallo_140600530.cpp" />
    <ClCompile Include="Source\_GrowmapdequeURECV_DATAVallocatorURECV_DATAstdstdI_14031A690.cpp" />
    <ClCompile Include="Source\_Insertion_sort1V_Deque_iteratorUMessageRangeMeter_140604710.cpp" />
    <ClCompile Include="Source\_Insertion_sortV_Deque_iteratorUMessageRangeMeterF_140603630.cpp" />
    <ClCompile Include="Source\_InternalPacketProcessCNetProcessAEAA_NKPEAU_MSG_H_14047A4F0.cpp" />
    <ClCompile Include="Source\_Iter_catPEAPEAUMessageRangeMeterFilterCryptoPPstd_140604AD0.cpp" />
    <ClCompile Include="Source\_Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_iter_14031B480.cpp" />
    <ClCompile Include="Source\_Iter_randomV_Deque_iteratorUMessageRangeMeterFilt_140605DA0.cpp" />
    <ClCompile Include="Source\_Iter_randomV_Deque_iteratorURECV_DATAVallocatorUR_14031F3A0.cpp" />
    <ClCompile Include="Source\_Make_heapV_Deque_iteratorUMessageRangeMeterFilter_1406043F0.cpp" />
    <ClCompile Include="Source\_Med3V_Deque_iteratorUMessageRangeMeterFilterCrypt_140604B40.cpp" />
    <ClCompile Include="Source\_MedianV_Deque_iteratorUMessageRangeMeterFilterCry_140603CB0.cpp" />
    <ClCompile Include="Source\_PopRecvMsgCNetProcessAEAAXGZ_140478680.cpp" />
    <ClCompile Include="Source\_Pop_heapV_Deque_iteratorUMessageRangeMeterFilterC_140605F60.cpp" />
    <ClCompile Include="Source\_Pop_heap_0V_Deque_iteratorUMessageRangeMeterFilte_140605B00.cpp" />
    <ClCompile Include="Source\_Ptr_catPEAPEAUMessageRangeMeterFilterCryptoPPPEAP_1406022A0.cpp" />
    <ClCompile Include="Source\_Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_ptr_14031B240.cpp" />
    <ClCompile Include="Source\_Ptr_catV_Deque_iteratorUMessageRangeMeterFilterCr_140605DC0.cpp" />
    <ClCompile Include="Source\_Ptr_catV_Deque_iteratorURECV_DATAVallocatorURECV__14031F400.cpp" />
    <ClCompile Include="Source\_Push_heapV_Deque_iteratorUMessageRangeMeterFilter_1406057B0.cpp" />
    <ClCompile Include="Source\_SendListCandidateRegisterAEAAHGEZ_1402B6CC0.cpp" />
    <ClCompile Include="Source\_SendLoopCNetProcessAEAAXKZ_140478D60.cpp" />
    <ClCompile Include="Source\_SendSpeedHackCheckMsgCNetProcessAEAAXHZ_14047B0E0.cpp" />
    <ClCompile Include="Source\_SendVotePaperAllVoterAEAAXXZ_1402BEB20.cpp" />
    <ClCompile Include="Source\_SendVoteScoreAllVoterAEAAXEZ_1402BEEC0.cpp" />
    <ClCompile Include="Source\_SortV_Deque_iteratorUMessageRangeMeterFilterCrypt_140601B10.cpp" />
    <ClCompile Include="Source\_Sort_heapV_Deque_iteratorUMessageRangeMeterFilter_1406045D0.cpp" />
    <ClCompile Include="Source\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EDE0.cpp" />
    <ClCompile Include="Source\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE10.cpp" />
    <ClCompile Include="Source\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE40.cpp" />
    <ClCompile Include="Source\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE70.cpp" />
    <ClCompile Include="Source\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EEA0.cpp" />
    <ClCompile Include="Source\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EED0.cpp" />
    <ClCompile Include="Source\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F1A0.cpp" />
    <ClCompile Include="Source\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F1D0.cpp" />
    <ClCompile Include="Source\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F200.cpp" />
    <ClCompile Include="Source\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F230.cpp" />
    <ClCompile Include="Source\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F260.cpp" />
    <ClCompile Include="Source\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F290.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031D800.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031D830.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031D860.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E010.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E040.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E070.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E0A0.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E0D0.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E100.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E130.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E160.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E190.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E1C0.cpp" />
    <ClCompile Include="Source\_stddeque_RECV_DATA_stdallocator_RECV_DATA____Xlen_14031ACD0.cpp" />
    <ClCompile Include="Source\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F560.cpp" />
    <ClCompile Include="Source\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F590.cpp" />
    <ClCompile Include="Source\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F5C0.cpp" />
    <ClCompile Include="Source\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F5F0.cpp" />
    <ClCompile Include="Source\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F7B0.cpp" />
    <ClCompile Include="Source\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F7E0.cpp" />
    <ClCompile Include="Source\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F810.cpp" />
    <ClCompile Include="Source\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F840.cpp" />
    <ClCompile Include="Source\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031D9E0.cpp" />
    <ClCompile Include="Source\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031DA10.cpp" />
    <ClCompile Include="Source\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031E4D0.cpp" />
    <ClCompile Include="Source\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031E500.cpp" />
    <ClCompile Include="Source\_TidydequeUMessageRangeMeterFilterCryptoPPVallocat_1406008E0.cpp" />
    <ClCompile Include="Source\_TidydequeURECV_DATAVallocatorURECV_DATAstdstdIEAA_14031FBC0.cpp" />
    <ClCompile Include="Source\_Unguarded_partitionV_Deque_iteratorUMessageRangeM_140602520.cpp" />
    <ClCompile Include="Source\_Uninit_copyPEAPEAUMessageRangeMeterFilterCryptoPP_1406022C0.cpp" />
    <ClCompile Include="Source\_Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPEAU_14031B2A0.cpp" />
    <ClCompile Include="Source\_Uninit_fill_nPEAPEAUMessageRangeMeterFilterCrypto_140602340.cpp" />
    <ClCompile Include="Source\_Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorPEA_14031B360.cpp" />
    <ClCompile Include="Source\_UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_1402B7430.cpp" />
    <ClCompile Include="Source\_Val_typeV_Deque_iteratorUMessageRangeMeterFilterC_1406043C0.cpp" />
    <ClCompile Include="Source\_XlendequeUMessageRangeMeterFilterCryptoPPVallocat_140600BA0.cpp" />
    <ClCompile Include="Source\_XlendequeURECV_DATAVallocatorURECV_DATAstdstdKAXX_14031AC40.cpp" />
    <ClCompile Include="Source\__imp_load__CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7B4.cpp" />
    <ClCompile Include="Source\__imp_load__CcrFG_rs_EncryptPacketYAHPEAXPEAEHZ_14066D7C6.cpp" />
  </ItemGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>