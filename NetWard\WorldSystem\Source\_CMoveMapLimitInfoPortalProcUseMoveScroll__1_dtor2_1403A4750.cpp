#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CMoveMapLimitInfoPortal::ProcUseMoveScroll_::_1_::dtor$2
 * Address: 0x1403A4750

void  CMoveMapLimitInfoPortal::ProcUseMoveScroll_::_1_::dtor_2(int64_t a1, int64_t a2)
{
  std::_Vector_iterator<char *,std::allocator<char *>>::~_Vector_iterator<char *,std::allocator<char *>>((std::_Vector_iterator<char *,std::allocator<char *> > *)(a2 + 168));
}
