#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?UpdateLazyClean@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x140206A90

char  CUnmannedTraderController::UpdateLazyClean(CUnmannedTraderController *this, char *pData)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderController *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  return CUnmannedTraderLazyCleaner::UpdateClear(&v6->m_kLazyCleaner, pData);
}
