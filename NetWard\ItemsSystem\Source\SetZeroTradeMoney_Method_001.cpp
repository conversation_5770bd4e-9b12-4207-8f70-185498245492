#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetZeroTradeMoney@CItemStore@@AEAAXXZ
 * Address: 0x140262240

void  CItemStore::Set<PERSON>eroTradeMoney(CItemStore *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-28h]@1
  CItemStore *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4->m_dwLastTradePoint = 0;
  memset_0(v4->m_dwLastTradeActPoint, 0, 0xCui64);
  v4->m_dwLastTradeDalant = 0;
  v4->m_dwLastTradeGold = 0;
}
