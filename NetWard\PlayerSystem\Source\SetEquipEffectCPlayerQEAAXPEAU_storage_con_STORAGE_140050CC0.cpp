#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetEquipEffect@CPlayer@@QEAAXPEAU_storage_con@_STORAGE_LIST@@_N@Z
 * Address: 0x140050CC0

void  CPlayer::SetEquipEffect(CPlayer *this, _STORAGE_LIST::_storage_con *pItem, bool bEquip)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1
  _STORAGE_LIST::_db_con *pItema; // [sp+38h] [bp+10h]@1
  bool v8; // [sp+40h] [bp+18h]@1

  v8 = bEquip;
  pItema = (_STORAGE_LIST::_db_con *)pItem;
  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  CPlayer::apply_case_equip_std_effect(v6, (_STORAGE_LIST::_db_con *)pItem, bEquip);
  CPlayer::apply_case_equip_upgrade_effect(v6, pItema, v8);
}
