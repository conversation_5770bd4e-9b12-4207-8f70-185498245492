#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_PvpPointGuildRankRecord@CRFWorldDatabase@@QEAA_NPEADKG@Z
 * Address: 0x1404A78D0

bool  CRFWorldDatabase::Update_PvpPointGuildRankRecord(CRFWorldDatabase *this, char *szDate, unsigned int dwSerial, unsigned int16_t wRank)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-168h]@1
  unsigned int v8; // [sp+20h] [bp-148h]@4
  char Dst; // [sp+40h] [bp-128h]@4
  unsigned int64_t v10; // [sp+150h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+170h] [bp+8h]@1
  char *v12; // [sp+178h] [bp+10h]@1
  unsigned int v13; // [sp+180h] [bp+18h]@1
  unsigned int16_t v14; // [sp+188h] [bp+20h]@1

  v14 = wRank;
  v13 = dwSerial;
  v12 = szDate;
  v11 = this;
  v4 = &v7;
  for ( i = 88i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v10 = (unsigned int64_t)&v7 ^ _security_cookie;
  memset_0(&Dst, 0, 0x100ui64);
  v8 = v13;
  sprintf(&Dst, "update [dbo].[tbl_PvpPointGuildRank%s] set rank=%u where serial=%u", v12, v14);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dst, 1);
}
