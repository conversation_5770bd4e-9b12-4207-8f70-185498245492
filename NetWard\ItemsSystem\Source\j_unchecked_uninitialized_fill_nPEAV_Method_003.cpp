#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCUnmannedTraderSchedule@@_KV1@V?$allocator@VCUnmannedTraderSchedule@@@std@@@stdext@@YAXPEAVCUnmannedTraderSchedule@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderSchedule@@@std@@@Z
 * Address: 0x14000BC7B

void  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderSchedule *,unsigned int64_t,CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(CUnmannedTraderSchedule *_First, unsigned int64_t _Count, CUnmannedTraderSchedule *_Val, std::allocator<CUnmannedTraderSchedule> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderSchedule *,unsigned int64_t,CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(
    _First,
    _Count,
    _Val,
    _Al);
}
