#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Ufill@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x14000C2D4

CUnmannedTraderClassInfo ** std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Ufill(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this, CUnmannedTraderClassInfo **_Ptr, unsigned int64_t _Count, CUnmannedTraderClassInfo *const *_Val)
{
  return std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Ufill(
           this,
           _Ptr,
           _Count,
           _Val);
}
