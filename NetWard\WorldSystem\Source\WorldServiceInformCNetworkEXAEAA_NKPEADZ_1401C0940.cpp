#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?WorldServiceInform@CNetworkEX@@AEAA_NKPEAD@Z
 * Address: 0x1401C0940

char  CNetworkEX::WorldServiceInform(CNetworkEX *this, unsigned int n, char *pMsg)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-38h]@1
  char *v7; // [sp+20h] [bp-18h]@4

  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v7 = pMsg;
  CMainThread::pc_AlterWorldService(&g_Main, *pMsg);
  return 1;
}
