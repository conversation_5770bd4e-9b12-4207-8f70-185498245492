#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Construct@VCUnmannedTraderGroupDivisionVersionInfo@@V1@@std@@YAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@AEBV1@@Z
 * Address: 0x14000745A

void  std::_Construct<CUnmannedTraderGroupDivisionVersionInfo,CUnmannedTraderGroupDivisionVersionInfo>(CUnmannedTraderGroupDivisionVersionInfo *_Ptr, CUnmannedTraderGroupDivisionVersionInfo *_Val)
{
  std::_Construct<CUnmannedTraderGroupDivisionVersionInfo,CUnmannedTraderGroupDivisionVersionInfo>(_Ptr, _Val);
}
