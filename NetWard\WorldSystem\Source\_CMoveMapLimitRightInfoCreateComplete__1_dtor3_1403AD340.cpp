#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CMoveMapLimitRightInfo::CreateComplete_::_1_::dtor$3
 * Address: 0x1403AD340

void  CMoveMapLimitRightInfo::CreateComplete_::_1_::dtor_3(int64_t a1, int64_t a2)
{
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>((std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)(a2 + 104));
}
