#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVCUnmannedTraderDivisionInfo@@_KPEAV1@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@stdext@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@_KAEBQEAV1@AEAV?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@Z
 * Address: 0x140008E2C

void  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderDivisionInfo * *,unsigned int64_t,CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(CUnmannedTraderDivisionInfo **_First, unsigned int64_t _Count, CUnmannedTraderDivisionInfo *const *_Val, std::allocator<CUnmannedTraderDivisionInfo *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderDivisionInfo * *,unsigned int64_t,CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
