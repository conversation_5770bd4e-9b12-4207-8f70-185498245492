#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?ResetInstanceItemStore@CItemStoreManager@@QEAA_NEH@Z
 * Address: 0x140348BD0

char  CItemStoreManager::ResetInstanceItemStore(CItemStoreManager *this, char byStoreType, int nSerial)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v6; // [sp+0h] [bp-B8h]@1
  int j; // [sp+30h] [bp-88h]@6
  int k; // [sp+34h] [bp-84h]@13
  char v9[4]; // [sp+48h] [bp-70h]@6
  void *Dst; // [sp+50h] [bp-68h]@6
  bool *v11; // [sp+68h] [bp-50h]@8
  int v12; // [sp+70h] [bp-48h]@13
  void *v13; // [sp+78h] [bp-40h]@12
  void *v14; // [sp+80h] [bp-38h]@15
  void *v15; // [sp+88h] [bp-30h]@15
  CItemStore *v16; // [sp+90h] [bp-28h]@17
  CItemStore *v17; // [sp+98h] [bp-20h]@17
  unsigned int64_t v18; // [sp+A0h] [bp-18h]@12
  void *v19; // [sp+A8h] [bp-10h]@18
  CItemStoreManager *v20; // [sp+C0h] [bp+8h]@1

  v20 = this;
  v3 = &v6;
  for ( i = 44i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned int8_t)byStoreType < 2 )
  {
    *(uint32_t*)v9 = 0;
    Dst = 0i64;
    for ( j = 0; j < v20->m_nInstanceItemStoreListNum; ++j )
    {
      v11 = &v20->m_InstanceItemStoreList[j].m_bUse;
      if ( *v11 && v11[1] == byStoreType && *((uint32_t*)v11 + 1) == nSerial )
      {
        *v11 = 0;
        if ( *((uint32_t*)v11 + 2) > 0 )
        {
          *(uint32_t*)v9 = *((uint32_t*)v11 + 2);
          v18 = *((uint32_t*)v11 + 2);
          v13 = operator new[](saturated_mul(8ui64, v18));
          Dst = v13;
          memset_0(v13, 0, 8i64 * *((uint32_t*)v11 + 2));
        }
        v12 = 0;
        for ( k = 0; k < *((uint32_t*)v11 + 2); ++k )
        {
          v14 = *(void **)(*((uint64_t*)v11 + 2) + 120i64 * k + 48);
          operator delete[](v14);
          v15 = *(void **)(*((uint64_t*)v11 + 2) + 120i64 * k + 80);
          operator delete[](v15);
          *(uint64_t*)(*((uint64_t*)v11 + 2) + 120i64 * k + 48) = 0i64;
          *(uint64_t*)(*((uint64_t*)v11 + 2) + 120i64 * k + 80) = 0i64;
          *((uint32_t*)Dst + 2 * k + 1) = *(uint32_t*)(*((uint64_t*)v11 + 2) + 68i64);
          ++v12;
        }
        if ( *((uint32_t*)v11 + 2) > 0 )
        {
          v17 = (CItemStore *)*((uint64_t*)v11 + 2);
          v16 = v17;
          if ( v17 )
            v19 = CItemStore::`vector deleting destructor'(v16, 3u);
          else
            v19 = 0i64;
          *((uint32_t*)v11 + 2) -= v12;
          if ( *((uint32_t*)v11 + 2) < 0 )
            *((uint32_t*)v11 + 2) = 0;
        }
        *((uint64_t*)v11 + 2) = 0i64;
        break;
      }
    }
    if ( *(uint32_t*)v9 <= 0 )
    {
      result = 0;
    }
    else
    {
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 113, v9, 16);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
