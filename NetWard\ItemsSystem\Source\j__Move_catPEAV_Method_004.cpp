#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Move_cat@PEAVCUnmannedTraderUserInfo@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAVCUnmannedTraderUserInfo@@@Z
 * Address: 0x140007B03

std::_Undefined_move_tag  std::_Move_cat<CUnmannedTraderUserInfo *>(CUnmannedTraderUserInfo *const *__formal)
{
  return std::_Move_cat<CUnmannedTraderUserInfo *>(__formal);
}
