#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SelectObject@CMapDisplay@@QEAAPEAVCGameObject@@PEAVCPoint@@@Z
 * Address: 0x14019F340

CGameObject * CMapDisplay::SelectObject(CMapDisplay *this, CPoint *pt)
{
  int *v2; // rdi@1
  signed int64_t i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  int64_t v6; // [sp+8h] [bp-10h]@7
  CMapDisplay *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < CGameObject::s_nTotalObjectNum; ++j )
  {
    v6 = CGameObject::s_pTotalObject[j];
    if ( *(uint8_t*)(v6 + 24)
      && *(CMapData **)(v6 + 88) == v7->m_pActMap
      && *(uint16_t*)(v6 + 104) == v7->m_wLayerIndex
      && pt->x >= *(uint32_t*)(v6 + 64)
      && pt->x <= *(uint32_t*)(v6 + 64) + 6
      && pt->y >= *(uint32_t*)(v6 + 68)
      && pt->y <= *(uint32_t*)(v6 + 68) + 6 )
    {
      return (CGameObject *)v6;
    }
  }
  return 0i64;
}
