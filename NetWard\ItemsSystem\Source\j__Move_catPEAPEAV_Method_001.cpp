#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Move_cat@PEAPEAVCUnmannedTraderDivisionInfo@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAPEAVCUnmannedTraderDivisionInfo@@@Z
 * Address: 0x14000F4F7

std::_Undefined_move_tag  std::_Move_cat<CUnmannedTraderDivisionInfo * *>(CUnmannedTraderDivisionInfo **const *__formal)
{
  return std::_Move_cat<CUnmannedTraderDivisionInfo * *>(__formal);
}
