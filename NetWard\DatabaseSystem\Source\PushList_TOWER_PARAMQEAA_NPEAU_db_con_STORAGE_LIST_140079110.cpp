#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?PushList@_TOWER_PARAM@@QEAA_NPEAU_db_con@_STORAGE_LIST@@PEAVCGuardTower@@@Z
 * Address: 0x140079110

char  _TOWER_PARAM::PushList(_TOWER_PARAM *this, _STORAGE_LIST::_db_con *pTowerItem, CGuardTower *pTowerObj)
{
  int *v3; // rdi@1
  signed int64_t i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  _TOWER_PARAM *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = &j;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  for ( j = 0; j < 6; ++j )
  {
    if ( !v7->m_List[j].m_pTowerItem )
    {
      v7->m_List[j].m_pTowerItem = pTowerItem;
      v7->m_List[j].m_wItemSerial = pTowerItem->m_wSerial;
      v7->m_List[j].m_pTowerObj = pTowerObj;
      return 1;
    }
  }
  return 0;
}
