#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?personal_amine_stop@CMgrAvatorItemHistory@@QEAAXPEBKHEGPEAD@Z
 * Address: 0x1400023A1

void  CMgrAvatorItemHistory::personal_amine_stop(CMgrAvatorItemHistory *this, const unsigned int *pdwMineCnt, int nMaxOreNum, char byTblCode, unsigned int16_t wItemIndex, char *szFileName)
{
  CMgrAvatorItemHistory::personal_amine_stop(this, pdwMineCnt, nMaxOreNum, byTblCode, wItemIndex, szFileName);
}
