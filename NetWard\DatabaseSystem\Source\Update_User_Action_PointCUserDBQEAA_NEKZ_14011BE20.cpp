#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_User_Action_Point@CUserDB@@QEAA_NEK@Z
 * Address: 0x14011BE20

char  CUserDB::Update_User_Action_Point(CUserDB *this, char byActionCode, unsigned int dwPoint)
{
  unsigned int v4; // [sp+20h] [bp+18h]@1

  v4 = dwPoint;
  if ( byActionCode == 2 )
  {
    if ( dwPoint > 0xF423F )
      v4 = 999999;
  }
  else if ( dwPoint > 0x98967F )
  {
    v4 = 9999999;
  }
  this->m_AvatorData.dbSupplement.dwActionPoint[(unsigned int8_t)byActionCode] = v4;
  this->m_bDataUpdate = 1;
  return 1;
}
