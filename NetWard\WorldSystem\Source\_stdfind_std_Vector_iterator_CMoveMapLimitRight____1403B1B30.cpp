#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _std::find_std::_Vector_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____CMoveMapLimitRight_____ptr64__::_1_::dtor$5
 * Address: 0x1403B1B30

void  std::find_std::_Vector_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____CMoveMapLimitRight_____ptr64__::_1_::dtor_5(int64_t a1, int64_t a2)
{
  if ( *(uint32_t*)(a2 + 120) & 1 )
  {
    *(uint32_t*)(a2 + 120) &= 0xFFFFFFFE;
    std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(*(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 192));
  }
}
