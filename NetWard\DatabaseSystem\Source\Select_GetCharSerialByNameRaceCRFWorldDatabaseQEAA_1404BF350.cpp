#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Select_GetCharSerialByNameRace@CRFWorldDatabase@@QEAAHPEADEPEAK@Z
 * Address: 0x1404BF350

signed int64_t  CRFWorldDatabase::Select_GetCharSerialByNameRace(CRFWorldDatabase *this, char *pwszName, char byRace, unsigned int *pSerial)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  signed int64_t result; // rax@8
  int64_t v7; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v10; // [sp+38h] [bp-150h]@22
  int16_t v11; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  unsigned int8_t v13; // [sp+164h] [bp-24h]@16
  unsigned int8_t v14; // [sp+165h] [bp-23h]@24
  unsigned int64_t v15; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+190h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+1A8h] [bp+20h]@1

  TargetValue = pSerial;
  v16 = this;
  v4 = &v7;
  for ( i = 96i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v15 = (unsigned int64_t)&v7 ^ _security_cookie;
  LODWORD(SQLStmt) = 2 * (unsigned int8_t)byRace + 1;
  sprintf(
    &Dest,
    "{ CALL pSelect_GetCharSerialByNameRace ( '%s', %d, %d ) }",
    pwszName,
    2 * (unsigned int)(unsigned int8_t)byRace);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &Dest);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v11 = SQLExecDirectA_0(v16->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &Dest, "_SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v11 = SQLFetch_0(v16->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v13 = 0;
        if ( v11 == 100 )
        {
          v13 = 2;
        }
        else
        {
          SQLStmt = v16->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
          v13 = 1;
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        result = v13;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 1u, -18, TargetValue, 0i64, &v10);
        if ( v11 && v11 != 1 )
        {
          v14 = 0;
          if ( v11 == 100 )
          {
            v14 = 2;
          }
          else
          {
            SQLStmt = v16->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &Dest, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
            v14 = 1;
          }
          if ( v16->m_hStmtSelect )
            SQLCloseCursor_0(v16->m_hStmtSelect);
          result = v14;
        }
        else
        {
          if ( v16->m_hStmtSelect )
            SQLCloseCursor_0(v16->m_hStmtSelect);
          if ( v16->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &Dest);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
