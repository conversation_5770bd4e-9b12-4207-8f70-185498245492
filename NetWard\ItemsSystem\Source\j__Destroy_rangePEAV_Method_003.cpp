#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Destroy_range@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@YAXPEAPEAVCUnmannedTraderSortType@@0AEAV?$allocator@PEAVCUnmannedTraderSortType@@@0@U_Scalar_ptr_iterator_tag@0@@Z
 * Address: 0x1400046B5

void  std::_Destroy_range<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last, std::allocator<CUnmannedTraderSortType *> *_Al, std::_Scalar_ptr_iterator_tag __formal)
{
  std::_Destroy_range<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(_First, _Last, _Al, __formal);
}
