#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$_Construct@VCUnmannedTraderSchedule@@V1@@std@@YAXPEAVCUnmannedTraderSchedule@@AEBV1@@Z
 * Address: 0x1400102DF

void  std::_Construct<CUnmannedTraderSchedule,CUnmannedTraderSchedule>(CUnmannedTraderSchedule *_Ptr, CUnmannedTraderSchedule *_Val)
{
  std::_Construct<CUnmannedTraderSchedule,CUnmannedTraderSchedule>(_Ptr, _Val);
}
