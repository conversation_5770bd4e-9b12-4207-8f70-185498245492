#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?PushLoad@CUnmannedTraderScheduler@@AEAAXXZ
 * Address: 0x140394020

void  CUnmannedTraderScheduler::PushLoad(CUnmannedTraderScheduler *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v3; // [sp+0h] [bp-38h]@1

  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 58, 0i64, 0);
}
