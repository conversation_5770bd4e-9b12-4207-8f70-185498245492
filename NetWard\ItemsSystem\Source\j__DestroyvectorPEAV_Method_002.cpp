#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Destroy@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAAXPEAPEAVCUnmannedTraderSortType@@0@Z
 * Address: 0x140011329

void  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Destroy(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last)
{
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Destroy(this, _First, _Last);
}
