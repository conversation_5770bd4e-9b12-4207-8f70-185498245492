#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Load@CUnmannedTraderController@@QEAA_NXZ
 * Address: 0x14034CCB0

bool  CUnmannedTraderController::Load(CUnmannedTraderController *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  bool result; // al@5
  CUnmannedTraderScheduler *v4; // rax@10
  int64_t v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderController *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( CUnmannedTraderController::InsertStateRecord(v6) )
  {
    if ( CUnmannedTraderController::UpdateClearDanglingOwnerRecord(v6) )
    {
      if ( CUnmannedTraderController::InsertDefalutRecord(v6) )
      {
        v4 = CUnmannedTraderScheduler::Instance();
        if ( CUnmannedTraderScheduler::Load(v4) )
          result = CUnmannedTraderTradeInfo::Init(&v6->m_kTradeInfo) != 0;
        else
          result = 0;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
