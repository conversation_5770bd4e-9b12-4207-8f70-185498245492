#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_copy@PEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@@stdext@@YAPEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@00@Z
 * Address: 0x14000C450

CUnmannedTraderDivisionInfo ** stdext::unchecked_copy<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *>(CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last, CUnmannedTraderDivisionInfo **_Dest)
{
  return stdext::unchecked_copy<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *>(_First, _Last, _Dest);
}
