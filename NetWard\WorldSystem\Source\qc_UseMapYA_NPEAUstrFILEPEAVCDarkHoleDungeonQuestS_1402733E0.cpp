#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?qc_UseMap@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x1402733E0

bool  qc_UseMap(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  bool result; // al@5
  int64_t v6; // [sp+0h] [bp-D8h]@1
  char poutszWord; // [sp+30h] [bp-A8h]@4
  CMapData *v8; // [sp+B8h] [bp-20h]@6
  unsigned int64_t v9; // [sp+C8h] [bp-10h]@4
  strFILE *fstra; // [sp+E0h] [bp+8h]@1
  CDarkHoleDungeonQuestSetup *pSetupa; // [sp+E8h] [bp+10h]@1

  pSetupa = pSetup;
  fstra = fstr;
  v3 = &v6;
  for ( i = 52i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v9 = (unsigned int64_t)&v6 ^ _security_cookie;
  if ( strFILE::word(fstra, &poutszWord) )
  {
    v8 = CMapOperation::GetMap(&g_MapOper, &poutszWord);
    if ( v8 )
    {
      if ( v8->m_pMapSet->m_nMapType == 1 )
      {
        pSetupa->m_pCurLoadQuest->pUseMap = v8;
        result = 1;
      }
      else
      {
        result = _false(fstra, pSetupa);
      }
    }
    else
    {
      result = _false(fstra, pSetupa);
    }
  }
  else
  {
    result = _false(fstra, pSetupa);
  }
  return result;
}
