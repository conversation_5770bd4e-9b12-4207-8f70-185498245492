#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?allocate@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEAAPEAPEAVCMoveMapLimitRight@@_K@Z
 * Address: 0x1400086DE

CMoveMapLimitRight ** std::allocator<CMoveMapLimitRight *>::allocate(std::allocator<CMoveMapLimitRight *> *this, unsigned int64_t _Count)
{
  return std::allocator<CMoveMapLimitRight *>::allocate(this, _Count);
}
