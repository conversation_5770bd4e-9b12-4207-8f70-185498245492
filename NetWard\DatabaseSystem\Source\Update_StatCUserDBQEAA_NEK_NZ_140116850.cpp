#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_Stat@CUserDB@@QEAA_NEK_N@Z
 * Address: 0x140116850

char  CUserDB::Update_Stat(CUserDB *this, char byStatIndex, unsigned int dwNewCum, bool bUpdate)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v7; // [sp+0h] [bp-38h]@1
  unsigned int v8; // [sp+20h] [bp-18h]@8
  unsigned int v9; // [sp+28h] [bp-10h]@8
  CUserDB *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( (unsigned int8_t)byStatIndex < 0x50ui64 )
  {
    if ( v10->m_AvatorData.dbStat.m_dwDamWpCnt[(unsigned int8_t)byStatIndex] <= dwNewCum || v10->m_byUserDgr )
    {
      v10->m_AvatorData.dbStat.m_dwDamWpCnt[(unsigned int8_t)byStatIndex] = dwNewCum;
      v10->m_bDataUpdate = 1;
      result = 1;
    }
    else
    {
      v9 = dwNewCum;
      v8 = v10->m_AvatorData.dbStat.m_dwDamWpCnt[(unsigned int8_t)byStatIndex];
      CLogFile::Write(
        &stru_1799C8E78,
        "%s:Update_Stat(Idx:%d)..%d >= %d",
        v10->m_aszAvatorName,
        (unsigned int8_t)byStatIndex);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(&stru_1799C8E78, "%s:Update_Stat(Idx:%d)", v10->m_aszAvatorName, (unsigned int8_t)byStatIndex);
    result = 0;
  }
  return result;
}
