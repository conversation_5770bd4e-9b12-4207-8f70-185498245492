#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_RankInGuild_Step4@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404B8CB0

char  CRFWorldDatabase::Update_RankInGuild_Step4(CRFWorldDatabase *this, unsigned int dwGuildSerial)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v5; // [sp+0h] [bp-28h]@1
  CRFWorldDatabase *v6; // [sp+30h] [bp+8h]@1
  unsigned int v7; // [sp+38h] [bp+10h]@1

  v7 = dwGuildSerial;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v6->vfptr,
    "CRFWorldDatabase::Update_RankInGuild_Step4( dwGuildSerial(%u) ) : Start Apply tb_gneral!",
    dwGuildSerial);
  if ( CRFNewDatabase::ExecUpdateQuery(
         (CRFNewDatabase *)&v6->vfptr,
         "update tbl_general set GuildRank = rank.NewRank\tfrom ( select serial, NewRank from #tbl_RankInGuildAll ) as ra"
         "nk where tbl_general.serial = rank.serial",
         0) )
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v6->vfptr,
      "CRFWorldDatabase::Update_RankInGuild_Step4( dwGuildSerial(%u) ) : End Apply tbl_general!",
      v7);
    result = 1;
  }
  else
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v6->vfptr,
      "CRFWorldDatabase::Update_RankInGuild_Step4( dwGuildSerial(%u) ) : update tbl_general set GuildRank = rank.NewRank\t"
      "from ( select serial, NewRank from #tbl_RankInGuildAll ) as rank where tbl_general.serial = rank.serial Fail!",
      v7);
    CRFWorldDatabase::Update_RankInGuild_Step6(v6);
    result = 0;
  }
  return result;
}
