#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Insert_n@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@IEAAXV?$_Vector_iterator@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@2@_KAEBVCUnmannedTraderUserInfo@@@Z
 * Address: 0x1400026A3

void  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Insert_n(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *_Where, unsigned int64_t _Count, CUnmannedTraderUserInfo *_Val)
{
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Insert_n(this, _Where, _Count, _Val);
}
