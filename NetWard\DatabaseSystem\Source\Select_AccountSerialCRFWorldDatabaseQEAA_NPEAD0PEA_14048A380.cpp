#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Select_AccountSerial@CRFWorldDatabase@@QEAA_NPEAD0PEAK@Z
 * Address: 0x14048A380

char  CRFWorldDatabase::Select_AccountSerial(CRFWorldDatabase *this, char *pwszCharacterName, char *szAccount, unsigned int *pSerial)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@8
  int64_t v7; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@21
  SQLLEN v10; // [sp+38h] [bp-150h]@21
  int16_t v11; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int v13; // [sp+164h] [bp-24h]@4
  unsigned int64_t v14; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+190h] [bp+8h]@1
  char *v16; // [sp+1A0h] [bp+18h]@1
  unsigned int *TargetValue; // [sp+1A8h] [bp+20h]@1

  TargetValue = pSerial;
  v16 = szAccount;
  v15 = this;
  v4 = &v7;
  for ( i = 96i64; i; --i )
  {
    *(uint32_t*)v4 = -*********;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v14 = (unsigned int64_t)&v7 ^ _security_cookie;
  v13 = 0;
  sprintf(&Dest, "select accountserial, account from tbl_base where name= '%s'", pwszCharacterName);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &Dest);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v11 = SQLExecDirectA_0(v15->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &Dest, "_SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v11 = SQLFetch_0(v15->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        if ( v11 != 100 )
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
        }
        if ( v15->m_hStmtSelect )
          SQLCloseCursor_0(v15->m_hStmtSelect);
        result = 0;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 1u, -18, TargetValue, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)13;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 2u, 1, v16, 13i64, &v10);
        if ( v11 && v11 != 1 )
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &Dest, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          result = 0;
        }
        else
        {
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          if ( v15->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &Dest);
          result = 1;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
