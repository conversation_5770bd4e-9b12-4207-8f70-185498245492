#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Load@CUnmannedTraderTaxRateManager@@QEAA_NXZ
 * Address: 0x14038DD80

char  CUnmannedTraderTaxRateManager::Load(CUnmannedTraderTaxRateManager *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  CWeeklyGuildRankManager *v4; // rax@10
  TRC_AutoTrade **v5; // rax@10
  TRC_AutoTrade **v6; // rax@10
  int64_t v7; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@6
  CGuild *pGuild; // [sp+28h] [bp-20h]@10
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v10; // [sp+30h] [bp-18h]@10
  CUnmannedTraderTaxRateManager *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v1 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v1 = -*********;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v11->m_vecTRC) )
  {
    result = 0;
  }
  else
  {
    for ( j = 0; j < 3; ++j )
    {
      if ( !*std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v11->m_vecTRC, j) )
        return 0;
      v4 = CWeeklyGuildRankManager::Instance();
      pGuild = CWeeklyGuildRankManager::GetPrevOwnerGuild(v4, j, 0);
      v10 = &v11->m_vecTRC;
      v5 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v11->m_vecTRC, j);
      TRC_AutoTrade::set_owner(*v5, pGuild);
      v6 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v11->m_vecTRC, j);
      if ( !TRC_AutoTrade::_db_load(*v6, j) )
        return 0;
    }
    result = 1;
  }
  return result;
}
