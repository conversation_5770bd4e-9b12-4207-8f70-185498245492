#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SUItemSystem_CheckData@CSUItemSystem@@QEAA_NXZ
 * Address: 0x1402E4100

char  CSUItemSystem::SUItemSystem_CheckData(CSUItemSystem *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  int64_t v4; // [sp+0h] [bp-48h]@1
  si_interpret *v5; // [sp+20h] [bp-28h]@8
  int v6; // [sp+28h] [bp-20h]@8
  int set_pos; // [sp+2Ch] [bp-1Ch]@8
  int v8; // [sp+30h] [bp-18h]@8
  int idx; // [sp+34h] [bp-14h]@8
  CSUItemSystem *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( CRecordData::GetRecordNum(v10->m_SUOrigin) > 0 )
  {
    if ( CSetItemType::GetEffectTypeCount(&v10->m_SetItemType) > 0 )
    {
      v5 = 0i64;
      v6 = 0;
      v8 = 0;
      idx = 0;
      set_pos = 0;
      v6 = CSetItemType::GetEffectTypeCount(&v10->m_SetItemType);
      while ( set_pos < v6 )
      {
        v5 = CSetItemType::Getsi_interpret(&v10->m_SetItemType, set_pos);
        if ( !v5 )
          return 0;
        if ( (signed int)(unsigned int8_t)si_interpret::GetEffectTypeCount(v5) > 8 )
          return 0;
        idx = 0;
        v8 = (unsigned int8_t)si_interpret::GetEffectTypeCount(v5);
        while ( idx < v8 )
        {
          if ( (signed int)(unsigned int8_t)si_interpret::GetCountOfItem(v5, idx) > 12 )
            return 0;
          if ( (signed int)(unsigned int8_t)si_interpret::GetCountOfEffect(v5, idx) > 8 )
            return 0;
          ++idx;
        }
        ++set_pos;
      }
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
