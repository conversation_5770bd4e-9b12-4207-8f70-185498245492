#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Update_AutoTradeAllClear@CUserDB@@QEAA_NXZ
 * Address: 0x140116EC0

char  CUserDB::Update_AutoTradeAllClear(CUserDB *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-28h]@1
  CUserDB *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  _TRADE_DB_BASE::Clear(&v5->m_AvatorData.dbTrade);
  return 1;
}
