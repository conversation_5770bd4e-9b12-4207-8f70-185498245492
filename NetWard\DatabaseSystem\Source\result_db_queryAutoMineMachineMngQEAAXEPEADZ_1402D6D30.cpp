#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?result_db_query@AutoMineMachineMng@@QEAAXEPEAD@Z
 * Address: 0x1402D6D30

void  AutoMineMachineMng::result_db_query(AutoMineMachineMng *this, char byRet, char *pdata)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v5; // [sp+0h] [bp-38h]@1
  char *v6; // [sp+20h] [bp-18h]@5
  AutoMineMachine *v7; // [sp+28h] [bp-10h]@5
  AutoMineMachineMng *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  char *pdataa; // [sp+50h] [bp+18h]@1

  pdataa = pdata;
  v9 = byRet;
  v8 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( *pdata == 1 )
  {
    v6 = pdata;
    v7 = AutoMineMachineMng::GetMachine(v8, pdata[2], pdata[1]);
    AutoMineMachine::SubChargeCost(v7, v9, pdataa);
  }
}
