#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?_Ufill@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitInfo@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x1400104D8

CMoveMapLimitInfo ** std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Ufill(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, CMoveMapLimitInfo **_Ptr, unsigned int64_t _Count, CMoveMapLimitInfo *const *_Val)
{
  return std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Ufill(this, _Ptr, _Count, _Val);
}
