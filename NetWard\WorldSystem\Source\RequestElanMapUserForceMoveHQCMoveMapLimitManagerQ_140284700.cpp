#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?RequestElanMapUserForceMoveHQ@CMoveMapLimitManager@@QEAAEXZ
 * Address: 0x140284700

char  CMoveMapLimitManager::RequestElanMapUserForceMoveHQ(CMoveMapLimitManager *this)
{
  int64_t *v1; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-48h]@1
  CMoveMapLimitManager *v5; // [sp+50h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  return CMoveMapLimitManager::Request(
           v5,
           0,
           0,
           CMoveMapLimitEnviromentValues::ELAN_MAP_CODE,
           CMoveMapLimitEnviromentValues::ELAN_1TH_LIMIT_NPC_RECORD_INDEX,
           -1,
           0i64);
}
