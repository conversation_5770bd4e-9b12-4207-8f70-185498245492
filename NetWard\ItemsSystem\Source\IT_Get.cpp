// NetWard RF Online Server - Item System
// File: IT_Get.cpp - Item Array Access
// Compatible with Visual Studio 2022 (C++20)
// Original Function: CArray<CLuaLooting_Novus_Item>::GetAtPtr

#pragma once
#include "../../Headers/Items/ItemCore.h"
#include "../../Headers/Items/LootingSystem.h"
#include "../../Headers/Common/NetWardTypes.h"
#include "../../Headers/Common/SafeArray.h"
#include <memory>
#include <stdexcept>

namespace NetWard::Items {

/**
 * @brief Safely retrieves a pointer to an item at the specified index
 * @param index The zero-based index of the item to retrieve
 * @return Pointer to the item if valid, nullptr if index is out of bounds
 *
 * @details This function provides safe access to items in the looting array.
 *          It performs bounds checking to prevent buffer overflows and
 *          ensures the array is properly allocated before access.
 *
 * @note This is a critical function for the RF Online looting system
 *       that handles Novus-specific item drops and rewards.
 */
template<typename ItemType>
ItemType* SafeArray<ItemType>::GetItemAt(uint32_t index) const noexcept
{
    // Validate array state and bounds
    if (!IsAllocated() || index >= GetCount()) {
        return nullptr;
    }

    // Return pointer to the item at the specified index
    return &m_itemBuffer[index];
}

// Explicit instantiation for Novus looting items
template class SafeArray<NovusLootItem>;

/**
 * @brief Specialized getter for Novus looting items with additional validation
 * @param index The index of the item to retrieve
 * @return Shared pointer to the item for safe memory management
 * @throws std::out_of_range if index is invalid
 */
std::shared_ptr<NovusLootItem> NovusLootArray::GetNovusItemAt(uint32_t index) const
{
    if (auto* rawPtr = GetItemAt(index)) {
        return std::make_shared<NovusLootItem>(*rawPtr);
    }

    throw std::out_of_range("Invalid item index: " + std::to_string(index));
}
