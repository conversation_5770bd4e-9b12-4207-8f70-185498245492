#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ??$unchecked_uninitialized_copy@PEAVCUnmannedTraderItemCodeInfo@@PEAV1@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@stdext@@YAPEAVCUnmannedTraderItemCodeInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@Z
 * Address: 0x14037C850

CUnmannedTraderItemCodeInfo * stdext::unchecked_uninitialized_copy<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *,std::allocator<CUnmannedTraderItemCodeInfo>>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, CUnmannedTraderItemCodeInfo *_Dest, std::allocator<CUnmannedTraderItemCodeInfo> *_Al)
{
  int64_t *v4; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v9; // [sp+31h] [bp-17h]@4
  CUnmannedTraderItemCodeInfo *__formal; // [sp+50h] [bp+8h]@1
  CUnmannedTraderItemCodeInfo *_Lasta; // [sp+58h] [bp+10h]@1
  CUnmannedTraderItemCodeInfo *_Desta; // [sp+60h] [bp+18h]@1
  std::allocator<CUnmannedTraderItemCodeInfo> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  _Desta = _Dest;
  _Lasta = _Last;
  __formal = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Ptr_cat<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *>(&__formal, &_Desta);
  return std::_Uninit_copy<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *,std::allocator<CUnmannedTraderItemCodeInfo>>(
           __formal,
           _Lasta,
           _Desta,
           _Ala,
           v9,
           v8);
}
