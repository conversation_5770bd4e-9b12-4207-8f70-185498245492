#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?Rebirth_Base@CRFWorldDatabase@@QEAA_NKPEAD@Z
 * Address: 0x140493F10

bool  CRFWorldDatabase::Rebirth_Base(CRFWorldDatabase *this, unsigned int dwCharacterSerial, char *pwszName)
{
  int64_t *v3; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v6; // [sp+0h] [bp-158h]@1
  char Dest; // [sp+30h] [bp-128h]@4
  unsigned int64_t v8; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+160h] [bp+8h]@1

  v9 = this;
  v3 = &v6;
  for ( i = 84i64; i; --i )
  {
    *(uint32_t*)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v8 = (unsigned int64_t)&v6 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pRebirth_Base( %d, '%s' ) }", dwCharacterSerial, pwszName);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &Dest, 1);
}
