#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_?trunk_swap_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@0KKPEAD@Z
 * Address: 0x140004EAD

void  CMgrAvatorItemHistory::trunk_swap_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pInputItem, _STORAGE_LIST::_db_con *pOutputItem, unsigned int dwFeeDalant, unsigned int dwNewDalant, char *pszFileName)
{
  CMgrAvatorItemHistory::trunk_swap_item(this, n, pInputItem, pOutputItem, dwFeeDalant, dwNewDalant, pszFileName);
}
