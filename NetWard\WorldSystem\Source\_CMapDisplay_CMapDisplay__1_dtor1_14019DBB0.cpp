#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: _CMapDisplay::_CMapDisplay_::_1_::dtor$1
 * Address: 0x14019DBB0

void  CMapDisplay::_CMapDisplay_::_1_::dtor_1(int64_t a1, int64_t a2)
{
  `eh vector destructor iterator'(
    (void *)(*(uint64_t*)(a2 + 112) + 1048i64),
    0x40ui64,
    60,
    (void ( *)(void *))CCollLineDraw::~CCollLineDraw);
}
