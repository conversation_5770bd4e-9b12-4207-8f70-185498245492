#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: j_??$unchecked_copy@PEAPEAVCLogTypeDBTask@@PEAPEAV1@@stdext@@YAPEAPEAVCLogTypeDBTask@@PEAPEAV1@00@Z
 * Address: 0x140008797

CLogTypeDBTask ** stdext::unchecked_copy<CLogTypeDBTask * *,CLogTypeDBTask * *>(CLogTypeDBTask **_First, CLogTypeDBTask **_Last, CLogTypeDBTask **_Dest)
{
  return stdext::unchecked_copy<CLogTypeDBTask * *,CLogTypeDBTask * *>(_First, _Last, _Dest);
}
