#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: SQLGetInstalledDrivers
 * Address: 0x1404DA7A4

int  SQLGetInstalledDrivers(char *lpszBuf, unsigned int16_t cbBufMax, unsigned int16_t *pcbBufOut)
{
  char *v3; // rsi@1
  unsigned int16_t *v4; // rbx@1
  unsigned int16_t v5; // di@1
  int64_t ( *v6)(); // rax@1
  int result; // eax@2

  v3 = lpszBuf;
  v4 = pcbBufOut;
  v5 = cbBufMax;
  v6 = ODBC___GetSetupProc("SQLGetInstalledDrivers");
  if ( v6 )
    result = ((int ( *)(char *, _QWORD, unsigned int16_t *))v6)(v3, v5, v4);
  else
    result = 0;
  return result;
}
