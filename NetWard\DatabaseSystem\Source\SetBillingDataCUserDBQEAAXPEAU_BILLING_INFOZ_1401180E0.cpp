#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SetBillingData@CUserDB@@QEAAXPEAU_BILLING_INFO@@@Z
 * Address: 0x1401180E0

void  CUserDB::SetBillingData(CUserDB *this, _BILLING_INFO *pBillingInfo)
{
  int64_t *v2; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v4; // [sp+0h] [bp-28h]@1
  CUserDB *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t*)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( pBillingInfo )
    memcpy_0(&v5->m_BillingInfo, pBillingInfo, 0x24ui64);
  v5->m_BillingInfo.bPCCheat = 0;
}
