#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?SellWaitItem@CUnmannedTraderRegistItemInfo@@QEAAEGPEAVCLogFile@@_JAEAE@Z
 * Address: 0x140352200

char  CUnmannedTraderRegistItemInfo::SellWaitItem(CUnmannedTraderRegistItemInfo *this, unsigned int16_t wInx, CLogFile *pkLogger, int64_t tResultTime, char *byStorageInx)
{
  int64_t *v5; // rdi@1
  signed int64_t i; // rcx@1
  char result; // al@5
  char *v8; // rax@10
  unsigned int v9; // eax@15
  char *v10; // rax@16
  unsigned int v11; // eax@17
  int v12; // eax@18
  int v13; // ecx@18
  char *v14; // rax@20
  int64_t v15; // [sp+0h] [bp-158h]@1
  unsigned int bDelete[2]; // [sp+20h] [bp-138h]@7
  char *strErrorCodePos; // [sp+28h] [bp-130h]@10
  int64_t tResultTimea; // [sp+30h] [bp-128h]@10
  unsigned int dwPrice[2]; // [sp+38h] [bp-120h]@16
  unsigned int dwTax[2]; // [sp+40h] [bp-118h]@16
  unsigned int dwLeftDalant; // [sp+48h] [bp-110h]@16
  unsigned int dwLeftGold; // [sp+50h] [bp-108h]@16
  char *pszFileName; // [sp+58h] [bp-100h]@17
  CPlayer *v24; // [sp+60h] [bp-F8h]@8
  _STORAGE_LIST::_storage_con *v25; // [sp+68h] [bp-F0h]@8
  unsigned int64_t ui64AddMoney; // [sp+70h] [bp-E8h]@14
  char szTran; // [sp+88h] [bp-D0h]@17
  char v28; // [sp+89h] [bp-CFh]@17
  tm *v29; // [sp+A8h] [bp-B0h]@17
  char DstBuf; // [sp+C0h] [bp-98h]@18
  _base_fld *v31; // [sp+108h] [bp-50h]@20
  int v32; // [sp+118h] [bp-40h]@10
  int64_t v33; // [sp+120h] [bp-38h]@10
  CPlayer *v34; // [sp+128h] [bp-30h]@10
  unsigned int v35; // [sp+130h] [bp-28h]@16
  char *v36; // [sp+138h] [bp-20h]@17
  unsigned int v37; // [sp+140h] [bp-18h]@17
  unsigned int64_t v38; // [sp+148h] [bp-10h]@4
  CUnmannedTraderRegistItemInfo *v39; // [sp+160h] [bp+8h]@1
  unsigned int16_t v40; // [sp+168h] [bp+10h]@1
  CLogFile *v41; // [sp+170h] [bp+18h]@1
  int64_t _Time; // [sp+178h] [bp+20h]@1

  _Time = tResultTime;
  v41 = pkLogger;
  v40 = wInx;
  v39 = this;
  v5 = &v15;
  for ( i = 84i64; i; --i )
  {
    *(uint32_t*)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  v38 = (unsigned int64_t)&v15 ^ _security_cookie;
  if ( (signed int)wInx < 2532 )
  {
    if ( v39->m_dwBuyerSerial )
    {
      v24 = &g_Player + wInx;
      v25 = (_STORAGE_LIST::_storage_con *)_STORAGE_LIST::GetPtrFromSerial(
                                             (_STORAGE_LIST *)&v24->m_Param.m_dbInven.m_nListNum,
                                             v39->m_wItemSerial);
      if ( v25 )
      {
        if ( v39->m_dwPrice <= v39->m_dwTax )
        {
          LODWORD(tResultTimea) = v39->m_dwTax;
          LODWORD(strErrorCodePos) = v39->m_dwPrice;
          bDelete[0] = v39->m_dwBuyerSerial;
          CLogFile::Write(
            v41,
            "CUnmannedTraderController::SellWaitItem(...) Exceed Tax Price!\r\n"
            "\t\t dwRegistSerial(%u) dwSeller(%u) dwBuyer(%u)\r\n"
            "\t\t dwPrice(%u) dwTax(%u)\r\n",
            v39->m_dwRegistSerial,
            v24->m_dwObjSerial);
          v39->m_dwTax = (signed int)ffloor((float)(signed int)v39->m_dwPrice * 0.*********);
        }
        LODWORD(ui64AddMoney) = v39->m_dwPrice - v39->m_dwTax;
        if ( !(_DWORD)ui64AddMoney
          || (v9 = CPlayerDB::GetDalant(&v24->m_Param), CanAddMoneyForMaxLimMoney((unsigned int)ui64AddMoney, v9)) )
        {
          _STORAGE_LIST::_storage_con::lock(v25, 0);
          *byStorageInx = BYTE3(v25[1].m_dwDur);
          CPlayer::AddDalant(v24, ui64AddMoney, 1);
          szTran = 0;
          memset(&v28, 0, 0x10ui64);
          W2M(v39->m_wszBuyerName, &szTran, 0x11u);
          v36 = v24->m_szItemHistoryFileName;
          v37 = CPlayerDB::GetGold(&v24->m_Param);
          v11 = CPlayerDB::GetDalant(&v24->m_Param);
          pszFileName = v36;
          dwLeftGold = v37;
          dwLeftDalant = v11;
          dwTax[0] = v39->m_dwTax;
          dwPrice[0] = ui64AddMoney;
          tResultTimea = _Time;
          strErrorCodePos = (char *)v25;
          bDelete[0] = v39->m_dwRegistSerial;
          CMgrAvatorItemHistory::auto_trade_login_sell(
            &CPlayer::s_MgrItemHistory,
            &szTran,
            v39->m_dwBuyerSerial,
            v39->m_szBuyerAccount,
            bDelete[0],
            (_STORAGE_LIST::_db_con *)v25,
            _Time,
            ui64AddMoney,
            dwTax[0],
            v11,
            v37,
            v36);
          v29 = localtime_12(&_Time);
          if ( v29 )
          {
            v12 = v29->tm_mon + 1;
            v13 = v29->tm_year;
            dwTax[0] = v29->tm_sec;
            dwPrice[0] = v29->tm_min;
            LODWORD(tResultTimea) = v29->tm_hour;
            LODWORD(strErrorCodePos) = v29->tm_mday;
            bDelete[0] = v12;
            sprintf_s(&DstBuf, 0x40ui64, "%04d-%02d-%02d %02d:%02d:%02d", (unsigned int)(v13 + 1900));
          }
          else
          {
            sprintf_s(&DstBuf, 0x40ui64, "Invalid(%u)", _Time);
          }
          v31 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v25->m_byTableCode, v25->m_wItemIndex);
          v14 = CPlayerDB::GetCharNameA(&v24->m_Param);
          dwLeftGold = v39->m_dwTax;
          dwLeftDalant = v39->m_dwPrice;
          *(uint64_t*)dwTax = v14;
          dwPrice[0] = v24->m_dwObjSerial;
          tResultTimea = (int64_t)&szTran;
          LODWORD(strErrorCodePos) = v39->m_dwBuyerSerial;
          bDelete[0] = v39->m_dwRegistSerial;
          CLogFile::Write(
            &stru_1799C94F0,
            "%s %s >> sellwait (%u) user(%u)%s -> user(%u)%s price(%u) tax(%u)",
            v31->m_strCode,
            &DstBuf);
          result = 0;
        }
        else
        {
          strErrorCodePos = "CUnmannedTraderRegistItemInfo::SellWaitItem()";
          LOBYTE(bDelete[0]) = 1;
          CPlayer::Emb_DelStorage(v24, 0, BYTE3(v25[1].m_dwDur), 0, 1, "CUnmannedTraderRegistItemInfo::SellWaitItem()");
          v35 = CPlayerDB::GetDalant(&v24->m_Param);
          v10 = CPlayerDB::GetCharNameA(&v24->m_Param);
          dwLeftGold = ui64AddMoney;
          dwLeftDalant = v35;
          dwTax[0] = v24->m_id.dwSerial;
          *(uint64_t*)dwPrice = v10;
          LODWORD(tResultTimea) = v39->m_dwTax;
          LODWORD(strErrorCodePos) = v39->m_dwPrice;
          bDelete[0] = ui64AddMoney;
          CLogFile::Write(
            v41,
            "CUnmannedTraderRegistItemInfo::SellWaitItem( WORD wInx(%u), DWORD & dwRealSellDalant, CLogFile * pkLogger )\r"
            "\n"
            "\t\tm_dwRegistSerial(%u) dwRealSellDalant(%u) = m_dwPrice(%u) - m_dwTax(%u)\r\n"
            "\t\tName(%s) Serial(%u) Cur(%u) Sell(%u) Exceed Max Money!\r\n",
            v40,
            v39->m_dwRegistSerial);
          result = 34;
        }
      }
      else
      {
        if ( v41 )
        {
          v32 = v39->m_wItemSerial;
          v33 = 50856i64 * v40;
          v34 = &g_Player;
          v8 = CPlayerDB::GetCharNameA((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * v40));
          LODWORD(tResultTimea) = v32;
          LODWORD(strErrorCodePos) = v39->m_dwRegistSerial;
          bDelete[0] = v34[(unsigned int64_t)v33 / 0xC6A8].m_id.dwSerial;
          CLogFile::Write(
            v41,
            "CUnmannedTraderRegistItemInfo::SellWaitItem( WORD wInx(%u), CLogFile * pkLogger )\r\n"
            "\t\tName(%s) Serial(%u) Invalid DB Data!\r\n"
            "\t\tm_dwRegistSerial(%u)\r\n"
            "\t\tpkPlayer->m_Param.m_dbInven.GetPtrFromSerial( m_wItemSerial(%u) ) == NULL!\r\n",
            v40,
            v8);
        }
        result = 37;
      }
    }
    else
    {
      bDelete[0] = CUnmannedTraderItemState::GetState(&v39->m_kState);
      CLogFile::Write(
        v41,
        "CUnmannedTraderRegistItemInfo::SellWaitItem(...)\r\n"
        "\t\t(0 == m_dwBuyerSerial) m_dwRegistSerial(%u) Serial(%u) State(%d)\r\n",
        *(&g_Player.m_id.dwSerial + 12714 * v40),
        v39->m_dwRegistSerial);
      result = 94;
    }
  }
  else
  {
    result = 99;
  }
  return result;
}
