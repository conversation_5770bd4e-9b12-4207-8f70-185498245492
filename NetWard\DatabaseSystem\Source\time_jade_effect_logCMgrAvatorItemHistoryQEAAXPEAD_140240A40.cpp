#include "../../Common/NetWardCore.h"
#include <windows.h>
#include <memory>
#include <iostream>

/*
 * Function: ?time_jade_effect_log@CMgrAvatorItemHistory@@QEAAXPEADPEAU_db_con@_STORAGE_LIST@@_N0@Z
 * Address: 0x140240A40

void  CMgrAvatorItemHistory::time_jade_effect_log(CMgrAvatorItemHistory *this, char *pszItemName, _STORAGE_LIST::_db_con *pItem, bool bAdd, char *pszFileName)
{
  int64_t *v5; // rdi@1
  signed int64_t i; // rcx@1
  int64_t v7; // [sp+0h] [bp-48h]@1
  unsigned int64_t v8; // [sp+20h] [bp-28h]@5
  char *v9; // [sp+28h] [bp-20h]@5
  char *v10; // [sp+30h] [bp-18h]@5
  CMgrAvatorItemHistory *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v5 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t*)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  if ( bAdd )
  {
    v10 = v11->m_szCurTime;
    v9 = v11->m_szCurDate;
    v8 = pItem->m_dwDur;
    sprintf_s<20000>(
      (char (*)[20000])sData,
      "Item Effect Add :%s_[%I64u] [Count :%d] [%s %s]\r\n",
      pszItemName,
      pItem->m_lnUID);
  }
  else
  {
    v10 = v11->m_szCurTime;
    v9 = v11->m_szCurDate;
    v8 = pItem->m_dwDur;
    sprintf_s<20000>(
      (char (*)[20000])sData,
      "Item Effect Del :%s_[%I64u] [Count :%d] [%s %s]\r\n",
      pszItemName,
      pItem->m_lnUID);
  }
  CMgrAvatorItemHistory::WriteFile(v11, pszFileName, sData);
}
